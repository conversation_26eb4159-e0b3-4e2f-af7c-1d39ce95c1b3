{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"footer\", {\n    staticClass: \"custom-footer\"\n  }, [_c(\"ul\", {\n    staticClass: \"clear font-size-zero\"\n  }, [_vm._m(0), _c(\"li\", [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.provideTip\"))), _c(\"strong\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.ssq\")))]), _vm._v(_vm._s(_vm.$t(\"commonFooter.provide\")))]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"lang-switch-btn\"\n  }, [_c(\"LangSwitch\"), _c(\"i\", [_vm._v(\"|\")])], 1), _vm.lang === \"zh\" ? _c(\"li\", [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.signHotline\")) + \"：400-993-6665\")]), _c(\"i\", [_vm._v(\"|\")])]) : _vm._e(), _c(\"li\", [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.record\")))])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"li\", [_c(\"img\", {\n    attrs: {\n      src: require(\"img/customFooterLogo.png\"),\n      width: \"142\",\n      alt: \"$t('commonFooter.ssqLogo')\"\n    }\n  })]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "_s", "$t", "lang", "_e", "staticRenderFns", "attrs", "src", "require", "width", "alt", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/components/customFooter/CustomFooter.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"footer\", { staticClass: \"custom-footer\" }, [\n    _c(\"ul\", { staticClass: \"clear font-size-zero\" }, [\n      _vm._m(0),\n      _c(\"li\", [\n        _c(\"span\", [\n          _vm._v(_vm._s(_vm.$t(\"commonFooter.provideTip\"))),\n          _c(\"strong\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.ssq\")))]),\n          _vm._v(_vm._s(_vm.$t(\"commonFooter.provide\"))),\n        ]),\n        _c(\"i\", [_vm._v(\"|\")]),\n      ]),\n      _c(\n        \"li\",\n        { staticClass: \"lang-switch-btn\" },\n        [_c(\"LangSwitch\"), _c(\"i\", [_vm._v(\"|\")])],\n        1\n      ),\n      _vm.lang === \"zh\"\n        ? _c(\"li\", [\n            _c(\"span\", [\n              _vm._v(\n                _vm._s(_vm.$t(\"commonFooter.signHotline\")) + \"：400-993-6665\"\n              ),\n            ]),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ])\n        : _vm._e(),\n      _c(\"li\", [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.record\")))])]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"li\", [\n      _c(\"img\", {\n        attrs: {\n          src: require(\"img/customFooterLogo.png\"),\n          width: \"142\",\n          alt: \"$t('commonFooter.ssqLogo')\",\n        },\n      }),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACpDF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CAChDH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,yBAAyB,CAAC,CAAC,CAAC,EACjDN,EAAE,CAAC,QAAQ,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1DP,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAC/C,CAAC,EACFN,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFJ,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CAACF,EAAE,CAAC,YAAY,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC1C,CACF,CAAC,EACDL,GAAG,CAACQ,IAAI,KAAK,IAAI,GACbP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACK,EAAE,CACJL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,0BAA0B,CAAC,CAAC,GAAG,eAC/C,CAAC,CACF,CAAC,EACFN,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,GACFL,GAAG,CAACS,EAAE,CAAC,CAAC,EACZR,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACxE,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIG,eAAe,GAAG,CACpB,YAAY;EACV,IAAIV,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,IAAI,EAAE,CACdA,EAAE,CAAC,KAAK,EAAE;IACRU,KAAK,EAAE;MACLC,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDhB,MAAM,CAACiB,aAAa,GAAG,IAAI;AAE3B,SAASjB,MAAM,EAAEW,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}