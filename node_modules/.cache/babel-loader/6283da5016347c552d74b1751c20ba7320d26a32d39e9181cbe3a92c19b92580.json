{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"chat\",\n    staticClass: \"hubble-chat\"\n  }, [_c(\"div\", {\n    staticClass: \"hubble-chat__header\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"operate\"\n  }, [_c(\"el-popover\", {\n    attrs: {\n      trigger: \"click\",\n      \"popper-class\": \"download-popover\"\n    },\n    model: {\n      value: _vm.languageSwitchVisible,\n      callback: function ($$v) {\n        _vm.languageSwitchVisible = $$v;\n      },\n      expression: \"languageSwitchVisible\"\n    }\n  }, [_c(\"el-tooltip\", {\n    attrs: {\n      slot: \"reference\",\n      \"open-delay\": 500,\n      effect: \"dark\",\n      content: \"输出语言切换\",\n      placement: \"top\"\n    },\n    slot: \"reference\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-ssq-diqiu\",\n    attrs: {\n      id: \"guide-lang\"\n    }\n  })]), _c(\"ul\", [_c(\"li\", {\n    class: _vm.isEn ? \"\" : \"active\",\n    on: {\n      click: function ($event) {\n        return _vm.switchEnLanguage(false);\n      }\n    }\n  }, [_vm._v(\"中文语言\")]), _c(\"li\", {\n    class: _vm.isEn ? \"active\" : \"\",\n    on: {\n      click: function ($event) {\n        return _vm.switchEnLanguage(true);\n      }\n    }\n  }, [_vm._v(\"英文语言\")])])], 1), _c(\"el-popover\", {\n    attrs: {\n      trigger: \"click\",\n      \"popper-class\": \"download-popover\"\n    }\n  }, [_c(\"el-tooltip\", {\n    attrs: {\n      slot: \"reference\",\n      \"open-delay\": 500,\n      effect: \"dark\",\n      content: \"文档下载\",\n      placement: \"top\"\n    },\n    slot: \"reference\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-ssq--bs-xiazai\"\n  })]), _c(\"ul\", [_c(\"li\", {\n    on: {\n      click: function ($event) {\n        return _vm.handleDownloadChat(false);\n      }\n    }\n  }, [_vm._v(\"源文件\")]), _c(\"li\", {\n    on: {\n      click: _vm.handleDownloadChat\n    }\n  }, [_vm._v(\"源文件 (携带对话)\")])])], 1)], 1)]), _c(\"div\", {\n    staticClass: \"hubble-chat__body\",\n    on: {\n      scroll: _vm.handleScroll\n    }\n  }, [_vm.chatInitLoading ? _c(\"div\", {\n    staticClass: \"hubble-chat__loading\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"views/agent/img/loading2.gif\"),\n      alt: \"\"\n    }\n  }), _c(\"br\"), _c(\"span\", [_vm._v(\"文档解析中\")])]) : [_c(\"ChatList\", {\n    attrs: {\n      messages: _vm.messages\n    },\n    on: {\n      selectQuote: _vm.handleSelectQuote,\n      showContinuousChat: _vm.handleShowContinuousChat\n    }\n  }), _c(\"transition\", {\n    attrs: {\n      name: \"fade\"\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showContinuousChat,\n      expression: \"showContinuousChat\"\n    }],\n    staticClass: \"continuous-chat\"\n  }, [_c(\"ChatList\", {\n    attrs: {\n      isContinuousChat: \"\",\n      messages: _vm.continuousMessages\n    },\n    on: {\n      selectQuote: _vm.handleSelectQuote,\n      loadMore: _vm.handleLoadMoreContinuousChat\n    }\n  }), _c(\"div\", {\n    staticClass: \"continuous-exit\"\n  }, [_c(\"span\", [_vm._v(\"连续对话模式：\")]), _c(\"span\", {\n    staticClass: \"exit\",\n    on: {\n      click: function ($event) {\n        _vm.showContinuousChat = false;\n      }\n    }\n  }, [_vm._v(\"退出\")])])], 1)])]], 2), _c(\"div\", {\n    staticClass: \"hubble-chat__dialog\"\n  }, [_c(\"transition\", {\n    attrs: {\n      name: \"slide\"\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: !!_vm.preQuote.content,\n      expression: \"!!preQuote.content\"\n    }],\n    staticClass: \"hubble-chat__quote\"\n  }, [_c(\"div\", {\n    staticClass: \"hubble-chat__quote-header\"\n  }, [_c(\"span\", [_vm._v(\"引用内容：\")]), _c(\"i\", {\n    staticClass: \"el-icon-ssq-guanbi\",\n    on: {\n      click: _vm.clearQuote\n    }\n  })]), _c(\"div\", {\n    staticClass: \"hubble-chat__quote-body chat-quote\",\n    on: {\n      click: function ($event) {\n        return _vm.handleSelectQuote(_vm.preQuote);\n      }\n    }\n  }, [_vm._v(_vm._s(_vm.preQuote.content))])])]), _c(\"transition\", {\n    attrs: {\n      name: \"slide\"\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showSuggestion,\n      expression: \"showSuggestion\"\n    }],\n    class: `hubble-chat__suggestion ${!_vm.preQuote.content ? \"without-quote\" : \"\"}`\n  }, [_c(\"div\", {\n    staticClass: \"hubble-chat__suggestion-header\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-ssq-gaoliangtishiH5\"\n  }), _vm._v(\" 建议问题： \"), _c(\"i\", {\n    staticClass: \"el-icon-ssq-guanbi\",\n    on: {\n      click: function ($event) {\n        _vm.showSuggestion = false;\n      }\n    }\n  })]), _c(\"ul\", {\n    staticClass: \"hubble-chat__suggestion-list\"\n  }, [_vm._l(_vm.currentSuggestions, function (suggestion, i) {\n    return _c(\"li\", {\n      key: i,\n      staticClass: \"hubble-chat__suggestion-item\",\n      on: {\n        click: function ($event) {\n          return _vm.handleSendSuggestion(suggestion);\n        }\n      }\n    }, [_c(\"span\", [_vm._v(_vm._s(suggestion.suggestQuestionContent))]), _c(\"i\", {\n      staticClass: \"el-icon-ssq-fajianxiang\"\n    })]);\n  }), !_vm.currentSuggestions.length ? _c(\"li\", {\n    staticClass: \"hubble-chat__suggestion-item empty\"\n  }, [_vm.loadSuggestion ? _c(\"img\", {\n    attrs: {\n      src: require(\"views/agent/img/loading.gif\"),\n      alt: \"\"\n    }\n  }) : _c(\"span\", [_vm._v(\"没有数据\")])]) : _vm._e()], 2)])])], 1), !_vm.chatInitLoading ? _c(\"div\", {\n    staticClass: \"hubble-chat__footer\"\n  }, [_c(\"div\", {\n    class: `hubble-chat__input ${_vm.currentPrompt.displayName ? \"line-feed\" : \"\"}`,\n    on: {\n      keydown: _vm.handleKeyDown\n    }\n  }, [_c(\"ul\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showPrompt,\n      expression: \"showPrompt\"\n    }],\n    staticClass: \"hubble-chat__prompt\"\n  }, _vm._l(_vm.filterPromptList, function (plugin, index) {\n    return _c(\"li\", {\n      key: plugin.pluginName,\n      class: `hubble-chat__prompt-plugin ${index === _vm.currentPromptIndex ? \"active\" : \"\"}`,\n      on: {\n        click: function ($event) {\n          return _vm.handleSelectPlugin(plugin);\n        }\n      }\n    }, [plugin.icon ? _c(\"i\", {\n      class: plugin.icon\n    }) : _vm._e(), _c(\"div\", [_c(\"p\", [_vm._v(\" \" + _vm._s(plugin.displayName) + \" \"), _c(\"span\", [_vm._v(\"（\" + _vm._s(plugin.shortName) + \"）\")])]), _c(\"span\", [_vm._v(_vm._s(plugin.desc))])])]);\n  }), 0), _c(\"span\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: !!_vm.currentPrompt.displayName,\n      expression: \"!!currentPrompt.displayName\"\n    }],\n    ref: \"inputPlugin\",\n    staticClass: \"hubble-chat__input-plugin\"\n  }, [_vm._v(\"/ \" + _vm._s(_vm.currentPrompt.displayName))]), _c(\"el-input\", {\n    ref: \"inputItem\",\n    attrs: {\n      id: \"guide-text\",\n      type: \"textarea\",\n      autosize: {\n        minRows: 1,\n        maxRows: 6\n      },\n      resize: \"none\",\n      placeholder: \"请输入您的问题\",\n      disabled: _vm.btnDisabled\n    },\n    on: {\n      blur: _vm.inputBlur,\n      focus: function ($event) {\n        _vm.inputActive = true;\n      }\n    },\n    model: {\n      value: _vm.input,\n      callback: function ($$v) {\n        _vm.input = $$v;\n      },\n      expression: \"input\"\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"hubble-chat__footer-operate\"\n  }, [_c(\"el-tooltip\", {\n    attrs: {\n      \"open-delay\": 500,\n      effect: \"dark\",\n      content: \"建议问题\",\n      placement: \"top\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"operate-icon el-icon-ssq-Hubbletishi\",\n    attrs: {\n      id: \"guide-suggestion\"\n    },\n    on: {\n      click: _vm.handleShowSuggestion\n    }\n  })]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      disabled: _vm.btnDisabled\n    },\n    on: {\n      click: _vm.handleSendMessage\n    }\n  }, [_vm._v(\"发送\")])], 1)]) : _vm._e(), _c(\"i\", {\n    ref: \"move\",\n    staticClass: \"hubble-chat__move el-icon-ssq-yidongbiaoqian\"\n  })]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"span\", [_c(\"img\", {\n    staticClass: \"avatar\",\n    attrs: {\n      src: require(\"views/agent/img/AIBot.png\"),\n      alt: \"\"\n    }\n  }), _vm._v(\" Hubble \")]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_m", "attrs", "trigger", "model", "value", "languageSwitchVisible", "callback", "$$v", "expression", "slot", "effect", "content", "placement", "id", "class", "isEn", "on", "click", "$event", "switchEnLanguage", "_v", "handleDownloadChat", "scroll", "handleScroll", "chatInitLoading", "src", "require", "alt", "messages", "selectQuote", "handleSelectQuote", "showContinuousChat", "handleShowContinuousChat", "name", "directives", "rawName", "isContinuousChat", "continuousMessages", "loadMore", "handleLoadMoreContinuousChat", "preQuote", "clearQuote", "_s", "showSuggestion", "_l", "currentSuggestions", "suggestion", "i", "key", "handleSendSuggestion", "suggest<PERSON><PERSON>ionContent", "length", "loadSuggestion", "_e", "currentPrompt", "displayName", "keydown", "handleKeyDown", "showPrompt", "filterPromptList", "plugin", "index", "pluginName", "currentPromptIndex", "handleSelectPlugin", "icon", "shortName", "desc", "type", "autosize", "minRows", "maxRows", "resize", "placeholder", "disabled", "btnDisabled", "blur", "inputBlur", "focus", "inputActive", "input", "handleShowSuggestion", "handleSendMessage", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/chat/chatView/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"chat\", staticClass: \"hubble-chat\" }, [\n    _c(\"div\", { staticClass: \"hubble-chat__header\" }, [\n      _vm._m(0),\n      _c(\n        \"div\",\n        { staticClass: \"operate\" },\n        [\n          _c(\n            \"el-popover\",\n            {\n              attrs: { trigger: \"click\", \"popper-class\": \"download-popover\" },\n              model: {\n                value: _vm.languageSwitchVisible,\n                callback: function ($$v) {\n                  _vm.languageSwitchVisible = $$v\n                },\n                expression: \"languageSwitchVisible\",\n              },\n            },\n            [\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    slot: \"reference\",\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"输出语言切换\",\n                    placement: \"top\",\n                  },\n                  slot: \"reference\",\n                },\n                [\n                  _c(\"i\", {\n                    staticClass: \"el-icon-ssq-diqiu\",\n                    attrs: { id: \"guide-lang\" },\n                  }),\n                ]\n              ),\n              _c(\"ul\", [\n                _c(\n                  \"li\",\n                  {\n                    class: _vm.isEn ? \"\" : \"active\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.switchEnLanguage(false)\n                      },\n                    },\n                  },\n                  [_vm._v(\"中文语言\")]\n                ),\n                _c(\n                  \"li\",\n                  {\n                    class: _vm.isEn ? \"active\" : \"\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.switchEnLanguage(true)\n                      },\n                    },\n                  },\n                  [_vm._v(\"英文语言\")]\n                ),\n              ]),\n            ],\n            1\n          ),\n          _c(\n            \"el-popover\",\n            { attrs: { trigger: \"click\", \"popper-class\": \"download-popover\" } },\n            [\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    slot: \"reference\",\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"文档下载\",\n                    placement: \"top\",\n                  },\n                  slot: \"reference\",\n                },\n                [_c(\"i\", { staticClass: \"el-icon-ssq--bs-xiazai\" })]\n              ),\n              _c(\"ul\", [\n                _c(\n                  \"li\",\n                  {\n                    on: {\n                      click: function ($event) {\n                        return _vm.handleDownloadChat(false)\n                      },\n                    },\n                  },\n                  [_vm._v(\"源文件\")]\n                ),\n                _c(\"li\", { on: { click: _vm.handleDownloadChat } }, [\n                  _vm._v(\"源文件 (携带对话)\"),\n                ]),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ]),\n    _c(\n      \"div\",\n      { staticClass: \"hubble-chat__body\", on: { scroll: _vm.handleScroll } },\n      [\n        _vm.chatInitLoading\n          ? _c(\"div\", { staticClass: \"hubble-chat__loading\" }, [\n              _c(\"img\", {\n                attrs: {\n                  src: require(\"views/agent/img/loading2.gif\"),\n                  alt: \"\",\n                },\n              }),\n              _c(\"br\"),\n              _c(\"span\", [_vm._v(\"文档解析中\")]),\n            ])\n          : [\n              _c(\"ChatList\", {\n                attrs: { messages: _vm.messages },\n                on: {\n                  selectQuote: _vm.handleSelectQuote,\n                  showContinuousChat: _vm.handleShowContinuousChat,\n                },\n              }),\n              _c(\"transition\", { attrs: { name: \"fade\" } }, [\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.showContinuousChat,\n                        expression: \"showContinuousChat\",\n                      },\n                    ],\n                    staticClass: \"continuous-chat\",\n                  },\n                  [\n                    _c(\"ChatList\", {\n                      attrs: {\n                        isContinuousChat: \"\",\n                        messages: _vm.continuousMessages,\n                      },\n                      on: {\n                        selectQuote: _vm.handleSelectQuote,\n                        loadMore: _vm.handleLoadMoreContinuousChat,\n                      },\n                    }),\n                    _c(\"div\", { staticClass: \"continuous-exit\" }, [\n                      _c(\"span\", [_vm._v(\"连续对话模式：\")]),\n                      _c(\n                        \"span\",\n                        {\n                          staticClass: \"exit\",\n                          on: {\n                            click: function ($event) {\n                              _vm.showContinuousChat = false\n                            },\n                          },\n                        },\n                        [_vm._v(\"退出\")]\n                      ),\n                    ]),\n                  ],\n                  1\n                ),\n              ]),\n            ],\n      ],\n      2\n    ),\n    _c(\n      \"div\",\n      { staticClass: \"hubble-chat__dialog\" },\n      [\n        _c(\"transition\", { attrs: { name: \"slide\" } }, [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: !!_vm.preQuote.content,\n                  expression: \"!!preQuote.content\",\n                },\n              ],\n              staticClass: \"hubble-chat__quote\",\n            },\n            [\n              _c(\"div\", { staticClass: \"hubble-chat__quote-header\" }, [\n                _c(\"span\", [_vm._v(\"引用内容：\")]),\n                _c(\"i\", {\n                  staticClass: \"el-icon-ssq-guanbi\",\n                  on: { click: _vm.clearQuote },\n                }),\n              ]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"hubble-chat__quote-body chat-quote\",\n                  on: {\n                    click: function ($event) {\n                      return _vm.handleSelectQuote(_vm.preQuote)\n                    },\n                  },\n                },\n                [_vm._v(_vm._s(_vm.preQuote.content))]\n              ),\n            ]\n          ),\n        ]),\n        _c(\"transition\", { attrs: { name: \"slide\" } }, [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.showSuggestion,\n                  expression: \"showSuggestion\",\n                },\n              ],\n              class: `hubble-chat__suggestion ${\n                !_vm.preQuote.content ? \"without-quote\" : \"\"\n              }`,\n            },\n            [\n              _c(\"div\", { staticClass: \"hubble-chat__suggestion-header\" }, [\n                _c(\"i\", { staticClass: \"el-icon-ssq-gaoliangtishiH5\" }),\n                _vm._v(\" 建议问题： \"),\n                _c(\"i\", {\n                  staticClass: \"el-icon-ssq-guanbi\",\n                  on: {\n                    click: function ($event) {\n                      _vm.showSuggestion = false\n                    },\n                  },\n                }),\n              ]),\n              _c(\n                \"ul\",\n                { staticClass: \"hubble-chat__suggestion-list\" },\n                [\n                  _vm._l(_vm.currentSuggestions, function (suggestion, i) {\n                    return _c(\n                      \"li\",\n                      {\n                        key: i,\n                        staticClass: \"hubble-chat__suggestion-item\",\n                        on: {\n                          click: function ($event) {\n                            return _vm.handleSendSuggestion(suggestion)\n                          },\n                        },\n                      },\n                      [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(suggestion.suggestQuestionContent)),\n                        ]),\n                        _c(\"i\", { staticClass: \"el-icon-ssq-fajianxiang\" }),\n                      ]\n                    )\n                  }),\n                  !_vm.currentSuggestions.length\n                    ? _c(\n                        \"li\",\n                        { staticClass: \"hubble-chat__suggestion-item empty\" },\n                        [\n                          _vm.loadSuggestion\n                            ? _c(\"img\", {\n                                attrs: {\n                                  src: require(\"views/agent/img/loading.gif\"),\n                                  alt: \"\",\n                                },\n                              })\n                            : _c(\"span\", [_vm._v(\"没有数据\")]),\n                        ]\n                      )\n                    : _vm._e(),\n                ],\n                2\n              ),\n            ]\n          ),\n        ]),\n      ],\n      1\n    ),\n    !_vm.chatInitLoading\n      ? _c(\"div\", { staticClass: \"hubble-chat__footer\" }, [\n          _c(\n            \"div\",\n            {\n              class: `hubble-chat__input ${\n                _vm.currentPrompt.displayName ? \"line-feed\" : \"\"\n              }`,\n              on: { keydown: _vm.handleKeyDown },\n            },\n            [\n              _c(\n                \"ul\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.showPrompt,\n                      expression: \"showPrompt\",\n                    },\n                  ],\n                  staticClass: \"hubble-chat__prompt\",\n                },\n                _vm._l(_vm.filterPromptList, function (plugin, index) {\n                  return _c(\n                    \"li\",\n                    {\n                      key: plugin.pluginName,\n                      class: `hubble-chat__prompt-plugin ${\n                        index === _vm.currentPromptIndex ? \"active\" : \"\"\n                      }`,\n                      on: {\n                        click: function ($event) {\n                          return _vm.handleSelectPlugin(plugin)\n                        },\n                      },\n                    },\n                    [\n                      plugin.icon ? _c(\"i\", { class: plugin.icon }) : _vm._e(),\n                      _c(\"div\", [\n                        _c(\"p\", [\n                          _vm._v(\" \" + _vm._s(plugin.displayName) + \" \"),\n                          _c(\"span\", [\n                            _vm._v(\"（\" + _vm._s(plugin.shortName) + \"）\"),\n                          ]),\n                        ]),\n                        _c(\"span\", [_vm._v(_vm._s(plugin.desc))]),\n                      ]),\n                    ]\n                  )\n                }),\n                0\n              ),\n              _c(\n                \"span\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: !!_vm.currentPrompt.displayName,\n                      expression: \"!!currentPrompt.displayName\",\n                    },\n                  ],\n                  ref: \"inputPlugin\",\n                  staticClass: \"hubble-chat__input-plugin\",\n                },\n                [_vm._v(\"/ \" + _vm._s(_vm.currentPrompt.displayName))]\n              ),\n              _c(\"el-input\", {\n                ref: \"inputItem\",\n                attrs: {\n                  id: \"guide-text\",\n                  type: \"textarea\",\n                  autosize: { minRows: 1, maxRows: 6 },\n                  resize: \"none\",\n                  placeholder: \"请输入您的问题\",\n                  disabled: _vm.btnDisabled,\n                },\n                on: {\n                  blur: _vm.inputBlur,\n                  focus: function ($event) {\n                    _vm.inputActive = true\n                  },\n                },\n                model: {\n                  value: _vm.input,\n                  callback: function ($$v) {\n                    _vm.input = $$v\n                  },\n                  expression: \"input\",\n                },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"hubble-chat__footer-operate\" },\n            [\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"建议问题\",\n                    placement: \"top\",\n                  },\n                },\n                [\n                  _c(\"i\", {\n                    staticClass: \"operate-icon el-icon-ssq-Hubbletishi\",\n                    attrs: { id: \"guide-suggestion\" },\n                    on: { click: _vm.handleShowSuggestion },\n                  }),\n                ]\n              ),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\", disabled: _vm.btnDisabled },\n                  on: { click: _vm.handleSendMessage },\n                },\n                [_vm._v(\"发送\")]\n              ),\n            ],\n            1\n          ),\n        ])\n      : _vm._e(),\n    _c(\"i\", {\n      ref: \"move\",\n      staticClass: \"hubble-chat__move el-icon-ssq-yidongbiaoqian\",\n    }),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"span\", [\n      _c(\"img\", {\n        staticClass: \"avatar\",\n        attrs: { src: require(\"views/agent/img/AIBot.png\"), alt: \"\" },\n      }),\n      _vm._v(\" Hubble \"),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,MAAM;IAAEC,WAAW,EAAE;EAAc,CAAC,EAAE,CAC5DH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDJ,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC,EACTJ,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEH,EAAE,CACA,YAAY,EACZ;IACEK,KAAK,EAAE;MAAEC,OAAO,EAAE,OAAO;MAAE,cAAc,EAAE;IAAmB,CAAC;IAC/DC,KAAK,EAAE;MACLC,KAAK,EAAET,GAAG,CAACU,qBAAqB;MAChCC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBZ,GAAG,CAACU,qBAAqB,GAAGE,GAAG;MACjC,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEZ,EAAE,CACA,YAAY,EACZ;IACEK,KAAK,EAAE;MACLQ,IAAI,EAAE,WAAW;MACjB,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE;IACb,CAAC;IACDH,IAAI,EAAE;EACR,CAAC,EACD,CACEb,EAAE,CAAC,GAAG,EAAE;IACNG,WAAW,EAAE,mBAAmB;IAChCE,KAAK,EAAE;MAAEY,EAAE,EAAE;IAAa;EAC5B,CAAC,CAAC,CAEN,CAAC,EACDjB,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEkB,KAAK,EAAEnB,GAAG,CAACoB,IAAI,GAAG,EAAE,GAAG,QAAQ;IAC/BC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAACwB,gBAAgB,CAAC,KAAK,CAAC;MACpC;IACF;EACF,CAAC,EACD,CAACxB,GAAG,CAACyB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDxB,EAAE,CACA,IAAI,EACJ;IACEkB,KAAK,EAAEnB,GAAG,CAACoB,IAAI,GAAG,QAAQ,GAAG,EAAE;IAC/BC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAACwB,gBAAgB,CAAC,IAAI,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAACxB,GAAG,CAACyB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,EACDxB,EAAE,CACA,YAAY,EACZ;IAAEK,KAAK,EAAE;MAAEC,OAAO,EAAE,OAAO;MAAE,cAAc,EAAE;IAAmB;EAAE,CAAC,EACnE,CACEN,EAAE,CACA,YAAY,EACZ;IACEK,KAAK,EAAE;MACLQ,IAAI,EAAE,WAAW;MACjB,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE;IACb,CAAC;IACDH,IAAI,EAAE;EACR,CAAC,EACD,CAACb,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAAyB,CAAC,CAAC,CACrD,CAAC,EACDH,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJ;IACEoB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAAC0B,kBAAkB,CAAC,KAAK,CAAC;MACtC;IACF;EACF,CAAC,EACD,CAAC1B,GAAG,CAACyB,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,EACDxB,EAAE,CAAC,IAAI,EAAE;IAAEoB,EAAE,EAAE;MAAEC,KAAK,EAAEtB,GAAG,CAAC0B;IAAmB;EAAE,CAAC,EAAE,CAClD1B,GAAG,CAACyB,EAAE,CAAC,YAAY,CAAC,CACrB,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFxB,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE,mBAAmB;IAAEiB,EAAE,EAAE;MAAEM,MAAM,EAAE3B,GAAG,CAAC4B;IAAa;EAAE,CAAC,EACtE,CACE5B,GAAG,CAAC6B,eAAe,GACf5B,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLwB,GAAG,EAAEC,OAAO,CAAC,8BAA8B,CAAC;MAC5CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACF/B,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACyB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,GACF,CACExB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MAAE2B,QAAQ,EAAEjC,GAAG,CAACiC;IAAS,CAAC;IACjCZ,EAAE,EAAE;MACFa,WAAW,EAAElC,GAAG,CAACmC,iBAAiB;MAClCC,kBAAkB,EAAEpC,GAAG,CAACqC;IAC1B;EACF,CAAC,CAAC,EACFpC,EAAE,CAAC,YAAY,EAAE;IAAEK,KAAK,EAAE;MAAEgC,IAAI,EAAE;IAAO;EAAE,CAAC,EAAE,CAC5CrC,EAAE,CACA,KAAK,EACL;IACEsC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,MAAM;MACZE,OAAO,EAAE,QAAQ;MACjB/B,KAAK,EAAET,GAAG,CAACoC,kBAAkB;MAC7BvB,UAAU,EAAE;IACd,CAAC,CACF;IACDT,WAAW,EAAE;EACf,CAAC,EACD,CACEH,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLmC,gBAAgB,EAAE,EAAE;MACpBR,QAAQ,EAAEjC,GAAG,CAAC0C;IAChB,CAAC;IACDrB,EAAE,EAAE;MACFa,WAAW,EAAElC,GAAG,CAACmC,iBAAiB;MAClCQ,QAAQ,EAAE3C,GAAG,CAAC4C;IAChB;EACF,CAAC,CAAC,EACF3C,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACyB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC/BxB,EAAE,CACA,MAAM,EACN;IACEG,WAAW,EAAE,MAAM;IACnBiB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBvB,GAAG,CAACoC,kBAAkB,GAAG,KAAK;MAChC;IACF;EACF,CAAC,EACD,CAACpC,GAAG,CAACyB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,CACN,EACD,CACF,CAAC,EACDxB,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEH,EAAE,CAAC,YAAY,EAAE;IAAEK,KAAK,EAAE;MAAEgC,IAAI,EAAE;IAAQ;EAAE,CAAC,EAAE,CAC7CrC,EAAE,CACA,KAAK,EACL;IACEsC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,MAAM;MACZE,OAAO,EAAE,QAAQ;MACjB/B,KAAK,EAAE,CAAC,CAACT,GAAG,CAAC6C,QAAQ,CAAC7B,OAAO;MAC7BH,UAAU,EAAE;IACd,CAAC,CACF;IACDT,WAAW,EAAE;EACf,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACyB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7BxB,EAAE,CAAC,GAAG,EAAE;IACNG,WAAW,EAAE,oBAAoB;IACjCiB,EAAE,EAAE;MAAEC,KAAK,EAAEtB,GAAG,CAAC8C;IAAW;EAC9B,CAAC,CAAC,CACH,CAAC,EACF7C,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,oCAAoC;IACjDiB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOvB,GAAG,CAACmC,iBAAiB,CAACnC,GAAG,CAAC6C,QAAQ,CAAC;MAC5C;IACF;EACF,CAAC,EACD,CAAC7C,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC+C,EAAE,CAAC/C,GAAG,CAAC6C,QAAQ,CAAC7B,OAAO,CAAC,CAAC,CACvC,CAAC,CAEL,CAAC,CACF,CAAC,EACFf,EAAE,CAAC,YAAY,EAAE;IAAEK,KAAK,EAAE;MAAEgC,IAAI,EAAE;IAAQ;EAAE,CAAC,EAAE,CAC7CrC,EAAE,CACA,KAAK,EACL;IACEsC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,MAAM;MACZE,OAAO,EAAE,QAAQ;MACjB/B,KAAK,EAAET,GAAG,CAACgD,cAAc;MACzBnC,UAAU,EAAE;IACd,CAAC,CACF;IACDM,KAAK,EAAE,2BACL,CAACnB,GAAG,CAAC6C,QAAQ,CAAC7B,OAAO,GAAG,eAAe,GAAG,EAAE;EAEhD,CAAC,EACD,CACEf,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAiC,CAAC,EAAE,CAC3DH,EAAE,CAAC,GAAG,EAAE;IAAEG,WAAW,EAAE;EAA8B,CAAC,CAAC,EACvDJ,GAAG,CAACyB,EAAE,CAAC,SAAS,CAAC,EACjBxB,EAAE,CAAC,GAAG,EAAE;IACNG,WAAW,EAAE,oBAAoB;IACjCiB,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBvB,GAAG,CAACgD,cAAc,GAAG,KAAK;MAC5B;IACF;EACF,CAAC,CAAC,CACH,CAAC,EACF/C,EAAE,CACA,IAAI,EACJ;IAAEG,WAAW,EAAE;EAA+B,CAAC,EAC/C,CACEJ,GAAG,CAACiD,EAAE,CAACjD,GAAG,CAACkD,kBAAkB,EAAE,UAAUC,UAAU,EAAEC,CAAC,EAAE;IACtD,OAAOnD,EAAE,CACP,IAAI,EACJ;MACEoD,GAAG,EAAED,CAAC;MACNhD,WAAW,EAAE,8BAA8B;MAC3CiB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOvB,GAAG,CAACsD,oBAAoB,CAACH,UAAU,CAAC;QAC7C;MACF;IACF,CAAC,EACD,CACElD,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC+C,EAAE,CAACI,UAAU,CAACI,sBAAsB,CAAC,CAAC,CAClD,CAAC,EACFtD,EAAE,CAAC,GAAG,EAAE;MAAEG,WAAW,EAAE;IAA0B,CAAC,CAAC,CAEvD,CAAC;EACH,CAAC,CAAC,EACF,CAACJ,GAAG,CAACkD,kBAAkB,CAACM,MAAM,GAC1BvD,EAAE,CACA,IAAI,EACJ;IAAEG,WAAW,EAAE;EAAqC,CAAC,EACrD,CACEJ,GAAG,CAACyD,cAAc,GACdxD,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLwB,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAC;MAC3CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,GACF/B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACyB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAEpC,CAAC,GACDzB,GAAG,CAAC0D,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CAEL,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,EACD,CAAC1D,GAAG,CAAC6B,eAAe,GAChB5B,EAAE,CAAC,KAAK,EAAE;IAAEG,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,EAAE,CACA,KAAK,EACL;IACEkB,KAAK,EAAE,sBACLnB,GAAG,CAAC2D,aAAa,CAACC,WAAW,GAAG,WAAW,GAAG,EAAE,EAChD;IACFvC,EAAE,EAAE;MAAEwC,OAAO,EAAE7D,GAAG,CAAC8D;IAAc;EACnC,CAAC,EACD,CACE7D,EAAE,CACA,IAAI,EACJ;IACEsC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,MAAM;MACZE,OAAO,EAAE,QAAQ;MACjB/B,KAAK,EAAET,GAAG,CAAC+D,UAAU;MACrBlD,UAAU,EAAE;IACd,CAAC,CACF;IACDT,WAAW,EAAE;EACf,CAAC,EACDJ,GAAG,CAACiD,EAAE,CAACjD,GAAG,CAACgE,gBAAgB,EAAE,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACpD,OAAOjE,EAAE,CACP,IAAI,EACJ;MACEoD,GAAG,EAAEY,MAAM,CAACE,UAAU;MACtBhD,KAAK,EAAE,8BACL+C,KAAK,KAAKlE,GAAG,CAACoE,kBAAkB,GAAG,QAAQ,GAAG,EAAE,EAChD;MACF/C,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOvB,GAAG,CAACqE,kBAAkB,CAACJ,MAAM,CAAC;QACvC;MACF;IACF,CAAC,EACD,CACEA,MAAM,CAACK,IAAI,GAAGrE,EAAE,CAAC,GAAG,EAAE;MAAEkB,KAAK,EAAE8C,MAAM,CAACK;IAAK,CAAC,CAAC,GAAGtE,GAAG,CAAC0D,EAAE,CAAC,CAAC,EACxDzD,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACyB,EAAE,CAAC,GAAG,GAAGzB,GAAG,CAAC+C,EAAE,CAACkB,MAAM,CAACL,WAAW,CAAC,GAAG,GAAG,CAAC,EAC9C3D,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACyB,EAAE,CAAC,GAAG,GAAGzB,GAAG,CAAC+C,EAAE,CAACkB,MAAM,CAACM,SAAS,CAAC,GAAG,GAAG,CAAC,CAC7C,CAAC,CACH,CAAC,EACFtE,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC+C,EAAE,CAACkB,MAAM,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC,CAC1C,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDvE,EAAE,CACA,MAAM,EACN;IACEsC,UAAU,EAAE,CACV;MACED,IAAI,EAAE,MAAM;MACZE,OAAO,EAAE,QAAQ;MACjB/B,KAAK,EAAE,CAAC,CAACT,GAAG,CAAC2D,aAAa,CAACC,WAAW;MACtC/C,UAAU,EAAE;IACd,CAAC,CACF;IACDV,GAAG,EAAE,aAAa;IAClBC,WAAW,EAAE;EACf,CAAC,EACD,CAACJ,GAAG,CAACyB,EAAE,CAAC,IAAI,GAAGzB,GAAG,CAAC+C,EAAE,CAAC/C,GAAG,CAAC2D,aAAa,CAACC,WAAW,CAAC,CAAC,CACvD,CAAC,EACD3D,EAAE,CAAC,UAAU,EAAE;IACbE,GAAG,EAAE,WAAW;IAChBG,KAAK,EAAE;MACLY,EAAE,EAAE,YAAY;MAChBuD,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC;MACpCC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,SAAS;MACtBC,QAAQ,EAAE/E,GAAG,CAACgF;IAChB,CAAC;IACD3D,EAAE,EAAE;MACF4D,IAAI,EAAEjF,GAAG,CAACkF,SAAS;MACnBC,KAAK,EAAE,SAAAA,CAAU5D,MAAM,EAAE;QACvBvB,GAAG,CAACoF,WAAW,GAAG,IAAI;MACxB;IACF,CAAC;IACD5E,KAAK,EAAE;MACLC,KAAK,EAAET,GAAG,CAACqF,KAAK;MAChB1E,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBZ,GAAG,CAACqF,KAAK,GAAGzE,GAAG;MACjB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDZ,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAA8B,CAAC,EAC9C,CACEH,EAAE,CACA,YAAY,EACZ;IACEK,KAAK,EAAE;MACL,YAAY,EAAE,GAAG;MACjBS,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE;IACb;EACF,CAAC,EACD,CACEhB,EAAE,CAAC,GAAG,EAAE;IACNG,WAAW,EAAE,sCAAsC;IACnDE,KAAK,EAAE;MAAEY,EAAE,EAAE;IAAmB,CAAC;IACjCG,EAAE,EAAE;MAAEC,KAAK,EAAEtB,GAAG,CAACsF;IAAqB;EACxC,CAAC,CAAC,CAEN,CAAC,EACDrF,EAAE,CACA,WAAW,EACX;IACEK,KAAK,EAAE;MAAEmE,IAAI,EAAE,SAAS;MAAEM,QAAQ,EAAE/E,GAAG,CAACgF;IAAY,CAAC;IACrD3D,EAAE,EAAE;MAAEC,KAAK,EAAEtB,GAAG,CAACuF;IAAkB;EACrC,CAAC,EACD,CAACvF,GAAG,CAACyB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,GACFzB,GAAG,CAAC0D,EAAE,CAAC,CAAC,EACZzD,EAAE,CAAC,GAAG,EAAE;IACNE,GAAG,EAAE,MAAM;IACXC,WAAW,EAAE;EACf,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIoF,eAAe,GAAG,CACpB,YAAY;EACV,IAAIxF,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,MAAM,EAAE,CAChBA,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE,QAAQ;IACrBE,KAAK,EAAE;MAAEwB,GAAG,EAAEC,OAAO,CAAC,2BAA2B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC9D,CAAC,CAAC,EACFhC,GAAG,CAACyB,EAAE,CAAC,UAAU,CAAC,CACnB,CAAC;AACJ,CAAC,CACF;AACD1B,MAAM,CAAC0F,aAAa,GAAG,IAAI;AAE3B,SAAS1F,MAAM,EAAEyF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}