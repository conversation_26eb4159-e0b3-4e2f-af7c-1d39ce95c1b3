{"ast": null, "code": "// 语言为中文时的文案\nimport utils from './module/utils/utils-zh.js';\nimport mixin from './module/mixins/mixins-zh.js';\nimport components from './module/components/components-ja.js';\nimport docList from './module/docList/docList-ja.js';\nimport console from './module/console/console-zh.js';\nimport userCentral from './module/usercentral/usercentral-zh.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-ja.js';\nimport entAuth from './module/entAuth/entAuth-zh.js';\nimport consts from './module/consts/ja.js';\nimport docTranslation from './module/docTranslation/docTranslation-ja.js';\nexport default {\n  ...utils,\n  ...mixin,\n  ...components,\n  ...docList,\n  ...docTranslation,\n  footerAd: {\n    title: 'ジャンププロンプト',\n    content1: 'アクセスしたページは、サードパーティのプロモーションページにジャンプします。',\n    content2: '続行しますか？',\n    bankContent: '即将进入宁波银行\"容易贷\"企业贷款介绍页面',\n    bankTip1: '让宁波银行主动给我打电话',\n    bankTip2: '向我发送一条短信，介绍如何办理',\n    bankFooter: '加宁波银行专属客服，一对一服务我',\n    cancel: 'キャンセル',\n    continue: '続行'\n  },\n  commonFooter: {\n    record: 'ICP主体企業登録番号：浙ICP備********号',\n    hubbleRecordId: '网信算备：330106973391501230011',\n    openPlatform: 'オープンプラットフォーム',\n    aboutBestSign: '弊社について',\n    contact: 'お問い合わせ先',\n    recruitment: '採用情報',\n    help: 'ヘルプセンター',\n    copyright: '無断転載禁止',\n    company: 'ベストサイン・ジャパン株式会社',\n    ssqLogo: 'ベストサイン ボトムバーロゴ',\n    provideTip: '電子契約サービスは',\n    ssq: 'ベストサインにより',\n    provide: '提供',\n    signHotline: '契約サービスホットライン',\n    langSwitch: '言語'\n  },\n  login: {\n    pswLogin: '密码登录',\n    usePswLogin: '使用密码登录',\n    verifyLogin: '验证码登录',\n    useVerifyLogin: '使用验证码登录',\n    scanLogin: '扫码登录',\n    scanFailure: '二维码已失效,请刷新重试',\n    scanSuccess: '扫码成功',\n    scanLoginTip: '请使用上上签APP扫一扫登录',\n    appLoginTip: '请在上上签APP中点击登录',\n    downloadApp: '下载上上签APP',\n    forgetPsw: '忘记密码',\n    login: '登录',\n    noAccount: '没有账号',\n    registerNow: '马上注册',\n    accountPlaceholder: '请输入手机或邮箱',\n    passwordPlaceholder: '请输入登录密码',\n    pictureVer: '请填写图片中的内容',\n    verifyCodePlaceholder: '请输入6位验证码',\n    getVerifyCode: '認証コードを取得する',\n    noRegister: '尚未注册',\n    or: '或',\n    errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',\n    errAccountOrPwdTip2: '你输入的密码和账号不匹配',\n    errEmailOrTel: '请输入正确的邮箱或手机号!',\n    errPwd: '请输入正确的密码!',\n    verCodeFormatErr: '验证码错误',\n    grapVerCodeErr: '图形验证码错误',\n    grapVerCodeFormatErr: '图形验证码格式错误',\n    lackAccount: '请填写账号后再获取',\n    lackGrapCode: '请先填写图形验证码',\n    getVerCodeTip: '请获取验证码',\n    loginView: '登录并查看合同',\n    regView: '注册并查看合同',\n    takeViewBtn: '登录并签署',\n    resendCode: '重新获取',\n    regTip: '填写正确的验证码后上上签将为您创建账号',\n    haveRead: '我已阅读并同意',\n    bestsignAgreement: '上上签服务协议',\n    and: '和',\n    digitalCertificateAgreement: '数字证书使用协议',\n    privacyPolicy: '隐私政策',\n    sendSuc: '发送成功',\n    lackVerCode: '请先输入验证码',\n    lackPsw: '请先输入密码',\n    notMatch: '您输入的密码和账号不匹配',\n    cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',\n    wrongLink: '非法链接',\n    footerTips: '电子签约服务由<span>上上签</span>提供',\n    bestSign: '上上签',\n    bestSignDescription: '电子签约行业领跑者',\n    /** 忘记密码 /forgotPassword start */\n    forgetPswStep: '验证注册账号 | 重新设置密码',\n    pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',\n    accountInput: '账号 | 请填写您的账号',\n    smsCodeInput: '验证码 | 获取验证码',\n    haveRegistereLoginNow: '我已注册， | 马上登录',\n    nextStep: '下一步 | 提交',\n    setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',\n    passwordResetSucceeded: '密码重置成功!',\n    /** 忘记密码 /forgotPassword end */\n    accountNotRegistered: '账号未注册',\n    loginAndDownload: '登录并下载合同',\n    registerAndDownload: '注册并下载合同',\n    inputPhone: '请输入手机号',\n    readContract: '读取合同',\n    errorPhone: '手机格式错误',\n    companyCert: '进行企业认证',\n    regAndCompanyCert: '注册并进行企业认证'\n  },\n  ...sign,\n  handwrite: {\n    title: '手書きサイン',\n    picSubmitTip: '署名画像が正常に提出されました',\n    settingDefault: 'デフォルト署名に設定',\n    replaceAllSignature: '全ての署名に使われます',\n    replaceAllSeal: '全てのスタンプに使います',\n    canUseSeal: '私の印章',\n    applyForSeal: '印章使用申請',\n    moreTip: 'あなたの手書き署名はデフォルト署名として保存され、契約書の署名にのみ使用されます。管理方法：【ユーザーセンター->署名管理】',\n    uploadPic: '画像をアップロード',\n    use: '使う',\n    clickExtend: '右矢印をクリックして手書きエリアを拡大',\n    upload: '署名画像をアップロード',\n    uploadTip1: 'ヒント：署名画像をアップロードする際は、署名が画像全体を満たすようにしてください',\n    uploadTip2: '署名は濃い色または黒色の文字でお願いします',\n    rewrite: '書き直し',\n    cancel: '取り消し',\n    confirm: '使用',\n    upgradeBrowser: 'お使いのブラウザはキャンバス署名機能に対応していません。ブラウザを更新してください。',\n    submitTip: '手描きの署名を提出成功',\n    title2: '署名を手書きしてください',\n    QRCode: 'QRコード読み取りで署名',\n    needWrite: '正しい名前を手書きしてください！',\n    needRewrite: '文字が判読できません。書き直してください',\n    ok: '確定',\n    clearTips: '判読可能な署名をお書きください',\n    isBlank: 'キャンバスが空です。署名を描いてから送信してください！',\n    success: '署名が正常に送信されました',\n    signNotMatch: '楷書で署名してください。身分証明書の記載と一致する必要があります。',\n    signNotMatchExact: '第{numList}文字の認識に失敗しました。楷書で署名してください。身分証明書の記載と一致する必要があります。',\n    msg: {\n      successToUser: '新しい署名を設定しました。ウェブサイトで「保存」ボタンをクリックしてください。',\n      successToSign: '新しい署名が有効になりました。契約書の署名ページをご確認ください。',\n      cantGet: '署名を取得できません。他のブラウザをお試しください。'\n    }\n  },\n  common: {\n    aboutBestSign: '关于公司',\n    contact: '联系我们',\n    recruitment: '诚聘英才',\n    copyright: '版权所有',\n    advice: '咨询建议',\n    notEmpty: '空にすることはできません!',\n    enter6to18n: '请输入6-18位数字、大小写字母',\n    ssqDes: '电子签约云平台领导者',\n    openPlatform: '开放平台',\n    company: '杭州尚尚签网络科技有限公司',\n    help: '帮助中心',\n    errEmailOrTel: '请输入正确的邮箱或手机号!',\n    verCodeFormatErr: '验证码错误',\n    signPwdType: '6桁の数字を入力してください',\n    enterActualEntName: '请填写真实的企业名称',\n    enterCorrectName: '请输入正确的姓名',\n    enterCorrectPhoneNum: '请输入正确的手机号',\n    enterCorrectEmail: '请输入正确的邮箱',\n    imgCodeErr: '图形验证码错误',\n    enterCorrectIdNum: '请输入正确的证件号码',\n    enterCorrectFormat: '请输入正确的格式',\n    enterCorrectDateFormat: '请输入正确的日期格式'\n  },\n  entAuth: {\n    ...entAuth,\n    entCertification: '企业实名认证',\n    subBaseInfo: '提交基本信息',\n    corDocuments: '企业证件',\n    license: '营业执照',\n    upload: 'アップロードをクリック',\n    uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',\n    hi: '你好',\n    exit: '退出',\n    help: '帮助',\n    hotline: '服务热线',\n    acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',\n    comfirmSubmit: '确认提交',\n    cerficated: '认证完成',\n    serialNumber: '证书序列号',\n    validity: '有效期',\n    entName: '企业名称',\n    nationalNo: '国家注册号',\n    corporationName: '法定代表人姓名',\n    city: '所在城市',\n    entCertificate: '企业实名证书',\n    certificationAuthority: '证书颁发机构',\n    bestsignPlatform: '上上签电子签约云平台',\n    notIssued: '未发放',\n    date: '{year}年{month}月{day}日',\n    congratulations: '恭喜您，成功完成企业实名认证',\n    continue: '继续',\n    rejectMessage: '由于如下原因，资料审核不通过，请核对',\n    recertification: '重新认证',\n    waitMessage: '客服将在一个工作日内完成审核，请耐心等待'\n  },\n  personalAuth: {\n    info: '提示',\n    submitPicError: '请上传照片后再使用'\n  },\n  home: {\n    ...home,\n    home: '首页',\n    contractDrafting: '合同起草',\n    contractManagement: '合同管理',\n    userCenter: '用户中心',\n    service: '服务',\n    enterpriseConsole: '企业控制台',\n    groupConsole: '集团控制台',\n    startSigning: '契約書を送る',\n    contractType: '发送普通合同 | 发送模板合同 ',\n    sendContract: '发送合同',\n    shortcuts: '快捷入口 | 没有任何文件快捷入口',\n    setting: '立即设置 | 设置更多快捷入口',\n    signNum: '签发量月度报表',\n    contractNum: '合同发送量 | 合同签署量',\n    contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',\n    type: '企业 | 个人',\n    basicInformation: '基本信息',\n    more: '更多',\n    certified: '已认证 | 未认证',\n    account: '账号',\n    time: '创建时间 |注册时间',\n    day: '日 | 月',\n    sendContractNum: '发送量 | 签署量',\n    num: '份',\n    realName: '立即企业实名 | 立即个人实名',\n    update: '产品最新公告',\n    mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',\n    countDes: {\n      1: '可发：对公合同',\n      2: '份',\n      3: '对私合同',\n      4: '份'\n    },\n    chargeNow: '立即充值',\n    myRechargeOrder: '我的充值订单',\n    statusTip: {\n      1: '需要我操作',\n      2: '需要他人签署',\n      3: '即将截止签约',\n      4: '签约完成'\n    },\n    useTemplate: '使用模板',\n    useLocalFile: '上传本地文件',\n    enterEnterpriseName: '请输入企业名称'\n  },\n  docDetail: {\n    canNotOperateTip: '无法{operate}合同',\n    shareSignLink: '分享签署链接',\n    faceSign: '刷脸签署',\n    faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',\n    contractRecipient: '合同收件方',\n    personalOperateLog: '个人合同操作日志',\n    recordDialog: {\n      date: '日期',\n      user: '用户',\n      operate: '操作',\n      view: '查看',\n      download: '下载'\n    },\n    remarks: '备注',\n    operateRecords: '操作记录',\n    borrowingRecords: '借阅记录',\n    currentHolder: '当前持有人',\n    currentEnterprise: '当前企业',\n    companyInterOperationLog: '公司内部操作日志',\n    receiverMap: {\n      sender: '合同发件人',\n      signer: '合同接收人',\n      ccUser: '合同抄送人'\n    },\n    downloadCode: '合同下载码',\n    noTagToAddHint: '还没有标签，请前往企业控制台添加',\n    requireFieldNotAllowEmpty: '必填项不能为空',\n    modifySuccess: '修改成功',\n    uncategorized: '未分类',\n    notAllowModifyContractType: '{type}中的合同不允许修改合同类型',\n    setTag: '设置标签',\n    contractTag: '合同标签',\n    plsInput: '请输入',\n    plsInputCompanyInternalNum: '请输入公司内部编号',\n    companyInternalNum: '公司内部编号',\n    none: '无',\n    plsSelect: '请选择',\n    modify: '修改',\n    contractDetailInfo: '合同详细信息',\n    slideContentTip: {\n      signNotice: '签约须知',\n      contractAncillaryInformation: '合同附属资料',\n      content: '内容',\n      document: '文档'\n    },\n    downloadDepositConfirmTip: {\n      title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',\n      hint: '提示',\n      confrim: '继续下载',\n      cancel: '取消'\n    },\n    downloadTip: {\n      title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',\n      hint: '提示',\n      confirm: '确定',\n      cancel: '取消'\n    },\n    transferSuccessGoManagePage: '转交成功，将返回合同管理页面',\n    claimSign: '认领签署',\n    downloadDepositPageTip: '下载签约存证页(脱敏版)',\n    resend: '重新发送',\n    proxySign: '代签署',\n    notPassed: '已驳回',\n    approving: '审批中',\n    signning: '签署中',\n    notarized: '已公正',\n    currentFolder: '当前文件夹',\n    archive: '归档',\n    deadlineForSigning: '截止签约时间',\n    endFinishTime: '签约完成/签约结束时间',\n    contractImportTime: '合同导入时间',\n    contractSendTime: '合同发送时间',\n    back: '返回',\n    contractInfo: '合同信息',\n    basicInfo: '基本信息',\n    contractNum: '合同编号',\n    sender: '发件方',\n    personAccount: '个人账号',\n    entAccount: '企业账号',\n    operator: '经办人',\n    signStartTime: '发起签约时间',\n    signDeadline: '签约截止时间',\n    contractExpireDate: '合同到期时间',\n    // none: '无',\n    edit: '修改',\n    settings: '设置',\n    from: '来源',\n    folder: '文件夹',\n    contractType: '合同类型',\n    reason: '理由',\n    sign: '署名',\n    approval: '审批',\n    viewAttach: '查看附页',\n    downloadContract: '下载合同',\n    downloadAttach: '下载签约存证',\n    print: '打印',\n    certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',\n    needMeSign: '需要我签署',\n    needMeApproval: '需要我审批',\n    inApproval: '审批中',\n    needOthersSign: '需要他人签署',\n    signComplete: '签约完成',\n    signOverdue: '逾期未签',\n    rejected: '已拒签',\n    revoked: '已撤销',\n    contractCompleteTime: '签约完成时间',\n    contractEndTime: '签约结束时间',\n    reject: '拒签',\n    revoke: '撤销',\n    download: '下载',\n    viewSignOrders: '查看签署顺序',\n    viewApprovalProcess: '承認フローを確認',\n    completed: '已完成',\n    cc: '抄送',\n    ccer: '抄送方',\n    signer: '签约方',\n    signSubject: '签约主体',\n    signSubjectTooltip: '发件方填写的签约主体为',\n    user: '用户',\n    IDNumber: '身份证号',\n    state: '状态',\n    time: '时间',\n    notice: '提醒',\n    detail: '详情',\n    RealNameCertificationRequired: '需要实名认证',\n    RealNameCertificationNotRequired: '不需要实名认证',\n    MustHandwrittenSignature: '必须手写签名',\n    handWritingRecognition: '开启手写笔迹识别',\n    privateMessage: '私信',\n    attachment: '资料',\n    rejectReason: '原因',\n    notSigned: '未签署',\n    notViewed: '未查看',\n    viewed: '已查看',\n    signed: '已签署',\n    viewedNotSigned: '已读未签',\n    notApproval: '未审批',\n    remindSucceed: '提醒消息已发送',\n    reviewDetails: '审批详情',\n    close: '关 闭',\n    entInnerOperateDetail: '企业内部操作详情',\n    approve: '同意',\n    disapprove: '驳回',\n    applySeal: '申请用印',\n    applied: '已申请',\n    apply: '申请',\n    toOtherSign: '转给其他人签',\n    handOver: '转交',\n    approvalOpinions: '审批意见',\n    useSeal: '用印',\n    signature: '签名',\n    use: '使用',\n    date: '日期',\n    fill: '填写',\n    times: '次',\n    place: '处',\n    contractDetail: '合同明细',\n    viewMore: '查看更多',\n    collapse: '收起',\n    signLink: '签署链接',\n    saveQRCode: '保存二维码或复制链接，分享给签署方',\n    signQRCode: '签署链接二维码',\n    copy: '复制',\n    copySucc: '复制成功',\n    copyFail: '复制失败',\n    certified: '已认证',\n    unCertified: '未认证',\n    claimed: '已认领'\n  },\n  uploadFile: {\n    thumbnails: '缩略图',\n    isUploading: '正在上传',\n    move: '移动',\n    delete: '删除',\n    replace: '替换',\n    tip: '提示',\n    understand: 'わかりました。',\n    totalPages: '{page}页',\n    uploadFile: '上传本地文件',\n    matchErr: '服务器开了点小差，请稍后再试',\n    inUploadingDeleteErr: '请在上传完毕后删除',\n    timeOutErr: '请求超时',\n    imgUnqualified: '画像フォーマットが要件に適合していません',\n    imgBiggerThan20M: '上传图片大小不能超过 20MB!',\n    error: '出错啦',\n    hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。'\n  },\n  contractInfo: {\n    contractName: '契約表題',\n    contractNameTooltip: '契約表題には特殊文字を含めず、100文字以内にしてください',\n    customNumber: '企業内部番号',\n    contractType: '契約タイプ',\n    signDeadLine: '契約期限',\n    signDeadLineTooltip: '契約書がこの日付までに署名完了しない場合、引き続き署名を続けることはできません',\n    selectDate: '日時の選択',\n    contractExpireDate: '契約満期日',\n    contractExpireDays: '契約書有効期間（日）',\n    expireDateTooltip: '契約内容の有効期間は後で契約書を管理するのに役立ちます',\n    notNecessary: '任意',\n    dateTips: 'お客様のために契約書有効期日を自動識別しました。確認してください',\n    contractTitleErr: '契約名には特殊文字を含めないでください',\n    contractTitleLengthErr: '契約名には100字を超えないでください',\n    internalNumber: '公司内部编号',\n    toSelect: '選択してください',\n    contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n    necessary: '必須項目'\n  },\n  template: {\n    templateList: {\n      linkBoxTip: '関連ファイルキャビネットID：'\n    },\n    dynamicTemplateUpdate: {\n      title: '动态模板新功能上线',\n      newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n      updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n      connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。'\n    },\n    sendCode: {\n      tip: '現在のテンプレート設定が送信コード生成条件を満たしていません。以下の要件を満たしているかどうかチェックしてください。',\n      fail: {\n        1: '空白の文書は含まれていません',\n        2: '契約者はただ1つの可変者(署名とccを含む)、そして可変者は第1の操作者でなければなりません;署名者は必ず捺印所を設けなければなりません',\n        3: '契約者のうち固定者のアカウントは空ではありません',\n        4: '送信前承認はありません',\n        5: '送信者はフィールドが空にならないように必ず記入します（記述フィールドと契約内容フィールドを含みます）',\n        6: '非テンプレートの組み合わせです'\n      }\n    },\n    sendCodeGuide: {\n      title: '发送码高级功能说明',\n      info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n      tip1: {\n        main: '1. 上上签',\n        sub: '',\n        line1: '向上上签申请开通档案+、合同预审、智能预审',\n        line2: '开通后可以到对应的菜单中操作使用'\n      },\n      tip2: {\n        main: '2. 档案柜管理员',\n        sub: '创建档案柜、配置智能预审',\n        line1: '',\n        line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。'\n      },\n      tip3: {\n        main: '3. 签约方',\n        sub: '扫码填资料、获取合同文件',\n        line1: '',\n        line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署'\n      },\n      tip4: {\n        main: '4. 档案柜管理员',\n        sub: '',\n        line1: '查看签约的相对方、发送的合同情况',\n        line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等'\n      }\n    }\n  },\n  style: {\n    signature: {\n      text: {\n        x: '34',\n        fontSize: '18'\n      }\n    }\n  },\n  resetPwd: {\n    title: '安全提示！',\n    notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',\n    oldLabel: '原密码',\n    oldPlaceholder: '请输入原密码',\n    newLabel: '新密码',\n    newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',\n    submit: '确定',\n    errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',\n    oldRule: '原密码不能为空',\n    newRule: '新密码不能为空',\n    success: '修改成功'\n  },\n  personAuthIntercept: {\n    title: '邀请您以',\n    name: '姓名：',\n    id: '身份证号：',\n    descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    desMore: '根据发起方要求，您还需要补充',\n    descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    tips: '实名认证通过后，可查看并签署合同。',\n    goOn: '是我本人，开始认证',\n    goMore: '去补充认证',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '是否继续以当前账号签署？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n    confirmReject: '是的，我要驳回实名'\n  },\n  authIntercept: {\n    title: '要求您以：',\n    name: '姓名为：',\n    id: '身份证号为：',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    descNoSame1: '签署合同。',\n    descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n    tips: '注：身份信息完全一致才能签署合同',\n    goOn: '是我本人，开始认证',\n    goHome: '我知道了',\n    goMore: '去补充认证',\n    authTip: '进行实名认证。',\n    viewAndSign: '完成认证后即可查看和签署合同',\n    tips2: '注：企业名称完全一致才能查看和签署合同。',\n    requestOtherAnth: '请求他人认证',\n    goAuth: '去实名认证',\n    requestSomeoneList: '请求以下人员完成实名认证：',\n    ent: '企业',\n    entName: '企业名称',\n    account: '账号',\n    accountPH: '手机或邮箱',\n    send: '发送',\n    lackEntName: '请填写企业名称',\n    errAccount: '请填写正确的邮箱或手机号',\n    successfulSent: '发送成功'\n  },\n  thirdPartApprovalDialog: {\n    title1: '署名前承認',\n    title2: '承認フロー',\n    content1: '承認後に署名できますので、しばらくお待ちください。',\n    content2: '需由第三方平台（非上上签平台）审批合同。可在上上签平台“接收方视角”页面中启用或关闭第三方平台审批。',\n    cancelBtnText: '承認フローを確認',\n    confirmBtnText: '確認',\n    iKnow: '理解'\n  },\n  endSignEarlyPrompt: {\n    cancel: '取消',\n    confirm: '确认',\n    signPrompt: '签署提示',\n    signTotalCountTip: '本次签署共包含{count}份合同文件',\n    signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n    hasSigned: '{count}人已签字',\n    hasNotSigned: '{count}人未签字',\n    noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。'\n  },\n  commonNomal: {\n    yesterday: '昨日',\n    ssq: 'ベストサイン',\n    ssqPlatform: 'ベストサイン電子契約クラウドプラットフォーム',\n    ssqTestPlatform: '（テスト用限定）ベストサイン電子署名クラウドプラットフォーム',\n    pageExpiredTip: 'ページは有効期限切れです。ページをリロードしてください',\n    pswCodeSimpleTip: 'パスワードは数字・大文字/小文字を含む6～18桁にする必要があります。再設定してください'\n  },\n  transferAdminDialog: {\n    title: '身分確認',\n    transfer: '引き継ぎ',\n    confirmAdmin: '私が管理者主任です',\n    content: 'システムの管理者主任は担当企業印章の管理・契約書の管理およびその他人員権限の管理が必要となります。通常企業の法定代表人・財務管理者・法務管理者・IT部門管理者もしくは企業業務責任者が担当します。| お客様が上記の身分かどうか確認してください。もしも違っていれば、関連する人に引き継いでください。'\n  },\n  choseBoxForReceiver: {\n    dataNeedForReceiver: '契約主体が提出しなければならない資料',\n    dataFromDataBox: '契約主体が提出しなければならない資料はアーカイブスの資料を通じて採集入手しなければなりません',\n    searchTp: 'アーカイブス名または番号を入力してください',\n    search: '検索',\n    boxNotFound: 'アーカイブスが見つかりません',\n    cancel: 'キャンセル',\n    confirm: '確　認'\n  },\n  localCommon: {\n    cancel: 'キャンセル',\n    confirm: '確認',\n    toSelect: '選択してください',\n    seal: '捺印',\n    signature: '署名',\n    signDate: '署名日時',\n    text: 'テキスト',\n    date: '日付',\n    qrCode: '二次元コード',\n    number: 'デジタル',\n    dynamicTable: '動態テンプレート',\n    terms: '契約条項',\n    checkBox: 'チェックボックス',\n    radioBox: 'ラジオボタン',\n    image: '画像',\n    confirmSeal: '業務照合印',\n    confirmRemark: '印章不適合の備考',\n    optional: 'オプション',\n    require: '必須項目',\n    tip: '注意',\n    comboBox: '検索候補'\n  },\n  twoFactor: {\n    signTip: '署名案内',\n    settingTwoFactor: '二要素認証を設定します',\n    step1: '1.バリデータアプリケーションのインストール',\n    step1Tip: '二要素身分認証には、携帯電話アプリケーションのインストールが必要です',\n    step2: '2.QRコードを読み取ります',\n    step2Tip1: 'ダウンロードしたバリデータを使用し、下記のQRコードを読み取ってください（携帯電話の時刻が現在時刻と一致していないと、二要素身分認証ができませんのでご注意ください）。',\n    step2Tip2: '画面には、二要素認証に必要な6桁の認証コードが表示されます。',\n    step3: '3.6桁の認証コードを入力します',\n    step3Tip: '画面に表示される認証コードを入力してください',\n    verifyCode6: '6桁の認証コード',\n    iosAddress: 'IOS版ダウンロードはこちら：',\n    androidAddress: 'Andro証明書版ダウンロードはこちら：',\n    chromeVerify: 'google身分バリデータ',\n    nextBtn: '次のステップ',\n    confirmSign: '署名を確認する',\n    dynamicCode: 'ワンタイムパスワード',\n    password: '署名コード',\n    pleaseInput: '入力してください',\n    twoFactorTip: '送信者の要求により、暗号化署名方法で署名する必要があります。',\n    passwordTip: '送信者に、署名コード入力での署名が求められています。',\n    twoFactorAndPasswordTip: '送信者の要請により、二要素認証を通り、正しい署名コードを入力することで署名を完了する必要があります',\n    passwordTip2: '署名コードは、送信者様へお問い合わせください。署名コードを入力しての署名となります。',\n    dynamicVerifyInfo: '正しいワンタイムパスワードを入力してください。認証システムのアカウントを設定し直した場合は、最新のワンタイムパスワードを入力してください。'\n  },\n  functionSupportDialog: {\n    title: '機能紹介',\n    inputTip: '関連する使用条件がある場合は、以下のフォームにご記入ください。ベストサインでは24時間以内に専門スタップからご連絡し、サービスのご案内を差し上げます。',\n    useSence: '使用場面',\n    useSenceTip: '例：人事/販売業者/物流帳票...',\n    estimatedOnlineTime: '予定オンライン時間',\n    requireContent: '必要内容',\n    requireContentTip: '御社がどのように電子契約を使用するのか大まかに説明ください。弊社からお客様のために適切なプランを作成します。',\n    getSupport: '専門サービスサポートの提供',\n    callServiceHotline: 'すぐにカスタマーサポート：<EMAIL>',\n    useSenceNotEmpty: '使用場面は空欄に出来ません',\n    requrieContentNotEmpty: '必要内容は空欄にできません',\n    oneWeek: '一週間以内',\n    oneMonth: '一ヶ月以内',\n    other: 'その他',\n    submitSuccess: 'コミット成功',\n    submitTrial: '試用に提出する',\n    toTrial: '試用に行く',\n    trialTip: '試用申請を提出すると、現在の機能はすぐに開通し、試用することができる。機能の使用を支援するために、次の表により多くのニーズを記入することができます。電子契約コンサルタントに署名すると、サービスが提供されます。',\n    applyTrial: '試用申請',\n    trialSuccTip: '機能が開通しましたので、お試しください',\n    goBuy: '直接購入',\n    trialTipMap: {\n      title: '試用の心得',\n      tip1: '1. オープン即使用、有効期間は7日間；',\n      tip2: '2. 試用期間中、機能は無料；',\n      tip3: '3. ビジネス主体ごとに、1つの機能を1回だけ試用する機会；',\n      tip4: '4. 試用期間中は自由に購入でき、無停止で使用できる；',\n      tip5: '5. 試用が終了した場合は、スキャンコードを確認して、詳細については前に署名した専門コンサルタントに連絡してください：'\n    },\n    contactAdminTip: '使用する場合は、Enterprise Administrator {tip} にお問い合わせください。',\n    trialEndTip: '試用期間が終了したら、クリックして購入してください',\n    trialRemainDayTip: '試用期間が残りました{day}日、クリックして購入してください',\n    trialEnd: '試用機能終了',\n    trialEndMap: {\n      deactivateTip: '{feature}機能は無効になっています。構成をクリアするか、継続料金を払ってから使用できます。',\n      feature1: '契約付属資料',\n      remove1: '構成の消去方法は、「テンプレートの編集」-構成済みの追加契約付属資料を見つけて削除します。',\n      feature2: '手書きの筆跡認識',\n      remove2: '構成をクリアする方法は、「テンプレートを編集」-構成済みのストローク認識を見つけて削除します。',\n      feature3: '契約装飾：スリット章+透かし',\n      remove3: '構成の消去方法は、「テンプレートの編集」-構成された契約装飾を見つけて削除します。',\n      feature4: '契約送信承認',\n      remove4: '構成方法の消去：Company Console-すべての承認フローを非アクティブにする'\n    }\n  },\n  setSignPwdDialog: {\n    tip: '設定後、署名パスワードが優先的に適用されます。ユーザーセンターもしくはアカウント管理から設定を変更出来ます。',\n    saveAndReturnSign: '保存して署名に戻る',\n    changeEmailVerify: 'メール認証に切り替える',\n    changePhoneVerify: '携帯電話認証に切り替える'\n  },\n  contractCompare: {\n    reUpload: '再アップロード',\n    title: '契約書比較',\n    packagePurchase: 'プラン購入',\n    packagePurchaseTitle: '【{title}機能】プラン購入',\n    myPackage: 'マイプラン',\n    packageDetail: 'プラン詳細',\n    per: '次',\n    packageContent: 'プラン内容：',\n    num: '{type}次数',\n    limitTime: '有効期間',\n    month: '月',\n    payNow: '今すぐ購入',\n    contactUs: 'お問い合わせ | QRコードをスキャンして専門アドバイザーに相談',\n    compareInfo1: 'ご利用ガイド：',\n    compareInfo2: '{index}、購入{type}に基づく利用可能限度額は、対応する企業の全メンバーが利用可能です。個人でご利用の場合は、画面上部のログイン主体を個人アカウントに切り替えてください。',\n    compareInfo3: '{index}、アップロードした契約書の{per}数に基づく使用量計算',\n    codePay: 'QRコードをスキャンして支払い',\n    aliPay: 'アリペイ支払い',\n    wxPay: 'ウィーチャット支払い',\n    payIno: '機能有効化 | 購入対象 | 支払金額',\n    finishPay: '支払い完了',\n    paySuccess: '購入成功',\n    originFile: '原本契約書ファイル',\n    compareFile: '比較用契約書ファイル',\n    documentSelect: 'ファイルを選択',\n    comparisonResult: '比較結果',\n    history: '履歴',\n    currentHistory: '文書記録',\n    noData: 'データなし',\n    differences: '{num}つの差異',\n    historyLog: '{num}件の記録',\n    uploadLimit: '比較するファイルをドラッグ＆ドロップ | 対応形式: PDF（スキャン済み含む）、Word',\n    dragInfo: 'マウスを離してアップロード',\n    uploadError: '非対応ファイル形式',\n    pageNum: '第{page}页',\n    difference: '差異 {num}',\n    download: '比較結果をダウンロード',\n    comparing: '契約書比較中...',\n    tip: '通知',\n    confirm: '確定',\n    toBuy: '購入へ進む',\n    translate: '契約書翻訳',\n    doCompare: '比較',\n    doTranslate: '翻訳',\n    review: '契約書審査',\n    doReview: '審査',\n    reviewUploadFile: '審査対象ファイルをここにドラッグ＆ドロップ',\n    reviewUpload: '審査基準ファイルをドラッグ | 例：「販売代理店管理規程」「調達規程」| 対応形式: PDF、Word',\n    reviewOriginFile: '審査対象契約書',\n    reviewTargetFile: '審査基準',\n    reviewResult: '審査結果',\n    uploadReviewFile: '審査基準ファイルをアップロード',\n    risk: 'リスク項目 {num}',\n    risks: '{num}つのリスク項目',\n    startReview: '審査を開始',\n    reviewing: '契約書審査中...',\n    noRisk: '審査完了 - リスク未検出',\n    allowUpload: '審査の参考として、「調達管理規程」などの社内規程、コンプライアンス規定、部門ガイドラインをアップロード可能です。| 例: 「甲は契約締結後5日以内に支払いを完了すること」 | 如：甲方需在合同签订后的5日内完成付款。',\n    notAllowUpload: '曖昧な表現や原則的な説明を審査基準に使用しないでください。 | 例: 「全ての契約条項は関連法令に準拠すること」',\n    resumeReview: '次のファイルへ進む',\n    close: '閉じる',\n    extract: '契約書抽出',\n    extractTitle: '抽出するキーワード',\n    selectKeyword: '下記の「キーワード」から選択',\n    keyword: 'キーワード',\n    addKeyword: '追加{keyword}',\n    introduce: '{keyword}定義',\n    startExtract: '抽出を開始',\n    extractTargetFile: '抽出対象契約書',\n    extractKeyWord: 'キーワード抽出',\n    extracting: '契約書抽出中...',\n    extractResult: '抽出結果',\n    extractUploadFile: '抽出するファイルをドラッグ＆ドロップ',\n    needExtractKeyword: '抽出するキーワードを選択',\n    summary: '契約書要約',\n    keySummary: 'キーワード要約',\n    deleteKeywordConfirm: 'このキーワードを削除しますか',\n    keywordPosition: 'キーワードの位置',\n    riskJudgement: 'リスク判定',\n    judgeTargetContract: 'リスク判定を開始',\n    interpretTargetContract: 'AI解読済み契約書',\n    startJudge: 'リスク評価を開始する',\n    startInterpret: '解読開始',\n    uploadText: 'リスク判断が必要なファイルのアップロードをお願いします',\n    interpretText: '解読が必要なファイルをアップロードしてください',\n    startTips: 'これでリスクはんだんを始められです',\n    interpretTips: 'これよりAI解読を開始いたします',\n    infoExtract: '抽出情報'\n  },\n  batchImport: {\n    iKnow: '理解'\n  },\n  templateCommon: {\n    tip: '注意'\n  },\n  mgapprovenote: {\n    SAQ: '问卷调查',\n    analyze: '分析',\n    annotate: '批注',\n    law: '法规',\n    case: '案例',\n    translate: '翻译',\n    mark: '标记',\n    tips: '上記内容はAIによって生成されたものであり、上上签の立場を代表するものではありません。参考のためだけにご利用ください。このマークを削除または変更しないでください。',\n    limit: '使用回数が上限に達しました。継続使用の需要がある場合はフォームにご記入ください。カスタマーサービスよりご連絡いたします。',\n    confirmTxt: '記入する',\n    content: '関連する段落',\n    experience: '業務経験',\n    datas: '関連データ',\n    terms: '類似条項',\n    original: '引用元',\n    export: 'エクスポート',\n    preview: '契約書閲覧',\n    history: '履歴'\n  },\n  sealConfirm: {\n    title: '印章確認ページ',\n    header: '印章確認',\n    signerEnt: '契約企業：',\n    abnormalSeal: '異常な印章：',\n    sealNormal: '印章正常',\n    tip1: '印章が正常に使用可能かどうかをご確認ください。正常な場合は、「印章正常」ボタンをクリックしてください。以降、この会社がこの印章を使用する際、システムは異常通知を送信しなくなります。',\n    tip2: '印章に問題がある場合は、速やかに契約相手と連絡を取り、印章の変更を行い、契約書を再送して署名してもらうか、または却下して再署名を行ってください。'\n  },\n  userCentral: userCentral,\n  ...console,\n  ...consts,\n  keyInfoExtract: {\n    operate: '情報抽出',\n    contractType: 'Predicted Contract Types',\n    tooltips: 'Select the Key Information',\n    predictText: 'Predicting',\n    extractText: 'Extracting',\n    errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n    result: 'result:'\n  },\n  judgeRisk: {\n    title: 'AI弁護士',\n    deepInference: 'AI法務',\n    showAll: 'Show More',\n    tips: 'Judging',\n    dialogTitle: '「AI弁護士」による契約書の審査',\n    aiInterpret: 'AI解読'\n  },\n  sealDistribute: {\n    requestSeal: '押印権限申請',\n    company: '会社',\n    applicant: '申請者',\n    accountID: 'アカウント',\n    submissionTime: '時間',\n    status: '状態',\n    agree: '同意済み',\n    unAgree: '却下済',\n    ifAgree: '同意する場合、',\n    applyTime: '申請者の印章利用期間は：',\n    to: '～',\n    placeHolderTime: '年-月-日',\n    senderCompany: '送信企業',\n    documentTitle: '契約書タイトル',\n    sealApplicationScope: '印章使用範囲',\n    applyforSeal: '印章申請',\n    reject: '却下',\n    approve: '同意'\n  },\n  sealApproval: {\n    sealRight: '印鑑権限',\n    requestSeal: '押印権限申請',\n    allEntContract: '全ての企業からの契約書',\n    partEntContract: '一部の企業からの契約書：',\n    pleaseInputRight: '権限を入力してください',\n    successTransfer: '引継ぎ完了後、',\n    getRight: '上記の権限を取得するか、新しい署名権限を直接編集して割り当てることができます。',\n    signAllEntContract: '全ての企業からの契約書に署名',\n    sign: '署名',\n    sendContract: '送信された契約書',\n    sealUseTime: '印鑑使用期間：',\n    currentStatus: '現在の状態：',\n    takeBackSeal: '印鑑を回収',\n    agree: '同意',\n    hasAgree: '同意済み',\n    hasReject: '却下済み',\n    hasDone: '完了',\n    ask: 'が',\n    giveYou: 'の印鑑をあなたに割り当てます',\n    hopeAsk: 'は',\n    hopeGive: 'の印鑑を引き継ぎたい',\n    hopeGiveYou: 'の関連印鑑をあなたに引き継ぎます',\n    noSettingTime: '時間設定なし',\n    approvalSuccess: '承認成功',\n    getSealSuccess: '印鑑取得成功'\n  },\n  workspace: {\n    create: '作成済み',\n    reviewing: '審査中',\n    completed: '完了',\n    noData: 'データなし',\n    introduce: '{keyword}の説明',\n    termsDetail: '用語の詳細',\n    extractFormat: '抽出形式',\n    optional: '任意',\n    required: '必須',\n    operate: '操作',\n    detail: '詳細',\n    delete: '削除',\n    agreement: {\n      uploadError: 'アップロードできるのはPDF、DOC、またはDOCXファイルのみです。',\n      extractionRequest: '抽出リクエストが送信されました。後で用語リストで結果を確認してください。',\n      upload: 'ファイルのアップロード',\n      define: '用語の定義',\n      extract: '協定の抽出',\n      drag: 'ファイルをここにドラッグするか、',\n      add: 'クリックして追加',\n      format: 'doc、docx、pdf形式をサポート',\n      fileName: 'ファイル名',\n      status: '状態',\n      completed: 'アップロード完了',\n      failed: 'アップロード失敗',\n      size: 'サイズ',\n      terms: '用語',\n      success: 'ファイル抽出完了、合計{total}個',\n      ongoing: 'ファイル抽出中... 合計{total}個',\n      tips: 'この画面をスキップしても抽出結果に影響はありません',\n      others: '続けてアップロード',\n      result: '抽出結果のダウンロードページに移動',\n      curProgress: '現在の進捗: ',\n      refresh: '更新',\n      details: '{successNum}個ロード済み、合計{length}個',\n      start: '抽出開始',\n      more: 'ファイルを追加',\n      skip: '抽出をスキップし、アップロードを完了。',\n      tiqu: '抽出開始',\n      chouqu: '抽出開始'\n    },\n    review: {\n      distribution: '配布審査',\n      Incomplete: '未終了',\n      createReview: '審査を作成',\n      manageReview: '審査管理',\n      reviewDetail: '審査の詳細',\n      reviewId: '審査番号',\n      reviewStatus: '審査状態',\n      reviewName: '審査名',\n      reviewStartTime: '審査開始時間',\n      reviewCompleteTime: '審査終了時間',\n      reviewDesc: 'バージョン：バージョン{reviewVersion}  |  審査番号：{reviewId}',\n      distribute: '審査を開始',\n      drag: '審査待ちの協定をこの領域にドラッグ',\n      content: '審査待ちの内容',\n      current: '配布待ち記録',\n      history: '履歴記録',\n      page: '第{page}ページ：',\n      users: '審査が必要なユーザー',\n      message: 'メッセージ',\n      modify: '修正',\n      placeholder: '複数のユーザーはセミコロン\";\"で区切ってください',\n      submit: '確定',\n      reupload: '審査の再アップロード',\n      finish: '審査終了',\n      reviewSummary: '審査概要',\n      initiator: '審査の発起人',\n      versionSummary: 'バージョン概要',\n      version: 'バージョン',\n      versionOrder: '第{version}版',\n      curReviewStatus: '現在のバージョン審査状態',\n      curReviewVersion: '現在のバージョン',\n      curReviewPopulation: '現在のバージョン審査人数',\n      curReviewStartTime: '現在のバージョン審査開始時間',\n      curReviewInitiator: '現在のバージョン審査発起人',\n      checkComments: '修正意見を集約表示',\n      overview: '審査結果の概要',\n      reviewer: '審査者',\n      reviewResult: '審査結果',\n      replyTime: '返信時間',\n      agreement: '審査の協定',\n      files: '関連協定',\n      fileName: '協定名',\n      numberOfModificationSuggestions: '修正意見数',\n      uploadTime: 'アップロード時間',\n      download: 'ダウンロード',\n      dispatch: '配布',\n      recent: '最新の審査時間：',\n      replyContent: '審査の返信内容',\n      advice: '協定の修正意見',\n      noIdea: '修正意見なし',\n      origin: '原文内容：',\n      revised: '修正後の内容：',\n      suggestion: '修正意見：',\n      dateMark: '{name} は <span style=\"color: #0988EC\">バージョン {version}</span> に {date} に記載されています',\n      unReviewed: 'まだレビューされていません',\n      revisionFiles: '修正協定',\n      staffReplyAggregation: '修正情報の集約',\n      staffReply: '{name}の審査情報',\n      tips: '提示',\n      tipsContent: '終了後、この審査は配布及び後続操作をサポートしなくなります。続けますか',\n      confirm: '確定',\n      cancel: 'キャンセル',\n      successMessage: '終了しました',\n      PASS: '承認',\n      REJECT: '不承認',\n      uploadErrorMessage: '現在、アップロードできるのはDOCX形式のファイルのみです。',\n      successInitiated: '審査が開始されました',\n      autoDistribute: 'インテリジェント分配',\n      requiredUsers: 'レビューが必要なユーザー',\n      contentToReview: 'レビューするコンテンツ',\n      termDetails: '用語の詳細',\n      term: '用語',\n      aiDistribute: 'AIインテリジェント分配',\n      noData: 'データがありません',\n      docIconAlt: 'ドキュメントアイコン',\n      docxIconAlt: 'DOCXアイコン',\n      pdfIconAlt: 'PDFアイコン',\n      requiredUsersError: 'レビューが必要なユーザーを入力してください',\n      selectContentError: 'レビューするコンテンツを選択してください',\n      initiateReviewSuccess: 'レビューが開始されました',\n      syncInitiated: '同期が開始されました'\n    },\n    contentTracing: {\n      title: '内容追跡',\n      fieldContent: 'フィールド内容',\n      originalResult: 'オリジナル結果',\n      contentSource: '内容の出典',\n      page: 'ページ'\n    }\n  },\n  hubblePackage: {\n    title: '私のパッケージ',\n    details: 'パッケージの詳細',\n    remainingPages: '残りの総ページ数',\n    pages: 'ページ',\n    usedPages: '使用済み',\n    remaining: '利用可能残数',\n    total: '合計',\n    expiryTime: '有効期限',\n    amount: '数量',\n    unitPrice: '単価',\n    copy: '部',\n    words: '千文字'\n  },\n  workspaceIndex: {\n    title: 'ワークスペース',\n    package: 'パッケージ使用量',\n    agreement: '契約管理',\n    review: 'レビュー管理',\n    term: '用語管理'\n  },\n  agreement: {\n    title: '契約管理',\n    exportList: '契約リストをエクスポート',\n    exportAllChecked: 'Excel（すべてのフィールド、選択された契約）',\n    exportCurrentChecked: 'Excel（現在のフィールド、選択された契約）',\n    exportAllMatched: 'Excel（すべてのフィールド、条件に一致）',\n    exportCurrentMatched: 'Excel（現在のフィールド、条件に一致）',\n    add: '契約を追加',\n    upload: '契約をアップロード',\n    operation: '操作',\n    download: 'ダウンロード',\n    details: '詳細',\n    delete: '削除',\n    relatedTaskStatus: '関連する抽出タスクの状態',\n    confirmDelete: '現在の契約を削除しますか？',\n    prompt: 'プロンプト',\n    booleanYes: 'はい',\n    booleanNo: 'いいえ',\n    defaultExportName: 'export.xlsx',\n    taskNotStarted: '抽出タスクは開始されていません',\n    taskStarted: '抽出タスクが開始されました（コンテンツ検索中）',\n    contentSearchCompleted: 'コンテンツ検索が完了しました（結果をフォーマット中）',\n    resultFormattingCompleted: '結果のフォーマットが完了しました（結果を校正中）',\n    resultVerificationCompleted: '結果の校正が完了しました'\n  },\n  filter: {\n    filter: 'フィルター',\n    refreshExtraction: '抽出をリフレッシュ',\n    extractTerms: '用語定義を抽出',\n    refreshList: 'リストをリフレッシュ',\n    currentCondition: '現在の条件で表示されている契約。',\n    when: '時',\n    selectCondition: '条件を選択してください',\n    enterCondition: '条件を入力してください',\n    yes: 'はい',\n    no: 'いいえ',\n    addCondition: '条件を追加',\n    reset: 'リセット',\n    confirm: '確認',\n    and: 'かつ',\n    or: 'または',\n    equals: '等しい',\n    notEquals: '等しくない',\n    contains: '含む',\n    notContains: '含まない',\n    greaterThan: 'より大きい',\n    greaterThanOrEquals: '以上',\n    lessThan: '未満',\n    lessThanOrEquals: '以下',\n    emptyCondition: 'フィルター条件は空にできません'\n  },\n  fieldConfig: {\n    button: 'フィールド設定',\n    header: '表示するフィールド',\n    submit: '完了',\n    cancel: 'キャンセル'\n  },\n  agreementDetail: {\n    detail: '契約詳細',\n    add: '契約を追加',\n    id: '契約番号',\n    file: '契約ファイル',\n    download: '契約をダウンロード',\n    replaceFile: '契約ファイルの置換',\n    uploadFile: '契約ファイルをアップロード',\n    relatedExtractionStatus: '関連する抽出タスクのステータス',\n    dataSource: 'データソース',\n    yes: 'はい',\n    no: 'いいえ',\n    select: '選択してください',\n    input: '入力してください',\n    save: '保存',\n    cancel: 'キャンセル',\n    page: '第 {page} ページ',\n    addDataSource: 'データソースを追加',\n    pageNo: '第',\n    pageSuffix: 'ページ',\n    submit: '送信',\n    inputDataSource: 'データソース内容を入力してください',\n    pageFormatError: 'ページ番号はカンマ区切りの数字のみをサポートします',\n    confirmDelete: '現在のデータソースを削除しますか？',\n    tips: 'ヒント',\n    uploadSuccess: 'アップロード成功'\n  },\n  termManagement: {\n    title: '用語管理',\n    batchDelete: '一括削除',\n    import: '用語のインポート',\n    export: '用語のエクスポート',\n    add: '+ 用語を追加',\n    name: '用語名',\n    definition: '用語の定義',\n    formatRequirement: '抽出形式の要件',\n    dataFormat: 'データ形式',\n    operation: '操作',\n    edit: '編集',\n    delete: '削除',\n    detail: '用語詳細',\n    addTitle: '用語を追加',\n    namePlaceholder: '専門用語を記入してください',\n    definitionPlaceholder: '用語の定義を記入してください',\n    formatRequirementPlaceholder: '用語の抽出形式の要件を記入してください',\n    dataFormatPlaceholder: '期待する抽出用語形式',\n    cancel: 'キャンセル',\n    confirmEdit: '変更を確認',\n    importTitle: '用語のインポート',\n    uploadTemplate: '用語テンプレートファイルをアップロード',\n    downloadTemplate: '用語テンプレートファイルをダウンロード',\n    extractType: {\n      text: 'テキスト',\n      longText: '長いテキスト',\n      date: '日付',\n      number: '数値',\n      boolean: 'はい/いいえ'\n    },\n    importSuccess: 'インポート成功',\n    deleteConfirm: '現在の用語を削除しますか？',\n    prompt: 'プロンプト',\n    nameEmptyError: '用語名は空にできません'\n  },\n  agent: {\n    extractTitle: '情報抽出',\n    riskTitle: 'AI弁護士',\n    feedback: '調査フィードバック',\n    toMini: 'ミニアプリで確認',\n    otherContract: '他の契約書の潜在リスクを分析?',\n    others: 'その他',\n    submit: '送信',\n    autoExtract: '抽出完了まで自動処理継続',\n    autoRisk: '分析プロセスを自動実行',\n    aiGenerated: 'AI Generated - © BestSign',\n    chooseRisk: '解析対象ファイルを選択',\n    chooseExtract: '抽出用ソースファイル指定',\n    analyzing: 'コンテンツ解析実行中',\n    advice: '修正案自動作成中',\n    options: '選択肢生成処理中',\n    inputTips: '正確な情報を入力必須',\n    chargeTip: '残高不足のためチャージ必須',\n    original: '原本',\n    revision: '改善提案',\n    diff: '比較',\n    locate: '原稿位置特定処理中',\n    custom: 'カスタム審査ルールを入力してください',\n    content: '原文の位置',\n    satisfy: '对分析结果满意，继续下一项分析',\n    dissatisfy: '对分析结果不满意，重新进行分析',\n    selectFunc: 'ご希望の機能をお選びください',\n    deepInference: 'AI法務',\n    deepThinking: '深い思考中',\n    deepThoughtCompleted: '深い思考が完了しました',\n    reJudge: '再判断',\n    confirm: '確認',\n    tipsContent: '再判定を行うと利用回数が減ります。続けますか？',\n    useLawyer: 'AI弁護士を起動',\n    interpretFinish: 'AI解読完了',\n    exportPDF: 'PDFレポート出力',\n    defaultExportName: 'export.pdf',\n    exporting: 'レポート出力中... しばらくお待ちください'\n  },\n  authorize: {\n    title: 'ご利用条件\t',\n    content: 'AI契約分析で業務効率化！同意の上、今すぐ体験',\n    cancel: 'いいえ',\n    confirm: '同意して利用開始',\n    contract: '『ハッブル製品利用条件』を確認'\n  },\n  hubbleEntry: {\n    smartAdvisor: 'スマート契約アドバイザー',\n    tooltips: 'この機能は有料です。BestSign電子契約アドバイザーにご連絡ください。',\n    confirm: '了解'\n  },\n  lang: 'ja'\n};", "map": {"version": 3, "names": ["utils", "mixin", "components", "docList", "console", "userCentral", "home", "sign", "entAuth", "consts", "docTranslation", "footerAd", "title", "content1", "content2", "bankContent", "bankTip1", "bankTip2", "bankFooter", "cancel", "continue", "commonFooter", "record", "hubbleRecordId", "openPlatform", "aboutBestSign", "contact", "recruitment", "help", "copyright", "company", "ssqLogo", "provideTip", "ssq", "provide", "signHotline", "langSwitch", "login", "pswLogin", "usePswLogin", "verifyLogin", "useVerifyLogin", "scanLogin", "scanFailure", "scanSuccess", "scanLoginTip", "appLoginTip", "downloadApp", "forgetPsw", "noAccount", "registerNow", "accountPlaceholder", "passwordPlaceholder", "pictureVer", "verifyCodePlaceholder", "getVerifyCode", "noRegister", "or", "errAccountOrPwdTip", "errAccountOrPwdTip2", "errEmailOrTel", "errPwd", "verCodeFormatErr", "grapVerCodeErr", "grapVerCodeFormatErr", "lackAccount", "lackGrapCode", "getVerCodeTip", "loginView", "reg<PERSON><PERSON><PERSON>", "takeViewBtn", "resendCode", "regTip", "haveRead", "bestsignAgreement", "and", "digitalCertificateAgreement", "privacyPolicy", "sendSuc", "lackVerCode", "lackPsw", "notMatch", "cookieTip", "wrongLink", "footerTips", "bestSign", "bestSignDescription", "forgetPswStep", "pictureVerCodeInput", "accountInput", "smsCodeInput", "haveRegistereLoginNow", "nextStep", "setNewPasswordInput", "passwordResetSucceeded", "accountNotRegistered", "loginAndDownload", "registerAndDownload", "inputPhone", "readContract", "errorPhone", "companyCert", "regAndCompanyCert", "handwrite", "picSubmitTip", "<PERSON><PERSON><PERSON><PERSON>", "replaceAllSignature", "replaceAllSeal", "canUseSeal", "applyForSeal", "moreTip", "uploadPic", "use", "clickExtend", "upload", "uploadTip1", "uploadTip2", "rewrite", "confirm", "upgradeBrowser", "submitTip", "title2", "QRCode", "needWrite", "needRewrite", "ok", "clearTips", "isBlank", "success", "signNotMatch", "signNotMatchExact", "msg", "successToUser", "successToSign", "cantGet", "common", "advice", "notEmpty", "enter6to18n", "ssqDes", "signPwdType", "enterActualEntName", "enterCorrectName", "enterCorrectPhoneNum", "enterCorrectEmail", "imgCodeErr", "enterCorrectIdNum", "enterCorrectFormat", "enterCorrectDateFormat", "entCertification", "subBaseInfo", "corDocuments", "license", "uploadLimit", "hi", "exit", "hotline", "acceptProtectingMethod", "comfirmSubmit", "cerficated", "serialNumber", "validity", "entName", "nationalNo", "corporationName", "city", "entCertificate", "certificationAuthority", "bestsignPlatform", "notIssued", "date", "congratulations", "rejectMessage", "recertification", "waitMessage", "<PERSON><PERSON>uth", "info", "submitPicError", "contractDrafting", "contractManagement", "userCenter", "service", "enterpriseConsole", "groupConsole", "startSigning", "contractType", "sendContract", "shortcuts", "setting", "signNum", "contractNum", "contractInFormation", "type", "basicInformation", "more", "certified", "account", "time", "day", "sendContractNum", "num", "realName", "update", "mark", "countDes", "chargeNow", "myRechargeOrder", "statusTip", "useTemplate", "useLocalFile", "enterEnterpriseName", "docDetail", "canNotOperateTip", "shareSignLink", "faceSign", "faceFirstVerifyCodeSecond", "contractRecipient", "personalOperateLog", "recordDialog", "user", "operate", "view", "download", "remarks", "operateRecords", "borrowingRecords", "currentHolder", "currentEnterprise", "companyInterOperationLog", "receiverMap", "sender", "signer", "ccUser", "downloadCode", "noTagToAddHint", "requireFieldNotAllowEmpty", "modifySuccess", "uncategorized", "notAllowModifyContractType", "setTag", "contractTag", "plsInput", "plsInputCompanyInternalNum", "companyInternalNum", "none", "plsSelect", "modify", "contractDetailInfo", "slideContentTip", "signNotice", "contractAncillaryInformation", "content", "document", "downloadDepositConfirmTip", "hint", "confrim", "downloadTip", "transferSuccessGoManagePage", "claimSign", "downloadDepositPageTip", "resend", "proxySign", "notPassed", "approving", "signning", "notarized", "currentFolder", "archive", "deadlineForSigning", "endFinishTime", "contractImportTime", "contractSendTime", "back", "contractInfo", "basicInfo", "personAccount", "entAccount", "operator", "signStartTime", "signDeadline", "contractExpireDate", "edit", "settings", "from", "folder", "reason", "approval", "viewAttach", "downloadContract", "downloadAttach", "print", "certificatedTooltip", "needMeSign", "needMeApproval", "inApproval", "needOthersSign", "signComplete", "signOverdue", "rejected", "revoked", "contractCompleteTime", "contractEndTime", "reject", "revoke", "viewSignOrders", "viewApprovalProcess", "completed", "cc", "ccer", "signSubject", "signSubjectTooltip", "IDNumber", "state", "notice", "detail", "RealNameCertificationRequired", "RealNameCertificationNotRequired", "MustHandwrittenSignature", "handWritingRecognition", "privateMessage", "attachment", "rejectReason", "notSigned", "notViewed", "viewed", "signed", "viewedNotSigned", "notApproval", "remindSucceed", "reviewDetails", "close", "entInnerOperateDetail", "approve", "disapprove", "applySeal", "applied", "apply", "toOtherSign", "handOver", "approvalOpinions", "useSeal", "signature", "fill", "times", "place", "contractDetail", "viewMore", "collapse", "signLink", "saveQRCode", "signQRCode", "copy", "copySucc", "copyFail", "unCertified", "claimed", "uploadFile", "thumbnails", "isUploading", "move", "delete", "replace", "tip", "understand", "totalPages", "matchErr", "inUploadingDeleteErr", "timeOutErr", "imgUnqualified", "imgBiggerThan20M", "error", "hasCATip", "contractName", "contractNameTooltip", "customNumber", "signDeadLine", "signDeadLineTooltip", "selectDate", "contractExpireDays", "expireDateTooltip", "notNecessary", "dateTips", "contractTitleErr", "contractTitleLengthErr", "internalNumber", "toSelect", "contractTypeErr", "necessary", "template", "templateList", "linkBoxTip", "dynamicTemplateUpdate", "newVersionDesc", "updateTip", "connectUs", "sendCode", "fail", "sendCodeGuide", "tip1", "main", "sub", "line1", "line2", "tip2", "tip3", "tip4", "style", "text", "x", "fontSize", "resetPwd", "<PERSON><PERSON><PERSON><PERSON>", "oldPlaceholder", "new<PERSON>abel", "newPlaceholder", "submit", "errorMsg", "oldRule", "newRule", "personAuthIntercept", "name", "id", "descNoAuth", "des<PERSON><PERSON>", "descNoSame", "descNoAuth1", "descNoAuth2", "tips", "goOn", "goMore", "descNoSame1", "descNoSame2", "goHome", "authInfo", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmReject", "authIntercept", "authTip", "viewAndSign", "tips2", "requestOtherAnth", "goAuth", "requestSomeoneList", "ent", "accountPH", "send", "lackEntName", "errAccount", "successfulSent", "thirdPartApprovalDialog", "title1", "cancelBtnText", "confirmBtnText", "iKnow", "endSignEarlyPrompt", "signPrompt", "signTotalCountTip", "signatureTip", "hasSigned", "hasNotSigned", "noNeedSealTip", "commonNomal", "yesterday", "ssqPlatform", "ssqTestPlatform", "pageExpiredTip", "pswCodeSimpleTip", "transferAdminDialog", "transfer", "confirmAdmin", "choseBoxForReceiver", "dataNeedForReceiver", "dataFromDataBox", "searchTp", "search", "boxNotFound", "localCommon", "seal", "signDate", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "confirmSeal", "confirmRemark", "optional", "require", "comboBox", "twoFactor", "signTip", "settingTwoFactor", "step1", "step1Tip", "step2", "step2Tip1", "step2Tip2", "step3", "step3Tip", "verifyCode6", "iosAddress", "android<PERSON><PERSON><PERSON>", "chromeVerify", "nextBtn", "confirmSign", "dynamicCode", "password", "pleaseInput", "twoFactorTip", "passwordTip", "twoFactorAndPasswordTip", "passwordTip2", "dynamicVerifyInfo", "functionSupportDialog", "inputTip", "useSence", "useSenceTip", "estimatedOnlineTime", "requireContent", "requireContentTip", "getSupport", "callServiceHotline", "useSenceNotEmpty", "requrieContentNotEmpty", "oneWeek", "oneMonth", "other", "submitSuccess", "submitTrial", "toTrial", "trialTip", "applyTrial", "trialSuccTip", "goBuy", "trialTipMap", "tip5", "contactAdminTip", "trialEndTip", "trialRemainDayTip", "trialEnd", "trialEndMap", "deactivateTip", "feature1", "remove1", "feature2", "remove2", "feature3", "remove3", "feature4", "remove4", "setSignPwdDialog", "saveAndReturnSign", "changeEmailVerify", "changePhoneVerify", "contractCompare", "reUpload", "packagePurchase", "packagePurchaseTitle", "myPackage", "packageDetail", "per", "packageContent", "limitTime", "month", "payNow", "contactUs", "compareInfo1", "compareInfo2", "compareInfo3", "codePay", "ali<PERSON>ay", "wxPay", "payIno", "finishPay", "paySuccess", "originFile", "compareFile", "documentSelect", "comparisonResult", "history", "currentHistory", "noData", "differences", "historyLog", "dragInfo", "uploadError", "pageNum", "difference", "comparing", "<PERSON><PERSON><PERSON>", "translate", "doCompare", "doTranslate", "review", "doReview", "reviewUploadFile", "reviewUpload", "reviewOriginFile", "reviewTargetFile", "reviewResult", "uploadReviewFile", "risk", "risks", "startReview", "reviewing", "noRisk", "allowUpload", "notAllowUpload", "resume<PERSON><PERSON>iew", "extract", "extractTitle", "selectKeyword", "keyword", "addKeyword", "introduce", "startExtract", "extractTargetFile", "extractKeyWord", "extracting", "extractResult", "extractUploadFile", "needExtractKeyword", "summary", "key<PERSON><PERSON><PERSON>y", "deleteKeywordConfirm", "keywordPosition", "riskJudgement", "judge<PERSON><PERSON><PERSON>Contract", "interpretTargetContract", "startJudge", "startInterpret", "uploadText", "interpretText", "startTips", "interpretTips", "infoExtract", "batchImport", "templateCommon", "mgapprovenote", "SAQ", "analyze", "annotate", "law", "case", "limit", "confirmTxt", "experience", "datas", "original", "export", "preview", "sealConfirm", "header", "signerEnt", "abnormalSeal", "sealNormal", "keyInfoExtract", "tooltips", "predictText", "extractText", "errorMessage", "result", "judgeRisk", "deepInference", "showAll", "dialogTitle", "aiInterpret", "sealDistribute", "requestSeal", "applicant", "accountID", "submissionTime", "status", "agree", "unAgree", "ifAgree", "applyTime", "to", "placeHolderTime", "senderCompany", "documentTitle", "sealApplicationScope", "applyforSeal", "sealApproval", "sealRight", "allEntContract", "partEntContract", "pleaseInputRight", "successTransfer", "getRight", "signAllEntContract", "sealUseTime", "currentStatus", "takeBackSeal", "hasAgree", "hasReject", "hasDone", "<PERSON><PERSON><PERSON>", "hopeAsk", "hopeGive", "hopeGiveYou", "noSettingTime", "approvalSuccess", "getSealSuccess", "workspace", "create", "termsDetail", "extractFormat", "required", "agreement", "extractionRequest", "define", "drag", "add", "format", "fileName", "failed", "size", "ongoing", "others", "curProgress", "refresh", "details", "start", "skip", "tiqu", "chouqu", "distribution", "Incomplete", "createReview", "manageReview", "reviewDetail", "reviewId", "reviewStatus", "reviewName", "reviewStartTime", "reviewCompleteTime", "reviewDesc", "distribute", "current", "page", "users", "message", "placeholder", "reupload", "finish", "reviewSummary", "initiator", "versionSummary", "version", "versionOrder", "curRevie<PERSON><PERSON><PERSON><PERSON>", "curReviewV<PERSON><PERSON>", "curReviewPopulation", "curReviewStartTime", "curReviewInitiator", "checkComments", "overview", "reviewer", "replyTime", "files", "numberOfModificationSuggestions", "uploadTime", "dispatch", "recent", "replyContent", "noIdea", "origin", "revised", "suggestion", "dateMark", "unReviewed", "revisionFiles", "staffReplyAggregation", "staffReply", "tipsContent", "successMessage", "PASS", "REJECT", "uploadErrorMessage", "successInitiated", "autoDistribute", "requiredUsers", "contentToReview", "termDetails", "term", "aiDistribute", "docIconAlt", "docxIconAlt", "pdfIconAlt", "requiredUsersError", "selectContentError", "initiateReviewSuccess", "syncInitiated", "contentTracing", "<PERSON><PERSON><PERSON>nt", "originalResult", "contentSource", "hubblePackage", "remainingPages", "pages", "usedPages", "remaining", "total", "expiryTime", "amount", "unitPrice", "words", "workspaceIndex", "package", "exportList", "exportAllChecked", "exportCurrentChecked", "exportAllMatched", "exportCurrentMatched", "operation", "relatedTaskStatus", "confirmDelete", "prompt", "booleanYes", "booleanNo", "defaultExportName", "taskNotStarted", "taskStarted", "contentSearchCompleted", "resultFormattingCompleted", "resultVerificationCompleted", "filter", "refreshExtraction", "extractTerms", "refreshList", "currentCondition", "when", "selectCondition", "enterCondition", "yes", "no", "addCondition", "reset", "equals", "notEquals", "contains", "notContains", "greaterThan", "greaterThanOrEquals", "lessThan", "lessThanOrEquals", "emptyCondition", "fieldConfig", "button", "agreementDetail", "file", "replaceFile", "relatedExtractionStatus", "dataSource", "select", "input", "save", "addDataSource", "pageNo", "pageSuffix", "inputDataSource", "pageFormatError", "uploadSuccess", "termManagement", "batchDelete", "import", "definition", "formatRequirement", "dataFormat", "addTitle", "namePlaceholder", "definitionPlaceholder", "formatRequirementPlaceholder", "dataFormatPlaceholder", "confirmEdit", "importTitle", "uploadTemplate", "downloadTemplate", "extractType", "longText", "boolean", "importSuccess", "deleteConfirm", "nameEmptyError", "agent", "riskTitle", "feedback", "to<PERSON><PERSON>", "otherContract", "autoExtract", "autoRisk", "aiGenerated", "chooseRisk", "chooseExtract", "analyzing", "options", "inputTips", "chargeTip", "revision", "diff", "locate", "custom", "satisfy", "dissatisfy", "selectFunc", "deepThinking", "deepThoughtCompleted", "reJudge", "useLawyer", "interpretFinish", "exportPDF", "exporting", "authorize", "contract", "hubbleEntry", "smartAdvisor", "lang"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/ja.js"], "sourcesContent": ["// 语言为中文时的文案\nimport utils from './module/utils/utils-zh.js';\nimport mixin from './module/mixins/mixins-zh.js';\nimport components from './module/components/components-ja.js';\n\nimport docList from './module/docList/docList-ja.js';\nimport console from './module/console/console-zh.js';\nimport userCentral from './module/usercentral/usercentral-zh.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-ja.js';\nimport entAuth from './module/entAuth/entAuth-zh.js';\nimport consts from './module/consts/ja.js';\nimport docTranslation from './module/docTranslation/docTranslation-ja.js';\n\nexport default {\n    ...utils,\n    ...mixin,\n    ...components,\n    ...docList,\n    ...docTranslation,\n    footerAd: {\n        title: 'ジャンププロンプト',\n        content1: 'アクセスしたページは、サードパーティのプロモーションページにジャンプします。',\n        content2: '続行しますか？',\n        bankContent: '即将进入宁波银行\"容易贷\"企业贷款介绍页面',\n        bankTip1: '让宁波银行主动给我打电话',\n        bankTip2: '向我发送一条短信，介绍如何办理',\n        bankFooter: '加宁波银行专属客服，一对一服务我',\n        cancel: 'キャンセル',\n        continue: '続行',\n    },\n    commonFooter: {\n        record: 'ICP主体企業登録番号：浙ICP備********号',\n        hubbleRecordId: '网信算备：330106973391501230011',\n        openPlatform: 'オープンプラットフォーム',\n        aboutBestSign: '弊社について',\n        contact: 'お問い合わせ先',\n        recruitment: '採用情報',\n        help: 'ヘルプセンター',\n        copyright: '無断転載禁止',\n        company: 'ベストサイン・ジャパン株式会社',\n        ssqLogo: 'ベストサイン ボトムバーロゴ',\n        provideTip: '電子契約サービスは',\n        ssq: 'ベストサインにより',\n        provide: '提供',\n        signHotline: '契約サービスホットライン',\n        langSwitch: '言語',\n    },\n    login: {\n        pswLogin: '密码登录',\n        usePswLogin: '使用密码登录',\n        verifyLogin: '验证码登录',\n        useVerifyLogin: '使用验证码登录',\n        scanLogin: '扫码登录',\n        scanFailure: '二维码已失效,请刷新重试',\n        scanSuccess: '扫码成功',\n        scanLoginTip: '请使用上上签APP扫一扫登录',\n        appLoginTip: '请在上上签APP中点击登录',\n        downloadApp: '下载上上签APP',\n        forgetPsw: '忘记密码',\n        login: '登录',\n        noAccount: '没有账号',\n        registerNow: '马上注册',\n        accountPlaceholder: '请输入手机或邮箱',\n        passwordPlaceholder: '请输入登录密码',\n        pictureVer: '请填写图片中的内容',\n        verifyCodePlaceholder: '请输入6位验证码',\n        getVerifyCode: '認証コードを取得する',\n        noRegister: '尚未注册',\n        or: '或',\n        errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',\n        errAccountOrPwdTip2: '你输入的密码和账号不匹配',\n        errEmailOrTel: '请输入正确的邮箱或手机号!',\n        errPwd: '请输入正确的密码!',\n        verCodeFormatErr: '验证码错误',\n        grapVerCodeErr: '图形验证码错误',\n        grapVerCodeFormatErr: '图形验证码格式错误',\n        lackAccount: '请填写账号后再获取',\n        lackGrapCode: '请先填写图形验证码',\n        getVerCodeTip: '请获取验证码',\n\n        loginView: '登录并查看合同',\n        regView: '注册并查看合同',\n        takeViewBtn: '登录并签署',\n        resendCode: '重新获取',\n        regTip: '填写正确的验证码后上上签将为您创建账号',\n        haveRead: '我已阅读并同意',\n        bestsignAgreement: '上上签服务协议',\n        and: '和',\n        digitalCertificateAgreement: '数字证书使用协议',\n        privacyPolicy: '隐私政策',\n        sendSuc: '发送成功',\n        lackVerCode: '请先输入验证码',\n        lackPsw: '请先输入密码',\n        notMatch: '您输入的密码和账号不匹配',\n        cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',\n        wrongLink: '非法链接',\n        footerTips: '电子签约服务由<span>上上签</span>提供',\n        bestSign: '上上签',\n        bestSignDescription: '电子签约行业领跑者',\n        /** 忘记密码 /forgotPassword start */\n        forgetPswStep: '验证注册账号 | 重新设置密码',\n        pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',\n        accountInput: '账号 | 请填写您的账号',\n        smsCodeInput: '验证码 | 获取验证码',\n        haveRegistereLoginNow: '我已注册， | 马上登录',\n        nextStep: '下一步 | 提交',\n        setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',\n        passwordResetSucceeded: '密码重置成功!',\n        /** 忘记密码 /forgotPassword end */\n        accountNotRegistered: '账号未注册',\n        loginAndDownload: '登录并下载合同',\n        registerAndDownload: '注册并下载合同',\n        inputPhone: '请输入手机号',\n        readContract: '读取合同',\n        errorPhone: '手机格式错误',\n        companyCert: '进行企业认证',\n        regAndCompanyCert: '注册并进行企业认证',\n    },\n    ...sign,\n    handwrite: {\n        title: '手書きサイン',\n        picSubmitTip: '署名画像が正常に提出されました',\n        settingDefault: 'デフォルト署名に設定',\n        replaceAllSignature: '全ての署名に使われます',\n        replaceAllSeal: '全てのスタンプに使います',\n        canUseSeal: '私の印章',\n        applyForSeal: '印章使用申請',\n        moreTip: 'あなたの手書き署名はデフォルト署名として保存され、契約書の署名にのみ使用されます。管理方法：【ユーザーセンター->署名管理】',\n        uploadPic: '画像をアップロード',\n        use: '使う',\n        clickExtend: '右矢印をクリックして手書きエリアを拡大',\n        upload: '署名画像をアップロード',\n        uploadTip1: 'ヒント：署名画像をアップロードする際は、署名が画像全体を満たすようにしてください',\n        uploadTip2: '署名は濃い色または黒色の文字でお願いします',\n        rewrite: '書き直し',\n        cancel: '取り消し',\n        confirm: '使用',\n        upgradeBrowser: 'お使いのブラウザはキャンバス署名機能に対応していません。ブラウザを更新してください。',\n        submitTip: '手描きの署名を提出成功',\n        title2: '署名を手書きしてください',\n        QRCode: 'QRコード読み取りで署名',\n        needWrite: '正しい名前を手書きしてください！',\n        needRewrite: '文字が判読できません。書き直してください',\n        ok: '確定',\n        clearTips: '判読可能な署名をお書きください',\n        isBlank: 'キャンバスが空です。署名を描いてから送信してください！',\n        success: '署名が正常に送信されました',\n        signNotMatch: '楷書で署名してください。身分証明書の記載と一致する必要があります。',\n        signNotMatchExact: '第{numList}文字の認識に失敗しました。楷書で署名してください。身分証明書の記載と一致する必要があります。',\n        msg: {\n            successToUser: '新しい署名を設定しました。ウェブサイトで「保存」ボタンをクリックしてください。',\n            successToSign: '新しい署名が有効になりました。契約書の署名ページをご確認ください。',\n            cantGet: '署名を取得できません。他のブラウザをお試しください。',\n        },\n    },\n    common: {\n\n        aboutBestSign: '关于公司',\n        contact: '联系我们',\n        recruitment: '诚聘英才',\n        copyright: '版权所有',\n        advice: '咨询建议',\n        notEmpty: '空にすることはできません!',\n        enter6to18n: '请输入6-18位数字、大小写字母',\n        ssqDes: '电子签约云平台领导者',\n        openPlatform: '开放平台',\n        company: '杭州尚尚签网络科技有限公司',\n        help: '帮助中心',\n        errEmailOrTel: '请输入正确的邮箱或手机号!',\n        verCodeFormatErr: '验证码错误',\n        signPwdType: '6桁の数字を入力してください',\n        enterActualEntName: '请填写真实的企业名称',\n        enterCorrectName: '请输入正确的姓名',\n        enterCorrectPhoneNum: '请输入正确的手机号',\n        enterCorrectEmail: '请输入正确的邮箱',\n        imgCodeErr: '图形验证码错误',\n        enterCorrectIdNum: '请输入正确的证件号码',\n        enterCorrectFormat: '请输入正确的格式',\n        enterCorrectDateFormat: '请输入正确的日期格式',\n\n    },\n    entAuth: {\n        ...entAuth,\n        entCertification: '企业实名认证',\n        subBaseInfo: '提交基本信息',\n        corDocuments: '企业证件',\n        license: '营业执照',\n        upload: 'アップロードをクリック',\n        uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',\n        hi: '你好',\n        exit: '退出',\n        help: '帮助',\n        hotline: '服务热线',\n        acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',\n        comfirmSubmit: '确认提交',\n        cerficated: '认证完成',\n        serialNumber: '证书序列号',\n        validity: '有效期',\n        entName: '企业名称',\n        nationalNo: '国家注册号',\n        corporationName: '法定代表人姓名',\n        city: '所在城市',\n        entCertificate: '企业实名证书',\n        certificationAuthority: '证书颁发机构',\n        bestsignPlatform: '上上签电子签约云平台',\n        notIssued: '未发放',\n        date: '{year}年{month}月{day}日',\n        congratulations: '恭喜您，成功完成企业实名认证',\n        continue: '继续',\n        rejectMessage: '由于如下原因，资料审核不通过，请核对',\n        recertification: '重新认证',\n        waitMessage: '客服将在一个工作日内完成审核，请耐心等待',\n    },\n    personalAuth: {\n        info: '提示',\n        submitPicError: '请上传照片后再使用',\n    },\n    home: {\n        ...home,\n        home: '首页',\n        contractDrafting: '合同起草',\n        contractManagement: '合同管理',\n        userCenter: '用户中心',\n        service: '服务',\n        enterpriseConsole: '企业控制台',\n        groupConsole: '集团控制台',\n        startSigning: '契約書を送る',\n        contractType: '发送普通合同 | 发送模板合同 ',\n        sendContract: '发送合同',\n        shortcuts: '快捷入口 | 没有任何文件快捷入口',\n        setting: '立即设置 | 设置更多快捷入口',\n        signNum: '签发量月度报表',\n        contractNum: '合同发送量 | 合同签署量',\n        contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',\n        type: '企业 | 个人',\n        basicInformation: '基本信息',\n        more: '更多',\n        certified: '已认证 | 未认证',\n        account: '账号',\n        time: '创建时间 |注册时间',\n        day: '日 | 月',\n        sendContractNum: '发送量 | 签署量',\n        num: '份',\n        realName: '立即企业实名 | 立即个人实名',\n        update: '产品最新公告',\n        mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',\n        countDes: {\n            1: '可发：对公合同',\n            2: '份',\n            3: '对私合同',\n            4: '份',\n        },\n        chargeNow: '立即充值',\n        myRechargeOrder: '我的充值订单',\n        statusTip: {\n            1: '需要我操作',\n            2: '需要他人签署',\n            3: '即将截止签约',\n            4: '签约完成',\n        },\n        useTemplate: '使用模板',\n        useLocalFile: '上传本地文件',\n        enterEnterpriseName: '请输入企业名称',\n    },\n    docDetail: {\n        canNotOperateTip: '无法{operate}合同',\n        shareSignLink: '分享签署链接',\n        faceSign: '刷脸签署',\n        faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',\n        contractRecipient: '合同收件方',\n        personalOperateLog: '个人合同操作日志',\n        recordDialog: {\n            date: '日期',\n            user: '用户',\n            operate: '操作',\n            view: '查看',\n            download: '下载',\n        },\n        remarks: '备注',\n        operateRecords: '操作记录',\n        borrowingRecords: '借阅记录',\n        currentHolder: '当前持有人',\n        currentEnterprise: '当前企业',\n        companyInterOperationLog: '公司内部操作日志',\n        receiverMap: {\n            sender: '合同发件人',\n            signer: '合同接收人',\n            ccUser: '合同抄送人',\n        },\n        downloadCode: '合同下载码',\n        noTagToAddHint: '还没有标签，请前往企业控制台添加',\n        requireFieldNotAllowEmpty: '必填项不能为空',\n        modifySuccess: '修改成功',\n        uncategorized: '未分类',\n        notAllowModifyContractType: '{type}中的合同不允许修改合同类型',\n        setTag: '设置标签',\n        contractTag: '合同标签',\n        plsInput: '请输入',\n        plsInputCompanyInternalNum: '请输入公司内部编号',\n        companyInternalNum: '公司内部编号',\n        none: '无',\n        plsSelect: '请选择',\n        modify: '修改',\n        contractDetailInfo: '合同详细信息',\n        slideContentTip: {\n            signNotice: '签约须知',\n            contractAncillaryInformation: '合同附属资料',\n            content: '内容',\n            document: '文档',\n        },\n        downloadDepositConfirmTip: {\n            title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',\n            hint: '提示',\n            confrim: '继续下载',\n            cancel: '取消',\n        },\n        downloadTip: {\n            title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',\n            hint: '提示',\n            confirm: '确定',\n            cancel: '取消',\n        },\n        transferSuccessGoManagePage: '转交成功，将返回合同管理页面',\n        claimSign: '认领签署',\n        downloadDepositPageTip: '下载签约存证页(脱敏版)',\n        resend: '重新发送',\n        proxySign: '代签署',\n        notPassed: '已驳回',\n        approving: '审批中',\n        signning: '签署中',\n        notarized: '已公正',\n        currentFolder: '当前文件夹',\n        archive: '归档',\n        deadlineForSigning: '截止签约时间',\n        endFinishTime: '签约完成/签约结束时间',\n        contractImportTime: '合同导入时间',\n        contractSendTime: '合同发送时间',\n        back: '返回',\n        contractInfo: '合同信息',\n        basicInfo: '基本信息',\n        contractNum: '合同编号',\n        sender: '发件方',\n        personAccount: '个人账号',\n        entAccount: '企业账号',\n        operator: '经办人',\n        signStartTime: '发起签约时间',\n        signDeadline: '签约截止时间',\n        contractExpireDate: '合同到期时间',\n        // none: '无',\n        edit: '修改',\n        settings: '设置',\n        from: '来源',\n        folder: '文件夹',\n        contractType: '合同类型',\n        reason: '理由',\n        sign: '署名',\n        approval: '审批',\n        viewAttach: '查看附页',\n        downloadContract: '下载合同',\n        downloadAttach: '下载签约存证',\n        print: '打印',\n        certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',\n        needMeSign: '需要我签署',\n        needMeApproval: '需要我审批',\n        inApproval: '审批中',\n        needOthersSign: '需要他人签署',\n        signComplete: '签约完成',\n        signOverdue: '逾期未签',\n        rejected: '已拒签',\n        revoked: '已撤销',\n        contractCompleteTime: '签约完成时间',\n        contractEndTime: '签约结束时间',\n        reject: '拒签',\n        revoke: '撤销',\n        download: '下载',\n        viewSignOrders: '查看签署顺序',\n        viewApprovalProcess: '承認フローを確認',\n        completed: '已完成',\n        cc: '抄送',\n        ccer: '抄送方',\n        signer: '签约方',\n        signSubject: '签约主体',\n        signSubjectTooltip: '发件方填写的签约主体为',\n        user: '用户',\n        IDNumber: '身份证号',\n        state: '状态',\n        time: '时间',\n        notice: '提醒',\n        detail: '详情',\n        RealNameCertificationRequired: '需要实名认证',\n        RealNameCertificationNotRequired: '不需要实名认证',\n        MustHandwrittenSignature: '必须手写签名',\n        handWritingRecognition: '开启手写笔迹识别',\n        privateMessage: '私信',\n        attachment: '资料',\n        rejectReason: '原因',\n        notSigned: '未签署',\n        notViewed: '未查看',\n        viewed: '已查看',\n        signed: '已签署',\n        viewedNotSigned: '已读未签',\n        notApproval: '未审批',\n        remindSucceed: '提醒消息已发送',\n        reviewDetails: '审批详情',\n        close: '关 闭',\n        entInnerOperateDetail: '企业内部操作详情',\n        approve: '同意',\n        disapprove: '驳回',\n        applySeal: '申请用印',\n        applied: '已申请',\n        apply: '申请',\n        toOtherSign: '转给其他人签',\n        handOver: '转交',\n        approvalOpinions: '审批意见',\n        useSeal: '用印',\n        signature: '签名',\n        use: '使用',\n        date: '日期',\n        fill: '填写',\n        times: '次',\n        place: '处',\n        contractDetail: '合同明细',\n        viewMore: '查看更多',\n        collapse: '收起',\n        signLink: '签署链接',\n        saveQRCode: '保存二维码或复制链接，分享给签署方',\n        signQRCode: '签署链接二维码',\n        copy: '复制',\n        copySucc: '复制成功',\n        copyFail: '复制失败',\n        certified: '已认证',\n        unCertified: '未认证',\n        claimed: '已认领',\n    },\n    uploadFile: {\n        thumbnails: '缩略图',\n        isUploading: '正在上传',\n        move: '移动',\n        delete: '删除',\n        replace: '替换',\n        tip: '提示',\n        understand: 'わかりました。',\n        totalPages: '{page}页',\n        uploadFile: '上传本地文件',\n        matchErr: '服务器开了点小差，请稍后再试',\n        inUploadingDeleteErr: '请在上传完毕后删除',\n        timeOutErr: '请求超时',\n        imgUnqualified: '画像フォーマットが要件に適合していません',\n        imgBiggerThan20M: '上传图片大小不能超过 20MB!',\n        error: '出错啦',\n        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',\n    },\n    contractInfo: {\n        contractName: '契約表題',\n        contractNameTooltip: '契約表題には特殊文字を含めず、100文字以内にしてください',\n        customNumber: '企業内部番号',\n        contractType: '契約タイプ',\n        signDeadLine: '契約期限',\n        signDeadLineTooltip: '契約書がこの日付までに署名完了しない場合、引き続き署名を続けることはできません',\n        selectDate: '日時の選択',\n        contractExpireDate: '契約満期日',\n        contractExpireDays: '契約書有効期間（日）',\n        expireDateTooltip: '契約内容の有効期間は後で契約書を管理するのに役立ちます',\n        notNecessary: '任意',\n        dateTips: 'お客様のために契約書有効期日を自動識別しました。確認してください',\n        contractTitleErr: '契約名には特殊文字を含めないでください',\n        contractTitleLengthErr: '契約名には100字を超えないでください',\n        internalNumber: '公司内部编号',\n        toSelect: '選択してください',\n        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n        necessary: '必須項目',\n    },\n    template: {\n        templateList: {\n            linkBoxTip: '関連ファイルキャビネットID：',\n        },\n        dynamicTemplateUpdate: {\n            title: '动态模板新功能上线',\n            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n            connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。',\n        },\n        sendCode: {\n            tip: '現在のテンプレート設定が送信コード生成条件を満たしていません。以下の要件を満たしているかどうかチェックしてください。',\n            fail: {\n                1: '空白の文書は含まれていません',\n                2: '契約者はただ1つの可変者(署名とccを含む)、そして可変者は第1の操作者でなければなりません;署名者は必ず捺印所を設けなければなりません',\n                3: '契約者のうち固定者のアカウントは空ではありません',\n                4: '送信前承認はありません',\n                5: '送信者はフィールドが空にならないように必ず記入します（記述フィールドと契約内容フィールドを含みます）',\n                6: '非テンプレートの組み合わせです',\n            },\n        },\n        sendCodeGuide: {\n            title: '发送码高级功能说明',\n            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n            tip1: {\n                main: '1. 上上签',\n                sub: '',\n                line1: '向上上签申请开通档案+、合同预审、智能预审',\n                line2: '开通后可以到对应的菜单中操作使用',\n            },\n            tip2: {\n                main: '2. 档案柜管理员',\n                sub: '创建档案柜、配置智能预审',\n                line1: '',\n                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',\n            },\n            tip3: {\n                main: '3. 签约方',\n                sub: '扫码填资料、获取合同文件',\n                line1: '',\n                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',\n            },\n            tip4: {\n                main: '4. 档案柜管理员',\n                sub: '',\n                line1: '查看签约的相对方、发送的合同情况',\n                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',\n            },\n        },\n    },\n    style: {\n        signature: {\n            text: {\n                x: '34',\n                fontSize: '18',\n            },\n        },\n    },\n    resetPwd: {\n        title: '安全提示！',\n        notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',\n        oldLabel: '原密码',\n        oldPlaceholder: '请输入原密码',\n        newLabel: '新密码',\n        newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',\n        submit: '确定',\n        errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',\n        oldRule: '原密码不能为空',\n        newRule: '新密码不能为空',\n        success: '修改成功',\n    },\n    personAuthIntercept: {\n        title: '邀请您以',\n        name: '姓名：',\n        id: '身份证号：',\n        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        desMore: '根据发起方要求，您还需要补充',\n        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        tips: '实名认证通过后，可查看并签署合同。',\n        goOn: '是我本人，开始认证',\n        goMore: '去补充认证',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '是否继续以当前账号签署？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n        confirmReject: '是的，我要驳回实名',\n    },\n    authIntercept: {\n        title: '要求您以：',\n        name: '姓名为：',\n        id: '身份证号为：',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        descNoSame1: '签署合同。',\n        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n        tips: '注：身份信息完全一致才能签署合同',\n        goOn: '是我本人，开始认证',\n        goHome: '我知道了',\n        goMore: '去补充认证',\n        authTip: '进行实名认证。',\n        viewAndSign: '完成认证后即可查看和签署合同',\n        tips2: '注：企业名称完全一致才能查看和签署合同。',\n        requestOtherAnth: '请求他人认证',\n        goAuth: '去实名认证',\n        requestSomeoneList: '请求以下人员完成实名认证：',\n        ent: '企业',\n        entName: '企业名称',\n        account: '账号',\n        accountPH: '手机或邮箱',\n        send: '发送',\n        lackEntName: '请填写企业名称',\n        errAccount: '请填写正确的邮箱或手机号',\n        successfulSent: '发送成功',\n    },\n    thirdPartApprovalDialog: {\n        title1: '署名前承認',\n        title2: '承認フロー',\n        content1: '承認後に署名できますので、しばらくお待ちください。',\n        content2: '需由第三方平台（非上上签平台）审批合同。可在上上签平台“接收方视角”页面中启用或关闭第三方平台审批。',\n        cancelBtnText: '承認フローを確認',\n        confirmBtnText: '確認',\n        iKnow: '理解',\n    },\n    endSignEarlyPrompt: {\n        cancel: '取消',\n        confirm: '确认',\n        signPrompt: '签署提示',\n        signTotalCountTip: '本次签署共包含{count}份合同文件',\n        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n        hasSigned: '{count}人已签字',\n        hasNotSigned: '{count}人未签字',\n        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',\n    },\n    commonNomal: {\n        yesterday: '昨日',\n        ssq: 'ベストサイン',\n        ssqPlatform: 'ベストサイン電子契約クラウドプラットフォーム',\n        ssqTestPlatform: '（テスト用限定）ベストサイン電子署名クラウドプラットフォーム',\n        pageExpiredTip: 'ページは有効期限切れです。ページをリロードしてください',\n        pswCodeSimpleTip: 'パスワードは数字・大文字/小文字を含む6～18桁にする必要があります。再設定してください',\n    },\n    transferAdminDialog: {\n        title: '身分確認',\n        transfer: '引き継ぎ',\n        confirmAdmin: '私が管理者主任です',\n        content: 'システムの管理者主任は担当企業印章の管理・契約書の管理およびその他人員権限の管理が必要となります。通常企業の法定代表人・財務管理者・法務管理者・IT部門管理者もしくは企業業務責任者が担当します。| お客様が上記の身分かどうか確認してください。もしも違っていれば、関連する人に引き継いでください。',\n    },\n    choseBoxForReceiver: {\n        dataNeedForReceiver: '契約主体が提出しなければならない資料',\n        dataFromDataBox: '契約主体が提出しなければならない資料はアーカイブスの資料を通じて採集入手しなければなりません',\n        searchTp: 'アーカイブス名または番号を入力してください',\n        search: '検索',\n        boxNotFound: 'アーカイブスが見つかりません',\n        cancel: 'キャンセル',\n        confirm: '確　認',\n    },\n    localCommon: {\n        cancel: 'キャンセル',\n        confirm: '確認',\n        toSelect: '選択してください',\n        seal: '捺印',\n        signature: '署名',\n        signDate: '署名日時',\n        text: 'テキスト',\n        date: '日付',\n        qrCode: '二次元コード',\n        number: 'デジタル',\n        dynamicTable: '動態テンプレート',\n        terms: '契約条項',\n        checkBox: 'チェックボックス',\n        radioBox: 'ラジオボタン',\n        image: '画像',\n        confirmSeal: '業務照合印',\n        confirmRemark: '印章不適合の備考',\n        optional: 'オプション',\n        require: '必須項目',\n        tip: '注意',\n        comboBox: '検索候補',\n    },\n    twoFactor: {\n        signTip: '署名案内',\n        settingTwoFactor: '二要素認証を設定します',\n        step1: '1.バリデータアプリケーションのインストール',\n        step1Tip: '二要素身分認証には、携帯電話アプリケーションのインストールが必要です',\n        step2: '2.QRコードを読み取ります',\n        step2Tip1: 'ダウンロードしたバリデータを使用し、下記のQRコードを読み取ってください（携帯電話の時刻が現在時刻と一致していないと、二要素身分認証ができませんのでご注意ください）。',\n        step2Tip2: '画面には、二要素認証に必要な6桁の認証コードが表示されます。',\n        step3: '3.6桁の認証コードを入力します',\n        step3Tip: '画面に表示される認証コードを入力してください',\n        verifyCode6: '6桁の認証コード',\n        iosAddress: 'IOS版ダウンロードはこちら：',\n        androidAddress: 'Andro証明書版ダウンロードはこちら：',\n        chromeVerify: 'google身分バリデータ',\n        nextBtn: '次のステップ',\n        confirmSign: '署名を確認する',\n        dynamicCode: 'ワンタイムパスワード',\n        password: '署名コード',\n        pleaseInput: '入力してください',\n        twoFactorTip: '送信者の要求により、暗号化署名方法で署名する必要があります。',\n        passwordTip: '送信者に、署名コード入力での署名が求められています。',\n        twoFactorAndPasswordTip: '送信者の要請により、二要素認証を通り、正しい署名コードを入力することで署名を完了する必要があります',\n        passwordTip2: '署名コードは、送信者様へお問い合わせください。署名コードを入力しての署名となります。',\n        dynamicVerifyInfo: '正しいワンタイムパスワードを入力してください。認証システムのアカウントを設定し直した場合は、最新のワンタイムパスワードを入力してください。',\n    },\n    functionSupportDialog: {\n        title: '機能紹介',\n        inputTip: '関連する使用条件がある場合は、以下のフォームにご記入ください。ベストサインでは24時間以内に専門スタップからご連絡し、サービスのご案内を差し上げます。',\n        useSence: '使用場面',\n        useSenceTip: '例：人事/販売業者/物流帳票...',\n        estimatedOnlineTime: '予定オンライン時間',\n        requireContent: '必要内容',\n        requireContentTip: '御社がどのように電子契約を使用するのか大まかに説明ください。弊社からお客様のために適切なプランを作成します。',\n        getSupport: '専門サービスサポートの提供',\n        callServiceHotline: 'すぐにカスタマーサポート：<EMAIL>',\n        useSenceNotEmpty: '使用場面は空欄に出来ません',\n        requrieContentNotEmpty: '必要内容は空欄にできません',\n        oneWeek: '一週間以内',\n        oneMonth: '一ヶ月以内',\n        other: 'その他',\n        submitSuccess: 'コミット成功',\n        submitTrial: '試用に提出する',\n        toTrial: '試用に行く',\n        trialTip: '試用申請を提出すると、現在の機能はすぐに開通し、試用することができる。機能の使用を支援するために、次の表により多くのニーズを記入することができます。電子契約コンサルタントに署名すると、サービスが提供されます。',\n        applyTrial: '試用申請',\n        trialSuccTip: '機能が開通しましたので、お試しください',\n        goBuy: '直接購入',\n        trialTipMap: {\n            title: '試用の心得',\n            tip1: '1. オープン即使用、有効期間は7日間；',\n            tip2: '2. 試用期間中、機能は無料；',\n            tip3: '3. ビジネス主体ごとに、1つの機能を1回だけ試用する機会；',\n            tip4: '4. 試用期間中は自由に購入でき、無停止で使用できる；',\n            tip5: '5. 試用が終了した場合は、スキャンコードを確認して、詳細については前に署名した専門コンサルタントに連絡してください：',\n        },\n        contactAdminTip: '使用する場合は、Enterprise Administrator {tip} にお問い合わせください。',\n        trialEndTip: '試用期間が終了したら、クリックして購入してください',\n        trialRemainDayTip: '試用期間が残りました{day}日、クリックして購入してください',\n        trialEnd: '試用機能終了',\n        trialEndMap: {\n            deactivateTip: '{feature}機能は無効になっています。構成をクリアするか、継続料金を払ってから使用できます。',\n            feature1: '契約付属資料',\n            remove1: '構成の消去方法は、「テンプレートの編集」-構成済みの追加契約付属資料を見つけて削除します。',\n            feature2: '手書きの筆跡認識',\n            remove2: '構成をクリアする方法は、「テンプレートを編集」-構成済みのストローク認識を見つけて削除します。',\n            feature3: '契約装飾：スリット章+透かし',\n            remove3: '構成の消去方法は、「テンプレートの編集」-構成された契約装飾を見つけて削除します。',\n            feature4: '契約送信承認',\n            remove4: '構成方法の消去：Company Console-すべての承認フローを非アクティブにする',\n        },\n    },\n    setSignPwdDialog: {\n        tip: '設定後、署名パスワードが優先的に適用されます。ユーザーセンターもしくはアカウント管理から設定を変更出来ます。',\n        saveAndReturnSign: '保存して署名に戻る',\n        changeEmailVerify: 'メール認証に切り替える',\n        changePhoneVerify: '携帯電話認証に切り替える',\n    },\n    contractCompare: {\n        reUpload: '再アップロード',\n        title: '契約書比較',\n        packagePurchase: 'プラン購入',\n        packagePurchaseTitle: '【{title}機能】プラン購入',\n        myPackage: 'マイプラン',\n        packageDetail: 'プラン詳細',\n        per: '次',\n        packageContent: 'プラン内容：',\n        num: '{type}次数',\n        limitTime: '有効期間',\n        month: '月',\n        payNow: '今すぐ購入',\n        contactUs: 'お問い合わせ | QRコードをスキャンして専門アドバイザーに相談',\n        compareInfo1: 'ご利用ガイド：',\n        compareInfo2: '{index}、購入{type}に基づく利用可能限度額は、対応する企業の全メンバーが利用可能です。個人でご利用の場合は、画面上部のログイン主体を個人アカウントに切り替えてください。',\n        compareInfo3: '{index}、アップロードした契約書の{per}数に基づく使用量計算',\n        codePay: 'QRコードをスキャンして支払い',\n        aliPay: 'アリペイ支払い',\n        wxPay: 'ウィーチャット支払い',\n        payIno: '機能有効化 | 購入対象 | 支払金額',\n        finishPay: '支払い完了',\n        paySuccess: '購入成功',\n        originFile: '原本契約書ファイル',\n        compareFile: '比較用契約書ファイル',\n        documentSelect: 'ファイルを選択',\n        comparisonResult: '比較結果',\n        history: '履歴',\n        currentHistory: '文書記録',\n        noData: 'データなし',\n        differences: '{num}つの差異',\n        historyLog: '{num}件の記録',\n        uploadLimit: '比較するファイルをドラッグ＆ドロップ | 対応形式: PDF（スキャン済み含む）、Word',\n        dragInfo: 'マウスを離してアップロード',\n        uploadError: '非対応ファイル形式',\n        pageNum: '第{page}页',\n        difference: '差異 {num}',\n        download: '比較結果をダウンロード',\n        comparing: '契約書比較中...',\n        tip: '通知',\n        confirm: '確定',\n        toBuy: '購入へ進む',\n        translate: '契約書翻訳',\n        doCompare: '比較',\n        doTranslate: '翻訳',\n        review: '契約書審査',\n        doReview: '審査',\n        reviewUploadFile: '審査対象ファイルをここにドラッグ＆ドロップ',\n        reviewUpload: '審査基準ファイルをドラッグ | 例：「販売代理店管理規程」「調達規程」| 対応形式: PDF、Word',\n        reviewOriginFile: '審査対象契約書',\n        reviewTargetFile: '審査基準',\n        reviewResult: '審査結果',\n        uploadReviewFile: '審査基準ファイルをアップロード',\n        risk: 'リスク項目 {num}',\n        risks: '{num}つのリスク項目',\n        startReview: '審査を開始',\n        reviewing: '契約書審査中...',\n        noRisk: '審査完了 - リスク未検出',\n        allowUpload: '審査の参考として、「調達管理規程」などの社内規程、コンプライアンス規定、部門ガイドラインをアップロード可能です。| 例: 「甲は契約締結後5日以内に支払いを完了すること」 | 如：甲方需在合同签订后的5日内完成付款。',\n        notAllowUpload: '曖昧な表現や原則的な説明を審査基準に使用しないでください。 | 例: 「全ての契約条項は関連法令に準拠すること」',\n        resumeReview: '次のファイルへ進む',\n        close: '閉じる',\n        extract: '契約書抽出',\n        extractTitle: '抽出するキーワード',\n        selectKeyword: '下記の「キーワード」から選択',\n        keyword: 'キーワード',\n        addKeyword: '追加{keyword}',\n        introduce: '{keyword}定義',\n        startExtract: '抽出を開始',\n        extractTargetFile: '抽出対象契約書',\n        extractKeyWord: 'キーワード抽出',\n        extracting: '契約書抽出中...',\n        extractResult: '抽出結果',\n        extractUploadFile: '抽出するファイルをドラッグ＆ドロップ',\n        needExtractKeyword: '抽出するキーワードを選択',\n        summary: '契約書要約',\n        keySummary: 'キーワード要約',\n        deleteKeywordConfirm: 'このキーワードを削除しますか',\n        keywordPosition: 'キーワードの位置',\n        riskJudgement: 'リスク判定',\n        judgeTargetContract: 'リスク判定を開始',\n        interpretTargetContract: 'AI解読済み契約書',\n        startJudge: 'リスク評価を開始する',\n        startInterpret: '解読開始',\n        uploadText: 'リスク判断が必要なファイルのアップロードをお願いします',\n        interpretText: '解読が必要なファイルをアップロードしてください',\n        startTips: 'これでリスクはんだんを始められです',\n        interpretTips: 'これよりAI解読を開始いたします',\n        infoExtract: '抽出情報',\n    },\n    batchImport: {\n        iKnow: '理解',\n    },\n    templateCommon: {\n        tip: '注意',\n    },\n    mgapprovenote: {\n        SAQ: '问卷调查',\n        analyze: '分析',\n        annotate: '批注',\n        law: '法规',\n        case: '案例',\n        translate: '翻译',\n        mark: '标记',\n        tips: '上記内容はAIによって生成されたものであり、上上签の立場を代表するものではありません。参考のためだけにご利用ください。このマークを削除または変更しないでください。',\n        limit: '使用回数が上限に達しました。継続使用の需要がある場合はフォームにご記入ください。カスタマーサービスよりご連絡いたします。',\n        confirmTxt: '記入する',\n        content: '関連する段落',\n        experience: '業務経験',\n        datas: '関連データ',\n        terms: '類似条項',\n        original: '引用元',\n        export: 'エクスポート',\n        preview: '契約書閲覧',\n        history: '履歴',\n    },\n    sealConfirm: {\n        title: '印章確認ページ',\n        header: '印章確認',\n        signerEnt: '契約企業：',\n        abnormalSeal: '異常な印章：',\n        sealNormal: '印章正常',\n        tip1: '印章が正常に使用可能かどうかをご確認ください。正常な場合は、「印章正常」ボタンをクリックしてください。以降、この会社がこの印章を使用する際、システムは異常通知を送信しなくなります。',\n        tip2: '印章に問題がある場合は、速やかに契約相手と連絡を取り、印章の変更を行い、契約書を再送して署名してもらうか、または却下して再署名を行ってください。',\n    },\n    userCentral: userCentral,\n    ...console,\n    ...consts,\n    keyInfoExtract: {\n        operate: '情報抽出',\n        contractType: 'Predicted Contract Types',\n        tooltips: 'Select the Key Information',\n        predictText: 'Predicting',\n        extractText: 'Extracting',\n        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n        result: 'result:',\n    },\n    judgeRisk: {\n        title: 'AI弁護士',\n        deepInference: 'AI法務',\n        showAll: 'Show More',\n        tips: 'Judging',\n        dialogTitle: '「AI弁護士」による契約書の審査',\n        aiInterpret: 'AI解読',\n    },\n    sealDistribute: {\n        requestSeal: '押印権限申請',\n        company: '会社',\n        applicant: '申請者',\n        accountID: 'アカウント',\n        submissionTime: '時間',\n        status: '状態',\n        agree: '同意済み',\n        unAgree: '却下済',\n        ifAgree: '同意する場合、',\n        applyTime: '申請者の印章利用期間は：',\n        to: '～',\n        placeHolderTime: '年-月-日',\n        senderCompany: '送信企業',\n        documentTitle: '契約書タイトル',\n        sealApplicationScope: '印章使用範囲',\n        applyforSeal: '印章申請',\n        reject: '却下',\n        approve: '同意',\n    },\n    sealApproval: {\n        sealRight: '印鑑権限',\n        requestSeal: '押印権限申請',\n        allEntContract: '全ての企業からの契約書',\n        partEntContract: '一部の企業からの契約書：',\n        pleaseInputRight: '権限を入力してください',\n        successTransfer: '引継ぎ完了後、',\n        getRight: '上記の権限を取得するか、新しい署名権限を直接編集して割り当てることができます。',\n        signAllEntContract: '全ての企業からの契約書に署名',\n        sign: '署名',\n        sendContract: '送信された契約書',\n        sealUseTime: '印鑑使用期間：',\n        currentStatus: '現在の状態：',\n        takeBackSeal: '印鑑を回収',\n        agree: '同意',\n        hasAgree: '同意済み',\n        hasReject: '却下済み',\n        hasDone: '完了',\n        ask: 'が',\n        giveYou: 'の印鑑をあなたに割り当てます',\n        hopeAsk: 'は',\n        hopeGive: 'の印鑑を引き継ぎたい',\n        hopeGiveYou: 'の関連印鑑をあなたに引き継ぎます',\n        noSettingTime: '時間設定なし',\n        approvalSuccess: '承認成功',\n        getSealSuccess: '印鑑取得成功',\n    },\n    workspace: {\n        create: '作成済み',\n        reviewing: '審査中',\n        completed: '完了',\n        noData: 'データなし',\n        introduce: '{keyword}の説明',\n        termsDetail: '用語の詳細',\n        extractFormat: '抽出形式',\n        optional: '任意',\n        required: '必須',\n        operate: '操作',\n        detail: '詳細',\n        delete: '削除',\n        agreement: {\n            uploadError: 'アップロードできるのはPDF、DOC、またはDOCXファイルのみです。',\n            extractionRequest: '抽出リクエストが送信されました。後で用語リストで結果を確認してください。',\n            upload: 'ファイルのアップロード',\n            define: '用語の定義',\n            extract: '協定の抽出',\n            drag: 'ファイルをここにドラッグするか、',\n            add: 'クリックして追加',\n            format: 'doc、docx、pdf形式をサポート',\n            fileName: 'ファイル名',\n            status: '状態',\n            completed: 'アップロード完了',\n            failed: 'アップロード失敗',\n            size: 'サイズ',\n            terms: '用語',\n            success: 'ファイル抽出完了、合計{total}個',\n            ongoing: 'ファイル抽出中... 合計{total}個',\n            tips: 'この画面をスキップしても抽出結果に影響はありません',\n            others: '続けてアップロード',\n            result: '抽出結果のダウンロードページに移動',\n            curProgress: '現在の進捗: ',\n            refresh: '更新',\n            details: '{successNum}個ロード済み、合計{length}個',\n            start: '抽出開始',\n            more: 'ファイルを追加',\n            skip: '抽出をスキップし、アップロードを完了。',\n            tiqu: '抽出開始',\n            chouqu: '抽出開始',\n        },\n        review: {\n            distribution: '配布審査',\n            Incomplete: '未終了',\n            createReview: '審査を作成',\n            manageReview: '審査管理',\n            reviewDetail: '審査の詳細',\n            reviewId: '審査番号',\n            reviewStatus: '審査状態',\n            reviewName: '審査名',\n            reviewStartTime: '審査開始時間',\n            reviewCompleteTime: '審査終了時間',\n            reviewDesc: 'バージョン：バージョン{reviewVersion}  |  審査番号：{reviewId}',\n            distribute: '審査を開始',\n            drag: '審査待ちの協定をこの領域にドラッグ',\n            content: '審査待ちの内容',\n            current: '配布待ち記録',\n            history: '履歴記録',\n            page: '第{page}ページ：',\n            users: '審査が必要なユーザー',\n            message: 'メッセージ',\n            modify: '修正',\n            placeholder: '複数のユーザーはセミコロン\";\"で区切ってください',\n            submit: '確定',\n            reupload: '審査の再アップロード',\n            finish: '審査終了',\n            reviewSummary: '審査概要',\n            initiator: '審査の発起人',\n            versionSummary: 'バージョン概要',\n            version: 'バージョン',\n            versionOrder: '第{version}版',\n            curReviewStatus: '現在のバージョン審査状態',\n            curReviewVersion: '現在のバージョン',\n            curReviewPopulation: '現在のバージョン審査人数',\n            curReviewStartTime: '現在のバージョン審査開始時間',\n            curReviewInitiator: '現在のバージョン審査発起人',\n            checkComments: '修正意見を集約表示',\n            overview: '審査結果の概要',\n            reviewer: '審査者',\n            reviewResult: '審査結果',\n            replyTime: '返信時間',\n            agreement: '審査の協定',\n            files: '関連協定',\n            fileName: '協定名',\n            numberOfModificationSuggestions: '修正意見数',\n            uploadTime: 'アップロード時間',\n            download: 'ダウンロード',\n            dispatch: '配布',\n            recent: '最新の審査時間：',\n            replyContent: '審査の返信内容',\n            advice: '協定の修正意見',\n            noIdea: '修正意見なし',\n            origin: '原文内容：',\n            revised: '修正後の内容：',\n            suggestion: '修正意見：',\n            dateMark: '{name} は <span style=\"color: #0988EC\">バージョン {version}</span> に {date} に記載されています',\n            unReviewed: 'まだレビューされていません',\n            revisionFiles: '修正協定',\n            staffReplyAggregation: '修正情報の集約',\n            staffReply: '{name}の審査情報',\n            tips: '提示',\n            tipsContent: '終了後、この審査は配布及び後続操作をサポートしなくなります。続けますか',\n            confirm: '確定',\n            cancel: 'キャンセル',\n            successMessage: '終了しました',\n            PASS: '承認',\n            REJECT: '不承認',\n            uploadErrorMessage: '現在、アップロードできるのはDOCX形式のファイルのみです。',\n            successInitiated: '審査が開始されました',\n            autoDistribute: 'インテリジェント分配',\n            requiredUsers: 'レビューが必要なユーザー',\n            contentToReview: 'レビューするコンテンツ',\n            termDetails: '用語の詳細',\n            term: '用語',\n            aiDistribute: 'AIインテリジェント分配',\n            noData: 'データがありません',\n            docIconAlt: 'ドキュメントアイコン',\n            docxIconAlt: 'DOCXアイコン',\n            pdfIconAlt: 'PDFアイコン',\n            requiredUsersError: 'レビューが必要なユーザーを入力してください',\n            selectContentError: 'レビューするコンテンツを選択してください',\n            initiateReviewSuccess: 'レビューが開始されました',\n            syncInitiated: '同期が開始されました',\n        },\n        contentTracing: {\n            title: '内容追跡',\n            fieldContent: 'フィールド内容',\n            originalResult: 'オリジナル結果',\n            contentSource: '内容の出典',\n            page: 'ページ',\n        },\n    },\n    hubblePackage: {\n        title: '私のパッケージ',\n        details: 'パッケージの詳細',\n        remainingPages: '残りの総ページ数',\n        pages: 'ページ',\n        usedPages: '使用済み',\n        remaining: '利用可能残数',\n        total: '合計',\n        expiryTime: '有効期限',\n        amount: '数量',\n        unitPrice: '単価',\n        copy: '部',\n        words: '千文字',\n    },\n    workspaceIndex: {\n        title: 'ワークスペース',\n        package: 'パッケージ使用量',\n        agreement: '契約管理',\n        review: 'レビュー管理',\n        term: '用語管理',\n    },\n    agreement: {\n        title: '契約管理',\n        exportList: '契約リストをエクスポート',\n        exportAllChecked: 'Excel（すべてのフィールド、選択された契約）',\n        exportCurrentChecked: 'Excel（現在のフィールド、選択された契約）',\n        exportAllMatched: 'Excel（すべてのフィールド、条件に一致）',\n        exportCurrentMatched: 'Excel（現在のフィールド、条件に一致）',\n        add: '契約を追加',\n        upload: '契約をアップロード',\n        operation: '操作',\n        download: 'ダウンロード',\n        details: '詳細',\n        delete: '削除',\n        relatedTaskStatus: '関連する抽出タスクの状態',\n        confirmDelete: '現在の契約を削除しますか？',\n        prompt: 'プロンプト',\n        booleanYes: 'はい',\n        booleanNo: 'いいえ',\n        defaultExportName: 'export.xlsx',\n        taskNotStarted: '抽出タスクは開始されていません',\n        taskStarted: '抽出タスクが開始されました（コンテンツ検索中）',\n        contentSearchCompleted: 'コンテンツ検索が完了しました（結果をフォーマット中）',\n        resultFormattingCompleted: '結果のフォーマットが完了しました（結果を校正中）',\n        resultVerificationCompleted: '結果の校正が完了しました',\n    },\n    filter: {\n        filter: 'フィルター',\n        refreshExtraction: '抽出をリフレッシュ',\n        extractTerms: '用語定義を抽出',\n        refreshList: 'リストをリフレッシュ',\n        currentCondition: '現在の条件で表示されている契約。',\n        when: '時',\n        selectCondition: '条件を選択してください',\n        enterCondition: '条件を入力してください',\n        yes: 'はい',\n        no: 'いいえ',\n        addCondition: '条件を追加',\n        reset: 'リセット',\n        confirm: '確認',\n        and: 'かつ',\n        or: 'または',\n        equals: '等しい',\n        notEquals: '等しくない',\n        contains: '含む',\n        notContains: '含まない',\n        greaterThan: 'より大きい',\n        greaterThanOrEquals: '以上',\n        lessThan: '未満',\n        lessThanOrEquals: '以下',\n        emptyCondition: 'フィルター条件は空にできません',\n    },\n    fieldConfig: {\n        button: 'フィールド設定',\n        header: '表示するフィールド',\n        submit: '完了',\n        cancel: 'キャンセル',\n    },\n    agreementDetail: {\n        detail: '契約詳細',\n        add: '契約を追加',\n        id: '契約番号',\n        file: '契約ファイル',\n        download: '契約をダウンロード',\n        replaceFile: '契約ファイルの置換',\n        uploadFile: '契約ファイルをアップロード',\n        relatedExtractionStatus: '関連する抽出タスクのステータス',\n        dataSource: 'データソース',\n        yes: 'はい',\n        no: 'いいえ',\n        select: '選択してください',\n        input: '入力してください',\n        save: '保存',\n        cancel: 'キャンセル',\n        page: '第 {page} ページ',\n        addDataSource: 'データソースを追加',\n        pageNo: '第',\n        pageSuffix: 'ページ',\n        submit: '送信',\n        inputDataSource: 'データソース内容を入力してください',\n        pageFormatError: 'ページ番号はカンマ区切りの数字のみをサポートします',\n        confirmDelete: '現在のデータソースを削除しますか？',\n        tips: 'ヒント',\n        uploadSuccess: 'アップロード成功',\n    },\n    termManagement: {\n        title: '用語管理',\n        batchDelete: '一括削除',\n        import: '用語のインポート',\n        export: '用語のエクスポート',\n        add: '+ 用語を追加',\n        name: '用語名',\n        definition: '用語の定義',\n        formatRequirement: '抽出形式の要件',\n        dataFormat: 'データ形式',\n        operation: '操作',\n        edit: '編集',\n        delete: '削除',\n        detail: '用語詳細',\n        addTitle: '用語を追加',\n        namePlaceholder: '専門用語を記入してください',\n        definitionPlaceholder: '用語の定義を記入してください',\n        formatRequirementPlaceholder: '用語の抽出形式の要件を記入してください',\n        dataFormatPlaceholder: '期待する抽出用語形式',\n        cancel: 'キャンセル',\n        confirmEdit: '変更を確認',\n        importTitle: '用語のインポート',\n        uploadTemplate: '用語テンプレートファイルをアップロード',\n        downloadTemplate: '用語テンプレートファイルをダウンロード',\n        extractType: {\n            text: 'テキスト',\n            longText: '長いテキスト',\n            date: '日付',\n            number: '数値',\n            boolean: 'はい/いいえ',\n        },\n        importSuccess: 'インポート成功',\n        deleteConfirm: '現在の用語を削除しますか？',\n        prompt: 'プロンプト',\n        nameEmptyError: '用語名は空にできません',\n    },\n    agent: {\n        extractTitle: '情報抽出',\n        riskTitle: 'AI弁護士',\n        feedback: '調査フィードバック',\n        toMini: 'ミニアプリで確認',\n        otherContract: '他の契約書の潜在リスクを分析?',\n        others: 'その他',\n        submit: '送信',\n        autoExtract: '抽出完了まで自動処理継続',\n        autoRisk: '分析プロセスを自動実行',\n        aiGenerated: 'AI Generated - © BestSign',\n        chooseRisk: '解析対象ファイルを選択',\n        chooseExtract: '抽出用ソースファイル指定',\n        analyzing: 'コンテンツ解析実行中',\n        advice: '修正案自動作成中',\n        options: '選択肢生成処理中',\n        inputTips: '正確な情報を入力必須',\n        chargeTip: '残高不足のためチャージ必須',\n        original: '原本',\n        revision: '改善提案',\n        diff: '比較',\n        locate: '原稿位置特定処理中',\n        custom: 'カスタム審査ルールを入力してください',\n        content: '原文の位置',\n        satisfy: '对分析结果满意，继续下一项分析',\n        dissatisfy: '对分析结果不满意，重新进行分析',\n        selectFunc: 'ご希望の機能をお選びください',\n        deepInference: 'AI法務',\n        deepThinking: '深い思考中',\n        deepThoughtCompleted: '深い思考が完了しました',\n        reJudge: '再判断',\n        confirm: '確認',\n        tipsContent: '再判定を行うと利用回数が減ります。続けますか？',\n        useLawyer: 'AI弁護士を起動',\n        interpretFinish: 'AI解読完了',\n        exportPDF: 'PDFレポート出力',\n        defaultExportName: 'export.pdf',\n        exporting: 'レポート出力中... しばらくお待ちください',\n    },\n    authorize: {\n        title: 'ご利用条件\t',\n        content: 'AI契約分析で業務効率化！同意の上、今すぐ体験',\n        cancel: 'いいえ',\n        confirm: '同意して利用開始',\n        contract: '『ハッブル製品利用条件』を確認',\n    },\n    hubbleEntry: {\n        smartAdvisor: 'スマート契約アドバイザー',\n        tooltips: 'この機能は有料です。BestSign電子契約アドバイザーにご連絡ください。',\n        confirm: '了解',\n    },\n    lang: 'ja',\n};\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,sCAAsC;AAE7D,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,cAAc,MAAM,8CAA8C;AAEzE,eAAe;EACX,GAAGV,KAAK;EACR,GAAGC,KAAK;EACR,GAAGC,UAAU;EACb,GAAGC,OAAO;EACV,GAAGO,cAAc;EACjBC,QAAQ,EAAE;IACNC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,wCAAwC;IAClDC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,kBAAkB;IAC9BC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACd,CAAC;EACDC,YAAY,EAAE;IACVC,MAAM,EAAE,4BAA4B;IACpCC,cAAc,EAAE,4BAA4B;IAC5CC,YAAY,EAAE,cAAc;IAC5BC,aAAa,EAAE,QAAQ;IACvBC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,WAAW;IACvBC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,QAAQ;IACrBC,WAAW,EAAE,OAAO;IACpBC,cAAc,EAAE,SAAS;IACzBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,gBAAgB;IAC9BC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,UAAU;IACvBC,SAAS,EAAE,MAAM;IACjBX,KAAK,EAAE,IAAI;IACXY,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE,UAAU;IAC9BC,mBAAmB,EAAE,SAAS;IAC9BC,UAAU,EAAE,WAAW;IACvBC,qBAAqB,EAAE,UAAU;IACjCC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE,MAAM;IAClBC,EAAE,EAAE,GAAG;IACPC,kBAAkB,EAAE,iBAAiB;IACrCC,mBAAmB,EAAE,cAAc;IACnCC,aAAa,EAAE,eAAe;IAC9BC,MAAM,EAAE,WAAW;IACnBC,gBAAgB,EAAE,OAAO;IACzBC,cAAc,EAAE,SAAS;IACzBC,oBAAoB,EAAE,WAAW;IACjCC,WAAW,EAAE,WAAW;IACxBC,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,QAAQ;IAEvBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,qBAAqB;IAC7BC,QAAQ,EAAE,SAAS;IACnBC,iBAAiB,EAAE,SAAS;IAC5BC,GAAG,EAAE,GAAG;IACRC,2BAA2B,EAAE,UAAU;IACvCC,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,QAAQ;IACjBC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,0CAA0C;IACrDC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,2BAA2B;IACvCC,QAAQ,EAAE,KAAK;IACfC,mBAAmB,EAAE,WAAW;IAChC;IACAC,aAAa,EAAE,iBAAiB;IAChCC,mBAAmB,EAAE,mBAAmB;IACxCC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,aAAa;IAC3BC,qBAAqB,EAAE,cAAc;IACrCC,QAAQ,EAAE,UAAU;IACpBC,mBAAmB,EAAE,+BAA+B;IACpDC,sBAAsB,EAAE,SAAS;IACjC;IACAC,oBAAoB,EAAE,OAAO;IAC7BC,gBAAgB,EAAE,SAAS;IAC3BC,mBAAmB,EAAE,SAAS;IAC9BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,QAAQ;IACrBC,iBAAiB,EAAE;EACvB,CAAC;EACD,GAAG/F,IAAI;EACPgG,SAAS,EAAE;IACP3F,KAAK,EAAE,QAAQ;IACf4F,YAAY,EAAE,iBAAiB;IAC/BC,cAAc,EAAE,YAAY;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,cAAc,EAAE,cAAc;IAC9BC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,QAAQ;IACtBC,OAAO,EAAE,gEAAgE;IACzEC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE,qBAAqB;IAClCC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE,0CAA0C;IACtDC,UAAU,EAAE,uBAAuB;IACnCC,OAAO,EAAE,MAAM;IACflG,MAAM,EAAE,MAAM;IACdmG,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,4CAA4C;IAC5DC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,cAAc;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,sBAAsB;IACnCC,EAAE,EAAE,IAAI;IACRC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,6BAA6B;IACtCC,OAAO,EAAE,eAAe;IACxBC,YAAY,EAAE,mCAAmC;IACjDC,iBAAiB,EAAE,0DAA0D;IAC7EC,GAAG,EAAE;MACDC,aAAa,EAAE,yCAAyC;MACxDC,aAAa,EAAE,mCAAmC;MAClDC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,MAAM,EAAE;IAEJ9G,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,MAAM;IACnBE,SAAS,EAAE,MAAM;IACjB2G,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,kBAAkB;IAC/BC,MAAM,EAAE,YAAY;IACpBnH,YAAY,EAAE,MAAM;IACpBM,OAAO,EAAE,eAAe;IACxBF,IAAI,EAAE,MAAM;IACZgC,aAAa,EAAE,eAAe;IAC9BE,gBAAgB,EAAE,OAAO;IACzB8E,WAAW,EAAE,gBAAgB;IAC7BC,kBAAkB,EAAE,YAAY;IAChCC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,WAAW;IACjCC,iBAAiB,EAAE,UAAU;IAC7BC,UAAU,EAAE,SAAS;IACrBC,iBAAiB,EAAE,YAAY;IAC/BC,kBAAkB,EAAE,UAAU;IAC9BC,sBAAsB,EAAE;EAE5B,CAAC;EACD5I,OAAO,EAAE;IACL,GAAGA,OAAO;IACV6I,gBAAgB,EAAE,QAAQ;IAC1BC,WAAW,EAAE,QAAQ;IACrBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,MAAM;IACftC,MAAM,EAAE,aAAa;IACrBuC,WAAW,EAAE,8BAA8B;IAC3CC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,IAAI;IACV/H,IAAI,EAAE,IAAI;IACVgI,OAAO,EAAE,MAAM;IACfC,sBAAsB,EAAE,wBAAwB;IAChDC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,OAAO;IACnBC,eAAe,EAAE,SAAS;IAC1BC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,QAAQ;IACxBC,sBAAsB,EAAE,QAAQ;IAChCC,gBAAgB,EAAE,YAAY;IAC9BC,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE,uBAAuB;IAC7BC,eAAe,EAAE,gBAAgB;IACjCvJ,QAAQ,EAAE,IAAI;IACdwJ,aAAa,EAAE,oBAAoB;IACnCC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACVC,IAAI,EAAE,IAAI;IACVC,cAAc,EAAE;EACpB,CAAC;EACD3K,IAAI,EAAE;IACF,GAAGA,IAAI;IACPA,IAAI,EAAE,IAAI;IACV4K,gBAAgB,EAAE,MAAM;IACxBC,kBAAkB,EAAE,MAAM;IAC1BC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,OAAO;IAC1BC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,QAAQ;IACtBC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,mBAAmB;IAC9BC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,eAAe;IAC5BC,mBAAmB,EAAE,wBAAwB;IAC7CC,IAAI,EAAE,SAAS;IACfC,gBAAgB,EAAE,MAAM;IACxBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,YAAY;IAClBC,GAAG,EAAE,OAAO;IACZC,eAAe,EAAE,WAAW;IAC5BC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,oCAAoC;IAC1CC,QAAQ,EAAE;MACN,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,MAAM;MACT,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,QAAQ;IACzBC,SAAS,EAAE;MACP,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,QAAQ;IACtBC,mBAAmB,EAAE;EACzB,CAAC;EACDC,SAAS,EAAE;IACPC,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,QAAQ;IACvBC,QAAQ,EAAE,MAAM;IAChBC,yBAAyB,EAAE,cAAc;IACzCC,iBAAiB,EAAE,OAAO;IAC1BC,kBAAkB,EAAE,UAAU;IAC9BC,YAAY,EAAE;MACVhD,IAAI,EAAE,IAAI;MACViD,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACd,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,MAAM;IACtBC,gBAAgB,EAAE,MAAM;IACxBC,aAAa,EAAE,OAAO;IACtBC,iBAAiB,EAAE,MAAM;IACzBC,wBAAwB,EAAE,UAAU;IACpCC,WAAW,EAAE;MACTC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE;IACZ,CAAC;IACDC,YAAY,EAAE,OAAO;IACrBC,cAAc,EAAE,kBAAkB;IAClCC,yBAAyB,EAAE,SAAS;IACpCC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,KAAK;IACpBC,0BAA0B,EAAE,qBAAqB;IACjDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,KAAK;IACfC,0BAA0B,EAAE,WAAW;IACvCC,kBAAkB,EAAE,QAAQ;IAC5BC,IAAI,EAAE,GAAG;IACTC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,IAAI;IACZC,kBAAkB,EAAE,QAAQ;IAC5BC,eAAe,EAAE;MACbC,UAAU,EAAE,MAAM;MAClBC,4BAA4B,EAAE,QAAQ;MACtCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACd,CAAC;IACDC,yBAAyB,EAAE;MACvBjP,KAAK,EAAE,6DAA6D;MACpEkP,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,MAAM;MACf5O,MAAM,EAAE;IACZ,CAAC;IACD6O,WAAW,EAAE;MACTpP,KAAK,EAAE,2BAA2B;MAClCkP,IAAI,EAAE,IAAI;MACVxI,OAAO,EAAE,IAAI;MACbnG,MAAM,EAAE;IACZ,CAAC;IACD8O,2BAA2B,EAAE,gBAAgB;IAC7CC,SAAS,EAAE,MAAM;IACjBC,sBAAsB,EAAE,cAAc;IACtCC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,OAAO;IACtBC,OAAO,EAAE,IAAI;IACbC,kBAAkB,EAAE,QAAQ;IAC5BC,aAAa,EAAE,aAAa;IAC5BC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,QAAQ;IAC1BC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,MAAM;IACjBpF,WAAW,EAAE,MAAM;IACnBwC,MAAM,EAAE,KAAK;IACb6C,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,QAAQ;IACtBC,kBAAkB,EAAE,QAAQ;IAC5B;IACAC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,KAAK;IACbnG,YAAY,EAAE,MAAM;IACpBoG,MAAM,EAAE,IAAI;IACZtR,IAAI,EAAE,IAAI;IACVuR,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE,MAAM;IACxBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,IAAI;IACXC,mBAAmB,EAAE,wBAAwB;IAC7CC,UAAU,EAAE,OAAO;IACnBC,cAAc,EAAE,OAAO;IACvBC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,oBAAoB,EAAE,QAAQ;IAC9BC,eAAe,EAAE,QAAQ;IACzBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZjF,QAAQ,EAAE,IAAI;IACdkF,cAAc,EAAE,QAAQ;IACxBC,mBAAmB,EAAE,UAAU;IAC/BC,SAAS,EAAE,KAAK;IAChBC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,KAAK;IACX7E,MAAM,EAAE,KAAK;IACb8E,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE,aAAa;IACjC3F,IAAI,EAAE,IAAI;IACV4F,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,IAAI;IACXnH,IAAI,EAAE,IAAI;IACVoH,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,6BAA6B,EAAE,QAAQ;IACvCC,gCAAgC,EAAE,SAAS;IAC3CC,wBAAwB,EAAE,QAAQ;IAClCC,sBAAsB,EAAE,UAAU;IAClCC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,KAAK;IACZC,qBAAqB,EAAE,UAAU;IACjCC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,MAAM;IACxBC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfrO,GAAG,EAAE,IAAI;IACT0D,IAAI,EAAE,IAAI;IACV4K,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,GAAG;IACVC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,mBAAmB;IAC/BC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,MAAM;IAChB9J,SAAS,EAAE,KAAK;IAChB+J,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACRC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,GAAG,EAAE,IAAI;IACTC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,SAAS;IACrBR,UAAU,EAAE,QAAQ;IACpBS,QAAQ,EAAE,gBAAgB;IAC1BC,oBAAoB,EAAE,WAAW;IACjCC,UAAU,EAAE,MAAM;IAClBC,cAAc,EAAE,sBAAsB;IACtCC,gBAAgB,EAAE,kBAAkB;IACpCC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;EACd,CAAC;EACDlG,YAAY,EAAE;IACVmG,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,+BAA+B;IACpDC,YAAY,EAAE,QAAQ;IACtB7L,YAAY,EAAE,OAAO;IACrB8L,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,yCAAyC;IAC9DC,UAAU,EAAE,OAAO;IACnBjG,kBAAkB,EAAE,OAAO;IAC3BkG,kBAAkB,EAAE,YAAY;IAChCC,iBAAiB,EAAE,6BAA6B;IAChDC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,kCAAkC;IAC5CC,gBAAgB,EAAE,qBAAqB;IACvCC,sBAAsB,EAAE,qBAAqB;IAC7CC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,qBAAqB;IACtCC,SAAS,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE;IACNC,YAAY,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACDC,qBAAqB,EAAE;MACnB3X,KAAK,EAAE,WAAW;MAClB4X,cAAc,EAAE,2BAA2B;MAC3CC,SAAS,EAAE,gGAAgG;MAC3GC,SAAS,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACNjC,GAAG,EAAE,4DAA4D;MACjEkC,IAAI,EAAE;QACF,CAAC,EAAE,gBAAgB;QACnB,CAAC,EAAE,sEAAsE;QACzE,CAAC,EAAE,0BAA0B;QAC7B,CAAC,EAAE,aAAa;QAChB,CAAC,EAAE,oDAAoD;QACvD,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,aAAa,EAAE;MACXjY,KAAK,EAAE,WAAW;MAClBoK,IAAI,EAAE,wMAAwM;MAC9M8N,IAAI,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE;MACX,CAAC;MACDC,IAAI,EAAE;QACFJ,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDE,IAAI,EAAE;QACFL,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDG,IAAI,EAAE;QACFN,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDI,KAAK,EAAE;IACHjE,SAAS,EAAE;MACPkE,IAAI,EAAE;QACFC,CAAC,EAAE,IAAI;QACPC,QAAQ,EAAE;MACd;IACJ;EACJ,CAAC;EACDC,QAAQ,EAAE;IACN9Y,KAAK,EAAE,OAAO;IACd6S,MAAM,EAAE,gCAAgC;IACxCkG,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,sBAAsB;IACtCC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,0BAA0B;IACpCC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBlS,OAAO,EAAE;EACb,CAAC;EACDmS,mBAAmB,EAAE;IACjBvZ,KAAK,EAAE,MAAM;IACbwZ,IAAI,EAAE,KAAK;IACXC,EAAE,EAAE,OAAO;IACXC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,qCAAqC;IACjDC,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,cAAc;IACnBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,0BAA0B;IAC3CC,aAAa,EAAE;EACnB,CAAC;EACDC,aAAa,EAAE;IACX9a,KAAK,EAAE,OAAO;IACdwZ,IAAI,EAAE,MAAM;IACZC,EAAE,EAAE,QAAQ;IACZI,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCI,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,qCAAqC;IAClDJ,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,WAAW;IACjBI,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE,OAAO;IACfc,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE,sBAAsB;IAC7BC,gBAAgB,EAAE,QAAQ;IAC1BC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,eAAe;IACnCC,GAAG,EAAE,IAAI;IACT/R,OAAO,EAAE,MAAM;IACfkC,OAAO,EAAE,IAAI;IACb8P,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,cAAc;IAC1BC,cAAc,EAAE;EACpB,CAAC;EACDC,uBAAuB,EAAE;IACrBC,MAAM,EAAE,OAAO;IACf/U,MAAM,EAAE,OAAO;IACf5G,QAAQ,EAAE,2BAA2B;IACrCC,QAAQ,EAAE,oDAAoD;IAC9D2b,aAAa,EAAE,UAAU;IACzBC,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE;EACX,CAAC;EACDC,kBAAkB,EAAE;IAChBzb,MAAM,EAAE,IAAI;IACZmG,OAAO,EAAE,IAAI;IACbuV,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,qBAAqB;IACxCC,YAAY,EAAE,mCAAmC;IACjDC,SAAS,EAAE,aAAa;IACxBC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE;EACnB,CAAC;EACDC,WAAW,EAAE;IACTC,SAAS,EAAE,IAAI;IACfnb,GAAG,EAAE,QAAQ;IACbob,WAAW,EAAE,wBAAwB;IACrCC,eAAe,EAAE,gCAAgC;IACjDC,cAAc,EAAE,6BAA6B;IAC7CC,gBAAgB,EAAE;EACtB,CAAC;EACDC,mBAAmB,EAAE;IACjB7c,KAAK,EAAE,MAAM;IACb8c,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,WAAW;IACzBhO,OAAO,EAAE;EACb,CAAC;EACDiO,mBAAmB,EAAE;IACjBC,mBAAmB,EAAE,oBAAoB;IACzCC,eAAe,EAAE,gDAAgD;IACjEC,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,gBAAgB;IAC7B9c,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE;EACb,CAAC;EACD4W,WAAW,EAAE;IACT/c,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE,IAAI;IACb2Q,QAAQ,EAAE,UAAU;IACpBkG,IAAI,EAAE,IAAI;IACV9I,SAAS,EAAE,IAAI;IACf+I,QAAQ,EAAE,MAAM;IAChB7E,IAAI,EAAE,MAAM;IACZ7O,IAAI,EAAE,IAAI;IACV2T,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,UAAU;IACxBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,UAAU;IACzBC,QAAQ,EAAE,OAAO;IACjBC,OAAO,EAAE,MAAM;IACfrI,GAAG,EAAE,IAAI;IACTsI,QAAQ,EAAE;EACd,CAAC;EACDC,SAAS,EAAE;IACPC,OAAO,EAAE,MAAM;IACfC,gBAAgB,EAAE,aAAa;IAC/BC,KAAK,EAAE,wBAAwB;IAC/BC,QAAQ,EAAE,oCAAoC;IAC9CC,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE,qFAAqF;IAChGC,SAAS,EAAE,gCAAgC;IAC3CC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,iBAAiB;IAC7BC,cAAc,EAAE,sBAAsB;IACtCC,YAAY,EAAE,eAAe;IAC7BC,OAAO,EAAE,QAAQ;IACjBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,UAAU;IACvBC,YAAY,EAAE,gCAAgC;IAC9CC,WAAW,EAAE,4BAA4B;IACzCC,uBAAuB,EAAE,mDAAmD;IAC5EC,YAAY,EAAE,4CAA4C;IAC1DC,iBAAiB,EAAE;EACvB,CAAC;EACDC,qBAAqB,EAAE;IACnB7f,KAAK,EAAE,MAAM;IACb8f,QAAQ,EAAE,6EAA6E;IACvFC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,mBAAmB,EAAE,WAAW;IAChCC,cAAc,EAAE,MAAM;IACtBC,iBAAiB,EAAE,wDAAwD;IAC3EC,UAAU,EAAE,eAAe;IAC3BC,kBAAkB,EAAE,sCAAsC;IAC1DC,gBAAgB,EAAE,eAAe;IACjCC,sBAAsB,EAAE,eAAe;IACvCC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,QAAQ;IACvBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,0GAA0G;IACpHC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,qBAAqB;IACnCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE;MACTlhB,KAAK,EAAE,OAAO;MACdkY,IAAI,EAAE,sBAAsB;MAC5BK,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,6BAA6B;MACnC0I,IAAI,EAAE;IACV,CAAC;IACDC,eAAe,EAAE,qDAAqD;IACtEC,WAAW,EAAE,2BAA2B;IACxCC,iBAAiB,EAAE,iCAAiC;IACpDC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;MACTC,aAAa,EAAE,mDAAmD;MAClEC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,+CAA+C;MACxDC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,iDAAiD;MAC1DC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,2CAA2C;MACpDC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdpM,GAAG,EAAE,wDAAwD;IAC7DqM,iBAAiB,EAAE,WAAW;IAC9BC,iBAAiB,EAAE,aAAa;IAChCC,iBAAiB,EAAE;EACvB,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE,SAAS;IACnBviB,KAAK,EAAE,OAAO;IACdwiB,eAAe,EAAE,OAAO;IACxBC,oBAAoB,EAAE,kBAAkB;IACxCC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE,OAAO;IACtBC,GAAG,EAAE,GAAG;IACRC,cAAc,EAAE,QAAQ;IACxBjX,GAAG,EAAE,UAAU;IACfkX,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,kCAAkC;IAC7CC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,4FAA4F;IAC1GC,YAAY,EAAE,qCAAqC;IACnDC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,YAAY;IACnBC,MAAM,EAAE,qBAAqB;IAC7BC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,WAAW;IACvBC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,WAAW;IACxBC,UAAU,EAAE,WAAW;IACvBtb,WAAW,EAAE,+CAA+C;IAC5Dub,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,WAAW;IACxBC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,UAAU;IACtBrX,QAAQ,EAAE,aAAa;IACvBsX,SAAS,EAAE,WAAW;IACtB1O,GAAG,EAAE,IAAI;IACTpP,OAAO,EAAE,IAAI;IACb+d,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,uBAAuB;IACzCC,YAAY,EAAE,qDAAqD;IACnEC,gBAAgB,EAAE,SAAS;IAC3BC,gBAAgB,EAAE,MAAM;IACxBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,iBAAiB;IACnCC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,OAAO;IACpBC,SAAS,EAAE,WAAW;IACtBC,MAAM,EAAE,eAAe;IACvBC,WAAW,EAAE,8GAA8G;IAC3HC,cAAc,EAAE,0DAA0D;IAC1EC,YAAY,EAAE,WAAW;IACzB9R,KAAK,EAAE,KAAK;IACZ+R,OAAO,EAAE,OAAO;IAChBC,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,aAAa;IACxBC,YAAY,EAAE,OAAO;IACrBC,iBAAiB,EAAE,SAAS;IAC5BC,cAAc,EAAE,SAAS;IACzBC,UAAU,EAAE,WAAW;IACvBC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,oBAAoB;IACvCC,kBAAkB,EAAE,cAAc;IAClCC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,SAAS;IACrBC,oBAAoB,EAAE,gBAAgB;IACtCC,eAAe,EAAE,UAAU;IAC3BC,aAAa,EAAE,OAAO;IACtBC,mBAAmB,EAAE,UAAU;IAC/BC,uBAAuB,EAAE,WAAW;IACpCC,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,MAAM;IACtBC,UAAU,EAAE,6BAA6B;IACzCC,aAAa,EAAE,yBAAyB;IACxCC,SAAS,EAAE,mBAAmB;IAC9BC,aAAa,EAAE,kBAAkB;IACjCC,WAAW,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACTzL,KAAK,EAAE;EACX,CAAC;EACD0L,cAAc,EAAE;IACZ3R,GAAG,EAAE;EACT,CAAC;EACD4R,aAAa,EAAE;IACXC,GAAG,EAAE,MAAM;IACXC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,IAAI;IACVrD,SAAS,EAAE,IAAI;IACf3Y,IAAI,EAAE,IAAI;IACVgO,IAAI,EAAE,mFAAmF;IACzFiO,KAAK,EAAE,8DAA8D;IACrEC,UAAU,EAAE,MAAM;IAClBlZ,OAAO,EAAE,QAAQ;IACjBmZ,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,OAAO;IACdvK,KAAK,EAAE,MAAM;IACbwK,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,OAAO;IAChBvE,OAAO,EAAE;EACb,CAAC;EACDwE,WAAW,EAAE;IACTvoB,KAAK,EAAE,SAAS;IAChBwoB,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,MAAM;IAClBzQ,IAAI,EAAE,4FAA4F;IAClGK,IAAI,EAAE;EACV,CAAC;EACD9Y,WAAW,EAAEA,WAAW;EACxB,GAAGD,OAAO;EACV,GAAGK,MAAM;EACT+oB,cAAc,EAAE;IACZ5b,OAAO,EAAE,MAAM;IACfnC,YAAY,EAAE,0BAA0B;IACxCge,QAAQ,EAAE,4BAA4B;IACtCC,WAAW,EAAE,YAAY;IACzBC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,yJAAyJ;IACvKC,MAAM,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACPlpB,KAAK,EAAE,OAAO;IACdmpB,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,WAAW;IACpBrP,IAAI,EAAE,SAAS;IACfsP,WAAW,EAAE,kBAAkB;IAC/BC,WAAW,EAAE;EACjB,CAAC;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,QAAQ;IACrBtoB,OAAO,EAAE,IAAI;IACbuoB,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,OAAO;IAClBC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,cAAc;IACzBC,EAAE,EAAE,GAAG;IACPC,eAAe,EAAE,OAAO;IACxBC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,SAAS;IACxBC,oBAAoB,EAAE,QAAQ;IAC9BC,YAAY,EAAE,MAAM;IACpBpY,MAAM,EAAE,IAAI;IACZ8B,OAAO,EAAE;EACb,CAAC;EACDuW,YAAY,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBhB,WAAW,EAAE,QAAQ;IACrBiB,cAAc,EAAE,aAAa;IAC7BC,eAAe,EAAE,cAAc;IAC/BC,gBAAgB,EAAE,aAAa;IAC/BC,eAAe,EAAE,SAAS;IAC1BC,QAAQ,EAAE,yCAAyC;IACnDC,kBAAkB,EAAE,gBAAgB;IACpCnrB,IAAI,EAAE,IAAI;IACVmL,YAAY,EAAE,UAAU;IACxBigB,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,OAAO;IACrBpB,KAAK,EAAE,IAAI;IACXqB,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,IAAI;IACb5Q,GAAG,EAAE,GAAG;IACR6Q,OAAO,EAAE,gBAAgB;IACzBC,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE,YAAY;IACtBC,WAAW,EAAE,kBAAkB;IAC/BC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE,MAAM;IACvBC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPC,MAAM,EAAE,MAAM;IACdrG,SAAS,EAAE,KAAK;IAChBlT,SAAS,EAAE,IAAI;IACf2R,MAAM,EAAE,OAAO;IACfiC,SAAS,EAAE,cAAc;IACzB4F,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,MAAM;IACrB7N,QAAQ,EAAE,IAAI;IACd8N,QAAQ,EAAE,IAAI;IACdhf,OAAO,EAAE,IAAI;IACb8F,MAAM,EAAE,IAAI;IACZ8C,MAAM,EAAE,IAAI;IACZqW,SAAS,EAAE;MACP5H,WAAW,EAAE,qCAAqC;MAClD6H,iBAAiB,EAAE,sCAAsC;MACzD5lB,MAAM,EAAE,aAAa;MACrB6lB,MAAM,EAAE,OAAO;MACftG,OAAO,EAAE,OAAO;MAChBuG,IAAI,EAAE,kBAAkB;MACxBC,GAAG,EAAE,UAAU;MACfC,MAAM,EAAE,qBAAqB;MAC7BC,QAAQ,EAAE,OAAO;MACjB3C,MAAM,EAAE,IAAI;MACZtX,SAAS,EAAE,UAAU;MACrBka,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,KAAK;MACX7O,KAAK,EAAE,IAAI;MACXxW,OAAO,EAAE,qBAAqB;MAC9BslB,OAAO,EAAE,uBAAuB;MAChC3S,IAAI,EAAE,2BAA2B;MACjC4S,MAAM,EAAE,WAAW;MACnB1D,MAAM,EAAE,mBAAmB;MAC3B2D,WAAW,EAAE,SAAS;MACtBC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,gCAAgC;MACzCC,KAAK,EAAE,MAAM;MACbzhB,IAAI,EAAE,SAAS;MACf0hB,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE;IACZ,CAAC;IACDrI,MAAM,EAAE;MACJsI,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,OAAO;MACrBC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE,OAAO;MACrBC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,eAAe,EAAE,QAAQ;MACzBC,kBAAkB,EAAE,QAAQ;MAC5BC,UAAU,EAAE,gDAAgD;MAC5DC,UAAU,EAAE,OAAO;MACnB1B,IAAI,EAAE,mBAAmB;MACzBrd,OAAO,EAAE,SAAS;MAClBgf,OAAO,EAAE,QAAQ;MACjBhK,OAAO,EAAE,MAAM;MACfiK,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAE,OAAO;MAChBxf,MAAM,EAAE,IAAI;MACZyf,WAAW,EAAE,2BAA2B;MACxChV,MAAM,EAAE,IAAI;MACZiV,QAAQ,EAAE,YAAY;MACtBC,MAAM,EAAE,MAAM;MACdC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,QAAQ;MACnBC,cAAc,EAAE,SAAS;MACzBC,OAAO,EAAE,OAAO;MAChBC,YAAY,EAAE,aAAa;MAC3BC,eAAe,EAAE,cAAc;MAC/BC,gBAAgB,EAAE,UAAU;MAC5BC,mBAAmB,EAAE,cAAc;MACnCC,kBAAkB,EAAE,gBAAgB;MACpCC,kBAAkB,EAAE,eAAe;MACnCC,aAAa,EAAE,WAAW;MAC1BC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,KAAK;MACf/J,YAAY,EAAE,MAAM;MACpBgK,SAAS,EAAE,MAAM;MACjBlD,SAAS,EAAE,OAAO;MAClBmD,KAAK,EAAE,MAAM;MACb7C,QAAQ,EAAE,KAAK;MACf8C,+BAA+B,EAAE,OAAO;MACxCC,UAAU,EAAE,UAAU;MACtBpiB,QAAQ,EAAE,QAAQ;MAClBqiB,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,UAAU;MAClBC,YAAY,EAAE,SAAS;MACvB7nB,MAAM,EAAE,SAAS;MACjB8nB,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,SAAS;MAClBC,UAAU,EAAE,OAAO;MACnBC,QAAQ,EAAE,iFAAiF;MAC3FC,UAAU,EAAE,eAAe;MAC3BC,aAAa,EAAE,MAAM;MACrBC,qBAAqB,EAAE,SAAS;MAChCC,UAAU,EAAE,aAAa;MACzBnW,IAAI,EAAE,IAAI;MACVoW,WAAW,EAAE,qCAAqC;MAClDzpB,OAAO,EAAE,IAAI;MACbnG,MAAM,EAAE,OAAO;MACf6vB,cAAc,EAAE,QAAQ;MACxBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,KAAK;MACbC,kBAAkB,EAAE,gCAAgC;MACpDC,gBAAgB,EAAE,YAAY;MAC9BC,cAAc,EAAE,YAAY;MAC5BC,aAAa,EAAE,cAAc;MAC7BC,eAAe,EAAE,aAAa;MAC9BC,WAAW,EAAE,OAAO;MACpBC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,cAAc;MAC5B7M,MAAM,EAAE,WAAW;MACnB8M,UAAU,EAAE,YAAY;MACxBC,WAAW,EAAE,UAAU;MACvBC,UAAU,EAAE,SAAS;MACrBC,kBAAkB,EAAE,uBAAuB;MAC3CC,kBAAkB,EAAE,sBAAsB;MAC1CC,qBAAqB,EAAE,cAAc;MACrCC,aAAa,EAAE;IACnB,CAAC;IACDC,cAAc,EAAE;MACZtxB,KAAK,EAAE,MAAM;MACbuxB,YAAY,EAAE,SAAS;MACvBC,cAAc,EAAE,SAAS;MACzBC,aAAa,EAAE,OAAO;MACtBzD,IAAI,EAAE;IACV;EACJ,CAAC;EACD0D,aAAa,EAAE;IACX1xB,KAAK,EAAE,SAAS;IAChB8sB,OAAO,EAAE,UAAU;IACnB6E,cAAc,EAAE,UAAU;IAC1BC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACf/c,IAAI,EAAE,GAAG;IACTgd,KAAK,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;IACZpyB,KAAK,EAAE,SAAS;IAChBqyB,OAAO,EAAE,UAAU;IACnBpG,SAAS,EAAE,MAAM;IACjBpH,MAAM,EAAE,QAAQ;IAChBgM,IAAI,EAAE;EACV,CAAC;EACD5E,SAAS,EAAE;IACPjsB,KAAK,EAAE,MAAM;IACbsyB,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,0BAA0B;IAC5CC,oBAAoB,EAAE,yBAAyB;IAC/CC,gBAAgB,EAAE,wBAAwB;IAC1CC,oBAAoB,EAAE,uBAAuB;IAC7CrG,GAAG,EAAE,OAAO;IACZ/lB,MAAM,EAAE,WAAW;IACnBqsB,SAAS,EAAE,IAAI;IACfzlB,QAAQ,EAAE,QAAQ;IAClB4f,OAAO,EAAE,IAAI;IACblX,MAAM,EAAE,IAAI;IACZgd,iBAAiB,EAAE,cAAc;IACjCC,aAAa,EAAE,eAAe;IAC9BC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,KAAK;IAChBC,iBAAiB,EAAE,aAAa;IAChCC,cAAc,EAAE,iBAAiB;IACjCC,WAAW,EAAE,yBAAyB;IACtCC,sBAAsB,EAAE,4BAA4B;IACpDC,yBAAyB,EAAE,0BAA0B;IACrDC,2BAA2B,EAAE;EACjC,CAAC;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,OAAO;IACfC,iBAAiB,EAAE,WAAW;IAC9BC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,YAAY;IACzBC,gBAAgB,EAAE,kBAAkB;IACpCC,IAAI,EAAE,GAAG;IACTC,eAAe,EAAE,aAAa;IAC9BC,cAAc,EAAE,aAAa;IAC7BC,GAAG,EAAE,IAAI;IACTC,EAAE,EAAE,KAAK;IACTC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbxtB,OAAO,EAAE,IAAI;IACb3C,GAAG,EAAE,IAAI;IACTlB,EAAE,EAAE,KAAK;IACTsxB,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,OAAO;IACpBC,mBAAmB,EAAE,IAAI;IACzBC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACTC,MAAM,EAAE,SAAS;IACjBrM,MAAM,EAAE,WAAW;IACnBrP,MAAM,EAAE,IAAI;IACZ5Y,MAAM,EAAE;EACZ,CAAC;EACDu0B,eAAe,EAAE;IACbhiB,MAAM,EAAE,MAAM;IACduZ,GAAG,EAAE,OAAO;IACZ5S,EAAE,EAAE,MAAM;IACVsb,IAAI,EAAE,QAAQ;IACd7nB,QAAQ,EAAE,WAAW;IACrB8nB,WAAW,EAAE,WAAW;IACxBxf,UAAU,EAAE,eAAe;IAC3Byf,uBAAuB,EAAE,iBAAiB;IAC1CC,UAAU,EAAE,QAAQ;IACpBnB,GAAG,EAAE,IAAI;IACTC,EAAE,EAAE,KAAK;IACTmB,MAAM,EAAE,UAAU;IAClBC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,IAAI;IACV90B,MAAM,EAAE,OAAO;IACfytB,IAAI,EAAE,cAAc;IACpBsH,aAAa,EAAE,WAAW;IAC1BC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,KAAK;IACjBrc,MAAM,EAAE,IAAI;IACZsc,eAAe,EAAE,mBAAmB;IACpCC,eAAe,EAAE,2BAA2B;IAC5C7C,aAAa,EAAE,mBAAmB;IAClC9Y,IAAI,EAAE,KAAK;IACX4b,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZ51B,KAAK,EAAE,MAAM;IACb61B,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE,UAAU;IAClBzN,MAAM,EAAE,WAAW;IACnBgE,GAAG,EAAE,SAAS;IACd7S,IAAI,EAAE,KAAK;IACXuc,UAAU,EAAE,OAAO;IACnBC,iBAAiB,EAAE,SAAS;IAC5BC,UAAU,EAAE,OAAO;IACnBtD,SAAS,EAAE,IAAI;IACf9hB,IAAI,EAAE,IAAI;IACV+E,MAAM,EAAE,IAAI;IACZ9C,MAAM,EAAE,MAAM;IACdojB,QAAQ,EAAE,OAAO;IACjBC,eAAe,EAAE,eAAe;IAChCC,qBAAqB,EAAE,gBAAgB;IACvCC,4BAA4B,EAAE,qBAAqB;IACnDC,qBAAqB,EAAE,YAAY;IACnC/1B,MAAM,EAAE,OAAO;IACfg2B,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,UAAU;IACvBC,cAAc,EAAE,qBAAqB;IACrCC,gBAAgB,EAAE,qBAAqB;IACvCC,WAAW,EAAE;MACThe,IAAI,EAAE,MAAM;MACZie,QAAQ,EAAE,QAAQ;MAClB9sB,IAAI,EAAE,IAAI;MACV4T,MAAM,EAAE,IAAI;MACZmZ,OAAO,EAAE;IACb,CAAC;IACDC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,eAAe;IAC9BjE,MAAM,EAAE,OAAO;IACfkE,cAAc,EAAE;EACpB,CAAC;EACDC,KAAK,EAAE;IACHnR,YAAY,EAAE,MAAM;IACpBoR,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,UAAU;IAClBC,aAAa,EAAE,iBAAiB;IAChC1K,MAAM,EAAE,KAAK;IACbxT,MAAM,EAAE,IAAI;IACZme,WAAW,EAAE,cAAc;IAC3BC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,2BAA2B;IACxCC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE,cAAc;IAC7BC,SAAS,EAAE,YAAY;IACvB/vB,MAAM,EAAE,UAAU;IAClBgwB,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,eAAe;IAC1B1P,QAAQ,EAAE,IAAI;IACd2P,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,oBAAoB;IAC5BnpB,OAAO,EAAE,OAAO;IAChBopB,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,gBAAgB;IAC5BlP,aAAa,EAAE,MAAM;IACrBmP,YAAY,EAAE,OAAO;IACrBC,oBAAoB,EAAE,aAAa;IACnCC,OAAO,EAAE,KAAK;IACd9xB,OAAO,EAAE,IAAI;IACbypB,WAAW,EAAE,yBAAyB;IACtCsI,SAAS,EAAE,UAAU;IACrBC,eAAe,EAAE,QAAQ;IACzBC,SAAS,EAAE,WAAW;IACtB1F,iBAAiB,EAAE,YAAY;IAC/B2F,SAAS,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACP74B,KAAK,EAAE,QAAQ;IACf+O,OAAO,EAAE,yBAAyB;IAClCxO,MAAM,EAAE,KAAK;IACbmG,OAAO,EAAE,UAAU;IACnBoyB,QAAQ,EAAE;EACd,CAAC;EACDC,WAAW,EAAE;IACTC,YAAY,EAAE,cAAc;IAC5BnQ,QAAQ,EAAE,uCAAuC;IACjDniB,OAAO,EAAE;EACb,CAAC;EACDuyB,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}