{"ast": null, "code": "import LangSwitch from 'components/langSwitch';\nimport CustomFooter from 'components/customFooter/CustomFooter.vue';\nimport { mapState, mapGetters } from 'vuex';\nimport JaFooter from './JaFooter/index.vue';\nexport default {\n  components: {\n    LangSwitch,\n    CustomFooter,\n    JaFooter\n  },\n  data() {\n    return {\n      // openHost: window.location.host.includes('info') ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',\n      // host: window.location.host.includes('info') ? 'www.bestsign.info' : 'www.bestsign.cn',\n      openHost: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',\n      host: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'www.bestsign.info' : 'www.bestsign.cn',\n      backEndCopyRightRange: this.$cookie.get('copyRightRange'),\n      defaultCopyRightRange: `2014-${new Date().getFullYear()}`\n    };\n  },\n  computed: {\n    isBrand() {\n      return ~~this.$cookie.get('isBrand') === 1;\n    },\n    // 判断二级域名是否是ent\n    isEntHost() {\n      const fullHost = window.location.href;\n      const secondHost = fullHost.split('.')[0].split('//')[1];\n      return secondHost === 'ent';\n    },\n    lang() {\n      return this.$i18n.locale;\n    },\n    ...mapState(['commonHeaderInfo']),\n    accessToken() {\n      return Vue.$cookie.get('access_token') === null ? '' : Vue.$cookie.get('access_token');\n    },\n    ...mapGetters(['getIsForeignVersion']),\n    isHubblePage() {\n      return this.$route.meta.isHubblePage === true;\n    }\n  },\n  methods: {\n    async handleFooterClick(type) {\n      const anonymousID = await this.$sensors.getAnonymousID();\n      const url = `?pageFrom=ent&identifyId=${anonymousID}`;\n      const map = {\n        about: `https://${this.host}/about/about-us${url}`,\n        contact: `https://${this.host}/contact-us${url}`,\n        recruitment: `https://${this.host}/about/join-us${url}`,\n        help: `https://${this.host}/help/FAQ${url}&entId=${this.commonHeaderInfo.currentEntId}&token=${this.accessToken}`\n      };\n      window.open(map[type]);\n      return false;\n    }\n  }\n};", "map": {"version": 3, "names": ["LangSwitch", "CustomFooter", "mapState", "mapGetters", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components", "data", "openHost", "GLOBAL", "ENV_NAME", "host", "backEndCopyRightRange", "$cookie", "get", "defaultCopyRightRange", "Date", "getFullYear", "computed", "isBrand", "isEntHost", "fullHost", "window", "location", "href", "secondHost", "split", "lang", "$i18n", "locale", "accessToken", "<PERSON><PERSON>", "isHubblePage", "$route", "meta", "methods", "handleFooterClick", "type", "anonymousID", "$sensors", "getAnonymousID", "url", "map", "about", "contact", "recruitment", "help", "commonHeaderInfo", "currentEntId", "open"], "sources": ["src/components/register_footer/RegisterFooter.vue"], "sourcesContent": ["<!-- 通用footer -->\n<template>\n    <JaFooter v-if=\"getIsForeignVersion\" />\n    <!-- 二级域名下显示定制footer -->\n    <CustomFooter v-else-if=\"!isEntHost\" class=\"login-footer\"></CustomFooter>\n    <footer v-else class=\"register-footer\">\n        <ul class=\"clear font-size-zero\">\n            <li class=\"lang-switch-btn\">\n                <LangSwitch></LangSwitch>\n                <i>|</i>\n            </li>\n            <li class=\"open-platform\">\n                <a target=\"_blank\" :href=\"`https://${openHost}/#/login`\">\n                    <span>{{ $t('common.openPlatform') }}</span>\n                </a>\n                <i>|</i>\n            </li>\n            <li class=\"about\">\n                <a target=\"_blank\" @click=\"handleFooterClick('about')\">\n                    <span>{{ $t('common.aboutBestSign') }}</span>\n                </a>\n                <i>|</i>\n            </li>\n            <li class=\"contact\">\n                <a target=\"_blank\" @click=\"handleFooterClick('contact')\">\n                    <span>{{ $t('common.contact') }}</span>\n                </a>\n                <i>|</i>\n            </li>\n            <li class=\"recruitment\">\n                <a target=\"_blank\" @click=\"handleFooterClick('recruitment')\">\n                    <span>{{ $t('common.recruitment') }}</span>\n                </a>\n                <i>|</i>\n            </li>\n            <li class=\"help\">\n                <a target=\"_blank\" @click=\"handleFooterClick('help')\">\n                    <span>{{ $t('common.help') }}</span>\n                </a>\n                <i>|</i>\n            </li>\n            <li class=\"copyright\">\n                <span>V4.0.0 {{ $t('common.copyright') }} © {{ backEndCopyRightRange || defaultCopyRightRange }} {{ $t('common.company') }}</span>\n                <i>|</i>\n            </li>\n            <li class=\"on-record\">\n                <span>{{ $t('home.record') }}</span>\n            </li>\n            <li class=\"on-record\" v-if=\"isHubblePage\">\n                <i>|</i>\n                <span>{{ $t('commonFooter.hubbleRecordId') }}</span>\n            </li>\n        </ul>\n    </footer>\n</template>\n\n<script>\n\nimport LangSwitch from 'components/langSwitch';\nimport CustomFooter from 'components/customFooter/CustomFooter.vue';\nimport { mapState, mapGetters } from 'vuex';\nimport JaFooter from './JaFooter/index.vue';\nexport default {\n    components: {\n        LangSwitch,\n        CustomFooter,\n        JaFooter,\n    },\n    data() {\n        return {\n            // openHost: window.location.host.includes('info') ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',\n            // host: window.location.host.includes('info') ? 'www.bestsign.info' : 'www.bestsign.cn',\n            openHost: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',\n            host: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'www.bestsign.info' : 'www.bestsign.cn',\n            backEndCopyRightRange: this.$cookie.get('copyRightRange'),\n            defaultCopyRightRange: `2014-${new Date().getFullYear()}`,\n        };\n    },\n    computed: {\n        isBrand() {\n            return ~~this.$cookie.get('isBrand') === 1;\n        },\n        // 判断二级域名是否是ent\n        isEntHost() {\n            const fullHost = window.location.href;\n            const secondHost = fullHost.split('.')[0].split('//')[1];\n            return secondHost === 'ent';\n        },\n        lang() {\n            return this.$i18n.locale;\n        },\n        ...mapState(['commonHeaderInfo']),\n        accessToken() {\n            return Vue.$cookie.get('access_token') === null ? '' : Vue.$cookie.get('access_token');\n        },\n        ...mapGetters(['getIsForeignVersion']),\n        isHubblePage() {\n            return this.$route.meta.isHubblePage === true;\n        },\n    },\n    methods: {\n        async handleFooterClick(type) {\n            const anonymousID  = await this.$sensors.getAnonymousID();\n            const url = `?pageFrom=ent&identifyId=${anonymousID}`;\n            const map = {\n                about: `https://${this.host}/about/about-us${url}`,\n                contact: `https://${this.host}/contact-us${url}`,\n                recruitment: `https://${this.host}/about/join-us${url}`,\n                help: `https://${this.host}/help/FAQ${url}&entId=${this.commonHeaderInfo.currentEntId}&token=${this.accessToken}`,\n\n            };\n            window.open(map[type]);\n            return false;\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\">\n\t$border-color: #ddd;\n\tfooter.register-footer {\n        box-sizing: border-box;\n\t\twidth: 100%;\n\t\theight: 35px;\n\t\t// line-height: 35px;\n\t\tpadding-top: 10px;\n\t\t// padding-bottom: 15px;\n\t\tborder-top: 1px solid $border-color;\n        background-color: #f6f6f6;\n\t\tul {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\ttext-align: center;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\tli {\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tfont-size: 12px;\n\t\t\t\tcolor: #666;\n                cursor: pointer;\n\t\t\t\ti {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tmargin: 0 5px;\n\t\t\t\t\tcolor: #d6d6d6;\n\t\t\t\t}\n\n\t\t\t\tspan:last-child {\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\n\t\t\t\ti:last-child {\n\t\t\t\t\tmargin: 0 10px;\n\t\t\t\t}\n\n                &.lang-switch-btn {\n                    span {\n                        font-size: 12px;\n                        color: #999999;\n                    }\n                    i.el-icon-ssq-diqiu {\n                        margin: 0;\n                        padding-right: 5px;\n                        vertical-align: bottom;\n                    }\n                }\n\t\t\t}\n\t\t}\n\t}\n\n</style>\n"], "mappings": "AA0DA,OAAAA,UAAA;AACA,OAAAC,YAAA;AACA,SAAAC,QAAA,EAAAC,UAAA;AACA,OAAAC,QAAA;AACA;EACAC,UAAA;IACAL,UAAA;IACAC,YAAA;IACAG;EACA;EACAE,KAAA;IACA;MACA;MACA;MACAC,QAAA,OAAAC,MAAA,CAAAC,QAAA;MACAC,IAAA,OAAAF,MAAA,CAAAC,QAAA;MACAE,qBAAA,OAAAC,OAAA,CAAAC,GAAA;MACAC,qBAAA,cAAAC,IAAA,GAAAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA;MACA,cAAAN,OAAA,CAAAC,GAAA;IACA;IACA;IACAM,UAAA;MACA,MAAAC,QAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;MACA,MAAAC,UAAA,GAAAJ,QAAA,CAAAK,KAAA,SAAAA,KAAA;MACA,OAAAD,UAAA;IACA;IACAE,KAAA;MACA,YAAAC,KAAA,CAAAC,MAAA;IACA;IACA,GAAA1B,QAAA;IACA2B,YAAA;MACA,OAAAC,GAAA,CAAAlB,OAAA,CAAAC,GAAA,iCAAAiB,GAAA,CAAAlB,OAAA,CAAAC,GAAA;IACA;IACA,GAAAV,UAAA;IACA4B,aAAA;MACA,YAAAC,MAAA,CAAAC,IAAA,CAAAF,YAAA;IACA;EACA;EACAG,OAAA;IACA,MAAAC,kBAAAC,IAAA;MACA,MAAAC,WAAA,cAAAC,QAAA,CAAAC,cAAA;MACA,MAAAC,GAAA,+BAAAH,WAAA;MACA,MAAAI,GAAA;QACAC,KAAA,kBAAAhC,IAAA,kBAAA8B,GAAA;QACAG,OAAA,kBAAAjC,IAAA,cAAA8B,GAAA;QACAI,WAAA,kBAAAlC,IAAA,iBAAA8B,GAAA;QACAK,IAAA,kBAAAnC,IAAA,YAAA8B,GAAA,eAAAM,gBAAA,CAAAC,YAAA,eAAAlB,WAAA;MAEA;MACAR,MAAA,CAAA2B,IAAA,CAAAP,GAAA,CAAAL,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}