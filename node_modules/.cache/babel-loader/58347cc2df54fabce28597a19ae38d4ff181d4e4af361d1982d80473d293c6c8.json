{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"ul\", {\n    staticClass: \"hubble-chat__body-box\",\n    on: {\n      scroll: function ($event) {\n        return _vm.$emit(\"loadMore\", $event);\n      }\n    }\n  }, [!_vm.messages.length ? _c(\"li\", {\n    staticClass: \"hubble-chat__body-empty\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"views/agent/img/empty.png\"),\n      alt: \"\"\n    }\n  }), _c(\"p\", [_vm._v(\"在下方输入或在合同上选取后点击“Hubble”，进行提问\")])]) : _vm._e(), _vm._l(_vm.currentMessageList, function (topic, index) {\n    return _c(\"li\", {\n      key: index,\n      staticClass: \"message\"\n    }, [topic.question ? _c(\"div\", {\n      staticClass: \"question\"\n    }, [_c(\"img\", {\n      staticClass: \"avatar\",\n      attrs: {\n        src: require(\"views/agent/img/avatar.png\"),\n        alt: \"\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"message-content\"\n    }, [topic.quote ? _c(\"div\", {\n      staticClass: \"quote chat-quote\",\n      on: {\n        click: function ($event) {\n          return _vm.$emit(\"selectQuote\", topic.questionDocumentQuote);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(topic.quote) + \" \")]) : _vm._e(), _vm._v(\" 问题：\" + _vm._s(topic.question) + \" \")])]) : _vm._e(), _c(\"span\", {\n      staticClass: \"time\"\n    }, [_vm._v(_vm._s(topic.chatTime))]), topic.answer ? _c(\"div\", {\n      staticClass: \"answer\"\n    }, [_c(\"img\", {\n      staticClass: \"avatar\",\n      attrs: {\n        src: require(\"views/agent/img/AIAvatar.png\"),\n        alt: \"\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"message-content\"\n    }, [_vm._v(\" \" + _vm._s(topic.answer) + \" \"), _vm.showCursor(index) ? _c(\"span\", {\n      staticClass: \"cursor\"\n    }) : _vm._e(), _c(\"br\"), _c(\"span\", {\n      staticClass: \"explain\"\n    }, [_vm._v(\"~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记\")])])]) : _vm._e(), _c(\"div\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: topic.chatId,\n        expression: \"topic.chatId\"\n      }],\n      staticClass: \"related\"\n    }, [_vm.showAnswerDocumentQuotes(topic) ? [_vm._v(\" 相关内容： \"), _vm._l(topic.answerDocumentQuotes, function (quote, i) {\n      return _c(\"span\", {\n        key: i,\n        on: {\n          click: function ($event) {\n            return _vm.$emit(\"selectQuote\", quote);\n          }\n        }\n      }, [_vm._v(_vm._s(quote.pageNumber))]);\n    })] : _vm._e(), _c(\"el-tooltip\", {\n      attrs: {\n        \"open-delay\": 500,\n        effect: \"dark\",\n        content: \"删除对话\",\n        placement: \"top\"\n      }\n    }, [_c(\"i\", {\n      staticClass: \"operate-icon el-icon-ssq-Hubbleshanchu\",\n      on: {\n        click: function ($event) {\n          return _vm.handleDelete(index);\n        }\n      }\n    })]), _c(\"el-tooltip\", {\n      attrs: {\n        \"open-delay\": 500,\n        effect: \"dark\",\n        content: \"开启连续对话\",\n        placement: \"top\"\n      }\n    }, [!_vm.isContinuousChat ? _c(\"i\", {\n      staticClass: \"operate-icon el-icon-ssq-Hubblelianxuduihua\",\n      on: {\n        click: function ($event) {\n          return _vm.$emit(\"showContinuousChat\", topic.chatId);\n        }\n      }\n    }) : _vm._e()])], 2)]);\n  })], 2);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "scroll", "$event", "$emit", "messages", "length", "attrs", "src", "require", "alt", "_v", "_e", "_l", "currentMessageList", "topic", "index", "key", "question", "quote", "click", "questionDocumentQuote", "_s", "chatTime", "answer", "showCursor", "directives", "name", "rawName", "value", "chatId", "expression", "showAnswerDocumentQuotes", "answerDocumentQuotes", "i", "pageNumber", "effect", "content", "placement", "handleDelete", "isContinuousChat", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/chat/chatView/chatList/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"ul\",\n    {\n      staticClass: \"hubble-chat__body-box\",\n      on: {\n        scroll: function ($event) {\n          return _vm.$emit(\"loadMore\", $event)\n        },\n      },\n    },\n    [\n      !_vm.messages.length\n        ? _c(\"li\", { staticClass: \"hubble-chat__body-empty\" }, [\n            _c(\"img\", {\n              attrs: { src: require(\"views/agent/img/empty.png\"), alt: \"\" },\n            }),\n            _c(\"p\", [\n              _vm._v(\"在下方输入或在合同上选取后点击“Hubble”，进行提问\"),\n            ]),\n          ])\n        : _vm._e(),\n      _vm._l(_vm.currentMessageList, function (topic, index) {\n        return _c(\"li\", { key: index, staticClass: \"message\" }, [\n          topic.question\n            ? _c(\"div\", { staticClass: \"question\" }, [\n                _c(\"img\", {\n                  staticClass: \"avatar\",\n                  attrs: {\n                    src: require(\"views/agent/img/avatar.png\"),\n                    alt: \"\",\n                  },\n                }),\n                _c(\"div\", { staticClass: \"message-content\" }, [\n                  topic.quote\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass: \"quote chat-quote\",\n                          on: {\n                            click: function ($event) {\n                              return _vm.$emit(\n                                \"selectQuote\",\n                                topic.questionDocumentQuote\n                              )\n                            },\n                          },\n                        },\n                        [_vm._v(\" \" + _vm._s(topic.quote) + \" \")]\n                      )\n                    : _vm._e(),\n                  _vm._v(\" 问题：\" + _vm._s(topic.question) + \" \"),\n                ]),\n              ])\n            : _vm._e(),\n          _c(\"span\", { staticClass: \"time\" }, [_vm._v(_vm._s(topic.chatTime))]),\n          topic.answer\n            ? _c(\"div\", { staticClass: \"answer\" }, [\n                _c(\"img\", {\n                  staticClass: \"avatar\",\n                  attrs: {\n                    src: require(\"views/agent/img/AIAvatar.png\"),\n                    alt: \"\",\n                  },\n                }),\n                _c(\"div\", { staticClass: \"message-content\" }, [\n                  _vm._v(\" \" + _vm._s(topic.answer) + \" \"),\n                  _vm.showCursor(index)\n                    ? _c(\"span\", { staticClass: \"cursor\" })\n                    : _vm._e(),\n                  _c(\"br\"),\n                  _c(\"span\", { staticClass: \"explain\" }, [\n                    _vm._v(\n                      \"~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记\"\n                    ),\n                  ]),\n                ]),\n              ])\n            : _vm._e(),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: topic.chatId,\n                  expression: \"topic.chatId\",\n                },\n              ],\n              staticClass: \"related\",\n            },\n            [\n              _vm.showAnswerDocumentQuotes(topic)\n                ? [\n                    _vm._v(\" 相关内容： \"),\n                    _vm._l(topic.answerDocumentQuotes, function (quote, i) {\n                      return _c(\n                        \"span\",\n                        {\n                          key: i,\n                          on: {\n                            click: function ($event) {\n                              return _vm.$emit(\"selectQuote\", quote)\n                            },\n                          },\n                        },\n                        [_vm._v(_vm._s(quote.pageNumber))]\n                      )\n                    }),\n                  ]\n                : _vm._e(),\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"删除对话\",\n                    placement: \"top\",\n                  },\n                },\n                [\n                  _c(\"i\", {\n                    staticClass: \"operate-icon el-icon-ssq-Hubbleshanchu\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.handleDelete(index)\n                      },\n                    },\n                  }),\n                ]\n              ),\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"开启连续对话\",\n                    placement: \"top\",\n                  },\n                },\n                [\n                  !_vm.isContinuousChat\n                    ? _c(\"i\", {\n                        staticClass:\n                          \"operate-icon el-icon-ssq-Hubblelianxuduihua\",\n                        on: {\n                          click: function ($event) {\n                            return _vm.$emit(\"showContinuousChat\", topic.chatId)\n                          },\n                        },\n                      })\n                    : _vm._e(),\n                ]\n              ),\n            ],\n            2\n          ),\n        ])\n      }),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,IAAI,EACJ;IACEE,WAAW,EAAE,uBAAuB;IACpCC,EAAE,EAAE;MACFC,MAAM,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACxB,OAAON,GAAG,CAACO,KAAK,CAAC,UAAU,EAAED,MAAM,CAAC;MACtC;IACF;EACF,CAAC,EACD,CACE,CAACN,GAAG,CAACQ,QAAQ,CAACC,MAAM,GAChBR,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;IACRS,KAAK,EAAE;MAAEC,GAAG,EAAEC,OAAO,CAAC,2BAA2B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC9D,CAAC,CAAC,EACFZ,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACc,EAAE,CAAC,8BAA8B,CAAC,CACvC,CAAC,CACH,CAAC,GACFd,GAAG,CAACe,EAAE,CAAC,CAAC,EACZf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACiB,kBAAkB,EAAE,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACrD,OAAOlB,EAAE,CAAC,IAAI,EAAE;MAAEmB,GAAG,EAAED,KAAK;MAAEhB,WAAW,EAAE;IAAU,CAAC,EAAE,CACtDe,KAAK,CAACG,QAAQ,GACVpB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,QAAQ;MACrBO,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;QAC1CC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5Ce,KAAK,CAACI,KAAK,GACPrB,EAAE,CACA,KAAK,EACL;MACEE,WAAW,EAAE,kBAAkB;MAC/BC,EAAE,EAAE;QACFmB,KAAK,EAAE,SAAAA,CAAUjB,MAAM,EAAE;UACvB,OAAON,GAAG,CAACO,KAAK,CACd,aAAa,EACbW,KAAK,CAACM,qBACR,CAAC;QACH;MACF;IACF,CAAC,EACD,CAACxB,GAAG,CAACc,EAAE,CAAC,GAAG,GAAGd,GAAG,CAACyB,EAAE,CAACP,KAAK,CAACI,KAAK,CAAC,GAAG,GAAG,CAAC,CAC1C,CAAC,GACDtB,GAAG,CAACe,EAAE,CAAC,CAAC,EACZf,GAAG,CAACc,EAAE,CAAC,MAAM,GAAGd,GAAG,CAACyB,EAAE,CAACP,KAAK,CAACG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAC9C,CAAC,CACH,CAAC,GACFrB,GAAG,CAACe,EAAE,CAAC,CAAC,EACZd,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAO,CAAC,EAAE,CAACH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACyB,EAAE,CAACP,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAAC,EACrER,KAAK,CAACS,MAAM,GACR1B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAS,CAAC,EAAE,CACnCF,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,QAAQ;MACrBO,KAAK,EAAE;QACLC,GAAG,EAAEC,OAAO,CAAC,8BAA8B,CAAC;QAC5CC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACc,EAAE,CAAC,GAAG,GAAGd,GAAG,CAACyB,EAAE,CAACP,KAAK,CAACS,MAAM,CAAC,GAAG,GAAG,CAAC,EACxC3B,GAAG,CAAC4B,UAAU,CAACT,KAAK,CAAC,GACjBlB,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAS,CAAC,CAAC,GACrCH,GAAG,CAACe,EAAE,CAAC,CAAC,EACZd,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAU,CAAC,EAAE,CACrCH,GAAG,CAACc,EAAE,CACJ,uCACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,GACFd,GAAG,CAACe,EAAE,CAAC,CAAC,EACZd,EAAE,CACA,KAAK,EACL;MACE4B,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAEd,KAAK,CAACe,MAAM;QACnBC,UAAU,EAAE;MACd,CAAC,CACF;MACD/B,WAAW,EAAE;IACf,CAAC,EACD,CACEH,GAAG,CAACmC,wBAAwB,CAACjB,KAAK,CAAC,GAC/B,CACElB,GAAG,CAACc,EAAE,CAAC,SAAS,CAAC,EACjBd,GAAG,CAACgB,EAAE,CAACE,KAAK,CAACkB,oBAAoB,EAAE,UAAUd,KAAK,EAAEe,CAAC,EAAE;MACrD,OAAOpC,EAAE,CACP,MAAM,EACN;QACEmB,GAAG,EAAEiB,CAAC;QACNjC,EAAE,EAAE;UACFmB,KAAK,EAAE,SAAAA,CAAUjB,MAAM,EAAE;YACvB,OAAON,GAAG,CAACO,KAAK,CAAC,aAAa,EAAEe,KAAK,CAAC;UACxC;QACF;MACF,CAAC,EACD,CAACtB,GAAG,CAACc,EAAE,CAACd,GAAG,CAACyB,EAAE,CAACH,KAAK,CAACgB,UAAU,CAAC,CAAC,CACnC,CAAC;IACH,CAAC,CAAC,CACH,GACDtC,GAAG,CAACe,EAAE,CAAC,CAAC,EACZd,EAAE,CACA,YAAY,EACZ;MACES,KAAK,EAAE;QACL,YAAY,EAAE,GAAG;QACjB6B,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE;MACb;IACF,CAAC,EACD,CACExC,EAAE,CAAC,GAAG,EAAE;MACNE,WAAW,EAAE,wCAAwC;MACrDC,EAAE,EAAE;QACFmB,KAAK,EAAE,SAAAA,CAAUjB,MAAM,EAAE;UACvB,OAAON,GAAG,CAAC0C,YAAY,CAACvB,KAAK,CAAC;QAChC;MACF;IACF,CAAC,CAAC,CAEN,CAAC,EACDlB,EAAE,CACA,YAAY,EACZ;MACES,KAAK,EAAE;QACL,YAAY,EAAE,GAAG;QACjB6B,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,QAAQ;QACjBC,SAAS,EAAE;MACb;IACF,CAAC,EACD,CACE,CAACzC,GAAG,CAAC2C,gBAAgB,GACjB1C,EAAE,CAAC,GAAG,EAAE;MACNE,WAAW,EACT,6CAA6C;MAC/CC,EAAE,EAAE;QACFmB,KAAK,EAAE,SAAAA,CAAUjB,MAAM,EAAE;UACvB,OAAON,GAAG,CAACO,KAAK,CAAC,oBAAoB,EAAEW,KAAK,CAACe,MAAM,CAAC;QACtD;MACF;IACF,CAAC,CAAC,GACFjC,GAAG,CAACe,EAAE,CAAC,CAAC,CAEhB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI6B,eAAe,GAAG,EAAE;AACxB7C,MAAM,CAAC8C,aAAa,GAAG,IAAI;AAE3B,SAAS9C,MAAM,EAAE6C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}