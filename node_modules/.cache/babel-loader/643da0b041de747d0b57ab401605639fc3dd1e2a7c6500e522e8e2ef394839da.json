{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { mapMutations, mapState } from 'vuex';\nimport Upload from '../components/upload/index.vue';\nexport default {\n  components: {\n    Upload\n  },\n  data() {\n    return {\n      loading: false\n    };\n  },\n  computed: {\n    ...mapState('hubble', ['currentPackage'])\n  },\n  methods: {\n    ...mapMutations('hubble', ['togglePackageDialog', 'toggleSlider']),\n    uploadSuccess({\n      topicId,\n      fileName\n    }) {\n      this.loading = false;\n      this.$emit('uploadSuccess', {\n        topicId,\n        fileName\n      });\n      this.$router.push(`/hubble/chat/${topicId}`);\n    }\n  },\n  created() {\n    this.toggleSlider(false);\n  }\n};", "map": {"version": 3, "names": ["mapMutations", "mapState", "Upload", "components", "data", "loading", "computed", "methods", "uploadSuccess", "topicId", "fileName", "$emit", "$router", "push", "created", "toggleSlider"], "sources": ["src/views/agent/upload/index.vue"], "sourcesContent": ["<template>\n    <div class=\"hubble-page__upload\">\n        <Upload\n            drag\n            class=\"hubble-page__upload-wrapper\"\n            @onUploadSuccess=\"uploadSuccess\"\n            v-loading=\"loading\"\n        >\n            <div>\n                <div class=\"hubble-page__upload-operate\">\n                    <i class=\"el-icon-ssq-shangchuanbendiwenjian\"></i>\n                    <p>将文档拖拽至此上传</p>\n                    <span class=\"accept\">目前仅支持PDF文档</span><br>\n                    <el-button>选择文档</el-button>\n                </div>\n                <span class=\"hubble-page__upload-drag-text\">释放鼠标完成上传</span>\n            </div>\n        </Upload>\n    </div>\n</template>\n\n<script>\nimport { mapMutations, mapState } from 'vuex';\nimport Upload from '../components/upload/index.vue';\nexport default {\n    components: {\n        Upload,\n    },\n    data() {\n        return {\n            loading: false,\n        };\n    },\n    computed: {\n        ...mapState('hubble', ['currentPackage']),\n    },\n    methods: {\n        ...mapMutations('hubble', ['togglePackageDialog', 'toggleSlider']),\n        uploadSuccess({ topicId, fileName }) {\n            this.loading = false;\n            this.$emit('uploadSuccess', { topicId, fileName });\n            this.$router.push(`/hubble/chat/${topicId}`);\n        },\n    },\n    created() {\n        this.toggleSlider(false);\n    },\n};\n</script>\n\n<style lang=\"scss\">\n.hubble-page__upload{\n    width: 100%;\n    height: 100%;\n    padding: 40px;\n    box-sizing: border-box;\n    background: #f8f8f8;\n    &-wrapper{\n        width: 100%;\n        height: 100%;\n        .el-upload{\n            width: 100%;\n            height: 100%;\n        }\n        .el-upload-dragger{\n            width: 100%;\n            height: 100%;\n            border: 3px dashed rgba(221,221,221,1);\n            border-radius: 8px;\n            position: relative;\n            background: transparent;\n            .hubble-page__upload-drag-text{\n                color: #0C8AEE;\n                position: absolute;\n                top: 50%;\n                left: 50%;\n                transform: translate(-50%, -50%);\n                display: none;\n            }\n            &:hover, &.is-dragover{\n                border-color: #0C8AEE;\n            }\n            &.is-dragover{\n                .hubble-page__upload-operate{\n                    display: none;\n                }\n                .hubble-page__upload-drag-text{\n                    display: block;\n                }\n            }\n        }\n    }\n    &-operate{\n        color: #999;\n        width: 150px;\n        text-align: center;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        i{\n            font-size: 56px;\n            margin-bottom: 20px;\n        }\n        p{\n            font-size: 16px;\n            margin-bottom: 14px;\n        }\n        span.accept{\n            color: #ddd;\n            font-size: 12px;\n        }\n        .el-button{\n            margin-top: 30px;\n        }\n    }\n}\n</style>\n"], "mappings": ";AAsBA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,MAAA;AACA;EACAC,UAAA;IACAD;EACA;EACAE,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAL,QAAA;EACA;EACAM,OAAA;IACA,GAAAP,YAAA;IACAQ,cAAA;MAAAC,OAAA;MAAAC;IAAA;MACA,KAAAL,OAAA;MACA,KAAAM,KAAA;QAAAF,OAAA;QAAAC;MAAA;MACA,KAAAE,OAAA,CAAAC,IAAA,iBAAAJ,OAAA;IACA;EACA;EACAK,QAAA;IACA,KAAAC,YAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}