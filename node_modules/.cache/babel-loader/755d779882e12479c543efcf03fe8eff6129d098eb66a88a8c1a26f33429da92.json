{"ast": null, "code": "export default {\n  sign: {\n    sealLabelsTip: '您需要再这份合同上盖{sealLabelslen}个章。{personStr}将为您盖${otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已经在页面上展示。请确认是否继续。',\n    continue: '继续',\n    needRemark: '您还需要填写备注',\n    notNeedRemark: '您不需要填写备注',\n    contractPartiesYouChoose: '您可以选择的签约主体:',\n    contractPartyFilled: '发件人填写的签约主体为:',\n    certifyOtherCompanies: '认证其他企业',\n    youCanAlso: '您也可以：',\n    needVerification: 'Для подписи вам нужна сертификация',\n    prompt: 'Напоминать',\n    submit: 'Подтвердить',\n    cancel: 'Отменить',\n    sign: '立即签约',\n    addSeal: '请使用电脑登录上上签官网添加印章',\n    noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',\n    requestSomeone: 'Запросить кого-то еще для аутентификации',\n    requestSomeoneList: '请求以下人员完成实名认证：',\n    electronicSeal: 'электронная печать',\n    changeTheSeal: '不想使用该印章？实名认证后可更换印章',\n    goToVerify: 'Перейти к сертификации подлинного имени',\n    noSealToChoose: 'Нет переключаемых печатей, если необходимо управлять печатью, сначала проверьте подлинное имя',\n    goVerify: '去认证',\n    goToVerifyEnt: 'Перейти в сертификационную компанию',\n    digitalCertificateTip: '上上签正在调用您的数字证书',\n    signDes: '您在安全签约环境中，请放心签署！',\n    signAgain: '继续签署',\n    send: '发送',\n    person: '个人',\n    ent: '企业',\n    entName: '企业名称',\n    account: '账号',\n    accountPH: '手机或邮箱',\n    approved: 'утверждение',\n    signVerification: 'Подписать',\n    cannotReview: '无法查看合同',\n    connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n    personalMaterials: '发件人要求您补充更多认证材料',\n    noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',\n    lackEntName: '请填写企业名称',\n    errAccount: '请填写正确的邮箱或手机号',\n    noticeAdmin: '申请加入',\n    signDone: '签署完成',\n    signDoneTip: '您已签署该合同',\n    approveDone: '审批完成',\n    approveDoneTip: '您已审批该合同',\n    completeSign: `请先点击“盖章处”或“签字处”完成签署`,\n    fillFirst: '请先在输入框中填写合同内容',\n    stillSignTip: '在您签署此合同后，仍有其他签署方可能填写合同内容，是否继续签署？',\n    riskDetails: '风险详情',\n    noviewDifference: '由于其他签署方仍可能填写发起方指定的合同内容，上上签不对协议的当前版本与生效版本之间的内容差异进行审核，并默认您认可并同意签署生效版本。',\n    start: 'Начать',\n    nextStep: 'Следующий',\n    help: '帮助',\n    faceFailed: '非常抱歉，您的人脸比对失败',\n    dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',\n    faceFailedtips: '提示',\n    verifyTry: '请核实身份信息后重试',\n    faceLimit: '今天的人脸比对次数已达到上限',\n    upSignReq: '请明天重试或联系合同发起者修改签署要求',\n    reqFace: '发件人要求你进行刷脸校验',\n    signAfterFace: '刷脸通过后即可完成合同签署',\n    date: 'Дата',\n    chooseSeal: '选择印章',\n    seal: '印章',\n    signature: '签名',\n    handwrite: '手写',\n    mysign: '我的签名',\n    approvePlace: '审批留言，可不填',\n    approvePlace_1: '审批留言',\n    approvePlace_2: '可不填',\n    approveAgree: '审批结果：同意',\n    approveReject: '审批结果：驳回',\n    signBy: '由',\n    signByEnd: '盖章',\n    sealBy: '由',\n    sealByEnd: '签名',\n    coverBy: '需盖',\n    applicant: '申请人',\n    continueVeri: '继续认证',\n    registerAndReal: '请注册并实名',\n    goToResiter: '请注册并认证',\n    sureToUse: '确定使用',\n    toSign: '签约吗?',\n    pleaseComplete: '请先完成',\n    confirmSign: '再确认签署',\n    admin: '管理员',\n    contratAdmin: '请联系管理员将您的账号',\n    addToEnt: '添加为企业成员',\n    alreadyExists: '在上上签已存在',\n    sendMsg: '上上签将以短信形式给管理员发以下内容：',\n    applyJoin: '申请加入',\n    title: 'заголовок',\n    viewImg: 'вид',\n    priLetter: 'личное письмо',\n    priLetterFromSomeone: 'письмо от {name}',\n    readLetter: 'Понял.',\n    approve: '同意',\n    disapprove: 'Отклонить',\n    refuseSign: 'Отказ в подписи',\n    paperSign: '改用纸质签署',\n    refuseTip: '请选择拒绝理由',\n    refuseReason: 'Укажите причину отказа, чтобы помочь другой стороне понять вашу проблему и ускорить процесс заключения контракта',\n    reasonWriteTip: '请填写拒签理由',\n    refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',\n    refuseConfirm: 'Отказ в подписи',\n    signValidationTitle: '签署校验',\n    email: 'E-mail',\n    phoneNumber: 'номер телефона',\n    password: '密码',\n    verificationCode: 'Код подтверждения',\n    mailVerificationCode: 'Pin Code',\n    forgetPsw: 'Забыли пароль',\n    if: '，是否',\n    forgetPassword: 'Забыли пароль',\n    rejectionVer: '拒签校验',\n    msgTip: 'Все время не получаете СМС сообщение? Пожалуйста, попробуйте еще раз ',\n    voiceVerCode: 'Голосовой код подтверждения',\n    SMSVerCode: 'СМС подтверждение ',\n    or: 'или ',\n    emailVerCode: 'код подтверждения почтового ящика ',\n    SentSuccessfully: 'Успешно отправлен',\n    intervalTip: 'Интервал отправки слишком короткий ',\n    signPsw: 'Пароль для подписи',\n    signSuc: 'Успешно подписано',\n    refuseSuc: '拒签成功',\n    approveSuc: '审批成功',\n    hdFile: '查看高清文件',\n    otherOperations: 'Другие операций',\n    reviewDetails: 'Детальное утверждение',\n    close: 'Закрыть',\n    submitter: '提交人',\n    signatory: 'Подписант',\n    reviewSchedule: '审批进度',\n    signByPc: '由{name}签名',\n    signPageDescription: '{index}-я страница, всего {total} страниц',\n    sealBySomeone: '由{name}盖章',\n    signDate: '签署日期',\n    download: 'Скачать',\n    signPage: 'Количество страниц: {page} страницы',\n    viewContract: '查看合同',\n    signNow: '立即签署',\n    sender: 'Отправитель',\n    signer: 'Подписант',\n    startSignTime: '发起签约时间',\n    signDeadLine: 'Крайний срок подписания',\n    authGuide: {\n      goToHome: 'Вернуться к меню',\n      tip_1: '认证完成后，可查看并签署合同。',\n      tip_2: '请使用身份 | 进行认证。',\n      tip_3: '发来合同',\n      tip_4: '请联系合同发起者 | 更改收件人。',\n      tip_5: '您认证的 | 无法查看合同',\n      new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',\n      new_tip_2: '基于发件方的合规要求，您需要以：',\n      new_tip_3: '完成以下步骤。',\n      new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',\n      entUserName: '姓名：',\n      idNumberForVerify: '身份证号：',\n      realNameAuth: '实名认证',\n      applySeal: '申请印章',\n      signContract: '签署合同'\n    },\n    switch: 'переключение',\n    selectSignature: '选择签名',\n    selectSigner: '选择签名人',\n    pleaseScanToSign: '请用支付宝或微信扫一扫签署',\n    pleaseScanAliPay: '请使用支付宝app扫描二维码签署',\n    pleaseScanWechat: '请使用微信app扫描二维码签署',\n    requiredFaceSign: '合同发件人要求您刷脸签署',\n    requiredDualSign: '合同发件人要求你使用双录校验',\n    qrcodeInvalid: '二维码信息已过期，请刷新',\n    faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',\n    verCodeVerify: '验证码校验',\n    applyToSign: '申请签署合同',\n    autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',\n    cannotSignBeforeApproval: '审批未完成，暂不能签署！',\n    finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',\n    uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',\n    cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',\n    unlimitedNotice: '该合同计费不限量使用',\n    units: '{num}份',\n    contractToPrivate: '对私合同',\n    contractToPublic: '对公合同',\n    paySum: '共{sum}需要您支付',\n    payTotal: '共计{total}元',\n    fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值',\n    contactToRecharge: '请联系主管理员充值',\n    deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',\n    needSignerPay: '合同发送方设置了签署方付费，并指定由您来支付合同费用。',\n    recharge: '充值',\n    toSubmit: '提交',\n    appliedSeal: '用印申请已提交',\n    noSeal: '无印章',\n    noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',\n    knew: '知道了',\n    noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',\n    hadAutoSet: '已在另外{num}处自动',\n    setThatSignature: '放置该签名',\n    setThatSeal: '放置该印章',\n    applyThatSeal: '申请该印章',\n    savedOnLeftSite: '已保存到左侧签名栏',\n    ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',\n    ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',\n    ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',\n    noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',\n    noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',\n    gotoAppendSeal: '去添加印章',\n    approvalFlowSuccessfulSet: '审批流设置成功',\n    mandate: '同意授权',\n    loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',\n    signIdentityAs: '当前正在以{person}的名义签署合同',\n    enterNextContract: '进入下一份合同',\n    fileList: '文件列表',\n    addSignerFile: '添加附属资料',\n    signatureFinish: '已全部盖章/签名',\n    dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',\n    noticeToManager: '给管理员发通知',\n    gotoAuthPerson: '去认证个人',\n    senderRequire: '发件方要求您',\n    senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',\n    suggestToAuth: '您还未实名认证，建议您实名认证后签署',\n    contactEntAdmin: '请联系企业主管理员',\n    setYourAccount: '将您的账号',\n    authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',\n    noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',\n    pleaseUse: '请使用',\n    me: '我',\n    myself: '本人，',\n    reAuthBtnTip: '我是当前手机号的实际使用者，',\n    reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '当前账号是否是您的常用手机号？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署手机号，并指定由您签署。',\n    confirmOk: '确认',\n    goOnAuth: {\n      0: '进行认证，',\n      1: '请进行实名认证，',\n      2: '进行实名认证，'\n    },\n    signContractAfterAuth: {\n      0: '认证完成后，可签署合同。',\n      1: '完成认证后即可签署合同。'\n    },\n    useIdentity: '以{name}的身份',\n    inTheName: '以',\n    of: '的',\n    identity: '身份',\n    nameIs: '姓名为',\n    IDNumIs: '身份证号为',\n    provideMoreAuthData: '补充更多认证材料',\n    leadToAuthBeforeSign: '继续认证后即可签署合同',\n    groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',\n    contactSender: '如有疑问请联系发件方。',\n    note: '注:',\n    identityInfo: '身份信息',\n    signNeedCoincidenceInfo: '完全一致才能签署合同。',\n    needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',\n    iHadReadContract: '已阅读，本人已知晓{alias}内容',\n    getVerCodeFirst: '请先获取验证码',\n    appScanVerify: '上上签APP扫码校验',\n    downloadBSApp: '下载上上签APP',\n    scanned: '扫码成功',\n    confirmInBSApp: '请在上上签APP中确认签署',\n    qrCodeExpired: '二维码已失效，请刷新重试',\n    appKey: 'APP安全校验',\n    goToScan: '去扫码',\n    setNotificationInUserCenter: '请先到用户中心设置通知方式',\n    doNotWantUseVerCode: '不想用验证码',\n    try: '试试',\n    retry: '重试',\n    faceExceedTimes: '当日刷脸次数已达上线，请明日再试',\n    goToFaceVerify: '去刷脸',\n    returnBack: '返回',\n    switchTo: '切换至',\n    youCanChooseIdentityBlow: '您可以选择以下签约主体',\n    needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',\n    lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',\n    manageSeal: '管理印章',\n    needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',\n    chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',\n    appendDrawSignature: '添加手绘签名',\n    senderUnFill: '（发件人未填写）',\n    declare: '说明',\n    fileLessThan: '请上传小于{num}M的文件',\n    fileNeedUploadImg: '上传时请使用支持的附件格式',\n    serverError: '服务器开了点小差，请稍后再试',\n    oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n    fileLimitFormatAndSize: '单个资料图片数量不超过10张。',\n    fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',\n    fileFormatFile: '支持pdf、txt、zip、xml格式，单份文件大小不超过10M',\n    signNeedKnow: '签约须知',\n    signNeedKnowFrom: '来自{sender}的签约须知',\n    approvalInfo: '审批须知',\n    approveNeedKnowFrom: '来自{sender}提交的审批资料',\n    setLabel: '设置标签',\n    addRidingSeal: '添加骑缝章',\n    delRidingSeal: '删除骑缝章',\n    file: '文件',\n    attachmentContent: '附件内容',\n    downloadFile: '下载源文件',\n    noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',\n    archiveTo: '归档到',\n    hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',\n    pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',\n    save: '保存',\n    remind: '提醒',\n    riskTip: '风险提醒',\n    chooseApplyPerson: '选择申请人',\n    chooseAdminSign: '选择印章管理员',\n    useSealByOther: '他人盖章',\n    getSeal: '获取印章',\n    nowApplySealList: '您正在请求以下印章',\n    nowAdminSealList: '你正在申请获得以下印章',\n    chooseApplyPersonToDeal: '请选择申请人，您的申请以及合同将会转交给所选人来处理',\n    chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',\n    contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',\n    sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',\n    successfulSent: '发送成功',\n    needSomeoneToSignature: '由{x}盖{y}',\n    needToSet: '需盖',\n    approver: '申请人：',\n    clickToSignature: '点击此处签名',\n    transferToOtherToSign: '转给其他人签',\n    signatureBy: '由{x}签名',\n    tipRightNumber: '请输入正确的数字',\n    tipRequired: '必填值不可为空',\n    confirm: '确定',\n    viewContractDetail: '查看合同详情',\n    crossPlatformCofirm: {\n      message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',\n      title: '数据授权',\n      confirmButtonText: '同意授权',\n      cancelButtonText: '取消'\n    },\n    sealScope: '印章使用范围',\n    currentContract: '当前合同',\n    allContract: '所有合同'\n  },\n  signJa: {\n    beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',\n    beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n    beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n    beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n    beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n    beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n    beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n    entNamePlaceholder: '请输入企业名称',\n    corporateNumberPlaceholder: '请输入法人番号',\n    corporateNumber: '法人番号',\n    singerNamePlaceholder: '请输入签署人姓名',\n    singerName: '签署人姓名',\n    businessPic: '印鉴证明书',\n    waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',\n    itsMe: '是我本人',\n    wrongInformation: '信息有误',\n    confirmChange: '确认更换',\n    communicateSender1: '不更换, 与甲方沟通',\n    communicateSender2: '取消, 去与发件方沟通'\n  },\n  signPC: {\n    commonSign: 'Подтвердить подпись',\n    contractVerification: 'Проверка подписи',\n    VerCodeVerify: 'Проверить код подтверждение',\n    QrCodeVerify: 'Проверка QR-кода',\n    verifyTip: 'BestSign в данный момент проверяет ваш безопасный цифровой сертификат, вы находитесь в защищенной среде электронной подписи, пожалуйста, будьте спокойны в подписывании!',\n    verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',\n    selectSeal: 'Электронная печать',\n    toAddSealWithConsole: ' Вы также можете добавить печать на платформе управление компаниями',\n    use: 'Использовать',\n    toAddSeal: 'Добавить печать',\n    mySeal: 'Моя печать',\n    operationCompleted: 'Операция завершена'\n  },\n  signTip: {\n    contractDetail: 'Информация о контракте',\n    downloadBtn: '下载APP',\n    tips: 'Напоминать',\n    submit: 'Подтвердить',\n    SigningCompleted: 'Успешно подписано',\n    submitCompleted: '等待他人处理',\n    noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n    noRightSign: '合同正在签署中，当前用户不允许签署操作',\n    noNeedSign: '内部决议合同，已无需签署',\n    ApprovalCompleted: '审批成功',\n    contractRevoked: '该合同已被撤销',\n    contractRefused: '该合同已被拒签',\n    linkExpired: '该链接已失效',\n    contractClosed: '该合同已截止签约',\n    approvalReject: '该合同审批已被驳回',\n    approving: '合同正在审批中',\n    viewContract: 'Просмотреть контракт',\n    downloadContract: '下载合同',\n    signed: 'контракта',\n    approved: 'утверждение',\n    approval: 'утверждение',\n    personHas: ' человек ',\n    personHave: ' человек ',\n    personHasnot: '人未',\n    personsHavenot: '人未',\n    cannotReview: '无法查看合同',\n    cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',\n    privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n    beenDeleted: '您的账号已被企业管理员删除',\n    unActive: '无法继续激活账',\n    back: '返回',\n    contratStatusDes: 'Статус контракта:',\n    contractConditionDes: 'Статус контракта:',\n    contractIng: 'Подписание {key}',\n    contractComplete: 'Подписание {key} завершено',\n    operate: '合同操作',\n    freeContract: '完成首次合同发送，可免费再获取合同份数',\n    sendContract: '去发合同',\n    congratulations: '恭喜{name}企业已完成{num}份合同签署，',\n    carbonSaving: '预估节碳{num}g',\n    signGift: '上上签赠送您{num}份对私合同（使用期限至{limit}）',\n    followPublic: '关注微信公众号，随时接收合同消息',\n    congratulationsSingle: '恭喜{name}完成合同签署，',\n    carbonSavingSingle: '预估新增节碳量2002.4g',\n    viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮'\n  },\n  view: {\n    title: '查看合同',\n    ok: '完成',\n    cannotReview: '无法查看合同',\n    privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器'\n  },\n  prepare: {\n    signHeaderTitle: 'Добавить файл и подписанта',\n    step1: 'Шаг 1',\n    uploadFile: 'Загрузить файлы',\n    step2: 'Шаг 2',\n    addSigner: 'Добавить подписывающую сторону',\n    step3: 'Шаг 3',\n    actionDemo: 'Процесс демонстраций',\n    next: 'Следующий',\n    isUploadingErr: 'Файл еще не был загружен. Пожалуйста, продолжите после завершения.',\n    noUploadFileErr: 'Файл не загружен, пожалуйста, продолжите после загрузки',\n    noContractTitleErr: 'Название договора не заполнено, после заполнения, можете продолжить',\n    contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',\n    expiredDateErr: 'Срок подписания неверный, прошу после поправки продолжить',\n    noExpiredDateErr: 'Пожалуйста, заполните срок подписания и продолжайте',\n    noRecipientsErr: 'Добавьте хотя бы одного подписанта',\n    noAccountErr: 'Аккаунт не может быть пустым',\n    noUserNameErr: '姓名不能为空',\n    noIDNumberErr: '身份证号码不能为空',\n    accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',\n    userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',\n    enterpriseNameErr: 'Неверный формат, введите правильное название компании',\n    idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',\n    signerErr: 'Подписавшая сторона имеет ошибку',\n    noSignerErr: 'Прошу по меньшей мере добавить одного подписчика',\n    lackAttachmentNameErr: 'Пожалуйста, заполните название вложения',\n    repeatRecipientsErr: 'Во время подписания нельзя добавлять повторно подписывающие стороны, если не в последовательном порядке',\n    innerContact: 'Контакты внутреннего отдела',\n    outerContact: 'Контакты внешнего отдела',\n    search: 'Поиск',\n    accountSelected: 'Уже выбран аккаунт',\n    groupNameAll: 'Полностью',\n    unclassified: 'Не отсортировано',\n    fileLessThan: 'Загрузка файла меньше {num}м',\n    beExcel: 'Загрузка файла Excel',\n    usePdf: 'при загрузке используйте файл PDF или рисунок',\n    fileNameMoreThan: 'имя файла с длиной более ${num}, будет автоматически удалено',\n    needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',\n    addSender: '添加为签约方',\n    tip: '提示',\n    cancel: '取消'\n  },\n  addReceiver: {\n    limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',\n    orderSignLabel: 'Последовательное подписание ',\n    contactAddress: 'Контактная адресная книга',\n    signOrder: 'Порядок подписания ',\n    account: 'Аккаунт ',\n    accountPlaceholder: 'Телефон/электронная почта (требуется)',\n    accountReceptionCollection: '前台代收',\n    accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',\n    accountReceptionCollectionTip2: '请选择前台代收',\n    signSubjectPerson: 'Тема подписи: Физическое лицо',\n    nameTips: 'Ф. И. О.  (выборочное заполнение)',\n    requiredNameTips: '姓名（必填，用于签约身份核对）',\n    entOperatorNameTips: 'Ф. И. О.  (выборочное заполнение)',\n    needAuth: 'Нужно аутентифицироваться',\n    signSubjectEnt: 'Тема подписи: Юридическое лицо ',\n    entNameTips: 'Наименование компании (Выборочное заполнение)',\n    operator: 'Исполнитель',\n    sign: 'Подписать ',\n    more: 'Больше',\n    faceFirst: 'Приоритетная чистка, резервное копирование кода подтверждения',\n    faceFirstTips: 'При подписании система по умолчанию выполняет проверку лица. Когда число раз, когда кисть не проходит, достигает верхнего предела дня, она автоматически переключается на проверку кода проверки',\n    mustFace: 'Обязательно подпишите с распознаванием лица ',\n    handWriteNotAllowed: 'Рукописные подписи не допускаются',\n    mustHandWrite: 'Обязательно подпишите рукой ',\n    fillIDNumber: 'паспортные данные ',\n    fillNoticeCall: 'номер телефона для уведомления ',\n    fillNoticeCallTips: 'Введите номер телефона для уведомления ',\n    addNotice: 'Добавить личное сообщение',\n    attachTips: 'Требования к приложению',\n    faceSign: 'Обязательно подпишите с распознаванием лица ',\n    faceSignTips: 'Данный пользователь должен пройти аутентификацию лица, чтобы завершить подписание ',\n    handWriteNotAllowedTips: 'Пользователь может выбрать только подпись, которая была установлена, или использовать подпись шрифта по умолчанию для завершения подписи',\n    handWriteTips: 'Пользователю нужна рукописная подпись для завершения подписи',\n    idNumberTips: 'Используется для подписи проверки личности',\n    verifyBefore: 'Подтвердите личность перед просмотром файлов',\n    verify: 'Подтвердить личность',\n    verifyTips: 'максимум 20 букв',\n    verifyTips2: 'Вы должны предоставить эту информацию для проверки данному пользователю',\n    sendToThirdPlatform: 'Отправить платформе третьей стороне',\n    platFormName: 'Название платформы',\n    fillThirdPlatFormName: 'Введите название третьей платформы',\n    attach: 'Прикрепленный файл ',\n    attachName: 'Название приложении',\n    exampleID: 'например: фото паспорта',\n    attachInfo: 'Инструкция приложения',\n    attachInfoTips: 'например:  загрузите фото паспорта',\n    addAttachRequire: 'Добавить требования к вложению',\n    addSignEnt: 'Добавить подпись компании ',\n    addSignPerson: 'Добавить подпись физического лица ',\n    selectContact: 'Выбрать контактное лицо',\n    save: 'Сохранить',\n    searchVerify: 'Проверка запроса',\n    fillImageContentTips: 'Пожалуйста, заполните содержание изображения',\n    ok: 'Подтвердить ',\n    findContact: 'Найти следующих подписывающих сторон с договора',\n    signer: 'Подписант ',\n    signerTips: 'Маленькое примечание: после выбора подписанта, платформа поможет вам определить место подписи и  печати',\n    add: 'Добавить ',\n    notAdd: 'Не добавляйте',\n    cc: 'Отправить копию ',\n    notNeedAuth: 'Не требует аутентификаций',\n    extracting: 'Извлечение…',\n    autoFill: 'Автоматически заполнить данные подписанта',\n    failExtracting: 'не получено подписавшей стороной',\n    idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',\n    noAccountErr: 'Аккаунт не может быть пустым',\n    noUserNameErr: '姓名不能为空',\n    noIDNumberErr: '身份证号码不能为空',\n    accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',\n    enterpriseNameErr: 'Неверный формат, введите правильное название компании',\n    userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',\n    riskCues: 'Предупреждение о риске',\n    riskCuesMsg: 'Если подписавшая сторона подписывается не с настоящим именем, вам необходимо будет предоставить паспорт подписавшей стороны в случае возникновения спора. Чтобы избежать риска, выберите нужное настоящее имя.',\n    confirmBtnText: 'Выбрать нужные данные',\n    cancelBtnText: 'Выбрать не нужные данные',\n    attachLengthErr: 'Вы можете добавить до 50 запросов на вложение только одному подписанту',\n    collapse: 'Сложить',\n    expand: 'Разверните',\n    delete: 'Удалить',\n    saySomething: 'Комментарий (скажите что-нибудь)',\n    addImage: 'Добавить фото',\n    addImageTips: 'поддерживает pdf、word、jpg、png, формат, максимум можно загрузить 10 шт.',\n    give: 'дайте',\n    fileMax: 'количество отдачи превысило верхний предел'\n  },\n  field: {\n    send: 'Отправить ',\n    contractDispatchApply: 'подаять заявку на контракт',\n    contractNeedYouSign: 'Данный документ нужно подписать вам ',\n    ifSignRightNow: 'Подписать ли его сейчас',\n    signRightNow: 'Подпишите сейчас',\n    signLater: 'Знак позже',\n    signaturePositionErr: 'Пожалуйста, укажите место подписи для каждого подписанта',\n    sendSucceed: 'Успешно отправлен',\n    confirm: 'Подтвердить ',\n    cancel: 'Отменить',\n    qrCodeTips: 'После подписания кода вы можете просмотреть детали подписи, проверить действительность подписи и проверить, был ли подделан договор.',\n    pagesField: '-я страница, всего {totalPages} страниц ',\n    suitableWidth: 'Соответствующая сторона ',\n    signCheck: 'Проверка подписи',\n    locateSignaturePosition: '定位签署位置',\n    locateTips: 'Может помочь быстро найти местоположение подписи. В настоящее время поддерживается только первое местоположение подписи для каждого подписанта',\n    step1: 'Шаг 1',\n    selectSigner: 'Выбрать подписанта',\n    step2: 'Шаг 2',\n    dragSignaturePosition: 'Перетащить место подписи',\n    signingField: 'Место подписи',\n    docTitle: 'Документ ',\n    totalPages: 'Количество страниц: {totalPages} страницы',\n    receiver: 'Приемник',\n    delete: 'Удалить',\n    deductPublicNotice: 'Если количество копий частного договора недостаточно, договор будет вычтен',\n    unlimitedNotice: '该合同计费不限量使用',\n    charge: 'Расчет',\n    units: '{num} ',\n    clickDecoration: '点击合同装饰',\n    contractToPrivate: 'корпоративный контракт',\n    contractToPublic: 'частный контракт',\n    costTips: {\n      1: 'Для публичного договора: подписант(не включая отправителя) договор с корпоративным аккаунтом',\n      2: 'Для частного договора: подписант(не включая отправителя) договор без корпоративного аккаунта',\n      3: 'количество тарификации рассчитывается исходя из количество копий файла.',\n      4: 'количество тарификации=количество файлов×партия введенных количество пользователей'\n    },\n    costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',\n    toCharge: 'Пополнить баланс ',\n    contractNeedCharge: {\n      1: 'Количество доступных контрактов недостаточна, и не может быть отправлено',\n      2: 'Количество доступных контрактов недостаточна, пожалуйста, свяжитесь с главным администратором, чтобы пополнить»'\n    },\n    chooseApprover: '选择审批人：',\n    nextStep: '下一步',\n    submitApproval: '提交审批',\n    autoSendAfterApproval: '*审批通过后，自动发送合同',\n    chooseApprovalFlow: '请选择一个审批流',\n    completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',\n    viewPrivateLetter: '查看私信',\n    addPrivateLetter: '添加私信',\n    append: '添加',\n    privateLetter: '私信',\n    signNeedKnow: '签约须知',\n    maximum5M: '请上传小于5M的文档',\n    uploadServerFailure: '上传到服务器失败',\n    uploadFailure: '上传失败',\n    pager: '页码',\n    seal: 'Поставить печать ',\n    signature: 'Подписать',\n    signDate: 'Дата подписания',\n    text: '文本',\n    date: '日期',\n    qrCode: '二维码',\n    number: '数字',\n    dynamicTable: '动态表格',\n    terms: '合同条款',\n    checkBox: '复选框',\n    radioBox: '单选框',\n    image: '图片'\n  },\n  paperSign: {\n    title: '使用纸质方式签署',\n    stepText: ['下一步', '确认纸质签', '确定'],\n    needUploadFile: '请先上传扫描件',\n    uploadError: '上传失败',\n    cancel: '取消',\n    downloadPaperFile: '获取纸质签文件',\n    step0: {\n      title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n      address: '邮寄地址：',\n      contactName: '接收人姓名：',\n      contactPhone: '接收人联系方式：',\n      defaultValue: '请通过线下方式向发件方索取'\n    },\n    step1: {\n      title0: '第一步：下载&打印纸质合同',\n      title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n      title1: '第二步：加盖印章',\n      title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n      title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n      title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。']\n    },\n    step2: {\n      title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n      uploadFile: '上传扫描件',\n      getCodeVerify: '获取合同签署校验',\n      isUploading: '上传中...'\n    }\n  },\n  allowPaperSignDialog: {\n    title: '允许纸质签',\n    content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',\n    tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',\n    icon: '转纸质签署 >>',\n    goSign: '去电子签',\n    cancel: '取消'\n  },\n  sealInconformityDialog: {\n    errorSeal: {\n      title: '印章提示',\n      tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n      tip1: '检测到有企业印章与企业名称：',\n      tip2: '是否要继续使用当前印章图片？',\n      tip3: '根据发件方要求，您需要使用企业名称为：',\n      tip4: '的印章',\n      tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n      tip6: '不匹配，请确保印章符合发件方要求。',\n      guide: '如何上传正确的印章 >>',\n      next: '继续使用',\n      tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',\n      tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？'\n    },\n    exampleSeal: {\n      title: '上传印章图案方式',\n      way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n      way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n      errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照']\n    },\n    confirm: '确认',\n    cancel: '取消'\n  },\n  addSealDialog: {\n    title: '添加印章图片',\n    dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNP等），由系统将此印章图片合并进入当前合同中。',\n    dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',\n    updateNewSeal: '上传新章'\n  }\n};", "map": {"version": 3, "names": ["sign", "sealLabelsTip", "continue", "needRemark", "notNeedRemark", "contractPartiesYouChoose", "contractPartyFilled", "certifyOtherCompanies", "youCanAlso", "needVerification", "prompt", "submit", "cancel", "addSeal", "noSealAvailable", "requestSomeone", "requestSomeoneList", "electronicSeal", "changeTheSeal", "goToVerify", "noSealToC<PERSON>ose", "goVerify", "goToVerifyEnt", "digitalCertificateTip", "signDes", "signAgain", "send", "person", "ent", "entName", "account", "accountPH", "approved", "signVerification", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFail", "personalMaterials", "noSupportface", "lackEntName", "errAccount", "noticeAdmin", "signDone", "signDoneTip", "approveDone", "approveDoneTip", "completeSign", "<PERSON><PERSON><PERSON><PERSON>", "stillSignTip", "riskDetails", "noviewDifference", "start", "nextStep", "help", "faceFailed", "dualFailed", "faceFailedtips", "verifyTry", "faceLimit", "upSignReq", "reqFace", "signAfterFace", "date", "chooseSeal", "seal", "signature", "handwrite", "mysign", "approve<PERSON>lace", "approvePlace_1", "approvePlace_2", "approveAgree", "approveReject", "signBy", "signByEnd", "sealBy", "sealByEnd", "coverBy", "applicant", "continueVeri", "registerAndReal", "goToResiter", "sureToUse", "toSign", "pleaseComplete", "confirmSign", "admin", "contratAdmin", "addToEnt", "alreadyExists", "sendMsg", "<PERSON><PERSON><PERSON><PERSON>", "title", "viewImg", "priLetter", "priLetterFromSomeone", "readLetter", "approve", "disapprove", "refuseSign", "paperSign", "refuseTip", "refuseReason", "reasonWriteTip", "refuseReasonOther", "refuseConfirm", "signValidationTitle", "email", "phoneNumber", "password", "verificationCode", "mailVerificationCode", "forgetPsw", "if", "forgetPassword", "rejectionVer", "msgTip", "voiceVerCode", "SMSVerCode", "or", "emailVerCode", "SentSuccessfully", "intervalTip", "signPsw", "signSuc", "refuseSuc", "approveSuc", "hdFile", "otherOperations", "reviewDetails", "close", "submitter", "signatory", "reviewSchedule", "signByPc", "signPageDescription", "sealBySomeone", "signDate", "download", "signPage", "viewContract", "signNow", "sender", "signer", "startSignTime", "signDeadLine", "authGuide", "goToHome", "tip_1", "tip_2", "tip_3", "tip_4", "tip_5", "new_tip_1", "new_tip_2", "new_tip_3", "new_tip_4", "entUserName", "idNumberForVerify", "realNameAuth", "applySeal", "signContract", "switch", "selectSignature", "selectS<PERSON>er", "pleaseScanToSign", "pleaseScanAliPay", "pleaseScanWechat", "requiredFaceSign", "requiredDualSign", "qrcodeInvalid", "faceFirstExceed", "verCodeVerify", "applyToSign", "autoRemindAfterApproval", "cannotSignBeforeApproval", "finishSignatureBeforeSign", "uploadFileOnRightSite", "cannotApplySealNeedPay", "unlimitedNotice", "units", "contractToPrivate", "contractToPublic", "paySum", "payTotal", "fundsLack", "contactToRecharge", "deductPublicNotice", "needSignerPay", "recharge", "toSubmit", "appliedSeal", "noSeal", "noSwitchSealNeedDistribute", "knew", "noSwitchSealNeedAppend", "hadAutoSet", "setThatSignature", "setThatSeal", "applyThatSeal", "savedOnLeftSite", "ridingSealMinLimit", "ridingSealMaxLimit", "ridingSealMinOrMaxLimit", "noSealForRiding", "noSwitchSealNeedAppendBySelf", "gotoAppendSeal", "approvalFlowSuccessfulSet", "mandate", "loginToAppendSeal", "signIdentityAs", "enterNextContract", "fileList", "addSignerFile", "signatureFinish", "dragSignatureTip", "noticeToManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senderRequire", "senderRequireUseFollowIdentity", "suggestToAuth", "contactEntAdmin", "set<PERSON>ourAccount", "authInfoUnMatchNeedResend", "noEntNameNeedResend", "pleaseUse", "me", "myself", "reAuthBtnTip", "reAuthBtnContent", "descNoSame1", "descNoSame2", "authInfoNoSame", "goHome", "authInfo", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmOk", "goOnAuth", "signContractAfterAuth", "useIdentity", "inTheName", "of", "identity", "nameIs", "IDNumIs", "provideMoreAuthData", "leadToAuthBeforeSign", "groupProxyAuthNeedMore", "contactSender", "note", "identityInfo", "signNeedCoincidenceInfo", "needAuthPermissionContactAdmin", "iHadReadContract", "getVerCodeFirst", "appScanVerify", "downloadBSApp", "scanned", "confirmInBSApp", "qrCodeExpired", "appKey", "goToScan", "setNotificationInUserCenter", "doNotWantUseVerCode", "try", "retry", "faceExceedTimes", "goToFaceVerify", "returnBack", "switchTo", "youCanChooseIdentityBlow", "needDrawSignatureFirst", "lacksSealNeedAppend", "manageSeal", "needDistributeSealToSelf", "chooseSealAfterAuth", "appendDrawSignature", "senderUnFill", "declare", "fileLessThan", "fileNeedUploadImg", "serverError", "oldFormatTip", "fileLimitFormatAndSize", "fileFormatImage", "fileFormatFile", "signNeedKnow", "signNeedKnowFrom", "approvalInfo", "approveNeedKnowFrom", "<PERSON><PERSON><PERSON><PERSON>", "addRidingSeal", "delRidingSeal", "file", "attachmentContent", "downloadFile", "noLabelPleaseAppend", "archiveTo", "hadArchivedToFolder", "pleaseScanToHandleWrite", "save", "remind", "riskTip", "chooseApp<PERSON><PERSON><PERSON>", "chooseAdminSign", "useSealByOther", "getSeal", "nowApplySealList", "nowAdminSealList", "chooseApplyPersonToDeal", "chooseApplyPersonToMandate", "contactGroupAdminToDistributeSeal", "sealApplySentPleaseWait", "successfulSent", "needSomeoneToSignature", "needToSet", "approver", "clickToSignature", "transferToOtherToSign", "signatureBy", "tipRightNumber", "tipRequired", "confirm", "viewContractDetail", "crossPlatformCofirm", "message", "confirmButtonText", "cancelButtonText", "sealScope", "currentContract", "allContract", "signJa", "beforeSignTip1", "beforeSignTip2", "beforeSignTip3", "beforeSignTip4", "beforeSignTip5", "beforeSignTip6", "beforeSignTip7", "entNamePlaceholder", "corporateNumberPlaceholder", "corporateNumber", "singerNamePlaceholder", "<PERSON><PERSON><PERSON>", "businessPic", "waitApprove", "itsMe", "wrongInformation", "confirmChange", "communicateSender1", "communicateSender2", "signPC", "commonSign", "contractVerification", "VerCodeVerify", "QrCodeVerify", "verifyTip", "verifyAllTip", "selectSeal", "toAddSealWithConsole", "use", "toAddSeal", "mySeal", "operationCompleted", "signTip", "contractDetail", "downloadBtn", "tips", "SigningCompleted", "submitCompleted", "noTurnSign", "noRightSign", "noNeedSign", "ApprovalCompleted", "contractRevoked", "contractRefused", "linkExpired", "contractClosed", "approvalReject", "approving", "downloadContract", "signed", "approval", "personHas", "personHave", "person<PERSON>asnot", "<PERSON><PERSON><PERSON><PERSON>", "cannotDownload", "privateStorage", "beenDeleted", "unActive", "back", "contratStatusDes", "contractConditionDes", "contractIng", "contractComplete", "operate", "freeContract", "sendContract", "congratulations", "carbonSaving", "signGift", "followPublic", "congratulations<PERSON><PERSON><PERSON>", "carbonSavingSingle", "viewContractTip", "view", "ok", "prepare", "signHeaderTitle", "step1", "uploadFile", "step2", "addSigner", "step3", "actionDemo", "next", "isUploadingErr", "noUploadFileErr", "noContractTitleErr", "contractTypeErr", "expiredDateErr", "noExpiredDateErr", "noRecipientsErr", "noAccountErr", "noUserNameErr", "noIDNumberErr", "accountFormatErr", "userNameFormatErr", "enterpriseNameErr", "idNumberForVerifyErr", "signer<PERSON>rr", "noSignerErr", "lackAttachmentNameErr", "repeatRecipientsErr", "innerContact", "outerContact", "search", "accountSelected", "groupNameAll", "unclassified", "beExcel", "usePdf", "fileNameMoreThan", "need<PERSON>dd<PERSON><PERSON>", "addSender", "tip", "addReceiver", "limitFaceConfigTip", "orderSignLabel", "contactAddress", "signOrder", "accountPlaceholder", "accountReceptionCollection", "accountReceptionCollectionTip1", "accountReceptionCollectionTip2", "signSub<PERSON><PERSON>erson", "nameTips", "requiredNameTips", "entOperatorNameTips", "needAuth", "signSubjectEnt", "entNameTips", "operator", "more", "faceFirst", "faceFirstTips", "mustFace", "handWriteNotAllowed", "mustHandWrite", "fillIDNumber", "fillNoticeCall", "fillNoticeCallTips", "addNotice", "attachTips", "faceSign", "faceSignTips", "handWriteNotAllowedTips", "handWriteTips", "idNumberTips", "verifyBefore", "verify", "verifyTips", "verifyTips2", "sendToThirdPlatform", "platFormName", "fillThirdPlatFormName", "attach", "attachName", "exampleID", "attachInfo", "attachInfoTips", "addAttachRequire", "addSignEnt", "addSign<PERSON>erson", "selectContact", "searchVerify", "fillImageContentTips", "findContact", "signer<PERSON><PERSON>s", "add", "notAdd", "cc", "notNeedAuth", "extracting", "autoFill", "failExtracting", "riskCues", "riskCuesMsg", "confirmBtnText", "cancelBtnText", "attachLengthErr", "collapse", "expand", "delete", "saySomething", "addImage", "addImageTips", "give", "fileMax", "field", "contractDispatchApply", "contractNeedYouSign", "ifSignRightNow", "signRightNow", "signLater", "signaturePositionErr", "sendSucceed", "qrCodeTips", "pagesField", "suitableWidth", "signCheck", "locateSignaturePosition", "locateTips", "dragSignaturePosition", "<PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "totalPages", "receiver", "charge", "clickDecoration", "costTips", "costInfo", "to<PERSON>harge", "contractNeedCharge", "chooseApprover", "submitApproval", "autoSendAfterApproval", "chooseApprovalFlow", "completeApprovalFlow", "viewPrivateLetter", "addPrivateLetter", "append", "privateLetter", "maximum5M", "uploadServerFailure", "uploadFailure", "pager", "text", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "stepText", "needUploadFile", "uploadError", "downloadPaperFile", "step0", "address", "contactName", "contactPhone", "defaultValue", "title0", "title0Desc", "title1", "title1Desc", "title2", "title2Desc", "getCodeVerify", "isUploading", "allowPaperSignDialog", "content", "icon", "goSign", "sealInconformityDialog", "errorSeal", "tip1", "tip2", "tip3", "tip4", "tip5", "tip6", "guide", "tip7", "tip8", "exampleSeal", "way1", "way2", "errorWay", "addSealDialog", "dec1", "dec2", "updateNewSeal"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/sign/sign-ru.js"], "sourcesContent": ["export default {\n    sign: {\n        sealLabelsTip: '您需要再这份合同上盖{sealLabelslen}个章。{personStr}将为您盖${otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已经在页面上展示。请确认是否继续。',\n        continue: '继续',\n        needRemark: '您还需要填写备注',\n        notNeedRemark: '您不需要填写备注',\n        contractPartiesYouChoose: '您可以选择的签约主体:',\n        contractPartyFilled: '发件人填写的签约主体为:',\n        certifyOtherCompanies: '认证其他企业',\n        youCanAlso: '您也可以：',\n        needVerification: 'Для подписи вам нужна сертификация',\n        prompt: 'Напоминать',\n        submit: 'Подтвердить',\n        cancel: 'Отменить',\n        sign: '立即签约',\n        addSeal: '请使用电脑登录上上签官网添加印章',\n        noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',\n        requestSomeone: 'Запросить кого-то еще для аутентификации',\n        requestSomeoneList: '请求以下人员完成实名认证：',\n        electronicSeal: 'электронная печать',\n        changeTheSeal: '不想使用该印章？实名认证后可更换印章',\n        goToVerify: 'Перейти к сертификации подлинного имени',\n        noSealToChoose: 'Нет переключаемых печатей, если необходимо управлять печатью, сначала проверьте подлинное имя',\n        goVerify: '去认证',\n        goToVerifyEnt: 'Перейти в сертификационную компанию',\n        digitalCertificateTip: '上上签正在调用您的数字证书',\n        signDes: '您在安全签约环境中，请放心签署！',\n        signAgain: '继续签署',\n        send: '发送',\n        person: '个人',\n        ent: '企业',\n        entName: '企业名称',\n        account: '账号',\n        accountPH: '手机或邮箱',\n        approved: 'утверждение',\n        signVerification: 'Подписать',\n        cannotReview: '无法查看合同',\n        connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n        personalMaterials: '发件人要求您补充更多认证材料',\n        noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',\n        lackEntName: '请填写企业名称',\n        errAccount: '请填写正确的邮箱或手机号',\n        noticeAdmin: '申请加入',\n        signDone: '签署完成',\n        signDoneTip: '您已签署该合同',\n        approveDone: '审批完成',\n        approveDoneTip: '您已审批该合同',\n        completeSign: `请先点击“盖章处”或“签字处”完成签署`,\n        fillFirst: '请先在输入框中填写合同内容',\n        stillSignTip: '在您签署此合同后，仍有其他签署方可能填写合同内容，是否继续签署？',\n        riskDetails: '风险详情',\n        noviewDifference: '由于其他签署方仍可能填写发起方指定的合同内容，上上签不对协议的当前版本与生效版本之间的内容差异进行审核，并默认您认可并同意签署生效版本。',\n        start: 'Начать',\n        nextStep: 'Следующий',\n        help: '帮助',\n        faceFailed: '非常抱歉，您的人脸比对失败',\n        dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',\n        faceFailedtips: '提示',\n        verifyTry: '请核实身份信息后重试',\n        faceLimit: '今天的人脸比对次数已达到上限',\n        upSignReq: '请明天重试或联系合同发起者修改签署要求',\n        reqFace: '发件人要求你进行刷脸校验',\n        signAfterFace: '刷脸通过后即可完成合同签署',\n        date: 'Дата',\n        chooseSeal: '选择印章',\n        seal: '印章',\n        signature: '签名',\n        handwrite: '手写',\n        mysign: '我的签名',\n        approvePlace: '审批留言，可不填',\n        approvePlace_1: '审批留言',\n        approvePlace_2: '可不填',\n        approveAgree: '审批结果：同意',\n        approveReject: '审批结果：驳回',\n        signBy: '由',\n        signByEnd: '盖章',\n        sealBy: '由',\n        sealByEnd: '签名',\n        coverBy: '需盖',\n        applicant: '申请人',\n        continueVeri: '继续认证',\n        registerAndReal: '请注册并实名',\n        goToResiter: '请注册并认证',\n        sureToUse: '确定使用',\n        toSign: '签约吗?',\n        pleaseComplete: '请先完成',\n        confirmSign: '再确认签署',\n        admin: '管理员',\n        contratAdmin: '请联系管理员将您的账号',\n        addToEnt: '添加为企业成员',\n        alreadyExists: '在上上签已存在',\n        sendMsg: '上上签将以短信形式给管理员发以下内容：',\n        applyJoin: '申请加入',\n        title: 'заголовок',\n        viewImg: 'вид',\n        priLetter: 'личное письмо',\n        priLetterFromSomeone: 'письмо от {name}',\n        readLetter: 'Понял.',\n        approve: '同意',\n        disapprove: 'Отклонить',\n        refuseSign: 'Отказ в подписи',\n        paperSign: '改用纸质签署',\n        refuseTip: '请选择拒绝理由',\n        refuseReason: 'Укажите причину отказа, чтобы помочь другой стороне понять вашу проблему и ускорить процесс заключения контракта',\n        reasonWriteTip: '请填写拒签理由',\n        refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',\n        refuseConfirm: 'Отказ в подписи',\n        signValidationTitle: '签署校验',\n        email: 'E-mail',\n        phoneNumber: 'номер телефона',\n        password: '密码',\n        verificationCode: 'Код подтверждения',\n        mailVerificationCode: 'Pin Code',\n        forgetPsw: 'Забыли пароль',\n        if: '，是否',\n        forgetPassword: 'Забыли пароль',\n        rejectionVer: '拒签校验',\n        msgTip: 'Все время не получаете СМС сообщение? Пожалуйста, попробуйте еще раз ',\n        voiceVerCode: 'Голосовой код подтверждения',\n        SMSVerCode: 'СМС подтверждение ',\n        or: 'или ',\n        emailVerCode: 'код подтверждения почтового ящика ',\n        SentSuccessfully: 'Успешно отправлен',\n        intervalTip: 'Интервал отправки слишком короткий ',\n        signPsw: 'Пароль для подписи',\n        signSuc: 'Успешно подписано',\n        refuseSuc: '拒签成功',\n        approveSuc: '审批成功',\n        hdFile: '查看高清文件',\n        otherOperations: 'Другие операций',\n        reviewDetails: 'Детальное утверждение',\n        close: 'Закрыть',\n        submitter: '提交人',\n        signatory: 'Подписант',\n        reviewSchedule: '审批进度',\n        signByPc: '由{name}签名',\n        signPageDescription: '{index}-я страница, всего {total} страниц',\n        sealBySomeone: '由{name}盖章',\n        signDate: '签署日期',\n        download: 'Скачать',\n        signPage: 'Количество страниц: {page} страницы',\n        viewContract: '查看合同',\n        signNow: '立即签署',\n        sender: 'Отправитель',\n        signer: 'Подписант',\n        startSignTime: '发起签约时间',\n        signDeadLine: 'Крайний срок подписания',\n        authGuide: {\n            goToHome: 'Вернуться к меню',\n            tip_1: '认证完成后，可查看并签署合同。',\n            tip_2: '请使用身份 | 进行认证。',\n            tip_3: '发来合同',\n            tip_4: '请联系合同发起者 | 更改收件人。',\n            tip_5: '您认证的 | 无法查看合同',\n            new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',\n            new_tip_2: '基于发件方的合规要求，您需要以：',\n            new_tip_3: '完成以下步骤。',\n            new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',\n            entUserName: '姓名：',\n            idNumberForVerify: '身份证号：',\n            realNameAuth: '实名认证',\n            applySeal: '申请印章',\n            signContract: '签署合同',\n        },\n        switch: 'переключение',\n        selectSignature: '选择签名',\n        selectSigner: '选择签名人',\n        pleaseScanToSign: '请用支付宝或微信扫一扫签署',\n        pleaseScanAliPay: '请使用支付宝app扫描二维码签署',\n        pleaseScanWechat: '请使用微信app扫描二维码签署',\n        requiredFaceSign: '合同发件人要求您刷脸签署',\n        requiredDualSign: '合同发件人要求你使用双录校验',\n        qrcodeInvalid: '二维码信息已过期，请刷新',\n        faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',\n        verCodeVerify: '验证码校验',\n        applyToSign: '申请签署合同',\n        autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',\n        cannotSignBeforeApproval: '审批未完成，暂不能签署！',\n        finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',\n        uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',\n        cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',\n        unlimitedNotice: '该合同计费不限量使用',\n        units: '{num}份',\n        contractToPrivate: '对私合同',\n        contractToPublic: '对公合同',\n        paySum: '共{sum}需要您支付',\n        payTotal: '共计{total}元',\n        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值',\n        contactToRecharge: '请联系主管理员充值',\n        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',\n        needSignerPay: '合同发送方设置了签署方付费，并指定由您来支付合同费用。',\n        recharge: '充值',\n        toSubmit: '提交',\n        appliedSeal: '用印申请已提交',\n        noSeal: '无印章',\n        noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',\n        knew: '知道了',\n        noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',\n        hadAutoSet: '已在另外{num}处自动',\n        setThatSignature: '放置该签名',\n        setThatSeal: '放置该印章',\n        applyThatSeal: '申请该印章',\n        savedOnLeftSite: '已保存到左侧签名栏',\n        ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',\n        ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',\n        ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',\n        noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',\n        noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',\n        gotoAppendSeal: '去添加印章',\n        approvalFlowSuccessfulSet: '审批流设置成功',\n        mandate: '同意授权',\n        loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',\n        signIdentityAs: '当前正在以{person}的名义签署合同',\n        enterNextContract: '进入下一份合同',\n        fileList: '文件列表',\n        addSignerFile: '添加附属资料',\n        signatureFinish: '已全部盖章/签名',\n        dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',\n        noticeToManager: '给管理员发通知',\n        gotoAuthPerson: '去认证个人',\n        senderRequire: '发件方要求您',\n        senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',\n        suggestToAuth: '您还未实名认证，建议您实名认证后签署',\n        contactEntAdmin: '请联系企业主管理员',\n        setYourAccount: '将您的账号',\n        authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',\n        noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',\n        pleaseUse: '请使用',\n        me: '我',\n        myself: '本人，',\n        reAuthBtnTip: '我是当前手机号的实际使用者，',\n        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '当前账号是否是您的常用手机号？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署手机号，并指定由您签署。',\n        confirmOk: '确认',\n        goOnAuth: {\n            0: '进行认证，',\n            1: '请进行实名认证，',\n            2: '进行实名认证，',\n        },\n        signContractAfterAuth: {\n            0: '认证完成后，可签署合同。',\n            1: '完成认证后即可签署合同。',\n        },\n        useIdentity: '以{name}的身份',\n        inTheName: '以',\n        of: '的',\n        identity: '身份',\n        nameIs: '姓名为',\n        IDNumIs: '身份证号为',\n        provideMoreAuthData: '补充更多认证材料',\n        leadToAuthBeforeSign: '继续认证后即可签署合同',\n        groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',\n        contactSender: '如有疑问请联系发件方。',\n        note: '注:',\n        identityInfo: '身份信息',\n        signNeedCoincidenceInfo: '完全一致才能签署合同。',\n        needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',\n        iHadReadContract: '已阅读，本人已知晓{alias}内容',\n        getVerCodeFirst: '请先获取验证码',\n        appScanVerify: '上上签APP扫码校验',\n        downloadBSApp: '下载上上签APP',\n        scanned: '扫码成功',\n        confirmInBSApp: '请在上上签APP中确认签署',\n        qrCodeExpired: '二维码已失效，请刷新重试',\n        appKey: 'APP安全校验',\n        goToScan: '去扫码',\n        setNotificationInUserCenter: '请先到用户中心设置通知方式',\n        doNotWantUseVerCode: '不想用验证码',\n        try: '试试',\n        retry: '重试',\n        faceExceedTimes: '当日刷脸次数已达上线，请明日再试',\n        goToFaceVerify: '去刷脸',\n        returnBack: '返回',\n        switchTo: '切换至',\n        youCanChooseIdentityBlow: '您可以选择以下签约主体',\n        needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',\n        lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',\n        manageSeal: '管理印章',\n        needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',\n        chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',\n        appendDrawSignature: '添加手绘签名',\n        senderUnFill: '（发件人未填写）',\n        declare: '说明',\n        fileLessThan: '请上传小于{num}M的文件',\n        fileNeedUploadImg: '上传时请使用支持的附件格式',\n        serverError: '服务器开了点小差，请稍后再试',\n        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n        fileLimitFormatAndSize: '单个资料图片数量不超过10张。',\n        fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',\n        fileFormatFile: '支持pdf、txt、zip、xml格式，单份文件大小不超过10M',\n        signNeedKnow: '签约须知',\n        signNeedKnowFrom: '来自{sender}的签约须知',\n        approvalInfo: '审批须知',\n        approveNeedKnowFrom: '来自{sender}提交的审批资料',\n        setLabel: '设置标签',\n        addRidingSeal: '添加骑缝章',\n        delRidingSeal: '删除骑缝章',\n        file: '文件',\n        attachmentContent: '附件内容',\n        downloadFile: '下载源文件',\n        noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',\n        archiveTo: '归档到',\n        hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',\n        pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',\n        save: '保存',\n        remind: '提醒',\n        riskTip: '风险提醒',\n        chooseApplyPerson: '选择申请人',\n        chooseAdminSign: '选择印章管理员',\n        useSealByOther: '他人盖章',\n        getSeal: '获取印章',\n        nowApplySealList: '您正在请求以下印章',\n        nowAdminSealList: '你正在申请获得以下印章',\n        chooseApplyPersonToDeal: '请选择申请人，您的申请以及合同将会转交给所选人来处理',\n        chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',\n        contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',\n        sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',\n        successfulSent: '发送成功',\n        needSomeoneToSignature: '由{x}盖{y}',\n        needToSet: '需盖',\n        approver: '申请人：',\n        clickToSignature: '点击此处签名',\n        transferToOtherToSign: '转给其他人签',\n        signatureBy: '由{x}签名',\n        tipRightNumber: '请输入正确的数字',\n        tipRequired: '必填值不可为空',\n        confirm: '确定',\n        viewContractDetail: '查看合同详情',\n        crossPlatformCofirm: {\n            message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',\n            title: '数据授权',\n            confirmButtonText: '同意授权',\n            cancelButtonText: '取消',\n        },\n        sealScope: '印章使用范围',\n        currentContract: '当前合同',\n        allContract: '所有合同',\n    },\n    signJa: {\n        beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',\n        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n        entNamePlaceholder: '请输入企业名称',\n        corporateNumberPlaceholder: '请输入法人番号',\n        corporateNumber: '法人番号',\n        singerNamePlaceholder: '请输入签署人姓名',\n        singerName: '签署人姓名',\n        businessPic: '印鉴证明书',\n        waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',\n        itsMe: '是我本人',\n        wrongInformation: '信息有误',\n        confirmChange: '确认更换',\n        communicateSender1: '不更换, 与甲方沟通',\n        communicateSender2: '取消, 去与发件方沟通',\n    },\n    signPC: {\n        commonSign: 'Подтвердить подпись',\n        contractVerification: 'Проверка подписи',\n        VerCodeVerify: 'Проверить код подтверждение',\n        QrCodeVerify: 'Проверка QR-кода',\n        verifyTip: 'BestSign в данный момент проверяет ваш безопасный цифровой сертификат, вы находитесь в защищенной среде электронной подписи, пожалуйста, будьте спокойны в подписывании!',\n        verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',\n        selectSeal: 'Электронная печать',\n        toAddSealWithConsole: ' Вы также можете добавить печать на платформе управление компаниями',\n        use: 'Использовать',\n        toAddSeal: 'Добавить печать',\n        mySeal: 'Моя печать',\n        operationCompleted: 'Операция завершена',\n    },\n    signTip: {\n        contractDetail: 'Информация о контракте',\n        downloadBtn: '下载APP',\n        tips: 'Напоминать',\n        submit: 'Подтвердить',\n        SigningCompleted: 'Успешно подписано',\n        submitCompleted: '等待他人处理',\n        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n        noRightSign: '合同正在签署中，当前用户不允许签署操作',\n        noNeedSign: '内部决议合同，已无需签署',\n        ApprovalCompleted: '审批成功',\n        contractRevoked: '该合同已被撤销',\n        contractRefused: '该合同已被拒签',\n        linkExpired: '该链接已失效',\n        contractClosed: '该合同已截止签约',\n        approvalReject: '该合同审批已被驳回',\n        approving: '合同正在审批中',\n        viewContract: 'Просмотреть контракт',\n        downloadContract: '下载合同',\n        signed: 'контракта',\n        approved: 'утверждение',\n        approval: 'утверждение',\n        personHas: ' человек ',\n        personHave: ' человек ',\n        personHasnot: '人未',\n        personsHavenot: '人未',\n        cannotReview: '无法查看合同',\n        cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',\n        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n        beenDeleted: '您的账号已被企业管理员删除',\n        unActive: '无法继续激活账',\n        back: '返回',\n        contratStatusDes: 'Статус контракта:',\n        contractConditionDes: 'Статус контракта:',\n        contractIng: 'Подписание {key}',\n        contractComplete: 'Подписание {key} завершено',\n        operate: '合同操作',\n        freeContract: '完成首次合同发送，可免费再获取合同份数',\n        sendContract: '去发合同',\n        congratulations: '恭喜{name}企业已完成{num}份合同签署，',\n        carbonSaving: '预估节碳{num}g',\n        signGift: '上上签赠送您{num}份对私合同（使用期限至{limit}）',\n        followPublic: '关注微信公众号，随时接收合同消息',\n        congratulationsSingle: '恭喜{name}完成合同签署，',\n        carbonSavingSingle: '预估新增节碳量2002.4g',\n        viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮',\n    },\n    view: {\n        title: '查看合同',\n        ok: '完成',\n        cannotReview: '无法查看合同',\n        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n    },\n    prepare: {\n        signHeaderTitle: 'Добавить файл и подписанта',\n        step1: 'Шаг 1',\n        uploadFile: 'Загрузить файлы',\n        step2: 'Шаг 2',\n        addSigner: 'Добавить подписывающую сторону',\n        step3: 'Шаг 3',\n        actionDemo: 'Процесс демонстраций',\n        next: 'Следующий',\n        isUploadingErr: 'Файл еще не был загружен. Пожалуйста, продолжите после завершения.',\n        noUploadFileErr: 'Файл не загружен, пожалуйста, продолжите после загрузки',\n        noContractTitleErr: 'Название договора не заполнено, после заполнения, можете продолжить',\n        contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',\n        expiredDateErr: 'Срок подписания неверный, прошу после поправки продолжить',\n        noExpiredDateErr: 'Пожалуйста, заполните срок подписания и продолжайте',\n        noRecipientsErr: 'Добавьте хотя бы одного подписанта',\n        noAccountErr: 'Аккаунт не может быть пустым',\n        noUserNameErr: '姓名不能为空',\n        noIDNumberErr: '身份证号码不能为空',\n        accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',\n        userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',\n        enterpriseNameErr: 'Неверный формат, введите правильное название компании',\n        idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',\n        signerErr: 'Подписавшая сторона имеет ошибку',\n        noSignerErr: 'Прошу по меньшей мере добавить одного подписчика',\n        lackAttachmentNameErr: 'Пожалуйста, заполните название вложения',\n        repeatRecipientsErr: 'Во время подписания нельзя добавлять повторно подписывающие стороны, если не в последовательном порядке',\n        innerContact: 'Контакты внутреннего отдела',\n        outerContact: 'Контакты внешнего отдела',\n        search: 'Поиск',\n        accountSelected: 'Уже выбран аккаунт',\n        groupNameAll: 'Полностью',\n        unclassified: 'Не отсортировано',\n        fileLessThan: 'Загрузка файла меньше {num}м',\n        beExcel: 'Загрузка файла Excel',\n        usePdf: 'при загрузке используйте файл PDF или рисунок',\n        fileNameMoreThan: 'имя файла с длиной более ${num}, будет автоматически удалено',\n        needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',\n        addSender: '添加为签约方',\n        tip: '提示',\n        cancel: '取消',\n    },\n    addReceiver: {\n        limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',\n        orderSignLabel: 'Последовательное подписание ',\n        contactAddress: 'Контактная адресная книга',\n        signOrder: 'Порядок подписания ',\n        account: 'Аккаунт ',\n        accountPlaceholder: 'Телефон/электронная почта (требуется)',\n        accountReceptionCollection: '前台代收',\n        accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',\n        accountReceptionCollectionTip2: '请选择前台代收',\n        signSubjectPerson: 'Тема подписи: Физическое лицо',\n        nameTips: 'Ф. И. О.  (выборочное заполнение)',\n        requiredNameTips: '姓名（必填，用于签约身份核对）',\n        entOperatorNameTips: 'Ф. И. О.  (выборочное заполнение)',\n        needAuth: 'Нужно аутентифицироваться',\n        signSubjectEnt: 'Тема подписи: Юридическое лицо ',\n        entNameTips: 'Наименование компании (Выборочное заполнение)',\n        operator: 'Исполнитель',\n        sign: 'Подписать ',\n        more: 'Больше',\n        faceFirst: 'Приоритетная чистка, резервное копирование кода подтверждения',\n        faceFirstTips: 'При подписании система по умолчанию выполняет проверку лица. Когда число раз, когда кисть не проходит, достигает верхнего предела дня, она автоматически переключается на проверку кода проверки',\n        mustFace: 'Обязательно подпишите с распознаванием лица ',\n        handWriteNotAllowed: 'Рукописные подписи не допускаются',\n        mustHandWrite: 'Обязательно подпишите рукой ',\n        fillIDNumber: 'паспортные данные ',\n        fillNoticeCall: 'номер телефона для уведомления ',\n        fillNoticeCallTips: 'Введите номер телефона для уведомления ',\n        addNotice: 'Добавить личное сообщение',\n        attachTips: 'Требования к приложению',\n        faceSign: 'Обязательно подпишите с распознаванием лица ',\n        faceSignTips: 'Данный пользователь должен пройти аутентификацию лица, чтобы завершить подписание ',\n        handWriteNotAllowedTips: 'Пользователь может выбрать только подпись, которая была установлена, или использовать подпись шрифта по умолчанию для завершения подписи',\n        handWriteTips: 'Пользователю нужна рукописная подпись для завершения подписи',\n        idNumberTips: 'Используется для подписи проверки личности',\n        verifyBefore: 'Подтвердите личность перед просмотром файлов',\n        verify: 'Подтвердить личность',\n        verifyTips: 'максимум 20 букв',\n        verifyTips2: 'Вы должны предоставить эту информацию для проверки данному пользователю',\n        sendToThirdPlatform: 'Отправить платформе третьей стороне',\n        platFormName: 'Название платформы',\n        fillThirdPlatFormName: 'Введите название третьей платформы',\n        attach: 'Прикрепленный файл ',\n        attachName: 'Название приложении',\n        exampleID: 'например: фото паспорта',\n        attachInfo: 'Инструкция приложения',\n        attachInfoTips: 'например:  загрузите фото паспорта',\n        addAttachRequire: 'Добавить требования к вложению',\n        addSignEnt: 'Добавить подпись компании ',\n        addSignPerson: 'Добавить подпись физического лица ',\n        selectContact: 'Выбрать контактное лицо',\n        save: 'Сохранить',\n        searchVerify: 'Проверка запроса',\n        fillImageContentTips: 'Пожалуйста, заполните содержание изображения',\n        ok: 'Подтвердить ',\n        findContact: 'Найти следующих подписывающих сторон с договора',\n        signer: 'Подписант ',\n        signerTips: 'Маленькое примечание: после выбора подписанта, платформа поможет вам определить место подписи и  печати',\n        add: 'Добавить ',\n        notAdd: 'Не добавляйте',\n        cc: 'Отправить копию ',\n        notNeedAuth: 'Не требует аутентификаций',\n        extracting: 'Извлечение…',\n        autoFill: 'Автоматически заполнить данные подписанта',\n        failExtracting: 'не получено подписавшей стороной',\n        idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',\n        noAccountErr: 'Аккаунт не может быть пустым',\n        noUserNameErr: '姓名不能为空',\n        noIDNumberErr: '身份证号码不能为空',\n        accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',\n        enterpriseNameErr: 'Неверный формат, введите правильное название компании',\n        userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',\n        riskCues: 'Предупреждение о риске',\n        riskCuesMsg: 'Если подписавшая сторона подписывается не с настоящим именем, вам необходимо будет предоставить паспорт подписавшей стороны в случае возникновения спора. Чтобы избежать риска, выберите нужное настоящее имя.',\n        confirmBtnText: 'Выбрать нужные данные',\n        cancelBtnText: 'Выбрать не нужные данные',\n        attachLengthErr: 'Вы можете добавить до 50 запросов на вложение только одному подписанту',\n        collapse: 'Сложить',\n        expand: 'Разверните',\n        delete: 'Удалить',\n        saySomething: 'Комментарий (скажите что-нибудь)',\n        addImage: 'Добавить фото',\n        addImageTips: 'поддерживает pdf、word、jpg、png, формат, максимум можно загрузить 10 шт.',\n        give: 'дайте',\n        fileMax: 'количество отдачи превысило верхний предел',\n    },\n    field: {\n        send: 'Отправить ',\n        contractDispatchApply: 'подаять заявку на контракт',\n        contractNeedYouSign: 'Данный документ нужно подписать вам ',\n        ifSignRightNow: 'Подписать ли его сейчас',\n        signRightNow: 'Подпишите сейчас',\n        signLater: 'Знак позже',\n        signaturePositionErr: 'Пожалуйста, укажите место подписи для каждого подписанта',\n        sendSucceed: 'Успешно отправлен',\n        confirm: 'Подтвердить ',\n        cancel: 'Отменить',\n        qrCodeTips: 'После подписания кода вы можете просмотреть детали подписи, проверить действительность подписи и проверить, был ли подделан договор.',\n        pagesField: '-я страница, всего {totalPages} страниц ',\n        suitableWidth: 'Соответствующая сторона ',\n        signCheck: 'Проверка подписи',\n        locateSignaturePosition: '定位签署位置',\n        locateTips: 'Может помочь быстро найти местоположение подписи. В настоящее время поддерживается только первое местоположение подписи для каждого подписанта',\n        step1: 'Шаг 1',\n        selectSigner: 'Выбрать подписанта',\n        step2: 'Шаг 2',\n        dragSignaturePosition: 'Перетащить место подписи',\n        signingField: 'Место подписи',\n        docTitle: 'Документ ',\n        totalPages: 'Количество страниц: {totalPages} страницы',\n        receiver: 'Приемник',\n        delete: 'Удалить',\n        deductPublicNotice: 'Если количество копий частного договора недостаточно, договор будет вычтен',\n        unlimitedNotice: '该合同计费不限量使用',\n        charge: 'Расчет',\n        units: '{num} ',\n        clickDecoration: '点击合同装饰',\n        contractToPrivate: 'корпоративный контракт',\n        contractToPublic: 'частный контракт',\n        costTips: {\n            1: 'Для публичного договора: подписант(не включая отправителя) договор с корпоративным аккаунтом',\n            2: 'Для частного договора: подписант(не включая отправителя) договор без корпоративного аккаунта',\n            3: 'количество тарификации рассчитывается исходя из количество копий файла.',\n            4: 'количество тарификации=количество файлов×партия введенных количество пользователей',\n        },\n        costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',\n        toCharge: 'Пополнить баланс ',\n        contractNeedCharge: {\n            1: 'Количество доступных контрактов недостаточна, и не может быть отправлено',\n            2: 'Количество доступных контрактов недостаточна, пожалуйста, свяжитесь с главным администратором, чтобы пополнить»',\n        },\n        chooseApprover: '选择审批人：',\n        nextStep: '下一步',\n        submitApproval: '提交审批',\n        autoSendAfterApproval: '*审批通过后，自动发送合同',\n        chooseApprovalFlow: '请选择一个审批流',\n        completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',\n        viewPrivateLetter: '查看私信',\n        addPrivateLetter: '添加私信',\n        append: '添加',\n        privateLetter: '私信',\n        signNeedKnow: '签约须知',\n        maximum5M: '请上传小于5M的文档',\n        uploadServerFailure: '上传到服务器失败',\n        uploadFailure: '上传失败',\n        pager: '页码',\n        seal: 'Поставить печать ',\n        signature: 'Подписать',\n        signDate: 'Дата подписания',\n        text: '文本',\n        date: '日期',\n        qrCode: '二维码',\n        number: '数字',\n        dynamicTable: '动态表格',\n        terms: '合同条款',\n        checkBox: '复选框',\n        radioBox: '单选框',\n        image: '图片',\n    },\n    paperSign: {\n        title: '使用纸质方式签署',\n        stepText: ['下一步', '确认纸质签', '确定'],\n        needUploadFile: '请先上传扫描件',\n        uploadError: '上传失败',\n        cancel: '取消',\n        downloadPaperFile: '获取纸质签文件',\n        step0: {\n            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n            address: '邮寄地址：',\n            contactName: '接收人姓名：',\n            contactPhone: '接收人联系方式：',\n            defaultValue: '请通过线下方式向发件方索取',\n        },\n        step1: {\n            title0: '第一步：下载&打印纸质合同',\n            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n            title1: '第二步：加盖印章',\n            title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],\n        },\n        step2: {\n            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n            uploadFile: '上传扫描件',\n            getCodeVerify: '获取合同签署校验',\n            isUploading: '上传中...',\n        },\n    },\n    allowPaperSignDialog: {\n        title: '允许纸质签',\n        content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',\n        tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',\n        icon: '转纸质签署 >>',\n        goSign: '去电子签',\n        cancel: '取消',\n    },\n    sealInconformityDialog: {\n        errorSeal: {\n            title: '印章提示',\n            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n            tip1: '检测到有企业印章与企业名称：',\n            tip2: '是否要继续使用当前印章图片？',\n            tip3: '根据发件方要求，您需要使用企业名称为：',\n            tip4: '的印章',\n            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n            tip6: '不匹配，请确保印章符合发件方要求。',\n            guide: '如何上传正确的印章 >>',\n            next: '继续使用',\n            tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',\n            tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？',\n        },\n        exampleSeal: {\n            title: '上传印章图案方式',\n            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],\n        },\n        confirm: '确认',\n        cancel: '取消',\n    },\n    addSealDialog: {\n        title: '添加印章图片',\n        dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNP等），由系统将此印章图片合并进入当前合同中。',\n        dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',\n        updateNewSeal: '上传新章',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE;IACFC,aAAa,EAAE,4GAA4G;IAC3HC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,UAAU;IACtBC,aAAa,EAAE,UAAU;IACzBC,wBAAwB,EAAE,aAAa;IACvCC,mBAAmB,EAAE,cAAc;IACnCC,qBAAqB,EAAE,QAAQ;IAC/BC,UAAU,EAAE,OAAO;IACnBC,gBAAgB,EAAE,oCAAoC;IACtDC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,UAAU;IAClBZ,IAAI,EAAE,MAAM;IACZa,OAAO,EAAE,kBAAkB;IAC3BC,eAAe,EAAE,mCAAmC;IACpDC,cAAc,EAAE,0CAA0C;IAC1DC,kBAAkB,EAAE,eAAe;IACnCC,cAAc,EAAE,oBAAoB;IACpCC,aAAa,EAAE,oBAAoB;IACnCC,UAAU,EAAE,yCAAyC;IACrDC,cAAc,EAAE,+FAA+F;IAC/GC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,qCAAqC;IACpDC,qBAAqB,EAAE,eAAe;IACtCC,OAAO,EAAE,kBAAkB;IAC3BC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,aAAa;IACvBC,gBAAgB,EAAE,WAAW;IAC7BC,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,qCAAqC;IAClDC,iBAAiB,EAAE,gBAAgB;IACnCC,aAAa,EAAE,yCAAyC;IACxDC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,cAAc;IAC1BC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,SAAS;IACzBC,YAAY,EAAE,qBAAqB;IACnCC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,kCAAkC;IAChDC,WAAW,EAAE,MAAM;IACnBC,gBAAgB,EAAE,sEAAsE;IACxFC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,eAAe;IAC3BC,UAAU,EAAE,yBAAyB;IACrCC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,gBAAgB;IAC3BC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,cAAc;IACvBC,aAAa,EAAE,eAAe;IAC9BC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,MAAM;IAClBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,KAAK;IACrBC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,SAAS;IACxBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,QAAQ;IACrBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,MAAM;IACtBC,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE,KAAK;IACZC,YAAY,EAAE,aAAa;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,SAAS;IACxBC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,eAAe;IAC1BC,oBAAoB,EAAE,kBAAkB;IACxCC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,iBAAiB;IAC7BC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,kHAAkH;IAChIC,cAAc,EAAE,SAAS;IACzBC,iBAAiB,EAAE,0BAA0B;IAC7CC,aAAa,EAAE,iBAAiB;IAChCC,mBAAmB,EAAE,MAAM;IAC3BC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,mBAAmB;IACrCC,oBAAoB,EAAE,UAAU;IAChCC,SAAS,EAAE,eAAe;IAC1BC,EAAE,EAAE,KAAK;IACTC,cAAc,EAAE,eAAe;IAC/BC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE,uEAAuE;IAC/EC,YAAY,EAAE,6BAA6B;IAC3CC,UAAU,EAAE,oBAAoB;IAChCC,EAAE,EAAE,MAAM;IACVC,YAAY,EAAE,oCAAoC;IAClDC,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,qCAAqC;IAClDC,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EAAE,mBAAmB;IAC5BC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,QAAQ;IAChBC,eAAe,EAAE,iBAAiB;IAClCC,aAAa,EAAE,uBAAuB;IACtCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,WAAW;IACrBC,mBAAmB,EAAE,2CAA2C;IAChEC,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,qCAAqC;IAC/CC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,yBAAyB;IACvCC,SAAS,EAAE;MACPC,QAAQ,EAAE,kBAAkB;MAC5BC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE,eAAe;MACtBC,SAAS,EAAE,uBAAuB;MAClCC,SAAS,EAAE,kBAAkB;MAC7BC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE,KAAK;MAClBC,iBAAiB,EAAE,OAAO;MAC1BC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,MAAM;MACjBC,YAAY,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,cAAc;IACtBC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,OAAO;IACrBC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,gBAAgB,EAAE,iBAAiB;IACnCC,gBAAgB,EAAE,cAAc;IAChCC,gBAAgB,EAAE,gBAAgB;IAClCC,aAAa,EAAE,cAAc;IAC7BC,eAAe,EAAE,mBAAmB;IACpCC,aAAa,EAAE,OAAO;IACtBC,WAAW,EAAE,QAAQ;IACrBC,uBAAuB,EAAE,qBAAqB;IAC9CC,wBAAwB,EAAE,cAAc;IACxCC,yBAAyB,EAAE,gBAAgB;IAC3CC,qBAAqB,EAAE,qBAAqB;IAC5CC,sBAAsB,EAAE,qBAAqB;IAC7CC,eAAe,EAAE,YAAY;IAC7BC,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,6BAA6B;IACxCC,iBAAiB,EAAE,WAAW;IAC9BC,kBAAkB,EAAE,qBAAqB;IACzCC,aAAa,EAAE,6BAA6B;IAC5CC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,KAAK;IACbC,0BAA0B,EAAE,2BAA2B;IACvDC,IAAI,EAAE,KAAK;IACXC,sBAAsB,EAAE,qBAAqB;IAC7CC,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,OAAO;IACzBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE,WAAW;IAC5BC,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,iBAAiB;IACrCC,uBAAuB,EAAE,yBAAyB;IAClDC,eAAe,EAAE,mBAAmB;IACpCC,4BAA4B,EAAE,yBAAyB;IACvDC,cAAc,EAAE,OAAO;IACvBC,yBAAyB,EAAE,SAAS;IACpCC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE,yBAAyB;IAC5CC,cAAc,EAAE,sBAAsB;IACtCC,iBAAiB,EAAE,SAAS;IAC5BC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE,UAAU;IAC3BC,gBAAgB,EAAE,uBAAuB;IACzCC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,OAAO;IACvBC,aAAa,EAAE,QAAQ;IACvBC,8BAA8B,EAAE,gBAAgB;IAChDC,aAAa,EAAE,oBAAoB;IACnCC,eAAe,EAAE,WAAW;IAC5BC,cAAc,EAAE,OAAO;IACvBC,yBAAyB,EAAE,+CAA+C;IAC1EC,mBAAmB,EAAE,iCAAiC;IACtDC,SAAS,EAAE,KAAK;IAChBC,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,gBAAgB;IAC9BC,gBAAgB,EAAE,wBAAwB;IAC1CC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,cAAc,EAAE,+BAA+B;IAC/CC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,iBAAiB;IACtBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,mBAAmB;IACpCC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;MACN,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,UAAU;MACb,CAAC,EAAE;IACP,CAAC;IACDC,qBAAqB,EAAE;MACnB,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,GAAG;IACdC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,OAAO;IAChBC,mBAAmB,EAAE,UAAU;IAC/BC,oBAAoB,EAAE,aAAa;IACnCC,sBAAsB,EAAE,iCAAiC;IACzDC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,MAAM;IACpBC,uBAAuB,EAAE,aAAa;IACtCC,8BAA8B,EAAE,oBAAoB;IACpDC,gBAAgB,EAAE,oBAAoB;IACtCC,eAAe,EAAE,SAAS;IAC1BC,aAAa,EAAE,YAAY;IAC3BC,aAAa,EAAE,UAAU;IACzBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,eAAe;IAC/BC,aAAa,EAAE,cAAc;IAC7BC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,KAAK;IACfC,2BAA2B,EAAE,eAAe;IAC5CC,mBAAmB,EAAE,QAAQ;IAC7BC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE,KAAK;IACrBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,KAAK;IACfC,wBAAwB,EAAE,aAAa;IACvCC,sBAAsB,EAAE,iBAAiB;IACzCC,mBAAmB,EAAE,oBAAoB;IACzCC,UAAU,EAAE,MAAM;IAClBC,wBAAwB,EAAE,sBAAsB;IAChDC,mBAAmB,EAAE,sBAAsB;IAC3CC,mBAAmB,EAAE,QAAQ;IAC7BC,YAAY,EAAE,UAAU;IACxBC,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,gBAAgB;IAC9BC,iBAAiB,EAAE,eAAe;IAClCC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE,+CAA+C;IAC7DC,sBAAsB,EAAE,iBAAiB;IACzCC,eAAe,EAAE,uCAAuC;IACxDC,cAAc,EAAE,kCAAkC;IAClDC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,iBAAiB;IACnCC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,mBAAmB;IACxCC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE,OAAO;IACtBC,IAAI,EAAE,IAAI;IACVC,iBAAiB,EAAE,MAAM;IACzBC,YAAY,EAAE,OAAO;IACrBC,mBAAmB,EAAE,kBAAkB;IACvCC,SAAS,EAAE,KAAK;IAChBC,mBAAmB,EAAE,iCAAiC;IACtDC,uBAAuB,EAAE,0BAA0B;IACnDC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE,OAAO;IAC1BC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,MAAM;IACtBC,OAAO,EAAE,MAAM;IACfC,gBAAgB,EAAE,WAAW;IAC7BC,gBAAgB,EAAE,aAAa;IAC/BC,uBAAuB,EAAE,4BAA4B;IACrDC,0BAA0B,EAAE,uDAAuD;IACnFC,iCAAiC,EAAE,cAAc;IACjDC,uBAAuB,EAAE,iCAAiC;IAC1DC,cAAc,EAAE,MAAM;IACtBC,sBAAsB,EAAE,UAAU;IAClCC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,MAAM;IAChBC,gBAAgB,EAAE,QAAQ;IAC1BC,qBAAqB,EAAE,QAAQ;IAC/BC,WAAW,EAAE,QAAQ;IACrBC,cAAc,EAAE,UAAU;IAC1BC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,IAAI;IACbC,kBAAkB,EAAE,QAAQ;IAC5BC,mBAAmB,EAAE;MACjBC,OAAO,EAAE,oCAAoC;MAC7C/O,KAAK,EAAE,MAAM;MACbgP,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE;IACtB,CAAC;IACDC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE;EACjB,CAAC;EACDC,MAAM,EAAE;IACJC,cAAc,EAAE,uBAAuB;IACvCC,cAAc,EAAE,sCAAsC;IACtDC,cAAc,EAAE,6BAA6B;IAC7CC,cAAc,EAAE,wEAAwE;IACxFC,cAAc,EAAE,uDAAuD;IACvEC,cAAc,EAAE,mCAAmC;IACnDC,cAAc,EAAE,oBAAoB;IACpCC,kBAAkB,EAAE,SAAS;IAC7BC,0BAA0B,EAAE,SAAS;IACrCC,eAAe,EAAE,MAAM;IACvBC,qBAAqB,EAAE,UAAU;IACjCC,UAAU,EAAE,OAAO;IACnBC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,gDAAgD;IAC7DC,KAAK,EAAE,MAAM;IACbC,gBAAgB,EAAE,MAAM;IACxBC,aAAa,EAAE,MAAM;IACrBC,kBAAkB,EAAE,YAAY;IAChCC,kBAAkB,EAAE;EACxB,CAAC;EACDC,MAAM,EAAE;IACJC,UAAU,EAAE,qBAAqB;IACjCC,oBAAoB,EAAE,kBAAkB;IACxCC,aAAa,EAAE,6BAA6B;IAC5CC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,0KAA0K;IACrLC,YAAY,EAAE,0CAA0C;IACxDC,UAAU,EAAE,oBAAoB;IAChCC,oBAAoB,EAAE,qEAAqE;IAC3FC,GAAG,EAAE,cAAc;IACnBC,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,YAAY;IACpBC,kBAAkB,EAAE;EACxB,CAAC;EACDC,OAAO,EAAE;IACLC,cAAc,EAAE,wBAAwB;IACxCC,WAAW,EAAE,OAAO;IACpBC,IAAI,EAAE,YAAY;IAClBzW,MAAM,EAAE,aAAa;IACrB0W,gBAAgB,EAAE,mBAAmB;IACrCC,eAAe,EAAE,QAAQ;IACzBC,UAAU,EAAE,uBAAuB;IACnCC,WAAW,EAAE,qBAAqB;IAClCC,UAAU,EAAE,cAAc;IAC1BC,iBAAiB,EAAE,MAAM;IACzBC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,QAAQ;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,WAAW;IAC3BC,SAAS,EAAE,SAAS;IACpBrP,YAAY,EAAE,sBAAsB;IACpCsP,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,WAAW;IACnBlW,QAAQ,EAAE,aAAa;IACvBmW,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,WAAW;IACvBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBrW,YAAY,EAAE,QAAQ;IACtBsW,cAAc,EAAE,oCAAoC;IACpDC,cAAc,EAAE,qCAAqC;IACrDC,WAAW,EAAE,eAAe;IAC5BC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,IAAI;IACVC,gBAAgB,EAAE,mBAAmB;IACrCC,oBAAoB,EAAE,mBAAmB;IACzCC,WAAW,EAAE,kBAAkB;IAC/BC,gBAAgB,EAAE,4BAA4B;IAC9CC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,qBAAqB;IACnCC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,0BAA0B;IAC3CC,YAAY,EAAE,YAAY;IAC1BC,QAAQ,EAAE,gCAAgC;IAC1CC,YAAY,EAAE,kBAAkB;IAChCC,qBAAqB,EAAE,iBAAiB;IACxCC,kBAAkB,EAAE,gBAAgB;IACpCC,eAAe,EAAE;EACrB,CAAC;EACDC,IAAI,EAAE;IACFhU,KAAK,EAAE,MAAM;IACbiU,EAAE,EAAE,IAAI;IACR1X,YAAY,EAAE,QAAQ;IACtBuW,cAAc,EAAE;EACpB,CAAC;EACDoB,OAAO,EAAE;IACLC,eAAe,EAAE,4BAA4B;IAC7CC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,gCAAgC;IAC3CC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,WAAW;IACjBC,cAAc,EAAE,oEAAoE;IACpFC,eAAe,EAAE,yDAAyD;IAC1EC,kBAAkB,EAAE,qEAAqE;IACzFC,eAAe,EAAE,kEAAkE;IACnFC,cAAc,EAAE,2DAA2D;IAC3EC,gBAAgB,EAAE,qDAAqD;IACvEC,eAAe,EAAE,oCAAoC;IACrDC,YAAY,EAAE,8BAA8B;IAC5CC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,WAAW;IAC1BC,gBAAgB,EAAE,4EAA4E;IAC9FC,iBAAiB,EAAE,6DAA6D;IAChFC,iBAAiB,EAAE,uDAAuD;IAC1EC,oBAAoB,EAAE,iEAAiE;IACvFC,SAAS,EAAE,kCAAkC;IAC7CC,WAAW,EAAE,kDAAkD;IAC/DC,qBAAqB,EAAE,yCAAyC;IAChEC,mBAAmB,EAAE,yGAAyG;IAC9HC,YAAY,EAAE,6BAA6B;IAC3CC,YAAY,EAAE,0BAA0B;IACxCC,MAAM,EAAE,OAAO;IACfC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,kBAAkB;IAChCjK,YAAY,EAAE,8BAA8B;IAC5CkK,OAAO,EAAE,sBAAsB;IAC/BC,MAAM,EAAE,+CAA+C;IACvDC,gBAAgB,EAAE,8DAA8D;IAChFC,aAAa,EAAE,+CAA+C;IAC9DC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,IAAI;IACTvb,MAAM,EAAE;EACZ,CAAC;EACDwb,WAAW,EAAE;IACTC,kBAAkB,EAAE,0BAA0B;IAC9CC,cAAc,EAAE,8BAA8B;IAC9CC,cAAc,EAAE,2BAA2B;IAC3CC,SAAS,EAAE,qBAAqB;IAChC1a,OAAO,EAAE,UAAU;IACnB2a,kBAAkB,EAAE,uCAAuC;IAC3DC,0BAA0B,EAAE,MAAM;IAClCC,8BAA8B,EAAE,mBAAmB;IACnDC,8BAA8B,EAAE,SAAS;IACzCC,iBAAiB,EAAE,+BAA+B;IAClDC,QAAQ,EAAE,mCAAmC;IAC7CC,gBAAgB,EAAE,iBAAiB;IACnCC,mBAAmB,EAAE,mCAAmC;IACxDC,QAAQ,EAAE,2BAA2B;IACrCC,cAAc,EAAE,iCAAiC;IACjDC,WAAW,EAAE,+CAA+C;IAC5DC,QAAQ,EAAE,aAAa;IACvBpd,IAAI,EAAE,YAAY;IAClBqd,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,+DAA+D;IAC1EC,aAAa,EAAE,kMAAkM;IACjNC,QAAQ,EAAE,8CAA8C;IACxDC,mBAAmB,EAAE,mCAAmC;IACxDC,aAAa,EAAE,8BAA8B;IAC7CC,YAAY,EAAE,oBAAoB;IAClCC,cAAc,EAAE,iCAAiC;IACjDC,kBAAkB,EAAE,yCAAyC;IAC7DC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE,yBAAyB;IACrCC,QAAQ,EAAE,8CAA8C;IACxDC,YAAY,EAAE,oFAAoF;IAClGC,uBAAuB,EAAE,0IAA0I;IACnKC,aAAa,EAAE,8DAA8D;IAC7EC,YAAY,EAAE,4CAA4C;IAC1DC,YAAY,EAAE,8CAA8C;IAC5DC,MAAM,EAAE,sBAAsB;IAC9BC,UAAU,EAAE,kBAAkB;IAC9BC,WAAW,EAAE,yEAAyE;IACtFC,mBAAmB,EAAE,qCAAqC;IAC1DC,YAAY,EAAE,oBAAoB;IAClCC,qBAAqB,EAAE,oCAAoC;IAC3DC,MAAM,EAAE,qBAAqB;IAC7BC,UAAU,EAAE,qBAAqB;IACjCC,SAAS,EAAE,yBAAyB;IACpCC,UAAU,EAAE,uBAAuB;IACnCC,cAAc,EAAE,oCAAoC;IACpDC,gBAAgB,EAAE,gCAAgC;IAClDC,UAAU,EAAE,4BAA4B;IACxCC,aAAa,EAAE,oCAAoC;IACnDC,aAAa,EAAE,yBAAyB;IACxCnM,IAAI,EAAE,WAAW;IACjBoM,YAAY,EAAE,kBAAkB;IAChCC,oBAAoB,EAAE,8CAA8C;IACpE1F,EAAE,EAAE,cAAc;IAClB2F,WAAW,EAAE,iDAAiD;IAC9DzW,MAAM,EAAE,YAAY;IACpB0W,UAAU,EAAE,yGAAyG;IACrHC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,eAAe;IACvBC,EAAE,EAAE,kBAAkB;IACtBC,WAAW,EAAE,2BAA2B;IACxCC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,2CAA2C;IACrDC,cAAc,EAAE,kCAAkC;IAClD5E,oBAAoB,EAAE,iEAAiE;IACvFN,YAAY,EAAE,8BAA8B;IAC5CC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,WAAW;IAC1BC,gBAAgB,EAAE,4EAA4E;IAC9FE,iBAAiB,EAAE,uDAAuD;IAC1ED,iBAAiB,EAAE,6DAA6D;IAChF+E,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,gNAAgN;IAC7NC,cAAc,EAAE,uBAAuB;IACvCC,aAAa,EAAE,0BAA0B;IACzCC,eAAe,EAAE,wEAAwE;IACzFC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,kCAAkC;IAChDC,QAAQ,EAAE,eAAe;IACzBC,YAAY,EAAE,wEAAwE;IACtFC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;EACb,CAAC;EACDC,KAAK,EAAE;IACHnf,IAAI,EAAE,YAAY;IAClBof,qBAAqB,EAAE,4BAA4B;IACnDC,mBAAmB,EAAE,sCAAsC;IAC3DC,cAAc,EAAE,yBAAyB;IACzCC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,YAAY;IACvBC,oBAAoB,EAAE,0DAA0D;IAChFC,WAAW,EAAE,mBAAmB;IAChC7M,OAAO,EAAE,cAAc;IACvB3T,MAAM,EAAE,UAAU;IAClBygB,UAAU,EAAE,sIAAsI;IAClJC,UAAU,EAAE,0CAA0C;IACtDC,aAAa,EAAE,0BAA0B;IACzCC,SAAS,EAAE,kBAAkB;IAC7BC,uBAAuB,EAAE,QAAQ;IACjCC,UAAU,EAAE,gJAAgJ;IAC5J3H,KAAK,EAAE,OAAO;IACd5P,YAAY,EAAE,oBAAoB;IAClC8P,KAAK,EAAE,OAAO;IACd0H,qBAAqB,EAAE,0BAA0B;IACjDC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE,WAAW;IACrBC,UAAU,EAAE,2CAA2C;IACvDC,QAAQ,EAAE,UAAU;IACpBxB,MAAM,EAAE,SAAS;IACjB7U,kBAAkB,EAAE,4EAA4E;IAChGR,eAAe,EAAE,YAAY;IAC7B8W,MAAM,EAAE,QAAQ;IAChB7W,KAAK,EAAE,QAAQ;IACf8W,eAAe,EAAE,QAAQ;IACzB7W,iBAAiB,EAAE,wBAAwB;IAC3CC,gBAAgB,EAAE,kBAAkB;IACpC6W,QAAQ,EAAE;MACN,CAAC,EAAE,8FAA8F;MACjG,CAAC,EAAE,8FAA8F;MACjG,CAAC,EAAE,yEAAyE;MAC5E,CAAC,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE,mCAAmC;IAC7CC,QAAQ,EAAE,mBAAmB;IAC7BC,kBAAkB,EAAE;MAChB,CAAC,EAAE,0EAA0E;MAC7E,CAAC,EAAE;IACP,CAAC;IACDC,cAAc,EAAE,QAAQ;IACxBnf,QAAQ,EAAE,KAAK;IACfof,cAAc,EAAE,MAAM;IACtBC,qBAAqB,EAAE,eAAe;IACtCC,kBAAkB,EAAE,UAAU;IAC9BC,oBAAoB,EAAE,sBAAsB;IAC5CC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnB3Q,YAAY,EAAE,MAAM;IACpB4Q,SAAS,EAAE,YAAY;IACvBC,mBAAmB,EAAE,UAAU;IAC/BC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,IAAI;IACXnf,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAE,WAAW;IACtBwE,QAAQ,EAAE,iBAAiB;IAC3B2a,IAAI,EAAE,IAAI;IACVtf,IAAI,EAAE,IAAI;IACVuf,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,MAAM;IACpBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE;EACX,CAAC;EACDvd,SAAS,EAAE;IACPR,KAAK,EAAE,UAAU;IACjBge,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;IAChCC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,MAAM;IACnBjjB,MAAM,EAAE,IAAI;IACZkjB,iBAAiB,EAAE,SAAS;IAC5BC,KAAK,EAAE;MACHpe,KAAK,EAAE,2BAA2B;MAClCqe,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAE;IAClB,CAAC;IACDpK,KAAK,EAAE;MACHqK,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,CAAC,wBAAwB,EAAE,UAAU,CAAC;MAClDC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,oBAAoB;MAChCC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACjDC,UAAU,EAAE,CAAC,8BAA8B,EAAE,6BAA6B;IAC9E,CAAC;IACDxK,KAAK,EAAE;MACHtU,KAAK,EAAE,CAAC,qBAAqB,EAAE,kCAAkC,CAAC;MAClEqU,UAAU,EAAE,OAAO;MACnB0K,aAAa,EAAE,UAAU;MACzBC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,oBAAoB,EAAE;IAClBjf,KAAK,EAAE,OAAO;IACdkf,OAAO,EAAE,kDAAkD;IAC3D1I,GAAG,EAAE,kCAAkC;IACvC2I,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,MAAM;IACdnkB,MAAM,EAAE;EACZ,CAAC;EACDokB,sBAAsB,EAAE;IACpBC,SAAS,EAAE;MACPtf,KAAK,EAAE,MAAM;MACbwW,GAAG,EAAE,iCAAiC;MACtC+I,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,cAAc;MACrBnL,IAAI,EAAE,MAAM;MACZoL,IAAI,EAAE,+BAA+B;MACrCC,IAAI,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;MACThgB,KAAK,EAAE,UAAU;MACjBigB,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;MACrDC,IAAI,EAAE,CAAC,MAAM,EAAE,oBAAoB,CAAC;MACpCC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC9C,CAAC;IACDvR,OAAO,EAAE,IAAI;IACb3T,MAAM,EAAE;EACZ,CAAC;EACDmlB,aAAa,EAAE;IACXpgB,KAAK,EAAE,QAAQ;IACfqgB,IAAI,EAAE,wDAAwD;IAC9DC,IAAI,EAAE,8BAA8B;IACpCC,aAAa,EAAE;EACnB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}