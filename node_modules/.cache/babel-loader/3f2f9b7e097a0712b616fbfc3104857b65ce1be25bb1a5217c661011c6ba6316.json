{"ast": null, "code": "// الترجمة للغة العربية، حالياً تعمل فقط عند فتح الروابط القصيرة عبر الجوال\nimport utils from './module/utils/utils-ar.js';\nimport components from './module/components/components-ar.js';\nimport console from './module/console/console-ar.js';\nimport docList from './module/docList/docList-ar.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-ar.js';\nimport entAuth from './module/entAuth/entAuth-ar.js';\nimport docTranslation from './module/docTranslation/docTranslation-ar.js';\nimport consts from '@/lang/module/consts/ar';\nexport default {\n  ...utils,\n  ...components,\n  ...docTranslation,\n  fileService: 'أرشيف+',\n  createSuccessful: 'تم الإنشاء بنجاح',\n  setLabel: 'تعيين التصنيف',\n  footerAd: {\n    title: 'تنبيه انتقال الصفحة',\n    content1: 'أنت على وشك زيارة صفحة ترويجية تابعة لطرف ثالث',\n    content2: 'هل تريد المتابعة؟',\n    bankContent: 'ستدخل قريباً إلى صفحة تعريف قروض الشركات \"قرض سهل\" من بنك نينغبو',\n    bankTip1: 'السماح لبنك نينغبو بالاتصال بي هاتفياً',\n    bankTip2: 'إرسال رسالة نصية لي تشرح كيفية التقديم',\n    bankFooter: 'إضافة خدمة عملاء بنك نينغبو المخصصة للخدمة الشخصية',\n    cancel: 'إلغاء',\n    continue: 'متابعة'\n  },\n  commonFooter: {\n    record: 'رقم سجل ICP الرئيسي: تشجيانغ ICP رقم ********',\n    hubbleRecordId: 'سجل شبكة المعلومات: 330106973391501230011',\n    openPlatform: 'منصة مفتوحة',\n    aboutBestSign: 'عن بست ساين',\n    contact: 'اتصل بنا',\n    recruitment: 'التوظيف',\n    help: 'مركز المساعدة',\n    copyright: 'حقوق النشر',\n    company: 'هانغتشو بست ساين المحدودة',\n    ssqLogo: 'شعار بست ساين في الشريط السفلي',\n    provideTip: 'خدمة التوقيع الإلكتروني مقدمة من',\n    ssq: ' بست ساين',\n    provide: '',\n    signHotline: 'الخط الساخن للخدمة',\n    langSwitch: 'اللغة'\n  },\n  login: {\n    pswLogin: 'تسجيل الدخول بكلمة المرور',\n    usePswLogin: 'تسجيل الدخول باستخدام كلمة المرور',\n    verifyLogin: 'رمز التحقق',\n    useVerifyLogin: 'تسجيل الدخول برمز التحقق',\n    scanLogin: 'تسجيل الدخول بالمسح الضوئي',\n    scanFailure: 'انتهت صلاحية رمز QR، يرجى المحاولة مرة أخرى',\n    scanSuccess: 'تم المسح بنجاح',\n    scanLoginTip: 'يرجى استخدام التطبيق للمسح وتسجيل الدخول',\n    appLoginTip: 'يرجى النقر على تسجيل الدخول في تطبيق بست ساين',\n    downloadApp: 'تنزيل تطبيق بست ساين',\n    forgetPsw: 'نسيت؟',\n    login: 'تسجيل الدخول',\n    noAccount: 'ليس لديك حساب؟',\n    registerNow: 'التسجيل الآن',\n    accountPlaceholder: 'رقم الهاتف أو البريد الإلكتروني',\n    passwordPlaceholder: 'كلمة المرور',\n    pictureVer: 'يرجى ملء المحتوى في الصورة',\n    verifyCodePlaceholder: 'يرجى إدخال رمز مكون من 6 أرقام',\n    getVerifyCode: 'إرسال',\n    noRegister: 'غير مسجل بعد',\n    or: 'أو',\n    errAccountOrPwdTip: 'كلمة المرور التي أدخلتها لا تتطابق مع رقم الحساب؟',\n    errAccountOrPwdTip2: 'كلمة المرور التي أدخلتها لا تتطابق مع الحساب',\n    errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',\n    errPwd: 'يرجى إدخال كلمة المرور الصحيحة!',\n    verCodeFormatErr: 'خطأ في رمز التحقق',\n    grapVerCodeErr: 'خطأ في رمز التحقق الرسومي',\n    grapVerCodeFormatErr: 'خطأ في تنسيق رمز التحقق الرسومي',\n    lackAccount: 'يرجى إدخال الحساب أولاً',\n    lackGrapCode: 'يرجى ملء رمز التحقق الرسومي أولاً.',\n    getVerCodeTip: 'يرجى الحصول على رمز التحقق',\n    loginView: 'تسجيل الدخول وعرض العقد',\n    regView: 'التسجيل وعرض العقد',\n    takeViewBtn: 'تسجيل الدخول والتوقيع',\n    resendCode: 'إعادة الإرسال',\n    regTip: 'بعد ملء رمز التحقق الصحيح، ستقوم بست ساين بإنشاء حساب لك',\n    haveRead: 'لقد قرأت ووافقت على',\n    bestsignAgreement: 'اتفاقية خدمة بست ساين',\n    and: 'و',\n    digitalCertificateAgreement: 'اتفاقية استخدام الشهادة الرقمية',\n    privacyPolicy: 'سياسة الخصوصية',\n    sendSuc: 'تم الإرسال بنجاح',\n    lackVerCode: 'يرجى إدخال رمز التحقق أولاً',\n    lackPsw: 'يرجى إدخال كلمة المرور أولاً',\n    notMatch: 'كلمة المرور والحساب المدخلان غير متطابقين',\n    cookieTip: 'غير قادر على قراءة وكتابة ملفات تعريف الارتباط، يرجى التحقق من عدم وجود وضع التصفح المتخفي أو تعطيل ملفات تعريف الارتباط',\n    wrongLink: 'رابط غير صالح',\n    footerTips: 'خدمة التوقيع الإلكتروني مقدمة من <span>بست ساين</span>',\n    bestSign: 'بست ساين',\n    bestSignDescription: 'رائد في صناعة التعاقدات الإلكترونية',\n    /** نسيت كلمة المرور /forgotPassword start */\n    forgetPswStep: 'التحقق من الحساب المسجل | إعادة تعيين كلمة المرور',\n    pictureVerCodeInput: 'رمز التحقق الرسومي | يرجى ملء محتويات الصورة',\n    accountInput: 'الحساب | يرجى إدخال حسابك',\n    smsCodeInput: 'رمز التحقق | الحصول على رمز التحقق',\n    haveRegistereLoginNow: 'تم التسجيل بالفعل، | تسجيل الدخول الآن',\n    nextStep: 'التالي | إرسال',\n    setNewPasswordInput: 'تعيين كلمة مرور جديدة | 6-18 رقم أو حرف',\n    passwordResetSucceeded: 'تمت إعادة تعيين كلمة المرور بنجاح',\n    /** نسيت كلمة المرور /forgotPassword end */\n    accountNotRegistered: 'الحساب غير مسجل',\n    loginAndDownload: 'تسجيل الدخول وتنزيل العقد',\n    registerAndDownload: 'التسجيل وتنزيل العقد',\n    inputPhone: 'يرجى إدخال رقم الهاتف',\n    readContract: 'قراءة العقد',\n    errorPhone: 'خطأ في تنسيق رقم الهاتف المحمول',\n    companyCert: 'إجراء تصديق الشركة',\n    regAndCompanyCert: 'التسجيل وإجراء تصديق الشركة'\n  },\n  ...sign,\n  handwrite: {\n    title: 'مباشرة على الشاشة',\n    picSubmitTip: 'تم تقديم صورة التوقيع بنجاح',\n    settingDefault: 'تعيين كافتراضي',\n    replaceAllSignature: 'استخدام لجميع التوقيعات',\n    replaceAllSeal: 'لجميع الأختام',\n    canUseSeal: 'أختامي',\n    applyForSeal: 'تقديم طلب لاستخدام الأختام',\n    moreTip: 'سيتم حفظ توقيعك اليدوي كتوقيع افتراضي، ويستخدم فقط لتوقيع العقود. مسار الإدارة: [مركز المستخدم <- إدارة التوقيعات]',\n    uploadPic: 'تحميل صورة',\n    use: 'استخدام',\n    clickExtend: 'انقر على السهم الأيمن لتوسيع المنطقة',\n    rewrite: 'إعادة الكتابة',\n    upload: 'تحميل صورة التوقيع',\n    uploadTip1: 'تنبيه: عند تحميل صورة التوقيع، يرجى ملء الصورة بالكامل بالتوقيع',\n    uploadTip2: 'يرجى استخدام ألوان داكنة أو نص أسود خالص للتوقيع.',\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    upgradeBrowser: 'المتصفح لا يدعم الكتابة اليدوية، يرجى ترقية متصفحك.',\n    submitTip: 'تم تقديم التوقيع اليدوي بنجاح',\n    needWrite: 'يرجى كتابة اسمك أولاً',\n    needRewrite: 'لا يمكن التعرف على التوقيع، يرجى المحاولة مرة أخرى',\n    title2: 'رسم توقيعك باليد',\n    QRCode: 'عن طريق مسح رمز QR',\n    ok: 'موافق',\n    clearTips: 'يرجى كتابة توقيع واضح يمكن التعرف عليه',\n    isBlank: 'اللوحة فارغة، يرجى رسم التوقيع يدوياً قبل الإرسال!',\n    success: 'تم تقديم التوقيع اليدوي بنجاح',\n    signNotMatch: 'يرجى كتابة توقيعك بأحرف كبيرة والحفاظ على توافقه مع معلومات هويتك الحقيقية.',\n    signNotMatchExact: 'الكلمة {numList} لا تتطابق، يرجى إعادة الكتابة',\n    msg: {\n      successToUser: 'تم تفعيل التوقيع الجديد، يرجى الانتقال إلى مركز المستخدم على الويب - إدارة التوقيع',\n      successToSign: 'تم تفعيل التوقيع الجديد، يرجى التحقق من صفحة توقيع العقد',\n      cantGet: 'لا يمكن الحصول على توقيع، جرب استخدام متصفح مختلف!'\n    }\n  },\n  common: {\n    aboutBestSign: 'عن بست ساين',\n    contact: 'اتصل بنا',\n    recruitment: 'التوظيف',\n    copyright: 'حقوق النشر',\n    advice: 'نصائح',\n    notEmpty: 'لا يمكن أن يكون فارغاً',\n    enter6to18n: 'يرجى إدخال 6-18 رقماً وحرفاً',\n    ssqDes: 'رائد في منصات التوقيع الإلكتروني السحابية',\n    openPlatform: 'منصة مفتوحة',\n    company: 'هانغتشو بست ساين المحدودة',\n    help: 'مركز المساعدة',\n    errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',\n    verCodeFormatErr: 'خطأ في رمز التحقق',\n    signPwdType: 'يرجى إدخال 6 أرقام',\n    enterActualEntName: 'يرجى إدخال اسم العمل الحقيقي',\n    enterCorrectName: 'يرجى إدخال اسم العمل الصحيح',\n    enterCorrectPhoneNum: 'يرجى إدخال رقم الهاتف المحمول الصحيح',\n    enterCorrectEmail: 'يرجى إدخال البريد الإلكتروني الصحيح',\n    imgCodeErr: 'خطأ في رمز التحقق',\n    enterCorrectIdNum: 'يرجى إدخال رقم الهوية الصحيح',\n    enterCorrectFormat: 'يرجى إدخال التنسيق الصحيح',\n    enterCorrectDateFormat: 'يرجى إدخال تنسيق التاريخ الصحيح'\n  },\n  entAuth: {\n    ...entAuth,\n    entCertification: 'تصديق الاسم الحقيقي للمؤسسة',\n    subBaseInfo: 'تقديم المعلومات الأساسية',\n    corDocuments: 'وثائق الشركة',\n    license: 'الترخيص',\n    upload: 'انقر للتحميل',\n    uploadLimit: 'الصور متاحة فقط بتنسيقات jpeg وjpg وpng وحجم لا يزيد عن 10 ميجابايت',\n    hi: 'مرحباً',\n    exit: 'خروج',\n    help: 'مساعدة',\n    hotline: 'الخط الساخن للخدمة',\n    acceptProtectingMethod: 'أقبل طريقة حماية المعلومات الشخصية التي أقدمها',\n    comfirmSubmit: 'تأكيد الإرسال',\n    cerficated: 'اكتمل التصديق',\n    entName: 'اسم المؤسسة',\n    serialNumber: 'الرقم التسلسلي',\n    validity: 'الصلاحية',\n    nationalNo: 'رقم التسجيل الوطني',\n    corporationName: 'اسم الممثل القانوني',\n    city: 'المدينة',\n    entCertificate: 'شهادة الاسم الحقيقي للمؤسسة',\n    certificationAuthority: 'هيئة التصديق',\n    bestsignPlatform: 'منصة بست ساين السحابية للتوقيع الإلكتروني',\n    notIssued: 'لم يتم إصدارها',\n    date: '{year}-{month}-{day}',\n    congratulations: 'تهانينا على إتمام عملية تصديق الاسم الحقيقي للمؤسسة بنجاح',\n    continue: 'متابعة',\n    rejectMessage: 'للأسباب التالية، لم يتم اجتياز تدقيق البيانات، يرجى التحقق',\n    recertification: 'إعادة التصديق',\n    waitMessage: 'ستتم المراجعة من قبل خدمة العملاء خلال يوم عمل واحد، يرجى الانتظار'\n  },\n  personalAuth: {\n    info: 'معلومات',\n    submitPicError: 'يرجى تحميل الصورة قبل الاستخدام'\n  },\n  home: {\n    ...home,\n    home: 'الرئيسية',\n    contractDrafting: 'صياغة العقود',\n    contractManagement: 'العقود',\n    userCenter: 'الإدارة',\n    service: 'الخدمة',\n    enterpriseConsole: 'لوحة تحكم المؤسسة',\n    groupConsole: 'لوحة تحكم المجموعة',\n    startSigning: 'بدء التوقيع',\n    contractType: 'عقد عادي | عقد قالب',\n    sendContract: 'ابدأ الآن',\n    shortcuts: 'الاختصارات | لا توجد اختصارات لأي مستندات',\n    setting: 'الإعداد فوراً | إنشاء المزيد من الاختصارات',\n    signNum: 'ملخص شهري للتوقيعات والتسليمات',\n    contractNum: 'العقود المرسلة | العقود الموقعة',\n    contractInFormation: 'لم ترسل أو توقع أي عقود هذا الشهر',\n    type: 'شركة | شخص ',\n    basicInformation: 'معلومات أساسية',\n    more: 'المزيد',\n    certified: 'مصدق | غير مصدق',\n    account: 'الحساب',\n    time: 'وقت الإنشاء | وقت التسجيل',\n    day: 'يوم | شهر',\n    sendContractNum: 'التسليمات | التوقيعات',\n    num: '',\n    realName: 'إنشاء حساب أعمال باسم حقيقي الآن | إنشاء حساب فردي باسم حقيقي الآن',\n    update: 'تحديث المنتج',\n    mark: 'هل ترغب في التوصية ببست ساين لأصدقائك وزملائك؟ يرجى تقييم اختيارك من 0 إلى 10.',\n    countDes: {\n      1: 'متاح: لعقد المؤسسة',\n      2: '',\n      3: 'للعقد الخاص',\n      4: ''\n    },\n    chargeNow: 'الشحن الآن',\n    myRechargeOrder: 'طلب إعادة الشحن الخاص بي',\n    statusTip: {\n      1: 'بحاجة إلى تشغيلي',\n      2: 'يحتاج الآخرون إلى التوقيع',\n      3: 'التوقيع على وشك الإغلاق',\n      4: 'اكتمل التوقيع'\n    },\n    useTemplate: 'استخدام القالب',\n    useLocalFile: 'تحميل ملف محلي'\n  },\n  docDetail: {\n    canNotOperateTip: 'غير قادر على {operate} العقد',\n    shareSignLink: 'مشاركة رابط التوقيع',\n    faceSign: 'التوقيع بمسح الوجه',\n    faceFirstVerifyCodeSecond: 'التوقيع ذو الأولوية بالتحقق من الوجه، التوقيع البديل بالتحقق من رمز الرسائل القصيرة',\n    contractRecipient: 'مستلم العقد',\n    personalOperateLog: 'سجل عمليات العقد الفردي',\n    recordDialog: {\n      date: 'التاريخ',\n      user: 'المستخدم',\n      operate: 'العملية',\n      view: 'عرض',\n      download: 'تنزيل'\n    },\n    remarks: 'ملاحظات',\n    operateRecords: 'سجل العمليات',\n    borrowingRecords: 'سجلات الاقتراض',\n    currentHolder: 'المالك الحالي',\n    currentEnterprise: 'الشركة تحت الحساب الحالي',\n    companyInterOperationLog: 'سجل العمليات الداخلية للشركة',\n    receiverMap: {\n      sender: 'مرسل العقد',\n      signer: 'مستلم العقد',\n      ccUser: 'العقد منسوخ إلى'\n    },\n    downloadCode: 'رمز تنزيل العقد',\n    noTagToAddHint: 'لا توجد علامات بعد؛ يرجى الذهاب إلى لوحة التحكم التجارية لإضافتها',\n    requireFieldNotAllowEmpty: 'لا يمكن أن تكون الحقول المطلوبة فارغة',\n    modifySuccess: 'تم التعديل بنجاح',\n    uncategorized: 'غير مصنف',\n    notAllowModifyContractType: 'لا يُسمح بتعديل نوع العقد للعقد تحت {type}',\n    setTag: 'تعيين العلامات',\n    contractTag: 'علامات العقد',\n    plsInput: 'يرجى الإدخال',\n    plsInputCompanyInternalNum: 'يرجى إدخال رقم الأعمال الداخلي',\n    companyInternalNum: 'رقم الأعمال الداخلي',\n    none: 'لا شيء',\n    plsSelect: 'يرجى الاختيار',\n    modify: 'تعديل',\n    contractDetailInfo: 'تفاصيل العقد',\n    slideContentTip: {\n      signNotice: 'تعليمات التوقيع',\n      contractAncillaryInformation: 'مرفقات العقد',\n      content: 'المحتوى',\n      document: 'المستند'\n    },\n    downloadDepositConfirmTip: {\n      title: 'صفحة إثبات التوقيع التي قمت بتنزيلها هي نسخة غير حساسة، مع إخفاء المعلومات الخاصة وغير قابلة للاستخدام في إجراءات المحكمة. إذا كنت بحاجة إلى استخدامها في إجراءات المحكمة، يرجى الاتصال بنا للحصول على النسخة الكاملة.',\n      hint: 'نصائح',\n      confrim: 'متابعة التنزيل',\n      cancel: 'إلغاء'\n    },\n    downloadTip: {\n      title: 'نظراً لأن العقد لم يكتمل بعد، فإنك تقوم بتنزيل ملف معاينة للعقد الذي لم يدخل حيز التنفيذ بعد',\n      hint: 'نصائح',\n      confirm: 'تأكيد',\n      cancel: 'إلغاء'\n    },\n    transferSuccessGoManagePage: 'تم النقل بنجاح وسيتم العودة إلى صفحة إدارة العقود',\n    claimSign: 'استرداد وتوقيع',\n    downloadDepositPageTip: 'تنزيل صفحة إثبات التوقيع (نسخة غير حساسة)',\n    resend: 'إعادة الإرسال',\n    proxySign: 'توقيع مفوض',\n    notPassed: 'مرفوض',\n    approving: 'قيد المراجعة',\n    signning: 'التوقيع قيد التقدم',\n    notarized: 'موثق',\n    currentFolder: 'المجلد الحالي',\n    archive: 'تم أرشفة العقد',\n    deadlineForSigning: 'الموعد النهائي للتوقيع',\n    endFinishTime: 'اكتمل التوقيع/تاريخ الإتمام',\n    contractImportTime: 'وقت استيراد العقد',\n    contractSendTime: 'وقت تسليم العقد',\n    back: 'رجوع',\n    contractInfo: 'معلومات العقد',\n    basicInfo: 'معلومات أساسية',\n    contractNum: 'رقم العقد',\n    sender: 'المرسل',\n    personAccount: 'حساب شخصي',\n    entAccount: 'حساب مؤسسة',\n    operator: 'المشغل',\n    signStartTime: 'وقت بدء التوقيع',\n    signDeadline: 'الموعد النهائي للتوقيع',\n    contractExpireDate: 'تاريخ انتهاء صلاحية العقد',\n    // none: 'لا شيء',\n    edit: 'تعديل',\n    settings: 'إعداد',\n    from: 'المصدر',\n    folder: 'مجلد',\n    contractType: 'نوع العقد',\n    reason: 'السبب',\n    sign: 'توقيع',\n    approval: 'موافقة',\n    viewAttach: 'عرض الصفحات المرفقة',\n    downloadContract: 'تنزيل العقد',\n    downloadAttach: 'تنزيل الصفحة المرفقة',\n    print: 'طباعة',\n    certificatedTooltip: 'تم توثيق العقد والأدلة ذات الصلة في السلسلة القضائية لمحكمة هانغتشو للإنترنت',\n    needMeSign: 'بحاجة إلى توقيعي',\n    needMeApproval: 'بحاجة إلى موافقتي',\n    inApproval: 'قيد النظر...',\n    needOthersSign: 'بحاجة إلى توقيع الآخرين',\n    signComplete: 'اكتمل التوقيع',\n    signOverdue: 'توقيع متأخر',\n    rejected: 'مرفوض',\n    revoked: 'ملغى',\n    contractCompleteTime: 'وقت اكتمال التوقيع',\n    contractEndTime: 'وقت انتهاء التوقيع',\n    reject: 'رفض',\n    revoke: 'إلغاء',\n    download: 'تنزيل',\n    viewSignOrders: 'عرض ترتيب التوقيع',\n    viewApprovalProcess: 'عرض عملية الموافقة',\n    completed: 'اكتمل التوقيع',\n    cc: 'نسخة',\n    ccer: 'طرف النسخة',\n    signer: 'الموقع',\n    signSubject: 'موضوع التوقيع',\n    signSubjectTooltip: 'موضوع التوقيع الذي ملأه المرسل هو',\n    user: 'المستخدم',\n    IDNumber: 'رقم الهوية',\n    state: 'الحالة',\n    time: 'الوقت',\n    notice: 'تذكير',\n    detail: 'التفاصيل',\n    RealNameCertificationRequired: 'مطلوب التصديق بالاسم الحقيقي',\n    RealNameCertificationNotRequired: 'لا يلزم التصديق بالاسم الحقيقي',\n    MustHandwrittenSignature: 'يجب التوقيع بخط اليد',\n    handWritingRecognition: 'تفعيل التعرف على الكتابة اليدوية',\n    privateMessage: 'رسالة',\n    attachment: 'مرفق',\n    rejectReason: 'السبب',\n    notSigned: 'غير موقع',\n    notViewed: 'غير مطلع عليه',\n    viewed: 'تم الاطلاع',\n    signed: 'تم التوقيع',\n    viewedNotSigned: 'قرئ ولم يتم التوقيع',\n    notApproval: 'غير معتمد',\n    remindSucceed: 'تم إرسال رسالة التذكير',\n    reviewDetails: 'تفاصيل الموافقة',\n    close: 'إغلاق',\n    entInnerOperateDetail: 'تفاصيل العمليات الداخلية',\n    approve: 'موافقة',\n    disapprove: 'رفض',\n    applySeal: 'طلب الختم',\n    applied: 'تم التقديم بالفعل',\n    apply: 'تقديم',\n    toOtherSign: 'نقل إلى شخص آخر للتوقيع',\n    handOver: 'نقل',\n    approvalOpinions: 'تعليقات الموافقة',\n    useSeal: 'ختم',\n    signature: 'توقيع',\n    use: 'استخدام',\n    date: 'تاريخ',\n    fill: 'ملء',\n    times: 'ثانوي',\n    place: 'مكان',\n    contractDetail: 'تفاصيل العقد',\n    viewMore: 'عرض المزيد',\n    collapse: 'طي',\n    signLink: 'رابط التوقيع',\n    saveQRCode: 'احفظ رمز QR أو انسخ الرابط وشاركه مع الموقع',\n    signQRCode: 'رمز QR لرابط التوقيع',\n    copy: 'نسخ',\n    copySucc: 'تم النسخ بنجاح',\n    copyFail: 'فشل النسخ',\n    certified: 'مصدق',\n    unCertified: 'غير مصدق',\n    claimed: 'تم المطالبة'\n  },\n  uploadFile: {\n    thumbnails: 'صورة مصغرة',\n    isUploading: 'جاري التحميل',\n    move: 'نقل',\n    delete: 'حذف',\n    replace: 'استبدال',\n    tip: 'نصيحة',\n    understand: 'فهمت',\n    totalPages: '{page} في المجموع',\n    uploadFile: 'تحميل ملف محلي',\n    matchErr: 'يوجد خلل بسيط في الخادم، يرجى المحاولة مرة أخرى لاحقاً.',\n    inUploadingDeleteErr: 'يرجى الحذف بعد التحميل',\n    timeOutErr: 'انتهت مهلة الطلب',\n    imgUnqualified: 'تنسيق الصورة لا يلبي المتطلبات',\n    imgBiggerThan20M: 'لا يمكن أن يتجاوز حجم الصورة 20 ميجابايت!',\n    error: 'خطأ',\n    hasCATip: 'ملف PDF الذي قمت بتحميله يحتوي بالفعل على شهادة رقمية، وهذا سيؤثر على توحيد وتكامل سلسلة أدلة توقيع العقد، لا يُنصح المستخدمون الشخصيون باستخدامه. يرجى تحميل ملف PDF لا يحتوي على أي شهادة رقمية كملف عقد.'\n  },\n  contractInfo: {\n    internalNumber: 'رقم العمل الداخلي',\n    contractName: 'اسم العقد',\n    contractNameTooltip: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة وألا يتجاوز 100 كلمة',\n    contractType: 'نوع العقد',\n    toSelect: 'يرجى الاختيار',\n    contractTypeErr: 'تم حذف نوع العقد الحالي. يرجى إعادة اختيار نوع العقد.',\n    signDeadLine: 'الموعد النهائي للتوقيع',\n    signDeadLineTooltip: 'إذا لم يتم توقيع العقد قبل هذا التاريخ، فلا يمكن متابعته',\n    selectDate: 'اختر التاريخ والوقت',\n    contractExpireDate: 'تاريخ انتهاء العقد',\n    expireDateTooltip: 'وقت انتهاء الصلاحية في محتويات العقد لإدارة العقد اللاحقة',\n    necessary: 'ضروري',\n    notNecessary: 'اختياري',\n    dateTips: 'تم تحديد تاريخ انتهاء العقد تلقائيًا لك، يرجى التأكيد',\n    contractTitleErr: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة',\n    contractTitleLengthErr: 'يرجى ألا يتجاوز طول اسم العقد 100 كلمة.'\n  },\n  template: {\n    templateList: {\n      linkBoxTip: 'معرف الخزانة المرتبطة:'\n    },\n    dynamicTemplateUpdate: {\n      title: 'تم إطلاق وظيفة جديدة للقوالب الديناميكية',\n      newVersionDesc: 'تدعم الوظيفة الجديدة عرض الرأس والتذييل وتحافظ على تخطيط صفحة المستند إلى أقصى درجة.',\n      updateTip: 'لا تتم مزامنة ميزة القالب الديناميكي السابقة وتوافقها. مطلوب الترقية اليدوية. إذا تم تحرير القوالب الديناميكية التي تم إنشاؤها قبل 26 يناير، فلن يتم حفظ العقود أو إرسالها. يمكن إرسال العقود قبل 1 مارس 2021 إذا لم يتم تحرير القوالب. يُوصَى بالترقية في أقرب وقت ممكن. القوالب غير الديناميكية غير متأثرة.',\n      connectUs: 'إذا كان لديك أي أسئلة، يرجى الاتصال عبر الخط الساخن 400-993-6665 أو الاتصال بخدمة العملاء عبر الإنترنت.'\n    },\n    sendCode: {\n      tip: 'إعدادات القالب الحالية لا تلبي شروط إنشاء رمز الإرسال. تحقق مما إذا كانت المتطلبات التالية مستوفاة:',\n      fail: {\n        1: 'لا تتضمن مستندات فارغة',\n        2: 'الطرف المتعاقد لديه طرف متغير واحد فقط (بما في ذلك التوقيع والنسخ)، ويجب أن يكون الطرف المتغير هو المشغل الأول؛ يجب أن يكون للموقع موضع ختم التوقيع',\n        3: 'الحساب الثابت للطرف المتعاقد لا يجب أن يكون فارغًا',\n        4: 'لا يؤدي إلى تفعيل الموافقة قبل الإرسال',\n        5: 'المحتويات التي يجب على المرسل ملؤها ليست فارغة (بما في ذلك حقل الوصف وحقل محتوى العقد)',\n        6: 'ليس مزيجًا من القوالب'\n      }\n    },\n    sendCodeGuide: {\n      title: 'شرح وظائف رمز الإرسال المتقدمة',\n      info: 'رمز إرسال القالب مناسب لملفات العقود التي لا تتطلب من المرسل ملء المحتوى، مثل شهادات الاستقالة واتفاقيات السرية وخطابات التفويض، ويمكن توفيرها لأي شخص يقوم بمسح الرمز للحصول عليها. إذا كان هناك معلومات يجب على المرسل ملؤها في المستند، أو إذا كان المرسل بحاجة إلى تحديد نطاق الأطراف المقابلة التي يمكنها الحصول على المستند عبر مسح الرمز، يمكنك استخدام الوظائف المتقدمة لـ \"أرشيف+\". من خلال مسح رمز QR الخاص بخزانة الملفات، يمكنك إكمال إرسال العقد تلقائيًا إلى أشخاص محددين، وملء المحتوى الذي يحتاج المرسل إلى ملئه، وحتى التحكم في نقاط زمنية للإرسال التلقائي؛ كما يتم تخزين الطرف المقابل الذي مسح الرمز في خزانة الملفات، ويمكن الاستعلام عنه في أي وقت. طريقة الاستخدام كالتالي:',\n      tip1: {\n        main: '1. BestSign',\n        sub: '',\n        line1: 'تقديم طلب إلى BestSign لتفعيل أرشيف+، مراجعة العقد المسبقة، المراجعة الذكية',\n        line2: 'بعد التفعيل يمكنك الذهاب إلى القوائم المقابلة للعمليات والاستخدام'\n      },\n      tip2: {\n        main: '2. مدير خزانة الملفات',\n        sub: 'إنشاء خزانة ملفات، تكوين المراجعة الذكية',\n        line1: '',\n        line2: 'قم بإنشاء حقول إدخال في خزانة الملفات تتطابق مع محتوى العقد، وربط قوالب العقد، وإعداد العلاقات وشروط الإرسال التلقائي عند مسح الرمز، وتقديم رمز QR لخزانة الملفات إلى الطرف المقابل للتوقيع.'\n      },\n      tip3: {\n        main: '3. طرف التوقيع',\n        sub: 'مسح الرمز لملء المعلومات، والحصول على ملفات العقد',\n        line1: '',\n        line2: 'يقوم طرف التوقيع بمسح رمز إرسال خزانة الملفات الذي يوفره المرسل للملء، وسيتم أرشفة المعلومات التي تم جمعها عبر خزانة الملفات وملؤها تلقائيًا في العقد، ويحتاج الطرف المقابل فقط إلى الختم أو التوقيع البسيط عند استلام العقد لإكمال توقيع العقد'\n      },\n      tip4: {\n        main: '4. مدير خزانة الملفات',\n        sub: '',\n        line1: 'عرض معلومات الطرف المقابل للتوقيع وحالة العقود المرسلة',\n        line2: 'يمكن لمدير الجهة المرسلة الذهاب إلى خزانة الملفات لعرض معلومات الطرف المقابل الذي قام بمسح الرمز، وحالة إرسال العقد، وما إذا كان قد تم إكمال التوقيع، وما إلى ذلك'\n      }\n    }\n  },\n  style: {\n    signature: {\n      text: {\n        x: '0',\n        fontSize: '18'\n      }\n    }\n  },\n  resetPwd: {\n    title: 'تنبيهات الأمان!',\n    notice: 'عامل الأمان لكلمة المرور منخفض ويوجد خطر أمني. يرجى إعادة تعيين كلمة المرور',\n    oldLabel: 'كلمة المرور الأصلية',\n    oldPlaceholder: 'يرجى إدخال كلمة المرور الأصلية',\n    newLabel: 'كلمة المرور الجديدة',\n    newPlaceholder: '6-18 رقمًا وأحرف كبيرة وصغيرة، تدعم الرموز الخاصة',\n    submit: 'إرسال',\n    errorMsg: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',\n    oldRule: 'لا يمكن أن تكون كلمة المرور الأصلية فارغة',\n    newRule: 'لا يمكن أن تكون كلمة المرور الجديدة فارغة',\n    success: 'نجاح!'\n  },\n  personAuthIntercept: {\n    title: 'ندعوك بـ',\n    name: 'الاسم:',\n    id: 'رقم الهوية:',\n    descNoAuth: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n    desMore: 'وفقًا لمتطلبات المبادر، تحتاج أيضًا إلى استكمال',\n    descNoSame: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمبادر للتأكد وإعادة إنشاء العقد.',\n    descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n    descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n    tips: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n    goOn: 'أنا الشخص نفسه، ابدأ المصادقة',\n    goMore: 'اذهب لاستكمال المصادقة',\n    descNoSame1: ' هوية لتوقيع العقد',\n    descNoSame2: 'هذا لا يتطابق مع معلومات الاسم الحقيقي المكتملة للحساب الذي قمت بتسجيل الدخول إليه حاليًا.',\n    goHome: 'العودة إلى صفحة قائمة العقود>>',\n    authInfo: 'تم اكتشاف أن هوية الاسم الحقيقي لحسابك الحالي هي ',\n    in: 'في',\n    finishAuth: 'أكمل مصادقة الاسم الحقيقي لتوقيع العقود بشكل متوافق',\n    ask: 'هل رقم الهاتف المحمول الحالي هو رقم هاتفك المعتاد؟',\n    reAuthBtnText: 'نعم، أريد استخدام هذا الحساب لإعادة المصادقة والتوقيع',\n    changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',\n    changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',\n    changePhoneTip2: '، لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد أنك الموقع.',\n    confirmReject: 'نعم، أريد رفض المصادقة باسم حقيقي'\n  },\n  authIntercept: {\n    title: 'يُطلب منك أن:',\n    name: 'الاسم:',\n    id: 'رقم الهوية:',\n    descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n    descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n    descNoSame1: 'لتوقيع العقد.',\n    descNoSame2: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمُرسل للتأكد وإعادة إنشاء العقد.',\n    tips: 'ملاحظة: يجب أن تتطابق معلومات الهوية تمامًا لتوقيع العقد',\n    goOn: 'أنا الشخص نفسه، ابدأ المصادقة',\n    goHome: 'فهمت',\n    goMore: 'اذهب لاستكمال المصادقة',\n    authTip: 'إجراء المصادقة باسم حقيقي.',\n    viewAndSign: 'بعد إكمال المصادقة، يمكنك عرض وتوقيع العقد',\n    tips2: 'ملاحظة: يجب أن يتطابق اسم الشركة تمامًا لعرض وتوقيع العقد.',\n    requestOtherAnth: 'طلب التحقق من قبل الآخرين',\n    goAuth: 'الذهاب للمصادقة باسم حقيقي',\n    requestSomeoneList: 'طلب من الأشخاص التاليين إكمال المصادقة باسم حقيقي:',\n    ent: 'شركة',\n    entName: 'اسم الشركة',\n    account: 'الحساب',\n    accountPH: 'هاتف أو بريد إلكتروني',\n    send: 'إرسال',\n    lackEntName: 'يرجى إدخال اسم الشركة',\n    errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',\n    successfulSent: 'تم الإرسال بنجاح'\n  },\n  thirdPartApprovalDialog: {\n    title1: 'موافقة قبل التوقيع',\n    title2: 'عملية الموافقة',\n    content1: 'يمكنك التوقيع فقط بعد الموافقة، يرجى الانتظار بصبر.',\n    content2: 'يتطلب الموافقة من منصة طرف ثالث (غير منصة BestSign).',\n    cancelBtnText: 'عرض عملية الموافقة',\n    confirmBtnText: 'تأكيد',\n    iKnow: 'فهمت'\n  },\n  endSignEarlyPrompt: {\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    signPrompt: 'إشعار التوقيع',\n    signTotalCountTip: 'يتضمن هذا التوقيع {count} ملفات عقود',\n    signatureTip: 'قام المرسل بتعيين {count} من أعضاء الشركة لتمثيل الشركة في التوقيع، حاليًا:',\n    hasSigned: '{count} أشخاص قاموا بالتوقيع',\n    hasNotSigned: '{count} أشخاص لم يقوموا بالتوقيع',\n    noNeedSealTip: 'بعد الانتهاء من الختم، لن يحتاج أعضاء الشركة الذين لم يوقعوا إلى التوقيع.'\n  },\n  commonNomal: {\n    yesterday: 'الأمس',\n    ssq: 'بيست ساين',\n    ssqPlatform: 'منصة بيست ساين للتوقيع الإلكتروني السحابية',\n    ssqTestPlatform: '(للأغراض التجريبية فقط) منصة بيست ساين للتوقيع الإلكتروني السحابية',\n    pageExpiredTip: 'انتهت صلاحية الصفحة، يرجى التحديث والمحاولة مرة أخرى',\n    pswCodeSimpleTip: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين'\n  },\n  transferAdminDialog: {\n    title: 'تأكيد الهوية',\n    transfer: 'نقل',\n    confirmAdmin: 'أنا المدير الرئيسي',\n    content: 'يتعين على مدير النظام الرئيسي أن يكون مسؤولاً عن إدارة أختام الشركة، وإدارة العقود وإدارة صلاحيات الموظفين الآخرين، وعادة ما ينتمي إلى الممثل القانوني للشركة، أو مدير الشؤون المالية، أو مدير الشؤون القانونية، أو مدير قسم تكنولوجيا المعلومات، أو المسؤول عن أعمال الشركة. | يرجى التأكد من أنك تستوفي الهوية المذكورة أعلاه، وإذا لم تكن كذلك، فمن المستحسن نقلها إلى الشخص المعني.'\n  },\n  choseBoxForReceiver: {\n    dataNeedForReceiver: 'المعلومات المطلوب تقديمها من طرف التوقيع',\n    dataFromDataBox: 'يجب الحصول على المعلومات المطلوب تقديمها من طرف التوقيع من خلال جمع المستندات من خزانة ملفات معينة.',\n    searchTp: 'يرجى إدخال اسم أو رمز خزانة الملفات.',\n    search: 'بحث',\n    boxNotFound: 'لا يمكن العثور على خزانة الملفات.',\n    cancel: 'إلغاء',\n    confirm: 'موافق'\n  },\n  localCommon: {\n    cancel: 'إلغاء',\n    confirm: 'تأكيد',\n    toSelect: 'يرجى الاختيار',\n    seal: 'ختم',\n    signature: 'توقيع',\n    signDate: 'تاريخ',\n    text: 'نص',\n    date: 'تاريخ',\n    qrCode: 'رمز الاستجابة السريعة',\n    number: 'رقمي',\n    dynamicTable: 'نماذج ديناميكية',\n    terms: 'شروط وأحكام العقد',\n    checkBox: 'خانات اختيار للخيارات المتعددة',\n    radioBox: 'خانة اختيار للخيارات الفردية',\n    image: 'صورة',\n    confirmSeal: 'ختم للاستعلامات',\n    tip: 'نصائح',\n    confirmRemark: 'ملاحظات بشأن الأختام التي لا تلبي المتطلبات',\n    optional: 'اختياري',\n    require: 'مطلوب',\n    comboBox: 'قائمة منسدلة'\n  },\n  twoFactor: {\n    signTip: 'إشعار التوقيع',\n    settingTwoFactor: 'إعداد أداة التحقق ثنائي العنصر',\n    step1: '1. تثبيت تطبيق التحقق',\n    step1Tip: 'يتطلب التحقق ثنائي العنصر تثبيت تطبيق الهاتف المحمول التالي:',\n    step2: '2.مسح رمز الاستجابة السريعة',\n    step2Tip1: 'استخدم أداة التحقق التي قمت بتنزيلها لمسح رمز الاستجابة السريعة أدناه (يرجى التأكد من أن الوقت على هاتفك يتطابق مع الوقت الحالي، وإلا فلن يمكن تنفيذ التحقق ثنائي العنصر).',\n    step2Tip2: 'سيتم عرض رمز التحقق المكون من 6 أرقام الذي يلزم للتحقق ثنائي العنصر على الشاشة.',\n    step3: '3.أدخل رمز التحقق المكون من 6 أرقام',\n    step3Tip: 'يرجى إدخال رمز التحقق المعروض على الشاشة',\n    verifyCode6: 'رمز التحقق المكون من 6 أرقام',\n    iosAddress: 'عنوان تنزيل iOS:',\n    androidAddress: 'عنوان تنزيل Android:',\n    chromeVerify: 'أداة التحقق من Google',\n    nextBtn: 'الخطوة التالية',\n    confirmSign: 'تأكيد التوقيع',\n    dynamicCode: 'رمز أداة التحقق الديناميكي',\n    password: 'رمز التوقيع المشفر',\n    pleaseInput: 'يرجى الإدخال',\n    twoFactorTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر لإكمال التوقيع.',\n    passwordTip: 'بناءً على طلب المرسل، تحتاج إلى التوقيع المشفر لإكمال التوقيع.',\n    twoFactorAndPasswordTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر والتوقيع المشفر لإكمال التوقيع.',\n    passwordTip2: 'يرجى الاتصال بالمرسل للحصول على رمز التوقيع المشفر، وبعد إدخاله يمكنك توقيع العقد.',\n    dynamicVerifyInfo: 'يرجى إدخال رمز أداة التحقق الديناميكي الصحيح. إذا كنت تقوم بالربط مرة أخرى، فيرجى إدخال رمز أداة التحقق الديناميكي للربط الأحدث.'\n  },\n  functionSupportDialog: {\n    title: 'تقديم الوظائف',\n    inputTip: 'إذا كانت لديك احتياجات استخدام ذات صلة، يرجى ملء احتياجاتك في النموذج التالي. ستقوم BestSign بترتيب متخصصين للاتصال بك وتقديم إرشادات الخدمة في غضون 24 ساعة.',\n    useSence: 'سيناريو التطبيق',\n    useSenceTip: 'مثل الموارد البشرية، الموزعين، وثائق الخدمات اللوجستية، إلخ',\n    estimatedOnlineTime: 'تاريخ الإطلاق المتوقع',\n    requireContent: 'المتطلبات',\n    requireContentTip: 'يرجى وصف كيفية استخدام شركتك للتوقيع الإلكتروني حتى نتمكن من تقديم حل مناسب لك',\n    getSupport: 'احصل على دعم خدمة احترافي',\n    callServiceHotline: 'الخط الساخن: 400-993-6665',\n    useSenceNotEmpty: 'لا يمكن أن يكون سيناريو الاستخدام فارغًا',\n    requrieContentNotEmpty: 'لا يمكن أن يكون محتوى الطلب فارغًا',\n    oneWeek: 'في غضون أسبوع',\n    oneMonth: 'في غضون شهر',\n    other: 'أخرى',\n    submitSuccess: 'تم الإرسال بنجاح',\n    submitTrial: 'تقديم طلب تجريبي',\n    toTrial: 'للتجربة',\n    trialTip: 'بعد تقديم طلب التجربة، سيتم تنشيط الوظيفة الحالية على الفور وستكون متاحة للتجربة. لمساعدتك بشكل أفضل على استخدام الميزات، يمكنك ملء المزيد من المتطلبات في النموذج أدناه. سيتصل بك مستشار BestSign لتقديم الخدمات.',\n    applyTrial: 'التقدم بطلب تجربة',\n    trialSuccTip: 'تم تنشيط الوظيفة. نرحب بتجربتها',\n    goBuy: 'اشتر الآن',\n    trialTipMap: {\n      title: 'تعليمات التجربة',\n      tip1: '1. استخدام فوري، صالح لمدة 7 أيام;',\n      tip2: '2. خلال فترة التجربة، لن يتم احتساب رسوم للوظيفة;',\n      tip3: '3. كل كيان شركة لديه فرصة تجربة واحدة فقط لوظيفة ما;',\n      tip4: '4. الشراء الذاتي متاح خلال فترة التجربة، والاستخدام غير منقطع;',\n      tip5: '5. إذا انتهت فترة التجربة الخاصة بك، يمكنك مسح الرمز والاتصال بمستشار BestSign المحترف للحصول على التفاصيل:'\n    },\n    contactAdminTip: 'للاستخدام، يرجى الاتصال بمسؤول المؤسسة {tip} للشراء والفتح',\n    trialEndTip: 'بعد انتهاء فترة التجربة، انقر للشراء',\n    trialRemainDayTip: 'بقي {day} أيام في فترة التجربة، انقر لشراء النسخ',\n    trialEnd: 'انتهت وظيفة التجربة',\n    trialEndMap: {\n      deactivateTip: 'تم تعطيل ميزة {feature}. يرجى مسح التكوين أو تجديده قبل متابعة استخدامه.',\n      feature1: 'مرفقات العقود',\n      remove1: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن بيانات مرفقات العقود الإضافية المكونة وحذفها.',\n      feature2: 'التعرف على الكتابة اليدوية',\n      remove2: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن التعرف على الكتابة اليدوية المكون وحذفه.',\n      feature3: 'زخرفة العقد: ختم الركوب + العلامة المائية',\n      remove3: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن زخرفة العقد المكونة وحذفها.',\n      feature4: 'موافقة إرسال العقد',\n      remove4: 'طريقة مسح التكوين هي: وحدة تحكم المؤسسة - تعطيل جميع عمليات الموافقة'\n    }\n  },\n  setSignPwdDialog: {\n    tip: \"بعد اكتمال الإعداد، سيتم استخدام كلمة مرور التوقيع أولاً افتراضيًا. يمكنك تسجيل الدخول إلى منصة التوقيع الإلكتروني لـ BestSign والدخول إلى 'مركز المستخدم' أو تسجيل الدخول إلى تطبيق BestSign والدخول إلى 'إدارة الحساب' لتغيير كلمة المرور.\",\n    saveAndReturnSign: 'حفظ والعودة للتوقيع',\n    changeEmailVerify: 'التبديل إلى التحقق عبر البريد الإلكتروني',\n    changePhoneVerify: 'التبديل إلى التحقق عبر رقم الجوال'\n  },\n  contractCompare: {\n    reUpload: 'إعادة الرفع',\n    title: 'مقارنة العقود',\n    packagePurchase: 'شراء الباقة',\n    packagePurchaseTitle: '【{title} خاصية】شراء الباقة ',\n    myPackage: 'باقتي',\n    packageDetail: 'تفاصيل الباقة',\n    per: 'مرة',\n    packageContent: 'محتوى الباقة يشمل:',\n    num: 'عدد المرات {type}',\n    limitTime: 'فترة الصلاحية',\n    month: 'شهر',\n    payNow: 'الشراء الآن',\n    contactUs: 'اتصل بنا | امسح الرمز للتواصل مع مستشار BestSign المحترف للمعرفة',\n    compareInfo1: 'تعليمات الاستخدام:',\n    compareInfo2: '{index}. يمكن لجميع أعضاء الشركة استخدام {type} المشتراة وعدد {per}، إذا كنت بحاجة فقط للاستخدام الشخصي، يمكنك التبديل إلى حساب شخصي في أعلى اليمين;',\n    compareInfo3: '{index}. يتم احتساب الاستخدام وفقًا لعدد {per} العقود المرفوعة',\n    codePay: 'يرجى المسح للدفع',\n    aliPay: 'الدفع عبر Alipay',\n    wxPay: 'الدفع عبر WeChat',\n    payIno: 'تفعيل الوظيفة | كائن الشراء | مبلغ الدفع',\n    finishPay: 'إكمال الدفع',\n    paySuccess: 'تم الشراء بنجاح',\n    originFile: 'ملف العقد الأصلي',\n    compareFile: 'ملف العقد للمقارنة',\n    documentSelect: 'اختيار الملف',\n    comparisonResult: 'نتيجة المقارنة',\n    history: 'السجل التاريخي',\n    currentHistory: 'سجل المستندات',\n    noData: 'لا توجد بيانات',\n    differences: '{num} اختلاف',\n    historyLog: '{num} سجل',\n    uploadLimit: 'اسحب الملفات التي تريد مقارنتها وأفلتها هنا للتحميل | نحن ندعم حاليًا ملفات PDF (بما في ذلك مسح PDF) وملفات Word',\n    dragInfo: 'حرر زر الماوس لإكمال التحميل',\n    uploadError: 'تنسيق الملف غير مدعوم',\n    pageNum: 'الصفحة {page}',\n    difference: 'الاختلاف {num}',\n    download: 'تنزيل نتيجة المقارنة',\n    comparing: 'جاري مقارنة العقود...',\n    tip: 'تنبيه',\n    confirm: 'تأكيد',\n    toBuy: 'للشراء',\n    translate: 'ترجمة العقد',\n    doCompare: 'مقارنة',\n    doTranslate: 'ترجمة',\n    review: 'مراجعة العقد',\n    doReview: 'مراجعة',\n    reviewUploadFile: 'اسحب الملف المراد مراجعته وأفلته هنا للتحميل',\n    reviewUpload: 'اسحب مرجع المراجعة وأفلته هنا للتحميل | مثل: \"إجراءات إدارة الموزع\" و\"وثائق سياسة المشتريات الشركة\" وغيرها من وثائق اللوائح المؤسسية المستخدمة لمراجعة العقود | حاليًا ندعم فقط ملفات PDF وWord',\n    reviewOriginFile: 'العقد قيد المراجعة',\n    reviewTargetFile: 'مرجع المراجعة',\n    reviewResult: 'نتيجة المراجعة',\n    uploadReviewFile: 'تحميل ملف مرجع المراجعة',\n    risk: 'نقطة مخاطرة {num}',\n    risks: '{num} نقطة مخاطرة',\n    startReview: 'بدء المراجعة',\n    reviewing: 'جاري مراجعة العقد...',\n    noRisk: 'اكتملت المراجعة، لم يتم العثور على مخاطر',\n    allowUpload: 'يمكن تحميل \"إجراءات إدارة مشتريات الشركة\" وغيرها من اللوائح التنظيمية التي يمكن أن توجه مراجعة العقود، أو تحميل الخطوط الحمراء للشركة، أو إرشادات أعمال القسم، إلخ، | مثل: يجب على الطرف الأول إكمال الدفع في غضون 5 أيام بعد توقيع العقد.',\n    notAllowUpload: 'لا تستخدم عبارات غامضة أو وصفًا مبدئيًا كأساس للمراجعة، | مثل: يجب ألا تنتهك جميع بنود العقد متطلبات القوانين واللوائح ذات الصلة.',\n    resumeReview: 'متابعة الملف التالي',\n    close: 'إغلاق',\n    extract: 'استخراج العقد',\n    extractTitle: 'الكلمات الرئيسية المراد استخراجها',\n    selectKeyword: 'يرجى اختيار من \"الكلمات الرئيسية\" أدناه',\n    keyword: 'الكلمات الرئيسية',\n    addKeyword: 'إضافة {keyword}',\n    introduce: 'تفسير {keyword}',\n    startExtract: 'بدء الاستخراج',\n    extractTargetFile: 'العقد المراد استخراجه',\n    extractKeyWord: 'استخراج الكلمات الرئيسية',\n    extracting: 'جاري استخراج العقد',\n    extractResult: 'نتيجة الاستخراج',\n    extractUploadFile: 'اسحب الملف المراد استخراجه وأفلته هنا للتحميل',\n    needExtractKeyword: 'يرجى اختيار الكلمات الرئيسية التي ترغب في استخراجها',\n    summary: 'ملخص العقد',\n    keySummary: 'ملخص محتوى الكلمات الرئيسية',\n    deleteKeywordConfirm: 'هل تريد بالتأكيد حذف هذه الكلمة الرئيسية؟',\n    keywordPosition: 'مواقع الكلمات الرئيسية ذات الصلة',\n    riskJudgement: 'تقييم المخاطر',\n    judgeTargetContract: 'العقد المراد تقييمه',\n    interpretTargetContract: 'العقود المُفسَّرة',\n    startJudge: 'بدء تقييم المخاطر',\n    startInterpret: 'ابدأ التفسير',\n    uploadText: 'يرجى تحميل المستندات لتقييم المخاطر',\n    interpretText: 'الرجاء رفع الملفات التي تحتاج إلى تفسير',\n    startTips: 'يمكننا الآن البدء في تقييم المخاطر',\n    interpretTips: 'يمكننا الآن بدء عملية التفسير',\n    infoExtract: 'استخراج المعلومات'\n  },\n  batchImport: {\n    iKnow: 'فهمت'\n  },\n  templateCommon: {\n    tip: 'نصيحة'\n  },\n  mgapprovenote: {\n    SAQ: 'استبيان استقصائي',\n    analyze: 'تحليل',\n    annotate: 'تعليق توضيحي',\n    law: 'البحث في البنود القانونية',\n    case: 'البحث عن حالات مشابهة',\n    translate: 'ترجمة',\n    mark: 'علامة',\n    tips: 'المحتوى أعلاه تم إنشاؤه بواسطة الذكاء الاصطناعي ولا يمثل موقف شانغشانغتشيان. إنه للرجوع إليه فقط. يرجى عدم حذف أو تعديل هذه العلامة.',\n    limit: 'تم الوصول إلى حد الاستخدام. إذا كنت بحاجة إلى استمرار الاستخدام، يرجى ملء النموذج. سيتصل بك خدمة العملاء لدينا.',\n    confirmTxt: 'الذهاب للتعبئة',\n    content: 'محتوى ذو صلة',\n    experience: 'الخبرة العملية',\n    datas: 'المعطيات المرتبطة',\n    terms: 'بنود مشابهة',\n    original: 'المصدر',\n    export: 'بيانات التصدير',\n    preview: 'عرض مسبق للعقد',\n    history: 'التسجيلات السابقة'\n  },\n  sealConfirm: {\n    title: 'صفحة تأكيد الختم الإلكتروني',\n    header: 'تأكيد الختم الإلكتروني',\n    signerEnt: 'الشركة المتعاقدة:',\n    abnormalSeal: 'ختم إلكتروني غير طبيعي:',\n    sealNormal: 'الختم طبيعي',\n    tip1: 'يرجى التأكد مما إذا كان الختم الإلكتروني طبيعيًا وقابلًا للاستخدام. إذا كان طبيعيًا، يمكنك النقر على زر \"الختم طبيعي\". بعد ذلك، عندما تستخدم هذه الشركة هذا الختم مرة أخرى، لن يرسل النظام إشعارات غير طبيعية إليك بعد الآن.',\n    tip2: 'إذا كانت هناك مشكلة في الختم، يرجى التواصل فورًا مع الطرف الموقع لاستبدال الختم، وإعادة إرسال العقد للتوقيع، أو الرفض وإعادة التوقيع.'\n  },\n  ...console,\n  ...docList,\n  ...consts,\n  keyInfoExtract: {\n    operate: 'استخراج المعلومات',\n    contractType: 'أنواع العقود المتوقعة',\n    tooltips: 'اختر المعلومات الرئيسية',\n    predictText: 'جاري التنبؤ',\n    extractText: 'جاري الاستخراج',\n    errorMessage: 'لقد استنفدت حد الاستخدام الخاص بك، إذا كانت لديك احتياجات أخرى، يمكنك ملء الاستبيان، وسنتصل بك ونجدد المزيد من الجرعات لك.',\n    result: 'النتيجة:'\n  },\n  judgeRisk: {\n    title: 'محامي الذكاء الاصطناعي',\n    deepInference: 'المعاملات القانونية الذكية',\n    showAll: 'عرض المزيد',\n    tips: 'جاري التقييم',\n    dialogTitle: 'مراجعة العقود بواسطة “محامي الذكاء الاصطناعي”',\n    aiInterpret: 'تفسير بالذكاء الاصطناعي'\n  },\n  workspace: {\n    create: 'تم الإنشاء',\n    reviewing: 'قيد المراجعة',\n    completed: 'تم الانتهاء',\n    noData: 'لا توجد بيانات',\n    introduce: 'شرح {keyword}',\n    termsDetail: 'تفاصيل المصطلح',\n    extractFormat: 'تنسيق الاستخراج',\n    optional: 'اختياري',\n    required: 'مطلوب',\n    operate: 'إجراء',\n    detail: 'التفاصيل',\n    delete: 'حذف',\n    agreement: {\n      uploadError: 'يمكن رفع ملفات PDF، DOC، DOCX فقط!',\n      extractionRequest: 'تم إرسال طلب الاستخراج، يرجى مراجعة نتائج الاستخراج في قائمة المصطلحات لاحقًا',\n      upload: 'رفع الملف',\n      define: 'تعريف المصطلح',\n      extract: 'استخراج الاتفاقية',\n      drag: 'اسحب الملف إلى هنا أو',\n      add: 'انقر للإضافة',\n      format: 'يدعم تنسيقات doc، docx، pdf',\n      fileName: 'اسم الملف',\n      status: 'الحالة',\n      completed: 'تم الرفع',\n      failed: 'فشل الرفع',\n      size: 'الحجم',\n      terms: 'المصطلحات',\n      success: 'تم استخراج الملف، إجمالي {total}',\n      ongoing: 'جارٍ استخراج الملف... إجمالي {total}',\n      tips: 'تخطي هذه الشاشة لا يؤثر على نتائج الاستخراج',\n      others: 'متابعة الرفع',\n      result: 'الانتقال إلى صفحة تنزيل نتائج الاستخراج',\n      curProgress: 'التقدم الحالي: ',\n      refresh: 'تحديث',\n      details: 'تم تحميل {successNum} من أصل {length}',\n      start: 'بدء الاستخراج',\n      more: 'إضافة ملف',\n      skip: 'تخطي الاستخراج وإكمال الرفع.',\n      tiqu: 'بدء الاستخراج',\n      chouqu: 'بدء الاستخراج'\n    },\n    review: {\n      distribution: 'توزيع المراجعة',\n      Incomplete: 'غير مكتمل',\n      createReview: 'إنشاء مراجعة',\n      manageReview: 'إدارة المراجعات',\n      reviewDetail: 'تفاصيل المراجعة',\n      reviewId: 'رقم المراجعة',\n      reviewStatus: 'حالة المراجعة',\n      reviewName: 'اسم المراجعة',\n      reviewStartTime: 'وقت بدء المراجعة',\n      reviewCompleteTime: 'وقت انتهاء المراجعة',\n      reviewDesc: 'الإصدار: إصدار {reviewVersion}  |  رقم المراجعة: {reviewId}',\n      distribute: 'بدء المراجعة',\n      drag: 'اسحب الاتفاقية للمراجعة إلى هذه المنطقة',\n      content: 'المحتوى قيد المراجعة',\n      current: 'سجلات التوزيع الحالية',\n      history: 'السجلات التاريخية',\n      page: 'الصفحة {page}:',\n      users: 'المستخدمون المطلوب مراجعتهم',\n      message: 'رسالة',\n      modify: 'تعديل',\n      placeholder: 'يفصل بين المستخدمين المتعددين بفاصلة منقوطة \";\"',\n      submit: 'تأكيد',\n      reupload: 'إعادة رفع اتفاقية المراجعة',\n      finish: 'إنهاء المراجعة',\n      reviewSummary: 'ملخص المراجعة',\n      initiator: 'مُبادِر المراجعة',\n      versionSummary: 'ملخص الإصدار',\n      version: 'الإصدار',\n      versionOrder: 'الإصدار {version}',\n      curReviewStatus: 'حالة مراجعة الإصدار الحالي',\n      curReviewVersion: 'الإصدار الحالي',\n      curReviewPopulation: 'عدد مراجعي الإصدار الحالي',\n      curReviewStartTime: 'وقت بدء مراجعة الإصدار الحالي',\n      curReviewInitiator: 'مُبادِر مراجعة الإصدار الحالي',\n      checkComments: 'عرض تجميعي لملاحظات التعديل',\n      overview: 'نظرة سريعة على نتائج المراجعة',\n      reviewer: 'المراجع',\n      reviewResult: 'نتيجة المراجعة',\n      replyTime: 'وقت الرد',\n      agreement: 'الاتفاقية المُراجعة',\n      files: 'الاتفاقيات ذات الصلة',\n      fileName: 'اسم الاتفاقية',\n      numberOfModificationSuggestions: 'عدد اقتراحات التعديل',\n      uploadTime: 'وقت الرفع',\n      download: 'تنزيل',\n      dispatch: 'توزيع',\n      recent: 'آخر وقت مراجعة: ',\n      replyContent: 'محتوى رد المراجعة',\n      advice: 'اقتراحات تعديل الاتفاقية',\n      noIdea: 'لا توجد اقتراحات تعديل حالياً',\n      origin: 'المحتوى الأصلي:',\n      revised: 'المحتوى المعدل:',\n      suggestion: 'اقتراح التعديل:',\n      dateMark: '{name} كتب في <span style=\"color: #0988EC\">الإصدار {version}</span> بتاريخ {date}',\n      unReviewed: 'لم يتم المراجعة بعد',\n      revisionFiles: 'ملفات التعديل',\n      staffReplyAggregation: 'تجميع معلومات التعديل',\n      staffReply: 'معلومات مراجعة {name}',\n      tips: 'ملاحظة',\n      tipsContent: 'سيؤدي الانتهاء إلى إيقاف التوزيع والعمليات اللاحقة لهذه المراجعة، هل تتابع؟',\n      confirm: 'تأكيد',\n      cancel: 'إلغاء',\n      successMessage: 'تم الانتهاء',\n      PASS: 'مقبول',\n      REJECT: 'مرفوض',\n      uploadErrorMessage: 'يدعم حاليًا ملفات docx فقط',\n      successInitiated: 'تم بدء المراجعة',\n      autoDistribute: 'التوزيع الذكي',\n      requiredUsers: 'المستخدمون الذين يحتاجون إلى مراجعة',\n      contentToReview: 'المحتوى للمراجعة',\n      termDetails: 'تفاصيل المصطلح',\n      term: 'المصطلح',\n      aiDistribute: 'التوزيع الذكي AI',\n      noData: 'لا توجد بيانات متاحة',\n      docIconAlt: 'أيقونة المستند',\n      docxIconAlt: 'أيقونة DOCX',\n      pdfIconAlt: 'أيقونة PDF',\n      requiredUsersError: 'يرجى ملء المستخدمين الذين يحتاجون إلى مراجعة',\n      selectContentError: 'يرجى اختيار المحتوى للمراجعة',\n      initiateReviewSuccess: 'تم بدء المراجعة',\n      syncInitiated: 'تم بدء المزامنة'\n    },\n    contentTracing: {\n      title: 'تتبع المحتوى',\n      fieldContent: 'محتوى الحقل',\n      originalResult: 'النتيجة الأصلية',\n      contentSource: 'مصدر المحتوى',\n      page: 'الصفحة '\n    }\n  },\n  hubblePackage: {\n    title: 'حزمتي',\n    details: 'تفاصيل الحزمة',\n    remainingPages: 'إجمالي الصفحات المتبقية',\n    pages: 'صفحات',\n    usedPages: 'المستخدمة',\n    remaining: 'المتبقي المتاح',\n    total: 'إجمالي',\n    expiryTime: 'وقت الانتهاء',\n    amount: 'العدد',\n    unitPrice: 'سعر الوحدة',\n    copy: 'نسخة',\n    words: 'ألف كلمة'\n  },\n  workspaceIndex: {\n    title: 'مساحة العمل',\n    package: 'حزم الاستخدام',\n    agreement: 'إدارة الاتفاقيات',\n    review: 'إدارة المراجعات',\n    term: 'إدارة المصطلحات'\n  },\n  agreement: {\n    title: 'إدارة الاتفاقيات',\n    exportList: 'تصدير قائمة الاتفاقيات',\n    exportAllChecked: 'Excel (جميع الحقول، الاتفاقيات المحددة)',\n    exportCurrentChecked: 'Excel (الحقول الحالية، الاتفاقيات المحددة)',\n    exportAllMatched: 'Excel (جميع الحقول، المطابقة للشروط)',\n    exportCurrentMatched: 'Excel (الحقول الحالية، المطابقة للشروط)',\n    add: 'إضافة اتفاقية',\n    upload: 'رفع اتفاقية',\n    operation: 'إجراء',\n    download: 'تنزيل الاتفاقية',\n    details: 'التفاصيل',\n    delete: 'حذف',\n    relatedTaskStatus: 'حالة المهمة المرتبطة بالاستخراج',\n    confirmDelete: 'هل تريد تأكيد حذف هذه الاتفاقية؟',\n    prompt: 'تلميح',\n    booleanYes: 'نعم',\n    booleanNo: 'لا',\n    defaultExportName: 'تصدير.xlsx',\n    taskNotStarted: 'لم تبدأ مهمة الاستخراج',\n    taskStarted: 'بدأت مهمة الاستخراج (جارٍ البحث في المحتوى)',\n    contentSearchCompleted: 'اكتمل البحث في المحتوى (جارٍ تنسيق النتائج)',\n    resultFormattingCompleted: 'اكتمل تنسيق النتائج (جارٍ مراجعة النتائج)',\n    resultVerificationCompleted: 'اكتملت مراجعة النتائج'\n  },\n  filter: {\n    filter: 'تصفية',\n    refreshExtraction: 'إعادة الاستخراج',\n    extractTerms: 'استخراج تعريفات المصطلحات',\n    refreshList: 'تحديث القائمة',\n    currentCondition: 'الاتفاقيات المعروضة تحت الشروط الحالية.',\n    when: 'عندما',\n    selectCondition: 'اختر الشرط',\n    enterCondition: 'أدخل الشرط',\n    yes: 'نعم',\n    no: 'لا',\n    addCondition: 'إضافة شرط',\n    reset: 'إعادة تعيين',\n    confirm: 'تأكيد',\n    and: 'و',\n    or: 'أو',\n    equals: 'يساوي',\n    notEquals: 'لا يساوي',\n    contains: 'يحتوي على',\n    notContains: 'لا يحتوي على',\n    greaterThan: 'أكبر من',\n    greaterThanOrEquals: 'أكبر من أو يساوي',\n    lessThan: 'أقل من',\n    lessThanOrEquals: 'أقل من أو يساوي',\n    emptyCondition: 'لا يمكن أن يكون شرط التصفية فارغًا'\n  },\n  fieldConfig: {\n    button: 'تكوين الحقول',\n    header: 'الحقول المطلوب عرضها',\n    submit: 'إنهاء',\n    cancel: 'إلغاء'\n  },\n  agreementDetail: {\n    detail: 'تفاصيل الاتفاقية',\n    add: 'إضافة اتفاقية',\n    id: 'رقم الاتفاقية',\n    file: 'ملف الاتفاقية',\n    download: 'تنزيل الاتفاقية',\n    replaceFile: 'استبدال ملف الاتفاقية',\n    uploadFile: 'رفع ملف الاتفاقية',\n    relatedExtractionStatus: 'حالة مهمة الاستخراج المرتبطة',\n    dataSource: 'مصدر البيانات',\n    yes: 'نعم',\n    no: 'لا',\n    select: 'اختر',\n    input: 'أدخل',\n    save: 'حفظ',\n    cancel: 'إلغاء',\n    page: 'الصفحة {page}',\n    addDataSource: 'إضافة مصدر بيانات',\n    pageNo: 'الصفحة ',\n    pageSuffix: '',\n    submit: 'إرسال',\n    inputDataSource: 'أدخل محتوى مصدر البيانات',\n    pageFormatError: 'يُدعم أرقام الصفحات مفصولة بفاصلة إنجليزية أو أرقام فقط',\n    confirmDelete: 'هل تريد تأكيد حذف مصدر البيانات الحالي؟',\n    tips: 'تلميح',\n    uploadSuccess: 'تم الرفع بنجاح'\n  },\n  termManagement: {\n    title: 'إدارة المصطلحات',\n    batchDelete: 'حذف جماعي',\n    import: 'استيراد مصطلحات',\n    export: 'تصدير مصطلحات',\n    add: '+ إضافة مصطلح',\n    name: 'اسم المصطلح',\n    definition: 'شرح المصطلح',\n    formatRequirement: 'متطلبات تنسيق الاستخراج',\n    dataFormat: 'تنسيق البيانات',\n    operation: 'إجراء',\n    edit: 'تعديل',\n    delete: 'حذف',\n    detail: 'تفاصيل المصطلح',\n    addTitle: 'إضافة مصطلح',\n    namePlaceholder: 'أدخل المصطلح المتخصص',\n    definitionPlaceholder: 'أدخل شرح المصطلح',\n    formatRequirementPlaceholder: 'أدخل متطلبات تنسيق استخراج المصطلح',\n    dataFormatPlaceholder: 'متطلبات تنسيق استخراج المصطلح المطلوبة',\n    cancel: 'إلغاء',\n    confirmEdit: 'تأكيد التعديل',\n    importTitle: 'استيراد مصطلحات',\n    uploadTemplate: 'رفع ملف القالب',\n    downloadTemplate: 'تنزيل ملف القالب',\n    extractType: {\n      text: 'نص',\n      longText: 'نص طويل',\n      date: 'تاريخ',\n      number: 'رقم',\n      boolean: 'نعم/لا'\n    },\n    importSuccess: 'تم الاستيراد بنجاح',\n    deleteConfirm: 'هل تريد تأكيد حذف هذا المصطلح؟',\n    prompt: 'تلميح',\n    nameEmptyError: 'اسم المصطلح لا يمكن أن يكون فارغًا'\n  },\n  agent: {\n    extractTitle: 'استخراج المعلومات',\n    riskTitle: 'محامي الذكاء الاصطناعي',\n    feedback: 'تغذية الراجِع',\n    toMini: 'انتقل إلى التطبيق المصغر للاطلاع',\n    otherContract: 'أرني المخاطر الخفية في العقود الأخرى',\n    others: 'الأخرى',\n    submit: 'انقر لإرسال',\n    autoExtract: 'يتم استخراج الخطوات التالية تلقائيًا حتى انتهاء العملية',\n    autoRisk: 'يتم تحليل الخطوات التالية تلقائيًا حتى انتهاء العملية',\n    aiGenerated: 'AI Generated - © BestSign',\n    chooseRisk: 'الرجاء تحديد الملف المراد تحليله',\n    chooseExtract: 'اختر الملف لبدء عملية الاستخراج',\n    analyzing: 'جارٍ تحليل المحتوى',\n    advice: 'جارٍ إنشاء اقتراحات التعديل',\n    options: 'جاري توليد الخيارات',\n    inputTips: 'الرجاء إدخال البيانات المطلوبة بدقة',\n    chargeTip: 'الرصيد غير كافٍ، يرجى إعادة الشحن',\n    original: 'النص الأصلي',\n    revision: 'اقتراحات التعديل',\n    diff: 'مقارنة',\n    locate: 'تحديد مكان النص الأصلي',\n    custom: 'حدد قواعد الفحص حسب الحاجة',\n    content: 'إحداثيات النص المصدر',\n    satisfy: 'أنا راضٍ بالتحليل، استمر في التحليل التالي',\n    dissatisfy: 'أنا غير راضٍ بالتحليل، أعيد التحليل',\n    selectFunc: 'الرجاء اختيار الوظيفة التي ترغب في استخدامها',\n    deepInference: 'المعاملات القانونية الذكية',\n    deepThinking: 'في حالة تفكير عميق',\n    deepThoughtCompleted: 'تم الانتهاء من التفكير العميق',\n    reJudge: 'إعادة الحكم',\n    confirm: 'تأكيد',\n    tipsContent: 'إعادة التقييم ستخصم من عدد المحاولات. هل تريد المتابعة؟',\n    useLawyer: 'استخدم محامي الذكاء',\n    interpretFinish: 'اكتمل التفسير الآلي',\n    exportPDF: 'تصدير تقرير PDF',\n    defaultExportName: 'تصدير.pdf',\n    exporting: 'جاري تصدير التقرير... الرجاء الانتظار'\n  },\n  authorize: {\n    title: 'الشروط والأحكام',\n    content: 'فعّل العقود الذكية - تحليل بالذكاء الاصطناعي يُسهّل عملك!موافقتك تمنحك تجربة مجانية',\n    cancel: 'لا، شكرًا',\n    confirm: 'موافقة والبدء',\n    contract: 'اطّلع على 《شروط استخدام منتج هابل》'\n  },\n  sealDistribute: {\n    requestSeal: 'طلب تخصيص الختم',\n    company: 'الشركة',\n    applicant: 'مقدم الطلب',\n    accountID: 'رقم الحساب',\n    submissionTime: 'الوقت',\n    status: 'الحالة',\n    agree: 'تمت الموافقة',\n    unAgree: 'تم الرفض',\n    ifAgree: 'إذا وافقت،',\n    applyTime: 'وقت ختم مقدم الطلب هو:',\n    to: 'إلى',\n    placeHolderTime: 'سنة-شهر-يوم',\n    senderCompany: 'الشركة المرسلة',\n    documentTitle: 'عنوان العقد',\n    sealApplicationScope: 'نطاق تطبيق الختم',\n    applyforSeal: 'طلب الختم',\n    reject: 'رفض',\n    approve: 'موافقة'\n  },\n  sealApproval: {\n    sealRight: 'صلاحيات الختم',\n    requestSeal: 'طلب تخصيص الختم',\n    allEntContract: 'العقود من جميع المؤسسات',\n    partEntContract: 'العقود من مؤسسات محددة: ',\n    pleaseInputRight: 'الرجاء إدخال الصلاحيات',\n    successTransfer: 'بعد نجاح التسليم،',\n    getRight: 'سيتم الحصول على الصلاحيات المذكورة أعلاه أو يمكن تحرير وتعيين صلاحيات توقيع جديدة مباشرة.',\n    signAllEntContract: 'توقيع العقود من جميع المؤسسات',\n    sign: 'توقيع',\n    sendContract: 'العقود المرسلة',\n    sealUseTime: 'فترة استخدام الختم: ',\n    currentStatus: 'الحالة الحالية: ',\n    takeBackSeal: 'استرجاع الختم',\n    agree: 'موافقة',\n    hasAgree: 'تمت الموافقة',\n    hasReject: 'تم الرفض',\n    hasDone: 'مكتمل',\n    ask: 'سوف',\n    giveYou: 'يتم تخصيص الختم لك',\n    hopeAsk: 'يأمل في',\n    hopeGive: 'تسليم الختم إلى',\n    hopeGiveYou: 'تسليم الختم ذي الصلة إليك',\n    noSettingTime: 'لا يوجد إعداد للوقت',\n    approvalSuccess: 'تمت الموافقة بنجاح',\n    getSealSuccess: 'تم الحصول على الختم بنجاح'\n  },\n  hubbleEntry: {\n    smartAdvisor: 'مستشار العقد الذكي',\n    tooltips: 'هذه الميزة غير مفعّلة. راجع مستشاري التوقيع الإلكتروني BestSign للشراء.',\n    confirm: 'نعم'\n  },\n  lang: 'ar'\n};", "map": {"version": 3, "names": ["utils", "components", "console", "docList", "home", "sign", "entAuth", "docTranslation", "consts", "fileService", "createSuccessful", "<PERSON><PERSON><PERSON><PERSON>", "footerAd", "title", "content1", "content2", "bankContent", "bankTip1", "bankTip2", "bankFooter", "cancel", "continue", "commonFooter", "record", "hubbleRecordId", "openPlatform", "aboutBestSign", "contact", "recruitment", "help", "copyright", "company", "ssqLogo", "provideTip", "ssq", "provide", "signHotline", "langSwitch", "login", "pswLogin", "usePswLogin", "verifyLogin", "useVerifyLogin", "scanLogin", "scanFailure", "scanSuccess", "scanLoginTip", "appLoginTip", "downloadApp", "forgetPsw", "noAccount", "registerNow", "accountPlaceholder", "passwordPlaceholder", "pictureVer", "verifyCodePlaceholder", "getVerifyCode", "noRegister", "or", "errAccountOrPwdTip", "errAccountOrPwdTip2", "errEmailOrTel", "errPwd", "verCodeFormatErr", "grapVerCodeErr", "grapVerCodeFormatErr", "lackAccount", "lackGrapCode", "getVerCodeTip", "loginView", "reg<PERSON><PERSON><PERSON>", "takeViewBtn", "resendCode", "regTip", "haveRead", "bestsignAgreement", "and", "digitalCertificateAgreement", "privacyPolicy", "sendSuc", "lackVerCode", "lackPsw", "notMatch", "cookieTip", "wrongLink", "footerTips", "bestSign", "bestSignDescription", "forgetPswStep", "pictureVerCodeInput", "accountInput", "smsCodeInput", "haveRegistereLoginNow", "nextStep", "setNewPasswordInput", "passwordResetSucceeded", "accountNotRegistered", "loginAndDownload", "registerAndDownload", "inputPhone", "readContract", "errorPhone", "companyCert", "regAndCompanyCert", "handwrite", "picSubmitTip", "<PERSON><PERSON><PERSON><PERSON>", "replaceAllSignature", "replaceAllSeal", "canUseSeal", "applyForSeal", "moreTip", "uploadPic", "use", "clickExtend", "rewrite", "upload", "uploadTip1", "uploadTip2", "confirm", "upgradeBrowser", "submitTip", "needWrite", "needRewrite", "title2", "QRCode", "ok", "clearTips", "isBlank", "success", "signNotMatch", "signNotMatchExact", "msg", "successToUser", "successToSign", "cantGet", "common", "advice", "notEmpty", "enter6to18n", "ssqDes", "signPwdType", "enterActualEntName", "enterCorrectName", "enterCorrectPhoneNum", "enterCorrectEmail", "imgCodeErr", "enterCorrectIdNum", "enterCorrectFormat", "enterCorrectDateFormat", "entCertification", "subBaseInfo", "corDocuments", "license", "uploadLimit", "hi", "exit", "hotline", "acceptProtectingMethod", "comfirmSubmit", "cerficated", "entName", "serialNumber", "validity", "nationalNo", "corporationName", "city", "entCertificate", "certificationAuthority", "bestsignPlatform", "notIssued", "date", "congratulations", "rejectMessage", "recertification", "waitMessage", "<PERSON><PERSON>uth", "info", "submitPicError", "contractDrafting", "contractManagement", "userCenter", "service", "enterpriseConsole", "groupConsole", "startSigning", "contractType", "sendContract", "shortcuts", "setting", "signNum", "contractNum", "contractInFormation", "type", "basicInformation", "more", "certified", "account", "time", "day", "sendContractNum", "num", "realName", "update", "mark", "countDes", "chargeNow", "myRechargeOrder", "statusTip", "useTemplate", "useLocalFile", "docDetail", "canNotOperateTip", "shareSignLink", "faceSign", "faceFirstVerifyCodeSecond", "contractRecipient", "personalOperateLog", "recordDialog", "user", "operate", "view", "download", "remarks", "operateRecords", "borrowingRecords", "currentHolder", "currentEnterprise", "companyInterOperationLog", "receiverMap", "sender", "signer", "ccUser", "downloadCode", "noTagToAddHint", "requireFieldNotAllowEmpty", "modifySuccess", "uncategorized", "notAllowModifyContractType", "setTag", "contractTag", "plsInput", "plsInputCompanyInternalNum", "companyInternalNum", "none", "plsSelect", "modify", "contractDetailInfo", "slideContentTip", "signNotice", "contractAncillaryInformation", "content", "document", "downloadDepositConfirmTip", "hint", "confrim", "downloadTip", "transferSuccessGoManagePage", "claimSign", "downloadDepositPageTip", "resend", "proxySign", "notPassed", "approving", "signning", "notarized", "currentFolder", "archive", "deadlineForSigning", "endFinishTime", "contractImportTime", "contractSendTime", "back", "contractInfo", "basicInfo", "personAccount", "entAccount", "operator", "signStartTime", "signDeadline", "contractExpireDate", "edit", "settings", "from", "folder", "reason", "approval", "viewAttach", "downloadContract", "downloadAttach", "print", "certificatedTooltip", "needMeSign", "needMeApproval", "inApproval", "needOthersSign", "signComplete", "signOverdue", "rejected", "revoked", "contractCompleteTime", "contractEndTime", "reject", "revoke", "viewSignOrders", "viewApprovalProcess", "completed", "cc", "ccer", "signSubject", "signSubjectTooltip", "IDNumber", "state", "notice", "detail", "RealNameCertificationRequired", "RealNameCertificationNotRequired", "MustHandwrittenSignature", "handWritingRecognition", "privateMessage", "attachment", "rejectReason", "notSigned", "notViewed", "viewed", "signed", "viewedNotSigned", "notApproval", "remindSucceed", "reviewDetails", "close", "entInnerOperateDetail", "approve", "disapprove", "applySeal", "applied", "apply", "toOtherSign", "handOver", "approvalOpinions", "useSeal", "signature", "fill", "times", "place", "contractDetail", "viewMore", "collapse", "signLink", "saveQRCode", "signQRCode", "copy", "copySucc", "copyFail", "unCertified", "claimed", "uploadFile", "thumbnails", "isUploading", "move", "delete", "replace", "tip", "understand", "totalPages", "matchErr", "inUploadingDeleteErr", "timeOutErr", "imgUnqualified", "imgBiggerThan20M", "error", "hasCATip", "internalNumber", "contractName", "contractNameTooltip", "toSelect", "contractTypeErr", "signDeadLine", "signDeadLineTooltip", "selectDate", "expireDateTooltip", "necessary", "notNecessary", "dateTips", "contractTitleErr", "contractTitleLengthErr", "template", "templateList", "linkBoxTip", "dynamicTemplateUpdate", "newVersionDesc", "updateTip", "connectUs", "sendCode", "fail", "sendCodeGuide", "tip1", "main", "sub", "line1", "line2", "tip2", "tip3", "tip4", "style", "text", "x", "fontSize", "resetPwd", "<PERSON><PERSON><PERSON><PERSON>", "oldPlaceholder", "new<PERSON>abel", "newPlaceholder", "submit", "errorMsg", "oldRule", "newRule", "personAuthIntercept", "name", "id", "descNoAuth", "des<PERSON><PERSON>", "descNoSame", "descNoAuth1", "descNoAuth2", "tips", "goOn", "goMore", "descNoSame1", "descNoSame2", "goHome", "authInfo", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmReject", "authIntercept", "authTip", "viewAndSign", "tips2", "requestOtherAnth", "goAuth", "requestSomeoneList", "ent", "accountPH", "send", "lackEntName", "errAccount", "successfulSent", "thirdPartApprovalDialog", "title1", "cancelBtnText", "confirmBtnText", "iKnow", "endSignEarlyPrompt", "signPrompt", "signTotalCountTip", "signatureTip", "hasSigned", "hasNotSigned", "noNeedSealTip", "commonNomal", "yesterday", "ssqPlatform", "ssqTestPlatform", "pageExpiredTip", "pswCodeSimpleTip", "transferAdminDialog", "transfer", "confirmAdmin", "choseBoxForReceiver", "dataNeedForReceiver", "dataFromDataBox", "searchTp", "search", "boxNotFound", "localCommon", "seal", "signDate", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "confirmSeal", "confirmRemark", "optional", "require", "comboBox", "twoFactor", "signTip", "settingTwoFactor", "step1", "step1Tip", "step2", "step2Tip1", "step2Tip2", "step3", "step3Tip", "verifyCode6", "iosAddress", "android<PERSON><PERSON><PERSON>", "chromeVerify", "nextBtn", "confirmSign", "dynamicCode", "password", "pleaseInput", "twoFactorTip", "passwordTip", "twoFactorAndPasswordTip", "passwordTip2", "dynamicVerifyInfo", "functionSupportDialog", "inputTip", "useSence", "useSenceTip", "estimatedOnlineTime", "requireContent", "requireContentTip", "getSupport", "callServiceHotline", "useSenceNotEmpty", "requrieContentNotEmpty", "oneWeek", "oneMonth", "other", "submitSuccess", "submitTrial", "toTrial", "trialTip", "applyTrial", "trialSuccTip", "goBuy", "trialTipMap", "tip5", "contactAdminTip", "trialEndTip", "trialRemainDayTip", "trialEnd", "trialEndMap", "deactivateTip", "feature1", "remove1", "feature2", "remove2", "feature3", "remove3", "feature4", "remove4", "setSignPwdDialog", "saveAndReturnSign", "changeEmailVerify", "changePhoneVerify", "contractCompare", "reUpload", "packagePurchase", "packagePurchaseTitle", "myPackage", "packageDetail", "per", "packageContent", "limitTime", "month", "payNow", "contactUs", "compareInfo1", "compareInfo2", "compareInfo3", "codePay", "ali<PERSON>ay", "wxPay", "payIno", "finishPay", "paySuccess", "originFile", "compareFile", "documentSelect", "comparisonResult", "history", "currentHistory", "noData", "differences", "historyLog", "dragInfo", "uploadError", "pageNum", "difference", "comparing", "<PERSON><PERSON><PERSON>", "translate", "doCompare", "doTranslate", "review", "doReview", "reviewUploadFile", "reviewUpload", "reviewOriginFile", "reviewTargetFile", "reviewResult", "uploadReviewFile", "risk", "risks", "startReview", "reviewing", "noRisk", "allowUpload", "notAllowUpload", "resume<PERSON><PERSON>iew", "extract", "extractTitle", "selectKeyword", "keyword", "addKeyword", "introduce", "startExtract", "extractTargetFile", "extractKeyWord", "extracting", "extractResult", "extractUploadFile", "needExtractKeyword", "summary", "key<PERSON><PERSON><PERSON>y", "deleteKeywordConfirm", "keywordPosition", "riskJudgement", "judge<PERSON><PERSON><PERSON>Contract", "interpretTargetContract", "startJudge", "startInterpret", "uploadText", "interpretText", "startTips", "interpretTips", "infoExtract", "batchImport", "templateCommon", "mgapprovenote", "SAQ", "analyze", "annotate", "law", "case", "limit", "confirmTxt", "experience", "datas", "original", "export", "preview", "sealConfirm", "header", "signerEnt", "abnormalSeal", "sealNormal", "keyInfoExtract", "tooltips", "predictText", "extractText", "errorMessage", "result", "judgeRisk", "deepInference", "showAll", "dialogTitle", "aiInterpret", "workspace", "create", "termsDetail", "extractFormat", "required", "agreement", "extractionRequest", "define", "drag", "add", "format", "fileName", "status", "failed", "size", "ongoing", "others", "curProgress", "refresh", "details", "start", "skip", "tiqu", "chouqu", "distribution", "Incomplete", "createReview", "manageReview", "reviewDetail", "reviewId", "reviewStatus", "reviewName", "reviewStartTime", "reviewCompleteTime", "reviewDesc", "distribute", "current", "page", "users", "message", "placeholder", "reupload", "finish", "reviewSummary", "initiator", "versionSummary", "version", "versionOrder", "curRevie<PERSON><PERSON><PERSON><PERSON>", "curReviewV<PERSON><PERSON>", "curReviewPopulation", "curReviewStartTime", "curReviewInitiator", "checkComments", "overview", "reviewer", "replyTime", "files", "numberOfModificationSuggestions", "uploadTime", "dispatch", "recent", "replyContent", "noIdea", "origin", "revised", "suggestion", "dateMark", "unReviewed", "revisionFiles", "staffReplyAggregation", "staffReply", "tipsContent", "successMessage", "PASS", "REJECT", "uploadErrorMessage", "successInitiated", "autoDistribute", "requiredUsers", "contentToReview", "termDetails", "term", "aiDistribute", "docIconAlt", "docxIconAlt", "pdfIconAlt", "requiredUsersError", "selectContentError", "initiateReviewSuccess", "syncInitiated", "contentTracing", "<PERSON><PERSON><PERSON>nt", "originalResult", "contentSource", "hubblePackage", "remainingPages", "pages", "usedPages", "remaining", "total", "expiryTime", "amount", "unitPrice", "words", "workspaceIndex", "package", "exportList", "exportAllChecked", "exportCurrentChecked", "exportAllMatched", "exportCurrentMatched", "operation", "relatedTaskStatus", "confirmDelete", "prompt", "booleanYes", "booleanNo", "defaultExportName", "taskNotStarted", "taskStarted", "contentSearchCompleted", "resultFormattingCompleted", "resultVerificationCompleted", "filter", "refreshExtraction", "extractTerms", "refreshList", "currentCondition", "when", "selectCondition", "enterCondition", "yes", "no", "addCondition", "reset", "equals", "notEquals", "contains", "notContains", "greaterThan", "greaterThanOrEquals", "lessThan", "lessThanOrEquals", "emptyCondition", "fieldConfig", "button", "agreementDetail", "file", "replaceFile", "relatedExtractionStatus", "dataSource", "select", "input", "save", "addDataSource", "pageNo", "pageSuffix", "inputDataSource", "pageFormatError", "uploadSuccess", "termManagement", "batchDelete", "import", "definition", "formatRequirement", "dataFormat", "addTitle", "namePlaceholder", "definitionPlaceholder", "formatRequirementPlaceholder", "dataFormatPlaceholder", "confirmEdit", "importTitle", "uploadTemplate", "downloadTemplate", "extractType", "longText", "boolean", "importSuccess", "deleteConfirm", "nameEmptyError", "agent", "riskTitle", "feedback", "to<PERSON><PERSON>", "otherContract", "autoExtract", "autoRisk", "aiGenerated", "chooseRisk", "chooseExtract", "analyzing", "options", "inputTips", "chargeTip", "revision", "diff", "locate", "custom", "satisfy", "dissatisfy", "selectFunc", "deepThinking", "deepThoughtCompleted", "reJudge", "useLawyer", "interpretFinish", "exportPDF", "exporting", "authorize", "contract", "sealDistribute", "requestSeal", "applicant", "accountID", "submissionTime", "agree", "unAgree", "ifAgree", "applyTime", "to", "placeHolderTime", "senderCompany", "documentTitle", "sealApplicationScope", "applyforSeal", "sealApproval", "sealRight", "allEntContract", "partEntContract", "pleaseInputRight", "successTransfer", "getRight", "signAllEntContract", "sealUseTime", "currentStatus", "takeBackSeal", "hasAgree", "hasReject", "hasDone", "<PERSON><PERSON><PERSON>", "hopeAsk", "hopeGive", "hopeGiveYou", "noSettingTime", "approvalSuccess", "getSealSuccess", "hubbleEntry", "smartAdvisor", "lang"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/ar.js"], "sourcesContent": ["\n// الترجمة للغة العربية، حالياً تعمل فقط عند فتح الروابط القصيرة عبر الجوال\nimport utils from './module/utils/utils-ar.js';\nimport components from './module/components/components-ar.js';\n\nimport console from './module/console/console-ar.js';\nimport docList from './module/docList/docList-ar.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-ar.js';\nimport entAuth from './module/entAuth/entAuth-ar.js';\nimport docTranslation from './module/docTranslation/docTranslation-ar.js';\nimport consts from '@/lang/module/consts/ar';\n\nexport default {\n    ...utils,\n    ...components,\n    ...docTranslation,\n    fileService: 'أرشيف+',\n    createSuccessful: 'تم الإنشاء بنجاح',\n    setLabel: 'تعيين التصنيف',\n    footerAd: {\n        title: 'تنبيه انتقال الصفحة',\n        content1: 'أنت على وشك زيارة صفحة ترويجية تابعة لطرف ثالث',\n        content2: 'هل تريد المتابعة؟',\n        bankContent: 'ستدخل قريباً إلى صفحة تعريف قروض الشركات \"قرض سهل\" من بنك نينغبو',\n        bankTip1: 'السماح لبنك نينغبو بالاتصال بي هاتفياً',\n        bankTip2: 'إرسال رسالة نصية لي تشرح كيفية التقديم',\n        bankFooter: 'إضافة خدمة عملاء بنك نينغبو المخصصة للخدمة الشخصية',\n        cancel: 'إلغاء',\n        continue: 'متابعة',\n    },\n    commonFooter: {\n        record: 'رقم سجل ICP الرئيسي: تشجيانغ ICP رقم ********',\n        hubbleRecordId: 'سجل شبكة المعلومات: 330106973391501230011',\n        openPlatform: 'منصة مفتوحة',\n        aboutBestSign: 'عن بست ساين',\n        contact: 'اتصل بنا',\n        recruitment: 'التوظيف',\n        help: 'مركز المساعدة',\n        copyright: 'حقوق النشر',\n        company: 'هانغتشو بست ساين المحدودة',\n        ssqLogo: 'شعار بست ساين في الشريط السفلي',\n        provideTip: 'خدمة التوقيع الإلكتروني مقدمة من',\n        ssq: ' بست ساين',\n        provide: '',\n        signHotline: 'الخط الساخن للخدمة',\n        langSwitch: 'اللغة',\n    },\n    login: {\n        pswLogin: 'تسجيل الدخول بكلمة المرور',\n        usePswLogin: 'تسجيل الدخول باستخدام كلمة المرور',\n        verifyLogin: 'رمز التحقق',\n        useVerifyLogin: 'تسجيل الدخول برمز التحقق',\n        scanLogin: 'تسجيل الدخول بالمسح الضوئي',\n        scanFailure: 'انتهت صلاحية رمز QR، يرجى المحاولة مرة أخرى',\n        scanSuccess: 'تم المسح بنجاح',\n        scanLoginTip: 'يرجى استخدام التطبيق للمسح وتسجيل الدخول',\n        appLoginTip: 'يرجى النقر على تسجيل الدخول في تطبيق بست ساين',\n        downloadApp: 'تنزيل تطبيق بست ساين',\n        forgetPsw: 'نسيت؟',\n        login: 'تسجيل الدخول',\n        noAccount: 'ليس لديك حساب؟',\n        registerNow: 'التسجيل الآن',\n        accountPlaceholder: 'رقم الهاتف أو البريد الإلكتروني',\n        passwordPlaceholder: 'كلمة المرور',\n        pictureVer: 'يرجى ملء المحتوى في الصورة',\n        verifyCodePlaceholder: 'يرجى إدخال رمز مكون من 6 أرقام',\n        getVerifyCode: 'إرسال',\n        noRegister: 'غير مسجل بعد',\n        or: 'أو',\n        errAccountOrPwdTip: 'كلمة المرور التي أدخلتها لا تتطابق مع رقم الحساب؟',\n        errAccountOrPwdTip2: 'كلمة المرور التي أدخلتها لا تتطابق مع الحساب',\n        errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',\n        errPwd: 'يرجى إدخال كلمة المرور الصحيحة!',\n        verCodeFormatErr: 'خطأ في رمز التحقق',\n        grapVerCodeErr: 'خطأ في رمز التحقق الرسومي',\n        grapVerCodeFormatErr: 'خطأ في تنسيق رمز التحقق الرسومي',\n        lackAccount: 'يرجى إدخال الحساب أولاً',\n        lackGrapCode: 'يرجى ملء رمز التحقق الرسومي أولاً.',\n        getVerCodeTip: 'يرجى الحصول على رمز التحقق',\n\n        loginView: 'تسجيل الدخول وعرض العقد',\n        regView: 'التسجيل وعرض العقد',\n        takeViewBtn: 'تسجيل الدخول والتوقيع',\n        resendCode: 'إعادة الإرسال',\n        regTip: 'بعد ملء رمز التحقق الصحيح، ستقوم بست ساين بإنشاء حساب لك',\n        haveRead: 'لقد قرأت ووافقت على',\n        bestsignAgreement: 'اتفاقية خدمة بست ساين',\n        and: 'و',\n        digitalCertificateAgreement: 'اتفاقية استخدام الشهادة الرقمية',\n        privacyPolicy: 'سياسة الخصوصية',\n        sendSuc: 'تم الإرسال بنجاح',\n        lackVerCode: 'يرجى إدخال رمز التحقق أولاً',\n        lackPsw: 'يرجى إدخال كلمة المرور أولاً',\n        notMatch: 'كلمة المرور والحساب المدخلان غير متطابقين',\n        cookieTip: 'غير قادر على قراءة وكتابة ملفات تعريف الارتباط، يرجى التحقق من عدم وجود وضع التصفح المتخفي أو تعطيل ملفات تعريف الارتباط',\n        wrongLink: 'رابط غير صالح',\n        footerTips: 'خدمة التوقيع الإلكتروني مقدمة من <span>بست ساين</span>',\n        bestSign: 'بست ساين',\n        bestSignDescription: 'رائد في صناعة التعاقدات الإلكترونية',\n        /** نسيت كلمة المرور /forgotPassword start */\n        forgetPswStep: 'التحقق من الحساب المسجل | إعادة تعيين كلمة المرور',\n        pictureVerCodeInput: 'رمز التحقق الرسومي | يرجى ملء محتويات الصورة',\n        accountInput: 'الحساب | يرجى إدخال حسابك',\n        smsCodeInput: 'رمز التحقق | الحصول على رمز التحقق',\n        haveRegistereLoginNow: 'تم التسجيل بالفعل، | تسجيل الدخول الآن',\n        nextStep: 'التالي | إرسال',\n        setNewPasswordInput: 'تعيين كلمة مرور جديدة | 6-18 رقم أو حرف',\n        passwordResetSucceeded: 'تمت إعادة تعيين كلمة المرور بنجاح',\n        /** نسيت كلمة المرور /forgotPassword end */\n        accountNotRegistered: 'الحساب غير مسجل',\n        loginAndDownload: 'تسجيل الدخول وتنزيل العقد',\n        registerAndDownload: 'التسجيل وتنزيل العقد',\n        inputPhone: 'يرجى إدخال رقم الهاتف',\n        readContract: 'قراءة العقد',\n        errorPhone: 'خطأ في تنسيق رقم الهاتف المحمول',\n        companyCert: 'إجراء تصديق الشركة',\n        regAndCompanyCert: 'التسجيل وإجراء تصديق الشركة',\n    },\n    ...sign,\n    handwrite: {\n        title: 'مباشرة على الشاشة',\n        picSubmitTip: 'تم تقديم صورة التوقيع بنجاح',\n        settingDefault: 'تعيين كافتراضي',\n        replaceAllSignature: 'استخدام لجميع التوقيعات',\n        replaceAllSeal: 'لجميع الأختام',\n        canUseSeal: 'أختامي',\n        applyForSeal: 'تقديم طلب لاستخدام الأختام',\n        moreTip: 'سيتم حفظ توقيعك اليدوي كتوقيع افتراضي، ويستخدم فقط لتوقيع العقود. مسار الإدارة: [مركز المستخدم <- إدارة التوقيعات]',\n        uploadPic: 'تحميل صورة',\n        use: 'استخدام',\n        clickExtend: 'انقر على السهم الأيمن لتوسيع المنطقة',\n        rewrite: 'إعادة الكتابة',\n        upload: 'تحميل صورة التوقيع',\n        uploadTip1: 'تنبيه: عند تحميل صورة التوقيع، يرجى ملء الصورة بالكامل بالتوقيع',\n        uploadTip2: 'يرجى استخدام ألوان داكنة أو نص أسود خالص للتوقيع.',\n        cancel: 'إلغاء',\n        confirm: 'تأكيد',\n        upgradeBrowser: 'المتصفح لا يدعم الكتابة اليدوية، يرجى ترقية متصفحك.',\n        submitTip: 'تم تقديم التوقيع اليدوي بنجاح',\n        needWrite: 'يرجى كتابة اسمك أولاً',\n        needRewrite: 'لا يمكن التعرف على التوقيع، يرجى المحاولة مرة أخرى',\n        title2: 'رسم توقيعك باليد',\n        QRCode: 'عن طريق مسح رمز QR',\n        ok: 'موافق',\n        clearTips: 'يرجى كتابة توقيع واضح يمكن التعرف عليه',\n        isBlank: 'اللوحة فارغة، يرجى رسم التوقيع يدوياً قبل الإرسال!',\n        success: 'تم تقديم التوقيع اليدوي بنجاح',\n        signNotMatch: 'يرجى كتابة توقيعك بأحرف كبيرة والحفاظ على توافقه مع معلومات هويتك الحقيقية.',\n        signNotMatchExact: 'الكلمة {numList} لا تتطابق، يرجى إعادة الكتابة',\n        msg: {\n            successToUser: 'تم تفعيل التوقيع الجديد، يرجى الانتقال إلى مركز المستخدم على الويب - إدارة التوقيع',\n            successToSign: 'تم تفعيل التوقيع الجديد، يرجى التحقق من صفحة توقيع العقد',\n            cantGet: 'لا يمكن الحصول على توقيع، جرب استخدام متصفح مختلف!',\n        },\n    },\n    common: {\n        aboutBestSign: 'عن بست ساين',\n        contact: 'اتصل بنا',\n        recruitment: 'التوظيف',\n        copyright: 'حقوق النشر',\n        advice: 'نصائح',\n        notEmpty: 'لا يمكن أن يكون فارغاً',\n        enter6to18n: 'يرجى إدخال 6-18 رقماً وحرفاً',\n        ssqDes: 'رائد في منصات التوقيع الإلكتروني السحابية',\n        openPlatform: 'منصة مفتوحة',\n        company: 'هانغتشو بست ساين المحدودة',\n        help: 'مركز المساعدة',\n\n        errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',\n        verCodeFormatErr: 'خطأ في رمز التحقق',\n        signPwdType: 'يرجى إدخال 6 أرقام',\n        enterActualEntName: 'يرجى إدخال اسم العمل الحقيقي',\n        enterCorrectName: 'يرجى إدخال اسم العمل الصحيح',\n        enterCorrectPhoneNum: 'يرجى إدخال رقم الهاتف المحمول الصحيح',\n        enterCorrectEmail: 'يرجى إدخال البريد الإلكتروني الصحيح',\n        imgCodeErr: 'خطأ في رمز التحقق',\n        enterCorrectIdNum: 'يرجى إدخال رقم الهوية الصحيح',\n        enterCorrectFormat: 'يرجى إدخال التنسيق الصحيح',\n        enterCorrectDateFormat: 'يرجى إدخال تنسيق التاريخ الصحيح',\n    },\n    entAuth: {\n        ...entAuth,\n        entCertification: 'تصديق الاسم الحقيقي للمؤسسة',\n        subBaseInfo: 'تقديم المعلومات الأساسية',\n        corDocuments: 'وثائق الشركة',\n        license: 'الترخيص',\n        upload: 'انقر للتحميل',\n        uploadLimit: 'الصور متاحة فقط بتنسيقات jpeg وjpg وpng وحجم لا يزيد عن 10 ميجابايت',\n        hi: 'مرحباً',\n        exit: 'خروج',\n        help: 'مساعدة',\n        hotline: 'الخط الساخن للخدمة',\n        acceptProtectingMethod: 'أقبل طريقة حماية المعلومات الشخصية التي أقدمها',\n        comfirmSubmit: 'تأكيد الإرسال',\n        cerficated: 'اكتمل التصديق',\n        entName: 'اسم المؤسسة',\n        serialNumber: 'الرقم التسلسلي',\n        validity: 'الصلاحية',\n        nationalNo: 'رقم التسجيل الوطني',\n        corporationName: 'اسم الممثل القانوني',\n        city: 'المدينة',\n        entCertificate: 'شهادة الاسم الحقيقي للمؤسسة',\n        certificationAuthority: 'هيئة التصديق',\n        bestsignPlatform: 'منصة بست ساين السحابية للتوقيع الإلكتروني',\n        notIssued: 'لم يتم إصدارها',\n        date: '{year}-{month}-{day}',\n        congratulations: 'تهانينا على إتمام عملية تصديق الاسم الحقيقي للمؤسسة بنجاح',\n        continue: 'متابعة',\n        rejectMessage: 'للأسباب التالية، لم يتم اجتياز تدقيق البيانات، يرجى التحقق',\n        recertification: 'إعادة التصديق',\n        waitMessage: 'ستتم المراجعة من قبل خدمة العملاء خلال يوم عمل واحد، يرجى الانتظار',\n    },\n    personalAuth: {\n        info: 'معلومات',\n        submitPicError: 'يرجى تحميل الصورة قبل الاستخدام',\n    },\n    home: {\n        ...home,\n        home: 'الرئيسية',\n        contractDrafting: 'صياغة العقود',\n        contractManagement: 'العقود',\n        userCenter: 'الإدارة',\n        service: 'الخدمة',\n        enterpriseConsole: 'لوحة تحكم المؤسسة',\n        groupConsole: 'لوحة تحكم المجموعة',\n        startSigning: 'بدء التوقيع',\n        contractType: 'عقد عادي | عقد قالب',\n        sendContract: 'ابدأ الآن',\n        shortcuts: 'الاختصارات | لا توجد اختصارات لأي مستندات',\n        setting: 'الإعداد فوراً | إنشاء المزيد من الاختصارات',\n        signNum: 'ملخص شهري للتوقيعات والتسليمات',\n        contractNum: 'العقود المرسلة | العقود الموقعة',\n        contractInFormation: 'لم ترسل أو توقع أي عقود هذا الشهر',\n        type: 'شركة | شخص ',\n        basicInformation: 'معلومات أساسية',\n        more: 'المزيد',\n        certified: 'مصدق | غير مصدق',\n        account: 'الحساب',\n        time: 'وقت الإنشاء | وقت التسجيل',\n        day: 'يوم | شهر',\n        sendContractNum: 'التسليمات | التوقيعات',\n        num: '',\n        realName: 'إنشاء حساب أعمال باسم حقيقي الآن | إنشاء حساب فردي باسم حقيقي الآن',\n        update: 'تحديث المنتج',\n        mark: 'هل ترغب في التوصية ببست ساين لأصدقائك وزملائك؟ يرجى تقييم اختيارك من 0 إلى 10.',\n        countDes: {\n            1: 'متاح: لعقد المؤسسة',\n            2: '',\n            3: 'للعقد الخاص',\n            4: '',\n        },\n        chargeNow: 'الشحن الآن',\n        myRechargeOrder: 'طلب إعادة الشحن الخاص بي',\n        statusTip: {\n            1: 'بحاجة إلى تشغيلي',\n            2: 'يحتاج الآخرون إلى التوقيع',\n            3: 'التوقيع على وشك الإغلاق',\n            4: 'اكتمل التوقيع',\n        },\n        useTemplate: 'استخدام القالب',\n        useLocalFile: 'تحميل ملف محلي',\n    },\n    docDetail: {\n        canNotOperateTip: 'غير قادر على {operate} العقد',\n        shareSignLink: 'مشاركة رابط التوقيع',\n        faceSign: 'التوقيع بمسح الوجه',\n        faceFirstVerifyCodeSecond: 'التوقيع ذو الأولوية بالتحقق من الوجه، التوقيع البديل بالتحقق من رمز الرسائل القصيرة',\n        contractRecipient: 'مستلم العقد',\n        personalOperateLog: 'سجل عمليات العقد الفردي',\n        recordDialog: {\n            date: 'التاريخ',\n            user: 'المستخدم',\n            operate: 'العملية',\n            view: 'عرض',\n            download: 'تنزيل',\n        },\n        remarks: 'ملاحظات',\n        operateRecords: 'سجل العمليات',\n        borrowingRecords: 'سجلات الاقتراض',\n        currentHolder: 'المالك الحالي',\n        currentEnterprise: 'الشركة تحت الحساب الحالي',\n        companyInterOperationLog: 'سجل العمليات الداخلية للشركة',\n        receiverMap: {\n            sender: 'مرسل العقد',\n            signer: 'مستلم العقد',\n            ccUser: 'العقد منسوخ إلى',\n        },\n        downloadCode: 'رمز تنزيل العقد',\n        noTagToAddHint: 'لا توجد علامات بعد؛ يرجى الذهاب إلى لوحة التحكم التجارية لإضافتها',\n        requireFieldNotAllowEmpty: 'لا يمكن أن تكون الحقول المطلوبة فارغة',\n        modifySuccess: 'تم التعديل بنجاح',\n        uncategorized: 'غير مصنف',\n        notAllowModifyContractType: 'لا يُسمح بتعديل نوع العقد للعقد تحت {type}',\n        setTag: 'تعيين العلامات',\n        contractTag: 'علامات العقد',\n        plsInput: 'يرجى الإدخال',\n        plsInputCompanyInternalNum: 'يرجى إدخال رقم الأعمال الداخلي',\n        companyInternalNum: 'رقم الأعمال الداخلي',\n        none: 'لا شيء',\n        plsSelect: 'يرجى الاختيار',\n        modify: 'تعديل',\n        contractDetailInfo: 'تفاصيل العقد',\n        slideContentTip: {\n            signNotice: 'تعليمات التوقيع',\n            contractAncillaryInformation: 'مرفقات العقد',\n            content: 'المحتوى',\n            document: 'المستند',\n        },\n        downloadDepositConfirmTip: {\n            title: 'صفحة إثبات التوقيع التي قمت بتنزيلها هي نسخة غير حساسة، مع إخفاء المعلومات الخاصة وغير قابلة للاستخدام في إجراءات المحكمة. إذا كنت بحاجة إلى استخدامها في إجراءات المحكمة، يرجى الاتصال بنا للحصول على النسخة الكاملة.',\n            hint: 'نصائح',\n            confrim: 'متابعة التنزيل',\n            cancel: 'إلغاء',\n        },\n        downloadTip: {\n            title: 'نظراً لأن العقد لم يكتمل بعد، فإنك تقوم بتنزيل ملف معاينة للعقد الذي لم يدخل حيز التنفيذ بعد',\n            hint: 'نصائح',\n            confirm: 'تأكيد',\n            cancel: 'إلغاء',\n        },\n        transferSuccessGoManagePage: 'تم النقل بنجاح وسيتم العودة إلى صفحة إدارة العقود',\n        claimSign: 'استرداد وتوقيع',\n        downloadDepositPageTip: 'تنزيل صفحة إثبات التوقيع (نسخة غير حساسة)',\n        resend: 'إعادة الإرسال',\n        proxySign: 'توقيع مفوض',\n        notPassed: 'مرفوض',\n        approving: 'قيد المراجعة',\n        signning: 'التوقيع قيد التقدم',\n        notarized: 'موثق',\n        currentFolder: 'المجلد الحالي',\n        archive: 'تم أرشفة العقد',\n        deadlineForSigning: 'الموعد النهائي للتوقيع',\n        endFinishTime: 'اكتمل التوقيع/تاريخ الإتمام',\n        contractImportTime: 'وقت استيراد العقد',\n        contractSendTime: 'وقت تسليم العقد',\n        back: 'رجوع',\n        contractInfo: 'معلومات العقد',\n        basicInfo: 'معلومات أساسية',\n        contractNum: 'رقم العقد',\n        sender: 'المرسل',\n        personAccount: 'حساب شخصي',\n        entAccount: 'حساب مؤسسة',\n        operator: 'المشغل',\n        signStartTime: 'وقت بدء التوقيع',\n        signDeadline: 'الموعد النهائي للتوقيع',\n        contractExpireDate: 'تاريخ انتهاء صلاحية العقد',\n        // none: 'لا شيء',\n        edit: 'تعديل',\n        settings: 'إعداد',\n        from: 'المصدر',\n        folder: 'مجلد',\n        contractType: 'نوع العقد',\n        reason: 'السبب',\n        sign: 'توقيع',\n        approval: 'موافقة',\n        viewAttach: 'عرض الصفحات المرفقة',\n        downloadContract: 'تنزيل العقد',\n        downloadAttach: 'تنزيل الصفحة المرفقة',\n        print: 'طباعة',\n        certificatedTooltip: 'تم توثيق العقد والأدلة ذات الصلة في السلسلة القضائية لمحكمة هانغتشو للإنترنت',\n        needMeSign: 'بحاجة إلى توقيعي',\n        needMeApproval: 'بحاجة إلى موافقتي',\n        inApproval: 'قيد النظر...',\n        needOthersSign: 'بحاجة إلى توقيع الآخرين',\n        signComplete: 'اكتمل التوقيع',\n        signOverdue: 'توقيع متأخر',\n        rejected: 'مرفوض',\n        revoked: 'ملغى',\n        contractCompleteTime: 'وقت اكتمال التوقيع',\n        contractEndTime: 'وقت انتهاء التوقيع',\n        reject: 'رفض',\n        revoke: 'إلغاء',\n        download: 'تنزيل',\n        viewSignOrders: 'عرض ترتيب التوقيع',\n        viewApprovalProcess: 'عرض عملية الموافقة',\n        completed: 'اكتمل التوقيع',\n        cc: 'نسخة',\n        ccer: 'طرف النسخة',\n        signer: 'الموقع',\n        signSubject: 'موضوع التوقيع',\n        signSubjectTooltip: 'موضوع التوقيع الذي ملأه المرسل هو',\n        user: 'المستخدم',\n        IDNumber: 'رقم الهوية',\n        state: 'الحالة',\n        time: 'الوقت',\n        notice: 'تذكير',\n        detail: 'التفاصيل',\n        RealNameCertificationRequired: 'مطلوب التصديق بالاسم الحقيقي',\n        RealNameCertificationNotRequired: 'لا يلزم التصديق بالاسم الحقيقي',\n        MustHandwrittenSignature: 'يجب التوقيع بخط اليد',\n        handWritingRecognition: 'تفعيل التعرف على الكتابة اليدوية',\n        privateMessage: 'رسالة',\n        attachment: 'مرفق',\n        rejectReason: 'السبب',\n        notSigned: 'غير موقع',\n        notViewed: 'غير مطلع عليه',\n        viewed: 'تم الاطلاع',\n        signed: 'تم التوقيع',\n        viewedNotSigned: 'قرئ ولم يتم التوقيع',\n        notApproval: 'غير معتمد',\n        remindSucceed: 'تم إرسال رسالة التذكير',\n        reviewDetails: 'تفاصيل الموافقة',\n        close: 'إغلاق',\n        entInnerOperateDetail: 'تفاصيل العمليات الداخلية',\n        approve: 'موافقة',\n        disapprove: 'رفض',\n        applySeal: 'طلب الختم',\n        applied: 'تم التقديم بالفعل',\n        apply: 'تقديم',\n        toOtherSign: 'نقل إلى شخص آخر للتوقيع',\n        handOver: 'نقل',\n        approvalOpinions: 'تعليقات الموافقة',\n        useSeal: 'ختم',\n        signature: 'توقيع',\n        use: 'استخدام',\n        date: 'تاريخ',\n        fill: 'ملء',\n        times: 'ثانوي',\n        place: 'مكان',\n        contractDetail: 'تفاصيل العقد',\n        viewMore: 'عرض المزيد',\n        collapse: 'طي',\n        signLink: 'رابط التوقيع',\n        saveQRCode: 'احفظ رمز QR أو انسخ الرابط وشاركه مع الموقع',\n        signQRCode: 'رمز QR لرابط التوقيع',\n        copy: 'نسخ',\n        copySucc: 'تم النسخ بنجاح',\n        copyFail: 'فشل النسخ',\n        certified: 'مصدق',\n        unCertified: 'غير مصدق',\n        claimed: 'تم المطالبة',\n    },\n    uploadFile: {\n        thumbnails: 'صورة مصغرة',\n        isUploading: 'جاري التحميل',\n        move: 'نقل',\n        delete: 'حذف',\n        replace: 'استبدال',\n        tip: 'نصيحة',\n        understand: 'فهمت',\n        totalPages: '{page} في المجموع',\n        uploadFile: 'تحميل ملف محلي',\n        matchErr: 'يوجد خلل بسيط في الخادم، يرجى المحاولة مرة أخرى لاحقاً.',\n        inUploadingDeleteErr: 'يرجى الحذف بعد التحميل',\n        timeOutErr: 'انتهت مهلة الطلب',\n        imgUnqualified: 'تنسيق الصورة لا يلبي المتطلبات',\n        imgBiggerThan20M: 'لا يمكن أن يتجاوز حجم الصورة 20 ميجابايت!',\n        error: 'خطأ',\n        hasCATip: 'ملف PDF الذي قمت بتحميله يحتوي بالفعل على شهادة رقمية، وهذا سيؤثر على توحيد وتكامل سلسلة أدلة توقيع العقد، لا يُنصح المستخدمون الشخصيون باستخدامه. يرجى تحميل ملف PDF لا يحتوي على أي شهادة رقمية كملف عقد.',\n\n    },\n    contractInfo: {\n        internalNumber: 'رقم العمل الداخلي',\n        contractName: 'اسم العقد',\n        contractNameTooltip: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة وألا يتجاوز 100 كلمة',\n        contractType: 'نوع العقد',\n        toSelect: 'يرجى الاختيار',\n        contractTypeErr: 'تم حذف نوع العقد الحالي. يرجى إعادة اختيار نوع العقد.',\n        signDeadLine: 'الموعد النهائي للتوقيع',\n        signDeadLineTooltip: 'إذا لم يتم توقيع العقد قبل هذا التاريخ، فلا يمكن متابعته',\n        selectDate: 'اختر التاريخ والوقت',\n        contractExpireDate: 'تاريخ انتهاء العقد',\n        expireDateTooltip: 'وقت انتهاء الصلاحية في محتويات العقد لإدارة العقد اللاحقة',\n        necessary: 'ضروري',\n        notNecessary: 'اختياري',\n        dateTips: 'تم تحديد تاريخ انتهاء العقد تلقائيًا لك، يرجى التأكيد',\n        contractTitleErr: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة',\n        contractTitleLengthErr: 'يرجى ألا يتجاوز طول اسم العقد 100 كلمة.',\n    },\n    template: {\n        templateList: {\n            linkBoxTip: 'معرف الخزانة المرتبطة:',\n        },\n        dynamicTemplateUpdate: {\n            title: 'تم إطلاق وظيفة جديدة للقوالب الديناميكية',\n            newVersionDesc: 'تدعم الوظيفة الجديدة عرض الرأس والتذييل وتحافظ على تخطيط صفحة المستند إلى أقصى درجة.',\n            updateTip: 'لا تتم مزامنة ميزة القالب الديناميكي السابقة وتوافقها. مطلوب الترقية اليدوية. إذا تم تحرير القوالب الديناميكية التي تم إنشاؤها قبل 26 يناير، فلن يتم حفظ العقود أو إرسالها. يمكن إرسال العقود قبل 1 مارس 2021 إذا لم يتم تحرير القوالب. يُوصَى بالترقية في أقرب وقت ممكن. القوالب غير الديناميكية غير متأثرة.',\n            connectUs: 'إذا كان لديك أي أسئلة، يرجى الاتصال عبر الخط الساخن 400-993-6665 أو الاتصال بخدمة العملاء عبر الإنترنت.',\n        },\n        sendCode: {\n            tip: 'إعدادات القالب الحالية لا تلبي شروط إنشاء رمز الإرسال. تحقق مما إذا كانت المتطلبات التالية مستوفاة:',\n            fail: {\n                1: 'لا تتضمن مستندات فارغة',\n                2: 'الطرف المتعاقد لديه طرف متغير واحد فقط (بما في ذلك التوقيع والنسخ)، ويجب أن يكون الطرف المتغير هو المشغل الأول؛ يجب أن يكون للموقع موضع ختم التوقيع',\n                3: 'الحساب الثابت للطرف المتعاقد لا يجب أن يكون فارغًا',\n                4: 'لا يؤدي إلى تفعيل الموافقة قبل الإرسال',\n                5: 'المحتويات التي يجب على المرسل ملؤها ليست فارغة (بما في ذلك حقل الوصف وحقل محتوى العقد)',\n                6: 'ليس مزيجًا من القوالب',\n            },\n        },\n        sendCodeGuide: {\n            title: 'شرح وظائف رمز الإرسال المتقدمة',\n            info: 'رمز إرسال القالب مناسب لملفات العقود التي لا تتطلب من المرسل ملء المحتوى، مثل شهادات الاستقالة واتفاقيات السرية وخطابات التفويض، ويمكن توفيرها لأي شخص يقوم بمسح الرمز للحصول عليها. إذا كان هناك معلومات يجب على المرسل ملؤها في المستند، أو إذا كان المرسل بحاجة إلى تحديد نطاق الأطراف المقابلة التي يمكنها الحصول على المستند عبر مسح الرمز، يمكنك استخدام الوظائف المتقدمة لـ \"أرشيف+\". من خلال مسح رمز QR الخاص بخزانة الملفات، يمكنك إكمال إرسال العقد تلقائيًا إلى أشخاص محددين، وملء المحتوى الذي يحتاج المرسل إلى ملئه، وحتى التحكم في نقاط زمنية للإرسال التلقائي؛ كما يتم تخزين الطرف المقابل الذي مسح الرمز في خزانة الملفات، ويمكن الاستعلام عنه في أي وقت. طريقة الاستخدام كالتالي:',\n            tip1: {\n                main: '1. BestSign',\n                sub: '',\n                line1: 'تقديم طلب إلى BestSign لتفعيل أرشيف+، مراجعة العقد المسبقة، المراجعة الذكية',\n                line2: 'بعد التفعيل يمكنك الذهاب إلى القوائم المقابلة للعمليات والاستخدام',\n            },\n            tip2: {\n                main: '2. مدير خزانة الملفات',\n                sub: 'إنشاء خزانة ملفات، تكوين المراجعة الذكية',\n                line1: '',\n                line2: 'قم بإنشاء حقول إدخال في خزانة الملفات تتطابق مع محتوى العقد، وربط قوالب العقد، وإعداد العلاقات وشروط الإرسال التلقائي عند مسح الرمز، وتقديم رمز QR لخزانة الملفات إلى الطرف المقابل للتوقيع.',\n            },\n            tip3: {\n                main: '3. طرف التوقيع',\n                sub: 'مسح الرمز لملء المعلومات، والحصول على ملفات العقد',\n                line1: '',\n                line2: 'يقوم طرف التوقيع بمسح رمز إرسال خزانة الملفات الذي يوفره المرسل للملء، وسيتم أرشفة المعلومات التي تم جمعها عبر خزانة الملفات وملؤها تلقائيًا في العقد، ويحتاج الطرف المقابل فقط إلى الختم أو التوقيع البسيط عند استلام العقد لإكمال توقيع العقد',\n            },\n            tip4: {\n                main: '4. مدير خزانة الملفات',\n                sub: '',\n                line1: 'عرض معلومات الطرف المقابل للتوقيع وحالة العقود المرسلة',\n                line2: 'يمكن لمدير الجهة المرسلة الذهاب إلى خزانة الملفات لعرض معلومات الطرف المقابل الذي قام بمسح الرمز، وحالة إرسال العقد، وما إذا كان قد تم إكمال التوقيع، وما إلى ذلك',\n            },\n        },\n    },\n    style: {\n        signature: {\n            text: {\n                x: '0',\n                fontSize: '18',\n            },\n        },\n    },\n    resetPwd: {\n        title: 'تنبيهات الأمان!',\n        notice: 'عامل الأمان لكلمة المرور منخفض ويوجد خطر أمني. يرجى إعادة تعيين كلمة المرور',\n        oldLabel: 'كلمة المرور الأصلية',\n        oldPlaceholder: 'يرجى إدخال كلمة المرور الأصلية',\n        newLabel: 'كلمة المرور الجديدة',\n        newPlaceholder: '6-18 رقمًا وأحرف كبيرة وصغيرة، تدعم الرموز الخاصة',\n        submit: 'إرسال',\n        errorMsg: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',\n        oldRule: 'لا يمكن أن تكون كلمة المرور الأصلية فارغة',\n        newRule: 'لا يمكن أن تكون كلمة المرور الجديدة فارغة',\n        success: 'نجاح!',\n    },\n    personAuthIntercept: {\n        title: 'ندعوك بـ',\n        name: 'الاسم:',\n        id: 'رقم الهوية:',\n        descNoAuth: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n        desMore: 'وفقًا لمتطلبات المبادر، تحتاج أيضًا إلى استكمال',\n        descNoSame: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمبادر للتأكد وإعادة إنشاء العقد.',\n        descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n        descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n        tips: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n        goOn: 'أنا الشخص نفسه، ابدأ المصادقة',\n        goMore: 'اذهب لاستكمال المصادقة',\n        descNoSame1: ' هوية لتوقيع العقد',\n        descNoSame2: 'هذا لا يتطابق مع معلومات الاسم الحقيقي المكتملة للحساب الذي قمت بتسجيل الدخول إليه حاليًا.',\n        goHome: 'العودة إلى صفحة قائمة العقود>>',\n        authInfo: 'تم اكتشاف أن هوية الاسم الحقيقي لحسابك الحالي هي ',\n        in: 'في',\n        finishAuth: 'أكمل مصادقة الاسم الحقيقي لتوقيع العقود بشكل متوافق',\n        ask: 'هل رقم الهاتف المحمول الحالي هو رقم هاتفك المعتاد؟',\n        reAuthBtnText: 'نعم، أريد استخدام هذا الحساب لإعادة المصادقة والتوقيع',\n        changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',\n        changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',\n        changePhoneTip2: '، لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد أنك الموقع.',\n        confirmReject: 'نعم، أريد رفض المصادقة باسم حقيقي',\n    },\n    authIntercept: {\n        title: 'يُطلب منك أن:',\n        name: 'الاسم:',\n        id: 'رقم الهوية:',\n        descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',\n        descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',\n        descNoSame1: 'لتوقيع العقد.',\n        descNoSame2: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمُرسل للتأكد وإعادة إنشاء العقد.',\n        tips: 'ملاحظة: يجب أن تتطابق معلومات الهوية تمامًا لتوقيع العقد',\n        goOn: 'أنا الشخص نفسه، ابدأ المصادقة',\n        goHome: 'فهمت',\n        goMore: 'اذهب لاستكمال المصادقة',\n        authTip: 'إجراء المصادقة باسم حقيقي.',\n        viewAndSign: 'بعد إكمال المصادقة، يمكنك عرض وتوقيع العقد',\n        tips2: 'ملاحظة: يجب أن يتطابق اسم الشركة تمامًا لعرض وتوقيع العقد.',\n        requestOtherAnth: 'طلب التحقق من قبل الآخرين',\n        goAuth: 'الذهاب للمصادقة باسم حقيقي',\n        requestSomeoneList: 'طلب من الأشخاص التاليين إكمال المصادقة باسم حقيقي:',\n        ent: 'شركة',\n        entName: 'اسم الشركة',\n        account: 'الحساب',\n        accountPH: 'هاتف أو بريد إلكتروني',\n        send: 'إرسال',\n        lackEntName: 'يرجى إدخال اسم الشركة',\n        errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',\n        successfulSent: 'تم الإرسال بنجاح',\n    },\n    thirdPartApprovalDialog: {\n        title1: 'موافقة قبل التوقيع',\n        title2: 'عملية الموافقة',\n        content1: 'يمكنك التوقيع فقط بعد الموافقة، يرجى الانتظار بصبر.',\n        content2: 'يتطلب الموافقة من منصة طرف ثالث (غير منصة BestSign).',\n        cancelBtnText: 'عرض عملية الموافقة',\n        confirmBtnText: 'تأكيد',\n        iKnow: 'فهمت',\n    },\n    endSignEarlyPrompt: {\n        cancel: 'إلغاء',\n        confirm: 'تأكيد',\n        signPrompt: 'إشعار التوقيع',\n        signTotalCountTip: 'يتضمن هذا التوقيع {count} ملفات عقود',\n        signatureTip: 'قام المرسل بتعيين {count} من أعضاء الشركة لتمثيل الشركة في التوقيع، حاليًا:',\n        hasSigned: '{count} أشخاص قاموا بالتوقيع',\n        hasNotSigned: '{count} أشخاص لم يقوموا بالتوقيع',\n        noNeedSealTip: 'بعد الانتهاء من الختم، لن يحتاج أعضاء الشركة الذين لم يوقعوا إلى التوقيع.',\n    },\n    commonNomal: {\n        yesterday: 'الأمس',\n        ssq: 'بيست ساين',\n        ssqPlatform: 'منصة بيست ساين للتوقيع الإلكتروني السحابية',\n        ssqTestPlatform: '(للأغراض التجريبية فقط) منصة بيست ساين للتوقيع الإلكتروني السحابية',\n        pageExpiredTip: 'انتهت صلاحية الصفحة، يرجى التحديث والمحاولة مرة أخرى',\n        pswCodeSimpleTip: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',\n    },\n    transferAdminDialog: {\n        title: 'تأكيد الهوية',\n        transfer: 'نقل',\n        confirmAdmin: 'أنا المدير الرئيسي',\n        content: 'يتعين على مدير النظام الرئيسي أن يكون مسؤولاً عن إدارة أختام الشركة، وإدارة العقود وإدارة صلاحيات الموظفين الآخرين، وعادة ما ينتمي إلى الممثل القانوني للشركة، أو مدير الشؤون المالية، أو مدير الشؤون القانونية، أو مدير قسم تكنولوجيا المعلومات، أو المسؤول عن أعمال الشركة. | يرجى التأكد من أنك تستوفي الهوية المذكورة أعلاه، وإذا لم تكن كذلك، فمن المستحسن نقلها إلى الشخص المعني.',\n    },\n    choseBoxForReceiver: {\n        dataNeedForReceiver: 'المعلومات المطلوب تقديمها من طرف التوقيع',\n        dataFromDataBox: 'يجب الحصول على المعلومات المطلوب تقديمها من طرف التوقيع من خلال جمع المستندات من خزانة ملفات معينة.',\n        searchTp: 'يرجى إدخال اسم أو رمز خزانة الملفات.',\n        search: 'بحث',\n        boxNotFound: 'لا يمكن العثور على خزانة الملفات.',\n        cancel: 'إلغاء',\n        confirm: 'موافق',\n    },\n    localCommon: {\n        cancel: 'إلغاء',\n        confirm: 'تأكيد',\n        toSelect: 'يرجى الاختيار',\n        seal: 'ختم',\n        signature: 'توقيع',\n        signDate: 'تاريخ',\n        text: 'نص',\n        date: 'تاريخ',\n        qrCode: 'رمز الاستجابة السريعة',\n        number: 'رقمي',\n        dynamicTable: 'نماذج ديناميكية',\n        terms: 'شروط وأحكام العقد',\n        checkBox: 'خانات اختيار للخيارات المتعددة',\n        radioBox: 'خانة اختيار للخيارات الفردية',\n        image: 'صورة',\n        confirmSeal: 'ختم للاستعلامات',\n        tip: 'نصائح',\n        confirmRemark: 'ملاحظات بشأن الأختام التي لا تلبي المتطلبات',\n        optional: 'اختياري',\n        require: 'مطلوب',\n        comboBox: 'قائمة منسدلة',\n    },\n    twoFactor: {\n        signTip: 'إشعار التوقيع',\n        settingTwoFactor: 'إعداد أداة التحقق ثنائي العنصر',\n        step1: '1. تثبيت تطبيق التحقق',\n        step1Tip: 'يتطلب التحقق ثنائي العنصر تثبيت تطبيق الهاتف المحمول التالي:',\n        step2: '2.مسح رمز الاستجابة السريعة',\n        step2Tip1: 'استخدم أداة التحقق التي قمت بتنزيلها لمسح رمز الاستجابة السريعة أدناه (يرجى التأكد من أن الوقت على هاتفك يتطابق مع الوقت الحالي، وإلا فلن يمكن تنفيذ التحقق ثنائي العنصر).',\n        step2Tip2: 'سيتم عرض رمز التحقق المكون من 6 أرقام الذي يلزم للتحقق ثنائي العنصر على الشاشة.',\n        step3: '3.أدخل رمز التحقق المكون من 6 أرقام',\n        step3Tip: 'يرجى إدخال رمز التحقق المعروض على الشاشة',\n        verifyCode6: 'رمز التحقق المكون من 6 أرقام',\n        iosAddress: 'عنوان تنزيل iOS:',\n        androidAddress: 'عنوان تنزيل Android:',\n        chromeVerify: 'أداة التحقق من Google',\n        nextBtn: 'الخطوة التالية',\n        confirmSign: 'تأكيد التوقيع',\n        dynamicCode: 'رمز أداة التحقق الديناميكي',\n        password: 'رمز التوقيع المشفر',\n        pleaseInput: 'يرجى الإدخال',\n        twoFactorTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر لإكمال التوقيع.',\n        passwordTip: 'بناءً على طلب المرسل، تحتاج إلى التوقيع المشفر لإكمال التوقيع.',\n        twoFactorAndPasswordTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر والتوقيع المشفر لإكمال التوقيع.',\n        passwordTip2: 'يرجى الاتصال بالمرسل للحصول على رمز التوقيع المشفر، وبعد إدخاله يمكنك توقيع العقد.',\n        dynamicVerifyInfo: 'يرجى إدخال رمز أداة التحقق الديناميكي الصحيح. إذا كنت تقوم بالربط مرة أخرى، فيرجى إدخال رمز أداة التحقق الديناميكي للربط الأحدث.',\n    },\n    functionSupportDialog: {\n        title: 'تقديم الوظائف',\n        inputTip: 'إذا كانت لديك احتياجات استخدام ذات صلة، يرجى ملء احتياجاتك في النموذج التالي. ستقوم BestSign بترتيب متخصصين للاتصال بك وتقديم إرشادات الخدمة في غضون 24 ساعة.',\n        useSence: 'سيناريو التطبيق',\n        useSenceTip: 'مثل الموارد البشرية، الموزعين، وثائق الخدمات اللوجستية، إلخ',\n        estimatedOnlineTime: 'تاريخ الإطلاق المتوقع',\n        requireContent: 'المتطلبات',\n        requireContentTip: 'يرجى وصف كيفية استخدام شركتك للتوقيع الإلكتروني حتى نتمكن من تقديم حل مناسب لك',\n        getSupport: 'احصل على دعم خدمة احترافي',\n        callServiceHotline: 'الخط الساخن: 400-993-6665',\n        useSenceNotEmpty: 'لا يمكن أن يكون سيناريو الاستخدام فارغًا',\n        requrieContentNotEmpty: 'لا يمكن أن يكون محتوى الطلب فارغًا',\n        oneWeek: 'في غضون أسبوع',\n        oneMonth: 'في غضون شهر',\n        other: 'أخرى',\n        submitSuccess: 'تم الإرسال بنجاح',\n        submitTrial: 'تقديم طلب تجريبي',\n        toTrial: 'للتجربة',\n        trialTip: 'بعد تقديم طلب التجربة، سيتم تنشيط الوظيفة الحالية على الفور وستكون متاحة للتجربة. لمساعدتك بشكل أفضل على استخدام الميزات، يمكنك ملء المزيد من المتطلبات في النموذج أدناه. سيتصل بك مستشار BestSign لتقديم الخدمات.',\n        applyTrial: 'التقدم بطلب تجربة',\n        trialSuccTip: 'تم تنشيط الوظيفة. نرحب بتجربتها',\n        goBuy: 'اشتر الآن',\n        trialTipMap: {\n            title: 'تعليمات التجربة',\n            tip1: '1. استخدام فوري، صالح لمدة 7 أيام;',\n            tip2: '2. خلال فترة التجربة، لن يتم احتساب رسوم للوظيفة;',\n            tip3: '3. كل كيان شركة لديه فرصة تجربة واحدة فقط لوظيفة ما;',\n            tip4: '4. الشراء الذاتي متاح خلال فترة التجربة، والاستخدام غير منقطع;',\n            tip5: '5. إذا انتهت فترة التجربة الخاصة بك، يمكنك مسح الرمز والاتصال بمستشار BestSign المحترف للحصول على التفاصيل:',\n        },\n        contactAdminTip: 'للاستخدام، يرجى الاتصال بمسؤول المؤسسة {tip} للشراء والفتح',\n        trialEndTip: 'بعد انتهاء فترة التجربة، انقر للشراء',\n        trialRemainDayTip: 'بقي {day} أيام في فترة التجربة، انقر لشراء النسخ',\n        trialEnd: 'انتهت وظيفة التجربة',\n        trialEndMap: {\n            deactivateTip: 'تم تعطيل ميزة {feature}. يرجى مسح التكوين أو تجديده قبل متابعة استخدامه.',\n            feature1: 'مرفقات العقود',\n            remove1: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن بيانات مرفقات العقود الإضافية المكونة وحذفها.',\n            feature2: 'التعرف على الكتابة اليدوية',\n            remove2: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن التعرف على الكتابة اليدوية المكون وحذفه.',\n            feature3: 'زخرفة العقد: ختم الركوب + العلامة المائية',\n            remove3: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن زخرفة العقد المكونة وحذفها.',\n            feature4: 'موافقة إرسال العقد',\n            remove4: 'طريقة مسح التكوين هي: وحدة تحكم المؤسسة - تعطيل جميع عمليات الموافقة',\n        },\n    },\n    setSignPwdDialog: {\n        tip: \"بعد اكتمال الإعداد، سيتم استخدام كلمة مرور التوقيع أولاً افتراضيًا. يمكنك تسجيل الدخول إلى منصة التوقيع الإلكتروني لـ BestSign والدخول إلى 'مركز المستخدم' أو تسجيل الدخول إلى تطبيق BestSign والدخول إلى 'إدارة الحساب' لتغيير كلمة المرور.\",\n        saveAndReturnSign: 'حفظ والعودة للتوقيع',\n        changeEmailVerify: 'التبديل إلى التحقق عبر البريد الإلكتروني',\n        changePhoneVerify: 'التبديل إلى التحقق عبر رقم الجوال',\n    },\n    contractCompare: {\n        reUpload: 'إعادة الرفع',\n        title: 'مقارنة العقود',\n        packagePurchase: 'شراء الباقة',\n        packagePurchaseTitle: '【{title} خاصية】شراء الباقة ',\n        myPackage: 'باقتي',\n        packageDetail: 'تفاصيل الباقة',\n        per: 'مرة',\n        packageContent: 'محتوى الباقة يشمل:',\n        num: 'عدد المرات {type}',\n        limitTime: 'فترة الصلاحية',\n        month: 'شهر',\n        payNow: 'الشراء الآن',\n        contactUs: 'اتصل بنا | امسح الرمز للتواصل مع مستشار BestSign المحترف للمعرفة',\n        compareInfo1: 'تعليمات الاستخدام:',\n        compareInfo2: '{index}. يمكن لجميع أعضاء الشركة استخدام {type} المشتراة وعدد {per}، إذا كنت بحاجة فقط للاستخدام الشخصي، يمكنك التبديل إلى حساب شخصي في أعلى اليمين;',\n        compareInfo3: '{index}. يتم احتساب الاستخدام وفقًا لعدد {per} العقود المرفوعة',\n        codePay: 'يرجى المسح للدفع',\n        aliPay: 'الدفع عبر Alipay',\n        wxPay: 'الدفع عبر WeChat',\n        payIno: 'تفعيل الوظيفة | كائن الشراء | مبلغ الدفع',\n        finishPay: 'إكمال الدفع',\n        paySuccess: 'تم الشراء بنجاح',\n        originFile: 'ملف العقد الأصلي',\n        compareFile: 'ملف العقد للمقارنة',\n        documentSelect: 'اختيار الملف',\n        comparisonResult: 'نتيجة المقارنة',\n        history: 'السجل التاريخي',\n        currentHistory: 'سجل المستندات',\n        noData: 'لا توجد بيانات',\n        differences: '{num} اختلاف',\n        historyLog: '{num} سجل',\n        uploadLimit: 'اسحب الملفات التي تريد مقارنتها وأفلتها هنا للتحميل | نحن ندعم حاليًا ملفات PDF (بما في ذلك مسح PDF) وملفات Word',\n        dragInfo: 'حرر زر الماوس لإكمال التحميل',\n        uploadError: 'تنسيق الملف غير مدعوم',\n        pageNum: 'الصفحة {page}',\n        difference: 'الاختلاف {num}',\n        download: 'تنزيل نتيجة المقارنة',\n        comparing: 'جاري مقارنة العقود...',\n        tip: 'تنبيه',\n        confirm: 'تأكيد',\n        toBuy: 'للشراء',\n        translate: 'ترجمة العقد',\n        doCompare: 'مقارنة',\n        doTranslate: 'ترجمة',\n        review: 'مراجعة العقد',\n        doReview: 'مراجعة',\n        reviewUploadFile: 'اسحب الملف المراد مراجعته وأفلته هنا للتحميل',\n        reviewUpload: 'اسحب مرجع المراجعة وأفلته هنا للتحميل | مثل: \"إجراءات إدارة الموزع\" و\"وثائق سياسة المشتريات الشركة\" وغيرها من وثائق اللوائح المؤسسية المستخدمة لمراجعة العقود | حاليًا ندعم فقط ملفات PDF وWord',\n        reviewOriginFile: 'العقد قيد المراجعة',\n        reviewTargetFile: 'مرجع المراجعة',\n        reviewResult: 'نتيجة المراجعة',\n        uploadReviewFile: 'تحميل ملف مرجع المراجعة',\n        risk: 'نقطة مخاطرة {num}',\n        risks: '{num} نقطة مخاطرة',\n        startReview: 'بدء المراجعة',\n        reviewing: 'جاري مراجعة العقد...',\n        noRisk: 'اكتملت المراجعة، لم يتم العثور على مخاطر',\n        allowUpload: 'يمكن تحميل \"إجراءات إدارة مشتريات الشركة\" وغيرها من اللوائح التنظيمية التي يمكن أن توجه مراجعة العقود، أو تحميل الخطوط الحمراء للشركة، أو إرشادات أعمال القسم، إلخ، | مثل: يجب على الطرف الأول إكمال الدفع في غضون 5 أيام بعد توقيع العقد.',\n        notAllowUpload: 'لا تستخدم عبارات غامضة أو وصفًا مبدئيًا كأساس للمراجعة، | مثل: يجب ألا تنتهك جميع بنود العقد متطلبات القوانين واللوائح ذات الصلة.',\n        resumeReview: 'متابعة الملف التالي',\n        close: 'إغلاق',\n        extract: 'استخراج العقد',\n        extractTitle: 'الكلمات الرئيسية المراد استخراجها',\n        selectKeyword: 'يرجى اختيار من \"الكلمات الرئيسية\" أدناه',\n        keyword: 'الكلمات الرئيسية',\n        addKeyword: 'إضافة {keyword}',\n        introduce: 'تفسير {keyword}',\n        startExtract: 'بدء الاستخراج',\n        extractTargetFile: 'العقد المراد استخراجه',\n        extractKeyWord: 'استخراج الكلمات الرئيسية',\n        extracting: 'جاري استخراج العقد',\n        extractResult: 'نتيجة الاستخراج',\n        extractUploadFile: 'اسحب الملف المراد استخراجه وأفلته هنا للتحميل',\n        needExtractKeyword: 'يرجى اختيار الكلمات الرئيسية التي ترغب في استخراجها',\n        summary: 'ملخص العقد',\n        keySummary: 'ملخص محتوى الكلمات الرئيسية',\n        deleteKeywordConfirm: 'هل تريد بالتأكيد حذف هذه الكلمة الرئيسية؟',\n        keywordPosition: 'مواقع الكلمات الرئيسية ذات الصلة',\n        riskJudgement: 'تقييم المخاطر',\n        judgeTargetContract: 'العقد المراد تقييمه',\n        interpretTargetContract: 'العقود المُفسَّرة',\n        startJudge: 'بدء تقييم المخاطر',\n        startInterpret: 'ابدأ التفسير',\n        uploadText: 'يرجى تحميل المستندات لتقييم المخاطر',\n        interpretText: 'الرجاء رفع الملفات التي تحتاج إلى تفسير',\n        startTips: 'يمكننا الآن البدء في تقييم المخاطر',\n        interpretTips: 'يمكننا الآن بدء عملية التفسير',\n        infoExtract: 'استخراج المعلومات',\n    },\n    batchImport: {\n        iKnow: 'فهمت',\n    },\n    templateCommon: {\n        tip: 'نصيحة',\n    },\n    mgapprovenote: {\n        SAQ: 'استبيان استقصائي',\n        analyze: 'تحليل',\n        annotate: 'تعليق توضيحي',\n        law: 'البحث في البنود القانونية',\n        case: 'البحث عن حالات مشابهة',\n        translate: 'ترجمة',\n        mark: 'علامة',\n        tips: 'المحتوى أعلاه تم إنشاؤه بواسطة الذكاء الاصطناعي ولا يمثل موقف شانغشانغتشيان. إنه للرجوع إليه فقط. يرجى عدم حذف أو تعديل هذه العلامة.',\n        limit: 'تم الوصول إلى حد الاستخدام. إذا كنت بحاجة إلى استمرار الاستخدام، يرجى ملء النموذج. سيتصل بك خدمة العملاء لدينا.',\n        confirmTxt: 'الذهاب للتعبئة',\n        content: 'محتوى ذو صلة',\n        experience: 'الخبرة العملية',\n        datas: 'المعطيات المرتبطة',\n        terms: 'بنود مشابهة',\n        original: 'المصدر',\n        export: 'بيانات التصدير',\n        preview: 'عرض مسبق للعقد',\n        history: 'التسجيلات السابقة',\n    },\n    sealConfirm: {\n        title: 'صفحة تأكيد الختم الإلكتروني',\n        header: 'تأكيد الختم الإلكتروني',\n        signerEnt: 'الشركة المتعاقدة:',\n        abnormalSeal: 'ختم إلكتروني غير طبيعي:',\n        sealNormal: 'الختم طبيعي',\n        tip1: 'يرجى التأكد مما إذا كان الختم الإلكتروني طبيعيًا وقابلًا للاستخدام. إذا كان طبيعيًا، يمكنك النقر على زر \"الختم طبيعي\". بعد ذلك، عندما تستخدم هذه الشركة هذا الختم مرة أخرى، لن يرسل النظام إشعارات غير طبيعية إليك بعد الآن.',\n        tip2: 'إذا كانت هناك مشكلة في الختم، يرجى التواصل فورًا مع الطرف الموقع لاستبدال الختم، وإعادة إرسال العقد للتوقيع، أو الرفض وإعادة التوقيع.',\n    },\n    ...console,\n    ...docList,\n    ...consts,\n    keyInfoExtract: {\n        operate: 'استخراج المعلومات',\n        contractType: 'أنواع العقود المتوقعة',\n        tooltips: 'اختر المعلومات الرئيسية',\n        predictText: 'جاري التنبؤ',\n        extractText: 'جاري الاستخراج',\n        errorMessage: 'لقد استنفدت حد الاستخدام الخاص بك، إذا كانت لديك احتياجات أخرى، يمكنك ملء الاستبيان، وسنتصل بك ونجدد المزيد من الجرعات لك.',\n        result: 'النتيجة:',\n    },\n    judgeRisk: {\n        title: 'محامي الذكاء الاصطناعي',\n        deepInference: 'المعاملات القانونية الذكية',\n        showAll: 'عرض المزيد',\n        tips: 'جاري التقييم',\n        dialogTitle: 'مراجعة العقود بواسطة “محامي الذكاء الاصطناعي”',\n        aiInterpret: 'تفسير بالذكاء الاصطناعي',\n    },\n    workspace: {\n        create: 'تم الإنشاء',\n        reviewing: 'قيد المراجعة',\n        completed: 'تم الانتهاء',\n        noData: 'لا توجد بيانات',\n        introduce: 'شرح {keyword}',\n        termsDetail: 'تفاصيل المصطلح',\n        extractFormat: 'تنسيق الاستخراج',\n        optional: 'اختياري',\n        required: 'مطلوب',\n        operate: 'إجراء',\n        detail: 'التفاصيل',\n        delete: 'حذف',\n        agreement: {\n            uploadError: 'يمكن رفع ملفات PDF، DOC، DOCX فقط!',\n            extractionRequest: 'تم إرسال طلب الاستخراج، يرجى مراجعة نتائج الاستخراج في قائمة المصطلحات لاحقًا',\n            upload: 'رفع الملف',\n            define: 'تعريف المصطلح',\n            extract: 'استخراج الاتفاقية',\n            drag: 'اسحب الملف إلى هنا أو',\n            add: 'انقر للإضافة',\n            format: 'يدعم تنسيقات doc، docx، pdf',\n            fileName: 'اسم الملف',\n            status: 'الحالة',\n            completed: 'تم الرفع',\n            failed: 'فشل الرفع',\n            size: 'الحجم',\n            terms: 'المصطلحات',\n            success: 'تم استخراج الملف، إجمالي {total}',\n            ongoing: 'جارٍ استخراج الملف... إجمالي {total}',\n            tips: 'تخطي هذه الشاشة لا يؤثر على نتائج الاستخراج',\n            others: 'متابعة الرفع',\n            result: 'الانتقال إلى صفحة تنزيل نتائج الاستخراج',\n            curProgress: 'التقدم الحالي: ',\n            refresh: 'تحديث',\n            details: 'تم تحميل {successNum} من أصل {length}',\n            start: 'بدء الاستخراج',\n            more: 'إضافة ملف',\n            skip: 'تخطي الاستخراج وإكمال الرفع.',\n            tiqu: 'بدء الاستخراج',\n            chouqu: 'بدء الاستخراج',\n        },\n        review: {\n            distribution: 'توزيع المراجعة',\n            Incomplete: 'غير مكتمل',\n            createReview: 'إنشاء مراجعة',\n            manageReview: 'إدارة المراجعات',\n            reviewDetail: 'تفاصيل المراجعة',\n            reviewId: 'رقم المراجعة',\n            reviewStatus: 'حالة المراجعة',\n            reviewName: 'اسم المراجعة',\n            reviewStartTime: 'وقت بدء المراجعة',\n            reviewCompleteTime: 'وقت انتهاء المراجعة',\n            reviewDesc: 'الإصدار: إصدار {reviewVersion}  |  رقم المراجعة: {reviewId}',\n            distribute: 'بدء المراجعة',\n            drag: 'اسحب الاتفاقية للمراجعة إلى هذه المنطقة',\n            content: 'المحتوى قيد المراجعة',\n            current: 'سجلات التوزيع الحالية',\n            history: 'السجلات التاريخية',\n            page: 'الصفحة {page}:',\n            users: 'المستخدمون المطلوب مراجعتهم',\n            message: 'رسالة',\n            modify: 'تعديل',\n            placeholder: 'يفصل بين المستخدمين المتعددين بفاصلة منقوطة \";\"',\n            submit: 'تأكيد',\n            reupload: 'إعادة رفع اتفاقية المراجعة',\n            finish: 'إنهاء المراجعة',\n            reviewSummary: 'ملخص المراجعة',\n            initiator: 'مُبادِر المراجعة',\n            versionSummary: 'ملخص الإصدار',\n            version: 'الإصدار',\n            versionOrder: 'الإصدار {version}',\n            curReviewStatus: 'حالة مراجعة الإصدار الحالي',\n            curReviewVersion: 'الإصدار الحالي',\n            curReviewPopulation: 'عدد مراجعي الإصدار الحالي',\n            curReviewStartTime: 'وقت بدء مراجعة الإصدار الحالي',\n            curReviewInitiator: 'مُبادِر مراجعة الإصدار الحالي',\n            checkComments: 'عرض تجميعي لملاحظات التعديل',\n            overview: 'نظرة سريعة على نتائج المراجعة',\n            reviewer: 'المراجع',\n            reviewResult: 'نتيجة المراجعة',\n            replyTime: 'وقت الرد',\n            agreement: 'الاتفاقية المُراجعة',\n            files: 'الاتفاقيات ذات الصلة',\n            fileName: 'اسم الاتفاقية',\n            numberOfModificationSuggestions: 'عدد اقتراحات التعديل',\n            uploadTime: 'وقت الرفع',\n            download: 'تنزيل',\n            dispatch: 'توزيع',\n            recent: 'آخر وقت مراجعة: ',\n            replyContent: 'محتوى رد المراجعة',\n            advice: 'اقتراحات تعديل الاتفاقية',\n            noIdea: 'لا توجد اقتراحات تعديل حالياً',\n            origin: 'المحتوى الأصلي:',\n            revised: 'المحتوى المعدل:',\n            suggestion: 'اقتراح التعديل:',\n            dateMark: '{name} كتب في <span style=\"color: #0988EC\">الإصدار {version}</span> بتاريخ {date}',\n            unReviewed: 'لم يتم المراجعة بعد',\n            revisionFiles: 'ملفات التعديل',\n            staffReplyAggregation: 'تجميع معلومات التعديل',\n            staffReply: 'معلومات مراجعة {name}',\n            tips: 'ملاحظة',\n            tipsContent: 'سيؤدي الانتهاء إلى إيقاف التوزيع والعمليات اللاحقة لهذه المراجعة، هل تتابع؟',\n            confirm: 'تأكيد',\n            cancel: 'إلغاء',\n            successMessage: 'تم الانتهاء',\n            PASS: 'مقبول',\n            REJECT: 'مرفوض',\n            uploadErrorMessage: 'يدعم حاليًا ملفات docx فقط',\n            successInitiated: 'تم بدء المراجعة',\n            autoDistribute: 'التوزيع الذكي',\n            requiredUsers: 'المستخدمون الذين يحتاجون إلى مراجعة',\n            contentToReview: 'المحتوى للمراجعة',\n            termDetails: 'تفاصيل المصطلح',\n            term: 'المصطلح',\n            aiDistribute: 'التوزيع الذكي AI',\n            noData: 'لا توجد بيانات متاحة',\n            docIconAlt: 'أيقونة المستند',\n            docxIconAlt: 'أيقونة DOCX',\n            pdfIconAlt: 'أيقونة PDF',\n            requiredUsersError: 'يرجى ملء المستخدمين الذين يحتاجون إلى مراجعة',\n            selectContentError: 'يرجى اختيار المحتوى للمراجعة',\n            initiateReviewSuccess: 'تم بدء المراجعة',\n            syncInitiated: 'تم بدء المزامنة',\n        },\n        contentTracing: {\n            title: 'تتبع المحتوى',\n            fieldContent: 'محتوى الحقل',\n            originalResult: 'النتيجة الأصلية',\n            contentSource: 'مصدر المحتوى',\n            page: 'الصفحة ',\n        },\n    },\n    hubblePackage: {\n        title: 'حزمتي',\n        details: 'تفاصيل الحزمة',\n        remainingPages: 'إجمالي الصفحات المتبقية',\n        pages: 'صفحات',\n        usedPages: 'المستخدمة',\n        remaining: 'المتبقي المتاح',\n        total: 'إجمالي',\n        expiryTime: 'وقت الانتهاء',\n        amount: 'العدد',\n        unitPrice: 'سعر الوحدة',\n        copy: 'نسخة',\n        words: 'ألف كلمة',\n    },\n    workspaceIndex: {\n        title: 'مساحة العمل',\n        package: 'حزم الاستخدام',\n        agreement: 'إدارة الاتفاقيات',\n        review: 'إدارة المراجعات',\n        term: 'إدارة المصطلحات',\n    },\n    agreement: {\n        title: 'إدارة الاتفاقيات',\n        exportList: 'تصدير قائمة الاتفاقيات',\n        exportAllChecked: 'Excel (جميع الحقول، الاتفاقيات المحددة)',\n        exportCurrentChecked: 'Excel (الحقول الحالية، الاتفاقيات المحددة)',\n        exportAllMatched: 'Excel (جميع الحقول، المطابقة للشروط)',\n        exportCurrentMatched: 'Excel (الحقول الحالية، المطابقة للشروط)',\n        add: 'إضافة اتفاقية',\n        upload: 'رفع اتفاقية',\n        operation: 'إجراء',\n        download: 'تنزيل الاتفاقية',\n        details: 'التفاصيل',\n        delete: 'حذف',\n        relatedTaskStatus: 'حالة المهمة المرتبطة بالاستخراج',\n        confirmDelete: 'هل تريد تأكيد حذف هذه الاتفاقية؟',\n        prompt: 'تلميح',\n        booleanYes: 'نعم',\n        booleanNo: 'لا',\n        defaultExportName: 'تصدير.xlsx',\n        taskNotStarted: 'لم تبدأ مهمة الاستخراج',\n        taskStarted: 'بدأت مهمة الاستخراج (جارٍ البحث في المحتوى)',\n        contentSearchCompleted: 'اكتمل البحث في المحتوى (جارٍ تنسيق النتائج)',\n        resultFormattingCompleted: 'اكتمل تنسيق النتائج (جارٍ مراجعة النتائج)',\n        resultVerificationCompleted: 'اكتملت مراجعة النتائج',\n    },\n    filter: {\n        filter: 'تصفية',\n        refreshExtraction: 'إعادة الاستخراج',\n        extractTerms: 'استخراج تعريفات المصطلحات',\n        refreshList: 'تحديث القائمة',\n        currentCondition: 'الاتفاقيات المعروضة تحت الشروط الحالية.',\n        when: 'عندما',\n        selectCondition: 'اختر الشرط',\n        enterCondition: 'أدخل الشرط',\n        yes: 'نعم',\n        no: 'لا',\n        addCondition: 'إضافة شرط',\n        reset: 'إعادة تعيين',\n        confirm: 'تأكيد',\n        and: 'و',\n        or: 'أو',\n        equals: 'يساوي',\n        notEquals: 'لا يساوي',\n        contains: 'يحتوي على',\n        notContains: 'لا يحتوي على',\n        greaterThan: 'أكبر من',\n        greaterThanOrEquals: 'أكبر من أو يساوي',\n        lessThan: 'أقل من',\n        lessThanOrEquals: 'أقل من أو يساوي',\n        emptyCondition: 'لا يمكن أن يكون شرط التصفية فارغًا',\n    },\n    fieldConfig: {\n        button: 'تكوين الحقول',\n        header: 'الحقول المطلوب عرضها',\n        submit: 'إنهاء',\n        cancel: 'إلغاء',\n    },\n    agreementDetail: {\n        detail: 'تفاصيل الاتفاقية',\n        add: 'إضافة اتفاقية',\n        id: 'رقم الاتفاقية',\n        file: 'ملف الاتفاقية',\n        download: 'تنزيل الاتفاقية',\n        replaceFile: 'استبدال ملف الاتفاقية',\n        uploadFile: 'رفع ملف الاتفاقية',\n        relatedExtractionStatus: 'حالة مهمة الاستخراج المرتبطة',\n        dataSource: 'مصدر البيانات',\n        yes: 'نعم',\n        no: 'لا',\n        select: 'اختر',\n        input: 'أدخل',\n        save: 'حفظ',\n        cancel: 'إلغاء',\n        page: 'الصفحة {page}',\n        addDataSource: 'إضافة مصدر بيانات',\n        pageNo: 'الصفحة ',\n        pageSuffix: '',\n        submit: 'إرسال',\n        inputDataSource: 'أدخل محتوى مصدر البيانات',\n        pageFormatError: 'يُدعم أرقام الصفحات مفصولة بفاصلة إنجليزية أو أرقام فقط',\n        confirmDelete: 'هل تريد تأكيد حذف مصدر البيانات الحالي؟',\n        tips: 'تلميح',\n        uploadSuccess: 'تم الرفع بنجاح',\n    },\n    termManagement: {\n        title: 'إدارة المصطلحات',\n        batchDelete: 'حذف جماعي',\n        import: 'استيراد مصطلحات',\n        export: 'تصدير مصطلحات',\n        add: '+ إضافة مصطلح',\n        name: 'اسم المصطلح',\n        definition: 'شرح المصطلح',\n        formatRequirement: 'متطلبات تنسيق الاستخراج',\n        dataFormat: 'تنسيق البيانات',\n        operation: 'إجراء',\n        edit: 'تعديل',\n        delete: 'حذف',\n        detail: 'تفاصيل المصطلح',\n        addTitle: 'إضافة مصطلح',\n        namePlaceholder: 'أدخل المصطلح المتخصص',\n        definitionPlaceholder: 'أدخل شرح المصطلح',\n        formatRequirementPlaceholder: 'أدخل متطلبات تنسيق استخراج المصطلح',\n        dataFormatPlaceholder: 'متطلبات تنسيق استخراج المصطلح المطلوبة',\n        cancel: 'إلغاء',\n        confirmEdit: 'تأكيد التعديل',\n        importTitle: 'استيراد مصطلحات',\n        uploadTemplate: 'رفع ملف القالب',\n        downloadTemplate: 'تنزيل ملف القالب',\n        extractType: {\n            text: 'نص',\n            longText: 'نص طويل',\n            date: 'تاريخ',\n            number: 'رقم',\n            boolean: 'نعم/لا',\n        },\n        importSuccess: 'تم الاستيراد بنجاح',\n        deleteConfirm: 'هل تريد تأكيد حذف هذا المصطلح؟',\n        prompt: 'تلميح',\n        nameEmptyError: 'اسم المصطلح لا يمكن أن يكون فارغًا',\n    },\n    agent: {\n        extractTitle: 'استخراج المعلومات',\n        riskTitle: 'محامي الذكاء الاصطناعي',\n        feedback: 'تغذية الراجِع',\n        toMini: 'انتقل إلى التطبيق المصغر للاطلاع',\n        otherContract: 'أرني المخاطر الخفية في العقود الأخرى',\n        others: 'الأخرى',\n        submit: 'انقر لإرسال',\n        autoExtract: 'يتم استخراج الخطوات التالية تلقائيًا حتى انتهاء العملية',\n        autoRisk: 'يتم تحليل الخطوات التالية تلقائيًا حتى انتهاء العملية',\n        aiGenerated: 'AI Generated - © BestSign',\n        chooseRisk: 'الرجاء تحديد الملف المراد تحليله',\n        chooseExtract: 'اختر الملف لبدء عملية الاستخراج',\n        analyzing: 'جارٍ تحليل المحتوى',\n        advice: 'جارٍ إنشاء اقتراحات التعديل',\n        options: 'جاري توليد الخيارات',\n        inputTips: 'الرجاء إدخال البيانات المطلوبة بدقة',\n        chargeTip: 'الرصيد غير كافٍ، يرجى إعادة الشحن',\n        original: 'النص الأصلي',\n        revision: 'اقتراحات التعديل',\n        diff: 'مقارنة',\n        locate: 'تحديد مكان النص الأصلي',\n        custom: 'حدد قواعد الفحص حسب الحاجة',\n        content: 'إحداثيات النص المصدر',\n        satisfy: 'أنا راضٍ بالتحليل، استمر في التحليل التالي',\n        dissatisfy: 'أنا غير راضٍ بالتحليل، أعيد التحليل',\n        selectFunc: 'الرجاء اختيار الوظيفة التي ترغب في استخدامها',\n        deepInference: 'المعاملات القانونية الذكية',\n        deepThinking: 'في حالة تفكير عميق',\n        deepThoughtCompleted: 'تم الانتهاء من التفكير العميق',\n        reJudge: 'إعادة الحكم',\n        confirm: 'تأكيد',\n        tipsContent: 'إعادة التقييم ستخصم من عدد المحاولات. هل تريد المتابعة؟',\n        useLawyer: 'استخدم محامي الذكاء',\n        interpretFinish: 'اكتمل التفسير الآلي',\n        exportPDF: 'تصدير تقرير PDF',\n        defaultExportName: 'تصدير.pdf',\n        exporting: 'جاري تصدير التقرير... الرجاء الانتظار',\n    },\n    authorize: {\n        title: 'الشروط والأحكام',\n        content: 'فعّل العقود الذكية - تحليل بالذكاء الاصطناعي يُسهّل عملك!موافقتك تمنحك تجربة مجانية',\n        cancel: 'لا، شكرًا',\n        confirm: 'موافقة والبدء',\n        contract: 'اطّلع على 《شروط استخدام منتج هابل》',\n    },\n    sealDistribute: {\n        requestSeal: 'طلب تخصيص الختم',\n        company: 'الشركة',\n        applicant: 'مقدم الطلب',\n        accountID: 'رقم الحساب',\n        submissionTime: 'الوقت',\n        status: 'الحالة',\n        agree: 'تمت الموافقة',\n        unAgree: 'تم الرفض',\n        ifAgree: 'إذا وافقت،',\n        applyTime: 'وقت ختم مقدم الطلب هو:',\n        to: 'إلى',\n        placeHolderTime: 'سنة-شهر-يوم',\n        senderCompany: 'الشركة المرسلة',\n        documentTitle: 'عنوان العقد',\n        sealApplicationScope: 'نطاق تطبيق الختم',\n        applyforSeal: 'طلب الختم',\n        reject: 'رفض',\n        approve: 'موافقة',\n    },\n    sealApproval: {\n        sealRight: 'صلاحيات الختم',\n        requestSeal: 'طلب تخصيص الختم',\n        allEntContract: 'العقود من جميع المؤسسات',\n        partEntContract: 'العقود من مؤسسات محددة: ',\n        pleaseInputRight: 'الرجاء إدخال الصلاحيات',\n        successTransfer: 'بعد نجاح التسليم،',\n        getRight: 'سيتم الحصول على الصلاحيات المذكورة أعلاه أو يمكن تحرير وتعيين صلاحيات توقيع جديدة مباشرة.',\n        signAllEntContract: 'توقيع العقود من جميع المؤسسات',\n        sign: 'توقيع',\n        sendContract: 'العقود المرسلة',\n        sealUseTime: 'فترة استخدام الختم: ',\n        currentStatus: 'الحالة الحالية: ',\n        takeBackSeal: 'استرجاع الختم',\n        agree: 'موافقة',\n        hasAgree: 'تمت الموافقة',\n        hasReject: 'تم الرفض',\n        hasDone: 'مكتمل',\n        ask: 'سوف',\n        giveYou: 'يتم تخصيص الختم لك',\n        hopeAsk: 'يأمل في',\n        hopeGive: 'تسليم الختم إلى',\n        hopeGiveYou: 'تسليم الختم ذي الصلة إليك',\n        noSettingTime: 'لا يوجد إعداد للوقت',\n        approvalSuccess: 'تمت الموافقة بنجاح',\n        getSealSuccess: 'تم الحصول على الختم بنجاح',\n    },\n    hubbleEntry: {\n        smartAdvisor: 'مستشار العقد الذكي',\n        tooltips: 'هذه الميزة غير مفعّلة. راجع مستشاري التوقيع الإلكتروني BestSign للشراء.',\n        confirm: 'نعم',\n    },\n    lang: 'ar',\n};\n\n"], "mappings": "AACA;AACA,OAAOA,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,UAAU,MAAM,sCAAsC;AAE7D,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,cAAc,MAAM,8CAA8C;AACzE,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,eAAe;EACX,GAAGR,KAAK;EACR,GAAGC,UAAU;EACb,GAAGM,cAAc;EACjBE,WAAW,EAAE,QAAQ;EACrBC,gBAAgB,EAAE,kBAAkB;EACpCC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE;IACNC,KAAK,EAAE,qBAAqB;IAC5BC,QAAQ,EAAE,gDAAgD;IAC1DC,QAAQ,EAAE,mBAAmB;IAC7BC,WAAW,EAAE,kEAAkE;IAC/EC,QAAQ,EAAE,wCAAwC;IAClDC,QAAQ,EAAE,wCAAwC;IAClDC,UAAU,EAAE,oDAAoD;IAChEC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACd,CAAC;EACDC,YAAY,EAAE;IACVC,MAAM,EAAE,+CAA+C;IACvDC,cAAc,EAAE,2CAA2C;IAC3DC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE,aAAa;IAC5BC,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,2BAA2B;IACpCC,OAAO,EAAE,gCAAgC;IACzCC,UAAU,EAAE,kCAAkC;IAC9CC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,oBAAoB;IACjCC,UAAU,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE,mCAAmC;IAChDC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,0BAA0B;IAC1CC,SAAS,EAAE,4BAA4B;IACvCC,WAAW,EAAE,6CAA6C;IAC1DC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE,0CAA0C;IACxDC,WAAW,EAAE,+CAA+C;IAC5DC,WAAW,EAAE,sBAAsB;IACnCC,SAAS,EAAE,OAAO;IAClBX,KAAK,EAAE,cAAc;IACrBY,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,kBAAkB,EAAE,iCAAiC;IACrDC,mBAAmB,EAAE,aAAa;IAClCC,UAAU,EAAE,4BAA4B;IACxCC,qBAAqB,EAAE,gCAAgC;IACvDC,aAAa,EAAE,OAAO;IACtBC,UAAU,EAAE,cAAc;IAC1BC,EAAE,EAAE,IAAI;IACRC,kBAAkB,EAAE,mDAAmD;IACvEC,mBAAmB,EAAE,8CAA8C;IACnEC,aAAa,EAAE,oDAAoD;IACnEC,MAAM,EAAE,iCAAiC;IACzCC,gBAAgB,EAAE,mBAAmB;IACrCC,cAAc,EAAE,2BAA2B;IAC3CC,oBAAoB,EAAE,iCAAiC;IACvDC,WAAW,EAAE,yBAAyB;IACtCC,YAAY,EAAE,oCAAoC;IAClDC,aAAa,EAAE,4BAA4B;IAE3CC,SAAS,EAAE,yBAAyB;IACpCC,OAAO,EAAE,oBAAoB;IAC7BC,WAAW,EAAE,uBAAuB;IACpCC,UAAU,EAAE,eAAe;IAC3BC,MAAM,EAAE,0DAA0D;IAClEC,QAAQ,EAAE,qBAAqB;IAC/BC,iBAAiB,EAAE,uBAAuB;IAC1CC,GAAG,EAAE,GAAG;IACRC,2BAA2B,EAAE,iCAAiC;IAC9DC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,kBAAkB;IAC3BC,WAAW,EAAE,6BAA6B;IAC1CC,OAAO,EAAE,8BAA8B;IACvCC,QAAQ,EAAE,2CAA2C;IACrDC,SAAS,EAAE,0HAA0H;IACrIC,SAAS,EAAE,eAAe;IAC1BC,UAAU,EAAE,wDAAwD;IACpEC,QAAQ,EAAE,UAAU;IACpBC,mBAAmB,EAAE,qCAAqC;IAC1D;IACAC,aAAa,EAAE,mDAAmD;IAClEC,mBAAmB,EAAE,8CAA8C;IACnEC,YAAY,EAAE,2BAA2B;IACzCC,YAAY,EAAE,oCAAoC;IAClDC,qBAAqB,EAAE,wCAAwC;IAC/DC,QAAQ,EAAE,gBAAgB;IAC1BC,mBAAmB,EAAE,yCAAyC;IAC9DC,sBAAsB,EAAE,mCAAmC;IAC3D;IACAC,oBAAoB,EAAE,iBAAiB;IACvCC,gBAAgB,EAAE,2BAA2B;IAC7CC,mBAAmB,EAAE,sBAAsB;IAC3CC,UAAU,EAAE,uBAAuB;IACnCC,YAAY,EAAE,aAAa;IAC3BC,UAAU,EAAE,iCAAiC;IAC7CC,WAAW,EAAE,oBAAoB;IACjCC,iBAAiB,EAAE;EACvB,CAAC;EACD,GAAGlG,IAAI;EACPmG,SAAS,EAAE;IACP3F,KAAK,EAAE,mBAAmB;IAC1B4F,YAAY,EAAE,6BAA6B;IAC3CC,cAAc,EAAE,gBAAgB;IAChCC,mBAAmB,EAAE,yBAAyB;IAC9CC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,4BAA4B;IAC1CC,OAAO,EAAE,oHAAoH;IAC7HC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,SAAS;IACdC,WAAW,EAAE,sCAAsC;IACnDC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE,oBAAoB;IAC5BC,UAAU,EAAE,iEAAiE;IAC7EC,UAAU,EAAE,mDAAmD;IAC/DlG,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE,OAAO;IAChBC,cAAc,EAAE,qDAAqD;IACrEC,SAAS,EAAE,+BAA+B;IAC1CC,SAAS,EAAE,uBAAuB;IAClCC,WAAW,EAAE,oDAAoD;IACjEC,MAAM,EAAE,kBAAkB;IAC1BC,MAAM,EAAE,oBAAoB;IAC5BC,EAAE,EAAE,OAAO;IACXC,SAAS,EAAE,wCAAwC;IACnDC,OAAO,EAAE,oDAAoD;IAC7DC,OAAO,EAAE,+BAA+B;IACxCC,YAAY,EAAE,6EAA6E;IAC3FC,iBAAiB,EAAE,gDAAgD;IACnEC,GAAG,EAAE;MACDC,aAAa,EAAE,oFAAoF;MACnGC,aAAa,EAAE,0DAA0D;MACzEC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,MAAM,EAAE;IACJ9G,aAAa,EAAE,aAAa;IAC5BC,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,SAAS;IACtBE,SAAS,EAAE,YAAY;IACvB2G,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,8BAA8B;IAC3CC,MAAM,EAAE,2CAA2C;IACnDnH,YAAY,EAAE,aAAa;IAC3BM,OAAO,EAAE,2BAA2B;IACpCF,IAAI,EAAE,eAAe;IAErBgC,aAAa,EAAE,oDAAoD;IACnEE,gBAAgB,EAAE,mBAAmB;IACrC8E,WAAW,EAAE,oBAAoB;IACjCC,kBAAkB,EAAE,8BAA8B;IAClDC,gBAAgB,EAAE,6BAA6B;IAC/CC,oBAAoB,EAAE,sCAAsC;IAC5DC,iBAAiB,EAAE,qCAAqC;IACxDC,UAAU,EAAE,mBAAmB;IAC/BC,iBAAiB,EAAE,8BAA8B;IACjDC,kBAAkB,EAAE,2BAA2B;IAC/CC,sBAAsB,EAAE;EAC5B,CAAC;EACD/I,OAAO,EAAE;IACL,GAAGA,OAAO;IACVgJ,gBAAgB,EAAE,6BAA6B;IAC/CC,WAAW,EAAE,0BAA0B;IACvCC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,SAAS;IAClBrC,MAAM,EAAE,cAAc;IACtBsC,WAAW,EAAE,qEAAqE;IAClFC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,MAAM;IACZ/H,IAAI,EAAE,QAAQ;IACdgI,OAAO,EAAE,oBAAoB;IAC7BC,sBAAsB,EAAE,gDAAgD;IACxEC,aAAa,EAAE,eAAe;IAC9BC,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE,aAAa;IACtBC,YAAY,EAAE,gBAAgB;IAC9BC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,oBAAoB;IAChCC,eAAe,EAAE,qBAAqB;IACtCC,IAAI,EAAE,SAAS;IACfC,cAAc,EAAE,6BAA6B;IAC7CC,sBAAsB,EAAE,cAAc;IACtCC,gBAAgB,EAAE,2CAA2C;IAC7DC,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,sBAAsB;IAC5BC,eAAe,EAAE,2DAA2D;IAC5EvJ,QAAQ,EAAE,QAAQ;IAClBwJ,aAAa,EAAE,4DAA4D;IAC3EC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACVC,IAAI,EAAE,SAAS;IACfC,cAAc,EAAE;EACpB,CAAC;EACD9K,IAAI,EAAE;IACF,GAAGA,IAAI;IACPA,IAAI,EAAE,UAAU;IAChB+K,gBAAgB,EAAE,cAAc;IAChCC,kBAAkB,EAAE,QAAQ;IAC5BC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,QAAQ;IACjBC,iBAAiB,EAAE,mBAAmB;IACtCC,YAAY,EAAE,oBAAoB;IAClCC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE,qBAAqB;IACnCC,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,2CAA2C;IACtDC,OAAO,EAAE,4CAA4C;IACrDC,OAAO,EAAE,gCAAgC;IACzCC,WAAW,EAAE,iCAAiC;IAC9CC,mBAAmB,EAAE,mCAAmC;IACxDC,IAAI,EAAE,aAAa;IACnBC,gBAAgB,EAAE,gBAAgB;IAClCC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,2BAA2B;IACjCC,GAAG,EAAE,WAAW;IAChBC,eAAe,EAAE,uBAAuB;IACxCC,GAAG,EAAE,EAAE;IACPC,QAAQ,EAAE,oEAAoE;IAC9EC,MAAM,EAAE,cAAc;IACtBC,IAAI,EAAE,gFAAgF;IACtFC,QAAQ,EAAE;MACN,CAAC,EAAE,oBAAoB;MACvB,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,0BAA0B;IAC3CC,SAAS,EAAE;MACP,CAAC,EAAE,kBAAkB;MACrB,CAAC,EAAE,2BAA2B;MAC9B,CAAC,EAAE,yBAAyB;MAC5B,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACPC,gBAAgB,EAAE,8BAA8B;IAChDC,aAAa,EAAE,qBAAqB;IACpCC,QAAQ,EAAE,oBAAoB;IAC9BC,yBAAyB,EAAE,qFAAqF;IAChHC,iBAAiB,EAAE,aAAa;IAChCC,kBAAkB,EAAE,yBAAyB;IAC7CC,YAAY,EAAE;MACV/C,IAAI,EAAE,SAAS;MACfgD,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE;IACd,CAAC;IACDC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,cAAc;IAC9BC,gBAAgB,EAAE,gBAAgB;IAClCC,aAAa,EAAE,eAAe;IAC9BC,iBAAiB,EAAE,0BAA0B;IAC7CC,wBAAwB,EAAE,8BAA8B;IACxDC,WAAW,EAAE;MACTC,MAAM,EAAE,YAAY;MACpBC,MAAM,EAAE,aAAa;MACrBC,MAAM,EAAE;IACZ,CAAC;IACDC,YAAY,EAAE,iBAAiB;IAC/BC,cAAc,EAAE,mEAAmE;IACnFC,yBAAyB,EAAE,uCAAuC;IAClEC,aAAa,EAAE,kBAAkB;IACjCC,aAAa,EAAE,UAAU;IACzBC,0BAA0B,EAAE,4CAA4C;IACxEC,MAAM,EAAE,gBAAgB;IACxBC,WAAW,EAAE,cAAc;IAC3BC,QAAQ,EAAE,cAAc;IACxBC,0BAA0B,EAAE,gCAAgC;IAC5DC,kBAAkB,EAAE,qBAAqB;IACzCC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,cAAc;IAClCC,eAAe,EAAE;MACbC,UAAU,EAAE,iBAAiB;MAC7BC,4BAA4B,EAAE,cAAc;MAC5CC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE;IACd,CAAC;IACDC,yBAAyB,EAAE;MACvBhP,KAAK,EAAE,wNAAwN;MAC/NiP,IAAI,EAAE,OAAO;MACbC,OAAO,EAAE,gBAAgB;MACzB3O,MAAM,EAAE;IACZ,CAAC;IACD4O,WAAW,EAAE;MACTnP,KAAK,EAAE,8FAA8F;MACrGiP,IAAI,EAAE,OAAO;MACbvI,OAAO,EAAE,OAAO;MAChBnG,MAAM,EAAE;IACZ,CAAC;IACD6O,2BAA2B,EAAE,mDAAmD;IAChFC,SAAS,EAAE,gBAAgB;IAC3BC,sBAAsB,EAAE,2CAA2C;IACnEC,MAAM,EAAE,eAAe;IACvBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,cAAc;IACzBC,QAAQ,EAAE,oBAAoB;IAC9BC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,eAAe;IAC9BC,OAAO,EAAE,gBAAgB;IACzBC,kBAAkB,EAAE,wBAAwB;IAC5CC,aAAa,EAAE,6BAA6B;IAC5CC,kBAAkB,EAAE,mBAAmB;IACvCC,gBAAgB,EAAE,iBAAiB;IACnCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,gBAAgB;IAC3BnF,WAAW,EAAE,WAAW;IACxBuC,MAAM,EAAE,QAAQ;IAChB6C,aAAa,EAAE,WAAW;IAC1BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE,iBAAiB;IAChCC,YAAY,EAAE,wBAAwB;IACtCC,kBAAkB,EAAE,2BAA2B;IAC/C;IACAC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,MAAM;IACdlG,YAAY,EAAE,WAAW;IACzBmG,MAAM,EAAE,OAAO;IACfxR,IAAI,EAAE,OAAO;IACbyR,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,qBAAqB;IACjCC,gBAAgB,EAAE,aAAa;IAC/BC,cAAc,EAAE,sBAAsB;IACtCC,KAAK,EAAE,OAAO;IACdC,mBAAmB,EAAE,8EAA8E;IACnGC,UAAU,EAAE,kBAAkB;IAC9BC,cAAc,EAAE,mBAAmB;IACnCC,UAAU,EAAE,cAAc;IAC1BC,cAAc,EAAE,yBAAyB;IACzCC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,OAAO;IACjBC,OAAO,EAAE,MAAM;IACfC,oBAAoB,EAAE,oBAAoB;IAC1CC,eAAe,EAAE,oBAAoB;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,OAAO;IACfjF,QAAQ,EAAE,OAAO;IACjBkF,cAAc,EAAE,mBAAmB;IACnCC,mBAAmB,EAAE,oBAAoB;IACzCC,SAAS,EAAE,eAAe;IAC1BC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,YAAY;IAClB7E,MAAM,EAAE,QAAQ;IAChB8E,WAAW,EAAE,eAAe;IAC5BC,kBAAkB,EAAE,mCAAmC;IACvD3F,IAAI,EAAE,UAAU;IAChB4F,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,QAAQ;IACflH,IAAI,EAAE,OAAO;IACbmH,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,UAAU;IAClBC,6BAA6B,EAAE,8BAA8B;IAC7DC,gCAAgC,EAAE,gCAAgC;IAClEC,wBAAwB,EAAE,sBAAsB;IAChDC,sBAAsB,EAAE,kCAAkC;IAC1DC,cAAc,EAAE,OAAO;IACvBC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,OAAO;IACrBC,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,YAAY;IACpBC,eAAe,EAAE,qBAAqB;IACtCC,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,wBAAwB;IACvCC,aAAa,EAAE,iBAAiB;IAChCC,KAAK,EAAE,OAAO;IACdC,qBAAqB,EAAE,0BAA0B;IACjDC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,yBAAyB;IACtCC,QAAQ,EAAE,KAAK;IACfC,gBAAgB,EAAE,kBAAkB;IACpCC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,OAAO;IAClBpO,GAAG,EAAE,SAAS;IACd0D,IAAI,EAAE,OAAO;IACb2K,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,MAAM;IACbC,cAAc,EAAE,cAAc;IAC9BC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,6CAA6C;IACzDC,UAAU,EAAE,sBAAsB;IAClCC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAE,WAAW;IACrB7J,SAAS,EAAE,MAAM;IACjB8J,WAAW,EAAE,UAAU;IACvBC,OAAO,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACRC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,SAAS;IAClBC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,mBAAmB;IAC/BR,UAAU,EAAE,gBAAgB;IAC5BS,QAAQ,EAAE,yDAAyD;IACnEC,oBAAoB,EAAE,wBAAwB;IAC9CC,UAAU,EAAE,kBAAkB;IAC9BC,cAAc,EAAE,gCAAgC;IAChDC,gBAAgB,EAAE,2CAA2C;IAC7DC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;EAEd,CAAC;EACDlG,YAAY,EAAE;IACVmG,cAAc,EAAE,mBAAmB;IACnCC,YAAY,EAAE,WAAW;IACzBC,mBAAmB,EAAE,6DAA6D;IAClF5L,YAAY,EAAE,WAAW;IACzB6L,QAAQ,EAAE,eAAe;IACzBC,eAAe,EAAE,uDAAuD;IACxEC,YAAY,EAAE,wBAAwB;IACtCC,mBAAmB,EAAE,0DAA0D;IAC/EC,UAAU,EAAE,qBAAqB;IACjCnG,kBAAkB,EAAE,oBAAoB;IACxCoG,iBAAiB,EAAE,2DAA2D;IAC9EC,SAAS,EAAE,OAAO;IAClBC,YAAY,EAAE,SAAS;IACvBC,QAAQ,EAAE,uDAAuD;IACjEC,gBAAgB,EAAE,wCAAwC;IAC1DC,sBAAsB,EAAE;EAC5B,CAAC;EACDC,QAAQ,EAAE;IACNC,YAAY,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACDC,qBAAqB,EAAE;MACnBxX,KAAK,EAAE,0CAA0C;MACjDyX,cAAc,EAAE,sFAAsF;MACtGC,SAAS,EAAE,+SAA+S;MAC1TC,SAAS,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACN/B,GAAG,EAAE,qGAAqG;MAC1GgC,IAAI,EAAE;QACF,CAAC,EAAE,wBAAwB;QAC3B,CAAC,EAAE,qJAAqJ;QACxJ,CAAC,EAAE,oDAAoD;QACvD,CAAC,EAAE,wCAAwC;QAC3C,CAAC,EAAE,wFAAwF;QAC3F,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,aAAa,EAAE;MACX9X,KAAK,EAAE,gCAAgC;MACvCoK,IAAI,EAAE,oqBAAoqB;MAC1qB2N,IAAI,EAAE;QACFC,IAAI,EAAE,aAAa;QACnBC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,6EAA6E;QACpFC,KAAK,EAAE;MACX,CAAC;MACDC,IAAI,EAAE;QACFJ,IAAI,EAAE,uBAAuB;QAC7BC,GAAG,EAAE,0CAA0C;QAC/CC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDE,IAAI,EAAE;QACFL,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,mDAAmD;QACxDC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDG,IAAI,EAAE;QACFN,IAAI,EAAE,uBAAuB;QAC7BC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,wDAAwD;QAC/DC,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDI,KAAK,EAAE;IACH/D,SAAS,EAAE;MACPgE,IAAI,EAAE;QACFC,CAAC,EAAE,GAAG;QACNC,QAAQ,EAAE;MACd;IACJ;EACJ,CAAC;EACDC,QAAQ,EAAE;IACN3Y,KAAK,EAAE,iBAAiB;IACxB4S,MAAM,EAAE,6EAA6E;IACrFgG,QAAQ,EAAE,qBAAqB;IAC/BC,cAAc,EAAE,gCAAgC;IAChDC,QAAQ,EAAE,qBAAqB;IAC/BC,cAAc,EAAE,mDAAmD;IACnEC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,gFAAgF;IAC1FC,OAAO,EAAE,2CAA2C;IACpDC,OAAO,EAAE,2CAA2C;IACpD/R,OAAO,EAAE;EACb,CAAC;EACDgS,mBAAmB,EAAE;IACjBpZ,KAAK,EAAE,UAAU;IACjBqZ,IAAI,EAAE,QAAQ;IACdC,EAAE,EAAE,aAAa;IACjBC,UAAU,EAAE,gGAAgG;IAC5GC,OAAO,EAAE,iDAAiD;IAC1DC,UAAU,EAAE,mIAAmI;IAC/IC,WAAW,EAAE,gGAAgG;IAC7GC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,EAAE,4DAA4D;IAClEC,IAAI,EAAE,+BAA+B;IACrCC,MAAM,EAAE,wBAAwB;IAChCC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE,4FAA4F;IACzGC,MAAM,EAAE,gCAAgC;IACxCC,QAAQ,EAAE,mDAAmD;IAC7DC,EAAE,EAAE,IAAI;IACRC,UAAU,EAAE,qDAAqD;IACjEC,GAAG,EAAE,oDAAoD;IACzDC,aAAa,EAAE,uDAAuD;IACtEC,eAAe,EAAE,0CAA0C;IAC3DC,eAAe,EAAE,uCAAuC;IACxDC,eAAe,EAAE,iEAAiE;IAClFC,aAAa,EAAE;EACnB,CAAC;EACDC,aAAa,EAAE;IACX3a,KAAK,EAAE,eAAe;IACtBqZ,IAAI,EAAE,QAAQ;IACdC,EAAE,EAAE,aAAa;IACjBI,WAAW,EAAE,gGAAgG;IAC7GC,WAAW,EAAE,4DAA4D;IACzEI,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,mIAAmI;IAChJJ,IAAI,EAAE,0DAA0D;IAChEC,IAAI,EAAE,+BAA+B;IACrCI,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE,wBAAwB;IAChCc,OAAO,EAAE,4BAA4B;IACrCC,WAAW,EAAE,4CAA4C;IACzDC,KAAK,EAAE,4DAA4D;IACnEC,gBAAgB,EAAE,2BAA2B;IAC7CC,MAAM,EAAE,4BAA4B;IACpCC,kBAAkB,EAAE,oDAAoD;IACxEC,GAAG,EAAE,MAAM;IACX9R,OAAO,EAAE,YAAY;IACrBoC,OAAO,EAAE,QAAQ;IACjB2P,SAAS,EAAE,uBAAuB;IAClCC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,uBAAuB;IACpCC,UAAU,EAAE,2CAA2C;IACvDC,cAAc,EAAE;EACpB,CAAC;EACDC,uBAAuB,EAAE;IACrBC,MAAM,EAAE,oBAAoB;IAC5B1U,MAAM,EAAE,gBAAgB;IACxB9G,QAAQ,EAAE,qDAAqD;IAC/DC,QAAQ,EAAE,sDAAsD;IAChEwb,aAAa,EAAE,oBAAoB;IACnCC,cAAc,EAAE,OAAO;IACvBC,KAAK,EAAE;EACX,CAAC;EACDC,kBAAkB,EAAE;IAChBtb,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE,OAAO;IAChBoV,UAAU,EAAE,eAAe;IAC3BC,iBAAiB,EAAE,sCAAsC;IACzDC,YAAY,EAAE,6EAA6E;IAC3FC,SAAS,EAAE,8BAA8B;IACzCC,YAAY,EAAE,kCAAkC;IAChDC,aAAa,EAAE;EACnB,CAAC;EACDC,WAAW,EAAE;IACTC,SAAS,EAAE,OAAO;IAClBhb,GAAG,EAAE,WAAW;IAChBib,WAAW,EAAE,4CAA4C;IACzDC,eAAe,EAAE,oEAAoE;IACrFC,cAAc,EAAE,sDAAsD;IACtEC,gBAAgB,EAAE;EACtB,CAAC;EACDC,mBAAmB,EAAE;IACjB1c,KAAK,EAAE,cAAc;IACrB2c,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,oBAAoB;IAClC9N,OAAO,EAAE;EACb,CAAC;EACD+N,mBAAmB,EAAE;IACjBC,mBAAmB,EAAE,0CAA0C;IAC/DC,eAAe,EAAE,qGAAqG;IACtHC,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE,mCAAmC;IAChD3c,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE;EACb,CAAC;EACDyW,WAAW,EAAE;IACT5c,MAAM,EAAE,OAAO;IACfmG,OAAO,EAAE,OAAO;IAChBgQ,QAAQ,EAAE,eAAe;IACzB0G,IAAI,EAAE,KAAK;IACX5I,SAAS,EAAE,OAAO;IAClB6I,QAAQ,EAAE,OAAO;IACjB7E,IAAI,EAAE,IAAI;IACV1O,IAAI,EAAE,OAAO;IACbwT,MAAM,EAAE,uBAAuB;IAC/BC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,iBAAiB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,gCAAgC;IAC1CC,QAAQ,EAAE,8BAA8B;IACxCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,iBAAiB;IAC9BhI,GAAG,EAAE,OAAO;IACZiI,aAAa,EAAE,6CAA6C;IAC5DC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE;EACd,CAAC;EACDC,SAAS,EAAE;IACPC,OAAO,EAAE,eAAe;IACxBC,gBAAgB,EAAE,gCAAgC;IAClDC,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,8DAA8D;IACxEC,KAAK,EAAE,6BAA6B;IACpCC,SAAS,EAAE,4KAA4K;IACvLC,SAAS,EAAE,iFAAiF;IAC5FC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,0CAA0C;IACpDC,WAAW,EAAE,8BAA8B;IAC3CC,UAAU,EAAE,kBAAkB;IAC9BC,cAAc,EAAE,sBAAsB;IACtCC,YAAY,EAAE,uBAAuB;IACrCC,OAAO,EAAE,gBAAgB;IACzBC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE,oBAAoB;IAC9BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,qEAAqE;IACnFC,WAAW,EAAE,gEAAgE;IAC7EC,uBAAuB,EAAE,qFAAqF;IAC9GC,YAAY,EAAE,oFAAoF;IAClGC,iBAAiB,EAAE;EACvB,CAAC;EACDC,qBAAqB,EAAE;IACnB1f,KAAK,EAAE,eAAe;IACtB2f,QAAQ,EAAE,+JAA+J;IACzKC,QAAQ,EAAE,iBAAiB;IAC3BC,WAAW,EAAE,6DAA6D;IAC1EC,mBAAmB,EAAE,uBAAuB;IAC5CC,cAAc,EAAE,WAAW;IAC3BC,iBAAiB,EAAE,gFAAgF;IACnGC,UAAU,EAAE,2BAA2B;IACvCC,kBAAkB,EAAE,2BAA2B;IAC/CC,gBAAgB,EAAE,0CAA0C;IAC5DC,sBAAsB,EAAE,oCAAoC;IAC5DC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,MAAM;IACbC,aAAa,EAAE,kBAAkB;IACjCC,WAAW,EAAE,kBAAkB;IAC/BC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,oNAAoN;IAC9NC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,iCAAiC;IAC/CC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE;MACT/gB,KAAK,EAAE,iBAAiB;MACxB+X,IAAI,EAAE,oCAAoC;MAC1CK,IAAI,EAAE,mDAAmD;MACzDC,IAAI,EAAE,sDAAsD;MAC5DC,IAAI,EAAE,gEAAgE;MACtE0I,IAAI,EAAE;IACV,CAAC;IACDC,eAAe,EAAE,4DAA4D;IAC7EC,WAAW,EAAE,sCAAsC;IACnDC,iBAAiB,EAAE,kDAAkD;IACrEC,QAAQ,EAAE,qBAAqB;IAC/BC,WAAW,EAAE;MACTC,aAAa,EAAE,0EAA0E;MACzFC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,6FAA6F;MACtGC,QAAQ,EAAE,4BAA4B;MACtCC,OAAO,EAAE,wFAAwF;MACjGC,QAAQ,EAAE,2CAA2C;MACrDC,OAAO,EAAE,2EAA2E;MACpFC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdlM,GAAG,EAAE,8OAA8O;IACnPmM,iBAAiB,EAAE,qBAAqB;IACxCC,iBAAiB,EAAE,0CAA0C;IAC7DC,iBAAiB,EAAE;EACvB,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE,aAAa;IACvBpiB,KAAK,EAAE,eAAe;IACtBqiB,eAAe,EAAE,aAAa;IAC9BC,oBAAoB,EAAE,6BAA6B;IACnDC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE,eAAe;IAC9BC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,oBAAoB;IACpC9W,GAAG,EAAE,mBAAmB;IACxB+W,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,aAAa;IACrBC,SAAS,EAAE,kEAAkE;IAC7EC,YAAY,EAAE,oBAAoB;IAClCC,YAAY,EAAE,sJAAsJ;IACpKC,YAAY,EAAE,gEAAgE;IAC9EC,OAAO,EAAE,kBAAkB;IAC3BC,MAAM,EAAE,kBAAkB;IAC1BC,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE,0CAA0C;IAClDC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,kBAAkB;IAC9BC,WAAW,EAAE,oBAAoB;IACjCC,cAAc,EAAE,cAAc;IAC9BC,gBAAgB,EAAE,gBAAgB;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,cAAc,EAAE,eAAe;IAC/BC,MAAM,EAAE,gBAAgB;IACxBC,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE,WAAW;IACvBnb,WAAW,EAAE,kHAAkH;IAC/Hob,QAAQ,EAAE,8BAA8B;IACxCC,WAAW,EAAE,uBAAuB;IACpCC,OAAO,EAAE,eAAe;IACxBC,UAAU,EAAE,gBAAgB;IAC5BnX,QAAQ,EAAE,sBAAsB;IAChCoX,SAAS,EAAE,uBAAuB;IAClCxO,GAAG,EAAE,OAAO;IACZnP,OAAO,EAAE,OAAO;IAChB4d,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,aAAa;IACxBC,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE,OAAO;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,gBAAgB,EAAE,8CAA8C;IAChEC,YAAY,EAAE,iMAAiM;IAC/MC,gBAAgB,EAAE,oBAAoB;IACtCC,gBAAgB,EAAE,eAAe;IACjCC,YAAY,EAAE,gBAAgB;IAC9BC,gBAAgB,EAAE,yBAAyB;IAC3CC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,sBAAsB;IACjCC,MAAM,EAAE,0CAA0C;IAClDC,WAAW,EAAE,4OAA4O;IACzPC,cAAc,EAAE,mIAAmI;IACnJC,YAAY,EAAE,qBAAqB;IACnC5R,KAAK,EAAE,OAAO;IACd6R,OAAO,EAAE,eAAe;IACxBC,YAAY,EAAE,mCAAmC;IACjDC,aAAa,EAAE,yCAAyC;IACxDC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,iBAAiB;IAC7BC,SAAS,EAAE,iBAAiB;IAC5BC,YAAY,EAAE,eAAe;IAC7BC,iBAAiB,EAAE,uBAAuB;IAC1CC,cAAc,EAAE,0BAA0B;IAC1CC,UAAU,EAAE,oBAAoB;IAChCC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,+CAA+C;IAClEC,kBAAkB,EAAE,qDAAqD;IACzEC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,6BAA6B;IACzCC,oBAAoB,EAAE,2CAA2C;IACjEC,eAAe,EAAE,kCAAkC;IACnDC,aAAa,EAAE,eAAe;IAC9BC,mBAAmB,EAAE,qBAAqB;IAC1CC,uBAAuB,EAAE,mBAAmB;IAC5CC,UAAU,EAAE,mBAAmB;IAC/BC,cAAc,EAAE,cAAc;IAC9BC,UAAU,EAAE,qCAAqC;IACjDC,aAAa,EAAE,yCAAyC;IACxDC,SAAS,EAAE,oCAAoC;IAC/CC,aAAa,EAAE,+BAA+B;IAC9CC,WAAW,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACTzL,KAAK,EAAE;EACX,CAAC;EACD0L,cAAc,EAAE;IACZzR,GAAG,EAAE;EACT,CAAC;EACD0R,aAAa,EAAE;IACXC,GAAG,EAAE,kBAAkB;IACvBC,OAAO,EAAE,OAAO;IAChBC,QAAQ,EAAE,cAAc;IACxBC,GAAG,EAAE,2BAA2B;IAChCC,IAAI,EAAE,uBAAuB;IAC7BrD,SAAS,EAAE,OAAO;IAClBxY,IAAI,EAAE,OAAO;IACb6N,IAAI,EAAE,sIAAsI;IAC5IiO,KAAK,EAAE,iHAAiH;IACxHC,UAAU,EAAE,gBAAgB;IAC5BhZ,OAAO,EAAE,cAAc;IACvBiZ,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE,mBAAmB;IAC1BvK,KAAK,EAAE,aAAa;IACpBwK,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,gBAAgB;IACxBC,OAAO,EAAE,gBAAgB;IACzBvE,OAAO,EAAE;EACb,CAAC;EACDwE,WAAW,EAAE;IACTpoB,KAAK,EAAE,6BAA6B;IACpCqoB,MAAM,EAAE,wBAAwB;IAChCC,SAAS,EAAE,mBAAmB;IAC9BC,YAAY,EAAE,yBAAyB;IACvCC,UAAU,EAAE,aAAa;IACzBzQ,IAAI,EAAE,8NAA8N;IACpOK,IAAI,EAAE;EACV,CAAC;EACD,GAAG/Y,OAAO;EACV,GAAGC,OAAO;EACV,GAAGK,MAAM;EACT8oB,cAAc,EAAE;IACZ1b,OAAO,EAAE,mBAAmB;IAC5BlC,YAAY,EAAE,uBAAuB;IACrC6d,QAAQ,EAAE,yBAAyB;IACnCC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE,4HAA4H;IAC1IC,MAAM,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACP/oB,KAAK,EAAE,wBAAwB;IAC/BgpB,aAAa,EAAE,4BAA4B;IAC3CC,OAAO,EAAE,YAAY;IACrBrP,IAAI,EAAE,cAAc;IACpBsP,WAAW,EAAE,+CAA+C;IAC5DC,WAAW,EAAE;EACjB,CAAC;EACDC,SAAS,EAAE;IACPC,MAAM,EAAE,YAAY;IACpBhE,SAAS,EAAE,cAAc;IACzBhT,SAAS,EAAE,aAAa;IACxByR,MAAM,EAAE,gBAAgB;IACxBiC,SAAS,EAAE,eAAe;IAC1BuD,WAAW,EAAE,gBAAgB;IAC7BC,aAAa,EAAE,iBAAiB;IAChCxL,QAAQ,EAAE,SAAS;IACnByL,QAAQ,EAAE,OAAO;IACjBzc,OAAO,EAAE,OAAO;IAChB8F,MAAM,EAAE,UAAU;IAClB8C,MAAM,EAAE,KAAK;IACb8T,SAAS,EAAE;MACPvF,WAAW,EAAE,oCAAoC;MACjDwF,iBAAiB,EAAE,+EAA+E;MAClGnjB,MAAM,EAAE,WAAW;MACnBojB,MAAM,EAAE,eAAe;MACvBjE,OAAO,EAAE,mBAAmB;MAC5BkE,IAAI,EAAE,uBAAuB;MAC7BC,GAAG,EAAE,cAAc;MACnBC,MAAM,EAAE,6BAA6B;MACrCC,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,QAAQ;MAChB3X,SAAS,EAAE,UAAU;MACrB4X,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,OAAO;MACbzM,KAAK,EAAE,WAAW;MAClBrW,OAAO,EAAE,kCAAkC;MAC3C+iB,OAAO,EAAE,sCAAsC;MAC/CvQ,IAAI,EAAE,6CAA6C;MACnDwQ,MAAM,EAAE,cAAc;MACtBtB,MAAM,EAAE,yCAAyC;MACjDuB,WAAW,EAAE,iBAAiB;MAC9BC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE,eAAe;MACtBlf,IAAI,EAAE,WAAW;MACjBmf,IAAI,EAAE,8BAA8B;MACpCC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE;IACZ,CAAC;IACDjG,MAAM,EAAE;MACJkG,YAAY,EAAE,gBAAgB;MAC9BC,UAAU,EAAE,WAAW;MACvBC,YAAY,EAAE,cAAc;MAC5BC,YAAY,EAAE,iBAAiB;MAC/BC,YAAY,EAAE,iBAAiB;MAC/BC,QAAQ,EAAE,cAAc;MACxBC,YAAY,EAAE,eAAe;MAC7BC,UAAU,EAAE,cAAc;MAC1BC,eAAe,EAAE,kBAAkB;MACnCC,kBAAkB,EAAE,qBAAqB;MACzCC,UAAU,EAAE,6DAA6D;MACzEC,UAAU,EAAE,cAAc;MAC1B3B,IAAI,EAAE,yCAAyC;MAC/C9a,OAAO,EAAE,sBAAsB;MAC/B0c,OAAO,EAAE,uBAAuB;MAChC5H,OAAO,EAAE,mBAAmB;MAC5B6H,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,6BAA6B;MACpCC,OAAO,EAAE,OAAO;MAChBld,MAAM,EAAE,OAAO;MACfmd,WAAW,EAAE,iDAAiD;MAC9D5S,MAAM,EAAE,OAAO;MACf6S,QAAQ,EAAE,4BAA4B;MACtCC,MAAM,EAAE,gBAAgB;MACxBC,aAAa,EAAE,eAAe;MAC9BC,SAAS,EAAE,kBAAkB;MAC7BC,cAAc,EAAE,cAAc;MAC9BC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,mBAAmB;MACjCC,eAAe,EAAE,4BAA4B;MAC7CC,gBAAgB,EAAE,gBAAgB;MAClCC,mBAAmB,EAAE,2BAA2B;MAChDC,kBAAkB,EAAE,+BAA+B;MACnDC,kBAAkB,EAAE,+BAA+B;MACnDC,aAAa,EAAE,6BAA6B;MAC5CC,QAAQ,EAAE,+BAA+B;MACzCC,QAAQ,EAAE,SAAS;MACnB3H,YAAY,EAAE,gBAAgB;MAC9B4H,SAAS,EAAE,UAAU;MACrBnD,SAAS,EAAE,qBAAqB;MAChCoD,KAAK,EAAE,sBAAsB;MAC7B9C,QAAQ,EAAE,eAAe;MACzB+C,+BAA+B,EAAE,sBAAsB;MACvDC,UAAU,EAAE,WAAW;MACvB9f,QAAQ,EAAE,OAAO;MACjB+f,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,kBAAkB;MAC1BC,YAAY,EAAE,mBAAmB;MACjCtlB,MAAM,EAAE,0BAA0B;MAClCulB,MAAM,EAAE,+BAA+B;MACvCC,MAAM,EAAE,iBAAiB;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE,iBAAiB;MAC7BC,QAAQ,EAAE,mFAAmF;MAC7FC,UAAU,EAAE,qBAAqB;MACjCC,aAAa,EAAE,eAAe;MAC9BC,qBAAqB,EAAE,uBAAuB;MAC9CC,UAAU,EAAE,uBAAuB;MACnC/T,IAAI,EAAE,QAAQ;MACdgU,WAAW,EAAE,6EAA6E;MAC1FlnB,OAAO,EAAE,OAAO;MAChBnG,MAAM,EAAE,OAAO;MACfstB,cAAc,EAAE,aAAa;MAC7BC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,OAAO;MACfC,kBAAkB,EAAE,4BAA4B;MAChDC,gBAAgB,EAAE,iBAAiB;MACnCC,cAAc,EAAE,eAAe;MAC/BC,aAAa,EAAE,qCAAqC;MACpDC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,gBAAgB;MAC7BC,IAAI,EAAE,SAAS;MACfC,YAAY,EAAE,kBAAkB;MAChCzK,MAAM,EAAE,sBAAsB;MAC9B0K,UAAU,EAAE,gBAAgB;MAC5BC,WAAW,EAAE,aAAa;MAC1BC,UAAU,EAAE,YAAY;MACxBC,kBAAkB,EAAE,8CAA8C;MAClEC,kBAAkB,EAAE,8BAA8B;MAClDC,qBAAqB,EAAE,iBAAiB;MACxCC,aAAa,EAAE;IACnB,CAAC;IACDC,cAAc,EAAE;MACZ/uB,KAAK,EAAE,cAAc;MACrBgvB,YAAY,EAAE,aAAa;MAC3BC,cAAc,EAAE,iBAAiB;MACjCC,aAAa,EAAE,cAAc;MAC7BzD,IAAI,EAAE;IACV;EACJ,CAAC;EACD0D,aAAa,EAAE;IACXnvB,KAAK,EAAE,OAAO;IACduqB,OAAO,EAAE,eAAe;IACxB6E,cAAc,EAAE,yBAAyB;IACzCC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,gBAAgB;IAC3BC,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE,cAAc;IAC1BC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,YAAY;IACvBza,IAAI,EAAE,MAAM;IACZ0a,KAAK,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;IACZ7vB,KAAK,EAAE,aAAa;IACpB8vB,OAAO,EAAE,eAAe;IACxBrG,SAAS,EAAE,kBAAkB;IAC7B/E,MAAM,EAAE,iBAAiB;IACzB4J,IAAI,EAAE;EACV,CAAC;EACD7E,SAAS,EAAE;IACPzpB,KAAK,EAAE,kBAAkB;IACzB+vB,UAAU,EAAE,wBAAwB;IACpCC,gBAAgB,EAAE,yCAAyC;IAC3DC,oBAAoB,EAAE,4CAA4C;IAClEC,gBAAgB,EAAE,sCAAsC;IACxDC,oBAAoB,EAAE,yCAAyC;IAC/DtG,GAAG,EAAE,eAAe;IACpBtjB,MAAM,EAAE,aAAa;IACrB6pB,SAAS,EAAE,OAAO;IAClBnjB,QAAQ,EAAE,iBAAiB;IAC3Bsd,OAAO,EAAE,UAAU;IACnB5U,MAAM,EAAE,KAAK;IACb0a,iBAAiB,EAAE,iCAAiC;IACpDC,aAAa,EAAE,kCAAkC;IACjDC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,YAAY;IAC/BC,cAAc,EAAE,wBAAwB;IACxCC,WAAW,EAAE,6CAA6C;IAC1DC,sBAAsB,EAAE,6CAA6C;IACrEC,yBAAyB,EAAE,2CAA2C;IACtEC,2BAA2B,EAAE;EACjC,CAAC;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,OAAO;IACfC,iBAAiB,EAAE,iBAAiB;IACpCC,YAAY,EAAE,2BAA2B;IACzCC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE,yCAAyC;IAC3DC,IAAI,EAAE,OAAO;IACbC,eAAe,EAAE,YAAY;IAC7BC,cAAc,EAAE,YAAY;IAC5BC,GAAG,EAAE,KAAK;IACVC,EAAE,EAAE,IAAI;IACRC,YAAY,EAAE,WAAW;IACzBC,KAAK,EAAE,aAAa;IACpBjrB,OAAO,EAAE,OAAO;IAChB3C,GAAG,EAAE,GAAG;IACRlB,EAAE,EAAE,IAAI;IACR+uB,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,SAAS;IACtBC,mBAAmB,EAAE,kBAAkB;IACvCC,QAAQ,EAAE,QAAQ;IAClBC,gBAAgB,EAAE,iBAAiB;IACnCC,cAAc,EAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACTC,MAAM,EAAE,cAAc;IACtBjK,MAAM,EAAE,sBAAsB;IAC9BrP,MAAM,EAAE,OAAO;IACfzY,MAAM,EAAE;EACZ,CAAC;EACDgyB,eAAe,EAAE;IACb1f,MAAM,EAAE,kBAAkB;IAC1BgX,GAAG,EAAE,eAAe;IACpBvQ,EAAE,EAAE,eAAe;IACnBkZ,IAAI,EAAE,eAAe;IACrBvlB,QAAQ,EAAE,iBAAiB;IAC3BwlB,WAAW,EAAE,uBAAuB;IACpCld,UAAU,EAAE,mBAAmB;IAC/Bmd,uBAAuB,EAAE,8BAA8B;IACvDC,UAAU,EAAE,eAAe;IAC3BnB,GAAG,EAAE,KAAK;IACVC,EAAE,EAAE,IAAI;IACRmB,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,KAAK;IACXvyB,MAAM,EAAE,OAAO;IACfkrB,IAAI,EAAE,eAAe;IACrBsH,aAAa,EAAE,mBAAmB;IAClCC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,EAAE;IACdja,MAAM,EAAE,OAAO;IACfka,eAAe,EAAE,0BAA0B;IAC3CC,eAAe,EAAE,yDAAyD;IAC1E7C,aAAa,EAAE,yCAAyC;IACxD1W,IAAI,EAAE,OAAO;IACbwZ,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZrzB,KAAK,EAAE,iBAAiB;IACxBszB,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,iBAAiB;IACzBrL,MAAM,EAAE,eAAe;IACvB2B,GAAG,EAAE,eAAe;IACpBxQ,IAAI,EAAE,aAAa;IACnBma,UAAU,EAAE,aAAa;IACzBC,iBAAiB,EAAE,yBAAyB;IAC5CC,UAAU,EAAE,gBAAgB;IAC5BtD,SAAS,EAAE,OAAO;IAClBxf,IAAI,EAAE,OAAO;IACb+E,MAAM,EAAE,KAAK;IACb9C,MAAM,EAAE,gBAAgB;IACxB8gB,QAAQ,EAAE,aAAa;IACvBC,eAAe,EAAE,sBAAsB;IACvCC,qBAAqB,EAAE,kBAAkB;IACzCC,4BAA4B,EAAE,oCAAoC;IAClEC,qBAAqB,EAAE,wCAAwC;IAC/DxzB,MAAM,EAAE,OAAO;IACfyzB,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,kBAAkB;IACpCC,WAAW,EAAE;MACT5b,IAAI,EAAE,IAAI;MACV6b,QAAQ,EAAE,SAAS;MACnBvqB,IAAI,EAAE,OAAO;MACbyT,MAAM,EAAE,KAAK;MACb+W,OAAO,EAAE;IACb,CAAC;IACDC,aAAa,EAAE,oBAAoB;IACnCC,aAAa,EAAE,gCAAgC;IAC/CjE,MAAM,EAAE,OAAO;IACfkE,cAAc,EAAE;EACpB,CAAC;EACDC,KAAK,EAAE;IACH/O,YAAY,EAAE,mBAAmB;IACjCgP,SAAS,EAAE,wBAAwB;IACnCC,QAAQ,EAAE,eAAe;IACzBC,MAAM,EAAE,kCAAkC;IAC1CC,aAAa,EAAE,sCAAsC;IACrD1K,MAAM,EAAE,QAAQ;IAChBpR,MAAM,EAAE,aAAa;IACrB+b,WAAW,EAAE,yDAAyD;IACtEC,QAAQ,EAAE,uDAAuD;IACjEC,WAAW,EAAE,2BAA2B;IACxCC,UAAU,EAAE,kCAAkC;IAC9CC,aAAa,EAAE,iCAAiC;IAChDC,SAAS,EAAE,oBAAoB;IAC/BxtB,MAAM,EAAE,6BAA6B;IACrCytB,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,qCAAqC;IAChDC,SAAS,EAAE,mCAAmC;IAC9CtN,QAAQ,EAAE,aAAa;IACvBuN,QAAQ,EAAE,kBAAkB;IAC5BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,wBAAwB;IAChCC,MAAM,EAAE,4BAA4B;IACpC7mB,OAAO,EAAE,sBAAsB;IAC/B8mB,OAAO,EAAE,4CAA4C;IACrDC,UAAU,EAAE,qCAAqC;IACjDC,UAAU,EAAE,8CAA8C;IAC1D9M,aAAa,EAAE,4BAA4B;IAC3C+M,YAAY,EAAE,oBAAoB;IAClCC,oBAAoB,EAAE,+BAA+B;IACrDC,OAAO,EAAE,aAAa;IACtBvvB,OAAO,EAAE,OAAO;IAChBknB,WAAW,EAAE,yDAAyD;IACtEsI,SAAS,EAAE,qBAAqB;IAChCC,eAAe,EAAE,qBAAqB;IACtCC,SAAS,EAAE,iBAAiB;IAC5B1F,iBAAiB,EAAE,WAAW;IAC9B2F,SAAS,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACPt2B,KAAK,EAAE,iBAAiB;IACxB8O,OAAO,EAAE,qFAAqF;IAC9FvO,MAAM,EAAE,WAAW;IACnBmG,OAAO,EAAE,eAAe;IACxB6vB,QAAQ,EAAE;EACd,CAAC;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,iBAAiB;IAC9Bv1B,OAAO,EAAE,QAAQ;IACjBw1B,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,OAAO;IACvB5M,MAAM,EAAE,QAAQ;IAChB6M,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,UAAU;IACnBC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,wBAAwB;IACnCC,EAAE,EAAE,KAAK;IACTC,eAAe,EAAE,aAAa;IAC9BC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE,aAAa;IAC5BC,oBAAoB,EAAE,kBAAkB;IACxCC,YAAY,EAAE,WAAW;IACzBrlB,MAAM,EAAE,KAAK;IACb8B,OAAO,EAAE;EACb,CAAC;EACDwjB,YAAY,EAAE;IACVC,SAAS,EAAE,eAAe;IAC1Bf,WAAW,EAAE,iBAAiB;IAC9BgB,cAAc,EAAE,yBAAyB;IACzCC,eAAe,EAAE,0BAA0B;IAC3CC,gBAAgB,EAAE,wBAAwB;IAC1CC,eAAe,EAAE,mBAAmB;IACpCC,QAAQ,EAAE,2FAA2F;IACrGC,kBAAkB,EAAE,+BAA+B;IACnDt4B,IAAI,EAAE,OAAO;IACbsL,YAAY,EAAE,gBAAgB;IAC9BitB,WAAW,EAAE,sBAAsB;IACnCC,aAAa,EAAE,kBAAkB;IACjCC,YAAY,EAAE,eAAe;IAC7BpB,KAAK,EAAE,QAAQ;IACfqB,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChB/d,GAAG,EAAE,KAAK;IACVge,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,iBAAiB;IAC3BC,WAAW,EAAE,2BAA2B;IACxCC,aAAa,EAAE,qBAAqB;IACpCC,eAAe,EAAE,oBAAoB;IACrCC,cAAc,EAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACTC,YAAY,EAAE,oBAAoB;IAClCnQ,QAAQ,EAAE,yEAAyE;IACnFhiB,OAAO,EAAE;EACb,CAAC;EACDoyB,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}