{"ast": null, "code": "export default {\n  CSCommon: {\n    chooseMember: 'Select member',\n    choosedMember: 'Selected member',\n    chooseRole: 'Select a role',\n    choosedRole: 'Selected role',\n    adjustDept: 'Adjustment department',\n    chooseDept: 'Select department',\n    choosedDept: 'Selected department',\n    search: 'Search for',\n    selectAll: 'Select all',\n    tip: 'Prompt',\n    warnTip: 'Please note:',\n    name: 'Name',\n    save: 'Save',\n    edit: 'Edit',\n    upload: 'Upload',\n    delete: 'Delete',\n    none: 'No',\n    pleaseInput: 'Please enter',\n    know: 'Understood',\n    done: 'To complete',\n    change: 'Change',\n    remind: 'Remind',\n    operate: 'Operate',\n    view: 'View',\n    date: 'Date',\n    loading: 'Loading',\n    saving: 'Saving',\n    submit: 'Submit',\n    admin: 'Primary administrator',\n    staff: 'Staff',\n    confirm: 'Confirm',\n    cancel: 'Cancel',\n    contract: 'Contract',\n    template: 'Template',\n    seal: 'Seal'\n  },\n  CSTips: {\n    errorTip: 'Error message',\n    serverError: 'The server has a small gap, please try again later.',\n    noneMemberChoosedTip: 'Please select a member first',\n    loginOverdue: '<PERSON><PERSON> has expired, please log in again'\n  },\n  CSSetting: {\n    admin: 'Primary administrator'\n  },\n  CSMembers: {\n    addMember: 'Add member',\n    searchTip: 'Support input account / name search'\n  },\n  CSSeals: {\n    signPwdType: 'Please enter 6 digits'\n  },\n  CSBusiness: {\n    unSort: 'Not sorted'\n  }\n};", "map": {"version": 3, "names": ["CSCommon", "chooseMember", "choosedMember", "chooseRole", "choosedRole", "adjustDept", "chooseDept", "choosedDept", "search", "selectAll", "tip", "warnTip", "name", "save", "edit", "upload", "delete", "none", "pleaseInput", "know", "done", "change", "remind", "operate", "view", "date", "loading", "saving", "submit", "admin", "staff", "confirm", "cancel", "contract", "template", "seal", "CSTips", "errorTip", "serverError", "noneMemberChoosedTip", "loginOverdue", "CSSetting", "CSMembers", "addMember", "searchTip", "CSSeals", "signPwdType", "CSBusiness", "unSort"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/console/console-en.js"], "sourcesContent": ["export default {\n    CSCommon: {\n        chooseMember: 'Select member',\n        choosedMember: 'Selected member',\n        chooseRole: 'Select a role',\n        choosedRole: 'Selected role',\n        adjustDept: 'Adjustment department',\n        chooseDept: 'Select department',\n        choosedDept: 'Selected department',\n        search: 'Search for',\n        selectAll: 'Select all',\n        tip: 'Prompt',\n        warnTip: 'Please note:',\n        name: 'Name',\n        save: 'Save',\n        edit: 'Edit',\n        upload: 'Upload',\n        delete: 'Delete',\n        none: 'No',\n        pleaseInput: 'Please enter',\n        know: 'Understood',\n        done: 'To complete',\n        change: 'Change',\n        remind: 'Remind',\n        operate: 'Operate',\n        view: 'View',\n        date: 'Date',\n        loading: 'Loading',\n        saving: 'Saving',\n        submit: 'Submit',\n        admin: 'Primary administrator',\n        staff: 'Staff',\n        confirm: 'Confirm',\n        cancel: 'Cancel',\n        contract: 'Contract',\n        template: 'Template',\n        seal: 'Seal',\n    },\n    CSTips: {\n        errorTip: 'Error message',\n        serverError: 'The server has a small gap, please try again later.',\n        noneMemberChoosedTip: 'Please select a member first',\n        loginOverdue: '<PERSON><PERSON> has expired, please log in again',\n    },\n    CSSetting: {\n        admin: 'Primary administrator',\n    },\n    CSMembers: {\n        addMember: 'Add member',\n        searchTip: 'Support input account / name search',\n    },\n    CSSeals: {\n        signPwdType: 'Please enter 6 digits',\n    },\n    CSBusiness: {\n        unSort: 'Not sorted',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,QAAQ,EAAE;IACNC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,iBAAiB;IAChCC,UAAU,EAAE,eAAe;IAC3BC,WAAW,EAAE,eAAe;IAC5BC,UAAU,EAAE,uBAAuB;IACnCC,UAAU,EAAE,mBAAmB;IAC/BC,WAAW,EAAE,qBAAqB;IAClCC,MAAM,EAAE,YAAY;IACpBC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,QAAQ;IACbC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACJC,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,qDAAqD;IAClEC,oBAAoB,EAAE,8BAA8B;IACpDC,YAAY,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACPZ,KAAK,EAAE;EACX,CAAC;EACDa,SAAS,EAAE;IACPC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACLC,WAAW,EAAE;EACjB,CAAC;EACDC,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}