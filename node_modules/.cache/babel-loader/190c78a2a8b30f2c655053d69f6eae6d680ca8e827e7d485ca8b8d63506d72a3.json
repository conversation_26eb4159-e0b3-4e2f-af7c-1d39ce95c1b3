{"ast": null, "code": "import { mapMutations, mapState } from 'vuex';\nimport Header from './components/header/index.vue';\nimport RegisterFooter from 'components/register_footer/RegisterFooter.vue';\nimport TopicSlider from './components/topicSlider/index.vue';\nexport default {\n  components: {\n    Header,\n    TopicSlider,\n    RegisterFooter\n  },\n  data() {\n    return {\n      hasPackage: false\n    };\n  },\n  computed: {\n    ...mapState('hubble', ['showPackageDialog', 'currentPackage', 'showSlider'])\n  },\n  watch: {\n    showSlider(val) {\n      if (val) {\n        this.$refs.sliderBox.style.width = '200px';\n      } else {\n        this.$refs.sliderBox.style.width = '0px';\n      }\n    }\n  },\n  methods: {\n    ...mapMutations('hubble', ['setPackage', 'togglePackageDialog']),\n    initAccount() {\n      return this.$http.post('/web/hubble/users/beta-plan-init');\n    },\n    initPackage() {\n      return this.$http('web/hubble/users/overview-plan-detail').then(res => {\n        this.setPackage(res.data);\n        if (!res.data.planType) {\n          this.togglePackageDialog(true);\n        } else {\n          this.hasPackage = true;\n        }\n      });\n    },\n    async checkPackage() {\n      this.togglePackageDialog(false);\n      await this.initPackage();\n    },\n    uploadSuccess({\n      topicId,\n      fileName\n    }) {\n      this.$refs.topicSlider.uploadSuccess({\n        topicId,\n        fileName\n      });\n    }\n  },\n  async created() {\n    await this.initAccount();\n    !this.$route.meta.isSharePage && this.initPackage();\n  }\n};", "map": {"version": 3, "names": ["mapMutations", "mapState", "Header", "<PERSON><PERSON><PERSON>er", "TopicSlider", "components", "data", "hasPackage", "computed", "watch", "showSlider", "val", "$refs", "sliderBox", "style", "width", "methods", "initAccount", "$http", "post", "initPackage", "then", "res", "setPackage", "planType", "togglePackageDialog", "checkPackage", "uploadSuccess", "topicId", "fileName", "topicSlider", "created", "$route", "meta", "isSharePage"], "sources": ["src/views/agent/index.vue"], "sourcesContent": ["<template>\n    <div class=\"hubble-page\">\n        <Header @initPackage=\"initPackage\"></Header>\n        <div class=\"hubble-page__layout\">\n            <div class=\"hubble-page__layout-slider-box\" ref=\"sliderBox\" v-if=\"$route.meta.hasSlider && hasPackage\">\n                <TopicSlider ref=\"topicSlider\" v-show=\"showSlider\"></TopicSlider>\n            </div>\n            <div class=\"hubble-page__layout-wrapper\">\n                <router-view @uploadSuccess=\"uploadSuccess\" :key=\"$route.params.topicId || ''\"></router-view>\n            </div>\n        </div>\n        <RegisterFooter class=\"login-footer\"></RegisterFooter>\n    </div>\n</template>\n<script>\nimport { mapMutations, mapState } from 'vuex';\nimport Header from './components/header/index.vue';\nimport RegisterFooter from 'components/register_footer/RegisterFooter.vue';\nimport TopicSlider from './components/topicSlider/index.vue';\nexport default {\n    components: {\n        Header,\n        TopicSlider,\n        RegisterFooter,\n    },\n    data() {\n        return {\n            hasPackage: false,\n        };\n    },\n    computed: {\n        ...mapState('hubble', ['showPackageDialog', 'currentPackage', 'showSlider']),\n    },\n    watch: {\n        showSlider(val) {\n            if (val) {\n                this.$refs.sliderBox.style.width = '200px';\n            } else {\n                this.$refs.sliderBox.style.width = '0px';\n            }\n        },\n    },\n    methods: {\n        ...mapMutations('hubble', ['setPackage', 'togglePackageDialog']),\n        initAccount() {\n            return this.$http.post('/web/hubble/users/beta-plan-init');\n        },\n        initPackage() {\n            return this.$http('web/hubble/users/overview-plan-detail').then(res => {\n                this.setPackage(res.data);\n                if (!res.data.planType) {\n                    this.togglePackageDialog(true);\n                } else {\n                    this.hasPackage = true;\n                }\n            });\n        },\n        async checkPackage() {\n            this.togglePackageDialog(false);\n            await this.initPackage();\n        },\n        uploadSuccess({ topicId, fileName }) {\n            this.$refs.topicSlider.uploadSuccess({ topicId, fileName });\n        },\n    },\n    async created() {\n        await this.initAccount();\n        !this.$route.meta.isSharePage && this.initPackage();\n    },\n};\n</script>\n<style lang=\"scss\">\n.hubble-page{\n    min-width: 1200px;\n    height: calc(100vh - 35px);\n    display: flex;\n    flex-direction: column;\n    background: #f8f8f8;\n    &__layout{\n        flex: 1;\n        display: flex;\n        height: calc(100vh - 60px - 35px);\n        overflow: hidden;\n        &-slider{\n            width: 200px;\n            &-box{\n                overflow-x: hidden;\n                transition: width 0.5s ease;\n                background: #fff;\n                box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);\n                position: relative;\n                z-index: 10;\n            }\n        }\n        &-wrapper{\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            overflow: hidden;\n        }\n    }\n    &__content{\n        display: flex;\n        flex: 1;\n        height: calc(100vh - 60px - 35px);\n        background: #f8f8f8;\n        &-document{\n            min-width: 600px;\n            height: 100%;\n            flex: 1 1 0%;\n        }\n        &-chat{\n            width: 40%;\n            min-width: 450px;\n            box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);\n        }\n    }\n    .el-dialog{\n        border-radius: 4px;\n        overflow: hidden;\n        &__header{\n            padding: 0 30px;\n            line-height: 50px;\n            border-bottom: 1px solid #eee;\n        }\n        &__close{\n            position: absolute;\n            top: 16px;\n            right: 20px;\n        }\n        &__title{\n            font-weight: normal;\n        }\n    }\n}\n</style>\n"], "mappings": "AAeA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,MAAA;AACA,OAAAC,cAAA;AACA,OAAAC,WAAA;AACA;EACAC,UAAA;IACAH,MAAA;IACAE,WAAA;IACAD;EACA;EACAG,KAAA;IACA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAP,QAAA;EACA;EACAQ,KAAA;IACAC,WAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,KAAA;MACA;QACA,KAAAH,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,GAAAhB,YAAA;IACAiB,YAAA;MACA,YAAAC,KAAA,CAAAC,IAAA;IACA;IACAC,YAAA;MACA,YAAAF,KAAA,0CAAAG,IAAA,CAAAC,GAAA;QACA,KAAAC,UAAA,CAAAD,GAAA,CAAAhB,IAAA;QACA,KAAAgB,GAAA,CAAAhB,IAAA,CAAAkB,QAAA;UACA,KAAAC,mBAAA;QACA;UACA,KAAAlB,UAAA;QACA;MACA;IACA;IACA,MAAAmB,aAAA;MACA,KAAAD,mBAAA;MACA,WAAAL,WAAA;IACA;IACAO,cAAA;MAAAC,OAAA;MAAAC;IAAA;MACA,KAAAjB,KAAA,CAAAkB,WAAA,CAAAH,aAAA;QAAAC,OAAA;QAAAC;MAAA;IACA;EACA;EACA,MAAAE,QAAA;IACA,WAAAd,WAAA;IACA,MAAAe,MAAA,CAAAC,IAAA,CAAAC,WAAA,SAAAd,WAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}