{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hubble-page\"\n  }, [_c(\"Header\", {\n    on: {\n      initPackage: _vm.initPackage\n    }\n  }), _c(\"div\", {\n    staticClass: \"hubble-page__layout\"\n  }, [_vm.$route.meta.hasSlider && _vm.hasPackage ? _c(\"div\", {\n    ref: \"sliderBox\",\n    staticClass: \"hubble-page__layout-slider-box\"\n  }, [_c(\"TopicSlider\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showSlider,\n      expression: \"showSlider\"\n    }],\n    ref: \"topicSlider\"\n  })], 1) : _vm._e(), _c(\"div\", {\n    staticClass: \"hubble-page__layout-wrapper\"\n  }, [_c(\"router-view\", {\n    key: _vm.$route.params.topicId || \"\",\n    on: {\n      uploadSuccess: _vm.uploadSuccess\n    }\n  })], 1)]), _c(\"RegisterFooter\", {\n    staticClass: \"login-footer\"\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "initPackage", "$route", "meta", "hasSlider", "hasPackage", "ref", "directives", "name", "rawName", "value", "showSlider", "expression", "_e", "key", "params", "topicId", "uploadSuccess", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hubble-page\" },\n    [\n      _c(\"Header\", { on: { initPackage: _vm.initPackage } }),\n      _c(\"div\", { staticClass: \"hubble-page__layout\" }, [\n        _vm.$route.meta.hasSlider && _vm.hasPackage\n          ? _c(\n              \"div\",\n              {\n                ref: \"sliderBox\",\n                staticClass: \"hubble-page__layout-slider-box\",\n              },\n              [\n                _c(\"TopicSlider\", {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.showSlider,\n                      expression: \"showSlider\",\n                    },\n                  ],\n                  ref: \"topicSlider\",\n                }),\n              ],\n              1\n            )\n          : _vm._e(),\n        _c(\n          \"div\",\n          { staticClass: \"hubble-page__layout-wrapper\" },\n          [\n            _c(\"router-view\", {\n              key: _vm.$route.params.topicId || \"\",\n              on: { uploadSuccess: _vm.uploadSuccess },\n            }),\n          ],\n          1\n        ),\n      ]),\n      _c(\"RegisterFooter\", { staticClass: \"login-footer\" }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,QAAQ,EAAE;IAAEG,EAAE,EAAE;MAAEC,WAAW,EAAEL,GAAG,CAACK;IAAY;EAAE,CAAC,CAAC,EACtDJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAChDH,GAAG,CAACM,MAAM,CAACC,IAAI,CAACC,SAAS,IAAIR,GAAG,CAACS,UAAU,GACvCR,EAAE,CACA,KAAK,EACL;IACES,GAAG,EAAE,WAAW;IAChBP,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,aAAa,EAAE;IAChBU,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEd,GAAG,CAACe,UAAU;MACrBC,UAAU,EAAE;IACd,CAAC,CACF;IACDN,GAAG,EAAE;EACP,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDV,GAAG,CAACiB,EAAE,CAAC,CAAC,EACZhB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA8B,CAAC,EAC9C,CACEF,EAAE,CAAC,aAAa,EAAE;IAChBiB,GAAG,EAAElB,GAAG,CAACM,MAAM,CAACa,MAAM,CAACC,OAAO,IAAI,EAAE;IACpChB,EAAE,EAAE;MAAEiB,aAAa,EAAErB,GAAG,CAACqB;IAAc;EACzC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFpB,EAAE,CAAC,gBAAgB,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,CACtD,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAImB,eAAe,GAAG,EAAE;AACxBvB,MAAM,CAACwB,aAAa,GAAG,IAAI;AAE3B,SAASxB,MAAM,EAAEuB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}