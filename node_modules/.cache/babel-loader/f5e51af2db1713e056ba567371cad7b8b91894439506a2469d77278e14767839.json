{"ast": null, "code": "export default {\n  CSCommon: {\n    chooseMember: 'اختيار عضو',\n    choosedMember: 'الأعضاء المختارون',\n    chooseRole: 'اختيار دور',\n    choosedRole: 'الأدوار المختارة',\n    adjustDept: 'تعديل القسم',\n    chooseDept: 'اختيار قسم',\n    choosedDept: 'الأقسام المختارة',\n    search: 'بحث',\n    selectAll: 'تحديد الكل',\n    tip: 'تنبيه',\n    warnTip: 'تنبيه ودي',\n    name: 'الاسم',\n    save: 'حفظ',\n    edit: 'تحرير',\n    upload: 'رفع',\n    delete: 'حذف',\n    none: 'لا شيء',\n    pleaseInput: 'يرجى الإدخال',\n    know: 'فهمت',\n    done: 'تم',\n    change: 'تغيير',\n    remind: 'تذكير',\n    operate: 'إجراء',\n    view: 'عرض',\n    date: 'التاريخ',\n    loading: 'جاري التحميل',\n    saving: 'جاري الحفظ',\n    submit: 'تقديم',\n    admin: 'المدير الرئيسي',\n    staff: 'موظف',\n    confirm: 'تأكيد',\n    cancel: 'إلغاء',\n    contract: 'عقد',\n    template: 'نموذج',\n    seal: 'ختم'\n  },\n  CSTips: {\n    errorTip: 'رسالة خطأ',\n    serverError: 'الخادم غير متوفر مؤقتاً، يرجى المحاولة لاحقاً',\n    noneMemberChoosedTip: 'يرجى اختيار عضو أولاً',\n    loginOverdue: 'انتهت صلاحية تسجيل الدخول، يرجى إعادة تسجيل الدخول'\n  },\n  CSSetting: {\n    admin: 'المدير الرئيسي'\n  },\n  CSMembers: {\n    addMember: 'إضافة عضو',\n    searchTip: 'يمكن البحث باستخدام الحساب/الاسم'\n  },\n  CSSeals: {\n    signPwdType: 'يرجى إدخال 6 أرقام'\n  },\n  CSBusiness: {\n    unSort: 'غير مصنف'\n  }\n};", "map": {"version": 3, "names": ["CSCommon", "chooseMember", "choosedMember", "chooseRole", "choosedRole", "adjustDept", "chooseDept", "choosedDept", "search", "selectAll", "tip", "warnTip", "name", "save", "edit", "upload", "delete", "none", "pleaseInput", "know", "done", "change", "remind", "operate", "view", "date", "loading", "saving", "submit", "admin", "staff", "confirm", "cancel", "contract", "template", "seal", "CSTips", "errorTip", "serverError", "noneMemberChoosedTip", "loginOverdue", "CSSetting", "CSMembers", "addMember", "searchTip", "CSSeals", "signPwdType", "CSBusiness", "unSort"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/console/console-ar.js"], "sourcesContent": ["export default {\n    CSCommon: {\n        chooseMember: 'اختيار عضو',\n        choosedMember: 'الأعضاء المختارون',\n        chooseRole: 'اختيار دور',\n        choosedRole: 'الأدوار المختارة',\n        adjustDept: 'تعديل القسم',\n        chooseDept: 'اختيار قسم',\n        choosedDept: 'الأقسام المختارة',\n        search: 'بحث',\n        selectAll: 'تحديد الكل',\n        tip: 'تنبيه',\n        warnTip: 'تنبيه ودي',\n        name: 'الاسم',\n        save: 'حفظ',\n        edit: 'تحرير',\n        upload: 'رفع',\n        delete: 'حذف',\n        none: 'لا شيء',\n        pleaseInput: 'يرجى الإدخال',\n        know: 'فهمت',\n        done: 'تم',\n        change: 'تغيير',\n        remind: 'تذكير',\n        operate: 'إجراء',\n        view: 'عرض',\n        date: 'التاريخ',\n        loading: 'جاري التحميل',\n        saving: 'جاري الحفظ',\n        submit: 'تقديم',\n        admin: 'المدير الرئيسي',\n        staff: 'موظف',\n        confirm: 'تأكيد',\n        cancel: 'إلغاء',\n        contract: 'عقد',\n        template: 'نموذج',\n        seal: 'ختم',\n    },\n    CSTips: {\n        errorTip: 'رسالة خطأ',\n        serverError: 'الخادم غير متوفر مؤقتاً، يرجى المحاولة لاحقاً',\n        noneMemberChoosedTip: 'يرجى اختيار عضو أولاً',\n        loginOverdue: 'انتهت صلاحية تسجيل الدخول، يرجى إعادة تسجيل الدخول',\n    },\n    CSSetting: {\n        admin: 'المدير الرئيسي',\n    },\n    CSMembers: {\n        addMember: 'إضافة عضو',\n        searchTip: 'يمكن البحث باستخدام الحساب/الاسم',\n    },\n    CSSeals: {\n        signPwdType: 'يرجى إدخال 6 أرقام',\n    },\n    CSBusiness: {\n        unSort: 'غير مصنف',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,QAAQ,EAAE;IACNC,YAAY,EAAE,YAAY;IAC1BC,aAAa,EAAE,mBAAmB;IAClCC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,OAAO;IACZC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,OAAO;IAChBC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,OAAO;IAChBC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACJC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,+CAA+C;IAC5DC,oBAAoB,EAAE,uBAAuB;IAC7CC,YAAY,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACPZ,KAAK,EAAE;EACX,CAAC;EACDa,SAAS,EAAE;IACPC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACLC,WAAW,EAAE;EACjB,CAAC;EACDC,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}