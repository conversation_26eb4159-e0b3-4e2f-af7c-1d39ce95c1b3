{"ast": null, "code": "export default {\n  docSlider: {\n    linkContractMap: {\n      notSupportFuctionTip: '会社がこの機能を有効にしていない場合は、カスタマー サービスに連絡して有効にすることができます。',\n      dissolveContractTip: '确定要解除关联合同',\n      inputContractNum: '请先输入合同编号',\n      linkSuccess: '关联成功',\n      linkExist: '合同关联已存在'\n    }\n  },\n  docContentTable: {\n    catchMap: {\n      download: '下载',\n      reject: '拒签',\n      revoke: '撤销',\n      delete: '删除',\n      cantOperate: '无法{operate}合同',\n      hybridNetHeader: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。',\n      hybridNetMsg: '建议您：检查网络是否正常',\n      checkNet: '请检查网络是否正常',\n      hybridNotConnect: '原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',\n      hybridSuggest: '建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行'\n    },\n    confirm: '确定',\n    searchAll: '全选',\n    isCheckingNet: '正在检查混合云网络环境',\n    transferSucess: '转交成功'\n  },\n  docDialog: {\n    confirm: '确认',\n    cancel: '取消',\n    notCliam: '合同未被认领',\n    chooseNewOwner: '请选择新持有人',\n    newOwner: '新持有人',\n    originOwner: '原持有人',\n    contractTransfer: '合同转交'\n  }\n};", "map": {"version": 3, "names": ["doc<PERSON>lider", "linkContractMap", "notSupportFuctionTip", "dissolveContractTip", "inputContractNum", "linkSuccess", "linkExist", "docContentTable", "catchMap", "download", "reject", "revoke", "delete", "cantOperate", "hybridNetHeader", "hybridNetMsg", "checkNet", "hybridNotConnect", "hybridSuggest", "confirm", "searchAll", "isCheckingNet", "transferSucess", "docDialog", "cancel", "notCliam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new<PERSON>wner", "origin<PERSON><PERSON>er", "contractTransfer"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/docList/docList-ja.js"], "sourcesContent": ["export default {\n    docSlider: {\n        linkContractMap: {\n            notSupportFuctionTip: '会社がこの機能を有効にしていない場合は、カスタマー サービスに連絡して有効にすることができます。',\n            dissolveContractTip: '确定要解除关联合同',\n            inputContractNum: '请先输入合同编号',\n            linkSuccess: '关联成功',\n            linkExist: '合同关联已存在',\n        },\n    },\n    docContentTable: {\n        catchMap: {\n            download: '下载',\n            reject: '拒签',\n            revoke: '撤销',\n            delete: '删除',\n            cantOperate: '无法{operate}合同',\n            hybridNetHeader: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。',\n            hybridNetMsg: '建议您：检查网络是否正常',\n            checkNet: '请检查网络是否正常',\n            hybridNotConnect: '原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',\n            hybridSuggest: '建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行',\n        },\n        confirm: '确定',\n        searchAll: '全选',\n        isCheckingNet: '正在检查混合云网络环境',\n        transferSucess: '转交成功',\n    },\n    docDialog: {\n        confirm: '确认',\n        cancel: '取消',\n        notCliam: '合同未被认领',\n        chooseNewOwner: '请选择新持有人',\n        newOwner: '新持有人',\n        originOwner: '原持有人',\n        contractTransfer: '合同转交',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,SAAS,EAAE;IACPC,eAAe,EAAE;MACbC,oBAAoB,EAAE,kDAAkD;MACxEC,mBAAmB,EAAE,WAAW;MAChCC,gBAAgB,EAAE,UAAU;MAC5BC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACf;EACJ,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE;MACNC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,eAAe;MAC5BC,eAAe,EAAE,0CAA0C;MAC3DC,YAAY,EAAE,cAAc;MAC5BC,QAAQ,EAAE,WAAW;MACrBC,gBAAgB,EAAE,wCAAwC;MAC1DC,aAAa,EAAE;IACnB,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,aAAa;IAC5BC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPJ,OAAO,EAAE,IAAI;IACbK,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,QAAQ;IAClBC,cAAc,EAAE,SAAS;IACzBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,MAAM;IACnBC,gBAAgB,EAAE;EACtB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}