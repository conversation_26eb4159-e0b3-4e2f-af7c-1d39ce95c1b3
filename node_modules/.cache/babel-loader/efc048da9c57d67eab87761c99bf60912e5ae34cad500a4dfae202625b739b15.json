{"ast": null, "code": "export default {\n  CSCommon: {\n    chooseMember: '选择成员',\n    choosedMember: '已选成员',\n    chooseRole: '选择角色',\n    choosedRole: '已选角色',\n    adjustDept: '调整部门',\n    chooseDept: '选择部门',\n    choosedDept: '已选部门',\n    search: '搜索',\n    selectAll: '全选',\n    tip: '提示',\n    warnTip: '温馨提示',\n    name: '姓名',\n    save: '保存',\n    edit: '编辑',\n    upload: '上传',\n    delete: '删除',\n    none: '无',\n    pleaseInput: '请输入',\n    know: '知道了',\n    done: '完成',\n    change: '更换',\n    remind: '提醒',\n    operate: '操作',\n    view: '查看',\n    date: '日期',\n    loading: '加载中',\n    saving: '正在保存',\n    submit: '提交',\n    admin: '主管理员',\n    staff: '员工',\n    confirm: '确定',\n    cancel: '取消',\n    contract: '合同',\n    template: '模板',\n    seal: '印章'\n  },\n  CSTips: {\n    errorTip: '错误提示',\n    serverError: '服务器开了点小差，请稍后再试',\n    noneMemberChoosedTip: '请先选择成员',\n    loginOverdue: '登录已失效，请重新登录'\n  },\n  CSSetting: {\n    admin: '主管理员'\n  },\n  CSMembers: {\n    addMember: '添加成员',\n    searchTip: '支持输入账号/姓名搜索'\n  },\n  CSSeals: {\n    signPwdType: '请输入6位数字'\n  },\n  CSBusiness: {\n    unSort: '未分类'\n  }\n};", "map": {"version": 3, "names": ["CSCommon", "chooseMember", "choosedMember", "chooseRole", "choosedRole", "adjustDept", "chooseDept", "choosedDept", "search", "selectAll", "tip", "warnTip", "name", "save", "edit", "upload", "delete", "none", "pleaseInput", "know", "done", "change", "remind", "operate", "view", "date", "loading", "saving", "submit", "admin", "staff", "confirm", "cancel", "contract", "template", "seal", "CSTips", "errorTip", "serverError", "noneMemberChoosedTip", "loginOverdue", "CSSetting", "CSMembers", "addMember", "searchTip", "CSSeals", "signPwdType", "CSBusiness", "unSort"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/console/console-zh.js"], "sourcesContent": ["export default {\n    CSCommon: {\n        chooseMember: '选择成员',\n        choosedMember: '已选成员',\n        chooseRole: '选择角色',\n        choosedRole: '已选角色',\n        adjustDept: '调整部门',\n        chooseDept: '选择部门',\n        choosedDept: '已选部门',\n        search: '搜索',\n        selectAll: '全选',\n        tip: '提示',\n        warnTip: '温馨提示',\n        name: '姓名',\n        save: '保存',\n        edit: '编辑',\n        upload: '上传',\n        delete: '删除',\n        none: '无',\n        pleaseInput: '请输入',\n        know: '知道了',\n        done: '完成',\n        change: '更换',\n        remind: '提醒',\n        operate: '操作',\n        view: '查看',\n        date: '日期',\n        loading: '加载中',\n        saving: '正在保存',\n        submit: '提交',\n        admin: '主管理员',\n        staff: '员工',\n        confirm: '确定',\n        cancel: '取消',\n        contract: '合同',\n        template: '模板',\n        seal: '印章',\n    },\n    CSTips: {\n        errorTip: '错误提示',\n        serverError: '服务器开了点小差，请稍后再试',\n        noneMemberChoosedTip: '请先选择成员',\n        loginOverdue: '登录已失效，请重新登录',\n    },\n    CSSetting: {\n        admin: '主管理员',\n    },\n    CSMembers: {\n        addMember: '添加成员',\n        searchTip: '支持输入账号/姓名搜索',\n    },\n    CSSeals: {\n        signPwdType: '请输入6位数字',\n    },\n    CSBusiness: {\n        unSort: '未分类',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,QAAQ,EAAE;IACNC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,GAAG;IACTC,WAAW,EAAE,KAAK;IAClBC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACJC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,oBAAoB,EAAE,QAAQ;IAC9BC,YAAY,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACPZ,KAAK,EAAE;EACX,CAAC;EACDa,SAAS,EAAE;IACPC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACLC,WAAW,EAAE;EACjB,CAAC;EACDC,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}