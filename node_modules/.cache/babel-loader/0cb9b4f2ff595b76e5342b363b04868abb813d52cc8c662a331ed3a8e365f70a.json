{"ast": null, "code": "export default {\n  ssoLoginConfig: {\n    notBelongToEntTip: 'تحتاج إلى إعادة تسجيل الدخول إلى منصة BestSign لإرسال العقد (أو إدارة القوالب)',\n    operationStep: {\n      one: 'الخطوة الأولى: بعد النقر على متابعة، العودة إلى صفحة تسجيل الدخول',\n      two: 'الخطوة الثانية: أدخل كلمة المرور للدخول إلى منصة BestSign',\n      three: 'الخطوة الثالثة: إرسال العقد (أو إدارة القوالب)'\n    },\n    continue: 'متابعة',\n    cancel: 'إلغاء',\n    tip: 'تنبيه'\n  },\n  sign: {\n    sealLabelsTip: 'تحتاج إلى وضع {sealLabelslen} ختم على العقد. سيقوم {personStr} بوضع {otherSealLen} أختام، والـ {mySealLen} المتبقية ستقوم أنت بختمها شخصياً. الأختام المطلوبة معروضة على الصفحة. يرجى التأكيد للمتابعة.',\n    continue: 'متابعة',\n    nonMainlandCARenewalTip: 'بعد طلب التجديد، سيرفض النظام تلقائياً نتيجة التحقق الأصلية. يرجى إكمال التحقق في أقرب وقت.',\n    reselect: 'إعادة الاختيار',\n    approvalFeatures: {\n      dialogTitle: 'تقديم الميزات الجديدة',\n      understand: 'فهمت',\n      feature1: 'تعليق توضيحي على النص',\n      feature2: 'تمييز الحقول',\n      tip1: 'انقر على الزر لتمييز جميع \"حقول محتوى القالب\" في العقد لتسهيل التقاط المعلومات الرئيسية.',\n      tip2: 'انقر على زر التلميح في الأسفل لتفعيل تمييز حقول محتوى القالب.',\n      tip3: 'من خلال التمييز، يمكنك تحديد موقع حقول تعبئة المحتوى في العقد بسرعة وإكمال الموافقة بكفاءة.',\n      tip4: 'اضغط مع الاستمرار على الماوس لتحديد فقرة ثم حرر الماوس، انقر على زر التعليق لإضافة نص تعليق توضيحي، بعد الانتهاء انقر على تعديل أو حذف. يمكن عرض محتوى التعليق في صفحة تفاصيل العقد - سجل العمليات الداخلية للشركة.',\n      tip5: 'الخطوة الأولى: حدد حقل النص المراد التعليق عليه، أضف التعليق;',\n      tip6: 'الخطوة الثانية: انقر لتحرير أو حذف التعليق.',\n      annotate: 'تعليق',\n      delete: 'حذف',\n      edit: 'تعديل',\n      operateTitle: 'إضافة تعليق للموافقة',\n      placeholder: 'لا يتجاوز 255 حرفاً'\n    },\n    needRemark: 'لا تزال بحاجة إلى إضافة ملاحظة',\n    notNeedRemark: 'لا تحتاج إلى إضافة ملاحظة',\n    switchToReceiver: 'تم التبديل إلى {receiver}',\n    notAddEntTip: 'المستخدم الحالي ليس عضواً في هذه المؤسسة، يرجى الاتصال بالمدير الرئيسي للانضمام إلى المؤسسة.',\n    contractPartiesYouChoose: 'يمكنك اختيار الأطراف المتعاقدة:',\n    contractPartyFilled: 'الطرف المتعاقد الذي حدده المرسل هو:',\n    certifyOtherCompanies: 'التحقق من مؤسسات أخرى',\n    youCanAlso: 'يمكنك أيضاً:',\n    needVerification: 'تحتاج إلى التحقق من هويتك قبل التوقيع',\n    prompt: 'تنبيه',\n    submit: 'تأكيد',\n    cancel: 'إلغاء',\n    sign: 'توقيع الآن',\n    addSeal: 'يرجى تسجيل الدخول إلى موقع BestSign باستخدام الكمبيوتر لإضافة ختم',\n    noSealAvailable: 'عذراً، ليس لديك أختام متاحة حالياً، يرجى الاتصال بمدير المؤسسة لإضافة ختم وتفويضه.',\n    memberNoSealAvailable: 'لا توجد أختام متاحة حالياً، يرجى الاتصال بالمدير للتكوين قبل التوقيع. أو اتصل بالمدير الرئيسي شخصياً للتكوين.',\n    noticeAdminFoSeal: 'إرسال إشعار للمدير',\n    requestSomeone: 'طلب تحقق شخص آخر',\n    requestOthersToContinue: 'إخطار المدير لاستكمال التحقق من الهوية',\n    requestOthersToContinueSucceed: 'تم إرسال إشعار إلى المدير',\n    requestSomeoneList: 'طلب من الأشخاص التاليين إكمال التحقق من الهوية:',\n    electronicSeal: 'الختم الإلكتروني',\n    changeTheSeal: 'لا تريد استخدام هذا الختم؟ يمكنك تغييره بعد التحقق من الهوية',\n    goToVerify: 'اذهب للتحقق من الهوية',\n    noSealToChoose: 'لا توجد أختام متاحة للتبديل، إذا كنت بحاجة إلى إدارة الأختام، يرجى التحقق من الهوية أولاً',\n    goVerify: 'اذهب للتحقق',\n    goToVerifyEnt: 'اذهب للتحقق من المؤسسة',\n    digitalCertificateTip: 'BestSign يقوم بتفعيل شهادتك الرقمية',\n    signDes: 'أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n    signAgain: 'متابعة التوقيع',\n    send: 'إرسال',\n    person: 'شخصي',\n    ent: 'مؤسسة',\n    entName: 'اسم المؤسسة',\n    account: 'الحساب',\n    accountPH: 'الهاتف أو البريد الإلكتروني',\n    approved: 'موافق',\n    signVerification: 'التحقق من التوقيع',\n    cannotReview: 'لا يمكن عرض العقد',\n    connectFail: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود.',\n    connectFailTip: 'يمكنك محاولة الحلول التالية:',\n    connectFailTip1: '1. تحديث الصفحة.',\n    connectFailTip2: '2. انتظر بصبر وحاول مرة أخرى لاحقاً. قد يكون السبب هو حدوث خلل في الخادم الذي نشرته المؤسسة المرسلة، ويحتاج فريق تكنولوجيا المعلومات إلى وقت لإعادة تشغيل الخادم.',\n    connectFailTip3: '3. هل أكدت المؤسسة المرسلة أنك تحتاج إلى استخدام شبكة WiFi محددة للوصول؟ إذا كان هناك مثل هذا التوضيح، فأنت بحاجة إلى تغيير الشبكة المتصلة بهاتفك أو جهاز الكمبيوتر.',\n    personalMaterials: 'يطلب المرسل منك توفير مزيد من مواد التحقق',\n    noSupportface: 'يطلب مُنشئ العقد التوقيع بالتعرف على الوجه، ولكن لا يتوفر التوقيع بالتعرف على الوجه لغير المقيمين في البر الرئيسي، يرجى الاتصال بالمُنشئ لتعديل متطلبات التوقيع',\n    lackEntName: 'يرجى إدخال اسم المؤسسة',\n    errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',\n    noticeAdmin: 'طلب الانضمام',\n    signDone: 'اكتمل التوقيع',\n    signDoneTip: 'لقد وقعت هذا العقد',\n    approveDone: 'اكتملت الموافقة',\n    approveDoneTip: 'لقد وافقت على هذا العقد',\n    completeSign: 'يرجى النقر على \"موقع الختم\" أو \"موقع التوقيع\" لإكمال التوقيع',\n    fillFirst: 'يرجى ملء محتوى العقد في مربع الإدخال أولاً',\n    stillSignTip: 'بعد توقيعك على {alias} هذا، قد يقوم الموقعون الآخرون بتغيير محتوى {alias}، هل تريد المتابعة؟',\n    signHighLightTip: 'يوجد {count} موقع في {alias} يمكن إضافته أو تعديله',\n    riskDetails: 'تفاصيل المخاطر',\n    noviewDifference: 'نظراً لأن المُنشئ قد فعّل وظيفة السماح للموقعين بملء الحقول الثابتة في {alias}، فقد يقوم الموقعون الآخرون بتغيير محتوى العقد الذي حدده المُنشئ. لا يقوم BestSign بمراجعة الاختلافات في المحتوى بين النسخة قبل التوقيع والنسخة السارية. عند اكتمال توقيعك على {alias}، يُعتبر أنك توافق على إضافة أو تعديل محتوى الحقول الثابتة في {alias} من قبل الموقعين الآخرين، وتقر بالنسخة السارية بعد اكتمال توقيع جميع الأطراف على {alias}.\\n' + 'إذا كنت لا توافق على السماح للموقعين الآخرين بتغيير حقول {alias} بعد توقيعك، يمكنك رفض هذا التوقيع والتفاوض مع المرسل (أي مطالبة المُنشئ بإغلاق وظيفة \"ملء الحقول من قبل الموقع\" لتجنب المخاطر المرتبطة).',\n    highLightTip: 'سيتم عرض هذا المحتوى المحفوف بالمخاطر بتأثير \"تمييز\"، يرجى التحقق بعناية. يمكن إلغاء التمييز بتحديث الصفحة.',\n    commonTip: 'تنبيه',\n    understand: 'فهمت',\n    view: 'عرض',\n    start: 'بدء',\n    nextStep: 'الخطوة التالية',\n    help: 'مساعدة',\n    faceFailed: 'عذراً، فشلت مطابقة الوجه',\n    dualFailed: 'نعتذر، لم تنجح عملية التحقق من التسجيل المزدوج. يرجى التحقق من معلوماتك والمحاولة مرة أخرى.',\n    faceFailedtips: 'تنبيه',\n    verifyTry: 'يرجى التحقق من معلومات الهوية والمحاولة مرة أخرى',\n    faceLimit: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم',\n    upSignReq: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم، يرجى المحاولة غداً أو الاتصال بمُنشئ العقد لتعديل متطلبات التوقيع',\n    reqFace: 'يطلب المرسل منك التحقق من الوجه',\n    signAfterFace: 'يمكنك إكمال توقيع العقد بعد اجتياز التحقق من الوجه',\n    qrcodeInvalid: 'انتهت صلاحية معلومات رمز QR، يرجى التحديث',\n    faceFirstExceed: 'فشل التحقق من الوجه، سيتم استخدام رمز التحقق بدلاً من ذلك',\n    date: 'التاريخ',\n    chooseSeal: 'اختيار الختم',\n    seal: 'ختم',\n    signature: 'توقيع',\n    handwrite: 'كتابة يدوية',\n    mysign: 'توقيعي',\n    approvePlace: 'رسالة الموافقة، اختيارية',\n    approvePlace_1: 'رسالة الموافقة',\n    approvePlace_2: 'اختياري، لا يتجاوز 255 حرفاً.',\n    approveAgree: 'نتيجة الموافقة: موافق',\n    approveReject: 'نتيجة الموافقة: مرفوض',\n    signBy: 'بواسطة',\n    signByEnd: 'ختم',\n    sealBy: 'بواسطة',\n    sealByEnd: 'توقيع',\n    coverBy: 'يحتاج إلى ختم',\n    applicant: 'مقدم الطلب',\n    continueVeri: 'متابعة التحقق',\n    registerAndReal: 'يرجى التسجيل والتحقق من الهوية',\n    goToResiter: 'يرجى التسجيل والتحقق',\n    sureToUse: 'تأكيد الاستخدام',\n    toSign: 'توقيع?',\n    pleaseComplete: 'يرجى إكمال',\n    confirmSign: 'ثم تأكيد التوقيع',\n    admin: 'المدير',\n    contratAdmin: 'يرجى الاتصال بالمدير لإضافة حسابك',\n    addToEnt: 'كعضو في المؤسسة',\n    alreadyExists: 'موجود بالفعل في BestSign',\n    sendMsg: 'سيرسل BestSign المحتوى التالي إلى المدير عبر الرسائل القصيرة:',\n    applyJoin: 'طلب الانضمام',\n    title: 'العنوان',\n    viewImg: 'عرض الصورة',\n    priLetter: 'رسالة خاصة',\n    priLetterFromSomeone: 'رسالة خاصة من {name}',\n    readLetter: 'فهمت',\n    approve: 'موافق',\n    disapprove: 'رفض',\n    refuseSign: 'رفض التوقيع',\n    paperSign: 'التحول إلى التوقيع الورقي',\n    refuseTip: 'يرجى اختيار سبب الرفض',\n    refuseReason: 'تعبئة سبب رفض التوقيع يساعد الطرف الآخر على فهم مشكلتك وتسريع عملية العقد',\n    reasonWriteTip: 'يرجى إدخال سبب رفض التوقيع',\n    refuseReasonOther: 'المزيد من أسباب رفض التوقيع (اختياري) | المزيد من أسباب رفض التوقيع (مطلوب)',\n    refuseConfirm: 'رفض التوقيع',\n    refuseConfirmTip: 'أنت ترفض التوقيع بسبب \"{reason}\"، هل تريد المتابعة؟ بعد التأكيد، لن تتمكن من التوقيع مرة أخرى.',\n    waitAndThink: 'دعني أفكر مرة أخرى',\n    signValidationTitle: 'التحقق من التوقيع',\n    email: 'البريد الإلكتروني',\n    phoneNumber: 'رقم الهاتف',\n    password: 'كلمة المرور',\n    verificationCode: 'رمز التحقق',\n    mailVerificationCode: 'رمز التحقق',\n    forgetPsw: 'نسيت كلمة المرور',\n    if: '، هل',\n    forgetPassword: 'نسيت كلمة المرور',\n    rejectionVer: 'التحقق من رفض التوقيع',\n    msgTip: 'لم تستلم الرسالة القصيرة؟ جرب',\n    voiceVerCode: 'رمز التحقق الصوتي',\n    SMSVerCode: 'رمز التحقق عبر الرسائل القصيرة',\n    or: 'أو',\n    emailVerCode: 'رمز التحقق عبر البريد الإلكتروني',\n    SentSuccessfully: 'تم الإرسال بنجاح!',\n    intervalTip: 'الفاصل الزمني بين الإرسال قصير جداً',\n    signPsw: 'كلمة مرور التوقيع',\n    useSignPsw: 'استخدام كلمة مرور التوقيع للتحقق',\n    setSignPsw: 'إعداد التحقق بكلمة مرور التوقيع',\n    useVerCode: 'استخدام رمز التحقق',\n    inputVerifyCodeTip: 'يرجى إدخال رمز التحقق',\n    inputSignPwdTip: 'يرجى إدخال كلمة مرور التوقيع',\n    signConfirmTip: {\n      1: 'هل أنت متأكد من أنك تريد توقيع {contract} هذا؟',\n      2: 'النقر على زر التأكيد سيؤدي إلى التوقيع الفوري على {contract} هذا',\n      confirm: 'تأكيد التوقيع'\n    },\n    signSuc: 'تم التوقيع بنجاح',\n    refuseSuc: 'تم رفض التوقيع بنجاح',\n    approveSuc: 'تمت الموافقة بنجاح',\n    hdFile: 'عرض الملف عالي الدقة',\n    otherOperations: 'عمليات أخرى',\n    reviewDetails: 'تفاصيل الموافقة',\n    close: 'إغلاق',\n    submitter: 'مقدم الطلب',\n    signatory: 'الموقع',\n    reviewSchedule: 'مراحل المراجعة',\n    signByPc: 'تم التوقيع بواسطة {name}',\n    signPageDescription: 'الصفحة {index} من {total}',\n    sealBySomeone: 'تم الختم بواسطة {name}',\n    signDate: 'تاريخ التوقيع',\n    download: 'تحميل',\n    signPage: 'عدد الصفحات: {page}',\n    signNow: 'التوقيع الآن',\n    sender: 'المرسل',\n    signer: 'الموقع',\n    startSignTime: 'وقت بدء التوقيع',\n    signDeadLine: 'الموعد النهائي للتوقيع',\n    authGuide: {\n      goToHome: 'العودة للصفحة الرئيسية',\n      tip_1: 'بعد اكتمال التحقق، يمكنك عرض وتوقيع العقد.',\n      tip_2: 'يرجى استخدام الهوية | للتحقق.',\n      tip_3: 'تم إرسال العقد',\n      tip_4: 'يرجى الاتصال بمنشئ العقد | لتغيير المستلم.',\n      tip_5: 'هويتك المؤكدة | لا يمكنها عرض العقد',\n      new_tip_1: 'بناءً على متطلبات المرسل، تحتاج إلى إكمال الخطوات التالية:',\n      new_tip_2: 'بناءً على متطلبات المرسل، تحتاج إلى:',\n      new_tip_3: 'إكمال الخطوات التالية.',\n      new_tip_4: 'إذا كان لديك بالفعل صلاحيات الختم، سيتم تجاوز الخطوة 2 تلقائياً',\n      entUserName: 'الاسم:',\n      idNumberForVerify: 'رقم الهوية:',\n      realNameAuth: 'التحقق من الهوية الحقيقية',\n      applySeal: 'طلب ختم',\n      signContract: 'توقيع العقد'\n    },\n    switch: 'تبديل',\n    rejectReasonList: {\n      // authReason: '不想/不会做实名认证',\n      signOperateReason: 'لدي استفسارات حول عملية التوقيع/التحقق، وأحتاج إلى مزيد من التواصل',\n      termReason: 'لدي تحفظات على شروط/محتوى العقد، وأحتاج إلى مزيد من التواصل',\n      explainReason: 'لست على دراية بمحتوى العقد، يرجى الإخطار مسبقاً',\n      otherReason: 'أخرى (يرجى ذكر السبب)'\n    },\n    selectSignature: 'اختيار التوقيع',\n    selectSigner: 'اختيار الموقع',\n    pleaseScanToSign: 'يرجى المسح الضوئي للتوقيع باستخدام Alipay أو WeChat',\n    pleaseScanAliPay: 'يرجى مسح رمز QR باستخدام تطبيق Alipay للتوقيع',\n    pleaseScanWechat: 'يرجى مسح رمز QR باستخدام تطبيق WeChat للتوقيع',\n    requiredFaceSign: 'يطلب مرسل العقد منك التوقيع بالتعرف على الوجه',\n    requiredDualSign: 'يطلب مرسل العقد منك إكمال عملية التحقق من التسجيل المزدوج',\n    verCodeVerify: 'التحقق من رمز التحقق',\n    applyToSign: 'طلب توقيع العقد',\n    autoRemindAfterApproval: '*بعد الموافقة، سيتم إرسال تذكير توقيع تلقائي للموقع',\n    cannotSignBeforeApproval: 'لم تكتمل الموافقة، لا يمكن التوقيع حالياً!',\n    finishSignatureBeforeSign: 'يرجى إكمال الختم/التوقيع قبل تأكيد التوقيع',\n    uploadFileOnRightSite: 'لديك مرفقات لم يتم تحميلها بعد، يرجى تحميلها في الشريط الجانبي الأيمن أولاً',\n    cannotApplySealNeedPay: 'هذا العقد يتطلب منك الدفع، لا يدعم طلب ختم شخص آخر',\n    unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',\n    units: '{num} نسخة',\n    contractToPrivate: 'عقد شخصي',\n    contractToPublic: 'عقد مؤسسي',\n    paySum: 'المجموع {sum} يتطلب دفعك',\n    payTotal: 'المجموع {total} يوان.',\n    fundsLack: 'عدد العقود المتاحة لديك غير كافٍ، لضمان توقيع العقد بسلاسة، نقترح إعادة الشحن على الفور.',\n    contactToRecharge: 'يرجى الاتصال بالمدير الرئيسي لإعادة الشحن.',\n    deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية.',\n    needSignerPay: 'قام مرسل العقد بتعيين الدفع على الطرف المقابل، وحددك لدفع رسوم العقد.',\n    recharge: 'إعادة الشحن',\n    toSubmit: 'تقديم',\n    appliedSeal: 'تم تقديم طلب الختم',\n    noSeal: 'لا يوجد ختم',\n    noSwitchSealNeedDistribute: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بمدير المؤسسة الرئيسي لإضافة ختم وتفويضه',\n    viewApproveProcess: 'عرض عملية الموافقة',\n    approveProcess: 'عملية الموافقة',\n    noApproveContent: 'لم يتم تقديم مواد الموافقة',\n    knew: 'فهمت',\n    noSwitchSealNeedAppend: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بالمدير لإضافة ختم',\n    hadAutoSet: 'تم تلقائياً في {num} موضع آخر',\n    setThatSignature: 'وضع هذا التوقيع',\n    setThatSeal: 'وضع هذا الختم',\n    applyThatSeal: 'طلب هذا الختم',\n    hasSetTip: 'تم الوضع تلقائياً في {index} موضع آخر',\n    hasSetSealTip: 'تم وضع هذا الختم تلقائياً في {index} موضع آخر',\n    hasSetSignatureTip: 'تم وضع هذا التوقيع تلقائياً في {index} موضع آخر',\n    hasApplyForSealTip: 'تم طلب هذا الختم تلقائياً في {index} موضع آخر',\n    savedOnLeftSite: 'تم الحفظ في شريط التوقيع على اليسار',\n    ridingSealMinLimit: 'المستند صفحة واحدة فقط، لا يمكن وضع ختم متداخل',\n    ridingSealMaxLimit: 'يتجاوز 146 صفحة، لا يدعم وضع ختم متداخل',\n    ridingSealMinOrMaxLimit: 'المستند إما صفحة واحدة أو يتجاوز 146 صفحة، لا يمكن وضع ختم متداخل',\n    noSealForRiding: 'ليس لديك ختم متاح للاستخدام، لا يمكن وضع ختم متداخل',\n    noSwitchSealNeedAppendBySelf: 'لا توجد أختام متاحة للتبديل، يمكنك الذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',\n    gotoAppendSeal: 'اذهب لإضافة ختم',\n    approvalFlowSuccessfulSet: 'تم إعداد تدفق الموافقة بنجاح',\n    mandate: 'موافقة على التفويض',\n    loginToAppendSeal: 'يمكنك أيضاً تسجيل الدخول إلى BestSign باستخدام الكمبيوتر والذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',\n    signIdentityAs: 'تقوم حالياً بالتوقيع باسم {person}',\n    enterNextContract: 'انتقل إلى العقد التالي',\n    fileList: 'قائمة الملفات',\n    addSignerFile: 'إضافة مواد ملحقة',\n    signatureFinish: 'تم الختم/التوقيع بالكامل',\n    dragSignatureTip: 'يرجى سحب الختم/التاريخ التالي وإفلاته في المستند، يمكن السحب والإفلات عدة مرات',\n    noticeToManager: 'إرسال إشعار للمدير',\n    gotoAuthPerson: 'اذهب للتحقق الشخصي',\n    senderRequire: 'يطلب المرسل',\n    senderRequireUseFollowIdentity: 'يطلب المرسل منك استيفاء أحد الهويات التالية',\n    suggestToAuth: 'لم تقم بالتحقق من هويتك بعد، نقترح التحقق من هويتك قبل التوقيع',\n    contactEntAdmin: 'يرجى الاتصال بمدير المؤسسة الرئيسي',\n    setYourAccount: 'لتعيين حسابك',\n    authInfoUnMatchNeedResend: 'للتوقيع على العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد',\n    noEntNameNeedResend: 'لم يتم تحديد اسم المؤسسة الموقعة، لا يمكن توقيع هذا العقد، يرجى الاتصال بالمُنشئ لإعادة إرسال العقد',\n    pleaseUse: 'يرجى استخدام',\n    me: 'أنا',\n    myself: 'نفسي،',\n    reAuthBtnTip: 'أنا المستخدم الفعلي لرقم الهاتف هذا،',\n    reAuthBtnContent: 'بعد إعادة التحقق من الهوية، سيتم رفض التحقق الأصلي لهذا الحساب، يرجى التأكيد.',\n    descNoSame1: ' للتوقيع على العقد بهوية',\n    descNoSame2: 'هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',\n    authInfoNoSame: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',\n    authInfoNoSame2: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية الأساسية لحسابك الحالي المسجل.',\n    goHome: 'العودة إلى قائمة العقود>>',\n    authInfo: 'تم اكتشاف أن هوية حسابك الحالي المؤكدة هي ',\n    authInfo2: 'تم اكتشاف أن معلومات الهوية الأساسية لحسابك الحالي هي ',\n    in: 'في',\n    finishAuth: 'أكمل التحقق من الهوية للتوقيع القانوني على العقد',\n    ask: 'هل تريد متابعة التوقيع بالحساب الحالي؟',\n    reAuthBtnText: 'نعم، أريد إعادة التحقق من الهوية والتوقيع بهذا الحساب',\n    changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',\n    changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',\n    changePhoneTip2: 'لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد توقيعك.',\n    confirmOk: 'تأكيد',\n    goOnAuth: {\n      0: 'للتحقق،',\n      1: 'يرجى التحقق من الهوية،',\n      2: 'للتحقق من الهوية،'\n    },\n    signContractAfterAuth: {\n      0: 'بعد اكتمال التحقق، يمكنك توقيع العقد.',\n      1: 'بعد اكتمال التحقق، يمكنك توقيع العقد.'\n    },\n    useIdentity: 'باستخدام هوية {name}',\n    inTheName: 'باسم',\n    of: 'من',\n    identity: 'هوية',\n    nameIs: 'الاسم هو',\n    IDNumIs: 'رقم الهوية هو',\n    provideMoreAuthData: 'توفير مزيد من مواد التحقق',\n    leadToAuthBeforeSign: 'متابعة التحقق قبل التوقيع على العقد',\n    groupProxyAuthNeedMore: 'حالة التحقق الحالية هي تحقق بالوكالة للمجموعة، إذا كنت تريد التوقيع بشكل منفصل، يرجى توفير مواد تحقق إضافية',\n    contactSender: 'إذا كان لديك أي استفسارات، يرجى الاتصال بالمرسل.',\n    note: 'ملاحظة:',\n    identityInfo: 'معلومات الهوية',\n    signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً للتوقيع على العقد.',\n    needAuthPermissionContactAdmin: 'ليس لديك صلاحية التحقق من الهوية حالياً، يرجى الاتصال بالمدير',\n    iHadReadContract: 'قرأت وفهمت محتوى {alias}',\n    scrollToBottomTip: 'تحتاج إلى التمرير حتى الصفحة الأخيرة',\n    getVerCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',\n    appScanVerify: 'التحقق بمسح رمز QR في تطبيق BestSign',\n    downloadBSApp: 'تحميل تطبيق BestSign',\n    scanned: 'تم المسح بنجاح',\n    confirmInBSApp: 'يرجى تأكيد التوقيع في تطبيق BestSign',\n    qrCodeExpired: 'انتهت صلاحية رمز QR، يرجى التحديث والمحاولة مرة أخرى',\n    appKey: 'التحقق الأمني للتطبيق',\n    goToScan: 'اذهب للمسح',\n    setNotificationInUserCenter: 'يرجى تعيين طريقة الإشعار في مركز المستخدم أولاً',\n    doNotWantUseVerCode: 'لا أريد استخدام رمز التحقق',\n    try: 'جرب',\n    retry: 'إعادة المحاولة',\n    goToFaceVerify: 'اذهب للتحقق من الوجه',\n    faceExceedTimes: 'تم الوصول إلى الحد الأقصى لعدد مرات التحقق من الوجه اليوم',\n    returnBack: 'عودة',\n    switchTo: 'التبديل إلى',\n    youCanChooseIdentityBlow: 'يمكنك اختيار أحد الأطراف المتعاقدة التالية',\n    needDrawSignatureFirst: 'ليس لديك توقيع، يرجى إضافة توقيع يدوي أولاً',\n    lacksSealNeedAppend: 'لم تقم بإضافة أي أختام بعد، يرجى إضافة ختم أولاً.',\n    manageSeal: 'إدارة الأختام',\n    needDistributeSealToSelf: 'ليس لديك ختم متاح، يرجى تعيين نفسك كحامل للختم أولاً',\n    chooseSealAfterAuth: 'لا تريد استخدام الختم أعلاه؟ يمكنك تغيير الختم بعد التحقق من الهوية',\n    appendDrawSignature: 'إضافة توقيع يدوي',\n    senderUnFill: '(لم يملأ المرسل)',\n    declare: 'توضيح',\n    fileLessThan: 'يرجى تحميل ملف أقل من {num}M',\n    fileNeedUploadImg: 'يرجى استخدام تنسيقات الملفات المدعومة عند التحميل',\n    serverError: 'واجه الخادم مشكلة صغيرة، يرجى المحاولة مرة أخرى لاحقاً',\n    oldFormatTip: 'يدعم تنسيقات jpg، png، jpeg، pdf، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',\n    fileLimitFormatAndSize: 'لا يتجاوز عدد صور المواد الواحدة 10 صور.',\n    fileFormatImage: 'يدعم تنسيقات jpg، png، jpeg، حجم الصورة الواحدة لا يتجاوز 20M، يسمح بتحميل 10 صور',\n    fileFormatFile: 'يدعم تنسيقات pdf، excel، word، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',\n    signNeedKnow: 'ملاحظات التوقيع',\n    signNeedKnowFrom: 'ملاحظات التوقيع من {sender}',\n    approvalInfo: 'ملاحظات الموافقة',\n    approveNeedKnowFrom: 'مواد الموافقة المقدمة من {sender}-{sendEmployeeName} ({approvalType})',\n    approveBeforeSend: 'الموافقة قبل إرسال العقد',\n    approveBeforeSign: 'الموافقة قبل توقيع العقد',\n    approveOperator: 'الموافق',\n    approvalOpinion: 'رسالة الموافقة',\n    employeeDefault: 'موظف',\n    setLabel: 'تعيين العلامة',\n    addRidingSeal: 'إضافة ختم متداخل',\n    delRidingSeal: 'حذف الختم المتداخل',\n    file: 'ملف مرفق',\n    compressedFile: 'ملف مضغوط',\n    attachmentContent: 'محتوى المرفق',\n    pleaseClickView: '(يرجى النقر للتحميل والعرض)',\n    downloadFile: 'تحميل الملف الأصلي',\n    noLabelPleaseAppend: 'لا توجد علامات بعد، يرجى الذهاب إلى لوحة تحكم المؤسسة للإضافة',\n    archiveTo: 'أرشفة إلى',\n    hadArchivedToFolder: 'تم نقل العقد بنجاح إلى مجلد {folderName} الخاص بـ {who}',\n    pleaseScanToHandleWrite: 'يرجى مسح الرمز باستخدام WeChat أو متصفح الهاتف للتوقيع اليدوي على الجهاز المحمول',\n    save: 'حفظ',\n    remind: 'تذكير',\n    riskTip: 'تنبيه المخاطر',\n    chooseApplyPerson: 'اختيار منفذ الختم',\n    chooseAdminSign: 'اختيار مدير الأختام',\n    useSealByOther: 'ختم بواسطة شخص آخر',\n    getSeal: 'الحصول على ختم',\n    nowApplySealList: 'أنت تطلب الأختام التالية',\n    nowAdminSealList: 'أنت تطلب الحصول على الأختام التالية',\n    chooseApplyPersonToDeal: 'يرجى اختيار منفذ الختم، سيتم معالجة العقد من قبل الشخص المختار (يمكنك متابعة المشاهدة ومتابعة هذا العقد)',\n    chooseTransferPerson: 'تحويل التوقيع لشخص آخر',\n    chooseApplyPersonToMandate: 'يرجى اختيار مدير الأختام، عندما يتلقى الشخص المختار الإشعار ويوافق على المراجعة، ستحصل على صلاحية استخدام هذا الختم، وعندها يمكنك استخدام هذا الختم للختم وتوقيع العقد',\n    contactGroupAdminToDistributeSeal: 'يرجى الاتصال بمدير المجموعة لتخصيص الختم',\n    sealApplySentPleaseWait: 'تم إرسال طلب تخصيص الختم، يرجى انتظار الموافقة. أو يمكنك اختيار طريقة ختم أخرى',\n    successfulSent: 'تم الإرسال بنجاح',\n    authTip: {\n      t2: ['ملاحظة:', 'يجب أن تتطابق تماماً للتوقيع على العقد.', 'اسم المؤسسة', 'معلومات الهوية', 'يجب أن تتطابق تماماً لعرض وتوقيع العقد.'],\n      t3: '{x} يطلب منك {text} التحقق من الهوية.',\n      tCommon1: 'باسم {entName}',\n      tCommon2_1: 'باسم {name} ورقم هوية {idCard}',\n      tCommon2_2: 'باسم {name}',\n      tCommon2_3: 'برقم هوية {idCard}',\n      viewAndSign1: 'بعد اكتمال التحقق يمكنك عرض وتوقيع العقد.',\n      viewAndSignConflict: '{x} يطلب منك {text} لعرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد.'\n    },\n    needSomeoneToSignature: 'يحتاج {x} إلى ختم {y}',\n    needToSet: 'يحتاج إلى ختم',\n    approver: 'مقدم الطلب:',\n    clickToSignature: 'انقر هنا للتوقيع',\n    transferToOtherToSign: 'تحويل للتوقيع من شخص آخر',\n    signatureBy: 'توقيع بواسطة {x}',\n    tipRightNumber: 'يرجى إدخال رقم صحيح',\n    tipRightIdCard: 'يرجى إدخال رقم هوية مقيم في البر الرئيسي من 18 رقماً بشكل صحيح',\n    tipRightPhoneNumber: 'يرجى إدخال رقم هاتف من 11 رقماً بشكل صحيح',\n    tip: 'تنبيه',\n    tipRequired: 'لا يمكن ترك الحقول المطلوبة فارغة',\n    confirm: 'تأكيد',\n    viewContractDetail: 'عرض تفاصيل العقد',\n    required: 'مطلوب',\n    optional: 'اختياري',\n    decimalLimit: 'محدود بـ {x} أرقام بعد العلامة العشرية',\n    intLimit: 'يجب أن يكون رقماً صحيحاً',\n    invalidContract: 'توقيع هذا العقد يعني موافقتك على إلغاء العقود التالية:',\n    No: 'الرقم',\n    chooseFrom2: 'قام المرسل بتعيين اختيار واحد من اثنين للختم، يرجى اختيار موضع واحد للختم',\n    crossPlatformCofirm: {\n      message: 'مرحباً، يحتاج العقد الحالي إلى توقيع عبر المنصات، وستحتاج الملفات الموقعة إلى النقل خارج الحدود، هل توافق؟',\n      title: 'تفويض البيانات',\n      confirmButtonText: 'موافق على التفويض',\n      cancelButtonText: 'إلغاء'\n    },\n    sealScope: 'نطاق استخدام الختم',\n    currentContract: 'العقد الحالي',\n    allContract: 'جميع العقود',\n    docView: 'معاينة العقد',\n    fixTextDisplay: 'تصحيح رموز الصفحة غير المقروءة',\n    allPage: '{num} إجمالي الصفحات',\n    notJoinTip: 'يرجى الاتصال بالمسؤول ليضيفك كعضو في الشركة قبل التوقيع'\n  },\n  signJa: {\n    beforeSignTip1: 'بناءً على طلب المرسل، يرجى التوقيع باسم هذه المؤسسة:',\n    beforeSignTip2: 'حدد المرسل {signer} لإكمال التوقيع. إذا كانت المعلومات صحيحة، يمكنك التوقيع مباشرة.',\n    beforeSignTip3: 'إذا كانت المعلومات غير صحيحة، يرجى الاتصال بالمرسل لتغيير معلومات الموقع المحدد.',\n    beforeSignTip4: 'تم اكتشاف أن الاسم المسجل لهذا الحساب هو {currentUser}، وهو لا يتطابق مع {signer} المطلوب من المرسل حالياً، هل تؤكد التغيير إلى {signer}',\n    beforeSignTip5: 'تم اكتشاف أن الاسم المرتبط بالحساب الحالي هو: {currentUser}، وهو لا يتطابق مع متطلب الطرف الأول بتوقيع {signer}',\n    beforeSignTip6: 'يرجى التأكيد على التغيير إلى {signer} المحدد من قبل الطرف الأول للتوقيع وفقاً للوضع الفعلي',\n    beforeSignTip7: 'أو التواصل مع الطرف الأول لتغيير الموقع المحدد',\n    entNamePlaceholder: 'يرجى إدخال اسم المؤسسة',\n    corporateNumberPlaceholder: 'يرجى إدخال رقم الشركة',\n    corporateNumber: 'رقم الشركة',\n    singerNamePlaceholder: 'يرجى إدخال اسم الموقع',\n    singerName: 'اسم الموقع',\n    itsMe: 'هذا أنا',\n    wrongInformation: 'معلومات خاطئة',\n    confirmChange: 'تأكيد التغيير',\n    communicateSender1: 'لا تغيير، التواصل مع الطرف الأول',\n    communicateSender2: 'إلغاء، التواصل مع المرسل',\n    createSeal: {\n      title: 'إدخال الاسم',\n      tip: 'يرجى إدخال اسمك (يمكن استخدام المسافة للتغيير إلى سطر جديد)',\n      emptyErr: 'يرجى إدخال الاسم'\n    },\n    areaRegister: 'مكان تسجيل الشركة',\n    jp: 'اليابان',\n    cn: 'الصين',\n    are: 'الإمارات العربية المتحدة',\n    other: 'أخرى',\n    plsSelect: 'يرجى الاختيار',\n    tip1: 'يجب على الشركات المسجلة في البر الرئيسي للصين إكمال التسجيل باستخدام الاسم الحقيقي على ent.bestsign.cn. عند توقيع العقود مع الشركات خارج البر الرئيسي للصين، يمكن استخدام وظيفة \"التوقيع عبر الحدود\" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',\n    tip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للصين، يمكنك تسجيل الدخول مباشرة إلى ent.bestsign.cn واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الصينية.',\n    tip3: 'يرجى تقديم رقم الوثيقة الذي حصلت عليه من هيئة الرقابة التجارية المحلية',\n    tip4: 'يرجى اتباع الخطوات التالية',\n    tip5: '1. يرجى الاتصال بمدير العلاقات الخاص بك لإرشادك خلال عملية التحقق من هوية الشركة.',\n    tip6: 'انقر على \"إدارة الرصيد\".',\n    tip7: '2. يرجى تحميل لقطة شاشة للعقد التجاري لشركتك مع BestSign أو مراسلات البريد الإلكتروني مع مدير العلاقات المخصص.',\n    tip8: 'شراء عقد واحد على الأقل والاحتفاظ بلقطة شاشة لسجل الشراء.',\n    tip9: '3. هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',\n    tip10: '4. سوف تقوم BestSign بمراجعة الطلب خلال ثلاثة أيام عمل بعد التقديم.',\n    tip11: 'تنبيه هام',\n    tip12: 'يجب أن يكون المشتري مستخدماً تجارياً.',\n    tip13: 'يجب أن يتطابق اسم الشركة في حساب الدفع تماماً مع \"اسم الشركة\" الذي أدخلته.',\n    tip14: 'هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',\n    comNum: 'رقم وثيقة الشركة',\n    buyRecord: 'المواد الداعمة',\n    selectArea: 'يرجى اختيار مكان تسجيل الشركة',\n    uaeTip1: 'يجب على الشركات المسجلة في الإمارات العربية المتحدة إكمال التسجيل باستخدام الاسم الحقيقي على uae.bestsign.com. عند توقيع العقود مع الشركات خارج الإمارات العربية المتحدة، يمكن استخدام وظيفة \"التوقيع عبر الحدود\" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',\n    uaeTip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للإمارات، يمكنك تسجيل الدخول مباشرة إلى uae.bestsign.com واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الإماراتية.',\n    uaeTip3: 'الشركات المسجلة خارج دولة الإمارات العربية المتحدة والبر الرئيسى للصين ، يجب أن تكون مسجلة في ent.bestsign.com الاسم الحقيقي . عند توقيع العقود مع الشركات في دولة الإمارات العربية المتحدة ، يمكن استخدام \" التوقيع عبر الحدود \" وظيفة لضمان أمن بيانات المستخدم ، لا تسرب ، فرضية كفاءة إنجاز العقود المتبادلة .'\n  },\n  signPC: {\n    commonSign: 'تأكيد التوقيع',\n    contractVerification: 'التحقق من العقد',\n    VerCodeVerify: 'التحقق من رمز التحقق',\n    QrCodeVerify: 'التحقق من رمز QR',\n    verifyTip: 'BestSign يقوم بتفعيل شهادتك الرقمية الآمنة، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n    verifyAllTip: 'BestSign يقوم بتفعيل شهادة المؤسسة الرقمية وشهادتك الشخصية الرقمية، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n    selectSeal: 'اختيار الختم',\n    adminGuideTip: 'نظراً لأنك المدير الرئيسي للمؤسسة، يمكنك تخصيص ختم المؤسسة لنفسك مباشرة',\n    toAddSealWithConsole: 'الختم الرسمي الإلكتروني في انتظار التفعيل. لإضافة أختام أخرى، يرجى الانتقال إلى لوحة التحكم.',\n    use: 'استخدام',\n    toAddSeal: 'اذهب لإضافة ختم',\n    mySeal: 'ختمي',\n    operationCompleted: 'اكتملت العملية',\n    FDASign: {\n      date: 'وقت التوقيع',\n      signerAdd: 'إضافة',\n      signerEdit: 'تعديل',\n      editTip: 'تنبيه: للأسماء الصينية يرجى إدخال النطق الصوتي، مثل San Zhang (张三)',\n      inputNameTip: 'يرجى إدخال اسمك',\n      inputName: 'يرجى إدخال الإنجليزية أو النطق الصوتي الصيني',\n      signerNameFillTip: 'لا تزال بحاجة إلى ملء اسم التوقيع',\n      plsInput: 'يرجى الإدخال',\n      plsSelect: 'يرجى الاختيار',\n      customInput: 'إدخال مخصص'\n    },\n    signPlaceBySigner: {\n      signGuide: 'دليل التوقيع',\n      howDragSeal: 'كيفية سحب الختم',\n      howDragSignature: 'كيفية سحب التوقيع',\n      iKnow: 'فهمت',\n      step: {\n        one: 'الخطوة الأولى: قراءة العقد',\n        two1: 'الخطوة الثانية: النقر على \"سحب الختم\"',\n        two2: 'الخطوة الثانية: النقر على \"سحب التوقيع\"',\n        three: 'الخطوة الثالثة: النقر على زر \"التوقيع\"'\n      },\n      dragSeal: 'سحب الختم',\n      continueDragSeal: 'متابعة سحب الختم',\n      dragSignature: 'سحب التوقيع',\n      continueDragSignature: 'متابعة سحب التوقيع',\n      dragPlace: 'اضغط هنا للسحب',\n      notRemind: 'لا تذكرني مرة أخرى',\n      signTip: {\n        one: 'الخطوة الأولى: من خلال النقر على \"بدء\"، حدد موقع التوقيع/الختم المطلوب.',\n        two: 'الخطوة الثانية: من خلال النقر على \"موقع التوقيع/موقع الختم\"، أكمل التوقيع/الختم حسب المتطلبات.'\n      },\n      finishSignatureBeforeSign: 'يرجى إكمال سحب التوقيع/سحب الختم قبل تأكيد التوقيع'\n    },\n    continueOperation: {\n      success: 'نجحت العملية',\n      exitApproval: 'الخروج من الموافقة',\n      continueApproval: 'متابعة الموافقة',\n      next: 'التالي:',\n      none: 'لا يوجد المزيد',\n      tip: 'تنبيه',\n      approvalProcess: 'يحتاج إلى موافقة {totalNum} شخص، حالياً {passNum} شخص وافقوا',\n      receiver: 'المستلم:'\n    }\n  },\n  signTip: {\n    contractDetail: 'تفاصيل العقد',\n    downloadBtn: 'تحميل التطبيق',\n    tips: 'تنبيهات',\n    submit: 'تأكيد',\n    SigningCompleted: 'تم التوقيع بنجاح',\n    submitCompleted: 'في انتظار معالجة الآخرين',\n    noTurnSign: 'لم يحن دورك في التوقيع أو ليس لديك صلاحية التوقيع أو انتهت صلاحية تسجيل الدخول',\n    noRightSign: 'العقد قيد التوقيع، المستخدم الحالي غير مسموح له بعملية التوقيع',\n    noNeedSign: 'عقد قرار داخلي، لم يعد بحاجة للتوقيع',\n    ApprovalCompleted: 'تمت الموافقة بنجاح',\n    contractRevoked: 'تم إلغاء {alias} هذا',\n    contractRefused: 'تم رفض {alias} هذا',\n    linkExpired: 'انتهت صلاحية هذا الرابط',\n    contractClosed: 'انتهى موعد توقيع {alias} هذا',\n    approvalReject: 'تم رفض موافقة {alias} هذا',\n    approving: '{alias} قيد الموافقة',\n    viewContract: 'عرض تفاصيل {alias}',\n    viewContractList: 'عرض قائمة العقود',\n    needMeSign: '({num} في انتظار التوقيع)',\n    downloadContract: 'تحميل العقد',\n    sign: 'تم التوقيع',\n    signed: 'تم التوقيع',\n    approved: 'تمت الموافقة',\n    approval: 'موافقة',\n    person: 'شخص',\n    personHas: 'قد',\n    personHave: 'قد',\n    personHasnot: 'لم',\n    personsHavenot: 'لم',\n    headsTaskDone: '{num}{has}{done}',\n    headsTaskNotDone: '{num}{not}{done}',\n    taskStatusBetween: '،',\n    cannotReview: 'لا يمكن عرض العقد',\n    cannotDownload: 'هذا العقد لا يدعم التحميل على الهاتف. لأن العقد مخزن بشكل خاص من قبل المرسل، لا يمكن لـ BestSign الوصول إلى العقد.',\n    privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود',\n    beenDeleted: 'تم حذف حسابك من قبل مدير المؤسسة',\n    unActive: 'لا يمكن متابعة تفعيل الحساب',\n    back: 'عودة',\n    contratStatusDes: 'حالة {key}:',\n    contractConditionDes: 'وضع {key}:',\n    contractIng: '{alias} {key} جارٍ',\n    contractComplete: 'اكتمل {alias} {key}',\n    dataProduct: {\n      tip1: 'من {entName} إلى السادة مسؤولي الموزعين/الموردين المتميزين:',\n      tip2: 'لشكركم على مساهمتكم في التطور المستقر لـ {entName}، نقدم بالتعاون مع {bankName} خدمات التمويل لسلسلة التوريد لمساعدة مؤسستكم على التطور السريع!',\n      btnText: 'اذهب لمشاركة هذه الأخبار السارة مع المدير'\n    },\n    signOnGoing: 'العقد {status} جارٍ',\n    operate: 'عملية العقد',\n    freeContract: 'أكمل إرسال العقد الأول، يمكنك الحصول على المزيد من العقود مجاناً',\n    sendContract: 'اذهب لإرسال العقد',\n    congratulations: 'تهانينا {name} على إكمال توقيع {num} عقد،',\n    carbonSaving: 'تقدير توفير الكربون {num}g',\n    signGift: 'BestSign يهديك {num} عقد مؤسسي (صالح حتى {limit})',\n    followPublic: 'تابع الحساب العام على WeChat لتلقي رسائل العقود في أي وقت',\n    congratulationsSingle: 'تهانينا {name} على إكمال توقيع العقد،',\n    carbonSavingSingle: 'تقدير زيادة توفير الكربون 2002.4g',\n    viewContractTip: 'إذا كنت تريد تغيير الشخص المسؤول عن الختم، يمكنك النقر على زر \"عرض التفاصيل\" لفتح صفحة تفاصيل العقد، ثم النقر على زر \"طلب ختم\"',\n    congratulationsCn: 'شكراً لاختيار التوقيع الإلكتروني!',\n    carbonSavingSingleCn: 'لقد قللت الكربون بمقدار {num}gCO2e للأرض',\n    carbonVerification: \"*محسوب علمياً بواسطة 'كربون ستوب'\"\n  },\n  view: {\n    title: 'عرض العقد',\n    ok: 'إكمال',\n    cannotReview: 'لا يمكن عرض العقد',\n    privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود'\n  },\n  prepare: {\n    sealArea: 'موقع الختم',\n    senderNotice: 'الطرف المرسل الحالي للعقد هو: {entName}',\n    preSetDialogConfirm: 'فهمت',\n    preSetDialogContact: 'اتصل بفريق مبيعات BestSign للتفعيل الآن',\n    preSetDialogInfo: 'بعد إعداد العقد مسبقاً، يقوم النظام تلقائياً بملء معلومات الأطراف المتعاقدة، ومتطلبات التوقيع، ومواقع التوقيع، وحقول وصف العقد وفقاً للقالب',\n    preSetDialogTitle: 'ما هو قالب الإعداد المسبق للعقد؟',\n    initialValues: 'تعيين القيم الأولية بناءً على محتوى العقد',\n    proxyUpload: 'بعد تحميل الملف المحلي، يمكنك اختيار طرف بدء العقد',\n    signHeaderTitle: 'إضافة الملفات والأطراف المتعاقدة',\n    step1: 'الخطوة الأولى',\n    confirmSender: 'تأكيد المرسل',\n    step2: 'الخطوة الثانية',\n    uploadFile: 'تحميل الملف',\n    step3: 'الخطوة الثالثة',\n    addSigner: 'إضافة طرف متعاقد',\n    actionDemo: 'عرض توضيحي للعملية',\n    next: 'الخطوة التالية',\n    isUploadingErr: 'لم يكتمل تحميل الملف بعد، يرجى المتابعة بعد الاكتمال',\n    noUploadFileErr: 'لم يتم تحميل ملف، يرجى التحميل قبل المتابعة',\n    noContractTitleErr: 'لم يتم إدخال اسم العقد، يرجى الإدخال قبل المتابعة',\n    contractTypeErr: 'تم حذف نوع العقد الحالي، يرجى إعادة اختيار نوع العقد',\n    expiredDateErr: 'خطأ في الموعد النهائي للتوقيع، يرجى التعديل قبل المتابعة',\n    noExpiredDateErr: 'يرجى تعيين الموعد النهائي للتوقيع قبل المتابعة',\n    describeFieldsErr: 'يرجى ملء حقول المحتوى المطلوبة قبل المتابعة',\n    noRecipientsErr: 'يرجى إضافة طرف متعاقد واحد على الأقل',\n    noAccountErr: 'لا يمكن ترك الحساب فارغاً',\n    noUserNameErr: 'لا يمكن ترك الاسم فارغاً',\n    noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',\n    accountFormatErr: 'التنسيق غير صحيح، يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n    userNameFormatErr: 'التنسيق غير صحيح، يرجى إدخال اسم صحيح',\n    enterpriseNameErr: 'يرجى إدخال اسم مؤسسة صحيح',\n    idNumberForVerifyErr: 'التنسيق غير صحيح، يرجى إدخال رقم هوية صحيح',\n    signerErr: 'خطأ في الطرف المتعاقد',\n    noSignerErr: 'يرجى إضافة موقع واحد على الأقل',\n    lackAttachmentNameErr: 'يرجى إدخال اسم المرفق',\n    repeatRecipientsErr: 'لا يمكن إضافة نفس الطرف المتعاقد مرتين عند عدم التوقيع بالترتيب',\n    innerContact: 'جهة اتصال داخلية',\n    outerContact: 'جهة اتصال خارجية',\n    search: 'بحث',\n    accountSelected: 'الحسابات المختارة',\n    groupNameAll: 'الكل',\n    unclassified: 'غير مصنف',\n    fileLessThan: 'يرجى تحميل ملف أقل من {num}M',\n    beExcel: 'يرجى تحميل ملف Excel',\n    usePdf: 'يرجى استخدام ملف PDF أو صورة عند التحميل',\n    usePdfFile: 'يرجى استخدام ملف PDF عند التحميل',\n    fileNameMoreThan: 'تجاوز طول اسم الملف {num}، تم اقتطاعه تلقائياً',\n    needAddSender: 'لم يتم تعيين مؤسستك/نفسك كطرف متعاقد، بعد إرسال العقد، لن تشارك في عملية التوقيع. هل تريد إضافة طرفك كموقع؟',\n    addSender: 'إضافة كطرف متعاقد',\n    tip: 'تنبيه',\n    cancel: 'إلغاء'\n  },\n  addReceiver: {\n    English: 'الإنجليزية',\n    Japanese: 'اليابانية',\n    Chinese: 'الصينية',\n    Arabic: 'Arabic',\n    setNoticelang: 'تعيين لغة الإشعارات',\n    limitFaceConfigTip: 'سعر العقد الخاص بك منخفض جداً، هذه الميزة غير متوفرة، يرجى الاتصال بـ BestSign للتفاوض',\n    individual: 'فرد متعاقد',\n    enterprise: 'مؤسسة متعاقدة',\n    addInstructions: 'إضافة ملاحظات التوقيع',\n    instructionsContent: 'المواد المقدمة تساعدك في تتبع حالة تنفيذ العقد وتحديد ما إذا كان تنفيذ الأعمال طبيعياً. بعد الإعداد، يجب على الموقع تقديمها وفقاً للمتطلبات',\n    addContractingInfo: 'تقديم مواد الطرف المتعاقد',\n    contractingInfoContent: 'المواد المقدمة تساعدك في التحقق من مؤهلات الطرف المتعاقد وتحديد ما إذا كان يمكن بدء أو مواصلة الأعمال معه. إذا كان الطرف المتعاقد قد قدم نفس المواد من قبل، فلا داعي لتقديمها مرة أخرى',\n    payer: 'الطرف الدافع',\n    handWriting: 'تفعيل التعرف على الكتابة اليدوية',\n    realName: 'يحتاج المسؤول إلى التحقق من الهوية',\n    sameTip: 'تنبيه: يجب أن يتطابق اسم المؤسسة للطرف المتعاقد تماماً للتوقيع',\n    proxy: 'استلام بالوكالة من الطرف الآخر',\n    aboradTip: 'تنبيه: هذا الطرف المتعاقد شخص خارج البلاد، هناك مخاطر في التحقق من الهوية، يرجى التحقق من هوية هذا الشخص أولاً',\n    busRole: 'دور العمل',\n    busRoleTip: 'يساعد في تحديد الطرف المتعاقد وتسهيل الإدارة',\n    busRolePlaceholder: 'مثل موظف/موزع',\n    handWritingTip: 'يحتاج هذا المستخدم إلى كتابة اسمه بخط واضح ومقروء عند التوقيع لإكمال التوقيع',\n    instructions: 'إضافة ملاحظات التوقيع | (محدود بـ 255 حرفاً)',\n    contractingParty: 'مواد الطرف المتعاقد',\n    signerPay: 'يدفع هذا الموقع رسوم هذا العقد',\n    afterReadingTitle: 'التوقيع بعد القراءة',\n    afterReading: 'يجب على الموقع القراءة وفهم محتوى العقد قبل متابعة العمليات اللاحقة.',\n    handWritingTips: 'سيتم مقارنة الاسم المكتوب بخط اليد مع الاسم المحدد من قبل المرسل أو في معلومات التحقق من الهوية، يجب أن تتطابق المقارنة لإكمال التوقيع',\n    SsTitle: 'الختم والتوقيع',\n    SsTip: 'عند التوقيع باستخدام ختم المؤسسة، يجب أيضاً إضافة التوقيع الشخصي لإكمال التوقيع. يجب إكمال التحقق من الهوية الشخصية قبل التوقيع',\n    signature: 'توقيع',\n    stamp: 'ختم',\n    Ss: 'ختم وتوقيع',\n    mutexError: 'تم تعيين \"{msg}\"، يرجى حذف إعداد \"{msg}\" أولاً قبل الاختيار',\n    handWriteNotAllowed: 'التوقيع اليدوي غير مسموح به',\n    forceHandWrite: 'التوقيع اليدوي إلزامي',\n    faceFirst: 'التعرف على الوجه أولاً، رمز التحقق احتياطي للتوقيع',\n    faceVerify: 'التوقيع بالتعرف على الوجه إلزامي',\n    attachmentRequired: 'إضافة مواد ملحقة للعقد',\n    newAttachmentRequired: 'تقديم مواد الطرف المتعاقد',\n    attachmentError: 'لا يمكن أن تكون أسماء المواد الملحقة للعقد متطابقة',\n    receiver: 'هاتف/بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',\n    receiverJa: 'بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',\n    orderSignLabel: 'التوقيع بالترتيب',\n    contactAddress: 'دليل جهات الاتصال',\n    signOrder: 'ترتيب التوقيع',\n    account: 'الحساب',\n    accountPlaceholder: 'هاتف/بريد إلكتروني (مطلوب)',\n    accountPlaceholderJa: 'بريد إلكتروني (مطلوب)',\n    accountReceptionCollection: 'استلام بالوكالة',\n    accountReceptionCollectionTip1: 'لا تعرف حساب الطرف الآخر المحدد أو ليس لديه حساب،',\n    accountReceptionCollectionTip2: 'يرجى اختيار استلام بالوكالة',\n    signSubjectPerson: 'الطرف المتعاقد: فرد',\n    nameTips: 'الاسم (اختياري، للتحقق من هوية التوقيع)',\n    requiredNameTips: 'الاسم (مطلوب، للتحقق من هوية التوقيع)',\n    entOperatorNameTips: 'الاسم (اختياري)',\n    needAuth: 'يحتاج إلى التحقق من الهوية',\n    operatorNeedAuth: 'يحتاج المسؤول إلى التحقق من الهوية',\n    signSubjectEnt: 'الطرف المتعاقد: شركة',\n    entNameTips: 'اسم المؤسسة (مطلوب، للتحقق من هوية التوقيع)',\n    operator: 'المسؤول',\n    sign: 'توقيع',\n    more: 'المزيد',\n    faceFirstTips: 'يستخدم النظام التحقق من الوجه افتراضياً عند التوقيع، عندما يصل عدد مرات فشل التحقق من الوجه إلى الحد الأقصى اليومي، يتم التبديل تلقائياً إلى التحقق برمز التحقق',\n    mustFace: 'التوقيع بالتعرف على الوجه إلزامي',\n    mustHandWrite: 'التوقيع اليدوي إلزامي',\n    fillIDNumber: 'رقم الهوية',\n    fillNoticeCall: 'هاتف الإشعار',\n    fillNoticeCallTips: 'يرجى إدخال هاتف الإشعار',\n    addNotice: 'إضافة رسالة خاصة',\n    attachTips: 'إضافة مواد ملحقة للعقد',\n    faceSign: 'التوقيع بالتعرف على الوجه إلزامي',\n    faceSignTips: 'يحتاج هذا المستخدم إلى اجتياز التحقق من الوجه لإكمال التوقيع (التوقيع بالتعرف على الوجه متاح حالياً فقط للمقيمين في البر الرئيسي)',\n    handWriteNotAllowedTips: 'يمكن لهذا المستخدم فقط اختيار توقيع معد مسبقاً أو استخدام الخط الافتراضي للتوقيع لإكمال التوقيع',\n    handWriteTips: 'يحتاج هذا المستخدم إلى التوقيع اليدوي لإكمال التوقيع',\n    idNumberTips: 'للتحقق من هوية التوقيع',\n    verifyBefore: 'التحقق من الهوية قبل عرض الملف',\n    verify: 'التحقق من الهوية',\n    verifyTips: '20 حرفاً كحد أقصى',\n    verifyTips2: 'يجب عليك تقديم معلومات التحقق هذه لهذا المستخدم',\n    sendToThirdPlatform: 'إرسال إلى منصة خارجية',\n    platFormName: 'اسم المنصة',\n    fillThirdPlatFormName: 'يرجى إدخال اسم المنصة الخارجية',\n    attach: 'المواد',\n    attachName: 'اسم المواد',\n    exampleID: 'مثال: صورة بطاقة الهوية',\n    attachInfo: 'ملاحظات',\n    attachInfoTips: 'مثال: يرجى تحميل صورة بطاقة هويتك',\n    addAttachRequire: 'إضافة مواد',\n    addSignEnt: 'إضافة مؤسسة متعاقدة',\n    addSignPerson: 'إضافة فرد متعاقد',\n    selectContact: 'اختيار جهة اتصال',\n    save: 'حفظ',\n    searchVerify: 'التحقق من البحث',\n    fillImageContentTips: 'يرجى ملء محتوى الصورة',\n    ok: 'موافق',\n    findContact: 'تم العثور على الأطراف المتعاقدة التالية في العقد',\n    signer: 'الطرف المتعاقد',\n    signerTips: 'تلميح: بعد اختيار الطرف المتعاقد، يمكن للمنصة المساعدة في تحديد موقع التوقيع والختم.',\n    add: 'إضافة',\n    notAdd: 'عدم الإضافة',\n    cc: 'نسخة',\n    notNeedAuth: 'لا يحتاج إلى التحقق من الهوية',\n    operatorNotNeedAuth: 'لا يحتاج المسؤول إلى التحقق من الهوية',\n    extracting: 'جاري الاستخراج',\n    autoFill: 'ملء الموقع تلقائياً',\n    failExtracting: 'لم يتم العثور على أطراف متعاقدة',\n    idNumberForVerifyErr: 'يرجى إدخال رقم هوية صحيح',\n    noAccountErr: 'لا يمكن ترك الحساب فارغاً',\n    noUserNameErr: 'لا يمكن ترك الاسم فارغاً',\n    noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',\n    noEntNameErr: 'لا يمكن ترك اسم المؤسسة فارغاً',\n    accountFormatErr: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n    enterpriseNameErr: 'يرجى إدخال اسم شركة صحيح',\n    userNameFormatErr: 'يرجى إدخال اسم صحيح',\n    riskCues: 'تنبيه المخاطر',\n    riskCuesMsg: 'إذا لم يوقع الطرف المتعاقد بالتحقق من الهوية، ستحتاج إلى تقديم دليل التحقق من هوية هذا الطرف بنفسك عند حدوث نزاع في الملف. لتجنب المخاطر، يرجى اختيار التحقق من الهوية.',\n    confirmBtnText: 'اختيار التحقق من الهوية',\n    cancelBtnText: 'اختيار عدم التحقق من الهوية',\n    attachLengthErr: 'يمكنك إضافة 50 مرفق كحد أقصى لكل موقع',\n    collapse: 'طي',\n    expand: 'توسيع',\n    delete: 'حذف',\n    saySomething: 'قل شيئاً',\n    addImage: 'إضافة مستند',\n    addImageTips: '(يدعم word وpdf والصور، لا يتجاوز 3 مستندات)',\n    give: 'إعطاء',\n    fileMax: 'تجاوز عدد التحميلات الحد الأقصى!',\n    signerLimit: 'نسختك الحالية لا تدعم أكثر من {limit} موقع نسبي/مستلم نسخة.',\n    showExamle: 'عرض الصور النموذجية',\n    downloadExamle: 'تنزيل الملف النموذجي'\n  },\n  addReceiverGuide: {\n    guideTitle: 'كيفية إضافة موقع جديد',\n    receiverType: 'تحتاج إلى اختيار طريقة مشاركة الموقع في العقد (واحد من ستة):',\n    asEntSign: 'التوقيع نيابة عن المؤسسة:',\n    signatureSub: 'المدير القانوني أو التنفيذي يوقع على العقد نيابة عن المؤسسة. يمكن للمؤسسة أخذ العقد بعد اكتمال التوقيع',\n    vipOnly: 'متاح فقط للنسخة المتقدمة',\n    sealSub: 'يحتاج الموقع إلى وضع ختم رسمي أو ختم خاص بالعقد وغيره',\n    stampSub: 'يحتاج الموقع إلى الختم والتوقيع نيابة عن المؤسسة',\n    confirmSeal: 'استخدام ختم التحقق من الأعمال نيابة عن المؤسسة',\n    confirmSealSub: 'كشوف الحسابات المالية وخطابات التأكيد وغيرها من الوثائق تحتاج إلى التحقق قبل الختم',\n    asPersonSign: 'التوقيع بصفة شخصية:',\n    asPersonSignTip: 'التوقيع بصفة شخصية فقط، لا يمثل أي مؤسسة',\n    asPersonSignDesc: 'العقود الشخصية للموقع، مثل عقود القرض واتفاقيات التوظيف والاستقالة وغيرها',\n    scanSign: 'التوقيع بالمسح الضوئي',\n    scanSignDesc: 'لا تحتاج إلى كتابة الموقع عند إرسال العقد، بعد الإرسال يمكن لأي شخص المسح/النقر على رابط التحقق للتوقيع، مناسب لسيناريوهات استلام مستندات الشحن',\n    selectSignTypeTip: 'يرجى اختيار طريقة مشاركة الطرف المتعاقد في العقد أولاً',\n    notRemind: 'عدم التذكير مرة أخرى',\n    sign: 'توقيع',\n    entSign: 'توقيع المؤسسة',\n    stamp: 'ختم',\n    stampSign: 'ختم وتوقيع',\n    requestSeal: 'ختم التحقق من الأعمال'\n  },\n  linkContract: {\n    title: 'ربط العقود',\n    connectMore: 'ربط المزيد من العقود',\n    placeholder: 'يرجى إدخال رقم العقد',\n    revoke: 'تم إلغاء العقد',\n    overdue: 'تجاوز موعد التوقيع',\n    approvalNotPassed: 'تم رفض الموافقة',\n    reject: 'تم رفض العقد',\n    signing: 'قيد التوقيع',\n    complete: 'مكتمل',\n    approvaling: 'قيد الموافقة',\n    disconnect: 'إلغاء الربط',\n    disconnectSuccess: 'تم إلغاء الربط بنجاح',\n    connectLimit: 'الحد الأقصى لعدد العقود المرتبطة هو 100 عقد'\n  },\n  field: {\n    fieldTip: {\n      title: 'موقع توقيع مفقود',\n      error: 'لم يتم تحديد مواقع التوقيع ({type}) في العقود التالية',\n      add: 'إضافة حقل',\n      continue: 'متابعة الإرسال'\n    },\n    accountCharge: {\n      notice: 'يتم احتساب رسوم هذا العقد حسب عدد الحسابات المشاركة',\n      able: 'يمكن الإرسال بشكل طبيعي',\n      unable: 'عدد الحسابات المتاحة غير كافٍ، يرجى الاتصال بخدمة عملاء BestSign',\n      notify: 'هذا العقد يرسل إشعارات باللغة الإنجليزية لجميع الأطراف المتعاقدة',\n      noNotify: {\n        1: 'هذا العقد لا يرسل إشعارات متعلقة بالتوقيع',\n        2: '(بما في ذلك الرسائل القصيرة والبريد الإلكتروني للتوقيع والموافقة والنسخ والموعد النهائي للتوقيع)'\n      }\n    },\n    ridingStamp: 'ختم متداخل',\n    watermark: 'علامة مائية',\n    senderSignature: 'توقيع صاحب الختم',\n    optional: 'اختياري',\n    clickDecoration: 'انقر لتزيين العقد',\n    decoration: 'تزيين العقد',\n    sysError: 'النظام مشغول، يرجى المحاولة لاحقاً',\n    partedMarkedError: 'يجب تحديد كل من الختم والتوقيع للأطراف المتعاقدة المحددة لـ \"الختم والتوقيع\"',\n    fieldTitle: 'يوجد {length} عقد يحتاج إلى تحديد مواقع التوقيع',\n    send: 'إرسال',\n    contractDispatchApply: 'طلب إرسال العقد',\n    contractNeedYouSign: 'هذا المستند يحتاج إلى توقيعك',\n    ifSignRightNow: 'هل تريد التوقيع الآن',\n    signRightNow: 'التوقيع الآن',\n    signLater: 'التوقيع لاحقاً',\n    signaturePositionErr: 'يرجى تحديد مواقع التوقيع لكل موقع',\n    sendSucceed: 'تم الإرسال بنجاح',\n    confirm: 'تأكيد',\n    cancel: 'إلغاء',\n    qrCodeTips: 'امسح الرمز بعد التوقيع لعرض تفاصيل التوقيع والتحقق من صلاحية التوقيع وما إذا تم العبث بالعقد',\n    pagesField: 'الصفحة {currentPage} من {totalPages}',\n    suitableWidth: 'عرض مناسب',\n    signCheck: 'التحقق من التوقيع',\n    locateSignaturePosition: 'تحديد موقع التوقيع',\n    locateTips: 'يمكن المساعدة في تحديد موقع التوقيع بسرعة. حالياً يدعم فقط تحديد أول موقع توقيع لكل موقع',\n    step1: 'الخطوة الأولى',\n    selectSigner: 'اختيار الطرف المتعاقد',\n    step2: 'الخطوة الثانية',\n    dragSignaturePosition: 'سحب موقع التوقيع',\n    signingField: 'حقل التوقيع',\n    docTitle: 'المستند',\n    totalPages: 'عدد الصفحات: {totalPages} صفحة',\n    receiver: 'المستلم',\n    delete: 'حذف',\n    deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية',\n    unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',\n    charge: 'الرسوم',\n    units: '{num} نسخة',\n    contractToPrivate: 'عقد شخصي',\n    contractToPublic: 'عقد مؤسسي',\n    costTips: {\n      1: 'عقد مؤسسي: عقد يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',\n      2: 'عقد شخصي: عقد لا يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',\n      3: 'يتم حساب عدد النسخ المحتسبة حسب عدد المستندات',\n      4: 'عدد النسخ المحتسبة = عدد المستندات × عدد مجموعات (صفوف) المستخدمين المستوردين بالدفعة'\n    },\n    toCharge: 'إعادة الشحن',\n    contractNeedCharge: {\n      1: 'عدد العقود المتاحة غير كافٍ، لا يمكن الإرسال',\n      2: 'عدد العقود المتاحة غير كافٍ، يرجى الاتصال بالمدير الرئيسي لإعادة الشحن'\n    },\n    chooseApprover: 'اختيار المراجع:',\n    nextStep: 'الخطوة التالية',\n    submitApproval: 'تقديم للموافقة',\n    autoSendAfterApproval: '*بعد الموافقة، سيتم إرسال العقد تلقائياً',\n    chooseApprovalFlow: 'يرجى اختيار تدفق موافقة',\n    completeApprovalFlow: 'تدفق الموافقة الذي قدمته غير مكتمل، يرجى إكماله وإعادة التقديم',\n    viewPrivateLetter: 'عرض الرسالة الخاصة',\n    addPrivateLetter: 'إضافة رسالة خاصة',\n    append: 'إضافة',\n    privateLetter: 'رسالة خاصة',\n    signNeedKnow: 'ملاحظات التوقيع',\n    maximum5M: 'يرجى تحميل مستند أقل من 5M',\n    uploadServerFailure: 'فشل التحميل إلى الخادم',\n    uploadFailure: 'فشل التحميل',\n    pager: 'رقم الصفحة',\n    seal: 'ختم',\n    signature: 'توقيع',\n    signDate: 'تاريخ التوقيع',\n    text: 'نص',\n    date: 'تاريخ',\n    qrCode: 'رمز QR',\n    number: 'رقم',\n    dynamicTable: 'جدول ديناميكي',\n    terms: 'بنود العقد',\n    checkBox: 'خانة اختيار',\n    radioBox: 'زر راديو',\n    image: 'صورة'\n  },\n  addressBook: {\n    innerMember: {\n      title: 'الأعضاء الداخليون للمؤسسة',\n      tips: 'تعديل معلومات موظفي المؤسسة لجعل المرسلين يجدون جهات الاتصال الداخلية بسرعة أكبر',\n      operation: 'اذهب إلى لوحة التحكم'\n    },\n    outerContacts: {\n      title: 'جهات الاتصال الخارجية للمؤسسة',\n      tips: 'دعوة شركائك للتسجيل والتحقق من الهوية مسبقاً لتسهيل إجراء الأعمال معك',\n      operation: 'دعوة شركائك'\n    },\n    myContacts: {\n      title: 'جهات اتصالي',\n      tips: 'تعديل جهات الاتصال للتأكد من دقة معلومات الموقع',\n      operation: 'اذهب إلى مركز المستخدم'\n    },\n    selected: 'الحسابات المختارة',\n    search: 'بحث',\n    loadMore: 'تحميل المزيد',\n    end: 'تم تحميل الكل'\n  },\n  dataBoxInvite: {\n    title: 'دعوة شركائك',\n    step1: 'مشاركة الرابط مع شركائك لإنشاء مؤسسة مسبقاً',\n    step2: 'سيظهر الشركاء المفوضون عبر الرابط/رمز QR في دليل العناوين',\n    step3: 'قم بإدارة المزيد لشركائك في \"الملفات+\"',\n    imgName: 'مشاركة رمز QR للجمع',\n    saveQrcode: 'حفظ رمز QR محلياً',\n    copy: 'نسخ',\n    copySuccess: 'تم النسخ بنجاح',\n    copyFailed: 'فشل النسخ'\n  },\n  shareView: {\n    title: 'إعادة توجيه للمراجعة',\n    account: 'رقم الهاتف/البريد الإلكتروني',\n    role: 'دور المراجع',\n    note: 'ملاحظات',\n    link: 'الرابط:',\n    signerMessage: 'رسالة الموقع',\n    rolePlaceholder: 'مثل القانوني للشركة، قائد القسم وغيرهم',\n    notePlaceholder: 'اترك رسالة للمراجع، في حدود 200 حرف',\n    generateLink: 'إنشاء رابط',\n    saveQrcode: 'حفظ رمز البرنامج الصغير لـ WeChat',\n    regenerateLink: 'إعادة إنشاء الرابط',\n    inputAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني',\n    inputCorrectAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n    accountInputTip: 'لضمان فتح الرابط بشكل طبيعي، يرجى إدخال معلومات مراجع العقد بدقة',\n    shareLinkTip1: 'احفظ رمز برنامج WeChat المصغر أو',\n    shareLinkTip: 'انسخ الرابط للمشاركة مع المراجعين',\n    linkTip1: 'نص العقد محتوى سري، يرجى عدم تسريبه للخارج في حالة عدم الضرورة',\n    linkTip2: 'صلاحية الرابط يومان؛ بعد إعادة إنشاء الرابط، يصبح الرابط السابق غير صالح تلقائياً'\n  },\n  recoverSpecialSeal: {\n    title: 'الختم غير قابل للاستخدام',\n    description1: 'يطلب المرسل منك استخدام هذا الختم للتوقيع على العقد، ولكن شركتك قد حذفت هذا الختم. لضمان سير التوقيع بسلاسة، يرجى طلب استعادة هذا الختم من المدير.',\n    description2: 'إذا كان هذا الختم غير مناسب للاستخدام المستمر، يمكنك الاتصال بالمرسل لتعديل متطلبات صورة الختم قبل توقيع العقد.',\n    postRecover: 'طلب استعادة الختم',\n    note: 'بعد النقر، سيتلقى المدير رسالة قصيرة/بريد إلكتروني لطلب استعادة الختم، كما يمكن رؤية الطلب في صفحة إدارة الأختام.',\n    requestSend: 'تم تقديم طلب الاستعادة بنجاح'\n  },\n  paperSign: {\n    title: 'استخدام طريقة التوقيع الورقي',\n    stepText: ['الخطوة التالية', 'تأكيد التوقيع الورقي', 'تأكيد'],\n    needUploadFile: 'يرجى تحميل النسخة الممسوحة ضوئياً أولاً',\n    uploadError: 'فشل التحميل',\n    cancel: 'إلغاء',\n    downloadPaperFile: 'الحصول على ملف التوقيع الورقي',\n    step0: {\n      title: 'تحتاج أولاً إلى تحميل وطباعة العقد، ووضع الختم المادي، ثم إرساله بالبريد إلى المرسل.',\n      address: 'عنوان البريد:',\n      contactName: 'اسم المستلم:',\n      contactPhone: 'معلومات الاتصال بالمستلم:',\n      defaultValue: 'يرجى الحصول عليها من المرسل خارج الإنترنت'\n    },\n    step1: {\n      title0: 'الخطوة الأولى: تحميل وطباعة العقد الورقي',\n      title0Desc: ['يجب أن يتضمن العقد المطبوع صورة الختم الإلكتروني الموقع. يرجى', 'الحصول على ملف التوقيع الورقي.'],\n      title1: 'الخطوة الثانية: وضع الختم',\n      title1Desc: 'ضع ختم الشركة الساري للعقد على العقد الورقي.',\n      title2: ['الخطوة الثالثة:', 'تحميل النسخة الممسوحة ضوئياً،', 'العودة إلى صفحة التوقيع، انقر على زر التوقيع، أكمل التوقيع الورقي'],\n      title2Desc: ['بعد تحويل العقد الورقي إلى نسخة ممسوحة ضوئياً (ملف PDF) وتحميلها،', 'لن يظهر صورة ختمك في العقد الإلكتروني، ولكن سيتم تسجيل عملية العملية هذه.']\n    },\n    step2: {\n      title: ['تحميل نسخة العقد الورقي الممسوحة ضوئياً (ملف PDF)', 'يرجى التأكد من تحميل وتوقيع العقد الورقي قبل النقر على زر التأكيد لإنهاء عملية التوقيع الورقي.'],\n      uploadFile: 'تحميل النسخة الممسوحة ضوئياً',\n      getCodeVerify: 'الحصول على رمز التحقق من توقيع العقد',\n      isUploading: 'جاري التحميل...'\n    }\n  },\n  allowPaperSignDialog: {\n    title: 'السماح بالتوقيع الورقي',\n    content: 'هذا العقد من {senderName} إلى {receiverName}، يسمح بالتوقيع بالطريقة الورقية.',\n    tip: 'يمكنك أيضاً اختيار تحميل وثيقة العقد وطباعتها، وتقديمها للمسؤول عن ختم المؤسسة للتوقيع خارج الإنترنت.',\n    icon: 'تحويل إلى توقيع ورقي >>',\n    goSign: 'اذهب للتوقيع الإلكتروني',\n    cancel: 'إلغاء'\n  },\n  sealInconformityDialog: {\n    errorSeal: {\n      title: 'تنبيه الختم',\n      tip: 'تم اكتشاف أن صورة ختمك الحالي لا تتطابق مع هوية مؤسستك، نتيجة التعرف على صورة الختم الحالي:',\n      tip1: 'تم اكتشاف ختم مؤسسة واسم مؤسسة:',\n      tip2: 'هل تريد متابعة استخدام صورة الختم الحالية؟',\n      tip3: 'وفقاً لمتطلبات المرسل، تحتاج إلى استخدام ختم باسم المؤسسة:',\n      tip4: 'الختم',\n      tip5: 'يرجى التأكد من أن الختم يتوافق مع المتطلبات، وإلا سيؤثر على صلاحية العقد!',\n      tip6: 'غير متطابق، يرجى التأكد من أن الختم يتوافق مع متطلبات المرسل.',\n      guide: 'كيفية تحميل الختم الصحيح >>',\n      next: 'متابعة الاستخدام',\n      tip7: 'واسم ختمك لا يتوافق مع المعايير، يحتوي على كلمة \"{keyWord}\".',\n      tip8: 'تم اكتشاف أن اسم الختم لا يتوافق مع المعايير، يحتوي على كلمة \"{keyWord}\"، هل تريد متابعة الاستخدام؟'\n    },\n    exampleSeal: {\n      title: 'طريقة تحميل نموذج الختم',\n      way1: ['الطريقة الأولى:', '1- ضع ختماً فعلياً على ورقة بيضاء', '2- التقط صورة وقم بتحميلها إلى المنصة'],\n      way2: ['الطريقة الثانية:', 'استخدم مباشرة وظيفة إنشاء الختم الإلكتروني في المنصة، كما هو موضح:'],\n      errorWay: ['طريقة خاطئة:', 'حمل الختم باليد', 'صورة غير ذات صلة', 'رخصة العمل']\n    },\n    confirm: 'تأكيد',\n    cancel: 'إلغاء'\n  },\n  addSealDialog: {\n    title: 'إضافة صورة الختم',\n    dec1: 'يرجى اختيار صورة ختم من المجلد المحلي (التنسيقات: JPG، JPEG، PNG، إلخ). سيقوم النظام بدمج صورة الختم هذه في العقد الحالي.',\n    dec2: 'بعد ذلك، تحتاج إلى النقر على زر \"التوقيع\" لاجتياز التحقق من التوقيع لإتمام الختم.',\n    updateNewSeal: 'رفع ختم جديد'\n  }\n};", "map": {"version": 3, "names": ["ssoLoginConfig", "notBelongToEntTip", "operationStep", "one", "two", "three", "continue", "cancel", "tip", "sign", "sealLabelsTip", "nonMainlandCARenewalTip", "reselect", "approvalFeatures", "dialogTitle", "understand", "feature1", "feature2", "tip1", "tip2", "tip3", "tip4", "tip5", "tip6", "annotate", "delete", "edit", "operateTitle", "placeholder", "needRemark", "notNeedRemark", "switchToReceiver", "notAddEntTip", "contractPartiesYouChoose", "contractPartyFilled", "certifyOtherCompanies", "youCanAlso", "needVerification", "prompt", "submit", "addSeal", "noSealAvailable", "memberNoSealAvailable", "noticeAdminFoSeal", "requestSomeone", "requestOthersToContinue", "requestOthersToContinueSucceed", "requestSomeoneList", "electronicSeal", "changeTheSeal", "goToVerify", "noSealToC<PERSON>ose", "goVerify", "goToVerifyEnt", "digitalCertificateTip", "signDes", "signAgain", "send", "person", "ent", "entName", "account", "accountPH", "approved", "signVerification", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFail", "connectFailTip", "connectFailTip1", "connectFailTip2", "connectFailTip3", "personalMaterials", "noSupportface", "lackEntName", "errAccount", "noticeAdmin", "signDone", "signDoneTip", "approveDone", "approveDoneTip", "completeSign", "<PERSON><PERSON><PERSON><PERSON>", "stillSignTip", "signHighLightTip", "riskDetails", "noviewDifference", "highLightTip", "commonTip", "view", "start", "nextStep", "help", "faceFailed", "dualFailed", "faceFailedtips", "verifyTry", "faceLimit", "upSignReq", "reqFace", "signAfterFace", "qrcodeInvalid", "faceFirstExceed", "date", "chooseSeal", "seal", "signature", "handwrite", "mysign", "approve<PERSON>lace", "approvePlace_1", "approvePlace_2", "approveAgree", "approveReject", "signBy", "signByEnd", "sealBy", "sealByEnd", "coverBy", "applicant", "continueVeri", "registerAndReal", "goToResiter", "sureToUse", "toSign", "pleaseComplete", "confirmSign", "admin", "contratAdmin", "addToEnt", "alreadyExists", "sendMsg", "<PERSON><PERSON><PERSON><PERSON>", "title", "viewImg", "priLetter", "priLetterFromSomeone", "readLetter", "approve", "disapprove", "refuseSign", "paperSign", "refuseTip", "refuseReason", "reasonWriteTip", "refuseReasonOther", "refuseConfirm", "refuseConfirmTip", "waitAndThink", "signValidationTitle", "email", "phoneNumber", "password", "verificationCode", "mailVerificationCode", "forgetPsw", "if", "forgetPassword", "rejectionVer", "msgTip", "voiceVerCode", "SMSVerCode", "or", "emailVerCode", "SentSuccessfully", "intervalTip", "signPsw", "useSignPsw", "setSignPsw", "useVerCode", "inputVerifyCodeTip", "inputSignPwdTip", "signConfirmTip", "confirm", "signSuc", "refuseSuc", "approveSuc", "hdFile", "otherOperations", "reviewDetails", "close", "submitter", "signatory", "reviewSchedule", "signByPc", "signPageDescription", "sealBySomeone", "signDate", "download", "signPage", "signNow", "sender", "signer", "startSignTime", "signDeadLine", "authGuide", "goToHome", "tip_1", "tip_2", "tip_3", "tip_4", "tip_5", "new_tip_1", "new_tip_2", "new_tip_3", "new_tip_4", "entUserName", "idNumberForVerify", "realNameAuth", "applySeal", "signContract", "switch", "rejectReasonList", "signOperateReason", "termReason", "explainReason", "otherReason", "selectSignature", "selectS<PERSON>er", "pleaseScanToSign", "pleaseScanAliPay", "pleaseScanWechat", "requiredFaceSign", "requiredDualSign", "verCodeVerify", "applyToSign", "autoRemindAfterApproval", "cannotSignBeforeApproval", "finishSignatureBeforeSign", "uploadFileOnRightSite", "cannotApplySealNeedPay", "unlimitedNotice", "units", "contractToPrivate", "contractToPublic", "paySum", "payTotal", "fundsLack", "contactToRecharge", "deductPublicNotice", "needSignerPay", "recharge", "toSubmit", "appliedSeal", "noSeal", "noSwitchSealNeedDistribute", "viewApproveProcess", "approveProcess", "noApprove<PERSON><PERSON>nt", "knew", "noSwitchSealNeedAppend", "hadAutoSet", "setThatSignature", "setThatSeal", "applyThatSeal", "hasSetTip", "hasSetSealTip", "hasSetSignatureTip", "hasApplyForSealTip", "savedOnLeftSite", "ridingSealMinLimit", "ridingSealMaxLimit", "ridingSealMinOrMaxLimit", "noSealForRiding", "noSwitchSealNeedAppendBySelf", "gotoAppendSeal", "approvalFlowSuccessfulSet", "mandate", "loginToAppendSeal", "signIdentityAs", "enterNextContract", "fileList", "addSignerFile", "signatureFinish", "dragSignatureTip", "noticeToManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senderRequire", "senderRequireUseFollowIdentity", "suggestToAuth", "contactEntAdmin", "set<PERSON>ourAccount", "authInfoUnMatchNeedResend", "noEntNameNeedResend", "pleaseUse", "me", "myself", "reAuthBtnTip", "reAuthBtnContent", "descNoSame1", "descNoSame2", "authInfoNoSame", "authInfoNoSame2", "goHome", "authInfo", "authInfo2", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmOk", "goOnAuth", "signContractAfterAuth", "useIdentity", "inTheName", "of", "identity", "nameIs", "IDNumIs", "provideMoreAuthData", "leadToAuthBeforeSign", "groupProxyAuthNeedMore", "contactSender", "note", "identityInfo", "signNeedCoincidenceInfo", "needAuthPermissionContactAdmin", "iHadReadContract", "scrollToBottomTip", "getVerCodeFirst", "appScanVerify", "downloadBSApp", "scanned", "confirmInBSApp", "qrCodeExpired", "appKey", "goToScan", "setNotificationInUserCenter", "doNotWantUseVerCode", "try", "retry", "goToFaceVerify", "faceExceedTimes", "returnBack", "switchTo", "youCanChooseIdentityBlow", "needDrawSignatureFirst", "lacksSealNeedAppend", "manageSeal", "needDistributeSealToSelf", "chooseSealAfterAuth", "appendDrawSignature", "senderUnFill", "declare", "fileLessThan", "fileNeedUploadImg", "serverError", "oldFormatTip", "fileLimitFormatAndSize", "fileFormatImage", "fileFormatFile", "signNeedKnow", "signNeedKnowFrom", "approvalInfo", "approveNeedKnowFrom", "approveBeforeSend", "approveBeforeSign", "approveOperator", "approvalOpinion", "employeeDefault", "<PERSON><PERSON><PERSON><PERSON>", "addRidingSeal", "delRidingSeal", "file", "compressedFile", "attachmentContent", "pleaseClickView", "downloadFile", "noLabelPleaseAppend", "archiveTo", "hadArchivedToFolder", "pleaseScanToHandleWrite", "save", "remind", "riskTip", "chooseApp<PERSON><PERSON><PERSON>", "chooseAdminSign", "useSealByOther", "getSeal", "nowApplySealList", "nowAdminSealList", "chooseApplyPersonToDeal", "chooseTransferPerson", "chooseApplyPersonToMandate", "contactGroupAdminToDistributeSeal", "sealApplySentPleaseWait", "successfulSent", "authTip", "t2", "t3", "tCommon1", "tCommon2_1", "tCommon2_2", "tCommon2_3", "viewAndSign1", "viewAndSignConflict", "needSomeoneToSignature", "needToSet", "approver", "clickToSignature", "transferToOtherToSign", "signatureBy", "tipRightNumber", "tipRightIdCard", "tipRightPhoneNumber", "tipRequired", "viewContractDetail", "required", "optional", "decimalLimit", "intLimit", "invalidContract", "No", "chooseFrom2", "crossPlatformCofirm", "message", "confirmButtonText", "cancelButtonText", "sealScope", "currentContract", "allContract", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixTextDisplay", "allPage", "notJoinTip", "signJa", "beforeSignTip1", "beforeSignTip2", "beforeSignTip3", "beforeSignTip4", "beforeSignTip5", "beforeSignTip6", "beforeSignTip7", "entNamePlaceholder", "corporateNumberPlaceholder", "corporateNumber", "singerNamePlaceholder", "<PERSON><PERSON><PERSON>", "itsMe", "wrongInformation", "confirmChange", "communicateSender1", "communicateSender2", "createSeal", "emptyErr", "areaRegister", "jp", "cn", "are", "other", "plsSelect", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "comNum", "buyRecord", "selectArea", "uaeTip1", "uaeTip2", "uaeTip3", "signPC", "commonSign", "contractVerification", "VerCodeVerify", "QrCodeVerify", "verifyTip", "verifyAllTip", "selectSeal", "adminGuideTip", "toAddSealWithConsole", "use", "toAddSeal", "mySeal", "operationCompleted", "FDASign", "signer<PERSON>dd", "signerEdit", "editTip", "inputNameTip", "inputName", "signerNameFillTip", "plsInput", "customInput", "signPlace<PERSON>y<PERSON><PERSON><PERSON>", "signGuide", "howDragSeal", "howDragSignature", "iKnow", "step", "two1", "two2", "dragSeal", "continueDragSeal", "dragSignature", "continueDragSignature", "dragPlace", "notR<PERSON>ind", "signTip", "continueOperation", "success", "exitApproval", "continueApproval", "next", "none", "approvalProcess", "receiver", "contractDetail", "downloadBtn", "tips", "SigningCompleted", "submitCompleted", "noTurnSign", "noRightSign", "noNeedSign", "ApprovalCompleted", "contractRevoked", "contractRefused", "linkExpired", "contractClosed", "approvalReject", "approving", "viewContract", "viewContractList", "needMeSign", "downloadContract", "signed", "approval", "personHas", "personHave", "person<PERSON>asnot", "<PERSON><PERSON><PERSON><PERSON>", "headsTaskDone", "headsTaskNotDone", "taskStatusBetween", "cannotDownload", "privateStorage", "beenDeleted", "unActive", "back", "contratStatusDes", "contractConditionDes", "contractIng", "contractComplete", "dataProduct", "btnText", "signOnGoing", "operate", "freeContract", "sendContract", "congratulations", "carbonSaving", "signGift", "followPublic", "congratulations<PERSON><PERSON><PERSON>", "carbonSavingSingle", "viewContractTip", "congratulationsCn", "carbonSavingSingleCn", "carbonVerification", "ok", "prepare", "sealArea", "senderNotice", "preSetDialogConfirm", "preSetDialogContact", "preSetDialogInfo", "preSetDialogTitle", "initialValues", "proxyUpload", "signHeaderTitle", "step1", "confirmSender", "step2", "uploadFile", "step3", "addSigner", "actionDemo", "isUploadingErr", "noUploadFileErr", "noContractTitleErr", "contractTypeErr", "expiredDateErr", "noExpiredDateErr", "<PERSON><PERSON><PERSON><PERSON><PERSON>rr", "noRecipientsErr", "noAccountErr", "noUserNameErr", "noIDNumberErr", "accountFormatErr", "userNameFormatErr", "enterpriseNameErr", "idNumberForVerifyErr", "signer<PERSON>rr", "noSignerErr", "lackAttachmentNameErr", "repeatRecipientsErr", "innerContact", "outerContact", "search", "accountSelected", "groupNameAll", "unclassified", "beExcel", "usePdf", "usePdfFile", "fileNameMoreThan", "need<PERSON>dd<PERSON><PERSON>", "addSender", "addReceiver", "English", "Japanese", "Chinese", "Arabic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limitFaceConfigTip", "individual", "enterprise", "addInstructions", "instructionsContent", "addContractingInfo", "contractingInfoContent", "payer", "handWriting", "realName", "sameTip", "proxy", "aboradTip", "busRole", "busRoleTip", "busRolePlaceholder", "handWritingTip", "instructions", "contractingParty", "signer<PERSON><PERSON>", "afterReadingTitle", "afterReading", "handWritingTips", "SsTitle", "SsTip", "stamp", "Ss", "mutexError", "handWriteNotAllowed", "forceHandWrite", "faceFirst", "faceVerify", "attachmentRequired", "newAttachmentRequired", "attachmentError", "<PERSON><PERSON><PERSON>", "orderSignLabel", "contactAddress", "signOrder", "accountPlaceholder", "accountPlaceholderJa", "accountReceptionCollection", "accountReceptionCollectionTip1", "accountReceptionCollectionTip2", "signSub<PERSON><PERSON>erson", "nameTips", "requiredNameTips", "entOperatorNameTips", "needAuth", "operatorNeedAuth", "signSubjectEnt", "entNameTips", "operator", "more", "faceFirstTips", "mustFace", "mustHandWrite", "fillIDNumber", "fillNoticeCall", "fillNoticeCallTips", "addNotice", "attachTips", "faceSign", "faceSignTips", "handWriteNotAllowedTips", "handWriteTips", "idNumberTips", "verifyBefore", "verify", "verifyTips", "verifyTips2", "sendToThirdPlatform", "platFormName", "fillThirdPlatFormName", "attach", "attachName", "exampleID", "attachInfo", "attachInfoTips", "addAttachRequire", "addSignEnt", "addSign<PERSON>erson", "selectContact", "searchVerify", "fillImageContentTips", "findContact", "signer<PERSON><PERSON>s", "add", "notAdd", "cc", "notNeedAuth", "operatorNotNeedAuth", "extracting", "autoFill", "failExtracting", "noEntNameErr", "riskCues", "riskCuesMsg", "confirmBtnText", "cancelBtnText", "attachLengthErr", "collapse", "expand", "saySomething", "addImage", "addImageTips", "give", "fileMax", "signerLimit", "showExamle", "downloadExamle", "addReceiverGuide", "guideTitle", "receiverType", "asEntSign", "signatureSub", "vipOnly", "sealSub", "stampSub", "confirmSeal", "confirmSealSub", "asPersonSign", "asPersonSignTip", "asPersonSignDesc", "scanSign", "scanSignDesc", "selectSignTypeTip", "entSign", "stampSign", "requestSeal", "linkContract", "connectMore", "revoke", "overdue", "approvalNotPassed", "reject", "signing", "complete", "approvaling", "disconnect", "disconnectSuccess", "connectLimit", "field", "fieldTip", "error", "accountCharge", "notice", "able", "unable", "notify", "noNotify", "ridingStamp", "watermark", "senderSignature", "clickDecoration", "decoration", "sysError", "partedMarkedError", "fieldTitle", "contractDispatchApply", "contractNeedYouSign", "ifSignRightNow", "signRightNow", "signLater", "signaturePositionErr", "sendSucceed", "qrCodeTips", "pagesField", "suitableWidth", "signCheck", "locateSignaturePosition", "locateTips", "dragSignaturePosition", "<PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "totalPages", "charge", "costTips", "to<PERSON>harge", "contractNeedCharge", "chooseApprover", "submitApproval", "autoSendAfterApproval", "chooseApprovalFlow", "completeApprovalFlow", "viewPrivateLetter", "addPrivateLetter", "append", "privateLetter", "maximum5M", "uploadServerFailure", "uploadFailure", "pager", "text", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "addressBook", "innerMember", "operation", "outerContacts", "myContacts", "selected", "loadMore", "end", "dataBoxInvite", "imgName", "saveQrcode", "copy", "copySuccess", "copyFailed", "shareView", "role", "link", "signerMessage", "rolePlaceholder", "notePlaceholder", "generateLink", "regenerateLink", "inputAccount", "inputCorrectAccount", "accountInputTip", "shareLinkTip1", "shareLinkTip", "linkTip1", "linkTip2", "recoverSpecialSeal", "description1", "description2", "postRecover", "requestSend", "stepText", "needUploadFile", "uploadError", "downloadPaperFile", "step0", "address", "contactName", "contactPhone", "defaultValue", "title0", "title0Desc", "title1", "title1Desc", "title2", "title2Desc", "getCodeVerify", "isUploading", "allowPaperSignDialog", "content", "icon", "goSign", "sealInconformityDialog", "errorSeal", "guide", "exampleSeal", "way1", "way2", "errorWay", "addSealDialog", "dec1", "dec2", "updateNewSeal"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/sign/sign-ar.js"], "sourcesContent": ["export default {\n    ssoLoginConfig: {\n        notBelongToEntTip: 'تحتاج إلى إعادة تسجيل الدخول إلى منصة BestSign لإرسال العقد (أو إدارة القوالب)',\n        operationStep: {\n            one: 'الخطوة الأولى: بعد النقر على متابعة، العودة إلى صفحة تسجيل الدخول',\n            two: 'الخطوة الثانية: أدخل كلمة المرور للدخول إلى منصة BestSign',\n            three: 'الخطوة الثالثة: إرسال العقد (أو إدارة القوالب)',\n        },\n        continue: 'متابعة',\n        cancel: 'إلغاء',\n        tip: 'تنبيه',\n    },\n    sign: {\n        sealLabelsTip: 'تحتاج إلى وضع {sealLabelslen} ختم على العقد. سيقوم {personStr} بوضع {otherSealLen} أختام، والـ {mySealLen} المتبقية ستقوم أنت بختمها شخصياً. الأختام المطلوبة معروضة على الصفحة. يرجى التأكيد للمتابعة.',\n        continue: 'متابعة',\n        nonMainlandCARenewalTip: 'بعد طلب التجديد، سيرفض النظام تلقائياً نتيجة التحقق الأصلية. يرجى إكمال التحقق في أقرب وقت.',\n        reselect: 'إعادة الاختيار',\n        approvalFeatures: {\n            dialogTitle: 'تقديم الميزات الجديدة',\n            understand: 'فهمت',\n            feature1: 'تعليق توضيحي على النص',\n            feature2: 'تمييز الحقول',\n            tip1: 'انقر على الزر لتمييز جميع \"حقول محتوى القالب\" في العقد لتسهيل التقاط المعلومات الرئيسية.',\n            tip2: 'انقر على زر التلميح في الأسفل لتفعيل تمييز حقول محتوى القالب.',\n            tip3: 'من خلال التمييز، يمكنك تحديد موقع حقول تعبئة المحتوى في العقد بسرعة وإكمال الموافقة بكفاءة.',\n            tip4: 'اضغط مع الاستمرار على الماوس لتحديد فقرة ثم حرر الماوس، انقر على زر التعليق لإضافة نص تعليق توضيحي، بعد الانتهاء انقر على تعديل أو حذف. يمكن عرض محتوى التعليق في صفحة تفاصيل العقد - سجل العمليات الداخلية للشركة.',\n            tip5: 'الخطوة الأولى: حدد حقل النص المراد التعليق عليه، أضف التعليق;',\n            tip6: 'الخطوة الثانية: انقر لتحرير أو حذف التعليق.',\n            annotate: 'تعليق',\n            delete: 'حذف',\n            edit: 'تعديل',\n            operateTitle: 'إضافة تعليق للموافقة',\n            placeholder: 'لا يتجاوز 255 حرفاً',\n        },\n        needRemark: 'لا تزال بحاجة إلى إضافة ملاحظة',\n        notNeedRemark: 'لا تحتاج إلى إضافة ملاحظة',\n        switchToReceiver: 'تم التبديل إلى {receiver}',\n        notAddEntTip: 'المستخدم الحالي ليس عضواً في هذه المؤسسة، يرجى الاتصال بالمدير الرئيسي للانضمام إلى المؤسسة.',\n        contractPartiesYouChoose: 'يمكنك اختيار الأطراف المتعاقدة:',\n        contractPartyFilled: 'الطرف المتعاقد الذي حدده المرسل هو:',\n        certifyOtherCompanies: 'التحقق من مؤسسات أخرى',\n        youCanAlso: 'يمكنك أيضاً:',\n        needVerification: 'تحتاج إلى التحقق من هويتك قبل التوقيع',\n        prompt: 'تنبيه',\n        submit: 'تأكيد',\n        cancel: 'إلغاء',\n        sign: 'توقيع الآن',\n        addSeal: 'يرجى تسجيل الدخول إلى موقع BestSign باستخدام الكمبيوتر لإضافة ختم',\n        noSealAvailable: 'عذراً، ليس لديك أختام متاحة حالياً، يرجى الاتصال بمدير المؤسسة لإضافة ختم وتفويضه.',\n        memberNoSealAvailable: 'لا توجد أختام متاحة حالياً، يرجى الاتصال بالمدير للتكوين قبل التوقيع. أو اتصل بالمدير الرئيسي شخصياً للتكوين.',\n        noticeAdminFoSeal: 'إرسال إشعار للمدير',\n        requestSomeone: 'طلب تحقق شخص آخر',\n        requestOthersToContinue: 'إخطار المدير لاستكمال التحقق من الهوية',\n        requestOthersToContinueSucceed: 'تم إرسال إشعار إلى المدير',\n        requestSomeoneList: 'طلب من الأشخاص التاليين إكمال التحقق من الهوية:',\n        electronicSeal: 'الختم الإلكتروني',\n        changeTheSeal: 'لا تريد استخدام هذا الختم؟ يمكنك تغييره بعد التحقق من الهوية',\n        goToVerify: 'اذهب للتحقق من الهوية',\n        noSealToChoose: 'لا توجد أختام متاحة للتبديل، إذا كنت بحاجة إلى إدارة الأختام، يرجى التحقق من الهوية أولاً',\n        goVerify: 'اذهب للتحقق',\n        goToVerifyEnt: 'اذهب للتحقق من المؤسسة',\n        digitalCertificateTip: 'BestSign يقوم بتفعيل شهادتك الرقمية',\n        signDes: 'أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n        signAgain: 'متابعة التوقيع',\n        send: 'إرسال',\n        person: 'شخصي',\n        ent: 'مؤسسة',\n        entName: 'اسم المؤسسة',\n        account: 'الحساب',\n        accountPH: 'الهاتف أو البريد الإلكتروني',\n        approved: 'موافق',\n        signVerification: 'التحقق من التوقيع',\n        cannotReview: 'لا يمكن عرض العقد',\n        connectFail: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود.',\n        connectFailTip: 'يمكنك محاولة الحلول التالية:',\n        connectFailTip1: '1. تحديث الصفحة.',\n        connectFailTip2: '2. انتظر بصبر وحاول مرة أخرى لاحقاً. قد يكون السبب هو حدوث خلل في الخادم الذي نشرته المؤسسة المرسلة، ويحتاج فريق تكنولوجيا المعلومات إلى وقت لإعادة تشغيل الخادم.',\n        connectFailTip3: '3. هل أكدت المؤسسة المرسلة أنك تحتاج إلى استخدام شبكة WiFi محددة للوصول؟ إذا كان هناك مثل هذا التوضيح، فأنت بحاجة إلى تغيير الشبكة المتصلة بهاتفك أو جهاز الكمبيوتر.',\n        personalMaterials: 'يطلب المرسل منك توفير مزيد من مواد التحقق',\n        noSupportface: 'يطلب مُنشئ العقد التوقيع بالتعرف على الوجه، ولكن لا يتوفر التوقيع بالتعرف على الوجه لغير المقيمين في البر الرئيسي، يرجى الاتصال بالمُنشئ لتعديل متطلبات التوقيع',\n        lackEntName: 'يرجى إدخال اسم المؤسسة',\n        errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',\n        noticeAdmin: 'طلب الانضمام',\n        signDone: 'اكتمل التوقيع',\n        signDoneTip: 'لقد وقعت هذا العقد',\n        approveDone: 'اكتملت الموافقة',\n        approveDoneTip: 'لقد وافقت على هذا العقد',\n        completeSign: 'يرجى النقر على \"موقع الختم\" أو \"موقع التوقيع\" لإكمال التوقيع',\n        fillFirst: 'يرجى ملء محتوى العقد في مربع الإدخال أولاً',\n        stillSignTip: 'بعد توقيعك على {alias} هذا، قد يقوم الموقعون الآخرون بتغيير محتوى {alias}، هل تريد المتابعة؟',\n        signHighLightTip: 'يوجد {count} موقع في {alias} يمكن إضافته أو تعديله',\n        riskDetails: 'تفاصيل المخاطر',\n        noviewDifference: 'نظراً لأن المُنشئ قد فعّل وظيفة السماح للموقعين بملء الحقول الثابتة في {alias}، فقد يقوم الموقعون الآخرون بتغيير محتوى العقد الذي حدده المُنشئ. لا يقوم BestSign بمراجعة الاختلافات في المحتوى بين النسخة قبل التوقيع والنسخة السارية. عند اكتمال توقيعك على {alias}، يُعتبر أنك توافق على إضافة أو تعديل محتوى الحقول الثابتة في {alias} من قبل الموقعين الآخرين، وتقر بالنسخة السارية بعد اكتمال توقيع جميع الأطراف على {alias}.\\n' +\n            'إذا كنت لا توافق على السماح للموقعين الآخرين بتغيير حقول {alias} بعد توقيعك، يمكنك رفض هذا التوقيع والتفاوض مع المرسل (أي مطالبة المُنشئ بإغلاق وظيفة \"ملء الحقول من قبل الموقع\" لتجنب المخاطر المرتبطة).',\n        highLightTip: 'سيتم عرض هذا المحتوى المحفوف بالمخاطر بتأثير \"تمييز\"، يرجى التحقق بعناية. يمكن إلغاء التمييز بتحديث الصفحة.',\n        commonTip: 'تنبيه',\n        understand: 'فهمت',\n        view: 'عرض',\n        start: 'بدء',\n        nextStep: 'الخطوة التالية',\n        help: 'مساعدة',\n        faceFailed: 'عذراً، فشلت مطابقة الوجه',\n        dualFailed: 'نعتذر، لم تنجح عملية التحقق من التسجيل المزدوج. يرجى التحقق من معلوماتك والمحاولة مرة أخرى.',\n        faceFailedtips: 'تنبيه',\n        verifyTry: 'يرجى التحقق من معلومات الهوية والمحاولة مرة أخرى',\n        faceLimit: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم',\n        upSignReq: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم، يرجى المحاولة غداً أو الاتصال بمُنشئ العقد لتعديل متطلبات التوقيع',\n        reqFace: 'يطلب المرسل منك التحقق من الوجه',\n        signAfterFace: 'يمكنك إكمال توقيع العقد بعد اجتياز التحقق من الوجه',\n        qrcodeInvalid: 'انتهت صلاحية معلومات رمز QR، يرجى التحديث',\n        faceFirstExceed: 'فشل التحقق من الوجه، سيتم استخدام رمز التحقق بدلاً من ذلك',\n        date: 'التاريخ',\n        chooseSeal: 'اختيار الختم',\n        seal: 'ختم',\n        signature: 'توقيع',\n        handwrite: 'كتابة يدوية',\n        mysign: 'توقيعي',\n        approvePlace: 'رسالة الموافقة، اختيارية',\n        approvePlace_1: 'رسالة الموافقة',\n        approvePlace_2: 'اختياري، لا يتجاوز 255 حرفاً.',\n        approveAgree: 'نتيجة الموافقة: موافق',\n        approveReject: 'نتيجة الموافقة: مرفوض',\n        signBy: 'بواسطة',\n        signByEnd: 'ختم',\n        sealBy: 'بواسطة',\n        sealByEnd: 'توقيع',\n        coverBy: 'يحتاج إلى ختم',\n        applicant: 'مقدم الطلب',\n        continueVeri: 'متابعة التحقق',\n        registerAndReal: 'يرجى التسجيل والتحقق من الهوية',\n        goToResiter: 'يرجى التسجيل والتحقق',\n        sureToUse: 'تأكيد الاستخدام',\n        toSign: 'توقيع?',\n        pleaseComplete: 'يرجى إكمال',\n        confirmSign: 'ثم تأكيد التوقيع',\n        admin: 'المدير',\n        contratAdmin: 'يرجى الاتصال بالمدير لإضافة حسابك',\n        addToEnt: 'كعضو في المؤسسة',\n        alreadyExists: 'موجود بالفعل في BestSign',\n        sendMsg: 'سيرسل BestSign المحتوى التالي إلى المدير عبر الرسائل القصيرة:',\n        applyJoin: 'طلب الانضمام',\n        title: 'العنوان',\n        viewImg: 'عرض الصورة',\n        priLetter: 'رسالة خاصة',\n        priLetterFromSomeone: 'رسالة خاصة من {name}',\n        readLetter: 'فهمت',\n        approve: 'موافق',\n        disapprove: 'رفض',\n        refuseSign: 'رفض التوقيع',\n        paperSign: 'التحول إلى التوقيع الورقي',\n        refuseTip: 'يرجى اختيار سبب الرفض',\n        refuseReason: 'تعبئة سبب رفض التوقيع يساعد الطرف الآخر على فهم مشكلتك وتسريع عملية العقد',\n        reasonWriteTip: 'يرجى إدخال سبب رفض التوقيع',\n        refuseReasonOther: 'المزيد من أسباب رفض التوقيع (اختياري) | المزيد من أسباب رفض التوقيع (مطلوب)',\n        refuseConfirm: 'رفض التوقيع',\n        refuseConfirmTip: 'أنت ترفض التوقيع بسبب \"{reason}\"، هل تريد المتابعة؟ بعد التأكيد، لن تتمكن من التوقيع مرة أخرى.',\n        waitAndThink: 'دعني أفكر مرة أخرى',\n        signValidationTitle: 'التحقق من التوقيع',\n        email: 'البريد الإلكتروني',\n        phoneNumber: 'رقم الهاتف',\n        password: 'كلمة المرور',\n        verificationCode: 'رمز التحقق',\n        mailVerificationCode: 'رمز التحقق',\n        forgetPsw: 'نسيت كلمة المرور',\n        if: '، هل',\n        forgetPassword: 'نسيت كلمة المرور',\n        rejectionVer: 'التحقق من رفض التوقيع',\n        msgTip: 'لم تستلم الرسالة القصيرة؟ جرب',\n        voiceVerCode: 'رمز التحقق الصوتي',\n        SMSVerCode: 'رمز التحقق عبر الرسائل القصيرة',\n        or: 'أو',\n        emailVerCode: 'رمز التحقق عبر البريد الإلكتروني',\n        SentSuccessfully: 'تم الإرسال بنجاح!',\n        intervalTip: 'الفاصل الزمني بين الإرسال قصير جداً',\n        signPsw: 'كلمة مرور التوقيع',\n        useSignPsw: 'استخدام كلمة مرور التوقيع للتحقق',\n        setSignPsw: 'إعداد التحقق بكلمة مرور التوقيع',\n        useVerCode: 'استخدام رمز التحقق',\n        inputVerifyCodeTip: 'يرجى إدخال رمز التحقق',\n        inputSignPwdTip: 'يرجى إدخال كلمة مرور التوقيع',\n        signConfirmTip: {\n            1: 'هل أنت متأكد من أنك تريد توقيع {contract} هذا؟',\n            2: 'النقر على زر التأكيد سيؤدي إلى التوقيع الفوري على {contract} هذا',\n            confirm: 'تأكيد التوقيع',\n        },\n        signSuc: 'تم التوقيع بنجاح',\n        refuseSuc: 'تم رفض التوقيع بنجاح',\n        approveSuc: 'تمت الموافقة بنجاح',\n        hdFile: 'عرض الملف عالي الدقة',\n        otherOperations: 'عمليات أخرى',\n        reviewDetails: 'تفاصيل الموافقة',\n        close: 'إغلاق',\n        submitter: 'مقدم الطلب',\n        signatory: 'الموقع',\n        reviewSchedule: 'مراحل المراجعة',\n        signByPc: 'تم التوقيع بواسطة {name}',\n        signPageDescription: 'الصفحة {index} من {total}',\n        sealBySomeone: 'تم الختم بواسطة {name}',\n        signDate: 'تاريخ التوقيع',\n        download: 'تحميل',\n        signPage: 'عدد الصفحات: {page}',\n        signNow: 'التوقيع الآن',\n        sender: 'المرسل',\n        signer: 'الموقع',\n        startSignTime: 'وقت بدء التوقيع',\n        signDeadLine: 'الموعد النهائي للتوقيع',\n        authGuide: {\n            goToHome: 'العودة للصفحة الرئيسية',\n            tip_1: 'بعد اكتمال التحقق، يمكنك عرض وتوقيع العقد.',\n            tip_2: 'يرجى استخدام الهوية | للتحقق.',\n            tip_3: 'تم إرسال العقد',\n            tip_4: 'يرجى الاتصال بمنشئ العقد | لتغيير المستلم.',\n            tip_5: 'هويتك المؤكدة | لا يمكنها عرض العقد',\n            new_tip_1: 'بناءً على متطلبات المرسل، تحتاج إلى إكمال الخطوات التالية:',\n            new_tip_2: 'بناءً على متطلبات المرسل، تحتاج إلى:',\n            new_tip_3: 'إكمال الخطوات التالية.',\n            new_tip_4: 'إذا كان لديك بالفعل صلاحيات الختم، سيتم تجاوز الخطوة 2 تلقائياً',\n            entUserName: 'الاسم:',\n            idNumberForVerify: 'رقم الهوية:',\n            realNameAuth: 'التحقق من الهوية الحقيقية',\n            applySeal: 'طلب ختم',\n            signContract: 'توقيع العقد',\n        },\n        switch: 'تبديل',\n        rejectReasonList: {\n            // authReason: '不想/不会做实名认证',\n            signOperateReason: 'لدي استفسارات حول عملية التوقيع/التحقق، وأحتاج إلى مزيد من التواصل',\n            termReason: 'لدي تحفظات على شروط/محتوى العقد، وأحتاج إلى مزيد من التواصل',\n            explainReason: 'لست على دراية بمحتوى العقد، يرجى الإخطار مسبقاً',\n            otherReason: 'أخرى (يرجى ذكر السبب)',\n        },\n        selectSignature: 'اختيار التوقيع',\n        selectSigner: 'اختيار الموقع',\n        pleaseScanToSign: 'يرجى المسح الضوئي للتوقيع باستخدام Alipay أو WeChat',\n        pleaseScanAliPay: 'يرجى مسح رمز QR باستخدام تطبيق Alipay للتوقيع',\n        pleaseScanWechat: 'يرجى مسح رمز QR باستخدام تطبيق WeChat للتوقيع',\n        requiredFaceSign: 'يطلب مرسل العقد منك التوقيع بالتعرف على الوجه',\n        requiredDualSign: 'يطلب مرسل العقد منك إكمال عملية التحقق من التسجيل المزدوج',\n        verCodeVerify: 'التحقق من رمز التحقق',\n        applyToSign: 'طلب توقيع العقد',\n        autoRemindAfterApproval: '*بعد الموافقة، سيتم إرسال تذكير توقيع تلقائي للموقع',\n        cannotSignBeforeApproval: 'لم تكتمل الموافقة، لا يمكن التوقيع حالياً!',\n        finishSignatureBeforeSign: 'يرجى إكمال الختم/التوقيع قبل تأكيد التوقيع',\n        uploadFileOnRightSite: 'لديك مرفقات لم يتم تحميلها بعد، يرجى تحميلها في الشريط الجانبي الأيمن أولاً',\n        cannotApplySealNeedPay: 'هذا العقد يتطلب منك الدفع، لا يدعم طلب ختم شخص آخر',\n        unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',\n        units: '{num} نسخة',\n        contractToPrivate: 'عقد شخصي',\n        contractToPublic: 'عقد مؤسسي',\n        paySum: 'المجموع {sum} يتطلب دفعك',\n        payTotal: 'المجموع {total} يوان.',\n        fundsLack: 'عدد العقود المتاحة لديك غير كافٍ، لضمان توقيع العقد بسلاسة، نقترح إعادة الشحن على الفور.',\n        contactToRecharge: 'يرجى الاتصال بالمدير الرئيسي لإعادة الشحن.',\n        deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية.',\n        needSignerPay: 'قام مرسل العقد بتعيين الدفع على الطرف المقابل، وحددك لدفع رسوم العقد.',\n        recharge: 'إعادة الشحن',\n        toSubmit: 'تقديم',\n        appliedSeal: 'تم تقديم طلب الختم',\n        noSeal: 'لا يوجد ختم',\n        noSwitchSealNeedDistribute: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بمدير المؤسسة الرئيسي لإضافة ختم وتفويضه',\n        viewApproveProcess: 'عرض عملية الموافقة',\n        approveProcess: 'عملية الموافقة',\n        noApproveContent: 'لم يتم تقديم مواد الموافقة',\n        knew: 'فهمت',\n        noSwitchSealNeedAppend: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بالمدير لإضافة ختم',\n        hadAutoSet: 'تم تلقائياً في {num} موضع آخر',\n        setThatSignature: 'وضع هذا التوقيع',\n        setThatSeal: 'وضع هذا الختم',\n        applyThatSeal: 'طلب هذا الختم',\n        hasSetTip: 'تم الوضع تلقائياً في {index} موضع آخر',\n        hasSetSealTip: 'تم وضع هذا الختم تلقائياً في {index} موضع آخر',\n        hasSetSignatureTip: 'تم وضع هذا التوقيع تلقائياً في {index} موضع آخر',\n        hasApplyForSealTip: 'تم طلب هذا الختم تلقائياً في {index} موضع آخر',\n        savedOnLeftSite: 'تم الحفظ في شريط التوقيع على اليسار',\n        ridingSealMinLimit: 'المستند صفحة واحدة فقط، لا يمكن وضع ختم متداخل',\n        ridingSealMaxLimit: 'يتجاوز 146 صفحة، لا يدعم وضع ختم متداخل',\n        ridingSealMinOrMaxLimit: 'المستند إما صفحة واحدة أو يتجاوز 146 صفحة، لا يمكن وضع ختم متداخل',\n        noSealForRiding: 'ليس لديك ختم متاح للاستخدام، لا يمكن وضع ختم متداخل',\n        noSwitchSealNeedAppendBySelf: 'لا توجد أختام متاحة للتبديل، يمكنك الذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',\n        gotoAppendSeal: 'اذهب لإضافة ختم',\n        approvalFlowSuccessfulSet: 'تم إعداد تدفق الموافقة بنجاح',\n        mandate: 'موافقة على التفويض',\n        loginToAppendSeal: 'يمكنك أيضاً تسجيل الدخول إلى BestSign باستخدام الكمبيوتر والذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',\n        signIdentityAs: 'تقوم حالياً بالتوقيع باسم {person}',\n        enterNextContract: 'انتقل إلى العقد التالي',\n        fileList: 'قائمة الملفات',\n        addSignerFile: 'إضافة مواد ملحقة',\n        signatureFinish: 'تم الختم/التوقيع بالكامل',\n        dragSignatureTip: 'يرجى سحب الختم/التاريخ التالي وإفلاته في المستند، يمكن السحب والإفلات عدة مرات',\n        noticeToManager: 'إرسال إشعار للمدير',\n        gotoAuthPerson: 'اذهب للتحقق الشخصي',\n        senderRequire: 'يطلب المرسل',\n        senderRequireUseFollowIdentity: 'يطلب المرسل منك استيفاء أحد الهويات التالية',\n        suggestToAuth: 'لم تقم بالتحقق من هويتك بعد، نقترح التحقق من هويتك قبل التوقيع',\n        contactEntAdmin: 'يرجى الاتصال بمدير المؤسسة الرئيسي',\n        setYourAccount: 'لتعيين حسابك',\n        authInfoUnMatchNeedResend: 'للتوقيع على العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد',\n        noEntNameNeedResend: 'لم يتم تحديد اسم المؤسسة الموقعة، لا يمكن توقيع هذا العقد، يرجى الاتصال بالمُنشئ لإعادة إرسال العقد',\n        pleaseUse: 'يرجى استخدام',\n        me: 'أنا',\n        myself: 'نفسي،',\n        reAuthBtnTip: 'أنا المستخدم الفعلي لرقم الهاتف هذا،',\n        reAuthBtnContent: 'بعد إعادة التحقق من الهوية، سيتم رفض التحقق الأصلي لهذا الحساب، يرجى التأكيد.',\n        descNoSame1: ' للتوقيع على العقد بهوية',\n        descNoSame2: 'هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',\n        authInfoNoSame: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',\n        authInfoNoSame2: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية الأساسية لحسابك الحالي المسجل.',\n        goHome: 'العودة إلى قائمة العقود>>',\n        authInfo: 'تم اكتشاف أن هوية حسابك الحالي المؤكدة هي ',\n        authInfo2: 'تم اكتشاف أن معلومات الهوية الأساسية لحسابك الحالي هي ',\n        in: 'في',\n        finishAuth: 'أكمل التحقق من الهوية للتوقيع القانوني على العقد',\n        ask: 'هل تريد متابعة التوقيع بالحساب الحالي؟',\n        reAuthBtnText: 'نعم، أريد إعادة التحقق من الهوية والتوقيع بهذا الحساب',\n        changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',\n        changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',\n        changePhoneTip2: 'لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد توقيعك.',\n        confirmOk: 'تأكيد',\n        goOnAuth: {\n            0: 'للتحقق،',\n            1: 'يرجى التحقق من الهوية،',\n            2: 'للتحقق من الهوية،',\n        },\n        signContractAfterAuth: {\n            0: 'بعد اكتمال التحقق، يمكنك توقيع العقد.',\n            1: 'بعد اكتمال التحقق، يمكنك توقيع العقد.',\n        },\n        useIdentity: 'باستخدام هوية {name}',\n        inTheName: 'باسم',\n        of: 'من',\n        identity: 'هوية',\n        nameIs: 'الاسم هو',\n        IDNumIs: 'رقم الهوية هو',\n        provideMoreAuthData: 'توفير مزيد من مواد التحقق',\n        leadToAuthBeforeSign: 'متابعة التحقق قبل التوقيع على العقد',\n        groupProxyAuthNeedMore: 'حالة التحقق الحالية هي تحقق بالوكالة للمجموعة، إذا كنت تريد التوقيع بشكل منفصل، يرجى توفير مواد تحقق إضافية',\n        contactSender: 'إذا كان لديك أي استفسارات، يرجى الاتصال بالمرسل.',\n        note: 'ملاحظة:',\n        identityInfo: 'معلومات الهوية',\n        signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً للتوقيع على العقد.',\n        needAuthPermissionContactAdmin: 'ليس لديك صلاحية التحقق من الهوية حالياً، يرجى الاتصال بالمدير',\n        iHadReadContract: 'قرأت وفهمت محتوى {alias}',\n        scrollToBottomTip: 'تحتاج إلى التمرير حتى الصفحة الأخيرة',\n        getVerCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',\n        appScanVerify: 'التحقق بمسح رمز QR في تطبيق BestSign',\n        downloadBSApp: 'تحميل تطبيق BestSign',\n        scanned: 'تم المسح بنجاح',\n        confirmInBSApp: 'يرجى تأكيد التوقيع في تطبيق BestSign',\n        qrCodeExpired: 'انتهت صلاحية رمز QR، يرجى التحديث والمحاولة مرة أخرى',\n        appKey: 'التحقق الأمني للتطبيق',\n        goToScan: 'اذهب للمسح',\n        setNotificationInUserCenter: 'يرجى تعيين طريقة الإشعار في مركز المستخدم أولاً',\n        doNotWantUseVerCode: 'لا أريد استخدام رمز التحقق',\n        try: 'جرب',\n        retry: 'إعادة المحاولة',\n        goToFaceVerify: 'اذهب للتحقق من الوجه',\n        faceExceedTimes: 'تم الوصول إلى الحد الأقصى لعدد مرات التحقق من الوجه اليوم',\n        returnBack: 'عودة',\n        switchTo: 'التبديل إلى',\n        youCanChooseIdentityBlow: 'يمكنك اختيار أحد الأطراف المتعاقدة التالية',\n        needDrawSignatureFirst: 'ليس لديك توقيع، يرجى إضافة توقيع يدوي أولاً',\n        lacksSealNeedAppend: 'لم تقم بإضافة أي أختام بعد، يرجى إضافة ختم أولاً.',\n        manageSeal: 'إدارة الأختام',\n        needDistributeSealToSelf: 'ليس لديك ختم متاح، يرجى تعيين نفسك كحامل للختم أولاً',\n        chooseSealAfterAuth: 'لا تريد استخدام الختم أعلاه؟ يمكنك تغيير الختم بعد التحقق من الهوية',\n        appendDrawSignature: 'إضافة توقيع يدوي',\n        senderUnFill: '(لم يملأ المرسل)',\n        declare: 'توضيح',\n        fileLessThan: 'يرجى تحميل ملف أقل من {num}M',\n        fileNeedUploadImg: 'يرجى استخدام تنسيقات الملفات المدعومة عند التحميل',\n        serverError: 'واجه الخادم مشكلة صغيرة، يرجى المحاولة مرة أخرى لاحقاً',\n        oldFormatTip: 'يدعم تنسيقات jpg، png، jpeg، pdf، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',\n        fileLimitFormatAndSize: 'لا يتجاوز عدد صور المواد الواحدة 10 صور.',\n        fileFormatImage: 'يدعم تنسيقات jpg، png، jpeg، حجم الصورة الواحدة لا يتجاوز 20M، يسمح بتحميل 10 صور',\n        fileFormatFile: 'يدعم تنسيقات pdf، excel، word، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',\n        signNeedKnow: 'ملاحظات التوقيع',\n        signNeedKnowFrom: 'ملاحظات التوقيع من {sender}',\n        approvalInfo: 'ملاحظات الموافقة',\n        approveNeedKnowFrom: 'مواد الموافقة المقدمة من {sender}-{sendEmployeeName} ({approvalType})',\n        approveBeforeSend: 'الموافقة قبل إرسال العقد',\n        approveBeforeSign: 'الموافقة قبل توقيع العقد',\n        approveOperator: 'الموافق',\n        approvalOpinion: 'رسالة الموافقة',\n        employeeDefault: 'موظف',\n        setLabel: 'تعيين العلامة',\n        addRidingSeal: 'إضافة ختم متداخل',\n        delRidingSeal: 'حذف الختم المتداخل',\n        file: 'ملف مرفق',\n        compressedFile: 'ملف مضغوط',\n        attachmentContent: 'محتوى المرفق',\n        pleaseClickView: '(يرجى النقر للتحميل والعرض)',\n        downloadFile: 'تحميل الملف الأصلي',\n        noLabelPleaseAppend: 'لا توجد علامات بعد، يرجى الذهاب إلى لوحة تحكم المؤسسة للإضافة',\n        archiveTo: 'أرشفة إلى',\n        hadArchivedToFolder: 'تم نقل العقد بنجاح إلى مجلد {folderName} الخاص بـ {who}',\n        pleaseScanToHandleWrite: 'يرجى مسح الرمز باستخدام WeChat أو متصفح الهاتف للتوقيع اليدوي على الجهاز المحمول',\n        save: 'حفظ',\n        remind: 'تذكير',\n        riskTip: 'تنبيه المخاطر',\n        chooseApplyPerson: 'اختيار منفذ الختم',\n        chooseAdminSign: 'اختيار مدير الأختام',\n        useSealByOther: 'ختم بواسطة شخص آخر',\n        getSeal: 'الحصول على ختم',\n        nowApplySealList: 'أنت تطلب الأختام التالية',\n        nowAdminSealList: 'أنت تطلب الحصول على الأختام التالية',\n        chooseApplyPersonToDeal: 'يرجى اختيار منفذ الختم، سيتم معالجة العقد من قبل الشخص المختار (يمكنك متابعة المشاهدة ومتابعة هذا العقد)',\n        chooseTransferPerson: 'تحويل التوقيع لشخص آخر',\n        chooseApplyPersonToMandate: 'يرجى اختيار مدير الأختام، عندما يتلقى الشخص المختار الإشعار ويوافق على المراجعة، ستحصل على صلاحية استخدام هذا الختم، وعندها يمكنك استخدام هذا الختم للختم وتوقيع العقد',\n        contactGroupAdminToDistributeSeal: 'يرجى الاتصال بمدير المجموعة لتخصيص الختم',\n        sealApplySentPleaseWait: 'تم إرسال طلب تخصيص الختم، يرجى انتظار الموافقة. أو يمكنك اختيار طريقة ختم أخرى',\n        successfulSent: 'تم الإرسال بنجاح',\n        authTip: {\n            t2: ['ملاحظة:', 'يجب أن تتطابق تماماً للتوقيع على العقد.', 'اسم المؤسسة', 'معلومات الهوية', 'يجب أن تتطابق تماماً لعرض وتوقيع العقد.'],\n            t3: '{x} يطلب منك {text} التحقق من الهوية.',\n            tCommon1: 'باسم {entName}',\n            tCommon2_1: 'باسم {name} ورقم هوية {idCard}',\n            tCommon2_2: 'باسم {name}',\n            tCommon2_3: 'برقم هوية {idCard}',\n            viewAndSign1: 'بعد اكتمال التحقق يمكنك عرض وتوقيع العقد.',\n            viewAndSignConflict: '{x} يطلب منك {text} لعرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد.',\n        },\n        needSomeoneToSignature: 'يحتاج {x} إلى ختم {y}',\n        needToSet: 'يحتاج إلى ختم',\n        approver: 'مقدم الطلب:',\n        clickToSignature: 'انقر هنا للتوقيع',\n        transferToOtherToSign: 'تحويل للتوقيع من شخص آخر',\n        signatureBy: 'توقيع بواسطة {x}',\n        tipRightNumber: 'يرجى إدخال رقم صحيح',\n        tipRightIdCard: 'يرجى إدخال رقم هوية مقيم في البر الرئيسي من 18 رقماً بشكل صحيح',\n        tipRightPhoneNumber: 'يرجى إدخال رقم هاتف من 11 رقماً بشكل صحيح',\n        tip: 'تنبيه',\n        tipRequired: 'لا يمكن ترك الحقول المطلوبة فارغة',\n        confirm: 'تأكيد',\n        viewContractDetail: 'عرض تفاصيل العقد',\n        required: 'مطلوب',\n        optional: 'اختياري',\n        decimalLimit: 'محدود بـ {x} أرقام بعد العلامة العشرية',\n        intLimit: 'يجب أن يكون رقماً صحيحاً',\n        invalidContract: 'توقيع هذا العقد يعني موافقتك على إلغاء العقود التالية:',\n        No: 'الرقم',\n        chooseFrom2: 'قام المرسل بتعيين اختيار واحد من اثنين للختم، يرجى اختيار موضع واحد للختم',\n        crossPlatformCofirm: {\n            message: 'مرحباً، يحتاج العقد الحالي إلى توقيع عبر المنصات، وستحتاج الملفات الموقعة إلى النقل خارج الحدود، هل توافق؟',\n            title: 'تفويض البيانات',\n            confirmButtonText: 'موافق على التفويض',\n            cancelButtonText: 'إلغاء',\n        },\n        sealScope: 'نطاق استخدام الختم',\n        currentContract: 'العقد الحالي',\n        allContract: 'جميع العقود',\n        docView: 'معاينة العقد',\n        fixTextDisplay: 'تصحيح رموز الصفحة غير المقروءة',\n        allPage: '{num} إجمالي الصفحات',\n        notJoinTip: 'يرجى الاتصال بالمسؤول ليضيفك كعضو في الشركة قبل التوقيع',\n\n    },\n    signJa: {\n        beforeSignTip1: 'بناءً على طلب المرسل، يرجى التوقيع باسم هذه المؤسسة:',\n        beforeSignTip2: 'حدد المرسل {signer} لإكمال التوقيع. إذا كانت المعلومات صحيحة، يمكنك التوقيع مباشرة.',\n        beforeSignTip3: 'إذا كانت المعلومات غير صحيحة، يرجى الاتصال بالمرسل لتغيير معلومات الموقع المحدد.',\n        beforeSignTip4: 'تم اكتشاف أن الاسم المسجل لهذا الحساب هو {currentUser}، وهو لا يتطابق مع {signer} المطلوب من المرسل حالياً، هل تؤكد التغيير إلى {signer}',\n        beforeSignTip5: 'تم اكتشاف أن الاسم المرتبط بالحساب الحالي هو: {currentUser}، وهو لا يتطابق مع متطلب الطرف الأول بتوقيع {signer}',\n        beforeSignTip6: 'يرجى التأكيد على التغيير إلى {signer} المحدد من قبل الطرف الأول للتوقيع وفقاً للوضع الفعلي',\n        beforeSignTip7: 'أو التواصل مع الطرف الأول لتغيير الموقع المحدد',\n        entNamePlaceholder: 'يرجى إدخال اسم المؤسسة',\n        corporateNumberPlaceholder: 'يرجى إدخال رقم الشركة',\n        corporateNumber: 'رقم الشركة',\n        singerNamePlaceholder: 'يرجى إدخال اسم الموقع',\n        singerName: 'اسم الموقع',\n        itsMe: 'هذا أنا',\n        wrongInformation: 'معلومات خاطئة',\n        confirmChange: 'تأكيد التغيير',\n        communicateSender1: 'لا تغيير، التواصل مع الطرف الأول',\n        communicateSender2: 'إلغاء، التواصل مع المرسل',\n        createSeal: {\n            title: 'إدخال الاسم',\n            tip: 'يرجى إدخال اسمك (يمكن استخدام المسافة للتغيير إلى سطر جديد)',\n            emptyErr: 'يرجى إدخال الاسم',\n        },\n        areaRegister: 'مكان تسجيل الشركة',\n        jp: 'اليابان',\n        cn: 'الصين',\n        are: 'الإمارات العربية المتحدة',\n        other: 'أخرى',\n        plsSelect: 'يرجى الاختيار',\n        tip1: 'يجب على الشركات المسجلة في البر الرئيسي للصين إكمال التسجيل باستخدام الاسم الحقيقي على ent.bestsign.cn. عند توقيع العقود مع الشركات خارج البر الرئيسي للصين، يمكن استخدام وظيفة \"التوقيع عبر الحدود\" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',\n        tip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للصين، يمكنك تسجيل الدخول مباشرة إلى ent.bestsign.cn واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الصينية.',\n        tip3: 'يرجى تقديم رقم الوثيقة الذي حصلت عليه من هيئة الرقابة التجارية المحلية',\n        tip4: 'يرجى اتباع الخطوات التالية',\n        tip5: '1. يرجى الاتصال بمدير العلاقات الخاص بك لإرشادك خلال عملية التحقق من هوية الشركة.',\n        tip6: 'انقر على \"إدارة الرصيد\".',\n        tip7: '2. يرجى تحميل لقطة شاشة للعقد التجاري لشركتك مع BestSign أو مراسلات البريد الإلكتروني مع مدير العلاقات المخصص.',\n        tip8: 'شراء عقد واحد على الأقل والاحتفاظ بلقطة شاشة لسجل الشراء.',\n        tip9: '3. هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',\n        tip10: '4. سوف تقوم BestSign بمراجعة الطلب خلال ثلاثة أيام عمل بعد التقديم.',\n        tip11: 'تنبيه هام',\n        tip12: 'يجب أن يكون المشتري مستخدماً تجارياً.',\n        tip13: 'يجب أن يتطابق اسم الشركة في حساب الدفع تماماً مع \"اسم الشركة\" الذي أدخلته.',\n        tip14: 'هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',\n        comNum: 'رقم وثيقة الشركة',\n        buyRecord: 'المواد الداعمة',\n        selectArea: 'يرجى اختيار مكان تسجيل الشركة',\n        uaeTip1: 'يجب على الشركات المسجلة في الإمارات العربية المتحدة إكمال التسجيل باستخدام الاسم الحقيقي على uae.bestsign.com. عند توقيع العقود مع الشركات خارج الإمارات العربية المتحدة، يمكن استخدام وظيفة \"التوقيع عبر الحدود\" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',\n        uaeTip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للإمارات، يمكنك تسجيل الدخول مباشرة إلى uae.bestsign.com واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الإماراتية.',\n        uaeTip3: 'الشركات المسجلة خارج دولة الإمارات العربية المتحدة والبر الرئيسى للصين ، يجب أن تكون مسجلة في ent.bestsign.com الاسم الحقيقي . عند توقيع العقود مع الشركات في دولة الإمارات العربية المتحدة ، يمكن استخدام \" التوقيع عبر الحدود \" وظيفة لضمان أمن بيانات المستخدم ، لا تسرب ، فرضية كفاءة إنجاز العقود المتبادلة .',\n    },\n    signPC: {\n        commonSign: 'تأكيد التوقيع',\n        contractVerification: 'التحقق من العقد',\n        VerCodeVerify: 'التحقق من رمز التحقق',\n        QrCodeVerify: 'التحقق من رمز QR',\n        verifyTip: 'BestSign يقوم بتفعيل شهادتك الرقمية الآمنة، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n        verifyAllTip: 'BestSign يقوم بتفعيل شهادة المؤسسة الرقمية وشهادتك الشخصية الرقمية، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',\n        selectSeal: 'اختيار الختم',\n        adminGuideTip: 'نظراً لأنك المدير الرئيسي للمؤسسة، يمكنك تخصيص ختم المؤسسة لنفسك مباشرة',\n        toAddSealWithConsole: 'الختم الرسمي الإلكتروني في انتظار التفعيل. لإضافة أختام أخرى، يرجى الانتقال إلى لوحة التحكم.',\n        use: 'استخدام',\n        toAddSeal: 'اذهب لإضافة ختم',\n        mySeal: 'ختمي',\n        operationCompleted: 'اكتملت العملية',\n        FDASign: {\n            date: 'وقت التوقيع',\n            signerAdd: 'إضافة',\n            signerEdit: 'تعديل',\n            editTip: 'تنبيه: للأسماء الصينية يرجى إدخال النطق الصوتي، مثل San Zhang (张三)',\n            inputNameTip: 'يرجى إدخال اسمك',\n            inputName: 'يرجى إدخال الإنجليزية أو النطق الصوتي الصيني',\n            signerNameFillTip: 'لا تزال بحاجة إلى ملء اسم التوقيع',\n            plsInput: 'يرجى الإدخال',\n            plsSelect: 'يرجى الاختيار',\n            customInput: 'إدخال مخصص',\n        },\n        signPlaceBySigner: {\n            signGuide: 'دليل التوقيع',\n            howDragSeal: 'كيفية سحب الختم',\n            howDragSignature: 'كيفية سحب التوقيع',\n            iKnow: 'فهمت',\n            step: {\n                one: 'الخطوة الأولى: قراءة العقد',\n                two1: 'الخطوة الثانية: النقر على \"سحب الختم\"',\n                two2: 'الخطوة الثانية: النقر على \"سحب التوقيع\"',\n                three: 'الخطوة الثالثة: النقر على زر \"التوقيع\"',\n            },\n            dragSeal: 'سحب الختم',\n            continueDragSeal: 'متابعة سحب الختم',\n            dragSignature: 'سحب التوقيع',\n            continueDragSignature: 'متابعة سحب التوقيع',\n            dragPlace: 'اضغط هنا للسحب',\n            notRemind: 'لا تذكرني مرة أخرى',\n            signTip: {\n                one: 'الخطوة الأولى: من خلال النقر على \"بدء\"، حدد موقع التوقيع/الختم المطلوب.',\n                two: 'الخطوة الثانية: من خلال النقر على \"موقع التوقيع/موقع الختم\"، أكمل التوقيع/الختم حسب المتطلبات.',\n            },\n            finishSignatureBeforeSign: 'يرجى إكمال سحب التوقيع/سحب الختم قبل تأكيد التوقيع',\n        },\n        continueOperation: {\n            success: 'نجحت العملية',\n            exitApproval: 'الخروج من الموافقة',\n            continueApproval: 'متابعة الموافقة',\n            next: 'التالي:',\n            none: 'لا يوجد المزيد',\n            tip: 'تنبيه',\n            approvalProcess: 'يحتاج إلى موافقة {totalNum} شخص، حالياً {passNum} شخص وافقوا',\n            receiver: 'المستلم:',\n        },\n    },\n    signTip: {\n        contractDetail: 'تفاصيل العقد',\n        downloadBtn: 'تحميل التطبيق',\n        tips: 'تنبيهات',\n        submit: 'تأكيد',\n        SigningCompleted: 'تم التوقيع بنجاح',\n        submitCompleted: 'في انتظار معالجة الآخرين',\n        noTurnSign: 'لم يحن دورك في التوقيع أو ليس لديك صلاحية التوقيع أو انتهت صلاحية تسجيل الدخول',\n        noRightSign: 'العقد قيد التوقيع، المستخدم الحالي غير مسموح له بعملية التوقيع',\n        noNeedSign: 'عقد قرار داخلي، لم يعد بحاجة للتوقيع',\n        ApprovalCompleted: 'تمت الموافقة بنجاح',\n        contractRevoked: 'تم إلغاء {alias} هذا',\n        contractRefused: 'تم رفض {alias} هذا',\n        linkExpired: 'انتهت صلاحية هذا الرابط',\n        contractClosed: 'انتهى موعد توقيع {alias} هذا',\n        approvalReject: 'تم رفض موافقة {alias} هذا',\n        approving: '{alias} قيد الموافقة',\n        viewContract: 'عرض تفاصيل {alias}',\n        viewContractList: 'عرض قائمة العقود',\n        needMeSign: '({num} في انتظار التوقيع)',\n        downloadContract: 'تحميل العقد',\n        sign: 'تم التوقيع',\n        signed: 'تم التوقيع',\n        approved: 'تمت الموافقة',\n        approval: 'موافقة',\n        person: 'شخص',\n        personHas: 'قد',\n        personHave: 'قد',\n        personHasnot: 'لم',\n        personsHavenot: 'لم',\n        headsTaskDone: '{num}{has}{done}',\n        headsTaskNotDone: '{num}{not}{done}',\n        taskStatusBetween: '،',\n        cannotReview: 'لا يمكن عرض العقد',\n        cannotDownload: 'هذا العقد لا يدعم التحميل على الهاتف. لأن العقد مخزن بشكل خاص من قبل المرسل، لا يمكن لـ BestSign الوصول إلى العقد.',\n        privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود',\n        beenDeleted: 'تم حذف حسابك من قبل مدير المؤسسة',\n        unActive: 'لا يمكن متابعة تفعيل الحساب',\n        back: 'عودة',\n        contratStatusDes: 'حالة {key}:',\n        contractConditionDes: 'وضع {key}:',\n        contractIng: '{alias} {key} جارٍ',\n        contractComplete: 'اكتمل {alias} {key}',\n        dataProduct: {\n            tip1: 'من {entName} إلى السادة مسؤولي الموزعين/الموردين المتميزين:',\n            tip2: 'لشكركم على مساهمتكم في التطور المستقر لـ {entName}، نقدم بالتعاون مع {bankName} خدمات التمويل لسلسلة التوريد لمساعدة مؤسستكم على التطور السريع!',\n            btnText: 'اذهب لمشاركة هذه الأخبار السارة مع المدير',\n        },\n        signOnGoing: 'العقد {status} جارٍ',\n        operate: 'عملية العقد',\n        freeContract: 'أكمل إرسال العقد الأول، يمكنك الحصول على المزيد من العقود مجاناً',\n        sendContract: 'اذهب لإرسال العقد',\n        congratulations: 'تهانينا {name} على إكمال توقيع {num} عقد،',\n        carbonSaving: 'تقدير توفير الكربون {num}g',\n        signGift: 'BestSign يهديك {num} عقد مؤسسي (صالح حتى {limit})',\n        followPublic: 'تابع الحساب العام على WeChat لتلقي رسائل العقود في أي وقت',\n        congratulationsSingle: 'تهانينا {name} على إكمال توقيع العقد،',\n        carbonSavingSingle: 'تقدير زيادة توفير الكربون 2002.4g',\n        viewContractTip: 'إذا كنت تريد تغيير الشخص المسؤول عن الختم، يمكنك النقر على زر \"عرض التفاصيل\" لفتح صفحة تفاصيل العقد، ثم النقر على زر \"طلب ختم\"',\n        congratulationsCn: 'شكراً لاختيار التوقيع الإلكتروني!',\n        carbonSavingSingleCn: 'لقد قللت الكربون بمقدار {num}gCO2e للأرض',\n        carbonVerification: \"*محسوب علمياً بواسطة 'كربون ستوب'\",\n    },\n    view: {\n        title: 'عرض العقد',\n        ok: 'إكمال',\n        cannotReview: 'لا يمكن عرض العقد',\n        privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود',\n    },\n    prepare: {\n        sealArea: 'موقع الختم',\n        senderNotice: 'الطرف المرسل الحالي للعقد هو: {entName}',\n        preSetDialogConfirm: 'فهمت',\n        preSetDialogContact: 'اتصل بفريق مبيعات BestSign للتفعيل الآن',\n        preSetDialogInfo: 'بعد إعداد العقد مسبقاً، يقوم النظام تلقائياً بملء معلومات الأطراف المتعاقدة، ومتطلبات التوقيع، ومواقع التوقيع، وحقول وصف العقد وفقاً للقالب',\n        preSetDialogTitle: 'ما هو قالب الإعداد المسبق للعقد؟',\n        initialValues: 'تعيين القيم الأولية بناءً على محتوى العقد',\n        proxyUpload: 'بعد تحميل الملف المحلي، يمكنك اختيار طرف بدء العقد',\n        signHeaderTitle: 'إضافة الملفات والأطراف المتعاقدة',\n        step1: 'الخطوة الأولى',\n        confirmSender: 'تأكيد المرسل',\n        step2: 'الخطوة الثانية',\n        uploadFile: 'تحميل الملف',\n        step3: 'الخطوة الثالثة',\n        addSigner: 'إضافة طرف متعاقد',\n        actionDemo: 'عرض توضيحي للعملية',\n        next: 'الخطوة التالية',\n        isUploadingErr: 'لم يكتمل تحميل الملف بعد، يرجى المتابعة بعد الاكتمال',\n        noUploadFileErr: 'لم يتم تحميل ملف، يرجى التحميل قبل المتابعة',\n        noContractTitleErr: 'لم يتم إدخال اسم العقد، يرجى الإدخال قبل المتابعة',\n        contractTypeErr: 'تم حذف نوع العقد الحالي، يرجى إعادة اختيار نوع العقد',\n        expiredDateErr: 'خطأ في الموعد النهائي للتوقيع، يرجى التعديل قبل المتابعة',\n        noExpiredDateErr: 'يرجى تعيين الموعد النهائي للتوقيع قبل المتابعة',\n        describeFieldsErr: 'يرجى ملء حقول المحتوى المطلوبة قبل المتابعة',\n        noRecipientsErr: 'يرجى إضافة طرف متعاقد واحد على الأقل',\n        noAccountErr: 'لا يمكن ترك الحساب فارغاً',\n        noUserNameErr: 'لا يمكن ترك الاسم فارغاً',\n        noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',\n        accountFormatErr: 'التنسيق غير صحيح، يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n        userNameFormatErr: 'التنسيق غير صحيح، يرجى إدخال اسم صحيح',\n        enterpriseNameErr: 'يرجى إدخال اسم مؤسسة صحيح',\n        idNumberForVerifyErr: 'التنسيق غير صحيح، يرجى إدخال رقم هوية صحيح',\n        signerErr: 'خطأ في الطرف المتعاقد',\n        noSignerErr: 'يرجى إضافة موقع واحد على الأقل',\n        lackAttachmentNameErr: 'يرجى إدخال اسم المرفق',\n        repeatRecipientsErr: 'لا يمكن إضافة نفس الطرف المتعاقد مرتين عند عدم التوقيع بالترتيب',\n        innerContact: 'جهة اتصال داخلية',\n        outerContact: 'جهة اتصال خارجية',\n        search: 'بحث',\n        accountSelected: 'الحسابات المختارة',\n        groupNameAll: 'الكل',\n        unclassified: 'غير مصنف',\n        fileLessThan: 'يرجى تحميل ملف أقل من {num}M',\n        beExcel: 'يرجى تحميل ملف Excel',\n        usePdf: 'يرجى استخدام ملف PDF أو صورة عند التحميل',\n        usePdfFile: 'يرجى استخدام ملف PDF عند التحميل',\n        fileNameMoreThan: 'تجاوز طول اسم الملف {num}، تم اقتطاعه تلقائياً',\n        needAddSender: 'لم يتم تعيين مؤسستك/نفسك كطرف متعاقد، بعد إرسال العقد، لن تشارك في عملية التوقيع. هل تريد إضافة طرفك كموقع؟',\n        addSender: 'إضافة كطرف متعاقد',\n        tip: 'تنبيه',\n        cancel: 'إلغاء',\n    },\n    addReceiver: {\n        English: 'الإنجليزية',\n        Japanese: 'اليابانية',\n        Chinese: 'الصينية',\n        Arabic: 'Arabic',\n        setNoticelang: 'تعيين لغة الإشعارات',\n        limitFaceConfigTip: 'سعر العقد الخاص بك منخفض جداً، هذه الميزة غير متوفرة، يرجى الاتصال بـ BestSign للتفاوض',\n        individual: 'فرد متعاقد',\n        enterprise: 'مؤسسة متعاقدة',\n        addInstructions: 'إضافة ملاحظات التوقيع',\n        instructionsContent: 'المواد المقدمة تساعدك في تتبع حالة تنفيذ العقد وتحديد ما إذا كان تنفيذ الأعمال طبيعياً. بعد الإعداد، يجب على الموقع تقديمها وفقاً للمتطلبات',\n        addContractingInfo: 'تقديم مواد الطرف المتعاقد',\n        contractingInfoContent: 'المواد المقدمة تساعدك في التحقق من مؤهلات الطرف المتعاقد وتحديد ما إذا كان يمكن بدء أو مواصلة الأعمال معه. إذا كان الطرف المتعاقد قد قدم نفس المواد من قبل، فلا داعي لتقديمها مرة أخرى',\n        payer: 'الطرف الدافع',\n        handWriting: 'تفعيل التعرف على الكتابة اليدوية',\n        realName: 'يحتاج المسؤول إلى التحقق من الهوية',\n        sameTip: 'تنبيه: يجب أن يتطابق اسم المؤسسة للطرف المتعاقد تماماً للتوقيع',\n        proxy: 'استلام بالوكالة من الطرف الآخر',\n        aboradTip: 'تنبيه: هذا الطرف المتعاقد شخص خارج البلاد، هناك مخاطر في التحقق من الهوية، يرجى التحقق من هوية هذا الشخص أولاً',\n\n        busRole: 'دور العمل',\n        busRoleTip: 'يساعد في تحديد الطرف المتعاقد وتسهيل الإدارة',\n        busRolePlaceholder: 'مثل موظف/موزع',\n        handWritingTip: 'يحتاج هذا المستخدم إلى كتابة اسمه بخط واضح ومقروء عند التوقيع لإكمال التوقيع',\n        instructions: 'إضافة ملاحظات التوقيع | (محدود بـ 255 حرفاً)',\n        contractingParty: 'مواد الطرف المتعاقد',\n        signerPay: 'يدفع هذا الموقع رسوم هذا العقد',\n        afterReadingTitle: 'التوقيع بعد القراءة',\n        afterReading: 'يجب على الموقع القراءة وفهم محتوى العقد قبل متابعة العمليات اللاحقة.',\n        handWritingTips: 'سيتم مقارنة الاسم المكتوب بخط اليد مع الاسم المحدد من قبل المرسل أو في معلومات التحقق من الهوية، يجب أن تتطابق المقارنة لإكمال التوقيع',\n        SsTitle: 'الختم والتوقيع',\n        SsTip: 'عند التوقيع باستخدام ختم المؤسسة، يجب أيضاً إضافة التوقيع الشخصي لإكمال التوقيع. يجب إكمال التحقق من الهوية الشخصية قبل التوقيع',\n        signature: 'توقيع',\n        stamp: 'ختم',\n        Ss: 'ختم وتوقيع',\n        mutexError: 'تم تعيين \"{msg}\"، يرجى حذف إعداد \"{msg}\" أولاً قبل الاختيار',\n        handWriteNotAllowed: 'التوقيع اليدوي غير مسموح به',\n        forceHandWrite: 'التوقيع اليدوي إلزامي',\n        faceFirst: 'التعرف على الوجه أولاً، رمز التحقق احتياطي للتوقيع',\n        faceVerify: 'التوقيع بالتعرف على الوجه إلزامي',\n        attachmentRequired: 'إضافة مواد ملحقة للعقد',\n        newAttachmentRequired: 'تقديم مواد الطرف المتعاقد',\n        attachmentError: 'لا يمكن أن تكون أسماء المواد الملحقة للعقد متطابقة',\n        receiver: 'هاتف/بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',\n        receiverJa: 'بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',\n        orderSignLabel: 'التوقيع بالترتيب',\n        contactAddress: 'دليل جهات الاتصال',\n        signOrder: 'ترتيب التوقيع',\n        account: 'الحساب',\n        accountPlaceholder: 'هاتف/بريد إلكتروني (مطلوب)',\n        accountPlaceholderJa: 'بريد إلكتروني (مطلوب)',\n        accountReceptionCollection: 'استلام بالوكالة',\n        accountReceptionCollectionTip1: 'لا تعرف حساب الطرف الآخر المحدد أو ليس لديه حساب،',\n        accountReceptionCollectionTip2: 'يرجى اختيار استلام بالوكالة',\n        signSubjectPerson: 'الطرف المتعاقد: فرد',\n        nameTips: 'الاسم (اختياري، للتحقق من هوية التوقيع)',\n        requiredNameTips: 'الاسم (مطلوب، للتحقق من هوية التوقيع)',\n        entOperatorNameTips: 'الاسم (اختياري)',\n        needAuth: 'يحتاج إلى التحقق من الهوية',\n        operatorNeedAuth: 'يحتاج المسؤول إلى التحقق من الهوية',\n        signSubjectEnt: 'الطرف المتعاقد: شركة',\n        entNameTips: 'اسم المؤسسة (مطلوب، للتحقق من هوية التوقيع)',\n        operator: 'المسؤول',\n        sign: 'توقيع',\n        more: 'المزيد',\n        faceFirstTips: 'يستخدم النظام التحقق من الوجه افتراضياً عند التوقيع، عندما يصل عدد مرات فشل التحقق من الوجه إلى الحد الأقصى اليومي، يتم التبديل تلقائياً إلى التحقق برمز التحقق',\n        mustFace: 'التوقيع بالتعرف على الوجه إلزامي',\n        mustHandWrite: 'التوقيع اليدوي إلزامي',\n        fillIDNumber: 'رقم الهوية',\n        fillNoticeCall: 'هاتف الإشعار',\n        fillNoticeCallTips: 'يرجى إدخال هاتف الإشعار',\n        addNotice: 'إضافة رسالة خاصة',\n        attachTips: 'إضافة مواد ملحقة للعقد',\n        faceSign: 'التوقيع بالتعرف على الوجه إلزامي',\n        faceSignTips: 'يحتاج هذا المستخدم إلى اجتياز التحقق من الوجه لإكمال التوقيع (التوقيع بالتعرف على الوجه متاح حالياً فقط للمقيمين في البر الرئيسي)',\n        handWriteNotAllowedTips: 'يمكن لهذا المستخدم فقط اختيار توقيع معد مسبقاً أو استخدام الخط الافتراضي للتوقيع لإكمال التوقيع',\n        handWriteTips: 'يحتاج هذا المستخدم إلى التوقيع اليدوي لإكمال التوقيع',\n        idNumberTips: 'للتحقق من هوية التوقيع',\n        verifyBefore: 'التحقق من الهوية قبل عرض الملف',\n        verify: 'التحقق من الهوية',\n        verifyTips: '20 حرفاً كحد أقصى',\n        verifyTips2: 'يجب عليك تقديم معلومات التحقق هذه لهذا المستخدم',\n        sendToThirdPlatform: 'إرسال إلى منصة خارجية',\n        platFormName: 'اسم المنصة',\n        fillThirdPlatFormName: 'يرجى إدخال اسم المنصة الخارجية',\n        attach: 'المواد',\n        attachName: 'اسم المواد',\n        exampleID: 'مثال: صورة بطاقة الهوية',\n        attachInfo: 'ملاحظات',\n        attachInfoTips: 'مثال: يرجى تحميل صورة بطاقة هويتك',\n        addAttachRequire: 'إضافة مواد',\n        addSignEnt: 'إضافة مؤسسة متعاقدة',\n        addSignPerson: 'إضافة فرد متعاقد',\n        selectContact: 'اختيار جهة اتصال',\n        save: 'حفظ',\n        searchVerify: 'التحقق من البحث',\n        fillImageContentTips: 'يرجى ملء محتوى الصورة',\n        ok: 'موافق',\n        findContact: 'تم العثور على الأطراف المتعاقدة التالية في العقد',\n        signer: 'الطرف المتعاقد',\n        signerTips: 'تلميح: بعد اختيار الطرف المتعاقد، يمكن للمنصة المساعدة في تحديد موقع التوقيع والختم.',\n        add: 'إضافة',\n        notAdd: 'عدم الإضافة',\n        cc: 'نسخة',\n        notNeedAuth: 'لا يحتاج إلى التحقق من الهوية',\n        operatorNotNeedAuth: 'لا يحتاج المسؤول إلى التحقق من الهوية',\n        extracting: 'جاري الاستخراج',\n        autoFill: 'ملء الموقع تلقائياً',\n        failExtracting: 'لم يتم العثور على أطراف متعاقدة',\n        idNumberForVerifyErr: 'يرجى إدخال رقم هوية صحيح',\n        noAccountErr: 'لا يمكن ترك الحساب فارغاً',\n        noUserNameErr: 'لا يمكن ترك الاسم فارغاً',\n        noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',\n        noEntNameErr: 'لا يمكن ترك اسم المؤسسة فارغاً',\n        accountFormatErr: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n        enterpriseNameErr: 'يرجى إدخال اسم شركة صحيح',\n        userNameFormatErr: 'يرجى إدخال اسم صحيح',\n        riskCues: 'تنبيه المخاطر',\n        riskCuesMsg: 'إذا لم يوقع الطرف المتعاقد بالتحقق من الهوية، ستحتاج إلى تقديم دليل التحقق من هوية هذا الطرف بنفسك عند حدوث نزاع في الملف. لتجنب المخاطر، يرجى اختيار التحقق من الهوية.',\n        confirmBtnText: 'اختيار التحقق من الهوية',\n        cancelBtnText: 'اختيار عدم التحقق من الهوية',\n        attachLengthErr: 'يمكنك إضافة 50 مرفق كحد أقصى لكل موقع',\n        collapse: 'طي',\n        expand: 'توسيع',\n        delete: 'حذف',\n        saySomething: 'قل شيئاً',\n        addImage: 'إضافة مستند',\n        addImageTips: '(يدعم word وpdf والصور، لا يتجاوز 3 مستندات)',\n        give: 'إعطاء',\n        fileMax: 'تجاوز عدد التحميلات الحد الأقصى!',\n        signerLimit: 'نسختك الحالية لا تدعم أكثر من {limit} موقع نسبي/مستلم نسخة.',\n        showExamle: 'عرض الصور النموذجية',\n        downloadExamle: 'تنزيل الملف النموذجي',\n    },\n    addReceiverGuide: {\n        guideTitle: 'كيفية إضافة موقع جديد',\n        receiverType: 'تحتاج إلى اختيار طريقة مشاركة الموقع في العقد (واحد من ستة):',\n        asEntSign: 'التوقيع نيابة عن المؤسسة:',\n        signatureSub: 'المدير القانوني أو التنفيذي يوقع على العقد نيابة عن المؤسسة. يمكن للمؤسسة أخذ العقد بعد اكتمال التوقيع',\n        vipOnly: 'متاح فقط للنسخة المتقدمة',\n        sealSub: 'يحتاج الموقع إلى وضع ختم رسمي أو ختم خاص بالعقد وغيره',\n        stampSub: 'يحتاج الموقع إلى الختم والتوقيع نيابة عن المؤسسة',\n        confirmSeal: 'استخدام ختم التحقق من الأعمال نيابة عن المؤسسة',\n        confirmSealSub: 'كشوف الحسابات المالية وخطابات التأكيد وغيرها من الوثائق تحتاج إلى التحقق قبل الختم',\n        asPersonSign: 'التوقيع بصفة شخصية:',\n        asPersonSignTip: 'التوقيع بصفة شخصية فقط، لا يمثل أي مؤسسة',\n        asPersonSignDesc: 'العقود الشخصية للموقع، مثل عقود القرض واتفاقيات التوظيف والاستقالة وغيرها',\n        scanSign: 'التوقيع بالمسح الضوئي',\n        scanSignDesc: 'لا تحتاج إلى كتابة الموقع عند إرسال العقد، بعد الإرسال يمكن لأي شخص المسح/النقر على رابط التحقق للتوقيع، مناسب لسيناريوهات استلام مستندات الشحن',\n        selectSignTypeTip: 'يرجى اختيار طريقة مشاركة الطرف المتعاقد في العقد أولاً',\n        notRemind: 'عدم التذكير مرة أخرى',\n        sign: 'توقيع',\n        entSign: 'توقيع المؤسسة',\n        stamp: 'ختم',\n        stampSign: 'ختم وتوقيع',\n        requestSeal: 'ختم التحقق من الأعمال',\n    },\n    linkContract: {\n        title: 'ربط العقود',\n        connectMore: 'ربط المزيد من العقود',\n        placeholder: 'يرجى إدخال رقم العقد',\n        revoke: 'تم إلغاء العقد',\n        overdue: 'تجاوز موعد التوقيع',\n        approvalNotPassed: 'تم رفض الموافقة',\n        reject: 'تم رفض العقد',\n        signing: 'قيد التوقيع',\n        complete: 'مكتمل',\n        approvaling: 'قيد الموافقة',\n        disconnect: 'إلغاء الربط',\n        disconnectSuccess: 'تم إلغاء الربط بنجاح',\n        connectLimit: 'الحد الأقصى لعدد العقود المرتبطة هو 100 عقد',\n    },\n    field: {\n        fieldTip: {\n            title: 'موقع توقيع مفقود',\n            error: 'لم يتم تحديد مواقع التوقيع ({type}) في العقود التالية',\n            add: 'إضافة حقل',\n            continue: 'متابعة الإرسال',\n        },\n        accountCharge: {\n            notice: 'يتم احتساب رسوم هذا العقد حسب عدد الحسابات المشاركة',\n            able: 'يمكن الإرسال بشكل طبيعي',\n            unable: 'عدد الحسابات المتاحة غير كافٍ، يرجى الاتصال بخدمة عملاء BestSign',\n            notify: 'هذا العقد يرسل إشعارات باللغة الإنجليزية لجميع الأطراف المتعاقدة',\n            noNotify: {\n                1: 'هذا العقد لا يرسل إشعارات متعلقة بالتوقيع',\n                2: '(بما في ذلك الرسائل القصيرة والبريد الإلكتروني للتوقيع والموافقة والنسخ والموعد النهائي للتوقيع)',\n            },\n        },\n        ridingStamp: 'ختم متداخل',\n        watermark: 'علامة مائية',\n        senderSignature: 'توقيع صاحب الختم',\n        optional: 'اختياري',\n        clickDecoration: 'انقر لتزيين العقد',\n        decoration: 'تزيين العقد',\n        sysError: 'النظام مشغول، يرجى المحاولة لاحقاً',\n        partedMarkedError: 'يجب تحديد كل من الختم والتوقيع للأطراف المتعاقدة المحددة لـ \"الختم والتوقيع\"',\n        fieldTitle: 'يوجد {length} عقد يحتاج إلى تحديد مواقع التوقيع',\n        send: 'إرسال',\n        contractDispatchApply: 'طلب إرسال العقد',\n        contractNeedYouSign: 'هذا المستند يحتاج إلى توقيعك',\n        ifSignRightNow: 'هل تريد التوقيع الآن',\n        signRightNow: 'التوقيع الآن',\n        signLater: 'التوقيع لاحقاً',\n        signaturePositionErr: 'يرجى تحديد مواقع التوقيع لكل موقع',\n        sendSucceed: 'تم الإرسال بنجاح',\n        confirm: 'تأكيد',\n        cancel: 'إلغاء',\n        qrCodeTips: 'امسح الرمز بعد التوقيع لعرض تفاصيل التوقيع والتحقق من صلاحية التوقيع وما إذا تم العبث بالعقد',\n        pagesField: 'الصفحة {currentPage} من {totalPages}',\n        suitableWidth: 'عرض مناسب',\n        signCheck: 'التحقق من التوقيع',\n        locateSignaturePosition: 'تحديد موقع التوقيع',\n        locateTips: 'يمكن المساعدة في تحديد موقع التوقيع بسرعة. حالياً يدعم فقط تحديد أول موقع توقيع لكل موقع',\n        step1: 'الخطوة الأولى',\n        selectSigner: 'اختيار الطرف المتعاقد',\n        step2: 'الخطوة الثانية',\n        dragSignaturePosition: 'سحب موقع التوقيع',\n        signingField: 'حقل التوقيع',\n        docTitle: 'المستند',\n        totalPages: 'عدد الصفحات: {totalPages} صفحة',\n        receiver: 'المستلم',\n        delete: 'حذف',\n        deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية',\n        unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',\n        charge: 'الرسوم',\n        units: '{num} نسخة',\n        contractToPrivate: 'عقد شخصي',\n        contractToPublic: 'عقد مؤسسي',\n        costTips: {\n            1: 'عقد مؤسسي: عقد يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',\n            2: 'عقد شخصي: عقد لا يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',\n            3: 'يتم حساب عدد النسخ المحتسبة حسب عدد المستندات',\n            4: 'عدد النسخ المحتسبة = عدد المستندات × عدد مجموعات (صفوف) المستخدمين المستوردين بالدفعة',\n        },\n        toCharge: 'إعادة الشحن',\n        contractNeedCharge: {\n            1: 'عدد العقود المتاحة غير كافٍ، لا يمكن الإرسال',\n            2: 'عدد العقود المتاحة غير كافٍ، يرجى الاتصال بالمدير الرئيسي لإعادة الشحن',\n        },\n        chooseApprover: 'اختيار المراجع:',\n        nextStep: 'الخطوة التالية',\n        submitApproval: 'تقديم للموافقة',\n        autoSendAfterApproval: '*بعد الموافقة، سيتم إرسال العقد تلقائياً',\n        chooseApprovalFlow: 'يرجى اختيار تدفق موافقة',\n        completeApprovalFlow: 'تدفق الموافقة الذي قدمته غير مكتمل، يرجى إكماله وإعادة التقديم',\n        viewPrivateLetter: 'عرض الرسالة الخاصة',\n        addPrivateLetter: 'إضافة رسالة خاصة',\n        append: 'إضافة',\n        privateLetter: 'رسالة خاصة',\n        signNeedKnow: 'ملاحظات التوقيع',\n        maximum5M: 'يرجى تحميل مستند أقل من 5M',\n        uploadServerFailure: 'فشل التحميل إلى الخادم',\n        uploadFailure: 'فشل التحميل',\n        pager: 'رقم الصفحة',\n        seal: 'ختم',\n        signature: 'توقيع',\n        signDate: 'تاريخ التوقيع',\n        text: 'نص',\n        date: 'تاريخ',\n        qrCode: 'رمز QR',\n        number: 'رقم',\n        dynamicTable: 'جدول ديناميكي',\n        terms: 'بنود العقد',\n        checkBox: 'خانة اختيار',\n        radioBox: 'زر راديو',\n        image: 'صورة',\n    },\n    addressBook: {\n        innerMember: {\n            title: 'الأعضاء الداخليون للمؤسسة',\n            tips: 'تعديل معلومات موظفي المؤسسة لجعل المرسلين يجدون جهات الاتصال الداخلية بسرعة أكبر',\n            operation: 'اذهب إلى لوحة التحكم',\n        },\n        outerContacts: {\n            title: 'جهات الاتصال الخارجية للمؤسسة',\n            tips: 'دعوة شركائك للتسجيل والتحقق من الهوية مسبقاً لتسهيل إجراء الأعمال معك',\n            operation: 'دعوة شركائك',\n        },\n        myContacts: {\n            title: 'جهات اتصالي',\n            tips: 'تعديل جهات الاتصال للتأكد من دقة معلومات الموقع',\n            operation: 'اذهب إلى مركز المستخدم',\n        },\n        selected: 'الحسابات المختارة',\n        search: 'بحث',\n        loadMore: 'تحميل المزيد',\n        end: 'تم تحميل الكل',\n    },\n    dataBoxInvite: {\n        title: 'دعوة شركائك',\n        step1: 'مشاركة الرابط مع شركائك لإنشاء مؤسسة مسبقاً',\n        step2: 'سيظهر الشركاء المفوضون عبر الرابط/رمز QR في دليل العناوين',\n        step3: 'قم بإدارة المزيد لشركائك في \"الملفات+\"',\n        imgName: 'مشاركة رمز QR للجمع',\n        saveQrcode: 'حفظ رمز QR محلياً',\n        copy: 'نسخ',\n        copySuccess: 'تم النسخ بنجاح',\n        copyFailed: 'فشل النسخ',\n    },\n    shareView: {\n        title: 'إعادة توجيه للمراجعة',\n        account: 'رقم الهاتف/البريد الإلكتروني',\n        role: 'دور المراجع',\n        note: 'ملاحظات',\n        link: 'الرابط:',\n        signerMessage: 'رسالة الموقع',\n        rolePlaceholder: 'مثل القانوني للشركة، قائد القسم وغيرهم',\n        notePlaceholder: 'اترك رسالة للمراجع، في حدود 200 حرف',\n        generateLink: 'إنشاء رابط',\n        saveQrcode: 'حفظ رمز البرنامج الصغير لـ WeChat',\n        regenerateLink: 'إعادة إنشاء الرابط',\n        inputAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني',\n        inputCorrectAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',\n        accountInputTip: 'لضمان فتح الرابط بشكل طبيعي، يرجى إدخال معلومات مراجع العقد بدقة',\n        shareLinkTip1: 'احفظ رمز برنامج WeChat المصغر أو',\n        shareLinkTip: 'انسخ الرابط للمشاركة مع المراجعين',\n        linkTip1: 'نص العقد محتوى سري، يرجى عدم تسريبه للخارج في حالة عدم الضرورة',\n        linkTip2: 'صلاحية الرابط يومان؛ بعد إعادة إنشاء الرابط، يصبح الرابط السابق غير صالح تلقائياً',\n    },\n    recoverSpecialSeal: {\n        title: 'الختم غير قابل للاستخدام',\n        description1: 'يطلب المرسل منك استخدام هذا الختم للتوقيع على العقد، ولكن شركتك قد حذفت هذا الختم. لضمان سير التوقيع بسلاسة، يرجى طلب استعادة هذا الختم من المدير.',\n        description2: 'إذا كان هذا الختم غير مناسب للاستخدام المستمر، يمكنك الاتصال بالمرسل لتعديل متطلبات صورة الختم قبل توقيع العقد.',\n        postRecover: 'طلب استعادة الختم',\n        note: 'بعد النقر، سيتلقى المدير رسالة قصيرة/بريد إلكتروني لطلب استعادة الختم، كما يمكن رؤية الطلب في صفحة إدارة الأختام.',\n        requestSend: 'تم تقديم طلب الاستعادة بنجاح',\n    },\n    paperSign: {\n        title: 'استخدام طريقة التوقيع الورقي',\n        stepText: ['الخطوة التالية', 'تأكيد التوقيع الورقي', 'تأكيد'],\n        needUploadFile: 'يرجى تحميل النسخة الممسوحة ضوئياً أولاً',\n        uploadError: 'فشل التحميل',\n        cancel: 'إلغاء',\n        downloadPaperFile: 'الحصول على ملف التوقيع الورقي',\n        step0: {\n            title: 'تحتاج أولاً إلى تحميل وطباعة العقد، ووضع الختم المادي، ثم إرساله بالبريد إلى المرسل.',\n            address: 'عنوان البريد:',\n            contactName: 'اسم المستلم:',\n            contactPhone: 'معلومات الاتصال بالمستلم:',\n            defaultValue: 'يرجى الحصول عليها من المرسل خارج الإنترنت',\n        },\n        step1: {\n            title0: 'الخطوة الأولى: تحميل وطباعة العقد الورقي',\n            title0Desc: ['يجب أن يتضمن العقد المطبوع صورة الختم الإلكتروني الموقع. يرجى', 'الحصول على ملف التوقيع الورقي.'],\n            title1: 'الخطوة الثانية: وضع الختم',\n            title1Desc: 'ضع ختم الشركة الساري للعقد على العقد الورقي.',\n            title2: ['الخطوة الثالثة:', 'تحميل النسخة الممسوحة ضوئياً،', 'العودة إلى صفحة التوقيع، انقر على زر التوقيع، أكمل التوقيع الورقي'],\n            title2Desc: ['بعد تحويل العقد الورقي إلى نسخة ممسوحة ضوئياً (ملف PDF) وتحميلها،', 'لن يظهر صورة ختمك في العقد الإلكتروني، ولكن سيتم تسجيل عملية العملية هذه.'],\n        },\n        step2: {\n            title: ['تحميل نسخة العقد الورقي الممسوحة ضوئياً (ملف PDF)', 'يرجى التأكد من تحميل وتوقيع العقد الورقي قبل النقر على زر التأكيد لإنهاء عملية التوقيع الورقي.'],\n            uploadFile: 'تحميل النسخة الممسوحة ضوئياً',\n            getCodeVerify: 'الحصول على رمز التحقق من توقيع العقد',\n            isUploading: 'جاري التحميل...',\n        },\n    },\n    allowPaperSignDialog: {\n        title: 'السماح بالتوقيع الورقي',\n        content: 'هذا العقد من {senderName} إلى {receiverName}، يسمح بالتوقيع بالطريقة الورقية.',\n        tip: 'يمكنك أيضاً اختيار تحميل وثيقة العقد وطباعتها، وتقديمها للمسؤول عن ختم المؤسسة للتوقيع خارج الإنترنت.',\n        icon: 'تحويل إلى توقيع ورقي >>',\n        goSign: 'اذهب للتوقيع الإلكتروني',\n        cancel: 'إلغاء',\n    },\n    sealInconformityDialog: {\n        errorSeal: {\n            title: 'تنبيه الختم',\n            tip: 'تم اكتشاف أن صورة ختمك الحالي لا تتطابق مع هوية مؤسستك، نتيجة التعرف على صورة الختم الحالي:',\n            tip1: 'تم اكتشاف ختم مؤسسة واسم مؤسسة:',\n            tip2: 'هل تريد متابعة استخدام صورة الختم الحالية؟',\n            tip3: 'وفقاً لمتطلبات المرسل، تحتاج إلى استخدام ختم باسم المؤسسة:',\n            tip4: 'الختم',\n            tip5: 'يرجى التأكد من أن الختم يتوافق مع المتطلبات، وإلا سيؤثر على صلاحية العقد!',\n            tip6: 'غير متطابق، يرجى التأكد من أن الختم يتوافق مع متطلبات المرسل.',\n            guide: 'كيفية تحميل الختم الصحيح >>',\n            next: 'متابعة الاستخدام',\n            tip7: 'واسم ختمك لا يتوافق مع المعايير، يحتوي على كلمة \"{keyWord}\".',\n            tip8: 'تم اكتشاف أن اسم الختم لا يتوافق مع المعايير، يحتوي على كلمة \"{keyWord}\"، هل تريد متابعة الاستخدام؟',\n        },\n        exampleSeal: {\n            title: 'طريقة تحميل نموذج الختم',\n            way1: ['الطريقة الأولى:', '1- ضع ختماً فعلياً على ورقة بيضاء', '2- التقط صورة وقم بتحميلها إلى المنصة'],\n            way2: ['الطريقة الثانية:', 'استخدم مباشرة وظيفة إنشاء الختم الإلكتروني في المنصة، كما هو موضح:'],\n            errorWay: ['طريقة خاطئة:', 'حمل الختم باليد', 'صورة غير ذات صلة', 'رخصة العمل'],\n        },\n        confirm: 'تأكيد',\n        cancel: 'إلغاء',\n    },\n    addSealDialog: {\n        title: 'إضافة صورة الختم',\n        dec1: 'يرجى اختيار صورة ختم من المجلد المحلي (التنسيقات: JPG، JPEG، PNG، إلخ). سيقوم النظام بدمج صورة الختم هذه في العقد الحالي.',\n        dec2: 'بعد ذلك، تحتاج إلى النقر على زر \"التوقيع\" لاجتياز التحقق من التوقيع لإتمام الختم.',\n        updateNewSeal: 'رفع ختم جديد',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,cAAc,EAAE;IACZC,iBAAiB,EAAE,gFAAgF;IACnGC,aAAa,EAAE;MACXC,GAAG,EAAE,mEAAmE;MACxEC,GAAG,EAAE,2DAA2D;MAChEC,KAAK,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,OAAO;IACfC,GAAG,EAAE;EACT,CAAC;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,yMAAyM;IACxNJ,QAAQ,EAAE,QAAQ;IAClBK,uBAAuB,EAAE,6FAA6F;IACtHC,QAAQ,EAAE,gBAAgB;IAC1BC,gBAAgB,EAAE;MACdC,WAAW,EAAE,uBAAuB;MACpCC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE,cAAc;MACxBC,IAAI,EAAE,0FAA0F;MAChGC,IAAI,EAAE,+DAA+D;MACrEC,IAAI,EAAE,6FAA6F;MACnGC,IAAI,EAAE,qNAAqN;MAC3NC,IAAI,EAAE,+DAA+D;MACrEC,IAAI,EAAE,6CAA6C;MACnDC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE;IACjB,CAAC;IACDC,UAAU,EAAE,gCAAgC;IAC5CC,aAAa,EAAE,2BAA2B;IAC1CC,gBAAgB,EAAE,2BAA2B;IAC7CC,YAAY,EAAE,8FAA8F;IAC5GC,wBAAwB,EAAE,iCAAiC;IAC3DC,mBAAmB,EAAE,qCAAqC;IAC1DC,qBAAqB,EAAE,uBAAuB;IAC9CC,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,uCAAuC;IACzDC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,OAAO;IACfhC,MAAM,EAAE,OAAO;IACfE,IAAI,EAAE,YAAY;IAClB+B,OAAO,EAAE,mEAAmE;IAC5EC,eAAe,EAAE,oFAAoF;IACrGC,qBAAqB,EAAE,+GAA+G;IACtIC,iBAAiB,EAAE,oBAAoB;IACvCC,cAAc,EAAE,kBAAkB;IAClCC,uBAAuB,EAAE,wCAAwC;IACjEC,8BAA8B,EAAE,2BAA2B;IAC3DC,kBAAkB,EAAE,iDAAiD;IACrEC,cAAc,EAAE,kBAAkB;IAClCC,aAAa,EAAE,8DAA8D;IAC7EC,UAAU,EAAE,uBAAuB;IACnCC,cAAc,EAAE,2FAA2F;IAC3GC,QAAQ,EAAE,aAAa;IACvBC,aAAa,EAAE,wBAAwB;IACvCC,qBAAqB,EAAE,qCAAqC;IAC5DC,OAAO,EAAE,4CAA4C;IACrDC,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE,OAAO;IACZC,OAAO,EAAE,aAAa;IACtBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE,OAAO;IACjBC,gBAAgB,EAAE,mBAAmB;IACrCC,YAAY,EAAE,mBAAmB;IACjCC,WAAW,EAAE,sGAAsG;IACnHC,cAAc,EAAE,8BAA8B;IAC9CC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,mKAAmK;IACpLC,eAAe,EAAE,sKAAsK;IACvLC,iBAAiB,EAAE,2CAA2C;IAC9DC,aAAa,EAAE,iKAAiK;IAChLC,WAAW,EAAE,wBAAwB;IACrCC,UAAU,EAAE,2CAA2C;IACvDC,WAAW,EAAE,cAAc;IAC3BC,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,oBAAoB;IACjCC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,yBAAyB;IACzCC,YAAY,EAAE,8DAA8D;IAC5EC,SAAS,EAAE,4CAA4C;IACvDC,YAAY,EAAE,8FAA8F;IAC5GC,gBAAgB,EAAE,oDAAoD;IACtEC,WAAW,EAAE,gBAAgB;IAC7BC,gBAAgB,EAAE,saAAsa,GACpb,2MAA2M;IAC/MC,YAAY,EAAE,6GAA6G;IAC3HC,SAAS,EAAE,OAAO;IAClBxE,UAAU,EAAE,MAAM;IAClByE,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,gBAAgB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,UAAU,EAAE,0BAA0B;IACtCC,UAAU,EAAE,6FAA6F;IACzGC,cAAc,EAAE,OAAO;IACvBC,SAAS,EAAE,kDAAkD;IAC7DC,SAAS,EAAE,wDAAwD;IACnEC,SAAS,EAAE,2HAA2H;IACtIC,OAAO,EAAE,iCAAiC;IAC1CC,aAAa,EAAE,oDAAoD;IACnEC,aAAa,EAAE,2CAA2C;IAC1DC,eAAe,EAAE,2DAA2D;IAC5EC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,cAAc;IAC1BC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,0BAA0B;IACxCC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE,+BAA+B;IAC/CC,YAAY,EAAE,uBAAuB;IACrCC,aAAa,EAAE,uBAAuB;IACtCC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,eAAe;IAC7BC,eAAe,EAAE,gCAAgC;IACjDC,WAAW,EAAE,sBAAsB;IACnCC,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,QAAQ;IAChBC,cAAc,EAAE,YAAY;IAC5BC,WAAW,EAAE,kBAAkB;IAC/BC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE,mCAAmC;IACjDC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE,0BAA0B;IACzCC,OAAO,EAAE,+DAA+D;IACxEC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,YAAY;IACvBC,oBAAoB,EAAE,sBAAsB;IAC5CC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,2BAA2B;IACtCC,SAAS,EAAE,uBAAuB;IAClCC,YAAY,EAAE,2EAA2E;IACzFC,cAAc,EAAE,4BAA4B;IAC5CC,iBAAiB,EAAE,6EAA6E;IAChGC,aAAa,EAAE,aAAa;IAC5BC,gBAAgB,EAAE,gGAAgG;IAClHC,YAAY,EAAE,oBAAoB;IAClCC,mBAAmB,EAAE,mBAAmB;IACxCC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,aAAa;IACvBC,gBAAgB,EAAE,YAAY;IAC9BC,oBAAoB,EAAE,YAAY;IAClCC,SAAS,EAAE,kBAAkB;IAC7BC,EAAE,EAAE,MAAM;IACVC,cAAc,EAAE,kBAAkB;IAClCC,YAAY,EAAE,uBAAuB;IACrCC,MAAM,EAAE,+BAA+B;IACvCC,YAAY,EAAE,mBAAmB;IACjCC,UAAU,EAAE,gCAAgC;IAC5CC,EAAE,EAAE,IAAI;IACRC,YAAY,EAAE,kCAAkC;IAChDC,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,qCAAqC;IAClDC,OAAO,EAAE,mBAAmB;IAC5BC,UAAU,EAAE,kCAAkC;IAC9CC,UAAU,EAAE,iCAAiC;IAC7CC,UAAU,EAAE,oBAAoB;IAChCC,kBAAkB,EAAE,uBAAuB;IAC3CC,eAAe,EAAE,8BAA8B;IAC/CC,cAAc,EAAE;MACZ,CAAC,EAAE,gDAAgD;MACnD,CAAC,EAAE,kEAAkE;MACrEC,OAAO,EAAE;IACb,CAAC;IACDC,OAAO,EAAE,kBAAkB;IAC3BC,SAAS,EAAE,sBAAsB;IACjCC,UAAU,EAAE,oBAAoB;IAChCC,MAAM,EAAE,sBAAsB;IAC9BC,eAAe,EAAE,aAAa;IAC9BC,aAAa,EAAE,iBAAiB;IAChCC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,QAAQ;IACnBC,cAAc,EAAE,gBAAgB;IAChCC,QAAQ,EAAE,0BAA0B;IACpCC,mBAAmB,EAAE,2BAA2B;IAChDC,aAAa,EAAE,wBAAwB;IACvCC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,qBAAqB;IAC/BC,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,iBAAiB;IAChCC,YAAY,EAAE,wBAAwB;IACtCC,SAAS,EAAE;MACPC,QAAQ,EAAE,wBAAwB;MAClCC,KAAK,EAAE,4CAA4C;MACnDC,KAAK,EAAE,+BAA+B;MACtCC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,4CAA4C;MACnDC,KAAK,EAAE,qCAAqC;MAC5CC,SAAS,EAAE,4DAA4D;MACvEC,SAAS,EAAE,sCAAsC;MACjDC,SAAS,EAAE,wBAAwB;MACnCC,SAAS,EAAE,iEAAiE;MAC5EC,WAAW,EAAE,QAAQ;MACrBC,iBAAiB,EAAE,aAAa;MAChCC,YAAY,EAAE,2BAA2B;MACzCC,SAAS,EAAE,SAAS;MACpBC,YAAY,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,OAAO;IACfC,gBAAgB,EAAE;MACd;MACAC,iBAAiB,EAAE,oEAAoE;MACvFC,UAAU,EAAE,6DAA6D;MACzEC,aAAa,EAAE,iDAAiD;MAChEC,WAAW,EAAE;IACjB,CAAC;IACDC,eAAe,EAAE,gBAAgB;IACjCC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,qDAAqD;IACvEC,gBAAgB,EAAE,+CAA+C;IACjEC,gBAAgB,EAAE,+CAA+C;IACjEC,gBAAgB,EAAE,+CAA+C;IACjEC,gBAAgB,EAAE,2DAA2D;IAC7EC,aAAa,EAAE,sBAAsB;IACrCC,WAAW,EAAE,iBAAiB;IAC9BC,uBAAuB,EAAE,qDAAqD;IAC9EC,wBAAwB,EAAE,4CAA4C;IACtEC,yBAAyB,EAAE,4CAA4C;IACvEC,qBAAqB,EAAE,6EAA6E;IACpGC,sBAAsB,EAAE,oDAAoD;IAC5EC,eAAe,EAAE,qCAAqC;IACtDC,KAAK,EAAE,YAAY;IACnBC,iBAAiB,EAAE,UAAU;IAC7BC,gBAAgB,EAAE,WAAW;IAC7BC,MAAM,EAAE,0BAA0B;IAClCC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE,0FAA0F;IACrGC,iBAAiB,EAAE,4CAA4C;IAC/DC,kBAAkB,EAAE,wEAAwE;IAC5FC,aAAa,EAAE,uEAAuE;IACtFC,QAAQ,EAAE,aAAa;IACvBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,aAAa;IACrBC,0BAA0B,EAAE,oFAAoF;IAChHC,kBAAkB,EAAE,oBAAoB;IACxCC,cAAc,EAAE,gBAAgB;IAChCC,gBAAgB,EAAE,4BAA4B;IAC9CC,IAAI,EAAE,MAAM;IACZC,sBAAsB,EAAE,8DAA8D;IACtFC,UAAU,EAAE,+BAA+B;IAC3CC,gBAAgB,EAAE,iBAAiB;IACnCC,WAAW,EAAE,eAAe;IAC5BC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,uCAAuC;IAClDC,aAAa,EAAE,+CAA+C;IAC9DC,kBAAkB,EAAE,iDAAiD;IACrEC,kBAAkB,EAAE,+CAA+C;IACnEC,eAAe,EAAE,qCAAqC;IACtDC,kBAAkB,EAAE,gDAAgD;IACpEC,kBAAkB,EAAE,yCAAyC;IAC7DC,uBAAuB,EAAE,mEAAmE;IAC5FC,eAAe,EAAE,qDAAqD;IACtEC,4BAA4B,EAAE,4EAA4E;IAC1GC,cAAc,EAAE,iBAAiB;IACjCC,yBAAyB,EAAE,8BAA8B;IACzDC,OAAO,EAAE,oBAAoB;IAC7BC,iBAAiB,EAAE,mGAAmG;IACtHC,cAAc,EAAE,oCAAoC;IACpDC,iBAAiB,EAAE,wBAAwB;IAC3CC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,kBAAkB;IACjCC,eAAe,EAAE,0BAA0B;IAC3CC,gBAAgB,EAAE,gFAAgF;IAClGC,eAAe,EAAE,oBAAoB;IACrCC,cAAc,EAAE,oBAAoB;IACpCC,aAAa,EAAE,aAAa;IAC5BC,8BAA8B,EAAE,6CAA6C;IAC7EC,aAAa,EAAE,gEAAgE;IAC/EC,eAAe,EAAE,oCAAoC;IACrDC,cAAc,EAAE,cAAc;IAC9BC,yBAAyB,EAAE,kIAAkI;IAC7JC,mBAAmB,EAAE,qGAAqG;IAC1HC,SAAS,EAAE,cAAc;IACzBC,EAAE,EAAE,KAAK;IACTC,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,sCAAsC;IACpDC,gBAAgB,EAAE,+EAA+E;IACjGC,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,+DAA+D;IAC5EC,cAAc,EAAE,wFAAwF;IACxGC,eAAe,EAAE,yFAAyF;IAC1GC,MAAM,EAAE,2BAA2B;IACnCC,QAAQ,EAAE,4CAA4C;IACtDC,SAAS,EAAE,wDAAwD;IACnEC,EAAE,EAAE,IAAI;IACRC,UAAU,EAAE,kDAAkD;IAC9DC,GAAG,EAAE,wCAAwC;IAC7CC,aAAa,EAAE,uDAAuD;IACtEC,eAAe,EAAE,0CAA0C;IAC3DC,eAAe,EAAE,uCAAuC;IACxDC,eAAe,EAAE,2DAA2D;IAC5EC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE;MACN,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,wBAAwB;MAC3B,CAAC,EAAE;IACP,CAAC;IACDC,qBAAqB,EAAE;MACnB,CAAC,EAAE,uCAAuC;MAC1C,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,sBAAsB;IACnCC,SAAS,EAAE,MAAM;IACjBC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,eAAe;IACxBC,mBAAmB,EAAE,2BAA2B;IAChDC,oBAAoB,EAAE,qCAAqC;IAC3DC,sBAAsB,EAAE,6GAA6G;IACrIC,aAAa,EAAE,kDAAkD;IACjEC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE,gBAAgB;IAC9BC,uBAAuB,EAAE,yCAAyC;IAClEC,8BAA8B,EAAE,+DAA+D;IAC/FC,gBAAgB,EAAE,0BAA0B;IAC5CC,iBAAiB,EAAE,sCAAsC;IACzDC,eAAe,EAAE,kCAAkC;IACnDC,aAAa,EAAE,sCAAsC;IACrDC,aAAa,EAAE,sBAAsB;IACrCC,OAAO,EAAE,gBAAgB;IACzBC,cAAc,EAAE,sCAAsC;IACtDC,aAAa,EAAE,sDAAsD;IACrEC,MAAM,EAAE,uBAAuB;IAC/BC,QAAQ,EAAE,YAAY;IACtBC,2BAA2B,EAAE,iDAAiD;IAC9EC,mBAAmB,EAAE,4BAA4B;IACjDC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,gBAAgB;IACvBC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,2DAA2D;IAC5EC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,aAAa;IACvBC,wBAAwB,EAAE,4CAA4C;IACtEC,sBAAsB,EAAE,6CAA6C;IACrEC,mBAAmB,EAAE,mDAAmD;IACxEC,UAAU,EAAE,eAAe;IAC3BC,wBAAwB,EAAE,sDAAsD;IAChFC,mBAAmB,EAAE,qEAAqE;IAC1FC,mBAAmB,EAAE,kBAAkB;IACvCC,YAAY,EAAE,kBAAkB;IAChCC,OAAO,EAAE,OAAO;IAChBC,YAAY,EAAE,8BAA8B;IAC5CC,iBAAiB,EAAE,mDAAmD;IACtEC,WAAW,EAAE,wDAAwD;IACrEC,YAAY,EAAE,iFAAiF;IAC/FC,sBAAsB,EAAE,0CAA0C;IAClEC,eAAe,EAAE,mFAAmF;IACpGC,cAAc,EAAE,8EAA8E;IAC9FC,YAAY,EAAE,iBAAiB;IAC/BC,gBAAgB,EAAE,6BAA6B;IAC/CC,YAAY,EAAE,kBAAkB;IAChCC,mBAAmB,EAAE,uEAAuE;IAC5FC,iBAAiB,EAAE,0BAA0B;IAC7CC,iBAAiB,EAAE,0BAA0B;IAC7CC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,gBAAgB;IACjCC,eAAe,EAAE,MAAM;IACvBC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,kBAAkB;IACjCC,aAAa,EAAE,oBAAoB;IACnCC,IAAI,EAAE,UAAU;IAChBC,cAAc,EAAE,WAAW;IAC3BC,iBAAiB,EAAE,cAAc;IACjCC,eAAe,EAAE,6BAA6B;IAC9CC,YAAY,EAAE,oBAAoB;IAClCC,mBAAmB,EAAE,+DAA+D;IACpFC,SAAS,EAAE,WAAW;IACtBC,mBAAmB,EAAE,yDAAyD;IAC9EC,uBAAuB,EAAE,kFAAkF;IAC3GC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,eAAe;IACxBC,iBAAiB,EAAE,mBAAmB;IACtCC,eAAe,EAAE,qBAAqB;IACtCC,cAAc,EAAE,oBAAoB;IACpCC,OAAO,EAAE,gBAAgB;IACzBC,gBAAgB,EAAE,0BAA0B;IAC5CC,gBAAgB,EAAE,qCAAqC;IACvDC,uBAAuB,EAAE,0GAA0G;IACnIC,oBAAoB,EAAE,wBAAwB;IAC9CC,0BAA0B,EAAE,wKAAwK;IACpMC,iCAAiC,EAAE,0CAA0C;IAC7EC,uBAAuB,EAAE,gFAAgF;IACzGC,cAAc,EAAE,kBAAkB;IAClCC,OAAO,EAAE;MACLC,EAAE,EAAE,CAAC,SAAS,EAAE,yCAAyC,EAAE,aAAa,EAAE,gBAAgB,EAAE,yCAAyC,CAAC;MACtIC,EAAE,EAAE,uCAAuC;MAC3CC,QAAQ,EAAE,gBAAgB;MAC1BC,UAAU,EAAE,gCAAgC;MAC5CC,UAAU,EAAE,aAAa;MACzBC,UAAU,EAAE,oBAAoB;MAChCC,YAAY,EAAE,2CAA2C;MACzDC,mBAAmB,EAAE;IACzB,CAAC;IACDC,sBAAsB,EAAE,uBAAuB;IAC/CC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,aAAa;IACvBC,gBAAgB,EAAE,kBAAkB;IACpCC,qBAAqB,EAAE,0BAA0B;IACjDC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,qBAAqB;IACrCC,cAAc,EAAE,gEAAgE;IAChFC,mBAAmB,EAAE,2CAA2C;IAChE9Y,GAAG,EAAE,OAAO;IACZ+Y,WAAW,EAAE,mCAAmC;IAChD3O,OAAO,EAAE,OAAO;IAChB4O,kBAAkB,EAAE,kBAAkB;IACtCC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,SAAS;IACnBC,YAAY,EAAE,wCAAwC;IACtDC,QAAQ,EAAE,0BAA0B;IACpCC,eAAe,EAAE,wDAAwD;IACzEC,EAAE,EAAE,OAAO;IACXC,WAAW,EAAE,2EAA2E;IACxFC,mBAAmB,EAAE;MACjBC,OAAO,EAAE,4GAA4G;MACrH7R,KAAK,EAAE,gBAAgB;MACvB8R,iBAAiB,EAAE,mBAAmB;MACtCC,gBAAgB,EAAE;IACtB,CAAC;IACDC,SAAS,EAAE,oBAAoB;IAC/BC,eAAe,EAAE,cAAc;IAC/BC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,cAAc;IACvBC,cAAc,EAAE,gCAAgC;IAChDC,OAAO,EAAE,sBAAsB;IAC/BC,UAAU,EAAE;EAEhB,CAAC;EACDC,MAAM,EAAE;IACJC,cAAc,EAAE,sDAAsD;IACtEC,cAAc,EAAE,qFAAqF;IACrGC,cAAc,EAAE,kFAAkF;IAClGC,cAAc,EAAE,0IAA0I;IAC1JC,cAAc,EAAE,iHAAiH;IACjIC,cAAc,EAAE,4FAA4F;IAC5GC,cAAc,EAAE,gDAAgD;IAChEC,kBAAkB,EAAE,wBAAwB;IAC5CC,0BAA0B,EAAE,uBAAuB;IACnDC,eAAe,EAAE,YAAY;IAC7BC,qBAAqB,EAAE,uBAAuB;IAC9CC,UAAU,EAAE,YAAY;IACxBC,KAAK,EAAE,SAAS;IAChBC,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,eAAe;IAC9BC,kBAAkB,EAAE,kCAAkC;IACtDC,kBAAkB,EAAE,0BAA0B;IAC9CC,UAAU,EAAE;MACRzT,KAAK,EAAE,aAAa;MACpB5H,GAAG,EAAE,6DAA6D;MAClEsb,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE,mBAAmB;IACjCC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,OAAO;IACXC,GAAG,EAAE,0BAA0B;IAC/BC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1Blb,IAAI,EAAE,uRAAuR;IAC7RC,IAAI,EAAE,2PAA2P;IACjQC,IAAI,EAAE,wEAAwE;IAC9EC,IAAI,EAAE,4BAA4B;IAClCC,IAAI,EAAE,mFAAmF;IACzFC,IAAI,EAAE,0BAA0B;IAChC8a,IAAI,EAAE,gHAAgH;IACtHC,IAAI,EAAE,2DAA2D;IACjEC,IAAI,EAAE,oEAAoE;IAC1EC,KAAK,EAAE,qEAAqE;IAC5EC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,uCAAuC;IAC9CC,KAAK,EAAE,4EAA4E;IACnFC,KAAK,EAAE,iEAAiE;IACxEC,MAAM,EAAE,kBAAkB;IAC1BC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE,+BAA+B;IAC3CC,OAAO,EAAE,oSAAoS;IAC7SC,OAAO,EAAE,kQAAkQ;IAC3QC,OAAO,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACJC,UAAU,EAAE,eAAe;IAC3BC,oBAAoB,EAAE,iBAAiB;IACvCC,aAAa,EAAE,sBAAsB;IACrCC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE,wFAAwF;IACnGC,YAAY,EAAE,gHAAgH;IAC9HC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,yEAAyE;IACxFC,oBAAoB,EAAE,8FAA8F;IACpHC,GAAG,EAAE,SAAS;IACdC,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,MAAM;IACdC,kBAAkB,EAAE,gBAAgB;IACpCC,OAAO,EAAE;MACL3X,IAAI,EAAE,aAAa;MACnB4X,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,oEAAoE;MAC7EC,YAAY,EAAE,iBAAiB;MAC/BC,SAAS,EAAE,8CAA8C;MACzDC,iBAAiB,EAAE,mCAAmC;MACtDC,QAAQ,EAAE,cAAc;MACxBpC,SAAS,EAAE,eAAe;MAC1BqC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACfC,SAAS,EAAE,cAAc;MACzBC,WAAW,EAAE,iBAAiB;MAC9BC,gBAAgB,EAAE,mBAAmB;MACrCC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;QACF5e,GAAG,EAAE,4BAA4B;QACjC6e,IAAI,EAAE,uCAAuC;QAC7CC,IAAI,EAAE,yCAAyC;QAC/C5e,KAAK,EAAE;MACX,CAAC;MACD6e,QAAQ,EAAE,WAAW;MACrBC,gBAAgB,EAAE,kBAAkB;MACpCC,aAAa,EAAE,aAAa;MAC5BC,qBAAqB,EAAE,oBAAoB;MAC3CC,SAAS,EAAE,gBAAgB;MAC3BC,SAAS,EAAE,oBAAoB;MAC/BC,OAAO,EAAE;QACLrf,GAAG,EAAE,yEAAyE;QAC9EC,GAAG,EAAE;MACT,CAAC;MACD+N,yBAAyB,EAAE;IAC/B,CAAC;IACDsR,iBAAiB,EAAE;MACfC,OAAO,EAAE,cAAc;MACvBC,YAAY,EAAE,oBAAoB;MAClCC,gBAAgB,EAAE,iBAAiB;MACnCC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,gBAAgB;MACtBtf,GAAG,EAAE,OAAO;MACZuf,eAAe,EAAE,8DAA8D;MAC/EC,QAAQ,EAAE;IACd;EACJ,CAAC;EACDR,OAAO,EAAE;IACLS,cAAc,EAAE,cAAc;IAC9BC,WAAW,EAAE,eAAe;IAC5BC,IAAI,EAAE,SAAS;IACf5d,MAAM,EAAE,OAAO;IACf6d,gBAAgB,EAAE,kBAAkB;IACpCC,eAAe,EAAE,0BAA0B;IAC3CC,UAAU,EAAE,gFAAgF;IAC5FC,WAAW,EAAE,gEAAgE;IAC7EC,UAAU,EAAE,sCAAsC;IAClDC,iBAAiB,EAAE,oBAAoB;IACvCC,eAAe,EAAE,sBAAsB;IACvCC,eAAe,EAAE,oBAAoB;IACrCC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,8BAA8B;IAC9CC,cAAc,EAAE,2BAA2B;IAC3CC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,oBAAoB;IAClCC,gBAAgB,EAAE,kBAAkB;IACpCC,UAAU,EAAE,2BAA2B;IACvCC,gBAAgB,EAAE,aAAa;IAC/B1gB,IAAI,EAAE,YAAY;IAClB2gB,MAAM,EAAE,YAAY;IACpBrd,QAAQ,EAAE,cAAc;IACxBsd,QAAQ,EAAE,QAAQ;IAClB3d,MAAM,EAAE,KAAK;IACb4d,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,kBAAkB;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,GAAG;IACtB3d,YAAY,EAAE,mBAAmB;IACjC4d,cAAc,EAAE,oHAAoH;IACpIC,cAAc,EAAE,qGAAqG;IACrHC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,6BAA6B;IACvCC,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,aAAa;IAC/BC,oBAAoB,EAAE,YAAY;IAClCC,WAAW,EAAE,oBAAoB;IACjCC,gBAAgB,EAAE,qBAAqB;IACvCC,WAAW,EAAE;MACTphB,IAAI,EAAE,6DAA6D;MACnEC,IAAI,EAAE,iJAAiJ;MACvJohB,OAAO,EAAE;IACb,CAAC;IACDC,WAAW,EAAE,qBAAqB;IAClCC,OAAO,EAAE,aAAa;IACtBC,YAAY,EAAE,kEAAkE;IAChFC,YAAY,EAAE,mBAAmB;IACjCC,eAAe,EAAE,2CAA2C;IAC5DC,YAAY,EAAE,4BAA4B;IAC1CC,QAAQ,EAAE,mDAAmD;IAC7DC,YAAY,EAAE,2DAA2D;IACzEC,qBAAqB,EAAE,uCAAuC;IAC9DC,kBAAkB,EAAE,mCAAmC;IACvDC,eAAe,EAAE,gIAAgI;IACjJC,iBAAiB,EAAE,mCAAmC;IACtDC,oBAAoB,EAAE,0CAA0C;IAChEC,kBAAkB,EAAE;EACxB,CAAC;EACD7d,IAAI,EAAE;IACF4C,KAAK,EAAE,WAAW;IAClBkb,EAAE,EAAE,OAAO;IACXrf,YAAY,EAAE,mBAAmB;IACjC6d,cAAc,EAAE;EACpB,CAAC;EACDyB,OAAO,EAAE;IACLC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,yCAAyC;IACvDC,mBAAmB,EAAE,MAAM;IAC3BC,mBAAmB,EAAE,yCAAyC;IAC9DC,gBAAgB,EAAE,6IAA6I;IAC/JC,iBAAiB,EAAE,kCAAkC;IACrDC,aAAa,EAAE,2CAA2C;IAC1DC,WAAW,EAAE,oDAAoD;IACjEC,eAAe,EAAE,kCAAkC;IACnDC,KAAK,EAAE,eAAe;IACtBC,aAAa,EAAE,cAAc;IAC7BC,KAAK,EAAE,gBAAgB;IACvBC,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE,oBAAoB;IAChC1E,IAAI,EAAE,gBAAgB;IACtB2E,cAAc,EAAE,sDAAsD;IACtEC,eAAe,EAAE,6CAA6C;IAC9DC,kBAAkB,EAAE,mDAAmD;IACvEC,eAAe,EAAE,sDAAsD;IACvEC,cAAc,EAAE,0DAA0D;IAC1EC,gBAAgB,EAAE,gDAAgD;IAClEC,iBAAiB,EAAE,6CAA6C;IAChEC,eAAe,EAAE,sCAAsC;IACvDC,YAAY,EAAE,2BAA2B;IACzCC,aAAa,EAAE,0BAA0B;IACzCC,aAAa,EAAE,+BAA+B;IAC9CC,gBAAgB,EAAE,6DAA6D;IAC/EC,iBAAiB,EAAE,uCAAuC;IAC1DC,iBAAiB,EAAE,2BAA2B;IAC9CC,oBAAoB,EAAE,4CAA4C;IAClEC,SAAS,EAAE,uBAAuB;IAClCC,WAAW,EAAE,gCAAgC;IAC7CC,qBAAqB,EAAE,uBAAuB;IAC9CC,mBAAmB,EAAE,iEAAiE;IACtFC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,kBAAkB;IAChCC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,UAAU;IACxBtQ,YAAY,EAAE,8BAA8B;IAC5CuQ,OAAO,EAAE,sBAAsB;IAC/BC,MAAM,EAAE,0CAA0C;IAClDC,UAAU,EAAE,kCAAkC;IAC9CC,gBAAgB,EAAE,gDAAgD;IAClEC,aAAa,EAAE,6GAA6G;IAC5HC,SAAS,EAAE,mBAAmB;IAC9B9lB,GAAG,EAAE,OAAO;IACZD,MAAM,EAAE;EACZ,CAAC;EACDgmB,WAAW,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,WAAW;IACrBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,qBAAqB;IACpCC,kBAAkB,EAAE,wFAAwF;IAC5GC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,eAAe;IAC3BC,eAAe,EAAE,uBAAuB;IACxCC,mBAAmB,EAAE,6IAA6I;IAClKC,kBAAkB,EAAE,2BAA2B;IAC/CC,sBAAsB,EAAE,wLAAwL;IAChNC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,oCAAoC;IAC9CC,OAAO,EAAE,gEAAgE;IACzEC,KAAK,EAAE,gCAAgC;IACvCC,SAAS,EAAE,gHAAgH;IAE3HC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,8CAA8C;IAC1DC,kBAAkB,EAAE,eAAe;IACnCC,cAAc,EAAE,8EAA8E;IAC9FC,YAAY,EAAE,8CAA8C;IAC5DC,gBAAgB,EAAE,qBAAqB;IACvCC,SAAS,EAAE,gCAAgC;IAC3CC,iBAAiB,EAAE,qBAAqB;IACxCC,YAAY,EAAE,sEAAsE;IACpFC,eAAe,EAAE,wIAAwI;IACzJC,OAAO,EAAE,gBAAgB;IACzBC,KAAK,EAAE,iIAAiI;IACxI5hB,SAAS,EAAE,OAAO;IAClB6hB,KAAK,EAAE,KAAK;IACZC,EAAE,EAAE,YAAY;IAChBC,UAAU,EAAE,6DAA6D;IACzEC,mBAAmB,EAAE,6BAA6B;IAClDC,cAAc,EAAE,uBAAuB;IACvCC,SAAS,EAAE,oDAAoD;IAC/DC,UAAU,EAAE,kCAAkC;IAC9CC,kBAAkB,EAAE,wBAAwB;IAC5CC,qBAAqB,EAAE,2BAA2B;IAClDC,eAAe,EAAE,oDAAoD;IACrE/I,QAAQ,EAAE,sEAAsE;IAChFgJ,UAAU,EAAE,iEAAiE;IAC7EC,cAAc,EAAE,kBAAkB;IAClCC,cAAc,EAAE,mBAAmB;IACnCC,SAAS,EAAE,eAAe;IAC1BtlB,OAAO,EAAE,QAAQ;IACjBulB,kBAAkB,EAAE,4BAA4B;IAChDC,oBAAoB,EAAE,uBAAuB;IAC7CC,0BAA0B,EAAE,iBAAiB;IAC7CC,8BAA8B,EAAE,mDAAmD;IACnFC,8BAA8B,EAAE,6BAA6B;IAC7DC,iBAAiB,EAAE,qBAAqB;IACxCC,QAAQ,EAAE,yCAAyC;IACnDC,gBAAgB,EAAE,uCAAuC;IACzDC,mBAAmB,EAAE,iBAAiB;IACtCC,QAAQ,EAAE,4BAA4B;IACtCC,gBAAgB,EAAE,oCAAoC;IACtDC,cAAc,EAAE,sBAAsB;IACtCC,WAAW,EAAE,6CAA6C;IAC1DC,QAAQ,EAAE,SAAS;IACnBxpB,IAAI,EAAE,OAAO;IACbypB,IAAI,EAAE,QAAQ;IACdC,aAAa,EAAE,iKAAiK;IAChLC,QAAQ,EAAE,kCAAkC;IAC5CC,aAAa,EAAE,uBAAuB;IACtCC,YAAY,EAAE,YAAY;IAC1BC,cAAc,EAAE,cAAc;IAC9BC,kBAAkB,EAAE,yBAAyB;IAC7CC,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE,wBAAwB;IACpCC,QAAQ,EAAE,kCAAkC;IAC5CC,YAAY,EAAE,mIAAmI;IACjJC,uBAAuB,EAAE,iGAAiG;IAC1HC,aAAa,EAAE,sDAAsD;IACrEC,YAAY,EAAE,wBAAwB;IACtCC,YAAY,EAAE,gCAAgC;IAC9CC,MAAM,EAAE,kBAAkB;IAC1BC,UAAU,EAAE,mBAAmB;IAC/BC,WAAW,EAAE,iDAAiD;IAC9DC,mBAAmB,EAAE,uBAAuB;IAC5CC,YAAY,EAAE,YAAY;IAC1BC,qBAAqB,EAAE,gCAAgC;IACvDC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,yBAAyB;IACpCC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,mCAAmC;IACnDC,gBAAgB,EAAE,YAAY;IAC9BC,UAAU,EAAE,qBAAqB;IACjCC,aAAa,EAAE,kBAAkB;IACjCC,aAAa,EAAE,kBAAkB;IACjCzU,IAAI,EAAE,KAAK;IACX0U,YAAY,EAAE,iBAAiB;IAC/BC,oBAAoB,EAAE,uBAAuB;IAC7C3I,EAAE,EAAE,OAAO;IACX4I,WAAW,EAAE,kDAAkD;IAC/DngB,MAAM,EAAE,gBAAgB;IACxBogB,UAAU,EAAE,sFAAsF;IAClGC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,aAAa;IACrBC,EAAE,EAAE,MAAM;IACVC,WAAW,EAAE,+BAA+B;IAC5CC,mBAAmB,EAAE,uCAAuC;IAC5DC,UAAU,EAAE,gBAAgB;IAC5BC,QAAQ,EAAE,qBAAqB;IAC/BC,cAAc,EAAE,iCAAiC;IACjDrH,oBAAoB,EAAE,0BAA0B;IAChDN,YAAY,EAAE,2BAA2B;IACzCC,aAAa,EAAE,0BAA0B;IACzCC,aAAa,EAAE,+BAA+B;IAC9C0H,YAAY,EAAE,gCAAgC;IAC9CzH,gBAAgB,EAAE,2CAA2C;IAC7DE,iBAAiB,EAAE,0BAA0B;IAC7CD,iBAAiB,EAAE,qBAAqB;IACxCyH,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,yKAAyK;IACtLC,cAAc,EAAE,yBAAyB;IACzCC,aAAa,EAAE,6BAA6B;IAC5CC,eAAe,EAAE,uCAAuC;IACxDC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,OAAO;IACf1rB,MAAM,EAAE,KAAK;IACb2rB,YAAY,EAAE,UAAU;IACxBC,QAAQ,EAAE,aAAa;IACvBC,YAAY,EAAE,8CAA8C;IAC5DC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,kCAAkC;IAC3CC,WAAW,EAAE,6DAA6D;IAC1EC,UAAU,EAAE,qBAAqB;IACjCC,cAAc,EAAE;EACpB,CAAC;EACDC,gBAAgB,EAAE;IACdC,UAAU,EAAE,uBAAuB;IACnCC,YAAY,EAAE,8DAA8D;IAC5EC,SAAS,EAAE,2BAA2B;IACtCC,YAAY,EAAE,wGAAwG;IACtHC,OAAO,EAAE,0BAA0B;IACnCC,OAAO,EAAE,uDAAuD;IAChEC,QAAQ,EAAE,kDAAkD;IAC5DC,WAAW,EAAE,gDAAgD;IAC7DC,cAAc,EAAE,oFAAoF;IACpGC,YAAY,EAAE,qBAAqB;IACnCC,eAAe,EAAE,0CAA0C;IAC3DC,gBAAgB,EAAE,2EAA2E;IAC7FC,QAAQ,EAAE,uBAAuB;IACjCC,YAAY,EAAE,iJAAiJ;IAC/JC,iBAAiB,EAAE,wDAAwD;IAC3EpP,SAAS,EAAE,sBAAsB;IACjC9e,IAAI,EAAE,OAAO;IACbmuB,OAAO,EAAE,eAAe;IACxBtG,KAAK,EAAE,KAAK;IACZuG,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACV3mB,KAAK,EAAE,YAAY;IACnB4mB,WAAW,EAAE,sBAAsB;IACnCptB,WAAW,EAAE,sBAAsB;IACnCqtB,MAAM,EAAE,gBAAgB;IACxBC,OAAO,EAAE,oBAAoB;IAC7BC,iBAAiB,EAAE,iBAAiB;IACpCC,MAAM,EAAE,cAAc;IACtBC,OAAO,EAAE,aAAa;IACtBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE,aAAa;IACzBC,iBAAiB,EAAE,sBAAsB;IACzCC,YAAY,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE;MACNxnB,KAAK,EAAE,kBAAkB;MACzBynB,KAAK,EAAE,uDAAuD;MAC9DzD,GAAG,EAAE,WAAW;MAChB9rB,QAAQ,EAAE;IACd,CAAC;IACDwvB,aAAa,EAAE;MACXC,MAAM,EAAE,qDAAqD;MAC7DC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE,kEAAkE;MAC1EC,MAAM,EAAE,kEAAkE;MAC1EC,QAAQ,EAAE;QACN,CAAC,EAAE,2CAA2C;QAC9C,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,aAAa;IACxBC,eAAe,EAAE,kBAAkB;IACnC5W,QAAQ,EAAE,SAAS;IACnB6W,eAAe,EAAE,mBAAmB;IACpCC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,oCAAoC;IAC9CC,iBAAiB,EAAE,8EAA8E;IACjGC,UAAU,EAAE,iDAAiD;IAC7DltB,IAAI,EAAE,OAAO;IACbmtB,qBAAqB,EAAE,iBAAiB;IACxCC,mBAAmB,EAAE,8BAA8B;IACnDC,cAAc,EAAE,sBAAsB;IACtCC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,gBAAgB;IAC3BC,oBAAoB,EAAE,mCAAmC;IACzDC,WAAW,EAAE,kBAAkB;IAC/BtmB,OAAO,EAAE,OAAO;IAChBrK,MAAM,EAAE,OAAO;IACf4wB,UAAU,EAAE,8FAA8F;IAC1GC,UAAU,EAAE,sCAAsC;IAClDC,aAAa,EAAE,WAAW;IAC1BC,SAAS,EAAE,mBAAmB;IAC9BC,uBAAuB,EAAE,oBAAoB;IAC7CC,UAAU,EAAE,0FAA0F;IACtGvN,KAAK,EAAE,eAAe;IACtBxW,YAAY,EAAE,uBAAuB;IACrC0W,KAAK,EAAE,gBAAgB;IACvBsN,qBAAqB,EAAE,kBAAkB;IACzCC,YAAY,EAAE,aAAa;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,gCAAgC;IAC5C5R,QAAQ,EAAE,SAAS;IACnBve,MAAM,EAAE,KAAK;IACbqN,kBAAkB,EAAE,uEAAuE;IAC3FR,eAAe,EAAE,qCAAqC;IACtDujB,MAAM,EAAE,QAAQ;IAChBtjB,KAAK,EAAE,YAAY;IACnBC,iBAAiB,EAAE,UAAU;IAC7BC,gBAAgB,EAAE,WAAW;IAC7BqjB,QAAQ,EAAE;MACN,CAAC,EAAE,mEAAmE;MACtE,CAAC,EAAE,qEAAqE;MACxE,CAAC,EAAE,+CAA+C;MAClD,CAAC,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE,aAAa;IACvBC,kBAAkB,EAAE;MAChB,CAAC,EAAE,8CAA8C;MACjD,CAAC,EAAE;IACP,CAAC;IACDC,cAAc,EAAE,iBAAiB;IACjCvsB,QAAQ,EAAE,gBAAgB;IAC1BwsB,cAAc,EAAE,gBAAgB;IAChCC,qBAAqB,EAAE,0CAA0C;IACjEC,kBAAkB,EAAE,yBAAyB;IAC7CC,oBAAoB,EAAE,gEAAgE;IACtFC,iBAAiB,EAAE,oBAAoB;IACvCC,gBAAgB,EAAE,kBAAkB;IACpCC,MAAM,EAAE,OAAO;IACfC,aAAa,EAAE,YAAY;IAC3Bxc,YAAY,EAAE,iBAAiB;IAC/Byc,SAAS,EAAE,4BAA4B;IACvCC,mBAAmB,EAAE,wBAAwB;IAC7CC,aAAa,EAAE,aAAa;IAC5BC,KAAK,EAAE,YAAY;IACnBrsB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,OAAO;IAClBiF,QAAQ,EAAE,eAAe;IACzBonB,IAAI,EAAE,IAAI;IACVxsB,IAAI,EAAE,OAAO;IACbysB,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,eAAe;IAC7BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,aAAa;IACvBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACTC,WAAW,EAAE;MACTnrB,KAAK,EAAE,2BAA2B;MAClC+X,IAAI,EAAE,kFAAkF;MACxFqT,SAAS,EAAE;IACf,CAAC;IACDC,aAAa,EAAE;MACXrrB,KAAK,EAAE,+BAA+B;MACtC+X,IAAI,EAAE,uEAAuE;MAC7EqT,SAAS,EAAE;IACf,CAAC;IACDE,UAAU,EAAE;MACRtrB,KAAK,EAAE,aAAa;MACpB+X,IAAI,EAAE,iDAAiD;MACvDqT,SAAS,EAAE;IACf,CAAC;IACDG,QAAQ,EAAE,mBAAmB;IAC7B9N,MAAM,EAAE,KAAK;IACb+N,QAAQ,EAAE,cAAc;IACxBC,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE;IACX1rB,KAAK,EAAE,aAAa;IACpB6b,KAAK,EAAE,6CAA6C;IACpDE,KAAK,EAAE,2DAA2D;IAClEE,KAAK,EAAE,wCAAwC;IAC/C0P,OAAO,EAAE,qBAAqB;IAC9BC,UAAU,EAAE,mBAAmB;IAC/BC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACPhsB,KAAK,EAAE,sBAAsB;IAC7BvE,OAAO,EAAE,8BAA8B;IACvCwwB,IAAI,EAAE,aAAa;IACnB1gB,IAAI,EAAE,SAAS;IACf2gB,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,cAAc;IAC7BC,eAAe,EAAE,wCAAwC;IACzDC,eAAe,EAAE,qCAAqC;IACtDC,YAAY,EAAE,YAAY;IAC1BV,UAAU,EAAE,mCAAmC;IAC/CW,cAAc,EAAE,oBAAoB;IACpCC,YAAY,EAAE,sCAAsC;IACpDC,mBAAmB,EAAE,2CAA2C;IAChEC,eAAe,EAAE,kEAAkE;IACnFC,aAAa,EAAE,kCAAkC;IACjDC,YAAY,EAAE,mCAAmC;IACjDC,QAAQ,EAAE,gEAAgE;IAC1EC,QAAQ,EAAE;EACd,CAAC;EACDC,kBAAkB,EAAE;IAChB/sB,KAAK,EAAE,0BAA0B;IACjCgtB,YAAY,EAAE,oJAAoJ;IAClKC,YAAY,EAAE,iHAAiH;IAC/HC,WAAW,EAAE,mBAAmB;IAChC3hB,IAAI,EAAE,mHAAmH;IACzH4hB,WAAW,EAAE;EACjB,CAAC;EACD3sB,SAAS,EAAE;IACPR,KAAK,EAAE,8BAA8B;IACrCotB,QAAQ,EAAE,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,OAAO,CAAC;IAC7DC,cAAc,EAAE,yCAAyC;IACzDC,WAAW,EAAE,aAAa;IAC1Bn1B,MAAM,EAAE,OAAO;IACfo1B,iBAAiB,EAAE,+BAA+B;IAClDC,KAAK,EAAE;MACHxtB,KAAK,EAAE,sFAAsF;MAC7FytB,OAAO,EAAE,eAAe;MACxBC,WAAW,EAAE,cAAc;MAC3BC,YAAY,EAAE,2BAA2B;MACzCC,YAAY,EAAE;IAClB,CAAC;IACD/R,KAAK,EAAE;MACHgS,MAAM,EAAE,0CAA0C;MAClDC,UAAU,EAAE,CAAC,+DAA+D,EAAE,gCAAgC,CAAC;MAC/GC,MAAM,EAAE,2BAA2B;MACnCC,UAAU,EAAE,8CAA8C;MAC1DC,MAAM,EAAE,CAAC,iBAAiB,EAAE,+BAA+B,EAAE,mEAAmE,CAAC;MACjIC,UAAU,EAAE,CAAC,mEAAmE,EAAE,2EAA2E;IACjK,CAAC;IACDnS,KAAK,EAAE;MACH/b,KAAK,EAAE,CAAC,mDAAmD,EAAE,gGAAgG,CAAC;MAC9Jgc,UAAU,EAAE,8BAA8B;MAC1CmS,aAAa,EAAE,sCAAsC;MACrDC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,oBAAoB,EAAE;IAClBruB,KAAK,EAAE,wBAAwB;IAC/BsuB,OAAO,EAAE,+EAA+E;IACxFl2B,GAAG,EAAE,uGAAuG;IAC5Gm2B,IAAI,EAAE,yBAAyB;IAC/BC,MAAM,EAAE,yBAAyB;IACjCr2B,MAAM,EAAE;EACZ,CAAC;EACDs2B,sBAAsB,EAAE;IACpBC,SAAS,EAAE;MACP1uB,KAAK,EAAE,aAAa;MACpB5H,GAAG,EAAE,6FAA6F;MAClGU,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,4CAA4C;MAClDC,IAAI,EAAE,4DAA4D;MAClEC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,2EAA2E;MACjFC,IAAI,EAAE,+DAA+D;MACrEw1B,KAAK,EAAE,6BAA6B;MACpClX,IAAI,EAAE,kBAAkB;MACxBxD,IAAI,EAAE,8DAA8D;MACpEC,IAAI,EAAE;IACV,CAAC;IACD0a,WAAW,EAAE;MACT5uB,KAAK,EAAE,yBAAyB;MAChC6uB,IAAI,EAAE,CAAC,iBAAiB,EAAE,mCAAmC,EAAE,uCAAuC,CAAC;MACvGC,IAAI,EAAE,CAAC,kBAAkB,EAAE,oEAAoE,CAAC;MAChGC,QAAQ,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,YAAY;IAClF,CAAC;IACDvsB,OAAO,EAAE,OAAO;IAChBrK,MAAM,EAAE;EACZ,CAAC;EACD62B,aAAa,EAAE;IACXhvB,KAAK,EAAE,kBAAkB;IACzBivB,IAAI,EAAE,2HAA2H;IACjIC,IAAI,EAAE,mFAAmF;IACzFC,aAAa,EAAE;EACnB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}