{"ast": null, "code": "export default {\n  submitBaiscInfo: '提交基本信息',\n  firstStep: '第一步',\n  secondStep: '第二步',\n  thirdStep: '第三步',\n  nextStep: '下一步',\n  back: '返回',\n  submit: '提交',\n  confirm: '确定',\n  affirm: '确认',\n  cancel: '取消',\n  tip: '提示',\n  account: '账号',\n  view: '查看',\n  iSee: '我知道了',\n  submitAttorney: '提交授权书',\n  plsDownloadSSQAttorneyTip: '请下载《上上签企业服务授权书》，加盖企业公章后拍照上传。',\n  ssqAttorney: '上上签企业服务授权书',\n  downloadAttorney: '请点击下载授权书',\n  imgSupportType: '支持png、jpeg、jpg、bmp格式且大小不超过10M。',\n  imgNoWatermarkTip: '仅支持无水印或”仅用于上上签实名认证“水印字样的照片',\n  confirmSubmit: '确认提交',\n  backToPreviousPage: '返回上一页',\n  uploadImgBeforeSubmit: '请上传照片后再提交',\n  phoneNumBuyTip: '请确保该手机号是在运营商处实名购买，否则将无法通过上上签实名验证。',\n  operatorPhoneVerify: '经办人手机号校验',\n  operatorName: '经办人姓名',\n  operatorIdNum: '经办人身份证号',\n  modifyOperator: '修改经办人',\n  changeIdCard: '更换证件',\n  operatorPassPhoneVerifyTip: ' 经办人在 {time} 已通过手机号校验，校验结果允许被复用。',\n  operatorPhoneNum: '经办人手机号',\n  verifyCode: '验证码',\n  tryFaceVerify: '试试刷脸校验',\n  afterPhoneVerifyTip: '手机号校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',\n  operatorFaceVerify: '经办人刷脸校验',\n  operatorPassFaceVerifyTip: ' 经办人在 {time} 已通过刷脸校验，校验结果允许被复用。',\n  alipayFaceVerify: '支付宝扫一扫刷脸认证',\n  tryPhoneVerify: '试试手机号校验',\n  scanFaceNow: '立即刷脸',\n  afterFaceVerifyTip: '刷脸校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',\n  plsEnterRightPhoneNum: '请先填写正确的手机号',\n  plsGetVerifyCodeFirst: '请先获取验证码',\n  agreeSsqProtectMethod: '我同意上上签对我提交的个人身份信息的保护方法',\n  ssqHowToProtectInfo: '《上上签如何保护您的个人信息》',\n  noEntNameTip: '由于贵公司没有企业名称，不支持授权书认证',\n  legalName: '法定代表人姓名',\n  legalIdCardNum: '法定代表人身份证号',\n  uploadIdCard: '上传身份证',\n  legalPassFaceVerifyTip: '法定代表人在 {time} 已通过刷脸认证，认证结果允许被复用。',\n  plsEnterLegalIdCardNum: '请先输入法定代表人身份证号',\n  plsUploadLegalIdCardImg: '请先上传法定代表人身份证',\n  pageExpiredTip: '当前页面已过期，请重新刷新页面',\n  faceFunctionNotEnabled: '刷脸服务暂时无法使用，请稍后再试',\n  plsSubmitLegalIdCard: '请提交法定代表人的身份证',\n  legalPassPhoneVerifyTip: '法定代表人在 {time} 已通过手机号认证，认证结果允许被复用。',\n  legalPhoneNum: '法定代表人手机号',\n  noPublicAccount: '没有对公账户？',\n  useSpecialMethod: '我是没有公户的分公司，使用特殊通道',\n  ssqRemitMoneyTip: '上上签将向下方对公账户汇入一笔钱（少于0.99元），您回填打款金额即可通过企业认证。',\n  accountName: '户名',\n  bank: '银行',\n  accountBank: '开户行',\n  accountBankName: '开户行名称',\n  bankAccount: '银行账户',\n  plsInputBankNameTip: '请输入银行名称，无需写分行或支行，如“中国银行”',\n  locationOfBank: '开户行所在地',\n  plsSelect: '请选择',\n  province: '省',\n  city: '市',\n  cityCounty: '市/县',\n  nameOfBank: '开户支行名称',\n  plsInputZhiBankName: '填写开户支行名称，如“杭州银行文创支行”',\n  canNotFindBank: '找不到对应银行？',\n  clickChangeReversePay: '点击页面下方“切换打款方式”，进入反向打款。',\n  other: '其他',\n  otherZhiBank: '开户支行名称(如支行名称选择\\'其他\\'请填写)',\n  bankAccount1: '银行账号',\n  plsInputBankAccount: '请输入银行账号',\n  remitNotSuccess: '汇款不成功？',\n  switchPayMethod: '切换打款方式',\n  whySubmitBankInfo: '为什么要提交银行卡信息',\n  submitBankInfoExplan: '银行卡信息认证是企业实名认证的一个验证环节，上上签会向该银行卡汇入一笔验证资金，不会从您的账户扣款。',\n  plsSelectBank: '请从下拉列表中选择银行',\n  plsSelectArea: '请从下拉列表中选择省,市/县',\n  plsInputNameOfAccountBank: '请输入开户行名称',\n  plsInputCorrectBankInfo: '请填写正确的对公银行信息',\n  plsInputFullBankName: '请填写开户行全称（包含支行名称）',\n  area: '地区',\n  contactCustomerService: '联系客服',\n  beiJing: '北京市',\n  dongCheng: '东城区',\n  fillJointBankNum: '该账户需要填写开户银行联行号才能汇款',\n  bankType: {\n    type1: '银行',\n    type2: '信用社联合社',\n    type3: '信用合作社',\n    type4: '农联社'\n  },\n  accountBankArea: '开户行所在地',\n  changeRemitMethod: '换种汇款方式',\n  canNotRemitAuth: '无法向您的企业对公账户发起打款认证，请选择其他认证方式',\n  bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',\n  faceVerify: '刷脸校验',\n  havePublicAccount: '我有对公账户',\n  ensureInfoSafeTip: '为保障企业信息安全、不被他人盗用，请先完成法人刷脸，然后再作打款认证',\n  submitEntBankInfo: '提交企业开户信息',\n  fillBackAmout: '填写回填金额',\n  legalPerson: '法人',\n  operatorMange: '经办人',\n  unionNum: '联行号',\n  getUnionNumTip: '请向贵公司/单位的财务沟通获取联行号，或根据银行支行信息',\n  uinonNumSearchOnline: '联行号在线查询',\n  remitProcessMap: {\n    submit: '打款申请已提交',\n    accept: '打款已受理',\n    success: '打款已成功',\n    receiveWait: '支付请求已受理,等待银行返回结果，请耐心等待',\n    successAttention: '打款已成功，请仔细核对账户收支明细。请注意：',\n    remarkTip: '汇款备注为：款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',\n    thirdChannel: '上上签通过第三方支付通道汇款,汇款通道为:',\n    remitNotSsq: '上上签通过第三方支付通道汇款，汇款方户名并非上上签',\n    remitPartySsq: '汇款方为“杭州尚尚签网络科技有限公司”'\n  },\n  close: '关闭',\n  name: '姓名',\n  idCardNum: '身份证号',\n  reversePayMap: {\n    remitTip1: '向上上签银行对公账户汇入0.01元，再勾选以下选项并点击“确定”，即可通过企业认证。',\n    iAgree: '我知情并同意：',\n    remitAuthUse: '向上上签指定的账户汇入的0.01元，如果认证通过，将用于购买1份对公合同；如果认证失败，也不能要求上上签退款。',\n    authFailure: '不满足以下要求会导致认证失败：',\n    authFailureReason1: '（1）汇款方为当前法人姓名：',\n    authFailureReason2: '（2）汇款方必须使用自己名下的账户进行汇款',\n    authFailureReason3: '（1）汇款方为当前企业名称：',\n    authFailureReason4: '（2）汇款方必须使用对公账户进行汇款',\n    plsInputBankName: '请输入银行账户名称',\n    authFailureReason5: '（3）汇款方向接收方汇出的单笔金额为0.01元人民币，不能大于0.01元或使用其他币种',\n    authFailureReason6: '（4）汇款日期，请确保在 {date} 后汇入',\n    authFailureReason7: '（5）接收方银行账户信息如下，不能汇入其他账户中：',\n    authFailureReason8: '（6）汇款方向接收方汇款时，必须且仅备注该验证码：',\n    remitDelay: '汇款到账可能有延迟，请耐心等待。',\n    failureReason: '认证实名失败的可能原因：',\n    wait30min: '因银行系统原因，您可在汇款成功30分钟后查询结果',\n    queryProgress: '查询进度',\n    inputRemitBankName: '请填入付款方银行户名',\n    queryFailureTip: '查询失败，请稍后重试...',\n    remitAuthSuccess: '已通过打款认证，汇款资金已用于购买1份对公合同。'\n  },\n  hourMinSec: '{hour}时{min}分{sec}秒',\n  minSec: '{min}分{sec}秒',\n  sec: '{sec}秒',\n  ssqRemitTip: '上上签已向贵账户提交一笔1元以下的汇款，1-2个工作日内到账，请于到账后在此确认到账金额。',\n  inputRightRemitAmout: '您需要查询账户的收支明细，正确输入这笔金额才能通过认证。',\n  notGetRemit: '如果您在账户上没有查到汇款金额，点此',\n  queryRemitProgress: '查一查打款进度',\n  inputWrongAccount: '账号填错了？',\n  remitNote: '汇款备注为：',\n  ssqApplyCaTip: '款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',\n  remitAmout: '汇款金额',\n  yuan: '元',\n  receiveAmout: '到账金额为0.01-0.99元之间',\n  maxRemitTimeTip: '最多只能打款四次哦！是否确定要重新提交填写账号？',\n  remitFailure: '打款未成功，即将返回企业打款页面，需要重新申请打款',\n  plsInputRemitAmout: '请输入0.01-0.99元之间的金额',\n  reSubmitBankInfo: '您需要重新提交银行卡信息',\n  amoutError: '金额错误',\n  reSubmit: '重新提交',\n  amoutInvalid: '金额失效',\n  authReject: '实名材料已被驳回，请重新提交',\n  authCertificate: '授权书认证',\n  entPayAuth: '企业打款认证',\n  legalPhoneAuth: '法定代表人手机号认证',\n  legalFaceAuth: '法定代表人刷脸认证',\n  sender: '发件人',\n  requireYouThrough: '{name}要求您通过{type}',\n  selectOneAuthMethod: '请选择以下任意一种实名方式：',\n  entCertificate: '企业证件',\n  personCertificate: '个人证件',\n  iAmLegal: '我是法定代表人',\n  iAmNotLegal: '我不是法定代表人，我是企业经办人',\n  receiveSsqPhone: '我接受上上签电话回访',\n  plsAgreeSsqPhone: '请先同意接受上上签电话回访',\n  submitInfoError: '您提交的个人证件的姓名或身份证号有误，请重新核实。',\n  submitIndividualEntAuth: '您正作为个体工商户提交企业认证，您的营业执照上无企业名称',\n  submitIndividualCorrectIdCard: '请如实提交法定代表人个人身份证',\n  entPersonInfoError: '您提交的企业证件信息有误/个人证件信息有误，请核实后重新提交。',\n  noEntName: '没有企业名称',\n  businessLicenseBlank: '营业执照的企业名称处为：空白',\n  addressName: '/地址/法定代表人姓名',\n  plsClick: '请点击',\n  haveEntName: '有企业名称，要求客服人工审核，',\n  checkFollowInfoTip: '核对以下信息并点击“确定”，您可以继续提交其他认证材料。企业信息转由上上签人工核对，如果信息不正确将驳回您提交的全部资料。信息核对需要约1个工作日。',\n  entName: '企业名称',\n  unifySocialCode: '统一社会信用代码',\n  uploadColorImgTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。企业信息将用于申请数字证书。',\n  businessLicense: '营业执照',\n  uploadBusinessLicenseTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照',\n  imgLimitTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或“仅用于上上签实名认证”水印字样的图片',\n  autoRecognizedTip: '以下信息自动识别，需仔细核对，如识别有误，请修正。',\n  iNoEntName: '我没有企业名称',\n  clickUseSpecialMethod: '点击使用特殊通道',\n  socialCodeBuisnessNum: '统一社会信用代码/工商注册号',\n  plsSubmitCorrectLegalName: '请提交正确的法定代表人姓名',\n  plsSubmitBusinessLicense: '请先提交营业执照',\n  noEntUploadBusinessTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。企业信息将用于申请数字证书。',\n  imgRequireTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。',\n  noEntNameUse: '没有企业名称的个体工商户须使用',\n  legalPlusCode: '法定代表人姓名加上统一社会信用',\n  codeAsEntName: '代码作为企业名称',\n  ssq: '上上签',\n  entAuth: '企业认证',\n  eleContractServiceBy: '电子签约服务由',\n  spericalProvide: '提供',\n  redirectWait: '正在跳转，请稍后',\n  businessLicenseImg: '营业执照照片',\n  idCardNational: '身份证国徽面',\n  idCardFace: '身份证人像面',\n  dueToSignRequire: '因合同签订需要',\n  authorizeSomeone: '我授权{name}获得以下信息：',\n  authoizedInfo: '我的账号基本信息(账号和名称)和企业基本信息(企业名称、统一社会信用代码或注册号、法定代表人姓名)',\n  iSubmitInfoToSsq: '我在上上签平台提交的',\n  authMaterials: '认证材料',\n  sureInfo: '确定无误',\n  modifyInfo: '修改信息',\n  additionalInfo: '补充信息',\n  ssqNotifyMethod: '我在上上签平台用作通知方式的',\n  phoneNum: '手机号',\n  email: '邮箱',\n  submitInfoPlsVerify: '已为您提交企业基本信息，请核实',\n  operatorIdCardFaceImg: '经办人身份证人像面',\n  operatorIdCardNational: '经办人身份证国徽面',\n  legalIdCardFaceImg: '法定代表人身份证人像面',\n  legalIdCardNational: '法定代表人身份证国徽面',\n  plsAgreeAuthorized: '请先同意授权',\n  finishConfirmInfo: '您已完成信息确认，请继续企业认证流程。',\n  entOpenMultipleBusiniss: '企业开通了多业务线，操作无法继续',\n  contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理',\n  signContract: '签署合同',\n  faceVerifyFailure: '刷脸认证失败',\n  reFaceVerifyAuth: '重新刷脸认证',\n  threeTimesFailure: '非常抱歉，您今天已连续3次刷脸比对失败，请选择其他认证方式',\n  faceVerifySucess: '刷脸认证成功',\n  chooseOtherAuthMethod: '选择其他认证方式',\n  plsContinueOnPc: '请在电脑网页端继续操作',\n  accountAppealSuccess: '账号申诉成功',\n  faceCompareSuccess: '刷脸对比成功',\n  plsUseWechatScan: '请使用微信浏览器扫描二维码',\n  wechatCannotScanFace: '因微信策略变更，当前无法刷脸',\n  plsUploadClearIdCardImg: '请上传清晰的身份证照片，系统将自动识别证件信息。照片仅限jpeg、jpg、png格式且大小不超过10M。',\n  uploadImgMap: {\n    tip1: '请上传清晰的身份证照片，',\n    tip2: '系统将自动识别证件信息。',\n    tip3: '照片仅限jpeg、jpg、png格式且大小不超过10M。',\n    tip4: '仅支持无水印或“仅用于上上签实名认证”水印字样的图片'\n  },\n  plsSubmitIdCard: '请先提交身份证',\n  noNameNoSupportEntRemit: '由于贵公司没有企业名称，不支持企业打款认证',\n  checkMoreVerison: '查看更多版本',\n  viewCertificate: '查看证书',\n  continueAuthNewEnt: '继续认证新企业',\n  continueBuyPackage: '继续购买套餐',\n  needSubmitBasicInfo: '仅要求您提交企业基本信息',\n  youFinishEntAuth: '您已完成企业认证',\n  loginSsqWebToEntAuth: '登录上上签官网完成完整的企业认证流程，您可以在上上签平台获得更高权限',\n  entAuthCertificate: '企业实名证书',\n  youFinishEntAuthTip: '您已完成企业实名认证',\n  backToHome: '返回首页',\n  youNeedMoreOperate: '您还需{operate}完成以下操作：',\n  goPc: '前往PC端',\n  addEntMemberStepMap: {\n    title: '添加企业成员步骤：',\n    step1: '1、进入企业控制台',\n    step2: '2、打开成员管理',\n    step3: '3、点击添加新成员',\n    step4: '4、输入账号、姓名，选择角色',\n    step5: '5、点击保存，即可添加新成员'\n  },\n  addEntMember: '添加企业成员',\n  addSealStepMap: {\n    title: '添加印章步骤：',\n    step1: '1、进入企业控制台',\n    step2: '2、打开印章列表',\n    step3: '3、点击新增印章',\n    step4: '4、输入印章名称，上传印章图案或生产电子签章',\n    step5: '5、点击保存，新增印章',\n    step6: '6、点击添加持有人',\n    step7: '7、选择企业成员',\n    step8: '8、点击确定，即可添加印章持有人'\n  },\n  addSeal: '添加印章',\n  waitApporve: '等待审核',\n  submitAuthMaterial: '提交认证材料',\n  authFinish: '认证完成',\n  plsSubmitBeforeTip: '请在{date}前完成所有材料提交，否则基本信息将失效。',\n  oneDayFinishApprove: '客服将在一个工作日内完成审核，请耐心等待',\n  entAuthFinish: '企业已实名完成',\n  baseInfoInvalid: '基本信息已失效，请重新提交',\n  missParams: '缺少参数!',\n  illegalLink: '非法链接',\n  cookieNotEnabe: '无法读写cookie，是否开启了无痕／隐身模式或其他禁用cookie的操作',\n  truthSubmitOperatorIdCard: '请如实提交经办人个人身份证',\n  abandonAttorneyAuth: '放弃授权书认证，',\n  modifyCertiInfo: '修改证件资料',\n  modifyAttorneyInfo: '修改授权书资料',\n  plsUploadBusinessLicense: '请上传营业执照！',\n  plsUploadLegalCerti: '请上传法人证件！',\n  plsUploadOperatorCerti: '请上传经办人证件！',\n  legalIdCardSubmit: '法定代表人身份证提交',\n  serviceAttorneryAuth: '服务授权书认证',\n  accountAppealMap: {\n    entApeal: '企业账号申诉',\n    apealSuccess: '申诉完成',\n    comName: '公司名称:',\n    account: '账号:',\n    verifyCode: '验证码:',\n    ener6Digtal: '请填写6位数字',\n    beMainManagerTip: '企业账号申请成功后，您将成为企业主管理员',\n    mainAccount: '主管理员账号',\n    continueSign: '继续签署',\n    continueConfirm: '继续认证',\n    plsEnterComName: '请先填写公司名称',\n    plsEnterCorrentComName: '请填写正确的公司名称',\n    sendSuccess: '发送成功！',\n    plsEnterCorrectCode: '请填写正确的验证码'\n  },\n  faceInitLoading: '初始化刷脸标签，请稍后',\n  wxFaceVersionTip: '刷脸需要微信版本7.0.12及以上，请先升级',\n  wxIosFaceVersionTip: '刷脸需要ios版本10.3及以上，请先升级',\n  wxAndroidVersionTip: '刷脸需要android版本5.0及以上，请先升级',\n  faceInitFailure: '刷脸标签初始化失败: ',\n  entAuthCertificateTip: '企业实名认证证书',\n  idCardHandHeld: '手持身份证认证',\n  faceAuth: '刷脸认证',\n  noMainlandAuth: '非大陆认证',\n  entAuthTip: '企业实名认证',\n  signIntro: '签署引导',\n  companySet: '企业设置'\n};", "map": {"version": 3, "names": ["submitBaiscInfo", "firstStep", "secondStep", "thirdStep", "nextStep", "back", "submit", "confirm", "affirm", "cancel", "tip", "account", "view", "iSee", "submit<PERSON><PERSON><PERSON><PERSON>", "plsDownloadSSQAttorneyTip", "ssqAttorney", "downloadAttorney", "imgSupportType", "imgNoWatermarkTip", "confirmSubmit", "backToPreviousPage", "uploadImgBeforeSubmit", "phoneNumBuyTip", "operatorPhoneVerify", "operatorName", "operatorIdNum", "modifyOperator", "changeIdCard", "operatorPassPhoneVerifyTip", "operatorPhoneNum", "verifyCode", "tryFaceVerify", "afterPhoneVerifyTip", "operatorFaceVerify", "operatorPassFaceVerifyTip", "alipayFaceVerify", "tryPhoneVerify", "scanFaceNow", "afterFaceVerifyTip", "plsEnterRightPhoneNum", "plsGetVerifyCodeFirst", "agreeSsqProtectMethod", "ssqHowToProtectInfo", "noEntNameTip", "legalName", "legalIdCardNum", "uploadIdCard", "legalPassFaceVerifyTip", "plsEnterLegalIdCardNum", "plsUploadLegalIdCardImg", "pageExpiredTip", "faceFunctionNotEnabled", "plsSubmitLegalIdCard", "legalPassPhoneVerifyTip", "legalPhoneNum", "noPublicAccount", "useSpecialMethod", "ssqRemitMoneyTip", "accountName", "bank", "accountBank", "accountBankName", "bankAccount", "plsInputBankNameTip", "locationOfBank", "plsSelect", "province", "city", "cityCounty", "nameOfBank", "plsInputZhiBankName", "canNotFindBank", "clickChangeReversePay", "other", "otherZhiBank", "bankAccount1", "plsInputBankAccount", "remitNotSuccess", "switchPayMethod", "whySubmitBankInfo", "submitBankInfoExplan", "plsSelectBank", "plsSelectArea", "plsInputNameOfAccountBank", "plsInputCorrectBankInfo", "plsInputFullBankName", "area", "contactCustomerService", "beiJing", "dongCheng", "fillJointBankNum", "bankType", "type1", "type2", "type3", "type4", "accountBankArea", "changeRemitMethod", "canNotRemitAuth", "bankMaintenanceTip", "faceVerify", "havePublicAccount", "ensureInfoSafeTip", "submitEntBankInfo", "fillBackAmout", "legal<PERSON>erson", "operatorMange", "unionNum", "getUnionNumTip", "uinonNumSearchOnline", "remitProcessMap", "accept", "success", "receiveWait", "successAttention", "remarkTip", "thirdChannel", "remitNotSsq", "remitPartySsq", "close", "name", "idCardNum", "reversePayMap", "remitTip1", "iAgree", "remitAuthUse", "authFailure", "authFailureReason1", "authFailureReason2", "authFailureReason3", "authFailureReason4", "plsInputBankName", "authFailureReason5", "authFailureReason6", "authFailureReason7", "authFailureReason8", "remitDelay", "failureReason", "wait30min", "queryProgress", "inputRemitBankName", "queryFailureTip", "remitAuthSuccess", "hourMinSec", "minSec", "sec", "ssqRemitTip", "inputRightRemitAmout", "notGetRemit", "queryRemitProgress", "inputWrongAccount", "remitNote", "ssqApplyCaTip", "remitAmout", "yuan", "receiveAmout", "maxRemitTimeTip", "remitFailure", "plsInputRemitAmout", "reSubmitBankInfo", "amoutError", "reSubmit", "amoutInvalid", "authReject", "authCertificate", "entPayAuth", "legalPhoneAuth", "legalFaceAuth", "sender", "requireYouThrough", "selectOneAuthMethod", "entCertificate", "personCertificate", "iAmLegal", "iAmNotLegal", "receiveSsqPhone", "plsAgreeSsqPhone", "submitInfoError", "submitIndividualEntAuth", "submitIndividualCorrectIdCard", "entPersonInfoError", "noEntName", "businessLicenseBlank", "addressName", "plsClick", "haveEntName", "checkFollowInfoTip", "entName", "unifySocialCode", "uploadColorImgTip", "businessLicense", "uploadBusinessLicenseTip", "imgLimitTip", "autoRecognizedTip", "iNoEntName", "clickUseSpecialMethod", "socialCodeBuisnessNum", "plsSubmitCorrectLegalName", "plsSubmitBusinessLicense", "noEntUploadBusinessTip", "imgRequireTip", "noEntNameUse", "legalPlusCode", "codeAsEntName", "ssq", "entAuth", "eleContractServiceBy", "spericalProvide", "redirectWait", "businessLicenseImg", "idCardNational", "idCardFace", "dueToSignRequire", "authorizeSomeone", "authoizedInfo", "iSubmitInfoToSsq", "authMaterials", "sureInfo", "modifyInfo", "additionalInfo", "ssqNotifyMethod", "phoneNum", "email", "submitInfoPlsVerify", "operatorIdCardFaceImg", "operatorIdCardNational", "legalIdCardFaceImg", "legalIdCardNational", "plsAgreeAuthorized", "finishConfirmInfo", "entOpenMultipleBusiniss", "contactSsqDeal", "signContract", "faceVerifyFailure", "reFaceVerifyAuth", "threeTimesFailure", "faceVerifySucess", "chooseOtherAuthMethod", "plsContinueOnPc", "accountAppealSuccess", "faceCompareSuccess", "plsUseWechatScan", "wechatCannotScanFace", "plsUploadClearIdCardImg", "uploadImgMap", "tip1", "tip2", "tip3", "tip4", "plsSubmitIdCard", "noNameNoSupportEntRemit", "check<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewCertificate", "continueAuthNewEnt", "continueBuyPackage", "needSubmitBasicInfo", "youFinishEntAuth", "loginSsqWebToEntAuth", "entAuthCertificate", "youFinishEntAuthTip", "backToHome", "youNeedMoreOperate", "goPc", "addEntMemberStepMap", "title", "step1", "step2", "step3", "step4", "step5", "addEntMember", "addSealStepMap", "step6", "step7", "step8", "addSeal", "waitApporve", "submitAuthMaterial", "auth<PERSON><PERSON><PERSON>", "plsSubmitBeforeTip", "oneDayFinishApprove", "ent<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseInfoInvalid", "missParams", "illegalLink", "cookieNotEnabe", "truthSubmitOperatorIdCard", "abandonAttorneyAuth", "modifyCertiInfo", "modifyAttorneyInfo", "plsUploadBusinessLicense", "plsUploadLegalCerti", "plsUploadOperatorCerti", "legalIdCardSubmit", "serviceAttorneryAuth", "accountAppealMap", "entApeal", "apealSuccess", "comName", "ener6Digtal", "beMainManagerTip", "mainAccount", "continueSign", "continueConfirm", "plsEnterComName", "plsEnterCorrentComName", "sendSuccess", "plsEnterCorrectCode", "faceInitLoading", "wxFaceVersionTip", "wxIosFaceVersionTip", "wxAndroidVersionTip", "faceInitFailure", "entAuthCertificateTip", "idCardHandHeld", "faceAuth", "noMainland<PERSON>uth", "entAuthTip", "signIntro", "companySet"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/entAuth/entAuth-zh.js"], "sourcesContent": ["export default {\n    submitBaiscInfo: '提交基本信息',\n    firstStep: '第一步',\n    secondStep: '第二步',\n    thirdStep: '第三步',\n    nextStep: '下一步',\n    back: '返回',\n    submit: '提交',\n    confirm: '确定',\n    affirm: '确认',\n    cancel: '取消',\n    tip: '提示',\n    account: '账号',\n    view: '查看',\n    iSee: '我知道了',\n    submitAttorney: '提交授权书',\n    plsDownloadSSQAttorneyTip: '请下载《上上签企业服务授权书》，加盖企业公章后拍照上传。',\n    ssqAttorney: '上上签企业服务授权书',\n    downloadAttorney: '请点击下载授权书',\n    imgSupportType: '支持png、jpeg、jpg、bmp格式且大小不超过10M。',\n    imgNoWatermarkTip: '仅支持无水印或”仅用于上上签实名认证“水印字样的照片',\n    confirmSubmit: '确认提交',\n    backToPreviousPage: '返回上一页',\n    uploadImgBeforeSubmit: '请上传照片后再提交',\n    phoneNumBuyTip: '请确保该手机号是在运营商处实名购买，否则将无法通过上上签实名验证。',\n    operatorPhoneVerify: '经办人手机号校验',\n    operatorName: '经办人姓名',\n    operatorIdNum: '经办人身份证号',\n    modifyOperator: '修改经办人',\n    changeIdCard: '更换证件',\n    operatorPassPhoneVerifyTip: ' 经办人在 {time} 已通过手机号校验，校验结果允许被复用。',\n    operatorPhoneNum: '经办人手机号',\n    verifyCode: '验证码',\n    tryFaceVerify: '试试刷脸校验',\n    afterPhoneVerifyTip: '手机号校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',\n    operatorFaceVerify: '经办人刷脸校验',\n    operatorPassFaceVerifyTip: ' 经办人在 {time} 已通过刷脸校验，校验结果允许被复用。',\n    alipayFaceVerify: '支付宝扫一扫刷脸认证',\n    tryPhoneVerify: '试试手机号校验',\n    scanFaceNow: '立即刷脸',\n    afterFaceVerifyTip: '刷脸校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',\n    plsEnterRightPhoneNum: '请先填写正确的手机号',\n    plsGetVerifyCodeFirst: '请先获取验证码',\n    agreeSsqProtectMethod: '我同意上上签对我提交的个人身份信息的保护方法',\n    ssqHowToProtectInfo: '《上上签如何保护您的个人信息》',\n    noEntNameTip: '由于贵公司没有企业名称，不支持授权书认证',\n    legalName: '法定代表人姓名',\n    legalIdCardNum: '法定代表人身份证号',\n    uploadIdCard: '上传身份证',\n    legalPassFaceVerifyTip: '法定代表人在 {time} 已通过刷脸认证，认证结果允许被复用。',\n    plsEnterLegalIdCardNum: '请先输入法定代表人身份证号',\n    plsUploadLegalIdCardImg: '请先上传法定代表人身份证',\n    pageExpiredTip: '当前页面已过期，请重新刷新页面',\n    faceFunctionNotEnabled: '刷脸服务暂时无法使用，请稍后再试',\n    plsSubmitLegalIdCard: '请提交法定代表人的身份证',\n    legalPassPhoneVerifyTip: '法定代表人在 {time} 已通过手机号认证，认证结果允许被复用。',\n    legalPhoneNum: '法定代表人手机号',\n    noPublicAccount: '没有对公账户？',\n    useSpecialMethod: '我是没有公户的分公司，使用特殊通道',\n    ssqRemitMoneyTip: '上上签将向下方对公账户汇入一笔钱（少于0.99元），您回填打款金额即可通过企业认证。',\n    accountName: '户名',\n    bank: '银行',\n    accountBank: '开户行',\n    accountBankName: '开户行名称',\n    bankAccount: '银行账户',\n    plsInputBankNameTip: '请输入银行名称，无需写分行或支行，如“中国银行”',\n    locationOfBank: '开户行所在地',\n    plsSelect: '请选择',\n    province: '省',\n    city: '市',\n    cityCounty: '市/县',\n    nameOfBank: '开户支行名称',\n    plsInputZhiBankName: '填写开户支行名称，如“杭州银行文创支行”',\n    canNotFindBank: '找不到对应银行？',\n    clickChangeReversePay: '点击页面下方“切换打款方式”，进入反向打款。',\n    other: '其他',\n    otherZhiBank: '开户支行名称(如支行名称选择\\'其他\\'请填写)',\n    bankAccount1: '银行账号',\n    plsInputBankAccount: '请输入银行账号',\n    remitNotSuccess: '汇款不成功？',\n    switchPayMethod: '切换打款方式',\n    whySubmitBankInfo: '为什么要提交银行卡信息',\n    submitBankInfoExplan: '银行卡信息认证是企业实名认证的一个验证环节，上上签会向该银行卡汇入一笔验证资金，不会从您的账户扣款。',\n    plsSelectBank: '请从下拉列表中选择银行',\n    plsSelectArea: '请从下拉列表中选择省,市/县',\n    plsInputNameOfAccountBank: '请输入开户行名称',\n    plsInputCorrectBankInfo: '请填写正确的对公银行信息',\n    plsInputFullBankName: '请填写开户行全称（包含支行名称）',\n    area: '地区',\n    contactCustomerService: '联系客服',\n    beiJing: '北京市',\n    dongCheng: '东城区',\n    fillJointBankNum: '该账户需要填写开户银行联行号才能汇款',\n    bankType: {\n        type1: '银行',\n        type2: '信用社联合社',\n        type3: '信用合作社',\n        type4: '农联社',\n    },\n    accountBankArea: '开户行所在地',\n    changeRemitMethod: '换种汇款方式',\n    canNotRemitAuth: '无法向您的企业对公账户发起打款认证，请选择其他认证方式',\n    bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',\n    faceVerify: '刷脸校验',\n    havePublicAccount: '我有对公账户',\n    ensureInfoSafeTip: '为保障企业信息安全、不被他人盗用，请先完成法人刷脸，然后再作打款认证',\n    submitEntBankInfo: '提交企业开户信息',\n    fillBackAmout: '填写回填金额',\n    legalPerson: '法人',\n    operatorMange: '经办人',\n    unionNum: '联行号',\n    getUnionNumTip: '请向贵公司/单位的财务沟通获取联行号，或根据银行支行信息',\n    uinonNumSearchOnline: '联行号在线查询',\n    remitProcessMap: {\n        submit: '打款申请已提交',\n        accept: '打款已受理',\n        success: '打款已成功',\n        receiveWait: '支付请求已受理,等待银行返回结果，请耐心等待',\n        successAttention: '打款已成功，请仔细核对账户收支明细。请注意：',\n        remarkTip: '汇款备注为：款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',\n        thirdChannel: '上上签通过第三方支付通道汇款,汇款通道为:',\n        remitNotSsq: '上上签通过第三方支付通道汇款，汇款方户名并非上上签',\n        remitPartySsq: '汇款方为“杭州尚尚签网络科技有限公司”',\n    },\n    close: '关闭',\n    name: '姓名',\n    idCardNum: '身份证号',\n    reversePayMap: {\n        remitTip1: '向上上签银行对公账户汇入0.01元，再勾选以下选项并点击“确定”，即可通过企业认证。',\n        iAgree: '我知情并同意：',\n        remitAuthUse: '向上上签指定的账户汇入的0.01元，如果认证通过，将用于购买1份对公合同；如果认证失败，也不能要求上上签退款。',\n        authFailure: '不满足以下要求会导致认证失败：',\n        authFailureReason1: '（1）汇款方为当前法人姓名：',\n        authFailureReason2: '（2）汇款方必须使用自己名下的账户进行汇款',\n        authFailureReason3: '（1）汇款方为当前企业名称：',\n        authFailureReason4: '（2）汇款方必须使用对公账户进行汇款',\n        plsInputBankName: '请输入银行账户名称',\n        authFailureReason5: '（3）汇款方向接收方汇出的单笔金额为0.01元人民币，不能大于0.01元或使用其他币种',\n        authFailureReason6: '（4）汇款日期，请确保在 {date} 后汇入',\n        authFailureReason7: '（5）接收方银行账户信息如下，不能汇入其他账户中：',\n        authFailureReason8: '（6）汇款方向接收方汇款时，必须且仅备注该验证码：',\n        remitDelay: '汇款到账可能有延迟，请耐心等待。',\n        failureReason: '认证实名失败的可能原因：',\n        wait30min: '因银行系统原因，您可在汇款成功30分钟后查询结果',\n        queryProgress: '查询进度',\n        inputRemitBankName: '请填入付款方银行户名',\n        queryFailureTip: '查询失败，请稍后重试...',\n        remitAuthSuccess: '已通过打款认证，汇款资金已用于购买1份对公合同。',\n    },\n    hourMinSec: '{hour}时{min}分{sec}秒',\n    minSec: '{min}分{sec}秒',\n    sec: '{sec}秒',\n    ssqRemitTip: '上上签已向贵账户提交一笔1元以下的汇款，1-2个工作日内到账，请于到账后在此确认到账金额。',\n    inputRightRemitAmout: '您需要查询账户的收支明细，正确输入这笔金额才能通过认证。',\n    notGetRemit: '如果您在账户上没有查到汇款金额，点此',\n    queryRemitProgress: '查一查打款进度',\n    inputWrongAccount: '账号填错了？',\n    remitNote: '汇款备注为：',\n    ssqApplyCaTip: '款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',\n    remitAmout: '汇款金额',\n    yuan: '元',\n    receiveAmout: '到账金额为0.01-0.99元之间',\n    maxRemitTimeTip: '最多只能打款四次哦！是否确定要重新提交填写账号？',\n    remitFailure: '打款未成功，即将返回企业打款页面，需要重新申请打款',\n    plsInputRemitAmout: '请输入0.01-0.99元之间的金额',\n    reSubmitBankInfo: '您需要重新提交银行卡信息',\n    amoutError: '金额错误',\n    reSubmit: '重新提交',\n    amoutInvalid: '金额失效',\n    authReject: '实名材料已被驳回，请重新提交',\n    authCertificate: '授权书认证',\n    entPayAuth: '企业打款认证',\n    legalPhoneAuth: '法定代表人手机号认证',\n    legalFaceAuth: '法定代表人刷脸认证',\n    sender: '发件人',\n    requireYouThrough: '{name}要求您通过{type}',\n    selectOneAuthMethod: '请选择以下任意一种实名方式：',\n    entCertificate: '企业证件',\n    personCertificate: '个人证件',\n    iAmLegal: '我是法定代表人',\n    iAmNotLegal: '我不是法定代表人，我是企业经办人',\n    receiveSsqPhone: '我接受上上签电话回访',\n    plsAgreeSsqPhone: '请先同意接受上上签电话回访',\n    submitInfoError: '您提交的个人证件的姓名或身份证号有误，请重新核实。',\n    submitIndividualEntAuth: '您正作为个体工商户提交企业认证，您的营业执照上无企业名称',\n    submitIndividualCorrectIdCard: '请如实提交法定代表人个人身份证',\n    entPersonInfoError: '您提交的企业证件信息有误/个人证件信息有误，请核实后重新提交。',\n    noEntName: '没有企业名称',\n    businessLicenseBlank: '营业执照的企业名称处为：空白',\n    addressName: '/地址/法定代表人姓名',\n    plsClick: '请点击',\n    haveEntName: '有企业名称，要求客服人工审核，',\n    checkFollowInfoTip: '核对以下信息并点击“确定”，您可以继续提交其他认证材料。企业信息转由上上签人工核对，如果信息不正确将驳回您提交的全部资料。信息核对需要约1个工作日。',\n    entName: '企业名称',\n    unifySocialCode: '统一社会信用代码',\n    uploadColorImgTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。企业信息将用于申请数字证书。',\n    businessLicense: '营业执照',\n    uploadBusinessLicenseTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照',\n    imgLimitTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或“仅用于上上签实名认证”水印字样的图片',\n    autoRecognizedTip: '以下信息自动识别，需仔细核对，如识别有误，请修正。',\n    iNoEntName: '我没有企业名称',\n    clickUseSpecialMethod: '点击使用特殊通道',\n    socialCodeBuisnessNum: '统一社会信用代码/工商注册号',\n    plsSubmitCorrectLegalName: '请提交正确的法定代表人姓名',\n    plsSubmitBusinessLicense: '请先提交营业执照',\n    noEntUploadBusinessTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。企业信息将用于申请数字证书。',\n    imgRequireTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。',\n    noEntNameUse: '没有企业名称的个体工商户须使用',\n    legalPlusCode: '法定代表人姓名加上统一社会信用',\n    codeAsEntName: '代码作为企业名称',\n    ssq: '上上签',\n    entAuth: '企业认证',\n    eleContractServiceBy: '电子签约服务由',\n    spericalProvide: '提供',\n    redirectWait: '正在跳转，请稍后',\n    businessLicenseImg: '营业执照照片',\n    idCardNational: '身份证国徽面',\n    idCardFace: '身份证人像面',\n    dueToSignRequire: '因合同签订需要',\n    authorizeSomeone: '我授权{name}获得以下信息：',\n    authoizedInfo: '我的账号基本信息(账号和名称)和企业基本信息(企业名称、统一社会信用代码或注册号、法定代表人姓名)',\n    iSubmitInfoToSsq: '我在上上签平台提交的',\n    authMaterials: '认证材料',\n    sureInfo: '确定无误',\n    modifyInfo: '修改信息',\n    additionalInfo: '补充信息',\n    ssqNotifyMethod: '我在上上签平台用作通知方式的',\n    phoneNum: '手机号',\n    email: '邮箱',\n    submitInfoPlsVerify: '已为您提交企业基本信息，请核实',\n    operatorIdCardFaceImg: '经办人身份证人像面',\n    operatorIdCardNational: '经办人身份证国徽面',\n    legalIdCardFaceImg: '法定代表人身份证人像面',\n    legalIdCardNational: '法定代表人身份证国徽面',\n    plsAgreeAuthorized: '请先同意授权',\n    finishConfirmInfo: '您已完成信息确认，请继续企业认证流程。',\n    entOpenMultipleBusiniss: '企业开通了多业务线，操作无法继续',\n    contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理',\n    signContract: '签署合同',\n    faceVerifyFailure: '刷脸认证失败',\n    reFaceVerifyAuth: '重新刷脸认证',\n    threeTimesFailure: '非常抱歉，您今天已连续3次刷脸比对失败，请选择其他认证方式',\n    faceVerifySucess: '刷脸认证成功',\n    chooseOtherAuthMethod: '选择其他认证方式',\n    plsContinueOnPc: '请在电脑网页端继续操作',\n    accountAppealSuccess: '账号申诉成功',\n    faceCompareSuccess: '刷脸对比成功',\n    plsUseWechatScan: '请使用微信浏览器扫描二维码',\n    wechatCannotScanFace: '因微信策略变更，当前无法刷脸',\n    plsUploadClearIdCardImg: '请上传清晰的身份证照片，系统将自动识别证件信息。照片仅限jpeg、jpg、png格式且大小不超过10M。',\n    uploadImgMap: {\n        tip1: '请上传清晰的身份证照片，',\n        tip2: '系统将自动识别证件信息。',\n        tip3: '照片仅限jpeg、jpg、png格式且大小不超过10M。',\n        tip4: '仅支持无水印或“仅用于上上签实名认证”水印字样的图片',\n    },\n    plsSubmitIdCard: '请先提交身份证',\n    noNameNoSupportEntRemit: '由于贵公司没有企业名称，不支持企业打款认证',\n    checkMoreVerison: '查看更多版本',\n    viewCertificate: '查看证书',\n    continueAuthNewEnt: '继续认证新企业',\n    continueBuyPackage: '继续购买套餐',\n    needSubmitBasicInfo: '仅要求您提交企业基本信息',\n    youFinishEntAuth: '您已完成企业认证',\n    loginSsqWebToEntAuth: '登录上上签官网完成完整的企业认证流程，您可以在上上签平台获得更高权限',\n    entAuthCertificate: '企业实名证书',\n    youFinishEntAuthTip: '您已完成企业实名认证',\n    backToHome: '返回首页',\n    youNeedMoreOperate: '您还需{operate}完成以下操作：',\n    goPc: '前往PC端',\n    addEntMemberStepMap: {\n        title: '添加企业成员步骤：',\n        step1: '1、进入企业控制台',\n        step2: '2、打开成员管理',\n        step3: '3、点击添加新成员',\n        step4: '4、输入账号、姓名，选择角色',\n        step5: '5、点击保存，即可添加新成员',\n    },\n    addEntMember: '添加企业成员',\n    addSealStepMap: {\n        title: '添加印章步骤：',\n        step1: '1、进入企业控制台',\n        step2: '2、打开印章列表',\n        step3: '3、点击新增印章',\n        step4: '4、输入印章名称，上传印章图案或生产电子签章',\n        step5: '5、点击保存，新增印章',\n        step6: '6、点击添加持有人',\n        step7: '7、选择企业成员',\n        step8: '8、点击确定，即可添加印章持有人',\n    },\n    addSeal: '添加印章',\n    waitApporve: '等待审核',\n    submitAuthMaterial: '提交认证材料',\n    authFinish: '认证完成',\n    plsSubmitBeforeTip: '请在{date}前完成所有材料提交，否则基本信息将失效。',\n    oneDayFinishApprove: '客服将在一个工作日内完成审核，请耐心等待',\n    entAuthFinish: '企业已实名完成',\n    baseInfoInvalid: '基本信息已失效，请重新提交',\n    missParams: '缺少参数!',\n    illegalLink: '非法链接',\n    cookieNotEnabe: '无法读写cookie，是否开启了无痕／隐身模式或其他禁用cookie的操作',\n    truthSubmitOperatorIdCard: '请如实提交经办人个人身份证',\n    abandonAttorneyAuth: '放弃授权书认证，',\n    modifyCertiInfo: '修改证件资料',\n    modifyAttorneyInfo: '修改授权书资料',\n    plsUploadBusinessLicense: '请上传营业执照！',\n    plsUploadLegalCerti: '请上传法人证件！',\n    plsUploadOperatorCerti: '请上传经办人证件！',\n    legalIdCardSubmit: '法定代表人身份证提交',\n    serviceAttorneryAuth: '服务授权书认证',\n    accountAppealMap: {\n        entApeal: '企业账号申诉',\n        apealSuccess: '申诉完成',\n        comName: '公司名称:',\n        account: '账号:',\n        verifyCode: '验证码:',\n        ener6Digtal: '请填写6位数字',\n        beMainManagerTip: '企业账号申请成功后，您将成为企业主管理员',\n        mainAccount: '主管理员账号',\n        continueSign: '继续签署',\n        continueConfirm: '继续认证',\n        plsEnterComName: '请先填写公司名称',\n        plsEnterCorrentComName: '请填写正确的公司名称',\n        sendSuccess: '发送成功！',\n        plsEnterCorrectCode: '请填写正确的验证码',\n    },\n    faceInitLoading: '初始化刷脸标签，请稍后',\n    wxFaceVersionTip: '刷脸需要微信版本7.0.12及以上，请先升级',\n    wxIosFaceVersionTip: '刷脸需要ios版本10.3及以上，请先升级',\n    wxAndroidVersionTip: '刷脸需要android版本5.0及以上，请先升级',\n    faceInitFailure: '刷脸标签初始化失败: ',\n    entAuthCertificateTip: '企业实名认证证书',\n    idCardHandHeld: '手持身份证认证',\n    faceAuth: '刷脸认证',\n    noMainlandAuth: '非大陆认证',\n    entAuthTip: '企业实名认证',\n    signIntro: '签署引导',\n    companySet: '企业设置',\n};\n"], "mappings": "AAAA,eAAe;EACXA,eAAe,EAAE,QAAQ;EACzBC,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE,IAAI;EACTC,OAAO,EAAE,IAAI;EACbC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,MAAM;EACZC,cAAc,EAAE,OAAO;EACvBC,yBAAyB,EAAE,8BAA8B;EACzDC,WAAW,EAAE,YAAY;EACzBC,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE,gCAAgC;EAChDC,iBAAiB,EAAE,4BAA4B;EAC/CC,aAAa,EAAE,MAAM;EACrBC,kBAAkB,EAAE,OAAO;EAC3BC,qBAAqB,EAAE,WAAW;EAClCC,cAAc,EAAE,mCAAmC;EACnDC,mBAAmB,EAAE,UAAU;EAC/BC,YAAY,EAAE,OAAO;EACrBC,aAAa,EAAE,SAAS;EACxBC,cAAc,EAAE,OAAO;EACvBC,YAAY,EAAE,MAAM;EACpBC,0BAA0B,EAAE,kCAAkC;EAC9DC,gBAAgB,EAAE,QAAQ;EAC1BC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE,QAAQ;EACvBC,mBAAmB,EAAE,mCAAmC;EACxDC,kBAAkB,EAAE,SAAS;EAC7BC,yBAAyB,EAAE,iCAAiC;EAC5DC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE,SAAS;EACzBC,WAAW,EAAE,MAAM;EACnBC,kBAAkB,EAAE,kCAAkC;EACtDC,qBAAqB,EAAE,YAAY;EACnCC,qBAAqB,EAAE,SAAS;EAChCC,qBAAqB,EAAE,wBAAwB;EAC/CC,mBAAmB,EAAE,iBAAiB;EACtCC,YAAY,EAAE,sBAAsB;EACpCC,SAAS,EAAE,SAAS;EACpBC,cAAc,EAAE,WAAW;EAC3BC,YAAY,EAAE,OAAO;EACrBC,sBAAsB,EAAE,kCAAkC;EAC1DC,sBAAsB,EAAE,eAAe;EACvCC,uBAAuB,EAAE,cAAc;EACvCC,cAAc,EAAE,iBAAiB;EACjCC,sBAAsB,EAAE,kBAAkB;EAC1CC,oBAAoB,EAAE,cAAc;EACpCC,uBAAuB,EAAE,mCAAmC;EAC5DC,aAAa,EAAE,UAAU;EACzBC,eAAe,EAAE,SAAS;EAC1BC,gBAAgB,EAAE,mBAAmB;EACrCC,gBAAgB,EAAE,4CAA4C;EAC9DC,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE,KAAK;EAClBC,eAAe,EAAE,OAAO;EACxBC,WAAW,EAAE,MAAM;EACnBC,mBAAmB,EAAE,0BAA0B;EAC/CC,cAAc,EAAE,QAAQ;EACxBC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,GAAG;EACbC,IAAI,EAAE,GAAG;EACTC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,QAAQ;EACpBC,mBAAmB,EAAE,sBAAsB;EAC3CC,cAAc,EAAE,UAAU;EAC1BC,qBAAqB,EAAE,wBAAwB;EAC/CC,KAAK,EAAE,IAAI;EACXC,YAAY,EAAE,0BAA0B;EACxCC,YAAY,EAAE,MAAM;EACpBC,mBAAmB,EAAE,SAAS;EAC9BC,eAAe,EAAE,QAAQ;EACzBC,eAAe,EAAE,QAAQ;EACzBC,iBAAiB,EAAE,aAAa;EAChCC,oBAAoB,EAAE,oDAAoD;EAC1EC,aAAa,EAAE,aAAa;EAC5BC,aAAa,EAAE,gBAAgB;EAC/BC,yBAAyB,EAAE,UAAU;EACrCC,uBAAuB,EAAE,cAAc;EACvCC,oBAAoB,EAAE,kBAAkB;EACxCC,IAAI,EAAE,IAAI;EACVC,sBAAsB,EAAE,MAAM;EAC9BC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,gBAAgB,EAAE,oBAAoB;EACtCC,QAAQ,EAAE;IACNC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACX,CAAC;EACDC,eAAe,EAAE,QAAQ;EACzBC,iBAAiB,EAAE,QAAQ;EAC3BC,eAAe,EAAE,6BAA6B;EAC9CC,kBAAkB,EAAE,6JAA6J;EACjLC,UAAU,EAAE,MAAM;EAClBC,iBAAiB,EAAE,QAAQ;EAC3BC,iBAAiB,EAAE,oCAAoC;EACvDC,iBAAiB,EAAE,UAAU;EAC7BC,aAAa,EAAE,QAAQ;EACvBC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE,8BAA8B;EAC9CC,oBAAoB,EAAE,SAAS;EAC/BC,eAAe,EAAE;IACbzG,MAAM,EAAE,SAAS;IACjB0G,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,wBAAwB;IACrCC,gBAAgB,EAAE,wBAAwB;IAC1CC,SAAS,EAAE,0CAA0C;IACrDC,YAAY,EAAE,uBAAuB;IACrCC,WAAW,EAAE,2BAA2B;IACxCC,aAAa,EAAE;EACnB,CAAC;EACDC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,MAAM;EACjBC,aAAa,EAAE;IACXC,SAAS,EAAE,4CAA4C;IACvDC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,yDAAyD;IACvEC,WAAW,EAAE,iBAAiB;IAC9BC,kBAAkB,EAAE,gBAAgB;IACpCC,kBAAkB,EAAE,uBAAuB;IAC3CC,kBAAkB,EAAE,gBAAgB;IACpCC,kBAAkB,EAAE,oBAAoB;IACxCC,gBAAgB,EAAE,WAAW;IAC7BC,kBAAkB,EAAE,6CAA6C;IACjEC,kBAAkB,EAAE,yBAAyB;IAC7CC,kBAAkB,EAAE,2BAA2B;IAC/CC,kBAAkB,EAAE,2BAA2B;IAC/CC,UAAU,EAAE,kBAAkB;IAC9BC,aAAa,EAAE,cAAc;IAC7BC,SAAS,EAAE,0BAA0B;IACrCC,aAAa,EAAE,MAAM;IACrBC,kBAAkB,EAAE,YAAY;IAChCC,eAAe,EAAE,eAAe;IAChCC,gBAAgB,EAAE;EACtB,CAAC;EACDC,UAAU,EAAE,qBAAqB;EACjCC,MAAM,EAAE,cAAc;EACtBC,GAAG,EAAE,QAAQ;EACbC,WAAW,EAAE,+CAA+C;EAC5DC,oBAAoB,EAAE,8BAA8B;EACpDC,WAAW,EAAE,oBAAoB;EACjCC,kBAAkB,EAAE,SAAS;EAC7BC,iBAAiB,EAAE,QAAQ;EAC3BC,SAAS,EAAE,QAAQ;EACnBC,aAAa,EAAE,oCAAoC;EACnDC,UAAU,EAAE,MAAM;EAClBC,IAAI,EAAE,GAAG;EACTC,YAAY,EAAE,mBAAmB;EACjCC,eAAe,EAAE,0BAA0B;EAC3CC,YAAY,EAAE,2BAA2B;EACzCC,kBAAkB,EAAE,oBAAoB;EACxCC,gBAAgB,EAAE,cAAc;EAChCC,UAAU,EAAE,MAAM;EAClBC,QAAQ,EAAE,MAAM;EAChBC,YAAY,EAAE,MAAM;EACpBC,UAAU,EAAE,gBAAgB;EAC5BC,eAAe,EAAE,OAAO;EACxBC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,YAAY;EAC5BC,aAAa,EAAE,WAAW;EAC1BC,MAAM,EAAE,KAAK;EACbC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,gBAAgB;EACrCC,cAAc,EAAE,MAAM;EACtBC,iBAAiB,EAAE,MAAM;EACzBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,kBAAkB;EAC/BC,eAAe,EAAE,YAAY;EAC7BC,gBAAgB,EAAE,eAAe;EACjCC,eAAe,EAAE,2BAA2B;EAC5CC,uBAAuB,EAAE,8BAA8B;EACvDC,6BAA6B,EAAE,iBAAiB;EAChDC,kBAAkB,EAAE,iCAAiC;EACrDC,SAAS,EAAE,QAAQ;EACnBC,oBAAoB,EAAE,gBAAgB;EACtCC,WAAW,EAAE,aAAa;EAC1BC,QAAQ,EAAE,KAAK;EACfC,WAAW,EAAE,iBAAiB;EAC9BC,kBAAkB,EAAE,4EAA4E;EAChGC,OAAO,EAAE,MAAM;EACfC,eAAe,EAAE,UAAU;EAC3BC,iBAAiB,EAAE,6EAA6E;EAChGC,eAAe,EAAE,MAAM;EACvBC,wBAAwB,EAAE,kCAAkC;EAC5DC,WAAW,EAAE,wDAAwD;EACrEC,iBAAiB,EAAE,2BAA2B;EAC9CC,UAAU,EAAE,SAAS;EACrBC,qBAAqB,EAAE,UAAU;EACjCC,qBAAqB,EAAE,gBAAgB;EACvCC,yBAAyB,EAAE,eAAe;EAC1CC,wBAAwB,EAAE,UAAU;EACpCC,sBAAsB,EAAE,yGAAyG;EACjIC,aAAa,EAAE,0DAA0D;EACzEC,YAAY,EAAE,iBAAiB;EAC/BC,aAAa,EAAE,iBAAiB;EAChCC,aAAa,EAAE,UAAU;EACzBC,GAAG,EAAE,KAAK;EACVC,OAAO,EAAE,MAAM;EACfC,oBAAoB,EAAE,SAAS;EAC/BC,eAAe,EAAE,IAAI;EACrBC,YAAY,EAAE,UAAU;EACxBC,kBAAkB,EAAE,QAAQ;EAC5BC,cAAc,EAAE,QAAQ;EACxBC,UAAU,EAAE,QAAQ;EACpBC,gBAAgB,EAAE,SAAS;EAC3BC,gBAAgB,EAAE,kBAAkB;EACpCC,aAAa,EAAE,mDAAmD;EAClEC,gBAAgB,EAAE,YAAY;EAC9BC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,MAAM;EAChBC,UAAU,EAAE,MAAM;EAClBC,cAAc,EAAE,MAAM;EACtBC,eAAe,EAAE,gBAAgB;EACjCC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,IAAI;EACXC,mBAAmB,EAAE,iBAAiB;EACtCC,qBAAqB,EAAE,WAAW;EAClCC,sBAAsB,EAAE,WAAW;EACnCC,kBAAkB,EAAE,aAAa;EACjCC,mBAAmB,EAAE,aAAa;EAClCC,kBAAkB,EAAE,QAAQ;EAC5BC,iBAAiB,EAAE,qBAAqB;EACxCC,uBAAuB,EAAE,kBAAkB;EAC3CC,cAAc,EAAE,yBAAyB;EACzCC,YAAY,EAAE,MAAM;EACpBC,iBAAiB,EAAE,QAAQ;EAC3BC,gBAAgB,EAAE,QAAQ;EAC1BC,iBAAiB,EAAE,+BAA+B;EAClDC,gBAAgB,EAAE,QAAQ;EAC1BC,qBAAqB,EAAE,UAAU;EACjCC,eAAe,EAAE,aAAa;EAC9BC,oBAAoB,EAAE,QAAQ;EAC9BC,kBAAkB,EAAE,QAAQ;EAC5BC,gBAAgB,EAAE,eAAe;EACjCC,oBAAoB,EAAE,gBAAgB;EACtCC,uBAAuB,EAAE,sDAAsD;EAC/EC,YAAY,EAAE;IACVC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,8BAA8B;IACpCC,IAAI,EAAE;EACV,CAAC;EACDC,eAAe,EAAE,SAAS;EAC1BC,uBAAuB,EAAE,uBAAuB;EAChDC,gBAAgB,EAAE,QAAQ;EAC1BC,eAAe,EAAE,MAAM;EACvBC,kBAAkB,EAAE,SAAS;EAC7BC,kBAAkB,EAAE,QAAQ;EAC5BC,mBAAmB,EAAE,cAAc;EACnCC,gBAAgB,EAAE,UAAU;EAC5BC,oBAAoB,EAAE,oCAAoC;EAC1DC,kBAAkB,EAAE,QAAQ;EAC5BC,mBAAmB,EAAE,YAAY;EACjCC,UAAU,EAAE,MAAM;EAClBC,kBAAkB,EAAE,qBAAqB;EACzCC,IAAI,EAAE,OAAO;EACbC,mBAAmB,EAAE;IACjBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE;EACX,CAAC;EACDC,YAAY,EAAE,QAAQ;EACtBC,cAAc,EAAE;IACZP,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,aAAa;IACpBG,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACX,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE,MAAM;EACnBC,kBAAkB,EAAE,QAAQ;EAC5BC,UAAU,EAAE,MAAM;EAClBC,kBAAkB,EAAE,8BAA8B;EAClDC,mBAAmB,EAAE,sBAAsB;EAC3CC,aAAa,EAAE,SAAS;EACxBC,eAAe,EAAE,eAAe;EAChCC,UAAU,EAAE,OAAO;EACnBC,WAAW,EAAE,MAAM;EACnBC,cAAc,EAAE,uCAAuC;EACvDC,yBAAyB,EAAE,eAAe;EAC1CC,mBAAmB,EAAE,UAAU;EAC/BC,eAAe,EAAE,QAAQ;EACzBC,kBAAkB,EAAE,SAAS;EAC7BC,wBAAwB,EAAE,UAAU;EACpCC,mBAAmB,EAAE,UAAU;EAC/BC,sBAAsB,EAAE,WAAW;EACnCC,iBAAiB,EAAE,YAAY;EAC/BC,oBAAoB,EAAE,SAAS;EAC/BC,gBAAgB,EAAE;IACdC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,OAAO;IAChBhS,OAAO,EAAE,KAAK;IACdoB,UAAU,EAAE,MAAM;IAClB6Q,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE,sBAAsB;IACxCC,WAAW,EAAE,QAAQ;IACrBC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,MAAM;IACvBC,eAAe,EAAE,UAAU;IAC3BC,sBAAsB,EAAE,YAAY;IACpCC,WAAW,EAAE,OAAO;IACpBC,mBAAmB,EAAE;EACzB,CAAC;EACDC,eAAe,EAAE,aAAa;EAC9BC,gBAAgB,EAAE,wBAAwB;EAC1CC,mBAAmB,EAAE,uBAAuB;EAC5CC,mBAAmB,EAAE,0BAA0B;EAC/CC,eAAe,EAAE,aAAa;EAC9BC,qBAAqB,EAAE,UAAU;EACjCC,cAAc,EAAE,SAAS;EACzBC,QAAQ,EAAE,MAAM;EAChBC,cAAc,EAAE,OAAO;EACvBC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,MAAM;EACjBC,UAAU,EAAE;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}