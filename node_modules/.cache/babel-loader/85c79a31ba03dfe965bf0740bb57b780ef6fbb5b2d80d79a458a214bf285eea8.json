{"ast": null, "code": "export default {\n  ssoLoginConfig: {\n    notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',\n    operationStep: {\n      one: '第一步 点击继续后，返回登录页面',\n      two: '第二步 输入密码，进入上上签平台',\n      three: '第三步 发送合同（或管理模板）'\n    },\n    continue: 'Continue',\n    cancel: 'Cancel',\n    tip: 'Tip'\n  },\n  sign: {\n    sealLabelsTip: 'You need to affix {sealLabelslen} seals on the contract. {personStr} will affix {otherSealLen} seal for you, and you will personally affix the remaining {mySealLen} seal. The seals required are displayed on the page. Please confirm if you wish to proceed.',\n    continue: 'Continue',\n    nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n    reselect: '重选',\n    approvalFeatures: {\n      dialogTitle: 'New Feature Introduction',\n      understand: 'I understand',\n      feature1: 'Sentence Highlighting Annotations',\n      feature2: 'Field Highlighting',\n      tip1: 'Click the button to highlight all \"template content fields\" in the contract to capture key information.',\n      tip2: 'Click the prompt button in the lower left corner to enable template content field highlighting.',\n      tip3: 'By highlighting, the content of the contract can be quickly located to fill in the fields, and the approval can be completed efficiently.',\n      tip4: 'After selecting a text field, click the annotation button to add annotation text. Once completed, click modify or delete. The content of the annotations can be viewed in the contract details page - company internal operation log.',\n      tip5: 'Step 1: Select the text field to be annotated and add the annotation.',\n      tip6: 'Step 2: Click to edit or delete the annotation.',\n      annotate: 'Annotations',\n      delete: 'Deletion',\n      edit: 'Modification',\n      operateTitle: 'Add Approval Annotations',\n      placeholder: 'No more than 255 words'\n    },\n    contractHighLight: {\n      dialogTitle: 'Contract highlighting reminder'\n    },\n    needRemark: '您还需要填写备注',\n    notNeedRemark: '您不需要填写备注',\n    switchToReceiver: 'You have been switched to {receiver}',\n    notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',\n    contractPartiesYouChoose: 'Contract parties you can choose:',\n    contractPartyFilled: 'Contract party that filled in by sender is:',\n    certifyOtherCompanies: 'Certify other companies',\n    youCanAlso: 'You can also:',\n    needVerification: 'You need real name verification to sign',\n    prompt: 'Hint',\n    submit: 'OK',\n    cancel: 'Cancel',\n    sign: 'Sign',\n    addSeal: 'Please login PC BestSign website to add seal',\n    noSealAvailable: 'Sorry, you don\\'t have a seal and no seal management rights. Please contact the administrator to assign a seal to you',\n    memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',\n    noticeAdminFoSeal: 'Send notification to the master administrator',\n    requestSomeone: 'Request someone else to verify',\n    requestOthersToContinue: 'Ask Administrator to fulfill',\n    requestOthersToContinueSucceed: 'Request sent successfully',\n    requestSomeoneList: 'Request the following personnel to complete the real name certification:',\n    electronicSeal: 'Electronic seal',\n    changeTheSeal: 'Don’t want to use this seal? Change the seal after real name verification',\n    goToVerify: 'Go to verify ',\n    noSealToChoose: 'No seal to choose, if you need managing seals, please get real name verification',\n    goToVerifyEnt: 'Go to verify enterprise',\n    digitalCertificateTip: 'BestSign is calling your digital certificate',\n    signDes: 'You\\'re in secure signing environment, feel free to sign!',\n    goVerify: 'Go verify ',\n    signAgain: 'Sign',\n    send: 'Send',\n    person: 'Natural person',\n    ent: 'Enterprise',\n    entName: 'Name of enterprise',\n    account: 'Account',\n    accountPH: 'Telephone or email',\n    approved: 'Approved',\n    signVerification: 'Sign',\n    cannotReview: 'Cannot review contract',\n    connectFail: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server.',\n    connectFailTip: '您可以尝试以下方法解决问题：',\n    connectFailTip1: '1、刷新页面。',\n    connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n    connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n    personalMaterials: 'Sender requests you to add more certification materials',\n    noSupportface: 'The contract initiator requests you to sign with face ID, non-mainland Chinese people do not support face ID signing, please contact the initiator to modify the signing requirements',\n    lackEntName: 'Please input name of enterprise',\n    errAccount: 'Please input correct telephone or email',\n    noticeAdmin: 'Apply',\n    signDone: 'Signing completed',\n    signDoneTip: 'You have signed this contract',\n    approveDone: 'Approval completed',\n    approveDoneTip: 'You have approved this contract',\n    completeSign: 'Please click \"Seal\" or \"Signature\" to complete the signing',\n    fillFirst: 'Please fill in the contract content in the input box first',\n    stillSignTip: 'After you sign this {alias}, there are still other signatories who may modify the {alias} and continue to sign it?',\n    signHighLightTip: 'A total of {count} {alias} contents can be added or modified',\n    riskDetails: 'Risk details',\n    noviewDifference: 'Because sender opened the parties can fill in the {alias} of fixed field function, other signed this {alias} may still change the initiator content specified in the {alias}, sign on the right version before the signing of this {alias} and the content of the difference between effective version for review, when you after signing the {alias}, signed as you agree to the other party to the {alias} content of fixed field increase or modify the content, And recognize the effective version of this {alias} signed by each signatory.\\n' + 'If you do not agree that other signatories can still change the fields of this {alias} after you sign, you may refuse to sign this {alias} and negotiate with the sender (that is, ask the initiator to turn off the function of \"Signatory fill in the fields\" to avoid the corresponding risks to you).',\n    highLightTip: 'These risky content will be \"highlighted\", please check carefully. Refresh the page to remove the highlighting effect.',\n    commonTip: 'Tip',\n    understand: 'I understand',\n    view: 'View',\n    start: 'Start',\n    nextStep: 'next',\n    help: 'Help',\n    faceFailed: 'Sorry, your face comparison failed',\n    dualFailed: 'Sorry, the dual-recording verification has failed. Please verify your information and try again.',\n    faceFailedtips: 'Tips',\n    qrcodeInvalid: 'Qrcode is invalid，please refresh',\n    faceFirstExceed: 'Face scan failed, Authentication code will be used in the next step for authentication.',\n    verifyTry: 'Verify the identity and try again',\n    faceLimit: 'Today\\'s face comparison has reached the line',\n    upSignReq: 'Please try again tomorrow or contact the contract initiator to modify the signing requirements',\n    reqFace: 'Sender requests you to do Face ID verification',\n    signAfterFace: 'After Face ID passes, you can complete the contract signing',\n    date: 'date',\n    chooseSeal: 'Choose seal',\n    seal: 'seal',\n    signature: 'Create signature ',\n    handwrite: 'Handwritten',\n    mysign: 'My signature',\n    approvePlace: 'Approval message, not necessary',\n    approvePlace_1: 'Approval message',\n    approvePlace_2: 'Optional, maximum 255 characters',\n    approveAgree: 'Approval result: Approved',\n    approveReject: 'Approval result: Reject',\n    signBy: 'Sign by ',\n    signByEnd: '',\n    sealBy: 'Seal by ',\n    sealByEnd: '',\n    coverBy: 'Cover by ',\n    applicant: 'Applicant:',\n    continueVeri: 'Go certify',\n    registerAndReal: 'Please register and real name',\n    goToResiter: 'Go to registration and certification',\n    sureToUse: 'Are you sure with ',\n    toSign: 'to sign?',\n    pleaseComplete: 'Please complete the signatue before confirming the ',\n    confirmSign: '',\n    admin: 'administrator',\n    contratAdmin: 'Please contact the administrator to ',\n    addToEnt: 'add your account as a business',\n    alreadyExists: 'On the BestSign already exists',\n    sendMsg: 'BestSign will send the following to the administrator as a text message:',\n    applyJoin: 'Apply to join ',\n    title: 'Title',\n    viewImg: 'View picture',\n    priLetter: 'Message',\n    priLetterFromSomeone: 'Message from {name}',\n    readLetter: 'OK',\n    approve: 'Approve',\n    disapprove: 'Reject',\n    refuseSign: 'Reject',\n    paperSign: '改用纸质签署',\n    refuseTip: 'Please choose the reason for rejection',\n    refuseReason: 'Fill in the reason for the refusal to help the other party understand your problem and speed up the contract process',\n    reasonWriteTip: 'Please fill in the reason for rejection',\n    refuseReasonOther: 'More reasons for rejection (optional) | More reasons for rejection (required)',\n    refuseConfirm: 'Reject',\n    refuseConfirmTip: 'You have refused to sign this contract for the reason \"{reason}\". Do you want to continue? After confirmation, you will not be able to sign this contract again.',\n    waitAndThink: 'Reconsider',\n    signValidationTitle: 'Sign Verification',\n    email: 'E-mail',\n    phoneNumber: 'Phone',\n    password: 'Password',\n    verificationCode: 'SMS Code',\n    mailVerificationCode: 'Verification code',\n    forgetPsw: 'Forget?',\n    if: ',',\n    forgetPassword: 'Forgot?',\n    rejectionVer: 'Verification',\n    msgTip: 'Can\\'t review the SMS messages? Try',\n    voiceVerCode: 'a voice call',\n    SMSVerCode: 'SMS verification code',\n    emailVerCode: 'E-mail verification code',\n    or: 'or',\n    SentSuccessfully: 'Send successfully!',\n    intervalTip: 'Please send again later',\n    signPsw: 'Sign Password',\n    signConfirmTip: {\n      1: 'Are you sure you want to sign this {contract}?',\n      2: 'Click the confirm button to sign the {contract} immediately',\n      confirm: 'Confirm'\n    },\n    useSignPsw: 'Use the password for verification',\n    setSignPsw: 'Set the signing password verification',\n    useVerCode: 'Use verification code',\n    inputSignPwdTip: 'Please enter the signing password',\n    inputVerifyCodeTip: 'Please enter verification code',\n    signSuc: 'Sign successfully',\n    refuseSuc: 'Refused',\n    approveSuc: 'Approval succeeded',\n    hdFile: 'View HD files',\n    otherOperations: 'Other operations',\n    reviewDetails: 'Approval details',\n    close: 'Close',\n    submitter: 'Approval initiator',\n    signatory: 'Signatory',\n    reviewSchedule: 'Approval schedule',\n    signByPc: 'Sign by {name}',\n    signPageDescription: 'Page {index}, {total} in total',\n    sealBySomeone: 'Seal by {name}',\n    signDate: 'Signing date',\n    download: 'Download',\n    signPage: 'Page: {page}',\n    signNow: 'Sign now',\n    sender: 'Sender',\n    signer: 'Signer',\n    startSignTime: 'Start signing time',\n    signDeadLine: 'Signing deadline',\n    authGuide: {\n      goToHome: 'Go to home',\n      tip_1: 'After the certification is completed, you can view and sign the contract.',\n      tip_2: 'Please use identity | to auth.',\n      tip_3: 'send a contract',\n      tip_4: 'Please contact the contract initiator | change recipient.',\n      tip_5: 'Your certified | unable to view contract',\n      new_tip_1: 'Based on the sender\\'s compliance requirements, you need to complete the following steps:',\n      new_tip_2: 'Based on the compliance requirements of the sender, you need to:',\n      new_tip_3: 'complete the following steps.',\n      new_tip_4: 'If you already have seal permission, step 2 will be automatically skipped for you',\n      entUserName: 'username:',\n      idNumberForVerify: 'IDCard:',\n      realNameAuth: 'Real-name authentication',\n      applySeal: 'Apply for the seal',\n      signContract: 'Sign contract'\n    },\n    switch: 'Switch',\n    rejectReasonList: {\n      // authReason: '不想/不会做实名认证',\n      signOperateReason: 'If you have questions about the signing operation/verification operation, further communication is needed',\n      termReason: 'I have doubts about the terms/content of the contract and need to communicate further',\n      explainReason: 'If you are not aware of the contract contents, please inform us in advance',\n      otherReason: 'Others (please fill in the reason)'\n    },\n    selectSignature: 'Select signature',\n    selectSigner: 'Choose signer',\n    pleaseScanToSign: 'Please scan and sign with Alipay or WeChat',\n    pleaseScanAliPay: 'Please use Alipay app to scan the QR code to sign',\n    pleaseScanWechat: 'Please use WeChat app to scan the QR code to sign',\n    requiredFaceSign: 'The sender of the contract asks you to scan your face to recognize and sign',\n    requiredDualSign: 'The contract sender requires you to complete dual-recording verification',\n    verCodeVerify: 'Verification code verification',\n    applyToSign: 'Apply to sign a contract',\n    autoRemindAfterApproval: '*After the approval is passed, the signing reminder will be automatically sent to the signatory',\n    cannotSignBeforeApproval: 'The approval has not been completed and cannot be signed temporarily',\n    finishSignatureBeforeSign: 'Please complete the stamp/signature before confirming the signature',\n    uploadFileOnRightSite: 'You still have attachments that have not been uploaded, please upload them in the right column first',\n    cannotApplySealNeedPay: 'This contract requires you to pay, and the application for seal is not supported',\n    cannotOtherSealReason: 'Face verification is required for this contract. Signature by others is not allowed',\n    unlimitedNotice: 'Unlimited use of the contract billing',\n    units: '{num}',\n    contractToPrivate: 'individual contracts',\n    contractToPublic: 'company contracts',\n    paySum: '共{sum}需要您支付',\n    payTotal: '共计{total}元.',\n    fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',\n    contactToRecharge: 'Please contact the main administrator to recharge.',\n    deductPublicNotice: 'Private contracts will be deducted when the number of available copies is insufficient.',\n    needSignerPay: 'The sender of the contract has set recipient payment, and designates you to pay for the contract.',\n    recharge: 'Recharge',\n    toSubmit: 'Submit',\n    appliedSeal: 'Application for seal has been submitted',\n    noSeal: 'No seal',\n    noSwitchSealNeedDistribute: 'Sorry, you do not currently have a seal that can be used, please contact the business owner administrator to add a seal and authorize',\n    viewApproveProcess: 'View the approval process',\n    approveProcess: 'Approval process',\n    noApproveContent: 'No approval note were submitted',\n    knew: 'I see',\n    noSwitchSealNeedAppend: 'There is no switchable stamp, please contact the administrator to add a stamp',\n    hadAutoSet: 'Automatically placed in {num} other places',\n    setThatSignature: 'by this signature',\n    setThatSeal: 'by this stamp',\n    applyThatSeal: 'applied by this stamp',\n    hasSetTip: 'Automatically placed at {index} other locations',\n    hasSetSealTip: 'The seal has been automatically placed in {index} other places',\n    hasSetSignatureTip: 'The signature has been automatically placed in {index} other places',\n    hasApplyForSealTip: 'The seal has been automatically applied for in {index} other places',\n    savedOnLeftSite: 'Saved to the signature bar on the left',\n    ridingSealMinLimit: 'The document has only one page and cannot be stamped with a seam seal',\n    ridingSealMaxLimit: 'More than 146 pages, stamping of seam seal is not supported',\n    ridingSealMinOrMaxLimit: 'The document has only one page or exceeds 146 pages, and cannot be stamped',\n    noSealForRiding: 'You do not have a seal that can be used, and cannot be stamped with a seam seal',\n    noSwitchSealNeedAppendBySelf: 'There is no switchable stamp, you can go to the enterprise console to add a stamp',\n    gotoAppendSeal: 'Go to add a seal',\n    approvalFlowSuccessfulSet: 'Approval flow is set successfully',\n    mandate: 'Agree to authorize',\n    loginToAppendSeal: 'You can also log in to BestSign website with a computer and go to the corporate console to add a seal',\n    signIdentityAs: 'Currently signing the contract in the name of {person}',\n    enterNextContract: 'Go to the next contract',\n    fileList: 'Document list',\n    addSignerFile: 'Add additional information',\n    signatureFinish: 'All stamped/signed',\n    dragSignatureTip: 'Please drag and drop the following signatures/dates into the file, you can drag and drop multiple times',\n    noticeToManager: 'Notify the administrator',\n    gotoAuthPerson: 'To authenticate individuals',\n    senderRequire: 'The sender requires you ',\n    senderRequireUseFollowIdentity: 'The sender requires you to meet the following identity:',\n    suggestToAuth: 'You have not verified your real name yet, it is recommended that you sign after real name verification',\n    contactEntAdmin: 'Please contact the business owner administrator',\n    setYourAccount: 'add your account',\n    authInfoUnMatchNeedResend: 'to sign the contract. This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again',\n    noEntNameNeedResend: 'The contract cannot be signed without specifying the name of the contracted company, please contact the initiator to resend the contract',\n    pleaseUse: 'Please authenticate as',\n    me: '我',\n    myself: '本人，',\n    reAuthBtnTip: '我是当前手机号的实际使用者，',\n    reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n    authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    authInfo2: '检测到您当前账号的基础身份信息为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '是否继续以当前账号签署？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n    confirmOk: 'OK',\n    goOnAuth: {\n      0: 'perform real-name authentication,',\n      1: 'Please perform real-name authentication,',\n      2: 'perform real-name authentication,'\n    },\n    signContractAfterAuth: {\n      0: 'after the certification is completed, the contract can be signed.',\n      1: 'after completing the certification, you can sign the contract.'\n    },\n    useIdentity: 'As {name}',\n    inTheName: 'as',\n    of: '',\n    identity: '',\n    nameIs: 'name as',\n    IDNumIs: 'ID number as',\n    provideMoreAuthData: 'Add more certification materials',\n    leadToAuthBeforeSign: 'You can sign the contract after continuing the certification',\n    groupProxyAuthNeedMore: 'Your current certification status is group certification, if you need to sign a contract separately, please add real-name certification materials',\n    contactSender: 'If you have any questions, please contact the sender.',\n    note: 'Note:',\n    identityInfo: 'Identity Information',\n    signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract.',\n    needAuthPermissionContactAdmin: 'You do not have the real-name authentication authority for the time being, please contact the administrator',\n    iHadReadContract: 'I have read, I know the content of the {alias}',\n    scrollToBottomTip: 'You need to scroll to the last page',\n    getVerCodeFirst: 'Please get the verification code first',\n    appScanVerify: 'Scan code verification with BestSign APP',\n    downloadBSApp: 'Download BestSign APP',\n    scanned: 'Scan code successfully',\n    confirmInBSApp: 'Please confirm your signature in BestSign APP',\n    qrCodeExpired: 'The QR code has expired, please refresh and try again',\n    appKey: 'APP security check',\n    goToScan: 'Go scan code',\n    setNotificationInUserCenter: 'Please go to the user center to set the notification method',\n    doNotWantUseVerCode: 'Don\\'t want to use verification code',\n    try: 'try',\n    retry: 'retry',\n    goToFaceVerify: 'go to face recognition',\n    faceExceedTimes: 'Face times has exceed',\n    returnBack: 'return back',\n    switchTo: 'switch to',\n    youCanChooseIdentityBlow: 'You can choose the following signing subject',\n    needDrawSignatureFirst: 'You have not signed, please add a hand-painted signature first',\n    lacksSealNeedAppend: 'You have not added any stamps, please add a stamp first.',\n    manageSeal: 'Management seal',\n    needDistributeSealToSelf: 'You don’t have a stamp available, please set yourself as the stamp holder',\n    chooseSealAfterAuth: 'Don\\'t want to use the above stamp? The seal can be replaced after real-name authentication',\n    appendDrawSignature: 'Add a hand-drawn signature',\n    senderUnFill: '（The sender did not fill in）',\n    declare: 'Description',\n    fileLessThan: 'Please upload a file smaller than {num}M',\n    fileNeedUploadImg: 'Please use pictures when uploading',\n    serverError: 'The server went up a bit, please try again later',\n    oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n    fileLimitFormatAndSize: 'The total size of auxiliary data shall not exceed XXM, and the number of single data pictures shall not exceed 10M.',\n    fileFormatImage: 'Support JPG, PNG and JPEG formats, and the size of a single picture no exceeds 20M',\n    fileFormatFile: 'Support PDF, excel, word, txt, zip, xml, JPG, PNG and JPEG formats, and the size of a single file no exceeds 10M',\n    signNeedKnow: 'Signing notice',\n    signNeedKnowFrom: 'Signing instructions from {sender}',\n    approvalInfo: 'Approval notice',\n    approveNeedKnowFrom: 'Approval materials submitted by {sendEmployeeName} at {sender}({approvalType})',\n    approveBeforeSend: 'Approve before sending',\n    approveBeforeSign: 'Approve before signing',\n    approveOperator: 'Approver',\n    approvalOpinion: 'Approval opinion',\n    employeeDefault: 'Employee',\n    setLabel: 'Set label',\n    addRidingSeal: 'Add seam seal',\n    delRidingSeal: 'Remove seam seal',\n    file: 'Attachment',\n    compressedFile: '压缩文件',\n    attachmentContent: 'Attachment content',\n    pleaseClickView: '（请点击下载查看）',\n    downloadFile: 'Download source file',\n    noLabelPleaseAppend: 'No tags yet, please go to the enterprise console to add',\n    archiveTo: 'Archive to',\n    hadArchivedToFolder: 'The contract has been successfully moved to the {folderName} folder of {who}',\n    pleaseScanToHandleWrite: 'Please use WeChat or mobile browser to scan the code, handwritten signature on the mobile device',\n    save: 'Save',\n    remind: 'Remind',\n    riskTip: 'Risk reminder',\n    chooseApplyPerson: 'Choose the person to stamp on the contract',\n    chooseAdminSign: 'Select a seal administrator',\n    useSealByOther: 'Stamped by others',\n    getSeal: 'Get a seal',\n    nowApplySealList: 'You are requesting the following seal',\n    nowAdminSealList: 'You are applying to receive the following seal',\n    chooseApplyPersonToDeal: 'Please select the person to stamp on the contract and the contract will be handled by the selected person (You can still continue to review and follow up on this contract)',\n    chooseApplyPersonToMandate: 'Please select the seal administrator. After the selected person receives the notification and passed the review, you will be granted the right to use the seal, and you can use the seal to seal and sign the contract.',\n    contactGroupAdminToDistributeSeal: 'Please contact the group administrator to assign a seal',\n    sealApplySentPleaseWait: 'The seal distribution application has been sent, please wait for approval. Or you can choose other stamping methods',\n    successfulSent: 'Sent successfully',\n    authTip: {\n      t2: ['Note: ', ' has to be exactly the same to sign the contract.', 'The company name', 'The identity information', ' has to be exactly the same to view and sign the contract.'],\n      t3: '{x} requires you to perform real name authentication {text}. ',\n      tCommon1: 'as {entName}',\n      tCommon2_1: 'as using your name as {name} and ID card No. {idCard}',\n      tCommon2_2: 'as using your name as {name}',\n      tCommon2_3: 'as using ID card No. {idCard}',\n      viewAndSign1: 'After completing the authentication, you can view and sign the contract. If you have any questions, please contact the sender. ',\n      viewAndSignConflict: '{x} requires you to view and sign the contract {text}. This does not match your real-name identity information. Please contact the sender yourself to confirm your identity information and request to initiate the contract again.'\n    },\n    needSomeoneToSignature: 'Seal {x} by {y}',\n    needToSet: 'Need to stamp',\n    approver: 'Applicant:',\n    clickToSignature: 'Click here to sign',\n    transferToOtherToSign: 'Transfer to other people to sign',\n    signatureBy: 'Signed by {x}',\n    tipRightNumber: 'Please enter the correct number',\n    tipRightIdCard: 'Please enter a valid 18-digit Mainland China Resident ID Card number',\n    tipRightPhoneNumber: 'Please enter a valid 11-digit mobile phone number',\n    tip: 'Tip',\n    tipRequired: 'Required value cannot be blank',\n    confirm: 'Confirm',\n    viewContractDetail: 'View contract details',\n    required: 'Required',\n    optional: 'Optional',\n    decimalLimit: 'Limited to {x} decimal places',\n    intLimit: 'integer required',\n    invalidContract: '签署此合同视为您同意将以下合同作废：',\n    No: '编号',\n    chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',\n    crossPlatformCofirm: {\n      message: 'Hello, the current contract needs to be signed across platforms, and the signed documents need to be transferred to overseas. Do you agree?',\n      title: 'Data authorization',\n      confirmButtonText: 'Agree to authorize',\n      cancelButtonText: 'Cancel'\n    },\n    sealScope: 'Seal Usage Scope',\n    currentContract: 'Current Contract',\n    allContract: 'All Contracts',\n    docView: 'Contract Preview',\n    fixTextDisplay: 'Fix Text Display',\n    allPage: '{num} Page',\n    notJoinTip: 'Please contact the administrator to be added as a company member before signing'\n  },\n  signJa: {\n    beforeSignTip1: 'According to the sender\\'s request, please sign in the name of this enterprise:',\n    beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n    beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n    beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n    beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n    beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n    beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n    entNamePlaceholder: '请输入企业名称',\n    corporateNumberPlaceholder: '请输入法人番号',\n    corporateNumber: 'corporate identification number',\n    singerNamePlaceholder: '请输入签署人姓名',\n    singerName: '签署人姓名',\n    businessPic: 'Seal certificate',\n    waitApprove: 'Under review. If you need to know the progress of the review, you can contact us by email: <EMAIL>',\n    itsMe: '是我本人',\n    wrongInformation: '信息有误',\n    confirmChange: '确认更换',\n    communicateSender1: '不更换, 与甲方沟通',\n    communicateSender2: '取消, 去与发件方沟通',\n    createSeal: {\n      title: '输入姓名',\n      tip: '请输入您的姓名（空格可以进行换行）',\n      emptyErr: '请输入姓名'\n    },\n    areaRegister: 'Country of Incorporation',\n    jp: 'Japan',\n    cn: 'Chinese Mainland',\n    are: 'United Arab Emirates',\n    other: 'Other',\n    plsSelect: 'Please select',\n    tip1: 'Enterprises registered in Chinese Mainland need to complete real name registration in ent.bestsign.cn. When signing contracts with enterprises outside the Chinese Mainland, the \"cross-border signing\" function can be used to efficiently complete mutual signing of contracts on the premise of ensuring the security of user data without leakage.',\n    tip2: 'If your enterprise has signed on the Chinese Mainland version to complete the real name authentication, you can directly log on to ent.bestsign.cn for convenient use of related services. It should be noted that the data generated by signing the overseas version is completely independent from the Chinese Mainland version.',\n    tip3: 'Please provide the identification number you obtained from the local commercial regulatory agency',\n    tip4: 'Please follow the steps below',\n    tip5: '1. Please contact your dedicated account manager to guide you through enterprise verification.',\n    tip6: 'Click on \\'Recharge Management\\'.',\n    tip7: '2. Please provide screenshots of your commercial contract with Esign or business correspondence emails with your dedicated account manager.',\n    tip8: 'Purchase at least one contract and save a screenshot of the purchase record.',\n    tip9: '3. This verification method is only available for enterprises outside Mainland China and Japan.',\n    tip10: '4. The review will be completed within 3 business days after submission.',\n    tip11: 'Important Note',\n    tip12: 'The purchaser must be a corporate user.',\n    tip13: 'The full name of the company in the payment account must be exactly the same as the \"company name\" you have filled in.',\n    tip14: 'Only enterprises outside Japan and Chinese Mainland can use this method.',\n    comNum: 'Enterprise ID number',\n    buyRecord: 'Supporting Documents',\n    selectArea: 'Please select the registered address of the enterprise',\n    uaeTip1: 'Enterprises registered in the United Arab Emirates must complete real name registration on uae.bestsign.com. When signing contracts with companies outside the United Arab Emirates, the \"cross-border signing\" function can be used to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',\n    uaeTip2: 'If your company has completed real name authentication on the UAE version of Shangshang, you can directly log in to uae.bestsign.com to conveniently use related services. It should be noted that the data generated by the overseas version you signed on is completely separate from the UAE version.',\n    uaeTip3: 'Enterprises registered outside the United Arab Emirates and Chinese Mainland need to complete real name registration in ent.bestsign.com. When signing contracts with companies in the United Arab Emirates, the \"cross-border signing\" function can be utilized to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.'\n  },\n  signPC: {\n    commonSign: 'Confirm signing',\n    contractVerification: 'Signing verification',\n    VerCodeVerify: 'Verification code check',\n    QrCodeVerify: 'QR code check',\n    verifyTip: 'BestSign is calling your Digital CA certificate (Certificate Athority), and you are in a secure signing environment, please be assured to sign!',\n    verifyAllTip: 'BestSign is calling the enterprise digital certificate and your personal digital certificate, you are in a secure signing environment, please rest assured to sign!',\n    selectSeal: 'Electronic seal',\n    adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n    toAddSealWithConsole: 'The electronic official seal is pending activation. To add other seals, please go to the console.',\n    use: 'Use',\n    toAddSeal: 'To add a seal',\n    mySeal: 'My seals',\n    operationCompleted: 'Operation completed',\n    FDASign: {\n      date: 'Date',\n      signerAdd: 'Add',\n      signerEdit: 'Edit',\n      editTip: 'Note: Chinese name please input Pinyin, such as San Zhang',\n      inputNameTip: 'Please enter your name',\n      inputName: 'Please input English name or Chinese name in Pinyin',\n      signerNameFillTip: 'You will also need to fill in your name',\n      plsInput: 'Please input',\n      plsSelect: 'Please select',\n      customInput: 'Custom input'\n    },\n    signPlaceBySigner: {\n      signGuide: 'Guide to signing',\n      howDragSeal: 'How to drag a stamp',\n      howDragSignature: 'How to drag signatures',\n      iKnow: 'I see',\n      step: {\n        one: 'Step 1: read the contract',\n        two1: 'Step 2: click \"Drag stamp\"',\n        two2: 'Step 2: click \"Drag signature\"',\n        three: 'Step 3: click the \"Sign\" button'\n      },\n      dragSeal: 'Drag stamp',\n      continueDragSeal: 'Continue to drag stamp',\n      dragSignature: 'Drag signature',\n      continueDragSignature: 'Continue to drag signature',\n      dragPlace: 'Drag it here',\n      notRemind: 'Never show again',\n      signTip: {\n        one: 'Step 1: Locate where you need to sign/stamp by clicking \"Start\".',\n        two: 'Step 2: Complete the signature/seal as required by clicking \"Signature/Stamp\".'\n      },\n      finishSignatureBeforeSign: 'Please finish dragging signature/stamp before confirming sign'\n    },\n    continueOperation: {\n      success: 'Success',\n      exitApproval: 'Exit approval',\n      continueApproval: 'Continue approving',\n      next: 'Next contract:',\n      none: 'None',\n      tip: 'Tip',\n      approvalProcess: 'The contract needs {totalNum} people to approve; Currently {passNum} people have approved.',\n      receiver: 'receivers:'\n    }\n  },\n  signTip: {\n    contractDetail: 'Contract Details',\n    downloadBtn: 'Download APP',\n    tips: 'Tip',\n    submit: 'OK',\n    SigningCompleted: 'Sign successfully',\n    submitCompleted: '等待他人处理',\n    noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n    noRightSign: '合同正在签署中，当前用户不允许签署操作',\n    noNeedSign: '内部决议合同，已无需签署',\n    ApprovalCompleted: 'Approval succeeded',\n    contractRevoked: 'The contract has been canceled',\n    contractRefused: 'The contract has been refused',\n    linkExpired: 'The link has expired',\n    contractClosed: 'The contract has been closed',\n    approvalReject: 'The contract approval has been rejected',\n    approving: 'The contract is under review',\n    viewContract: 'View contract',\n    viewContractList: 'View Contract List',\n    needMeSign: ' ({num} Pending for Signature)',\n    downloadContract: 'Download contract',\n    sign: 'sign',\n    signed: ' signed',\n    approved: 'approved',\n    approval: 'approval',\n    person: ' person',\n    personHas: ' ',\n    personHave: ' ',\n    personHasnot: ' not ',\n    personsHavenot: ' not ',\n    headsTaskDone: '{num}{has}{done}',\n    headsTaskNotDone: '{num}{not}{done}',\n    taskStatusBetween: ',',\n    cannotReview: 'Cannot review contract',\n    cannotDownload: 'The contract does not support mobile phone downloads. Because the contract is privately stored by the sender, the contract cannot be obtained by BestSign.',\n    privateStorage: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server',\n    beenDeleted: 'Your account has been deleted by the enterprise administrator',\n    unActive: 'Unable to continue to activate account',\n    back: 'back',\n    contratStatusDes: '{key} status: ',\n    contractConditionDes: '{key} condition: ',\n    contractIng: 'Contract in {key}',\n    contractComplete: 'Contract {key} completed',\n    dataProduct: {\n      tip1: '{entName}致各位优质经销商/供应商企业负责人：',\n      tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',\n      btnText: '去向老板分享这个喜讯'\n    },\n    signOnGoing: 'Signing',\n    operate: 'operate',\n    freeContract: 'Upon completing the first contract dispatch, additional contract copies can be obtained for free.',\n    sendContract: 'Send contract',\n    congratulations: 'Congratulations to {name} for completing {num} contract signings,',\n    carbonSaving: 'with an estimated carbon saving of {num}g.',\n    signGift: 'BestSign presents you with {num} corporate contracts (usage period valid until {limit}).',\n    followPublic: 'Follow our WeChat official account to receive contract updates promptly.',\n    congratulationsSingle: 'Congratulations to {name} on the contract signing,',\n    carbonSavingSingle: 'Estimated carbon reduction: 2,002.4g',\n    viewContractTip: 'If you need to change the person who stamps, you can click the \\\"View detail\\\" button to open the contract details page, then click the \\\"Apply for Stamp\\\" button.',\n    congratulationsCn: 'Thank you for choosing e-signature!',\n    carbonSavingSingleCn: 'You have reduced carbon by {num}gCO2e for the Earth',\n    carbonVerification: \"*Scientifically calculated by 'Carbonstop'\"\n  },\n  view: {\n    title: 'View Contract',\n    ok: 'OK',\n    cannotReview: 'Cannot review contract',\n    privateStorage: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server'\n  },\n  prepare: {\n    sealArea: 'Seal here',\n    senderNotice: 'Current contract sender:{entName}',\n    preSetDialogConfirm: 'OK',\n    preSetDialogContact: 'Contact BestSign sales representatives to open your account immediately',\n    preSetDialogInfo: 'When sending a contract, the system will automatically fill in the corresponding signatory information, signing requirements, signing locations, contract description fields, etc. according to the pre-set template',\n    preSetDialogTitle: 'What is a pre-set contract template?',\n    initialValues: 'Pre-set initial values based on contract content',\n    proxyUpload: 'Select contract sender after uploading local documents',\n    signHeaderTitle: 'Add files and signer',\n    step1: 'Step 1',\n    confirmSender: 'Confirmation of sender',\n    step2: 'Step 2',\n    uploadFile: 'Upload a file',\n    step3: 'Step 3',\n    addSigner: 'Add Signer',\n    actionDemo: 'Action demo',\n    next: 'next',\n    isUploadingErr: 'The file has not been uploaded yet. Please continue after the completion.',\n    noUploadFileErr: 'File not uploaded, please continue after uploading',\n    noContractTitleErr: 'The contract name is not filled out, please fill in and continue',\n    contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',\n    expiredDateErr: 'The deadline for signing is incorrect. Please continue to modify it.',\n    noExpiredDateErr: 'Please fill in the signing deadline and continue',\n    noRecipientsErr: 'Add at least one contractor',\n    noAccountErr: 'Account cannot be empty',\n    noUserNameErr: 'Name cannot be empty',\n    noIDNumberErr: 'ID card cannot be empty',\n    noEntNameErr: 'Business name cannot be empty',\n    accountFormatErr: 'Please enter the correct phone number or email address.',\n    userNameFormatErr: 'Please enter the correct name',\n    enterpriseNameErr: 'Please enter the correct company name',\n    idNumberForVerifyErr: 'Please enter the correct ID card',\n    signerErr: 'The signing party is wrong',\n    noSignerErr: 'Please add at least one signer',\n    lackAttachmentNameErr: 'Please fill in the attachment name',\n    repeatRecipientsErr: 'Cannot add signing parties repeatedly when not in sequential order',\n    innerContact: 'Internal contact',\n    outerContact: 'External contact',\n    search: 'Search for',\n    accountSelected: 'Selected account',\n    groupNameAll: 'All',\n    unclassified: 'Unclassified',\n    fileLessThan: 'Please upload files less than {num}M',\n    beExcel: 'Please upload Excel file',\n    usePdf: 'Use PDF files or pictures when uploading',\n    usePdfFile: 'Use PDF files when uploading',\n    fileNameMoreThan: 'File name length more than {num}, has been automatically intercepted for you',\n    needAddSender: 'Your company/you are not set as a signing party. After the contract is sent out, you will not participate in the signing process. Do you need to add yourself as a signing party?',\n    addSender: 'Add as signing party',\n    tip: 'Hint',\n    cancel: 'Cancel'\n  },\n  addReceiver: {\n    English: 'English',\n    Japanese: 'Japanese',\n    Chinese: 'Chinese',\n    Arabic: 'Arabic',\n    setNoticelang: 'Notification Language',\n    limitFaceConfigTip: 'This feature is unavailable due to your contract price being too low. Please contact BestSign for consultation',\n    individual: 'Individual signatory',\n    enterprise: 'Company signatory',\n    addInstructions: 'Add signing instructions',\n    instructionsContent: 'The submitted information is used to help you track the status of contract performance and determine whether business is being performed properly. Once set up, this signatory must submit the information as required',\n    addContractingInfo: 'Submit information of the signatory',\n    contractingInfoContent: 'The submitted information is used to help you check the qualifications of the signatories and determine whether you can start or continue business with them. If the same information has already been submitted by the signatory, it may not be submitted again.',\n    payer: 'Signatory who pays for the contract signing',\n    handWriting: 'Turn on handwriting recognition',\n    realName: 'Handler individual real name authentication required',\n    sameTip: 'Tip: Signing is only allowed when the signatory\\'s company name is exactly the same to the name designated by the sender',\n    proxy: 'Signing notice received by the signatory\\'s reception desk (No specific account to receive the notice)',\n    aboradTip: 'Tips: This is a foreign signatory and with risks in real name authentication. Please verify the identity of the person first.',\n    busRole: 'Business roles',\n    busRoleTip: 'Helps you identify the signatory for easy management',\n    busRolePlaceholder: 'Such as employees/distributors',\n    handWritingTip: 'The signatory will need to sign with a legible handwritten signature.',\n    instructions: 'Add signing instructions for the signatory |  (up to 255 words)',\n    contractingParty: 'Information of the signatory',\n    signerPay: 'This signatory will pay for the contract signing',\n    afterReadingTitle: 'Sign after reading',\n    afterReading: 'The signatory must read and know the content of the contract before signing.',\n    handWritingTips: 'The signatory\\'s handwritten name will be compared with the name specified by the sender or with the real name authentication information in the system before signing',\n    SsTitle: 'Signing with both stamp and signature',\n    SsTip: 'If you need to sign with a company stamp and also with your personal signature at the same time, your individual real name authentication must be completed before signing.',\n    signature: 'Signature',\n    stamp: 'Stamp',\n    Ss: 'Stamp and Signature',\n    mutexError: 'Already set\"{msg}\", please delete the \"{msg}\"  setting first',\n    forceHandWrite: 'Handwritten signature required',\n    faceVerify: 'Must sign with facial verification',\n    attachmentRequired: 'Add attachments to the contract',\n    newAttachmentRequired: 'submit information of the contracting party ',\n    attachmentError: 'The name of attachment cannot be the same to other attachment\\'s',\n    receiver: 'Receiving phone/email |（supports a maximum of 5, can use a semicolon）',\n    orderSignLabel: 'Sequential signing',\n    contactAddress: 'Contact address book',\n    signOrder: 'Sequence',\n    account: 'Account',\n    accountPlaceholder: 'Phone/email (required)',\n    accountReceptionCollection: 'Front desk collection',\n    accountReceptionCollectionTip1: 'Do not know the other party specific account number or the other party has no account number',\n    chooseTransferPerson: 'Transfer contract',\n    accountReceptionCollectionTip2: 'Please choose the front desk to collect',\n    signSubjectPerson: 'Signatory: individual',\n    nameTips: 'Name (optional)',\n    requiredNameTips: 'Name (required for signature identification)',\n    entOperatorNameTips: 'Name (optional)',\n    needAuth: 'Real name required',\n    operatorNeedAuth: 'Handler individual real name authentication required',\n    signSubjectEnt: 'Signatory: enterprise',\n    entNameTips: 'Enterprise name (Optional)',\n    operator: 'Operator',\n    sign: 'Sign',\n    more: 'More',\n    faceFirst: 'Priority sign with facial verification, alternate sign with SMS code verification',\n    faceFirstTips: 'When signing, the system defaults to face verification. When the number of times the brush fails to pass reaches the upper limit of the day, it automatically switches to verification code verification',\n    mustFace: 'Must sign with facial recognition',\n    handWriteNotAllowed: 'Handwritten signature not allowed',\n    mustHandWrite: 'Must sign with handwritten signature',\n    fillIDNumber: 'ID number',\n    fillNoticeCall: 'notification phone',\n    fillNoticeCallTips: 'Fill in the notification phone',\n    addNotice: 'Add private message',\n    attachTips: 'Annex requirements',\n    faceSign: 'Must sign with facial recognition',\n    faceSignTips: 'The user needs to pass the face authentication to complete the signing (the face signing is only supported by the mainland residents)',\n    handWriteNotAllowedTips: 'The user can only select the signature that has been set or use the default font signature to complete the signing',\n    handWriteTips: 'The user needs a handwritten signature to complete the signing',\n    idNumberTips: 'Used for signing identity check',\n    verifyBefore: 'Verify identity before viewing files',\n    verify: 'Verify identidy',\n    verifyTips: 'Up to 20 words',\n    verifyTips2: 'You must provide this verification information to this user',\n    sendToThirdPlatform: 'Send to third-party platforms',\n    platFormName: 'Platform name',\n    fillThirdPlatFormName: 'Please enter a third-party platform name',\n    attach: 'Attachment',\n    attachName: 'Accessory name',\n    exampleID: 'Example: ID card photo',\n    attachInfo: 'Attachment description',\n    attachInfoTips: 'Example: Please upload my ID card photo',\n    addAttachRequire: 'Add attachment requirements',\n    addSignEnt: 'Add signing enterprise',\n    addSignPerson: 'Add signing individual',\n    selectContact: 'Select contact',\n    save: 'Save',\n    searchVerify: 'Query check',\n    fillImageContentTips: 'Please fill in the image content',\n    ok: 'Confirm',\n    findContact: 'Find the following contractors from the contract',\n    signer: 'Signer',\n    signerTips: 'Tip: After selecting the contractor, the platform can help locate the signature and stamp location.',\n    add: 'Add',\n    notAdd: 'Don\\'t add',\n    cc: 'Cc',\n    notNeedAuth: 'No real name required',\n    operatorNotNeedAuth: 'No real name required',\n    extracting: 'Extracting',\n    autoFill: 'Auto-fill signer',\n    failExtracting: 'Not drawn to the signatory',\n    idNumberForVerifyErr: 'Please enter the correct ID card',\n    noAccountErr: 'Account cannot be empty',\n    noUserNameErr: 'Name cannot be empty',\n    noIDNumberErr: 'ID card cannot be empty',\n    noEntNameErr: 'Business name cannot be empty',\n    accountFormatErr: 'Please enter the correct phone number or email address.',\n    enterpriseNameErr: 'Please enter the correct company name',\n    userNameFormatErr: 'Please enter the correct name',\n    riskCues: 'risk warning',\n    riskCuesMsg: 'If the signatory party does not sign the real name, you will need to provide evidence of the identity of the signatory party in the event of a dispute. To avoid risk, please choose real name',\n    confirmBtnText: 'Choose real name',\n    cancelBtnText: 'Choose not to have a real name',\n    attachLengthErr: 'You can only add up to 50 attachment requests to a single signer',\n    collapse: 'Fold',\n    expand: 'Expand',\n    delete: 'Delete',\n    saySomething: 'say something',\n    addImage: 'add pictures',\n    addImageTips: '(Support pdf, word, ,jpg, png format, up to10 uploads)',\n    give: 'to',\n    fileMax: 'Uploads exceed the maximum number',\n    signerLimit: 'Your current version does not support more than {limit} relative signatories.',\n    showExamle: 'View Example Images',\n    downloadExamle: 'Download Example File'\n  },\n  addReceiverGuide: {\n    notRemind: 'Do not remind me next time',\n    sign: 'Signature',\n    entSign: 'Company signature',\n    stamp: 'Stamp',\n    stampSign: 'Stamp and signature',\n    requestSeal: 'Stamp for Business Chedck',\n    'guideTitle': 'How to add a new signatory',\n    'receiverType': 'You need to choose the way the signatory participates in the contract (one of six options).',\n    'asEntSign': 'Signing on behalf of the company:',\n    'sealSub': 'The signatory needs to stamp the contract with the company seal created in the Company console module.',\n    'signatureSub': 'The legal person or senior executive signs the contract on behalf of the enterprise. The enterprise has the right to transfer the contract, making the signer unable to view the contract.',\n    'vipOnly': 'Available in Advanced Edition',\n    'stampSub': 'The signatory can stamp and put handwritten shgnature on the contract.',\n    'confirmSealSub': 'Such as financial statements, confirmation letters and other documents. The signatory shall check before stamping.',\n    'asPersonSign': 'Signed on behalf of an individual.',\n    'asPersonSignTip': 'Individual sign, not on behalf of any company',\n    'asPersonSignDesc': 'Private contracts of the signatory, such as loan contracts, employment and resignation, etc.',\n    'scanSign': 'Signing by scaning the QR code',\n    'scanSignDesc': 'No need to specify the signatory when start the contract. After the contract is created, anyone can scan the QR code/click the link to sign, which is applicable to some scenarios, such as logistics document signing in goods receiving scenario.',\n    'selectSignTypeTip': 'Please select the way the signatory participates in the contract first'\n  },\n  linkContract: {\n    title: 'Relate this contract to another contract',\n    connectMore: 'Relate this contract to more contracts',\n    placeholder: 'Please enter the contract ID',\n    revoke: 'The contract has been cancelled',\n    overdue: 'Overdue and unsigned',\n    approvalNotPassed: 'Approval rejected',\n    reject: 'The contract has been rejected by signatory',\n    signing: 'signing',\n    complete: 'complete',\n    approvaling: 'approval',\n    disconnect: 'Unrelate these contracts',\n    disconnectSuccess: 'disconnect successfully',\n    connectLimit: 'Up to 100 contracts can be related'\n  },\n  field: {\n    fieldTip: {\n      title: 'No signing location indicated',\n      error: 'Notsign with ({type}) in the designated locations in the following contract',\n      add: 'Add fields',\n      continue: 'Continue to send'\n    },\n    accountCharge: {\n      notice: 'The contract is charged by the number of participating signatory',\n      able: 'Can be sent normally',\n      unable: 'Not enough accounts available for use. Please contact BestSign customer service.',\n      notify: 'The contract will notify all contracting parties in English.',\n      noNotify: {\n        1: 'This contract will not issue any signing related notices',\n        2: '(including notification SMS and email for signing, approval, CC, deadline for signing, etc.)'\n      }\n    },\n    ridingStamp: 'Paging Stamp',\n    watermark: 'Watermark',\n    senderSignature: 'Signature of the sealer',\n    optional: 'Optional',\n    decoration: 'Decoration',\n    clickDecoration: '点击合同装饰',\n    sysError: 'System busy, please try later',\n    partedMarkedError: 'You must set the locations of the stamp and signature for the signatory with \"Stamp and signature\"',\n    fieldTitle: '{length} contracts need to be set the signing locations',\n    send: 'Send',\n    contractDispatchApply: 'Apply for a contract',\n    contractNeedYouSign: 'This document needs you to sign',\n    ifSignRightNow: 'Whether to sign it now',\n    signRightNow: 'Sign it now',\n    signLater: 'Sign later',\n    signaturePositionErr: 'Please specify the signing location for each signer',\n    sendSucceed: 'Send successfully',\n    confirm: 'Confirm',\n    cancel: 'Cancel',\n    qrCodeTips: 'After signing the code, you can view the signing details, verify the validity of the signature and whether the contract has been tampered with.',\n    pagesField: 'Page {currentPage}, {totalPages} in total',\n    suitableWidth: 'Suitable for width',\n    signCheck: 'Signature inspection',\n    locateSignaturePosition: 'Locate the signing location',\n    locateTips: 'Can help to quickly locate to the signing location. Currently only supports the first signing location for each signing party',\n    step1: 'Step 1',\n    selectSigner: 'Select signer',\n    step2: 'Step 2',\n    dragSignaturePosition: 'Drag sign position',\n    signingField: 'Signing field',\n    docTitle: 'Document',\n    totalPages: 'Pages: {totalPages}',\n    receiver: 'Receiver',\n    delete: 'Delete',\n    deductPublicNotice: 'If the number of copies of the private contract is insufficient, the contract will be deducted',\n    unlimitedNotice: 'Unlimited use of the contract billing',\n    charge: 'Billing',\n    units: '{num} ',\n    contractToPrivate: 'for private contract',\n    contractToPublic: 'for enterprise contract',\n    costTips: {\n      1: 'Contract for a public contract: a contract with a corporate account in the signatory (excluding the sender)',\n      2: 'Private contract: a contract that does not have a corporate account in the signatory (excluding the sender)',\n      3: 'The number of billing shares is calculated based on the number of copies of the file.',\n      4: 'Billing Shares = File Copies × Batch Import Users'\n    },\n    costInfo: 'After successfully sending the contract, the fee will be deducted immediately, and it will not be refunded for contract completion, expiration, withdrawal, or refusal.',\n    toCharge: 'Recharge',\n    contractNeedCharge: {\n      1: 'The number of available contracts is insufficient and cannot be sent',\n      2: 'The number of available contracts is insufficient. Please contact the main administrator to top up.'\n    },\n    chooseApprover: 'Select approver:',\n    nextStep: 'Next',\n    submitApproval: 'Submit for approval',\n    autoSendAfterApproval: '*After approval, the contract will be sent automatically',\n    chooseApprovalFlow: 'Please select an approval flow',\n    completeApprovalFlow: 'The approval process you submitted is incomplete, please complete and resubmit',\n    viewPrivateLetter: 'View messages',\n    addPrivateLetter: 'Add messages',\n    append: 'Add',\n    privateLetter: 'Messages',\n    signNeedKnow: 'Signing notice',\n    maximum5M: 'Please upload documents smaller than 5M',\n    uploadServerFailure: 'Failed to upload to server',\n    uploadFailure: 'Upload failed',\n    pager: 'Page',\n    seal: 'Seal',\n    signature: 'Signature',\n    signDate: 'Date of signing',\n    text: 'Text',\n    date: 'Date',\n    qrCode: 'QR code',\n    number: 'Digits',\n    dynamicTable: 'Dynamic table',\n    terms: 'Contract terms',\n    checkBox: 'Checkbox',\n    radioBox: 'Radio button',\n    image: 'Picture'\n  },\n  addressBook: {\n    innerMember: {\n      title: 'Internal members of the company',\n      tips: 'Adjust enterprise membership information',\n      operation: 'To the console'\n    },\n    outerContacts: {\n      title: 'External contacts',\n      tips: 'Invite your business partners to finish the real names authentication in advance for rapid business development',\n      operation: 'Invite your business partners'\n    },\n    myContacts: {\n      title: 'My contacts',\n      tips: 'Modify contact, to ensure that the signatory information accurate',\n      operation: 'To the user center'\n    },\n    selected: 'Selected accounts',\n    search: 'Search',\n    loadMore: 'Load more',\n    end: 'All loading completed'\n  },\n  dataBoxInvite: {\n    title: 'Invite your business partners',\n    step1: 'Share the link with your business partner to create a company account in advance',\n    step2: 'Company account created through the link / QR code will appear in your address book',\n    step3: 'Manage your partners in \"File +\" module',\n    imgName: 'Share the QR code',\n    saveQrcode: 'Save the QR code',\n    copy: 'Copy',\n    copySuccess: 'Copy success',\n    copyFailed: 'COPY FAILED'\n  },\n  shareView: {\n    title: 'Forward for Review',\n    account: 'Phone Number/Email',\n    role: 'Reviewer Role',\n    note: 'Remarks',\n    link: 'Link:',\n    saveQrcode: 'Save Wechat Mini-Program Code',\n    signerMessage: 'Signer\\'s Message',\n    rolePlaceholder: 'e.g., company legal advisor, department head, etc.',\n    notePlaceholder: 'Leave a message for the reviewer, within 200 characters',\n    generateLink: 'Generate Link',\n    regenerateLink: 'Regenerate Link',\n    inputAccount: 'Please enter your phone number or email',\n    inputCorrectAccount: 'Please enter correct phone number or email',\n    accountInputTip: 'To ensure the link opens correctly, please enter the contract reviewer\\'s information accurately',\n    shareLinkTip1: 'Save the Wechat mini-program code or ',\n    shareLinkTip: 'copy the link to share with the reviewer',\n    linkTip1: 'The contents of the contract text are confidential, please do not disclose them unless necessary',\n    linkTip2: 'The link is valid for 2 days; after regenerating the link, the previous link will automatically become invalid'\n  },\n  recoverSpecialSeal: {\n    title: '印章无法使用',\n    description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',\n    description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',\n    postRecover: '申请恢复印章',\n    note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',\n    requestSend: '恢复申请提交成功'\n  },\n  paperSign: {\n    title: '使用纸质方式签署',\n    stepText: ['下一步', '确认纸质签', '确定'],\n    needUploadFile: '请先上传扫描件',\n    uploadError: '上传失败',\n    cancel: '取消',\n    downloadPaperFile: '获取纸质签文件',\n    step0: {\n      title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n      address: '邮寄地址：',\n      contactName: '接收人姓名：',\n      contactPhone: '接收人联系方式：',\n      defaultValue: '请通过线下方式向发件方索取'\n    },\n    step1: {\n      title0: '第一步：下载&打印纸质合同',\n      title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n      title1: '第二步：加盖印章',\n      title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n      title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n      title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。']\n    },\n    step2: {\n      title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n      uploadFile: '上传扫描件',\n      getCodeVerify: '获取合同签署校验',\n      isUploading: '上传中...'\n    }\n  },\n  allowPaperSignDialog: {\n    title: 'Allow Paper Signatures',\n    content: 'This contract is issued by {senderName} to {receiverName}, allowing for signing in paper form.',\n    tip: \"You may choose to compile the contract documents at present and print them, subsequently to be signed and sealed offline by the individual responsible for the company's seal, in accordance with formal procedures.\",\n    icon: 'Conversion to paper-based signing >>',\n    goSign: 'To sign electronically',\n    cancel: 'Cancel'\n  },\n  sealInconformityDialog: {\n    errorSeal: {\n      title: '印章提示',\n      tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n      tip1: '检测到有企业印章与企业名称：',\n      tip2: '是否要继续使用当前印章图片？',\n      tip3: '根据发件方要求，您需要使用企业名称为：',\n      tip4: '的印章',\n      tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n      tip6: '不匹配，请确保印章符合发件方要求。',\n      guide: '如何上传正确的印章 >>',\n      next: '继续使用',\n      tip7: 'And your seal name does not comply with the specifications, with the words “{keyWord}” .',\n      tip8: 'Detected that the seal name does not comply with the specifications and contains the words “{keyWord}”. Do you want to continue using it?'\n    },\n    exampleSeal: {\n      title: '上传印章图案方式',\n      way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n      way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n      errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照']\n    },\n    confirm: '确认',\n    cancel: '取消'\n  },\n  addSealDialog: {\n    title: 'Add Stamp Image',\n    dec1: 'Please select a stamp image from your local folder (formats: JPG, JPEG, PNG, etc.). The system will merge this stamp image into the current contract.',\n    dec2: 'After that, you need to click the \"Sign\" button to pass the signing verification to complete the stamping.',\n    updateNewSeal: 'Upload New Stamp'\n  }\n};", "map": {"version": 3, "names": ["ssoLoginConfig", "notBelongToEntTip", "operationStep", "one", "two", "three", "continue", "cancel", "tip", "sign", "sealLabelsTip", "nonMainlandCARenewalTip", "reselect", "approvalFeatures", "dialogTitle", "understand", "feature1", "feature2", "tip1", "tip2", "tip3", "tip4", "tip5", "tip6", "annotate", "delete", "edit", "operateTitle", "placeholder", "contractHighLight", "needRemark", "notNeedRemark", "switchToReceiver", "notAddEntTip", "contractPartiesYouChoose", "contractPartyFilled", "certifyOtherCompanies", "youCanAlso", "needVerification", "prompt", "submit", "addSeal", "noSealAvailable", "memberNoSealAvailable", "noticeAdminFoSeal", "requestSomeone", "requestOthersToContinue", "requestOthersToContinueSucceed", "requestSomeoneList", "electronicSeal", "changeTheSeal", "goToVerify", "noSealToC<PERSON>ose", "goToVerifyEnt", "digitalCertificateTip", "signDes", "goVerify", "signAgain", "send", "person", "ent", "entName", "account", "accountPH", "approved", "signVerification", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFail", "connectFailTip", "connectFailTip1", "connectFailTip2", "connectFailTip3", "personalMaterials", "noSupportface", "lackEntName", "errAccount", "noticeAdmin", "signDone", "signDoneTip", "approveDone", "approveDoneTip", "completeSign", "<PERSON><PERSON><PERSON><PERSON>", "stillSignTip", "signHighLightTip", "riskDetails", "noviewDifference", "highLightTip", "commonTip", "view", "start", "nextStep", "help", "faceFailed", "dualFailed", "faceFailedtips", "qrcodeInvalid", "faceFirstExceed", "verifyTry", "faceLimit", "upSignReq", "reqFace", "signAfterFace", "date", "chooseSeal", "seal", "signature", "handwrite", "mysign", "approve<PERSON>lace", "approvePlace_1", "approvePlace_2", "approveAgree", "approveReject", "signBy", "signByEnd", "sealBy", "sealByEnd", "coverBy", "applicant", "continueVeri", "registerAndReal", "goToResiter", "sureToUse", "toSign", "pleaseComplete", "confirmSign", "admin", "contratAdmin", "addToEnt", "alreadyExists", "sendMsg", "<PERSON><PERSON><PERSON><PERSON>", "title", "viewImg", "priLetter", "priLetterFromSomeone", "readLetter", "approve", "disapprove", "refuseSign", "paperSign", "refuseTip", "refuseReason", "reasonWriteTip", "refuseReasonOther", "refuseConfirm", "refuseConfirmTip", "waitAndThink", "signValidationTitle", "email", "phoneNumber", "password", "verificationCode", "mailVerificationCode", "forgetPsw", "if", "forgetPassword", "rejectionVer", "msgTip", "voiceVerCode", "SMSVerCode", "emailVerCode", "or", "SentSuccessfully", "intervalTip", "signPsw", "signConfirmTip", "confirm", "useSignPsw", "setSignPsw", "useVerCode", "inputSignPwdTip", "inputVerifyCodeTip", "signSuc", "refuseSuc", "approveSuc", "hdFile", "otherOperations", "reviewDetails", "close", "submitter", "signatory", "reviewSchedule", "signByPc", "signPageDescription", "sealBySomeone", "signDate", "download", "signPage", "signNow", "sender", "signer", "startSignTime", "signDeadLine", "authGuide", "goToHome", "tip_1", "tip_2", "tip_3", "tip_4", "tip_5", "new_tip_1", "new_tip_2", "new_tip_3", "new_tip_4", "entUserName", "idNumberForVerify", "realNameAuth", "applySeal", "signContract", "switch", "rejectReasonList", "signOperateReason", "termReason", "explainReason", "otherReason", "selectSignature", "selectS<PERSON>er", "pleaseScanToSign", "pleaseScanAliPay", "pleaseScanWechat", "requiredFaceSign", "requiredDualSign", "verCodeVerify", "applyToSign", "autoRemindAfterApproval", "cannotSignBeforeApproval", "finishSignatureBeforeSign", "uploadFileOnRightSite", "cannotApplySealNeedPay", "cannotOtherSealReason", "unlimitedNotice", "units", "contractToPrivate", "contractToPublic", "paySum", "payTotal", "fundsLack", "contactToRecharge", "deductPublicNotice", "needSignerPay", "recharge", "toSubmit", "appliedSeal", "noSeal", "noSwitchSealNeedDistribute", "viewApproveProcess", "approveProcess", "noApprove<PERSON><PERSON>nt", "knew", "noSwitchSealNeedAppend", "hadAutoSet", "setThatSignature", "setThatSeal", "applyThatSeal", "hasSetTip", "hasSetSealTip", "hasSetSignatureTip", "hasApplyForSealTip", "savedOnLeftSite", "ridingSealMinLimit", "ridingSealMaxLimit", "ridingSealMinOrMaxLimit", "noSealForRiding", "noSwitchSealNeedAppendBySelf", "gotoAppendSeal", "approvalFlowSuccessfulSet", "mandate", "loginToAppendSeal", "signIdentityAs", "enterNextContract", "fileList", "addSignerFile", "signatureFinish", "dragSignatureTip", "noticeToManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senderRequire", "senderRequireUseFollowIdentity", "suggestToAuth", "contactEntAdmin", "set<PERSON>ourAccount", "authInfoUnMatchNeedResend", "noEntNameNeedResend", "pleaseUse", "me", "myself", "reAuthBtnTip", "reAuthBtnContent", "descNoSame1", "descNoSame2", "authInfoNoSame", "authInfoNoSame2", "goHome", "authInfo", "authInfo2", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmOk", "goOnAuth", "signContractAfterAuth", "useIdentity", "inTheName", "of", "identity", "nameIs", "IDNumIs", "provideMoreAuthData", "leadToAuthBeforeSign", "groupProxyAuthNeedMore", "contactSender", "note", "identityInfo", "signNeedCoincidenceInfo", "needAuthPermissionContactAdmin", "iHadReadContract", "scrollToBottomTip", "getVerCodeFirst", "appScanVerify", "downloadBSApp", "scanned", "confirmInBSApp", "qrCodeExpired", "appKey", "goToScan", "setNotificationInUserCenter", "doNotWantUseVerCode", "try", "retry", "goToFaceVerify", "faceExceedTimes", "returnBack", "switchTo", "youCanChooseIdentityBlow", "needDrawSignatureFirst", "lacksSealNeedAppend", "manageSeal", "needDistributeSealToSelf", "chooseSealAfterAuth", "appendDrawSignature", "senderUnFill", "declare", "fileLessThan", "fileNeedUploadImg", "serverError", "oldFormatTip", "fileLimitFormatAndSize", "fileFormatImage", "fileFormatFile", "signNeedKnow", "signNeedKnowFrom", "approvalInfo", "approveNeedKnowFrom", "approveBeforeSend", "approveBeforeSign", "approveOperator", "approvalOpinion", "employeeDefault", "<PERSON><PERSON><PERSON><PERSON>", "addRidingSeal", "delRidingSeal", "file", "compressedFile", "attachmentContent", "pleaseClickView", "downloadFile", "noLabelPleaseAppend", "archiveTo", "hadArchivedToFolder", "pleaseScanToHandleWrite", "save", "remind", "riskTip", "chooseApp<PERSON><PERSON><PERSON>", "chooseAdminSign", "useSealByOther", "getSeal", "nowApplySealList", "nowAdminSealList", "chooseApplyPersonToDeal", "chooseApplyPersonToMandate", "contactGroupAdminToDistributeSeal", "sealApplySentPleaseWait", "successfulSent", "authTip", "t2", "t3", "tCommon1", "tCommon2_1", "tCommon2_2", "tCommon2_3", "viewAndSign1", "viewAndSignConflict", "needSomeoneToSignature", "needToSet", "approver", "clickToSignature", "transferToOtherToSign", "signatureBy", "tipRightNumber", "tipRightIdCard", "tipRightPhoneNumber", "tipRequired", "viewContractDetail", "required", "optional", "decimalLimit", "intLimit", "invalidContract", "No", "chooseFrom2", "crossPlatformCofirm", "message", "confirmButtonText", "cancelButtonText", "sealScope", "currentContract", "allContract", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixTextDisplay", "allPage", "notJoinTip", "signJa", "beforeSignTip1", "beforeSignTip2", "beforeSignTip3", "beforeSignTip4", "beforeSignTip5", "beforeSignTip6", "beforeSignTip7", "entNamePlaceholder", "corporateNumberPlaceholder", "corporateNumber", "singerNamePlaceholder", "<PERSON><PERSON><PERSON>", "businessPic", "waitApprove", "itsMe", "wrongInformation", "confirmChange", "communicateSender1", "communicateSender2", "createSeal", "emptyErr", "areaRegister", "jp", "cn", "are", "other", "plsSelect", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "comNum", "buyRecord", "selectArea", "uaeTip1", "uaeTip2", "uaeTip3", "signPC", "commonSign", "contractVerification", "VerCodeVerify", "QrCodeVerify", "verifyTip", "verifyAllTip", "selectSeal", "adminGuideTip", "toAddSealWithConsole", "use", "toAddSeal", "mySeal", "operationCompleted", "FDASign", "signer<PERSON>dd", "signerEdit", "editTip", "inputNameTip", "inputName", "signerNameFillTip", "plsInput", "customInput", "signPlace<PERSON>y<PERSON><PERSON><PERSON>", "signGuide", "howDragSeal", "howDragSignature", "iKnow", "step", "two1", "two2", "dragSeal", "continueDragSeal", "dragSignature", "continueDragSignature", "dragPlace", "notR<PERSON>ind", "signTip", "continueOperation", "success", "exitApproval", "continueApproval", "next", "none", "approvalProcess", "receiver", "contractDetail", "downloadBtn", "tips", "SigningCompleted", "submitCompleted", "noTurnSign", "noRightSign", "noNeedSign", "ApprovalCompleted", "contractRevoked", "contractRefused", "linkExpired", "contractClosed", "approvalReject", "approving", "viewContract", "viewContractList", "needMeSign", "downloadContract", "signed", "approval", "personHas", "personHave", "person<PERSON>asnot", "<PERSON><PERSON><PERSON><PERSON>", "headsTaskDone", "headsTaskNotDone", "taskStatusBetween", "cannotDownload", "privateStorage", "beenDeleted", "unActive", "back", "contratStatusDes", "contractConditionDes", "contractIng", "contractComplete", "dataProduct", "btnText", "signOnGoing", "operate", "freeContract", "sendContract", "congratulations", "carbonSaving", "signGift", "followPublic", "congratulations<PERSON><PERSON><PERSON>", "carbonSavingSingle", "viewContractTip", "congratulationsCn", "carbonSavingSingleCn", "carbonVerification", "ok", "prepare", "sealArea", "senderNotice", "preSetDialogConfirm", "preSetDialogContact", "preSetDialogInfo", "preSetDialogTitle", "initialValues", "proxyUpload", "signHeaderTitle", "step1", "confirmSender", "step2", "uploadFile", "step3", "addSigner", "actionDemo", "isUploadingErr", "noUploadFileErr", "noContractTitleErr", "contractTypeErr", "expiredDateErr", "noExpiredDateErr", "noRecipientsErr", "noAccountErr", "noUserNameErr", "noIDNumberErr", "noEntNameErr", "accountFormatErr", "userNameFormatErr", "enterpriseNameErr", "idNumberForVerifyErr", "signer<PERSON>rr", "noSignerErr", "lackAttachmentNameErr", "repeatRecipientsErr", "innerContact", "outerContact", "search", "accountSelected", "groupNameAll", "unclassified", "beExcel", "usePdf", "usePdfFile", "fileNameMoreThan", "need<PERSON>dd<PERSON><PERSON>", "addSender", "addReceiver", "English", "Japanese", "Chinese", "Arabic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limitFaceConfigTip", "individual", "enterprise", "addInstructions", "instructionsContent", "addContractingInfo", "contractingInfoContent", "payer", "handWriting", "realName", "sameTip", "proxy", "aboradTip", "busRole", "busRoleTip", "busRolePlaceholder", "handWritingTip", "instructions", "contractingParty", "signer<PERSON><PERSON>", "afterReadingTitle", "afterReading", "handWritingTips", "SsTitle", "SsTip", "stamp", "Ss", "mutexError", "forceHandWrite", "faceVerify", "attachmentRequired", "newAttachmentRequired", "attachmentError", "orderSignLabel", "contactAddress", "signOrder", "accountPlaceholder", "accountReceptionCollection", "accountReceptionCollectionTip1", "chooseTransferPerson", "accountReceptionCollectionTip2", "signSub<PERSON><PERSON>erson", "nameTips", "requiredNameTips", "entOperatorNameTips", "needAuth", "operatorNeedAuth", "signSubjectEnt", "entNameTips", "operator", "more", "faceFirst", "faceFirstTips", "mustFace", "handWriteNotAllowed", "mustHandWrite", "fillIDNumber", "fillNoticeCall", "fillNoticeCallTips", "addNotice", "attachTips", "faceSign", "faceSignTips", "handWriteNotAllowedTips", "handWriteTips", "idNumberTips", "verifyBefore", "verify", "verifyTips", "verifyTips2", "sendToThirdPlatform", "platFormName", "fillThirdPlatFormName", "attach", "attachName", "exampleID", "attachInfo", "attachInfoTips", "addAttachRequire", "addSignEnt", "addSign<PERSON>erson", "selectContact", "searchVerify", "fillImageContentTips", "findContact", "signer<PERSON><PERSON>s", "add", "notAdd", "cc", "notNeedAuth", "operatorNotNeedAuth", "extracting", "autoFill", "failExtracting", "riskCues", "riskCuesMsg", "confirmBtnText", "cancelBtnText", "attachLengthErr", "collapse", "expand", "saySomething", "addImage", "addImageTips", "give", "fileMax", "signerLimit", "showExamle", "downloadExamle", "addReceiverGuide", "entSign", "stampSign", "requestSeal", "linkContract", "connectMore", "revoke", "overdue", "approvalNotPassed", "reject", "signing", "complete", "approvaling", "disconnect", "disconnectSuccess", "connectLimit", "field", "fieldTip", "error", "accountCharge", "notice", "able", "unable", "notify", "noNotify", "ridingStamp", "watermark", "senderSignature", "decoration", "clickDecoration", "sysError", "partedMarkedError", "fieldTitle", "contractDispatchApply", "contractNeedYouSign", "ifSignRightNow", "signRightNow", "signLater", "signaturePositionErr", "sendSucceed", "qrCodeTips", "pagesField", "suitableWidth", "signCheck", "locateSignaturePosition", "locateTips", "dragSignaturePosition", "<PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "totalPages", "charge", "costTips", "costInfo", "to<PERSON>harge", "contractNeedCharge", "chooseApprover", "submitApproval", "autoSendAfterApproval", "chooseApprovalFlow", "completeApprovalFlow", "viewPrivateLetter", "addPrivateLetter", "append", "privateLetter", "maximum5M", "uploadServerFailure", "uploadFailure", "pager", "text", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "addressBook", "innerMember", "operation", "outerContacts", "myContacts", "selected", "loadMore", "end", "dataBoxInvite", "imgName", "saveQrcode", "copy", "copySuccess", "copyFailed", "shareView", "role", "link", "signerMessage", "rolePlaceholder", "notePlaceholder", "generateLink", "regenerateLink", "inputAccount", "inputCorrectAccount", "accountInputTip", "shareLinkTip1", "shareLinkTip", "linkTip1", "linkTip2", "recoverSpecialSeal", "description1", "description2", "postRecover", "requestSend", "stepText", "needUploadFile", "uploadError", "downloadPaperFile", "step0", "address", "contactName", "contactPhone", "defaultValue", "title0", "title0Desc", "title1", "title1Desc", "title2", "title2Desc", "getCodeVerify", "isUploading", "allowPaperSignDialog", "content", "icon", "goSign", "sealInconformityDialog", "errorSeal", "guide", "exampleSeal", "way1", "way2", "errorWay", "addSealDialog", "dec1", "dec2", "updateNewSeal"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/sign/sign-en.js"], "sourcesContent": ["export default {\n    ssoLoginConfig: {\n        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',\n        operationStep: {\n            one: '第一步 点击继续后，返回登录页面',\n            two: '第二步 输入密码，进入上上签平台',\n            three: '第三步 发送合同（或管理模板）',\n        },\n        continue: 'Continue',\n        cancel: 'Cancel',\n        tip: 'Tip',\n    },\n    sign: {\n        sealLabelsTip: 'You need to affix {sealLabelslen} seals on the contract. {personStr} will affix {otherSealLen} seal for you, and you will personally affix the remaining {mySealLen} seal. The seals required are displayed on the page. Please confirm if you wish to proceed.',\n        continue: 'Continue',\n        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n        reselect: '重选',\n        approvalFeatures: {\n            dialogTitle: 'New Feature Introduction',\n            understand: 'I understand',\n            feature1: 'Sentence Highlighting Annotations',\n            feature2: 'Field Highlighting',\n            tip1: 'Click the button to highlight all \"template content fields\" in the contract to capture key information.',\n            tip2: 'Click the prompt button in the lower left corner to enable template content field highlighting.',\n            tip3: 'By highlighting, the content of the contract can be quickly located to fill in the fields, and the approval can be completed efficiently.',\n            tip4: 'After selecting a text field, click the annotation button to add annotation text. Once completed, click modify or delete. The content of the annotations can be viewed in the contract details page - company internal operation log.',\n            tip5: 'Step 1: Select the text field to be annotated and add the annotation.',\n            tip6: 'Step 2: Click to edit or delete the annotation.',\n            annotate: 'Annotations',\n            delete: 'Deletion',\n            edit: 'Modification',\n            operateTitle: 'Add Approval Annotations',\n            placeholder: 'No more than 255 words',\n        },\n        contractHighLight: {\n            dialogTitle: 'Contract highlighting reminder',\n        },\n        needRemark: '您还需要填写备注',\n        notNeedRemark: '您不需要填写备注',\n        switchToReceiver: 'You have been switched to {receiver}',\n        notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',\n        contractPartiesYouChoose: 'Contract parties you can choose:',\n        contractPartyFilled: 'Contract party that filled in by sender is:',\n        certifyOtherCompanies: 'Certify other companies',\n        youCanAlso: 'You can also:',\n        needVerification: 'You need real name verification to sign',\n        prompt: 'Hint',\n        submit: 'OK',\n        cancel: 'Cancel',\n        sign: 'Sign',\n        addSeal: 'Please login PC BestSign website to add seal',\n        noSealAvailable: 'Sorry, you don\\'t have a seal and no seal management rights. Please contact the administrator to assign a seal to you',\n        memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',\n        noticeAdminFoSeal: 'Send notification to the master administrator',\n        requestSomeone: 'Request someone else to verify',\n        requestOthersToContinue: 'Ask Administrator to fulfill',\n        requestOthersToContinueSucceed: 'Request sent successfully',\n        requestSomeoneList: 'Request the following personnel to complete the real name certification:',\n        electronicSeal: 'Electronic seal',\n        changeTheSeal: 'Don’t want to use this seal? Change the seal after real name verification',\n        goToVerify: 'Go to verify ',\n        noSealToChoose: 'No seal to choose, if you need managing seals, please get real name verification',\n        goToVerifyEnt: 'Go to verify enterprise',\n        digitalCertificateTip: 'BestSign is calling your digital certificate',\n        signDes: 'You\\'re in secure signing environment, feel free to sign!',\n        goVerify: 'Go verify ',\n        signAgain: 'Sign',\n        send: 'Send',\n        person: 'Natural person',\n        ent: 'Enterprise',\n        entName: 'Name of enterprise',\n        account: 'Account',\n        accountPH: 'Telephone or email',\n        approved: 'Approved',\n        signVerification: 'Sign',\n        cannotReview: 'Cannot review contract',\n        connectFail: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server.',\n        connectFailTip: '您可以尝试以下方法解决问题：',\n        connectFailTip1: '1、刷新页面。',\n        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n        personalMaterials: 'Sender requests you to add more certification materials',\n        noSupportface: 'The contract initiator requests you to sign with face ID, non-mainland Chinese people do not support face ID signing, please contact the initiator to modify the signing requirements',\n        lackEntName: 'Please input name of enterprise',\n        errAccount: 'Please input correct telephone or email',\n        noticeAdmin: 'Apply',\n        signDone: 'Signing completed',\n        signDoneTip: 'You have signed this contract',\n        approveDone: 'Approval completed',\n        approveDoneTip: 'You have approved this contract',\n        completeSign: 'Please click \"Seal\" or \"Signature\" to complete the signing',\n        fillFirst: 'Please fill in the contract content in the input box first',\n        stillSignTip: 'After you sign this {alias}, there are still other signatories who may modify the {alias} and continue to sign it?',\n        signHighLightTip: 'A total of {count} {alias} contents can be added or modified',\n        riskDetails: 'Risk details',\n        noviewDifference: 'Because sender opened the parties can fill in the {alias} of fixed field function, other signed this {alias} may still change the initiator content specified in the {alias}, sign on the right version before the signing of this {alias} and the content of the difference between effective version for review, when you after signing the {alias}, signed as you agree to the other party to the {alias} content of fixed field increase or modify the content, And recognize the effective version of this {alias} signed by each signatory.\\n' +\n            'If you do not agree that other signatories can still change the fields of this {alias} after you sign, you may refuse to sign this {alias} and negotiate with the sender (that is, ask the initiator to turn off the function of \"Signatory fill in the fields\" to avoid the corresponding risks to you).',\n        highLightTip: 'These risky content will be \"highlighted\", please check carefully. Refresh the page to remove the highlighting effect.',\n        commonTip: 'Tip',\n        understand: 'I understand',\n        view: 'View',\n        start: 'Start',\n        nextStep: 'next',\n        help: 'Help',\n        faceFailed: 'Sorry, your face comparison failed',\n        dualFailed: 'Sorry, the dual-recording verification has failed. Please verify your information and try again.',\n        faceFailedtips: 'Tips',\n        qrcodeInvalid: 'Qrcode is invalid，please refresh',\n        faceFirstExceed: 'Face scan failed, Authentication code will be used in the next step for authentication.',\n        verifyTry: 'Verify the identity and try again',\n        faceLimit: 'Today\\'s face comparison has reached the line',\n        upSignReq: 'Please try again tomorrow or contact the contract initiator to modify the signing requirements',\n        reqFace: 'Sender requests you to do Face ID verification',\n        signAfterFace: 'After Face ID passes, you can complete the contract signing',\n        date: 'date',\n        chooseSeal: 'Choose seal',\n        seal: 'seal',\n        signature: 'Create signature ',\n        handwrite: 'Handwritten',\n        mysign: 'My signature',\n        approvePlace: 'Approval message, not necessary',\n        approvePlace_1: 'Approval message',\n        approvePlace_2: 'Optional, maximum 255 characters',\n        approveAgree: 'Approval result: Approved',\n        approveReject: 'Approval result: Reject',\n        signBy: 'Sign by ',\n        signByEnd: '',\n        sealBy: 'Seal by ',\n        sealByEnd: '',\n        coverBy: 'Cover by ',\n        applicant: 'Applicant:',\n        continueVeri: 'Go certify',\n        registerAndReal: 'Please register and real name',\n        goToResiter: 'Go to registration and certification',\n        sureToUse: 'Are you sure with ',\n        toSign: 'to sign?',\n        pleaseComplete: 'Please complete the signatue before confirming the ',\n        confirmSign: '',\n        admin: 'administrator',\n        contratAdmin: 'Please contact the administrator to ',\n        addToEnt: 'add your account as a business',\n        alreadyExists: 'On the BestSign already exists',\n        sendMsg: 'BestSign will send the following to the administrator as a text message:',\n        applyJoin: 'Apply to join ',\n        title: 'Title',\n        viewImg: 'View picture',\n        priLetter: 'Message',\n        priLetterFromSomeone: 'Message from {name}',\n        readLetter: 'OK',\n        approve: 'Approve',\n        disapprove: 'Reject',\n        refuseSign: 'Reject',\n        paperSign: '改用纸质签署',\n        refuseTip: 'Please choose the reason for rejection',\n        refuseReason: 'Fill in the reason for the refusal to help the other party understand your problem and speed up the contract process',\n        reasonWriteTip: 'Please fill in the reason for rejection',\n        refuseReasonOther: 'More reasons for rejection (optional) | More reasons for rejection (required)',\n        refuseConfirm: 'Reject',\n        refuseConfirmTip: 'You have refused to sign this contract for the reason \"{reason}\". Do you want to continue? After confirmation, you will not be able to sign this contract again.',\n        waitAndThink: 'Reconsider',\n        signValidationTitle: 'Sign Verification',\n        email: 'E-mail',\n        phoneNumber: 'Phone',\n        password: 'Password',\n        verificationCode: 'SMS Code',\n        mailVerificationCode: 'Verification code',\n        forgetPsw: 'Forget?',\n        if: ',',\n        forgetPassword: 'Forgot?',\n        rejectionVer: 'Verification',\n        msgTip: 'Can\\'t review the SMS messages? Try',\n        voiceVerCode: 'a voice call',\n        SMSVerCode: 'SMS verification code',\n        emailVerCode: 'E-mail verification code',\n        or: 'or',\n        SentSuccessfully: 'Send successfully!',\n        intervalTip: 'Please send again later',\n        signPsw: 'Sign Password',\n        signConfirmTip: {\n            1: 'Are you sure you want to sign this {contract}?',\n            2: 'Click the confirm button to sign the {contract} immediately',\n            confirm: 'Confirm',\n        },\n        useSignPsw: 'Use the password for verification',\n        setSignPsw: 'Set the signing password verification',\n        useVerCode: 'Use verification code',\n        inputSignPwdTip: 'Please enter the signing password',\n        inputVerifyCodeTip: 'Please enter verification code',\n        signSuc: 'Sign successfully',\n        refuseSuc: 'Refused',\n        approveSuc: 'Approval succeeded',\n        hdFile: 'View HD files',\n        otherOperations: 'Other operations',\n        reviewDetails: 'Approval details',\n        close: 'Close',\n        submitter: 'Approval initiator',\n        signatory: 'Signatory',\n        reviewSchedule: 'Approval schedule',\n        signByPc: 'Sign by {name}',\n        signPageDescription: 'Page {index}, {total} in total',\n        sealBySomeone: 'Seal by {name}',\n        signDate: 'Signing date',\n        download: 'Download',\n        signPage: 'Page: {page}',\n        signNow: 'Sign now',\n        sender: 'Sender',\n        signer: 'Signer',\n        startSignTime: 'Start signing time',\n        signDeadLine: 'Signing deadline',\n        authGuide: {\n            goToHome: 'Go to home',\n            tip_1: 'After the certification is completed, you can view and sign the contract.',\n            tip_2: 'Please use identity | to auth.',\n            tip_3: 'send a contract',\n            tip_4: 'Please contact the contract initiator | change recipient.',\n            tip_5: 'Your certified | unable to view contract',\n            new_tip_1: 'Based on the sender\\'s compliance requirements, you need to complete the following steps:',\n            new_tip_2: 'Based on the compliance requirements of the sender, you need to:',\n            new_tip_3: 'complete the following steps.',\n            new_tip_4: 'If you already have seal permission, step 2 will be automatically skipped for you',\n            entUserName: 'username:',\n            idNumberForVerify: 'IDCard:',\n            realNameAuth: 'Real-name authentication',\n            applySeal: 'Apply for the seal',\n            signContract: 'Sign contract',\n        },\n        switch: 'Switch',\n        rejectReasonList: {\n            // authReason: '不想/不会做实名认证',\n            signOperateReason: 'If you have questions about the signing operation/verification operation, further communication is needed',\n            termReason: 'I have doubts about the terms/content of the contract and need to communicate further',\n            explainReason: 'If you are not aware of the contract contents, please inform us in advance',\n            otherReason: 'Others (please fill in the reason)',\n        },\n        selectSignature: 'Select signature',\n        selectSigner: 'Choose signer',\n        pleaseScanToSign: 'Please scan and sign with Alipay or WeChat',\n        pleaseScanAliPay: 'Please use Alipay app to scan the QR code to sign',\n        pleaseScanWechat: 'Please use WeChat app to scan the QR code to sign',\n        requiredFaceSign: 'The sender of the contract asks you to scan your face to recognize and sign',\n        requiredDualSign: 'The contract sender requires you to complete dual-recording verification',\n        verCodeVerify: 'Verification code verification',\n        applyToSign: 'Apply to sign a contract',\n        autoRemindAfterApproval: '*After the approval is passed, the signing reminder will be automatically sent to the signatory',\n        cannotSignBeforeApproval: 'The approval has not been completed and cannot be signed temporarily',\n        finishSignatureBeforeSign: 'Please complete the stamp/signature before confirming the signature',\n        uploadFileOnRightSite: 'You still have attachments that have not been uploaded, please upload them in the right column first',\n        cannotApplySealNeedPay: 'This contract requires you to pay, and the application for seal is not supported',\n        cannotOtherSealReason: 'Face verification is required for this contract. Signature by others is not allowed',\n        unlimitedNotice: 'Unlimited use of the contract billing',\n        units: '{num}',\n        contractToPrivate: 'individual contracts',\n        contractToPublic: 'company contracts',\n        paySum: '共{sum}需要您支付',\n        payTotal: '共计{total}元.',\n        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',\n        contactToRecharge: 'Please contact the main administrator to recharge.',\n        deductPublicNotice: 'Private contracts will be deducted when the number of available copies is insufficient.',\n        needSignerPay: 'The sender of the contract has set recipient payment, and designates you to pay for the contract.',\n        recharge: 'Recharge',\n        toSubmit: 'Submit',\n        appliedSeal: 'Application for seal has been submitted',\n        noSeal: 'No seal',\n        noSwitchSealNeedDistribute: 'Sorry, you do not currently have a seal that can be used, please contact the business owner administrator to add a seal and authorize',\n        viewApproveProcess: 'View the approval process',\n        approveProcess: 'Approval process',\n        noApproveContent: 'No approval note were submitted',\n        knew: 'I see',\n        noSwitchSealNeedAppend: 'There is no switchable stamp, please contact the administrator to add a stamp',\n        hadAutoSet: 'Automatically placed in {num} other places',\n        setThatSignature: 'by this signature',\n        setThatSeal: 'by this stamp',\n        applyThatSeal: 'applied by this stamp',\n        hasSetTip: 'Automatically placed at {index} other locations',\n        hasSetSealTip: 'The seal has been automatically placed in {index} other places',\n        hasSetSignatureTip: 'The signature has been automatically placed in {index} other places',\n        hasApplyForSealTip: 'The seal has been automatically applied for in {index} other places',\n        savedOnLeftSite: 'Saved to the signature bar on the left',\n        ridingSealMinLimit: 'The document has only one page and cannot be stamped with a seam seal',\n        ridingSealMaxLimit: 'More than 146 pages, stamping of seam seal is not supported',\n        ridingSealMinOrMaxLimit: 'The document has only one page or exceeds 146 pages, and cannot be stamped',\n        noSealForRiding: 'You do not have a seal that can be used, and cannot be stamped with a seam seal',\n        noSwitchSealNeedAppendBySelf: 'There is no switchable stamp, you can go to the enterprise console to add a stamp',\n        gotoAppendSeal: 'Go to add a seal',\n        approvalFlowSuccessfulSet: 'Approval flow is set successfully',\n        mandate: 'Agree to authorize',\n        loginToAppendSeal: 'You can also log in to BestSign website with a computer and go to the corporate console to add a seal',\n        signIdentityAs: 'Currently signing the contract in the name of {person}',\n        enterNextContract: 'Go to the next contract',\n        fileList: 'Document list',\n        addSignerFile: 'Add additional information',\n        signatureFinish: 'All stamped/signed',\n        dragSignatureTip: 'Please drag and drop the following signatures/dates into the file, you can drag and drop multiple times',\n        noticeToManager: 'Notify the administrator',\n        gotoAuthPerson: 'To authenticate individuals',\n        senderRequire: 'The sender requires you ',\n        senderRequireUseFollowIdentity: 'The sender requires you to meet the following identity:',\n        suggestToAuth: 'You have not verified your real name yet, it is recommended that you sign after real name verification',\n        contactEntAdmin: 'Please contact the business owner administrator',\n        setYourAccount: 'add your account',\n        authInfoUnMatchNeedResend: 'to sign the contract. This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again',\n        noEntNameNeedResend: 'The contract cannot be signed without specifying the name of the contracted company, please contact the initiator to resend the contract',\n        pleaseUse: 'Please authenticate as',\n        me: '我',\n        myself: '本人，',\n        reAuthBtnTip: '我是当前手机号的实际使用者，',\n        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n        authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        authInfo2: '检测到您当前账号的基础身份信息为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '是否继续以当前账号签署？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n        confirmOk: 'OK',\n        goOnAuth: {\n            0: 'perform real-name authentication,',\n            1: 'Please perform real-name authentication,',\n            2: 'perform real-name authentication,',\n        },\n        signContractAfterAuth: {\n            0: 'after the certification is completed, the contract can be signed.',\n            1: 'after completing the certification, you can sign the contract.',\n        },\n        useIdentity: 'As {name}',\n        inTheName: 'as',\n        of: '',\n        identity: '',\n        nameIs: 'name as',\n        IDNumIs: 'ID number as',\n        provideMoreAuthData: 'Add more certification materials',\n        leadToAuthBeforeSign: 'You can sign the contract after continuing the certification',\n        groupProxyAuthNeedMore: 'Your current certification status is group certification, if you need to sign a contract separately, please add real-name certification materials',\n        contactSender: 'If you have any questions, please contact the sender.',\n        note: 'Note:',\n        identityInfo: 'Identity Information',\n        signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract.',\n        needAuthPermissionContactAdmin: 'You do not have the real-name authentication authority for the time being, please contact the administrator',\n        iHadReadContract: 'I have read, I know the content of the {alias}',\n        scrollToBottomTip: 'You need to scroll to the last page',\n        getVerCodeFirst: 'Please get the verification code first',\n        appScanVerify: 'Scan code verification with BestSign APP',\n        downloadBSApp: 'Download BestSign APP',\n        scanned: 'Scan code successfully',\n        confirmInBSApp: 'Please confirm your signature in BestSign APP',\n        qrCodeExpired: 'The QR code has expired, please refresh and try again',\n        appKey: 'APP security check',\n        goToScan: 'Go scan code',\n        setNotificationInUserCenter: 'Please go to the user center to set the notification method',\n        doNotWantUseVerCode: 'Don\\'t want to use verification code',\n        try: 'try',\n        retry: 'retry',\n        goToFaceVerify: 'go to face recognition',\n        faceExceedTimes: 'Face times has exceed',\n        returnBack: 'return back',\n        switchTo: 'switch to',\n        youCanChooseIdentityBlow: 'You can choose the following signing subject',\n        needDrawSignatureFirst: 'You have not signed, please add a hand-painted signature first',\n        lacksSealNeedAppend: 'You have not added any stamps, please add a stamp first.',\n        manageSeal: 'Management seal',\n        needDistributeSealToSelf: 'You don’t have a stamp available, please set yourself as the stamp holder',\n        chooseSealAfterAuth: 'Don\\'t want to use the above stamp? The seal can be replaced after real-name authentication',\n        appendDrawSignature: 'Add a hand-drawn signature',\n        senderUnFill: '（The sender did not fill in）',\n        declare: 'Description',\n        fileLessThan: 'Please upload a file smaller than {num}M',\n        fileNeedUploadImg: 'Please use pictures when uploading',\n        serverError: 'The server went up a bit, please try again later',\n        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n        fileLimitFormatAndSize: 'The total size of auxiliary data shall not exceed XXM, and the number of single data pictures shall not exceed 10M.',\n        fileFormatImage: 'Support JPG, PNG and JPEG formats, and the size of a single picture no exceeds 20M',\n        fileFormatFile: 'Support PDF, excel, word, txt, zip, xml, JPG, PNG and JPEG formats, and the size of a single file no exceeds 10M',\n        signNeedKnow: 'Signing notice',\n        signNeedKnowFrom: 'Signing instructions from {sender}',\n        approvalInfo: 'Approval notice',\n        approveNeedKnowFrom: 'Approval materials submitted by {sendEmployeeName} at {sender}({approvalType})',\n        approveBeforeSend: 'Approve before sending',\n        approveBeforeSign: 'Approve before signing',\n        approveOperator: 'Approver',\n        approvalOpinion: 'Approval opinion',\n        employeeDefault: 'Employee',\n        setLabel: 'Set label',\n        addRidingSeal: 'Add seam seal',\n        delRidingSeal: 'Remove seam seal',\n        file: 'Attachment',\n        compressedFile: '压缩文件',\n        attachmentContent: 'Attachment content',\n        pleaseClickView: '（请点击下载查看）',\n        downloadFile: 'Download source file',\n        noLabelPleaseAppend: 'No tags yet, please go to the enterprise console to add',\n        archiveTo: 'Archive to',\n        hadArchivedToFolder: 'The contract has been successfully moved to the {folderName} folder of {who}',\n        pleaseScanToHandleWrite: 'Please use WeChat or mobile browser to scan the code, handwritten signature on the mobile device',\n        save: 'Save',\n        remind: 'Remind',\n        riskTip: 'Risk reminder',\n        chooseApplyPerson: 'Choose the person to stamp on the contract',\n        chooseAdminSign: 'Select a seal administrator',\n        useSealByOther: 'Stamped by others',\n        getSeal: 'Get a seal',\n        nowApplySealList: 'You are requesting the following seal',\n        nowAdminSealList: 'You are applying to receive the following seal',\n        chooseApplyPersonToDeal: 'Please select the person to stamp on the contract and the contract will be handled by the selected person (You can still continue to review and follow up on this contract)',\n        chooseApplyPersonToMandate: 'Please select the seal administrator. After the selected person receives the notification and passed the review, you will be granted the right to use the seal, and you can use the seal to seal and sign the contract.',\n        contactGroupAdminToDistributeSeal: 'Please contact the group administrator to assign a seal',\n        sealApplySentPleaseWait: 'The seal distribution application has been sent, please wait for approval. Or you can choose other stamping methods',\n        successfulSent: 'Sent successfully',\n        authTip: {\n            t2: ['Note: ', ' has to be exactly the same to sign the contract.', 'The company name', 'The identity information', ' has to be exactly the same to view and sign the contract.'],\n            t3: '{x} requires you to perform real name authentication {text}. ',\n            tCommon1: 'as {entName}',\n            tCommon2_1: 'as using your name as {name} and ID card No. {idCard}',\n            tCommon2_2: 'as using your name as {name}',\n            tCommon2_3: 'as using ID card No. {idCard}',\n            viewAndSign1: 'After completing the authentication, you can view and sign the contract. If you have any questions, please contact the sender. ',\n            viewAndSignConflict: '{x} requires you to view and sign the contract {text}. This does not match your real-name identity information. Please contact the sender yourself to confirm your identity information and request to initiate the contract again.',\n        },\n        needSomeoneToSignature: 'Seal {x} by {y}',\n        needToSet: 'Need to stamp',\n        approver: 'Applicant:',\n        clickToSignature: 'Click here to sign',\n        transferToOtherToSign: 'Transfer to other people to sign',\n        signatureBy: 'Signed by {x}',\n        tipRightNumber: 'Please enter the correct number',\n        tipRightIdCard: 'Please enter a valid 18-digit Mainland China Resident ID Card number',\n        tipRightPhoneNumber: 'Please enter a valid 11-digit mobile phone number',\n        tip: 'Tip',\n        tipRequired: 'Required value cannot be blank',\n        confirm: 'Confirm',\n        viewContractDetail: 'View contract details',\n        required: 'Required',\n        optional: 'Optional',\n        decimalLimit: 'Limited to {x} decimal places',\n        intLimit: 'integer required',\n        invalidContract: '签署此合同视为您同意将以下合同作废：',\n        No: '编号',\n        chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',\n        crossPlatformCofirm: {\n            message: 'Hello, the current contract needs to be signed across platforms, and the signed documents need to be transferred to overseas. Do you agree?',\n            title: 'Data authorization',\n            confirmButtonText: 'Agree to authorize',\n            cancelButtonText: 'Cancel',\n        },\n        sealScope: 'Seal Usage Scope',\n        currentContract: 'Current Contract',\n        allContract: 'All Contracts',\n        docView: 'Contract Preview',\n        fixTextDisplay: 'Fix Text Display',\n        allPage: '{num} Page',\n        notJoinTip: 'Please contact the administrator to be added as a company member before signing',\n    },\n    signJa: {\n        beforeSignTip1: 'According to the sender\\'s request, please sign in the name of this enterprise:',\n        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n        entNamePlaceholder: '请输入企业名称',\n        corporateNumberPlaceholder: '请输入法人番号',\n        corporateNumber: 'corporate identification number',\n        singerNamePlaceholder: '请输入签署人姓名',\n        singerName: '签署人姓名',\n        businessPic: 'Seal certificate',\n        waitApprove: 'Under review. If you need to know the progress of the review, you can contact us by email: <EMAIL>',\n        itsMe: '是我本人',\n        wrongInformation: '信息有误',\n        confirmChange: '确认更换',\n        communicateSender1: '不更换, 与甲方沟通',\n        communicateSender2: '取消, 去与发件方沟通',\n        createSeal: {\n            title: '输入姓名',\n            tip: '请输入您的姓名（空格可以进行换行）',\n            emptyErr: '请输入姓名',\n        },\n        areaRegister: 'Country of Incorporation',\n        jp: 'Japan',\n        cn: 'Chinese Mainland',\n        are: 'United Arab Emirates',\n        other: 'Other',\n        plsSelect: 'Please select',\n        tip1: 'Enterprises registered in Chinese Mainland need to complete real name registration in ent.bestsign.cn. When signing contracts with enterprises outside the Chinese Mainland, the \"cross-border signing\" function can be used to efficiently complete mutual signing of contracts on the premise of ensuring the security of user data without leakage.',\n        tip2: 'If your enterprise has signed on the Chinese Mainland version to complete the real name authentication, you can directly log on to ent.bestsign.cn for convenient use of related services. It should be noted that the data generated by signing the overseas version is completely independent from the Chinese Mainland version.',\n        tip3: 'Please provide the identification number you obtained from the local commercial regulatory agency',\n        tip4: 'Please follow the steps below',\n        tip5: '1. Please contact your dedicated account manager to guide you through enterprise verification.',\n        tip6: 'Click on \\'Recharge Management\\'.',\n        tip7: '2. Please provide screenshots of your commercial contract with Esign or business correspondence emails with your dedicated account manager.',\n        tip8: 'Purchase at least one contract and save a screenshot of the purchase record.',\n        tip9: '3. This verification method is only available for enterprises outside Mainland China and Japan.',\n        tip10: '4. The review will be completed within 3 business days after submission.',\n        tip11: 'Important Note',\n        tip12: 'The purchaser must be a corporate user.',\n        tip13: 'The full name of the company in the payment account must be exactly the same as the \"company name\" you have filled in.',\n        tip14: 'Only enterprises outside Japan and Chinese Mainland can use this method.',\n        comNum: 'Enterprise ID number',\n        buyRecord: 'Supporting Documents',\n        selectArea: 'Please select the registered address of the enterprise',\n        uaeTip1: 'Enterprises registered in the United Arab Emirates must complete real name registration on uae.bestsign.com. When signing contracts with companies outside the United Arab Emirates, the \"cross-border signing\" function can be used to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',\n        uaeTip2: 'If your company has completed real name authentication on the UAE version of Shangshang, you can directly log in to uae.bestsign.com to conveniently use related services. It should be noted that the data generated by the overseas version you signed on is completely separate from the UAE version.',\n        uaeTip3: 'Enterprises registered outside the United Arab Emirates and Chinese Mainland need to complete real name registration in ent.bestsign.com. When signing contracts with companies in the United Arab Emirates, the \"cross-border signing\" function can be utilized to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',\n    },\n    signPC: {\n        commonSign: 'Confirm signing',\n        contractVerification: 'Signing verification',\n        VerCodeVerify: 'Verification code check',\n        QrCodeVerify: 'QR code check',\n        verifyTip: 'BestSign is calling your Digital CA certificate (Certificate Athority), and you are in a secure signing environment, please be assured to sign!',\n        verifyAllTip: 'BestSign is calling the enterprise digital certificate and your personal digital certificate, you are in a secure signing environment, please rest assured to sign!',\n        selectSeal: 'Electronic seal',\n        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n        toAddSealWithConsole: 'The electronic official seal is pending activation. To add other seals, please go to the console.',\n        use: 'Use',\n        toAddSeal: 'To add a seal',\n        mySeal: 'My seals',\n        operationCompleted: 'Operation completed',\n        FDASign: {\n            date: 'Date',\n            signerAdd: 'Add',\n            signerEdit: 'Edit',\n            editTip: 'Note: Chinese name please input Pinyin, such as San Zhang',\n            inputNameTip: 'Please enter your name',\n            inputName: 'Please input English name or Chinese name in Pinyin',\n            signerNameFillTip: 'You will also need to fill in your name',\n            plsInput: 'Please input',\n            plsSelect: 'Please select',\n            customInput: 'Custom input',\n        },\n        signPlaceBySigner: {\n            signGuide: 'Guide to signing',\n            howDragSeal: 'How to drag a stamp',\n            howDragSignature: 'How to drag signatures',\n            iKnow: 'I see',\n            step: {\n                one: 'Step 1: read the contract',\n                two1: 'Step 2: click \"Drag stamp\"',\n                two2: 'Step 2: click \"Drag signature\"',\n                three: 'Step 3: click the \"Sign\" button',\n            },\n            dragSeal: 'Drag stamp',\n            continueDragSeal: 'Continue to drag stamp',\n            dragSignature: 'Drag signature',\n            continueDragSignature: 'Continue to drag signature',\n            dragPlace: 'Drag it here',\n            notRemind: 'Never show again',\n            signTip: {\n                one: 'Step 1: Locate where you need to sign/stamp by clicking \"Start\".',\n                two: 'Step 2: Complete the signature/seal as required by clicking \"Signature/Stamp\".',\n            },\n            finishSignatureBeforeSign: 'Please finish dragging signature/stamp before confirming sign',\n        },\n        continueOperation: {\n            success: 'Success',\n            exitApproval: 'Exit approval',\n            continueApproval: 'Continue approving',\n            next: 'Next contract:',\n            none: 'None',\n            tip: 'Tip',\n            approvalProcess: 'The contract needs {totalNum} people to approve; Currently {passNum} people have approved.',\n            receiver: 'receivers:',\n        },\n    },\n    signTip: {\n        contractDetail: 'Contract Details',\n        downloadBtn: 'Download APP',\n        tips: 'Tip',\n        submit: 'OK',\n        SigningCompleted: 'Sign successfully',\n        submitCompleted: '等待他人处理',\n        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n        noRightSign: '合同正在签署中，当前用户不允许签署操作',\n        noNeedSign: '内部决议合同，已无需签署',\n        ApprovalCompleted: 'Approval succeeded',\n        contractRevoked: 'The contract has been canceled',\n        contractRefused: 'The contract has been refused',\n        linkExpired: 'The link has expired',\n        contractClosed: 'The contract has been closed',\n        approvalReject: 'The contract approval has been rejected',\n        approving: 'The contract is under review',\n        viewContract: 'View contract',\n        viewContractList: 'View Contract List',\n        needMeSign: ' ({num} Pending for Signature)',\n        downloadContract: 'Download contract',\n        sign: 'sign',\n        signed: ' signed',\n        approved: 'approved',\n        approval: 'approval',\n        person: ' person',\n        personHas: ' ',\n        personHave: ' ',\n        personHasnot: ' not ',\n        personsHavenot: ' not ',\n        headsTaskDone: '{num}{has}{done}',\n        headsTaskNotDone: '{num}{not}{done}',\n        taskStatusBetween: ',',\n        cannotReview: 'Cannot review contract',\n        cannotDownload: 'The contract does not support mobile phone downloads. Because the contract is privately stored by the sender, the contract cannot be obtained by BestSign.',\n        privateStorage: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server',\n        beenDeleted: 'Your account has been deleted by the enterprise administrator',\n        unActive: 'Unable to continue to activate account',\n        back: 'back',\n        contratStatusDes: '{key} status: ',\n        contractConditionDes: '{key} condition: ',\n        contractIng: 'Contract in {key}',\n        contractComplete: 'Contract {key} completed',\n        dataProduct: {\n            tip1: '{entName}致各位优质经销商/供应商企业负责人：',\n            tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',\n            btnText: '去向老板分享这个喜讯',\n        },\n        signOnGoing: 'Signing',\n        operate: 'operate',\n        freeContract: 'Upon completing the first contract dispatch, additional contract copies can be obtained for free.',\n        sendContract: 'Send contract',\n        congratulations: 'Congratulations to {name} for completing {num} contract signings,',\n        carbonSaving: 'with an estimated carbon saving of {num}g.',\n        signGift: 'BestSign presents you with {num} corporate contracts (usage period valid until {limit}).',\n        followPublic: 'Follow our WeChat official account to receive contract updates promptly.',\n        congratulationsSingle: 'Congratulations to {name} on the contract signing,',\n        carbonSavingSingle: 'Estimated carbon reduction: 2,002.4g',\n        viewContractTip: 'If you need to change the person who stamps, you can click the \\\"View detail\\\" button to open the contract details page, then click the \\\"Apply for Stamp\\\" button.',\n        congratulationsCn: 'Thank you for choosing e-signature!',\n        carbonSavingSingleCn: 'You have reduced carbon by {num}gCO2e for the Earth',\n        carbonVerification: \"*Scientifically calculated by 'Carbonstop'\",\n    },\n    view: {\n        title: 'View Contract',\n        ok: 'OK',\n        cannotReview: 'Cannot review contract',\n        privateStorage: 'Sender\\'s enterprise uses the contract private storage, but the current network cannot connect to the contract storage server',\n    },\n    prepare: {\n        sealArea: 'Seal here',\n        senderNotice: 'Current contract sender:{entName}',\n        preSetDialogConfirm: 'OK',\n        preSetDialogContact: 'Contact BestSign sales representatives to open your account immediately',\n        preSetDialogInfo: 'When sending a contract, the system will automatically fill in the corresponding signatory information, signing requirements, signing locations, contract description fields, etc. according to the pre-set template',\n        preSetDialogTitle: 'What is a pre-set contract template?',\n        initialValues: 'Pre-set initial values based on contract content',\n        proxyUpload: 'Select contract sender after uploading local documents',\n        signHeaderTitle: 'Add files and signer',\n        step1: 'Step 1',\n        confirmSender: 'Confirmation of sender',\n        step2: 'Step 2',\n        uploadFile: 'Upload a file',\n        step3: 'Step 3',\n        addSigner: 'Add Signer',\n        actionDemo: 'Action demo',\n        next: 'next',\n        isUploadingErr: 'The file has not been uploaded yet. Please continue after the completion.',\n        noUploadFileErr: 'File not uploaded, please continue after uploading',\n        noContractTitleErr: 'The contract name is not filled out, please fill in and continue',\n        contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',\n        expiredDateErr: 'The deadline for signing is incorrect. Please continue to modify it.',\n        noExpiredDateErr: 'Please fill in the signing deadline and continue',\n        noRecipientsErr: 'Add at least one contractor',\n        noAccountErr: 'Account cannot be empty',\n        noUserNameErr: 'Name cannot be empty',\n        noIDNumberErr: 'ID card cannot be empty',\n        noEntNameErr: 'Business name cannot be empty',\n        accountFormatErr: 'Please enter the correct phone number or email address.',\n        userNameFormatErr: 'Please enter the correct name',\n        enterpriseNameErr: 'Please enter the correct company name',\n        idNumberForVerifyErr: 'Please enter the correct ID card',\n        signerErr: 'The signing party is wrong',\n        noSignerErr: 'Please add at least one signer',\n        lackAttachmentNameErr: 'Please fill in the attachment name',\n        repeatRecipientsErr: 'Cannot add signing parties repeatedly when not in sequential order',\n        innerContact: 'Internal contact',\n        outerContact: 'External contact',\n        search: 'Search for',\n        accountSelected: 'Selected account',\n        groupNameAll: 'All',\n        unclassified: 'Unclassified',\n        fileLessThan: 'Please upload files less than {num}M',\n        beExcel: 'Please upload Excel file',\n        usePdf: 'Use PDF files or pictures when uploading',\n        usePdfFile: 'Use PDF files when uploading',\n        fileNameMoreThan: 'File name length more than {num}, has been automatically intercepted for you',\n        needAddSender: 'Your company/you are not set as a signing party. After the contract is sent out, you will not participate in the signing process. Do you need to add yourself as a signing party?',\n        addSender: 'Add as signing party',\n        tip: 'Hint',\n        cancel: 'Cancel',\n    },\n    addReceiver: {\n        English: 'English',\n        Japanese: 'Japanese',\n        Chinese: 'Chinese',\n        Arabic: 'Arabic',\n        setNoticelang: 'Notification Language',\n        limitFaceConfigTip: 'This feature is unavailable due to your contract price being too low. Please contact BestSign for consultation',\n        individual: 'Individual signatory',\n        enterprise: 'Company signatory',\n        addInstructions: 'Add signing instructions',\n        instructionsContent: 'The submitted information is used to help you track the status of contract performance and determine whether business is being performed properly. Once set up, this signatory must submit the information as required',\n        addContractingInfo: 'Submit information of the signatory',\n        contractingInfoContent: 'The submitted information is used to help you check the qualifications of the signatories and determine whether you can start or continue business with them. If the same information has already been submitted by the signatory, it may not be submitted again.',\n        payer: 'Signatory who pays for the contract signing',\n        handWriting: 'Turn on handwriting recognition',\n        realName: 'Handler individual real name authentication required',\n        sameTip: 'Tip: Signing is only allowed when the signatory\\'s company name is exactly the same to the name designated by the sender',\n        proxy: 'Signing notice received by the signatory\\'s reception desk (No specific account to receive the notice)',\n        aboradTip: 'Tips: This is a foreign signatory and with risks in real name authentication. Please verify the identity of the person first.',\n        busRole: 'Business roles',\n        busRoleTip: 'Helps you identify the signatory for easy management',\n        busRolePlaceholder: 'Such as employees/distributors',\n        handWritingTip: 'The signatory will need to sign with a legible handwritten signature.',\n        instructions: 'Add signing instructions for the signatory |  (up to 255 words)',\n        contractingParty: 'Information of the signatory',\n        signerPay: 'This signatory will pay for the contract signing',\n        afterReadingTitle: 'Sign after reading',\n        afterReading: 'The signatory must read and know the content of the contract before signing.',\n        handWritingTips: 'The signatory\\'s handwritten name will be compared with the name specified by the sender or with the real name authentication information in the system before signing',\n        SsTitle: 'Signing with both stamp and signature',\n        SsTip: 'If you need to sign with a company stamp and also with your personal signature at the same time, your individual real name authentication must be completed before signing.',\n        signature: 'Signature',\n        stamp: 'Stamp',\n        Ss: 'Stamp and Signature',\n        mutexError: 'Already set\"{msg}\", please delete the \"{msg}\"  setting first',\n        forceHandWrite: 'Handwritten signature required',\n        faceVerify: 'Must sign with facial verification',\n        attachmentRequired: 'Add attachments to the contract',\n        newAttachmentRequired: 'submit information of the contracting party ',\n        attachmentError: 'The name of attachment cannot be the same to other attachment\\'s',\n        receiver: 'Receiving phone/email |（supports a maximum of 5, can use a semicolon）',\n        orderSignLabel: 'Sequential signing',\n        contactAddress: 'Contact address book',\n        signOrder: 'Sequence',\n        account: 'Account',\n        accountPlaceholder: 'Phone/email (required)',\n        accountReceptionCollection: 'Front desk collection',\n        accountReceptionCollectionTip1: 'Do not know the other party specific account number or the other party has no account number',\n        chooseTransferPerson: 'Transfer contract',\n        accountReceptionCollectionTip2: 'Please choose the front desk to collect',\n        signSubjectPerson: 'Signatory: individual',\n        nameTips: 'Name (optional)',\n        requiredNameTips: 'Name (required for signature identification)',\n        entOperatorNameTips: 'Name (optional)',\n        needAuth: 'Real name required',\n        operatorNeedAuth: 'Handler individual real name authentication required',\n        signSubjectEnt: 'Signatory: enterprise',\n        entNameTips: 'Enterprise name (Optional)',\n        operator: 'Operator',\n        sign: 'Sign',\n        more: 'More',\n        faceFirst: 'Priority sign with facial verification, alternate sign with SMS code verification',\n        faceFirstTips: 'When signing, the system defaults to face verification. When the number of times the brush fails to pass reaches the upper limit of the day, it automatically switches to verification code verification',\n        mustFace: 'Must sign with facial recognition',\n        handWriteNotAllowed: 'Handwritten signature not allowed',\n        mustHandWrite: 'Must sign with handwritten signature',\n        fillIDNumber: 'ID number',\n        fillNoticeCall: 'notification phone',\n        fillNoticeCallTips: 'Fill in the notification phone',\n        addNotice: 'Add private message',\n        attachTips: 'Annex requirements',\n        faceSign: 'Must sign with facial recognition',\n        faceSignTips: 'The user needs to pass the face authentication to complete the signing (the face signing is only supported by the mainland residents)',\n        handWriteNotAllowedTips: 'The user can only select the signature that has been set or use the default font signature to complete the signing',\n        handWriteTips: 'The user needs a handwritten signature to complete the signing',\n        idNumberTips: 'Used for signing identity check',\n        verifyBefore: 'Verify identity before viewing files',\n        verify: 'Verify identidy',\n        verifyTips: 'Up to 20 words',\n        verifyTips2: 'You must provide this verification information to this user',\n        sendToThirdPlatform: 'Send to third-party platforms',\n        platFormName: 'Platform name',\n        fillThirdPlatFormName: 'Please enter a third-party platform name',\n        attach: 'Attachment',\n        attachName: 'Accessory name',\n        exampleID: 'Example: ID card photo',\n        attachInfo: 'Attachment description',\n        attachInfoTips: 'Example: Please upload my ID card photo',\n        addAttachRequire: 'Add attachment requirements',\n        addSignEnt: 'Add signing enterprise',\n        addSignPerson: 'Add signing individual',\n        selectContact: 'Select contact',\n        save: 'Save',\n        searchVerify: 'Query check',\n        fillImageContentTips: 'Please fill in the image content',\n        ok: 'Confirm',\n        findContact: 'Find the following contractors from the contract',\n        signer: 'Signer',\n        signerTips: 'Tip: After selecting the contractor, the platform can help locate the signature and stamp location.',\n        add: 'Add',\n        notAdd: 'Don\\'t add',\n        cc: 'Cc',\n        notNeedAuth: 'No real name required',\n        operatorNotNeedAuth: 'No real name required',\n        extracting: 'Extracting',\n        autoFill: 'Auto-fill signer',\n        failExtracting: 'Not drawn to the signatory',\n        idNumberForVerifyErr: 'Please enter the correct ID card',\n        noAccountErr: 'Account cannot be empty',\n        noUserNameErr: 'Name cannot be empty',\n        noIDNumberErr: 'ID card cannot be empty',\n        noEntNameErr: 'Business name cannot be empty',\n        accountFormatErr: 'Please enter the correct phone number or email address.',\n        enterpriseNameErr: 'Please enter the correct company name',\n        userNameFormatErr: 'Please enter the correct name',\n        riskCues: 'risk warning',\n        riskCuesMsg: 'If the signatory party does not sign the real name, you will need to provide evidence of the identity of the signatory party in the event of a dispute. To avoid risk, please choose real name',\n        confirmBtnText: 'Choose real name',\n        cancelBtnText: 'Choose not to have a real name',\n        attachLengthErr: 'You can only add up to 50 attachment requests to a single signer',\n        collapse: 'Fold',\n        expand: 'Expand',\n        delete: 'Delete',\n        saySomething: 'say something',\n        addImage: 'add pictures',\n        addImageTips: '(Support pdf, word, ,jpg, png format, up to10 uploads)',\n        give: 'to',\n        fileMax: 'Uploads exceed the maximum number',\n        signerLimit: 'Your current version does not support more than {limit} relative signatories.',\n        showExamle: 'View Example Images',\n        downloadExamle: 'Download Example File',\n    },\n    addReceiverGuide: {\n        notRemind: 'Do not remind me next time',\n        sign: 'Signature',\n        entSign: 'Company signature',\n        stamp: 'Stamp',\n        stampSign: 'Stamp and signature',\n        requestSeal: 'Stamp for Business Chedck',\n        'guideTitle': 'How to add a new signatory',\n        'receiverType': 'You need to choose the way the signatory participates in the contract (one of six options).',\n        'asEntSign': 'Signing on behalf of the company:',\n        'sealSub': 'The signatory needs to stamp the contract with the company seal created in the Company console module.',\n        'signatureSub': 'The legal person or senior executive signs the contract on behalf of the enterprise. The enterprise has the right to transfer the contract, making the signer unable to view the contract.',\n        'vipOnly': 'Available in Advanced Edition',\n        'stampSub': 'The signatory can stamp and put handwritten shgnature on the contract.',\n        'confirmSealSub': 'Such as financial statements, confirmation letters and other documents. The signatory shall check before stamping.',\n        'asPersonSign': 'Signed on behalf of an individual.',\n        'asPersonSignTip': 'Individual sign, not on behalf of any company',\n        'asPersonSignDesc': 'Private contracts of the signatory, such as loan contracts, employment and resignation, etc.',\n        'scanSign': 'Signing by scaning the QR code',\n        'scanSignDesc': 'No need to specify the signatory when start the contract. After the contract is created, anyone can scan the QR code/click the link to sign, which is applicable to some scenarios, such as logistics document signing in goods receiving scenario.',\n        'selectSignTypeTip': 'Please select the way the signatory participates in the contract first',\n    },\n    linkContract: {\n        title: 'Relate this contract to another contract',\n        connectMore: 'Relate this contract to more contracts',\n        placeholder: 'Please enter the contract ID',\n        revoke: 'The contract has been cancelled',\n        overdue: 'Overdue and unsigned',\n        approvalNotPassed: 'Approval rejected',\n        reject: 'The contract has been rejected by signatory',\n        signing: 'signing',\n        complete: 'complete',\n        approvaling: 'approval',\n        disconnect: 'Unrelate these contracts',\n        disconnectSuccess: 'disconnect successfully',\n        connectLimit: 'Up to 100 contracts can be related',\n    },\n    field: {\n        fieldTip: {\n            title: 'No signing location indicated',\n            error: 'Notsign with ({type}) in the designated locations in the following contract',\n            add: 'Add fields',\n            continue: 'Continue to send',\n        },\n        accountCharge: {\n            notice: 'The contract is charged by the number of participating signatory',\n            able: 'Can be sent normally',\n            unable: 'Not enough accounts available for use. Please contact BestSign customer service.',\n            notify: 'The contract will notify all contracting parties in English.',\n            noNotify: {\n                1: 'This contract will not issue any signing related notices',\n                2: '(including notification SMS and email for signing, approval, CC, deadline for signing, etc.)',\n            },\n        },\n        ridingStamp: 'Paging Stamp',\n        watermark: 'Watermark',\n        senderSignature: 'Signature of the sealer',\n        optional: 'Optional',\n        decoration: 'Decoration',\n        clickDecoration: '点击合同装饰',\n        sysError: 'System busy, please try later',\n        partedMarkedError: 'You must set the locations of the stamp and signature for the signatory with \"Stamp and signature\"',\n        fieldTitle: '{length} contracts need to be set the signing locations',\n        send: 'Send',\n        contractDispatchApply: 'Apply for a contract',\n        contractNeedYouSign: 'This document needs you to sign',\n        ifSignRightNow: 'Whether to sign it now',\n        signRightNow: 'Sign it now',\n        signLater: 'Sign later',\n        signaturePositionErr: 'Please specify the signing location for each signer',\n        sendSucceed: 'Send successfully',\n        confirm: 'Confirm',\n        cancel: 'Cancel',\n        qrCodeTips: 'After signing the code, you can view the signing details, verify the validity of the signature and whether the contract has been tampered with.',\n        pagesField: 'Page {currentPage}, {totalPages} in total',\n        suitableWidth: 'Suitable for width',\n        signCheck: 'Signature inspection',\n        locateSignaturePosition: 'Locate the signing location',\n        locateTips: 'Can help to quickly locate to the signing location. Currently only supports the first signing location for each signing party',\n        step1: 'Step 1',\n        selectSigner: 'Select signer',\n        step2: 'Step 2',\n        dragSignaturePosition: 'Drag sign position',\n        signingField: 'Signing field',\n        docTitle: 'Document',\n        totalPages: 'Pages: {totalPages}',\n        receiver: 'Receiver',\n        delete: 'Delete',\n        deductPublicNotice: 'If the number of copies of the private contract is insufficient, the contract will be deducted',\n        unlimitedNotice: 'Unlimited use of the contract billing',\n        charge: 'Billing',\n        units: '{num} ',\n        contractToPrivate: 'for private contract',\n        contractToPublic: 'for enterprise contract',\n        costTips: {\n            1: 'Contract for a public contract: a contract with a corporate account in the signatory (excluding the sender)',\n            2: 'Private contract: a contract that does not have a corporate account in the signatory (excluding the sender)',\n            3: 'The number of billing shares is calculated based on the number of copies of the file.',\n            4: 'Billing Shares = File Copies × Batch Import Users',\n        },\n        costInfo: 'After successfully sending the contract, the fee will be deducted immediately, and it will not be refunded for contract completion, expiration, withdrawal, or refusal.',\n        toCharge: 'Recharge',\n        contractNeedCharge: {\n            1: 'The number of available contracts is insufficient and cannot be sent',\n            2: 'The number of available contracts is insufficient. Please contact the main administrator to top up.',\n        },\n        chooseApprover: 'Select approver:',\n        nextStep: 'Next',\n        submitApproval: 'Submit for approval',\n        autoSendAfterApproval: '*After approval, the contract will be sent automatically',\n        chooseApprovalFlow: 'Please select an approval flow',\n        completeApprovalFlow: 'The approval process you submitted is incomplete, please complete and resubmit',\n        viewPrivateLetter: 'View messages',\n        addPrivateLetter: 'Add messages',\n        append: 'Add',\n        privateLetter: 'Messages',\n        signNeedKnow: 'Signing notice',\n        maximum5M: 'Please upload documents smaller than 5M',\n        uploadServerFailure: 'Failed to upload to server',\n        uploadFailure: 'Upload failed',\n        pager: 'Page',\n        seal: 'Seal',\n        signature: 'Signature',\n        signDate: 'Date of signing',\n        text: 'Text',\n        date: 'Date',\n        qrCode: 'QR code',\n        number: 'Digits',\n        dynamicTable: 'Dynamic table',\n        terms: 'Contract terms',\n        checkBox: 'Checkbox',\n        radioBox: 'Radio button',\n        image: 'Picture',\n    },\n    addressBook: {\n        innerMember: {\n            title: 'Internal members of the company',\n            tips: 'Adjust enterprise membership information',\n            operation: 'To the console',\n        },\n        outerContacts: {\n            title: 'External contacts',\n            tips: 'Invite your business partners to finish the real names authentication in advance for rapid business development',\n            operation: 'Invite your business partners',\n        },\n        myContacts: {\n            title: 'My contacts',\n            tips: 'Modify contact, to ensure that the signatory information accurate',\n            operation: 'To the user center',\n        },\n        selected: 'Selected accounts',\n        search: 'Search',\n        loadMore: 'Load more',\n        end: 'All loading completed',\n    },\n    dataBoxInvite: {\n        title: 'Invite your business partners',\n        step1: 'Share the link with your business partner to create a company account in advance',\n        step2: 'Company account created through the link / QR code will appear in your address book',\n        step3: 'Manage your partners in \"File +\" module',\n        imgName: 'Share the QR code',\n        saveQrcode: 'Save the QR code',\n        copy: 'Copy',\n        copySuccess: 'Copy success',\n        copyFailed: 'COPY FAILED',\n    },\n    shareView: {\n        title: 'Forward for Review',\n        account: 'Phone Number/Email',\n        role: 'Reviewer Role',\n        note: 'Remarks',\n        link: 'Link:',\n        saveQrcode: 'Save Wechat Mini-Program Code',\n        signerMessage: 'Signer\\'s Message',\n        rolePlaceholder: 'e.g., company legal advisor, department head, etc.',\n        notePlaceholder: 'Leave a message for the reviewer, within 200 characters',\n        generateLink: 'Generate Link',\n        regenerateLink: 'Regenerate Link',\n        inputAccount: 'Please enter your phone number or email',\n        inputCorrectAccount: 'Please enter correct phone number or email',\n        accountInputTip: 'To ensure the link opens correctly, please enter the contract reviewer\\'s information accurately',\n        shareLinkTip1: 'Save the Wechat mini-program code or ',\n        shareLinkTip: 'copy the link to share with the reviewer',\n        linkTip1: 'The contents of the contract text are confidential, please do not disclose them unless necessary',\n        linkTip2: 'The link is valid for 2 days; after regenerating the link, the previous link will automatically become invalid',\n    },\n    recoverSpecialSeal: {\n        title: '印章无法使用',\n        description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',\n        description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',\n        postRecover: '申请恢复印章',\n        note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',\n        requestSend: '恢复申请提交成功',\n    },\n    paperSign: {\n        title: '使用纸质方式签署',\n        stepText: ['下一步', '确认纸质签', '确定'],\n        needUploadFile: '请先上传扫描件',\n        uploadError: '上传失败',\n        cancel: '取消',\n        downloadPaperFile: '获取纸质签文件',\n        step0: {\n            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n            address: '邮寄地址：',\n            contactName: '接收人姓名：',\n            contactPhone: '接收人联系方式：',\n            defaultValue: '请通过线下方式向发件方索取',\n        },\n        step1: {\n            title0: '第一步：下载&打印纸质合同',\n            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n            title1: '第二步：加盖印章',\n            title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],\n        },\n        step2: {\n            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n            uploadFile: '上传扫描件',\n            getCodeVerify: '获取合同签署校验',\n            isUploading: '上传中...',\n        },\n    },\n    allowPaperSignDialog: {\n        title: 'Allow Paper Signatures',\n        content: 'This contract is issued by {senderName} to {receiverName}, allowing for signing in paper form.',\n        tip: \"You may choose to compile the contract documents at present and print them, subsequently to be signed and sealed offline by the individual responsible for the company's seal, in accordance with formal procedures.\",\n        icon: 'Conversion to paper-based signing >>',\n        goSign: 'To sign electronically',\n        cancel: 'Cancel',\n    },\n    sealInconformityDialog: {\n        errorSeal: {\n            title: '印章提示',\n            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n            tip1: '检测到有企业印章与企业名称：',\n            tip2: '是否要继续使用当前印章图片？',\n            tip3: '根据发件方要求，您需要使用企业名称为：',\n            tip4: '的印章',\n            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n            tip6: '不匹配，请确保印章符合发件方要求。',\n            guide: '如何上传正确的印章 >>',\n            next: '继续使用',\n            tip7: 'And your seal name does not comply with the specifications, with the words “{keyWord}” .',\n            tip8: 'Detected that the seal name does not comply with the specifications and contains the words “{keyWord}”. Do you want to continue using it?',\n        },\n        exampleSeal: {\n            title: '上传印章图案方式',\n            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],\n        },\n        confirm: '确认',\n        cancel: '取消',\n    },\n    addSealDialog: {\n        title: 'Add Stamp Image',\n        dec1: 'Please select a stamp image from your local folder (formats: JPG, JPEG, PNG, etc.). The system will merge this stamp image into the current contract.',\n        dec2: 'After that, you need to click the \"Sign\" button to pass the signing verification to complete the stamping.',\n        updateNewSeal: 'Upload New Stamp',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,cAAc,EAAE;IACZC,iBAAiB,EAAE,0BAA0B;IAC7CC,aAAa,EAAE;MACXC,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE;EACT,CAAC;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,iQAAiQ;IAChRJ,QAAQ,EAAE,UAAU;IACpBK,uBAAuB,EAAE,6BAA6B;IACtDC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE;MACdC,WAAW,EAAE,0BAA0B;MACvCC,UAAU,EAAE,cAAc;MAC1BC,QAAQ,EAAE,mCAAmC;MAC7CC,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE,yGAAyG;MAC/GC,IAAI,EAAE,iGAAiG;MACvGC,IAAI,EAAE,2IAA2I;MACjJC,IAAI,EAAE,uOAAuO;MAC7OC,IAAI,EAAE,uEAAuE;MAC7EC,IAAI,EAAE,iDAAiD;MACvDC,QAAQ,EAAE,aAAa;MACvBC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,cAAc;MACpBC,YAAY,EAAE,0BAA0B;MACxCC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACff,WAAW,EAAE;IACjB,CAAC;IACDgB,UAAU,EAAE,UAAU;IACtBC,aAAa,EAAE,UAAU;IACzBC,gBAAgB,EAAE,sCAAsC;IACxDC,YAAY,EAAE,0BAA0B;IACxCC,wBAAwB,EAAE,kCAAkC;IAC5DC,mBAAmB,EAAE,6CAA6C;IAClEC,qBAAqB,EAAE,yBAAyB;IAChDC,UAAU,EAAE,eAAe;IAC3BC,gBAAgB,EAAE,yCAAyC;IAC3DC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,IAAI;IACZjC,MAAM,EAAE,QAAQ;IAChBE,IAAI,EAAE,MAAM;IACZgC,OAAO,EAAE,8CAA8C;IACvDC,eAAe,EAAE,uHAAuH;IACxIC,qBAAqB,EAAE,oCAAoC;IAC3DC,iBAAiB,EAAE,+CAA+C;IAClEC,cAAc,EAAE,gCAAgC;IAChDC,uBAAuB,EAAE,8BAA8B;IACvDC,8BAA8B,EAAE,2BAA2B;IAC3DC,kBAAkB,EAAE,0EAA0E;IAC9FC,cAAc,EAAE,iBAAiB;IACjCC,aAAa,EAAE,2EAA2E;IAC1FC,UAAU,EAAE,eAAe;IAC3BC,cAAc,EAAE,kFAAkF;IAClGC,aAAa,EAAE,yBAAyB;IACxCC,qBAAqB,EAAE,8CAA8C;IACrEC,OAAO,EAAE,2DAA2D;IACpEC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,gBAAgB;IACxBC,GAAG,EAAE,YAAY;IACjBC,OAAO,EAAE,oBAAoB;IAC7BC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,oBAAoB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,MAAM;IACxBC,YAAY,EAAE,wBAAwB;IACtCC,WAAW,EAAE,gIAAgI;IAC7IC,cAAc,EAAE,gBAAgB;IAChCC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,uDAAuD;IACxEC,eAAe,EAAE,gEAAgE;IACjFC,iBAAiB,EAAE,yDAAyD;IAC5EC,aAAa,EAAE,uLAAuL;IACtMC,WAAW,EAAE,iCAAiC;IAC9CC,UAAU,EAAE,yCAAyC;IACrDC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,mBAAmB;IAC7BC,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE,oBAAoB;IACjCC,cAAc,EAAE,iCAAiC;IACjDC,YAAY,EAAE,4DAA4D;IAC1EC,SAAS,EAAE,4DAA4D;IACvEC,YAAY,EAAE,oHAAoH;IAClIC,gBAAgB,EAAE,8DAA8D;IAChFC,WAAW,EAAE,cAAc;IAC3BC,gBAAgB,EAAE,qhBAAqhB,GACniB,2SAA2S;IAC/SC,YAAY,EAAE,wHAAwH;IACtIC,SAAS,EAAE,KAAK;IAChBzE,UAAU,EAAE,cAAc;IAC1B0E,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,oCAAoC;IAChDC,UAAU,EAAE,kGAAkG;IAC9GC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE,kCAAkC;IACjDC,eAAe,EAAE,yFAAyF;IAC1GC,SAAS,EAAE,mCAAmC;IAC9CC,SAAS,EAAE,+CAA+C;IAC1DC,SAAS,EAAE,gGAAgG;IAC3GC,OAAO,EAAE,gDAAgD;IACzDC,aAAa,EAAE,6DAA6D;IAC5EC,IAAI,EAAE,MAAM;IACZC,UAAU,EAAE,aAAa;IACzBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,mBAAmB;IAC9BC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,cAAc;IACtBC,YAAY,EAAE,iCAAiC;IAC/CC,cAAc,EAAE,kBAAkB;IAClCC,cAAc,EAAE,kCAAkC;IAClDC,YAAY,EAAE,2BAA2B;IACzCC,aAAa,EAAE,yBAAyB;IACxCC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,YAAY;IAC1BC,eAAe,EAAE,+BAA+B;IAChDC,WAAW,EAAE,sCAAsC;IACnDC,SAAS,EAAE,oBAAoB;IAC/BC,MAAM,EAAE,UAAU;IAClBC,cAAc,EAAE,qDAAqD;IACrEC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,eAAe;IACtBC,YAAY,EAAE,sCAAsC;IACpDC,QAAQ,EAAE,gCAAgC;IAC1CC,aAAa,EAAE,gCAAgC;IAC/CC,OAAO,EAAE,0EAA0E;IACnFC,SAAS,EAAE,gBAAgB;IAC3BC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE,SAAS;IACpBC,oBAAoB,EAAE,qBAAqB;IAC3CC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE,QAAQ;IACpBC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,wCAAwC;IACnDC,YAAY,EAAE,sHAAsH;IACpIC,cAAc,EAAE,yCAAyC;IACzDC,iBAAiB,EAAE,+EAA+E;IAClGC,aAAa,EAAE,QAAQ;IACvBC,gBAAgB,EAAE,kKAAkK;IACpLC,YAAY,EAAE,YAAY;IAC1BC,mBAAmB,EAAE,mBAAmB;IACxCC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,mBAAmB;IACzCC,SAAS,EAAE,SAAS;IACpBC,EAAE,EAAE,GAAG;IACPC,cAAc,EAAE,SAAS;IACzBC,YAAY,EAAE,cAAc;IAC5BC,MAAM,EAAE,qCAAqC;IAC7CC,YAAY,EAAE,cAAc;IAC5BC,UAAU,EAAE,uBAAuB;IACnCC,YAAY,EAAE,0BAA0B;IACxCC,EAAE,EAAE,IAAI;IACRC,gBAAgB,EAAE,oBAAoB;IACtCC,WAAW,EAAE,yBAAyB;IACtCC,OAAO,EAAE,eAAe;IACxBC,cAAc,EAAE;MACZ,CAAC,EAAE,gDAAgD;MACnD,CAAC,EAAE,6DAA6D;MAChEC,OAAO,EAAE;IACb,CAAC;IACDC,UAAU,EAAE,mCAAmC;IAC/CC,UAAU,EAAE,uCAAuC;IACnDC,UAAU,EAAE,uBAAuB;IACnCC,eAAe,EAAE,mCAAmC;IACpDC,kBAAkB,EAAE,gCAAgC;IACpDC,OAAO,EAAE,mBAAmB;IAC5BC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,oBAAoB;IAChCC,MAAM,EAAE,eAAe;IACvBC,eAAe,EAAE,kBAAkB;IACnCC,aAAa,EAAE,kBAAkB;IACjCC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,oBAAoB;IAC/BC,SAAS,EAAE,WAAW;IACtBC,cAAc,EAAE,mBAAmB;IACnCC,QAAQ,EAAE,gBAAgB;IAC1BC,mBAAmB,EAAE,gCAAgC;IACrDC,aAAa,EAAE,gBAAgB;IAC/BC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,cAAc;IACxBC,OAAO,EAAE,UAAU;IACnBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,kBAAkB;IAChCC,SAAS,EAAE;MACPC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,2EAA2E;MAClFC,KAAK,EAAE,gCAAgC;MACvCC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,2DAA2D;MAClEC,KAAK,EAAE,0CAA0C;MACjDC,SAAS,EAAE,2FAA2F;MACtGC,SAAS,EAAE,kEAAkE;MAC7EC,SAAS,EAAE,+BAA+B;MAC1CC,SAAS,EAAE,mFAAmF;MAC9FC,WAAW,EAAE,WAAW;MACxBC,iBAAiB,EAAE,SAAS;MAC5BC,YAAY,EAAE,0BAA0B;MACxCC,SAAS,EAAE,oBAAoB;MAC/BC,YAAY,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,QAAQ;IAChBC,gBAAgB,EAAE;MACd;MACAC,iBAAiB,EAAE,2GAA2G;MAC9HC,UAAU,EAAE,uFAAuF;MACnGC,aAAa,EAAE,4EAA4E;MAC3FC,WAAW,EAAE;IACjB,CAAC;IACDC,eAAe,EAAE,kBAAkB;IACnCC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,4CAA4C;IAC9DC,gBAAgB,EAAE,mDAAmD;IACrEC,gBAAgB,EAAE,mDAAmD;IACrEC,gBAAgB,EAAE,6EAA6E;IAC/FC,gBAAgB,EAAE,0EAA0E;IAC5FC,aAAa,EAAE,gCAAgC;IAC/CC,WAAW,EAAE,0BAA0B;IACvCC,uBAAuB,EAAE,iGAAiG;IAC1HC,wBAAwB,EAAE,sEAAsE;IAChGC,yBAAyB,EAAE,qEAAqE;IAChGC,qBAAqB,EAAE,sGAAsG;IAC7HC,sBAAsB,EAAE,kFAAkF;IAC1GC,qBAAqB,EAAE,qFAAqF;IAC5GC,eAAe,EAAE,uCAAuC;IACxDC,KAAK,EAAE,OAAO;IACdC,iBAAiB,EAAE,sBAAsB;IACzCC,gBAAgB,EAAE,mBAAmB;IACrCC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,8BAA8B;IACzCC,iBAAiB,EAAE,oDAAoD;IACvEC,kBAAkB,EAAE,yFAAyF;IAC7GC,aAAa,EAAE,mGAAmG;IAClHC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,yCAAyC;IACtDC,MAAM,EAAE,SAAS;IACjBC,0BAA0B,EAAE,uIAAuI;IACnKC,kBAAkB,EAAE,2BAA2B;IAC/CC,cAAc,EAAE,kBAAkB;IAClCC,gBAAgB,EAAE,iCAAiC;IACnDC,IAAI,EAAE,OAAO;IACbC,sBAAsB,EAAE,+EAA+E;IACvGC,UAAU,EAAE,4CAA4C;IACxDC,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,eAAe;IAC5BC,aAAa,EAAE,uBAAuB;IACtCC,SAAS,EAAE,iDAAiD;IAC5DC,aAAa,EAAE,gEAAgE;IAC/EC,kBAAkB,EAAE,qEAAqE;IACzFC,kBAAkB,EAAE,qEAAqE;IACzFC,eAAe,EAAE,wCAAwC;IACzDC,kBAAkB,EAAE,uEAAuE;IAC3FC,kBAAkB,EAAE,6DAA6D;IACjFC,uBAAuB,EAAE,4EAA4E;IACrGC,eAAe,EAAE,iFAAiF;IAClGC,4BAA4B,EAAE,mFAAmF;IACjHC,cAAc,EAAE,kBAAkB;IAClCC,yBAAyB,EAAE,mCAAmC;IAC9DC,OAAO,EAAE,oBAAoB;IAC7BC,iBAAiB,EAAE,uGAAuG;IAC1HC,cAAc,EAAE,wDAAwD;IACxEC,iBAAiB,EAAE,yBAAyB;IAC5CC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE,4BAA4B;IAC3CC,eAAe,EAAE,oBAAoB;IACrCC,gBAAgB,EAAE,yGAAyG;IAC3HC,eAAe,EAAE,0BAA0B;IAC3CC,cAAc,EAAE,6BAA6B;IAC7CC,aAAa,EAAE,0BAA0B;IACzCC,8BAA8B,EAAE,yDAAyD;IACzFC,aAAa,EAAE,wGAAwG;IACvHC,eAAe,EAAE,iDAAiD;IAClEC,cAAc,EAAE,kBAAkB;IAClCC,yBAAyB,EAAE,sMAAsM;IACjOC,mBAAmB,EAAE,0IAA0I;IAC/JC,SAAS,EAAE,wBAAwB;IACnCC,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,gBAAgB;IAC9BC,gBAAgB,EAAE,wBAAwB;IAC1CC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,cAAc,EAAE,+BAA+B;IAC/CC,eAAe,EAAE,6BAA6B;IAC9CC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,cAAc;IACnBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,0BAA0B;IAC3CC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;MACN,CAAC,EAAE,mCAAmC;MACtC,CAAC,EAAE,0CAA0C;MAC7C,CAAC,EAAE;IACP,CAAC;IACDC,qBAAqB,EAAE;MACnB,CAAC,EAAE,mEAAmE;MACtE,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,WAAW;IACxBC,SAAS,EAAE,IAAI;IACfC,EAAE,EAAE,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,cAAc;IACvBC,mBAAmB,EAAE,kCAAkC;IACvDC,oBAAoB,EAAE,8DAA8D;IACpFC,sBAAsB,EAAE,mJAAmJ;IAC3KC,aAAa,EAAE,uDAAuD;IACtEC,IAAI,EAAE,OAAO;IACbC,YAAY,EAAE,sBAAsB;IACpCC,uBAAuB,EAAE,yEAAyE;IAClGC,8BAA8B,EAAE,6GAA6G;IAC7IC,gBAAgB,EAAE,gDAAgD;IAClEC,iBAAiB,EAAE,qCAAqC;IACxDC,eAAe,EAAE,wCAAwC;IACzDC,aAAa,EAAE,0CAA0C;IACzDC,aAAa,EAAE,uBAAuB;IACtCC,OAAO,EAAE,wBAAwB;IACjCC,cAAc,EAAE,+CAA+C;IAC/DC,aAAa,EAAE,uDAAuD;IACtEC,MAAM,EAAE,oBAAoB;IAC5BC,QAAQ,EAAE,cAAc;IACxBC,2BAA2B,EAAE,6DAA6D;IAC1FC,mBAAmB,EAAE,sCAAsC;IAC3DC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,OAAO;IACdC,cAAc,EAAE,wBAAwB;IACxCC,eAAe,EAAE,uBAAuB;IACxCC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,WAAW;IACrBC,wBAAwB,EAAE,8CAA8C;IACxEC,sBAAsB,EAAE,gEAAgE;IACxFC,mBAAmB,EAAE,0DAA0D;IAC/EC,UAAU,EAAE,iBAAiB;IAC7BC,wBAAwB,EAAE,2EAA2E;IACrGC,mBAAmB,EAAE,6FAA6F;IAClHC,mBAAmB,EAAE,4BAA4B;IACjDC,YAAY,EAAE,8BAA8B;IAC5CC,OAAO,EAAE,aAAa;IACtBC,YAAY,EAAE,0CAA0C;IACxDC,iBAAiB,EAAE,oCAAoC;IACvDC,WAAW,EAAE,kDAAkD;IAC/DC,YAAY,EAAE,+CAA+C;IAC7DC,sBAAsB,EAAE,qHAAqH;IAC7IC,eAAe,EAAE,oFAAoF;IACrGC,cAAc,EAAE,kHAAkH;IAClIC,YAAY,EAAE,gBAAgB;IAC9BC,gBAAgB,EAAE,oCAAoC;IACtDC,YAAY,EAAE,iBAAiB;IAC/BC,mBAAmB,EAAE,gFAAgF;IACrGC,iBAAiB,EAAE,wBAAwB;IAC3CC,iBAAiB,EAAE,wBAAwB;IAC3CC,eAAe,EAAE,UAAU;IAC3BC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,UAAU;IAC3BC,QAAQ,EAAE,WAAW;IACrBC,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE,kBAAkB;IACjCC,IAAI,EAAE,YAAY;IAClBC,cAAc,EAAE,MAAM;IACtBC,iBAAiB,EAAE,oBAAoB;IACvCC,eAAe,EAAE,WAAW;IAC5BC,YAAY,EAAE,sBAAsB;IACpCC,mBAAmB,EAAE,yDAAyD;IAC9EC,SAAS,EAAE,YAAY;IACvBC,mBAAmB,EAAE,8EAA8E;IACnGC,uBAAuB,EAAE,kGAAkG;IAC3HC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,eAAe;IACxBC,iBAAiB,EAAE,4CAA4C;IAC/DC,eAAe,EAAE,6BAA6B;IAC9CC,cAAc,EAAE,mBAAmB;IACnCC,OAAO,EAAE,YAAY;IACrBC,gBAAgB,EAAE,uCAAuC;IACzDC,gBAAgB,EAAE,gDAAgD;IAClEC,uBAAuB,EAAE,6KAA6K;IACtMC,0BAA0B,EAAE,yNAAyN;IACrPC,iCAAiC,EAAE,yDAAyD;IAC5FC,uBAAuB,EAAE,qHAAqH;IAC9IC,cAAc,EAAE,mBAAmB;IACnCC,OAAO,EAAE;MACLC,EAAE,EAAE,CAAC,QAAQ,EAAE,mDAAmD,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,4DAA4D,CAAC;MACjLC,EAAE,EAAE,+DAA+D;MACnEC,QAAQ,EAAE,cAAc;MACxBC,UAAU,EAAE,uDAAuD;MACnEC,UAAU,EAAE,8BAA8B;MAC1CC,UAAU,EAAE,+BAA+B;MAC3CC,YAAY,EAAE,iIAAiI;MAC/IC,mBAAmB,EAAE;IACzB,CAAC;IACDC,sBAAsB,EAAE,iBAAiB;IACzCC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,YAAY;IACtBC,gBAAgB,EAAE,oBAAoB;IACtCC,qBAAqB,EAAE,kCAAkC;IACzDC,WAAW,EAAE,eAAe;IAC5BC,cAAc,EAAE,iCAAiC;IACjDC,cAAc,EAAE,sEAAsE;IACtFC,mBAAmB,EAAE,mDAAmD;IACxE/Y,GAAG,EAAE,KAAK;IACVgZ,WAAW,EAAE,gCAAgC;IAC7ChP,OAAO,EAAE,SAAS;IAClBiP,kBAAkB,EAAE,uBAAuB;IAC3CC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,+BAA+B;IAC7CC,QAAQ,EAAE,kBAAkB;IAC5BC,eAAe,EAAE,oBAAoB;IACrCC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,qBAAqB;IAClCC,mBAAmB,EAAE;MACjBC,OAAO,EAAE,6IAA6I;MACtJ7R,KAAK,EAAE,oBAAoB;MAC3B8R,iBAAiB,EAAE,oBAAoB;MACvCC,gBAAgB,EAAE;IACtB,CAAC;IACDC,SAAS,EAAE,kBAAkB;IAC7BC,eAAe,EAAE,kBAAkB;IACnCC,WAAW,EAAE,eAAe;IAC5BC,OAAO,EAAE,kBAAkB;IAC3BC,cAAc,EAAE,kBAAkB;IAClCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EAChB,CAAC;EACDC,MAAM,EAAE;IACJC,cAAc,EAAE,iFAAiF;IACjGC,cAAc,EAAE,sCAAsC;IACtDC,cAAc,EAAE,6BAA6B;IAC7CC,cAAc,EAAE,wEAAwE;IACxFC,cAAc,EAAE,uDAAuD;IACvEC,cAAc,EAAE,mCAAmC;IACnDC,cAAc,EAAE,oBAAoB;IACpCC,kBAAkB,EAAE,SAAS;IAC7BC,0BAA0B,EAAE,SAAS;IACrCC,eAAe,EAAE,iCAAiC;IAClDC,qBAAqB,EAAE,UAAU;IACjCC,UAAU,EAAE,OAAO;IACnBC,WAAW,EAAE,kBAAkB;IAC/BC,WAAW,EAAE,oHAAoH;IACjIC,KAAK,EAAE,MAAM;IACbC,gBAAgB,EAAE,MAAM;IACxBC,aAAa,EAAE,MAAM;IACrBC,kBAAkB,EAAE,YAAY;IAChCC,kBAAkB,EAAE,aAAa;IACjCC,UAAU,EAAE;MACR3T,KAAK,EAAE,MAAM;MACb7H,GAAG,EAAE,mBAAmB;MACxByb,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE,0BAA0B;IACxCC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,kBAAkB;IACtBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,eAAe;IAC1Brb,IAAI,EAAE,wVAAwV;IAC9VC,IAAI,EAAE,oUAAoU;IAC1UC,IAAI,EAAE,mGAAmG;IACzGC,IAAI,EAAE,+BAA+B;IACrCC,IAAI,EAAE,gGAAgG;IACtGC,IAAI,EAAE,mCAAmC;IACzCib,IAAI,EAAE,6IAA6I;IACnJC,IAAI,EAAE,8EAA8E;IACpFC,IAAI,EAAE,iGAAiG;IACvGC,KAAK,EAAE,0EAA0E;IACjFC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,yCAAyC;IAChDC,KAAK,EAAE,wHAAwH;IAC/HC,KAAK,EAAE,0EAA0E;IACjFC,MAAM,EAAE,sBAAsB;IAC9BC,SAAS,EAAE,sBAAsB;IACjCC,UAAU,EAAE,wDAAwD;IACpEC,OAAO,EAAE,4VAA4V;IACrWC,OAAO,EAAE,0SAA0S;IACnTC,OAAO,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACJC,UAAU,EAAE,iBAAiB;IAC7BC,oBAAoB,EAAE,sBAAsB;IAC5CC,aAAa,EAAE,yBAAyB;IACxCC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,iJAAiJ;IAC5JC,YAAY,EAAE,qKAAqK;IACnLC,UAAU,EAAE,iBAAiB;IAC7BC,aAAa,EAAE,2BAA2B;IAC1CC,oBAAoB,EAAE,mGAAmG;IACzHC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,UAAU;IAClBC,kBAAkB,EAAE,qBAAqB;IACzCC,OAAO,EAAE;MACL7X,IAAI,EAAE,MAAM;MACZ8X,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,2DAA2D;MACpEC,YAAY,EAAE,wBAAwB;MACtCC,SAAS,EAAE,qDAAqD;MAChEC,iBAAiB,EAAE,yCAAyC;MAC5DC,QAAQ,EAAE,cAAc;MACxBpC,SAAS,EAAE,eAAe;MAC1BqC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACfC,SAAS,EAAE,kBAAkB;MAC7BC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,wBAAwB;MAC1CC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACF/e,GAAG,EAAE,2BAA2B;QAChCgf,IAAI,EAAE,4BAA4B;QAClCC,IAAI,EAAE,gCAAgC;QACtC/e,KAAK,EAAE;MACX,CAAC;MACDgf,QAAQ,EAAE,YAAY;MACtBC,gBAAgB,EAAE,wBAAwB;MAC1CC,aAAa,EAAE,gBAAgB;MAC/BC,qBAAqB,EAAE,4BAA4B;MACnDC,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,kBAAkB;MAC7BC,OAAO,EAAE;QACLxf,GAAG,EAAE,kEAAkE;QACvEC,GAAG,EAAE;MACT,CAAC;MACDgO,yBAAyB,EAAE;IAC/B,CAAC;IACDwR,iBAAiB,EAAE;MACfC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,eAAe;MAC7BC,gBAAgB,EAAE,oBAAoB;MACtCC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZzf,GAAG,EAAE,KAAK;MACV0f,eAAe,EAAE,4FAA4F;MAC7GC,QAAQ,EAAE;IACd;EACJ,CAAC;EACDR,OAAO,EAAE;IACLS,cAAc,EAAE,kBAAkB;IAClCC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,KAAK;IACX9d,MAAM,EAAE,IAAI;IACZ+d,gBAAgB,EAAE,mBAAmB;IACrCC,eAAe,EAAE,QAAQ;IACzBC,UAAU,EAAE,uBAAuB;IACnCC,WAAW,EAAE,qBAAqB;IAClCC,UAAU,EAAE,cAAc;IAC1BC,iBAAiB,EAAE,oBAAoB;IACvCC,eAAe,EAAE,gCAAgC;IACjDC,eAAe,EAAE,+BAA+B;IAChDC,WAAW,EAAE,sBAAsB;IACnCC,cAAc,EAAE,8BAA8B;IAC9CC,cAAc,EAAE,yCAAyC;IACzDC,SAAS,EAAE,8BAA8B;IACzCC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,oBAAoB;IACtCC,UAAU,EAAE,gCAAgC;IAC5CC,gBAAgB,EAAE,mBAAmB;IACrC7gB,IAAI,EAAE,MAAM;IACZ8gB,MAAM,EAAE,SAAS;IACjBvd,QAAQ,EAAE,UAAU;IACpBwd,QAAQ,EAAE,UAAU;IACpB7d,MAAM,EAAE,SAAS;IACjB8d,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,GAAG;IACfC,YAAY,EAAE,OAAO;IACrBC,cAAc,EAAE,OAAO;IACvBC,aAAa,EAAE,kBAAkB;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,GAAG;IACtB7d,YAAY,EAAE,wBAAwB;IACtC8d,cAAc,EAAE,4JAA4J;IAC5KC,cAAc,EAAE,+HAA+H;IAC/IC,WAAW,EAAE,+DAA+D;IAC5EC,QAAQ,EAAE,wCAAwC;IAClDC,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,gBAAgB;IAClCC,oBAAoB,EAAE,mBAAmB;IACzCC,WAAW,EAAE,mBAAmB;IAChCC,gBAAgB,EAAE,0BAA0B;IAC5CC,WAAW,EAAE;MACTvhB,IAAI,EAAE,6BAA6B;MACnCC,IAAI,EAAE,8DAA8D;MACpEuhB,OAAO,EAAE;IACb,CAAC;IACDC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,mGAAmG;IACjHC,YAAY,EAAE,eAAe;IAC7BC,eAAe,EAAE,mEAAmE;IACpFC,YAAY,EAAE,4CAA4C;IAC1DC,QAAQ,EAAE,0FAA0F;IACpGC,YAAY,EAAE,0EAA0E;IACxFC,qBAAqB,EAAE,oDAAoD;IAC3EC,kBAAkB,EAAE,sCAAsC;IAC1DC,eAAe,EAAE,qKAAqK;IACtLC,iBAAiB,EAAE,qCAAqC;IACxDC,oBAAoB,EAAE,qDAAqD;IAC3EC,kBAAkB,EAAE;EACxB,CAAC;EACD/d,IAAI,EAAE;IACF4C,KAAK,EAAE,eAAe;IACtBob,EAAE,EAAE,IAAI;IACRvf,YAAY,EAAE,wBAAwB;IACtC+d,cAAc,EAAE;EACpB,CAAC;EACDyB,OAAO,EAAE;IACLC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,mCAAmC;IACjDC,mBAAmB,EAAE,IAAI;IACzBC,mBAAmB,EAAE,yEAAyE;IAC9FC,gBAAgB,EAAE,sNAAsN;IACxOC,iBAAiB,EAAE,sCAAsC;IACzDC,aAAa,EAAE,kDAAkD;IACjEC,WAAW,EAAE,wDAAwD;IACrEC,eAAe,EAAE,sBAAsB;IACvCC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,wBAAwB;IACvCC,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE,eAAe;IAC3BC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,YAAY;IACvBC,UAAU,EAAE,aAAa;IACzB1E,IAAI,EAAE,MAAM;IACZ2E,cAAc,EAAE,2EAA2E;IAC3FC,eAAe,EAAE,oDAAoD;IACrEC,kBAAkB,EAAE,kEAAkE;IACtFC,eAAe,EAAE,iFAAiF;IAClGC,cAAc,EAAE,sEAAsE;IACtFC,gBAAgB,EAAE,kDAAkD;IACpEC,eAAe,EAAE,6BAA6B;IAC9CC,YAAY,EAAE,yBAAyB;IACvCC,aAAa,EAAE,sBAAsB;IACrCC,aAAa,EAAE,yBAAyB;IACxCC,YAAY,EAAE,+BAA+B;IAC7CC,gBAAgB,EAAE,yDAAyD;IAC3EC,iBAAiB,EAAE,+BAA+B;IAClDC,iBAAiB,EAAE,uCAAuC;IAC1DC,oBAAoB,EAAE,kCAAkC;IACxDC,SAAS,EAAE,4BAA4B;IACvCC,WAAW,EAAE,gCAAgC;IAC7CC,qBAAqB,EAAE,oCAAoC;IAC3DC,mBAAmB,EAAE,oEAAoE;IACzFC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,kBAAkB;IAChCC,MAAM,EAAE,YAAY;IACpBC,eAAe,EAAE,kBAAkB;IACnCC,YAAY,EAAE,KAAK;IACnBC,YAAY,EAAE,cAAc;IAC5BvQ,YAAY,EAAE,sCAAsC;IACpDwQ,OAAO,EAAE,0BAA0B;IACnCC,MAAM,EAAE,0CAA0C;IAClDC,UAAU,EAAE,8BAA8B;IAC1CC,gBAAgB,EAAE,8EAA8E;IAChGC,aAAa,EAAE,mLAAmL;IAClMC,SAAS,EAAE,sBAAsB;IACjCjmB,GAAG,EAAE,MAAM;IACXD,MAAM,EAAE;EACZ,CAAC;EACDmmB,WAAW,EAAE;IACTC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,uBAAuB;IACtCC,kBAAkB,EAAE,gHAAgH;IACpIC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE,mBAAmB;IAC/BC,eAAe,EAAE,0BAA0B;IAC3CC,mBAAmB,EAAE,wNAAwN;IAC7OC,kBAAkB,EAAE,qCAAqC;IACzDC,sBAAsB,EAAE,mQAAmQ;IAC3RC,KAAK,EAAE,6CAA6C;IACpDC,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,sDAAsD;IAChEC,OAAO,EAAE,0HAA0H;IACnIC,KAAK,EAAE,wGAAwG;IAC/GC,SAAS,EAAE,+HAA+H;IAC1IC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,sDAAsD;IAClEC,kBAAkB,EAAE,gCAAgC;IACpDC,cAAc,EAAE,uEAAuE;IACvFC,YAAY,EAAE,iEAAiE;IAC/EC,gBAAgB,EAAE,8BAA8B;IAChDC,SAAS,EAAE,kDAAkD;IAC7DC,iBAAiB,EAAE,oBAAoB;IACvCC,YAAY,EAAE,8EAA8E;IAC5FC,eAAe,EAAE,wKAAwK;IACzLC,OAAO,EAAE,uCAAuC;IAChDC,KAAK,EAAE,6KAA6K;IACpL9hB,SAAS,EAAE,WAAW;IACtB+hB,KAAK,EAAE,OAAO;IACdC,EAAE,EAAE,qBAAqB;IACzBC,UAAU,EAAE,8DAA8D;IAC1EC,cAAc,EAAE,gCAAgC;IAChDC,UAAU,EAAE,oCAAoC;IAChDC,kBAAkB,EAAE,iCAAiC;IACrDC,qBAAqB,EAAE,8CAA8C;IACrEC,eAAe,EAAE,kEAAkE;IACnF7I,QAAQ,EAAE,uEAAuE;IACjF8I,cAAc,EAAE,oBAAoB;IACpCC,cAAc,EAAE,sBAAsB;IACtCC,SAAS,EAAE,UAAU;IACrBrlB,OAAO,EAAE,SAAS;IAClBslB,kBAAkB,EAAE,wBAAwB;IAC5CC,0BAA0B,EAAE,uBAAuB;IACnDC,8BAA8B,EAAE,8FAA8F;IAC9HC,oBAAoB,EAAE,mBAAmB;IACzCC,8BAA8B,EAAE,yCAAyC;IACzEC,iBAAiB,EAAE,uBAAuB;IAC1CC,QAAQ,EAAE,iBAAiB;IAC3BC,gBAAgB,EAAE,8CAA8C;IAChEC,mBAAmB,EAAE,iBAAiB;IACtCC,QAAQ,EAAE,oBAAoB;IAC9BC,gBAAgB,EAAE,sDAAsD;IACxEC,cAAc,EAAE,uBAAuB;IACvCC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE,UAAU;IACpBxpB,IAAI,EAAE,MAAM;IACZypB,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,mFAAmF;IAC9FC,aAAa,EAAE,0MAA0M;IACzNC,QAAQ,EAAE,mCAAmC;IAC7CC,mBAAmB,EAAE,mCAAmC;IACxDC,aAAa,EAAE,sCAAsC;IACrDC,YAAY,EAAE,WAAW;IACzBC,cAAc,EAAE,oBAAoB;IACpCC,kBAAkB,EAAE,gCAAgC;IACpDC,SAAS,EAAE,qBAAqB;IAChCC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,mCAAmC;IAC7CC,YAAY,EAAE,uIAAuI;IACrJC,uBAAuB,EAAE,oHAAoH;IAC7IC,aAAa,EAAE,gEAAgE;IAC/EC,YAAY,EAAE,iCAAiC;IAC/CC,YAAY,EAAE,sCAAsC;IACpDC,MAAM,EAAE,iBAAiB;IACzBC,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,6DAA6D;IAC1EC,mBAAmB,EAAE,+BAA+B;IACpDC,YAAY,EAAE,eAAe;IAC7BC,qBAAqB,EAAE,0CAA0C;IACjEC,MAAM,EAAE,YAAY;IACpBC,UAAU,EAAE,gBAAgB;IAC5BC,SAAS,EAAE,wBAAwB;IACnCC,UAAU,EAAE,wBAAwB;IACpCC,cAAc,EAAE,yCAAyC;IACzDC,gBAAgB,EAAE,6BAA6B;IAC/CC,UAAU,EAAE,wBAAwB;IACpCC,aAAa,EAAE,wBAAwB;IACvCC,aAAa,EAAE,gBAAgB;IAC/BzU,IAAI,EAAE,MAAM;IACZ0U,YAAY,EAAE,aAAa;IAC3BC,oBAAoB,EAAE,kCAAkC;IACxD1I,EAAE,EAAE,SAAS;IACb2I,WAAW,EAAE,kDAAkD;IAC/DpgB,MAAM,EAAE,QAAQ;IAChBqgB,UAAU,EAAE,qGAAqG;IACjHC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,YAAY;IACpBC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,uBAAuB;IACpCC,mBAAmB,EAAE,uBAAuB;IAC5CC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,kBAAkB;IAC5BC,cAAc,EAAE,4BAA4B;IAC5CpH,oBAAoB,EAAE,kCAAkC;IACxDP,YAAY,EAAE,yBAAyB;IACvCC,aAAa,EAAE,sBAAsB;IACrCC,aAAa,EAAE,yBAAyB;IACxCC,YAAY,EAAE,+BAA+B;IAC7CC,gBAAgB,EAAE,yDAAyD;IAC3EE,iBAAiB,EAAE,uCAAuC;IAC1DD,iBAAiB,EAAE,+BAA+B;IAClDuH,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,gMAAgM;IAC7MC,cAAc,EAAE,kBAAkB;IAClCC,aAAa,EAAE,gCAAgC;IAC/CC,eAAe,EAAE,kEAAkE;IACnFC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,QAAQ;IAChB3rB,MAAM,EAAE,QAAQ;IAChB4rB,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE,cAAc;IACxBC,YAAY,EAAE,wDAAwD;IACtEC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,mCAAmC;IAC5CC,WAAW,EAAE,+EAA+E;IAC5FC,UAAU,EAAE,qBAAqB;IACjCC,cAAc,EAAE;EACpB,CAAC;EACDC,gBAAgB,EAAE;IACdnO,SAAS,EAAE,4BAA4B;IACvCjf,IAAI,EAAE,WAAW;IACjBqtB,OAAO,EAAE,mBAAmB;IAC5BrF,KAAK,EAAE,OAAO;IACdsF,SAAS,EAAE,qBAAqB;IAChCC,WAAW,EAAE,2BAA2B;IACxC,YAAY,EAAE,4BAA4B;IAC1C,cAAc,EAAE,6FAA6F;IAC7G,WAAW,EAAE,mCAAmC;IAChD,SAAS,EAAE,wGAAwG;IACnH,cAAc,EAAE,4LAA4L;IAC5M,SAAS,EAAE,+BAA+B;IAC1C,UAAU,EAAE,wEAAwE;IACpF,gBAAgB,EAAE,oHAAoH;IACtI,cAAc,EAAE,oCAAoC;IACpD,iBAAiB,EAAE,+CAA+C;IAClE,kBAAkB,EAAE,8FAA8F;IAClH,UAAU,EAAE,gCAAgC;IAC5C,cAAc,EAAE,qPAAqP;IACrQ,mBAAmB,EAAE;EACzB,CAAC;EACDC,YAAY,EAAE;IACV5lB,KAAK,EAAE,0CAA0C;IACjD6lB,WAAW,EAAE,wCAAwC;IACrDtsB,WAAW,EAAE,8BAA8B;IAC3CusB,MAAM,EAAE,iCAAiC;IACzCC,OAAO,EAAE,sBAAsB;IAC/BC,iBAAiB,EAAE,mBAAmB;IACtCC,MAAM,EAAE,6CAA6C;IACrDC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,0BAA0B;IACtCC,iBAAiB,EAAE,yBAAyB;IAC5CC,YAAY,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE;MACNzmB,KAAK,EAAE,+BAA+B;MACtC0mB,KAAK,EAAE,6EAA6E;MACpFzC,GAAG,EAAE,YAAY;MACjBhsB,QAAQ,EAAE;IACd,CAAC;IACD0uB,aAAa,EAAE;MACXC,MAAM,EAAE,kEAAkE;MAC1EC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE,kFAAkF;MAC1FC,MAAM,EAAE,8DAA8D;MACtEC,QAAQ,EAAE;QACN,CAAC,EAAE,0DAA0D;QAC7D,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,WAAW;IACtBC,eAAe,EAAE,yBAAyB;IAC1C7V,QAAQ,EAAE,UAAU;IACpB8V,UAAU,EAAE,YAAY;IACxBC,eAAe,EAAE,QAAQ;IACzBC,QAAQ,EAAE,+BAA+B;IACzCC,iBAAiB,EAAE,oGAAoG;IACvHC,UAAU,EAAE,yDAAyD;IACrEnsB,IAAI,EAAE,MAAM;IACZosB,qBAAqB,EAAE,sBAAsB;IAC7CC,mBAAmB,EAAE,iCAAiC;IACtDC,cAAc,EAAE,wBAAwB;IACxCC,YAAY,EAAE,aAAa;IAC3BC,SAAS,EAAE,YAAY;IACvBC,oBAAoB,EAAE,qDAAqD;IAC3EC,WAAW,EAAE,mBAAmB;IAChC5lB,OAAO,EAAE,SAAS;IAClBjK,MAAM,EAAE,QAAQ;IAChB8vB,UAAU,EAAE,iJAAiJ;IAC7JC,UAAU,EAAE,2CAA2C;IACvDC,aAAa,EAAE,oBAAoB;IACnCC,SAAS,EAAE,sBAAsB;IACjCC,uBAAuB,EAAE,6BAA6B;IACtDC,UAAU,EAAE,+HAA+H;IAC3ItM,KAAK,EAAE,QAAQ;IACf1W,YAAY,EAAE,eAAe;IAC7B4W,KAAK,EAAE,QAAQ;IACfqM,qBAAqB,EAAE,oBAAoB;IAC3CC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,qBAAqB;IACjC3Q,QAAQ,EAAE,UAAU;IACpB1e,MAAM,EAAE,QAAQ;IAChBuN,kBAAkB,EAAE,gGAAgG;IACpHR,eAAe,EAAE,uCAAuC;IACxDuiB,MAAM,EAAE,SAAS;IACjBtiB,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,sBAAsB;IACzCC,gBAAgB,EAAE,yBAAyB;IAC3CqiB,QAAQ,EAAE;MACN,CAAC,EAAE,6GAA6G;MAChH,CAAC,EAAE,6GAA6G;MAChH,CAAC,EAAE,uFAAuF;MAC1F,CAAC,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE,yKAAyK;IACnLC,QAAQ,EAAE,UAAU;IACpBC,kBAAkB,EAAE;MAChB,CAAC,EAAE,sEAAsE;MACzE,CAAC,EAAE;IACP,CAAC;IACDC,cAAc,EAAE,kBAAkB;IAClCzrB,QAAQ,EAAE,MAAM;IAChB0rB,cAAc,EAAE,qBAAqB;IACrCC,qBAAqB,EAAE,0DAA0D;IACjFC,kBAAkB,EAAE,gCAAgC;IACpDC,oBAAoB,EAAE,gFAAgF;IACtGC,iBAAiB,EAAE,eAAe;IAClCC,gBAAgB,EAAE,cAAc;IAChCC,MAAM,EAAE,KAAK;IACbC,aAAa,EAAE,UAAU;IACzBzb,YAAY,EAAE,gBAAgB;IAC9B0b,SAAS,EAAE,yCAAyC;IACpDC,mBAAmB,EAAE,4BAA4B;IACjDC,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,MAAM;IACbvrB,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,WAAW;IACtBiF,QAAQ,EAAE,iBAAiB;IAC3BsmB,IAAI,EAAE,MAAM;IACZ1rB,IAAI,EAAE,MAAM;IACZ2rB,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,eAAe;IAC7BC,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACTC,WAAW,EAAE;MACTrqB,KAAK,EAAE,iCAAiC;MACxCiY,IAAI,EAAE,0CAA0C;MAChDqS,SAAS,EAAE;IACf,CAAC;IACDC,aAAa,EAAE;MACXvqB,KAAK,EAAE,mBAAmB;MAC1BiY,IAAI,EAAE,iHAAiH;MACvHqS,SAAS,EAAE;IACf,CAAC;IACDE,UAAU,EAAE;MACRxqB,KAAK,EAAE,aAAa;MACpBiY,IAAI,EAAE,mEAAmE;MACzEqS,SAAS,EAAE;IACf,CAAC;IACDG,QAAQ,EAAE,mBAAmB;IAC7B9M,MAAM,EAAE,QAAQ;IAChB+M,QAAQ,EAAE,WAAW;IACrBC,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE;IACX5qB,KAAK,EAAE,+BAA+B;IACtC+b,KAAK,EAAE,kFAAkF;IACzFE,KAAK,EAAE,qFAAqF;IAC5FE,KAAK,EAAE,yCAAyC;IAChD0O,OAAO,EAAE,mBAAmB;IAC5BC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACPlrB,KAAK,EAAE,oBAAoB;IAC3BvE,OAAO,EAAE,oBAAoB;IAC7B0vB,IAAI,EAAE,eAAe;IACrB3f,IAAI,EAAE,SAAS;IACf4f,IAAI,EAAE,OAAO;IACbN,UAAU,EAAE,+BAA+B;IAC3CO,aAAa,EAAE,mBAAmB;IAClCC,eAAe,EAAE,oDAAoD;IACrEC,eAAe,EAAE,yDAAyD;IAC1EC,YAAY,EAAE,eAAe;IAC7BC,cAAc,EAAE,iBAAiB;IACjCC,YAAY,EAAE,yCAAyC;IACvDC,mBAAmB,EAAE,4CAA4C;IACjEC,eAAe,EAAE,kGAAkG;IACnHC,aAAa,EAAE,uCAAuC;IACtDC,YAAY,EAAE,0CAA0C;IACxDC,QAAQ,EAAE,kGAAkG;IAC5GC,QAAQ,EAAE;EACd,CAAC;EACDC,kBAAkB,EAAE;IAChBjsB,KAAK,EAAE,QAAQ;IACfksB,YAAY,EAAE,qDAAqD;IACnEC,YAAY,EAAE,0CAA0C;IACxDC,WAAW,EAAE,QAAQ;IACrB5gB,IAAI,EAAE,yCAAyC;IAC/C6gB,WAAW,EAAE;EACjB,CAAC;EACD7rB,SAAS,EAAE;IACPR,KAAK,EAAE,UAAU;IACjBssB,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;IAChCC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,MAAM;IACnBt0B,MAAM,EAAE,IAAI;IACZu0B,iBAAiB,EAAE,SAAS;IAC5BC,KAAK,EAAE;MACH1sB,KAAK,EAAE,2BAA2B;MAClC2sB,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAE;IAClB,CAAC;IACD/Q,KAAK,EAAE;MACHgR,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,CAAC,wBAAwB,EAAE,UAAU,CAAC;MAClDC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,oBAAoB;MAChCC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACjDC,UAAU,EAAE,CAAC,8BAA8B,EAAE,6BAA6B;IAC9E,CAAC;IACDnR,KAAK,EAAE;MACHjc,KAAK,EAAE,CAAC,qBAAqB,EAAE,kCAAkC,CAAC;MAClEkc,UAAU,EAAE,OAAO;MACnBmR,aAAa,EAAE,UAAU;MACzBC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,oBAAoB,EAAE;IAClBvtB,KAAK,EAAE,wBAAwB;IAC/BwtB,OAAO,EAAE,gGAAgG;IACzGr1B,GAAG,EAAE,sNAAsN;IAC3Ns1B,IAAI,EAAE,sCAAsC;IAC5CC,MAAM,EAAE,wBAAwB;IAChCx1B,MAAM,EAAE;EACZ,CAAC;EACDy1B,sBAAsB,EAAE;IACpBC,SAAS,EAAE;MACP5tB,KAAK,EAAE,MAAM;MACb7H,GAAG,EAAE,iCAAiC;MACtCU,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,mBAAmB;MACzB20B,KAAK,EAAE,cAAc;MACrBlW,IAAI,EAAE,MAAM;MACZxD,IAAI,EAAE,0FAA0F;MAChGC,IAAI,EAAE;IACV,CAAC;IACD0Z,WAAW,EAAE;MACT9tB,KAAK,EAAE,UAAU;MACjB+tB,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;MACrDC,IAAI,EAAE,CAAC,MAAM,EAAE,oBAAoB,CAAC;MACpCC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC9C,CAAC;IACD9rB,OAAO,EAAE,IAAI;IACbjK,MAAM,EAAE;EACZ,CAAC;EACDg2B,aAAa,EAAE;IACXluB,KAAK,EAAE,iBAAiB;IACxBmuB,IAAI,EAAE,uJAAuJ;IAC7JC,IAAI,EAAE,4GAA4G;IAClHC,aAAa,EAAE;EACnB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}