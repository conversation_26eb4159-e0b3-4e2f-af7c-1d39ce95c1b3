{"ast": null, "code": "export default {\n  home: 'الصفحة الرئيسية',\n  addEnterprise: 'إضافة مؤسسة',\n  lookoverEnterprise: 'عرض جميع المؤسسات',\n  chooseEnterprise: 'اختيار مؤسسة',\n  enterEnterprise: 'يرجى إدخال اسم المؤسسة',\n  inApproval: 'قيد الموافقة',\n  copyright: 'جميع الحقوق محفوظة',\n  record: 'رقم تسجيل ICP: 浙ICP备14031930号',\n  chooseSignType: 'اختيار طريقة التوقيع',\n  agreeProtocal: 'الموافقة على الاتفاقية',\n  ok: 'فهمت',\n  newVersionTip: 'تنبيه النسخة الجديدة',\n  notice: 'إعلان',\n  clickToLook: 'انقر للعرض',\n  bizManage: 'دعم الإدارة الموحدة لحقول الأعمال للمؤسسات',\n  protocalUpdate: 'تحديث سياسة الخصوصية',\n  startUse: 'بدء الاستخدام',\n  joinSuccess: 'لقد انضممت بنجاح إلى',\n  contactus: 'اتصل بنا',\n  askOnline: 'استشارة عبر الإنترنت',\n  askWeChat: 'استشارة WeChat | متابعة الحساب العام',\n  downLoad: 'تحميل تطبيق الهاتف المحمول',\n  convenience: 'سواء كنت في المكتب أو المنزل أو في رحلة عمل، يمكن لـ BestSign أن يتيح لك مراجعة وتوقيع وإرسال المستندات في أي وقت وأي مكان.',\n  news: 'مرحباً بك لمتابعة حسابنا العام على WeChat، ومتابعة أحدث أخبارنا.',\n  tip: 'تنبيه',\n  unverify: 'لم يتم التحقق من الهوية بعد، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإجراء التحقق من الهوية أولاً.',\n  isGroupProxyAuth: 'حالة التحقق الحالية لمؤسستك هي تحقق بالوكالة للمجموعة، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإكمال مواد التحقق من الهوية أولاً.',\n  createCompnayP: {\n    p1: 'سيتم إنشاء مؤسسة جديدة لك، يرجى إكمال اسم المؤسسة',\n    p2: 'تقديم مواد المؤسسة ذات الصلة فوراً (للتحقق من الهوية)، والحصول على اسم المؤسسة',\n    p3: 'تخطي التحقق، ملء يدوي',\n    p4: 'اسم المؤسسة غير المتحقق منه يظهر فقط لنفسك'\n  },\n  plzEnterRightEnt: 'يرجى إدخال اسم مؤسسة صحيح',\n  goAuthenticate: 'اذهب للتحقق',\n  keepLaunch: 'متابعة البدء',\n  enterprise: 'مؤسسة',\n  person: 'فرد',\n  usercenter: 'مركز المستخدم',\n  console: 'لوحة تحكم المؤسسة',\n  entAccount: 'حساب مؤسسة',\n  personAccount: 'حساب شخصي',\n  viewAllEnt: 'عرض جميع المؤسسات',\n  addEnt: 'إضافة مؤسسة',\n  createEnt: 'إنشاء مؤسسة',\n  exit: 'خروج',\n  viewDetail: 'انقر لعرض التفاصيل',\n  message: 'رسائل',\n  datainsure: 'حماية البيانات',\n  video: 'فيديو العمليات',\n  help: 'مساعدة',\n  dynamic: 'تحديثات المنتج',\n  contractManage: 'إدارة العقود',\n  templateManage: 'إدارة القوالب',\n  statisticCharts: 'التقارير الإحصائية',\n  authenticating: 'مؤسسة قيد التحقق',\n  ecology: 'حساب مؤسسة من {developerName}',\n  ecologyPerson: 'حساب شخصي من {developerName}',\n  unAuthenticate: 'مؤسسة غير متحقق منها',\n  rejectAuthenticate: 'تم رفض التحقق من الهوية',\n  contactManage: 'يرجى الاتصال بالمدير لتخصيص الصلاحيات لك',\n  noResult: 'لا توجد نتائج',\n  confirm: 'تأكيد',\n  cancel: 'إلغاء',\n  createSuccess: 'تم الإنشاء بنجاح',\n  notifyPhone: 'تقديم هاتف الإشعار',\n  phone: 'هاتف',\n  phonePlaceholder: 'يرجى إدخال رقم هاتف من 11 رقماً',\n  verifyCode: 'رمز التحقق',\n  sixNumberPlaceholder: 'يرجى إدخال 6 أرقام',\n  submit: 'تقديم',\n  phoneInputError: 'يرجى إدخال رقم هاتف صحيح',\n  verCodeInputErr: 'يرجى إدخال رمز تحقق صحيح',\n  setSuccess: 'تم الإعداد بنجاح',\n  offlineContractManage: 'إدارة العقود خارج الإنترنت',\n  phoneSetTip: 'تقديم رقم هاتف الإشعار، يمكن استخدامه لتلقي الرسائل القصيرة الصادرة عن النظام، مثل إشعارات التوقيع ورموز التحقق من التوقيع وغيرها.',\n  noMoreTip: 'عدم التذكير مرة أخرى',\n  phoneSetAfterTip: '(إذا كنت ترغب في الإعداد لاحقاً، يمكنك إضافة رقم الهاتف في [الإشعارات] في [مركز المستخدم])',\n  theEnterprise: 'هذه المؤسسة',\n  you: 'أنت',\n  passAutEntHint: 'سيظهر اسم المؤسسة بعد اجتياز التحقق من هوية المؤسسة',\n  passAutPersonHint: 'سيظهر الاسم الشخصي بعد اجتياز التحقق من الهوية',\n  createEntNow: 'إنشاء مؤسسة الآن',\n  announcement: {\n    pwReset: 'انتهت صلاحية كلمة المرور الخاصة بك، لأمان حسابك، يرجى الذهاب إلى [مركز المستخدم] لتغيير كلمة المرور في أقرب وقت'\n  },\n  partAuthSearch: 'هل تريد البحث عن حالة التحقق من هوية الطرف الآخر قبل إرسال العقد؟'\n};", "map": {"version": 3, "names": ["home", "addEnterprise", "lookoverEnterprise", "chooseEnterprise", "enterEnterprise", "inApproval", "copyright", "record", "chooseSignType", "agreeProtocal", "ok", "newVersionTip", "notice", "clickToLook", "bizManage", "protocalUpdate", "startUse", "joinSuccess", "contactus", "askOnline", "askWeChat", "downLoad", "convenience", "news", "tip", "unverify", "isGroupProxyAuth", "createCompnayP", "p1", "p2", "p3", "p4", "plzEnterRightEnt", "goAuthenticate", "keepLaunch", "enterprise", "person", "usercenter", "console", "entAccount", "personAccount", "viewAllEnt", "addEnt", "createEnt", "exit", "viewDetail", "message", "datainsure", "video", "help", "dynamic", "contractManage", "templateManage", "statistic<PERSON><PERSON>s", "authenticating", "ecology", "<PERSON><PERSON><PERSON>", "unAuthenticate", "rejectAuthenticate", "contactManage", "noResult", "confirm", "cancel", "createSuccess", "notifyPhone", "phone", "phonePlaceholder", "verifyCode", "sixNumberPlaceholder", "submit", "phoneInputError", "verCodeInputErr", "setSuccess", "offlineContractManage", "phoneSetTip", "noMoreTip", "phoneSetAfterTip", "theEnterprise", "you", "passAutEntHint", "passAutPersonHint", "createEntNow", "announcement", "pw<PERSON><PERSON><PERSON>", "partAuthSearch"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/home/<USER>"], "sourcesContent": ["export default {\n    home: 'الصفحة الرئيسية',\n    addEnterprise: 'إضافة مؤسسة',\n    lookoverEnterprise: 'عرض جميع المؤسسات',\n    chooseEnterprise: 'اختيار مؤسسة',\n    enterEnterprise: 'يرجى إدخال اسم المؤسسة',\n    inApproval: 'قيد الموافقة',\n    copyright: 'جميع الحقوق محفوظة',\n    record: 'رقم تسجيل ICP: 浙ICP备14031930号',\n    chooseSignType: 'اختيار طريقة التوقيع',\n    agreeProtocal: 'الموافقة على الاتفاقية',\n    ok: 'فهمت',\n    newVersionTip: 'تنبيه النسخة الجديدة',\n    notice: 'إعلان',\n    clickToLook: 'انقر للعرض',\n    bizManage: 'دعم الإدارة الموحدة لحقول الأعمال للمؤسسات',\n    protocalUpdate: 'تحديث سياسة الخصوصية',\n    startUse: 'بدء الاستخدام',\n    joinSuccess: 'لقد انضممت بنجاح إلى',\n    contactus: 'اتصل بنا',\n    askOnline: 'استشارة عبر الإنترنت',\n    askWeChat: 'استشارة WeChat | متابعة الحساب العام',\n    downLoad: 'تحميل تطبيق الهاتف المحمول',\n    convenience: 'سواء كنت في المكتب أو المنزل أو في رحلة عمل، يمكن لـ BestSign أن يتيح لك مراجعة وتوقيع وإرسال المستندات في أي وقت وأي مكان.',\n    news: 'مرحباً بك لمتابعة حسابنا العام على WeChat، ومتابعة أحدث أخبارنا.',\n    tip: 'تنبيه',\n    unverify: 'لم يتم التحقق من الهوية بعد، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإجراء التحقق من الهوية أولاً.',\n    isGroupProxyAuth: 'حالة التحقق الحالية لمؤسستك هي تحقق بالوكالة للمجموعة، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإكمال مواد التحقق من الهوية أولاً.',\n    createCompnayP: {\n        p1: 'سيتم إنشاء مؤسسة جديدة لك، يرجى إكمال اسم المؤسسة',\n        p2: 'تقديم مواد المؤسسة ذات الصلة فوراً (للتحقق من الهوية)، والحصول على اسم المؤسسة',\n        p3: 'تخطي التحقق، ملء يدوي',\n        p4: 'اسم المؤسسة غير المتحقق منه يظهر فقط لنفسك',\n    },\n    plzEnterRightEnt: 'يرجى إدخال اسم مؤسسة صحيح',\n    goAuthenticate: 'اذهب للتحقق',\n    keepLaunch: 'متابعة البدء',\n    enterprise: 'مؤسسة',\n    person: 'فرد',\n    usercenter: 'مركز المستخدم',\n    console: 'لوحة تحكم المؤسسة',\n    entAccount: 'حساب مؤسسة',\n    personAccount: 'حساب شخصي',\n    viewAllEnt: 'عرض جميع المؤسسات',\n    addEnt: 'إضافة مؤسسة',\n    createEnt: 'إنشاء مؤسسة',\n    exit: 'خروج',\n    viewDetail: 'انقر لعرض التفاصيل',\n    message: 'رسائل',\n    datainsure: 'حماية البيانات',\n    video: 'فيديو العمليات',\n    help: 'مساعدة',\n    dynamic: 'تحديثات المنتج',\n    contractManage: 'إدارة العقود',\n    templateManage: 'إدارة القوالب',\n    statisticCharts: 'التقارير الإحصائية',\n    authenticating: 'مؤسسة قيد التحقق',\n    ecology: 'حساب مؤسسة من {developerName}',\n    ecologyPerson: 'حساب شخصي من {developerName}',\n    unAuthenticate: 'مؤسسة غير متحقق منها',\n    rejectAuthenticate: 'تم رفض التحقق من الهوية',\n    contactManage: 'يرجى الاتصال بالمدير لتخصيص الصلاحيات لك',\n    noResult: 'لا توجد نتائج',\n    confirm: 'تأكيد',\n    cancel: 'إلغاء',\n    createSuccess: 'تم الإنشاء بنجاح',\n    notifyPhone: 'تقديم هاتف الإشعار',\n    phone: 'هاتف',\n    phonePlaceholder: 'يرجى إدخال رقم هاتف من 11 رقماً',\n    verifyCode: 'رمز التحقق',\n    sixNumberPlaceholder: 'يرجى إدخال 6 أرقام',\n    submit: 'تقديم',\n    phoneInputError: 'يرجى إدخال رقم هاتف صحيح',\n    verCodeInputErr: 'يرجى إدخال رمز تحقق صحيح',\n    setSuccess: 'تم الإعداد بنجاح',\n    offlineContractManage: 'إدارة العقود خارج الإنترنت',\n    phoneSetTip: 'تقديم رقم هاتف الإشعار، يمكن استخدامه لتلقي الرسائل القصيرة الصادرة عن النظام، مثل إشعارات التوقيع ورموز التحقق من التوقيع وغيرها.',\n    noMoreTip: 'عدم التذكير مرة أخرى',\n    phoneSetAfterTip: '(إذا كنت ترغب في الإعداد لاحقاً، يمكنك إضافة رقم الهاتف في [الإشعارات] في [مركز المستخدم])',\n    theEnterprise: 'هذه المؤسسة',\n    you: 'أنت',\n    passAutEntHint: 'سيظهر اسم المؤسسة بعد اجتياز التحقق من هوية المؤسسة',\n    passAutPersonHint: 'سيظهر الاسم الشخصي بعد اجتياز التحقق من الهوية',\n    createEntNow: 'إنشاء مؤسسة الآن',\n\n    announcement: {\n        pwReset: 'انتهت صلاحية كلمة المرور الخاصة بك، لأمان حسابك، يرجى الذهاب إلى [مركز المستخدم] لتغيير كلمة المرور في أقرب وقت',\n    },\n    partAuthSearch: 'هل تريد البحث عن حالة التحقق من هوية الطرف الآخر قبل إرسال العقد؟',\n};\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAE,aAAa;EAC5BC,kBAAkB,EAAE,mBAAmB;EACvCC,gBAAgB,EAAE,cAAc;EAChCC,eAAe,EAAE,wBAAwB;EACzCC,UAAU,EAAE,cAAc;EAC1BC,SAAS,EAAE,oBAAoB;EAC/BC,MAAM,EAAE,+BAA+B;EACvCC,cAAc,EAAE,sBAAsB;EACtCC,aAAa,EAAE,wBAAwB;EACvCC,EAAE,EAAE,MAAM;EACVC,aAAa,EAAE,sBAAsB;EACrCC,MAAM,EAAE,OAAO;EACfC,WAAW,EAAE,YAAY;EACzBC,SAAS,EAAE,4CAA4C;EACvDC,cAAc,EAAE,sBAAsB;EACtCC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,sBAAsB;EACnCC,SAAS,EAAE,UAAU;EACrBC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sCAAsC;EACjDC,QAAQ,EAAE,4BAA4B;EACtCC,WAAW,EAAE,6HAA6H;EAC1IC,IAAI,EAAE,kEAAkE;EACxEC,GAAG,EAAE,OAAO;EACZC,QAAQ,EAAE,6GAA6G;EACvHC,gBAAgB,EAAE,4IAA4I;EAC9JC,cAAc,EAAE;IACZC,EAAE,EAAE,mDAAmD;IACvDC,EAAE,EAAE,gFAAgF;IACpFC,EAAE,EAAE,uBAAuB;IAC3BC,EAAE,EAAE;EACR,CAAC;EACDC,gBAAgB,EAAE,2BAA2B;EAC7CC,cAAc,EAAE,aAAa;EAC7BC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,OAAO;EACnBC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,eAAe;EAC3BC,OAAO,EAAE,mBAAmB;EAC5BC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,WAAW;EAC1BC,UAAU,EAAE,mBAAmB;EAC/BC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,aAAa;EACxBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,oBAAoB;EAChCC,OAAO,EAAE,OAAO;EAChBC,UAAU,EAAE,gBAAgB;EAC5BC,KAAK,EAAE,gBAAgB;EACvBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,gBAAgB;EACzBC,cAAc,EAAE,cAAc;EAC9BC,cAAc,EAAE,eAAe;EAC/BC,eAAe,EAAE,oBAAoB;EACrCC,cAAc,EAAE,kBAAkB;EAClCC,OAAO,EAAE,+BAA+B;EACxCC,aAAa,EAAE,8BAA8B;EAC7CC,cAAc,EAAE,sBAAsB;EACtCC,kBAAkB,EAAE,yBAAyB;EAC7CC,aAAa,EAAE,0CAA0C;EACzDC,QAAQ,EAAE,eAAe;EACzBC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,OAAO;EACfC,aAAa,EAAE,kBAAkB;EACjCC,WAAW,EAAE,oBAAoB;EACjCC,KAAK,EAAE,MAAM;EACbC,gBAAgB,EAAE,iCAAiC;EACnDC,UAAU,EAAE,YAAY;EACxBC,oBAAoB,EAAE,oBAAoB;EAC1CC,MAAM,EAAE,OAAO;EACfC,eAAe,EAAE,0BAA0B;EAC3CC,eAAe,EAAE,0BAA0B;EAC3CC,UAAU,EAAE,kBAAkB;EAC9BC,qBAAqB,EAAE,4BAA4B;EACnDC,WAAW,EAAE,oIAAoI;EACjJC,SAAS,EAAE,sBAAsB;EACjCC,gBAAgB,EAAE,4FAA4F;EAC9GC,aAAa,EAAE,aAAa;EAC5BC,GAAG,EAAE,KAAK;EACVC,cAAc,EAAE,qDAAqD;EACrEC,iBAAiB,EAAE,gDAAgD;EACnEC,YAAY,EAAE,kBAAkB;EAEhCC,YAAY,EAAE;IACVC,OAAO,EAAE;EACb,CAAC;EACDC,cAAc,EAAE;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}