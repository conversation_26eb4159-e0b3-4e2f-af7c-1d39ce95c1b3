{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  data() {\n    return {\n      dialogVisible: true,\n      shareData: {},\n      password: '',\n      shareToken: this.$route.params.token\n    };\n  },\n  methods: {\n    handleSubmit() {\n      this.$http.post('/auth-center/user/hubble/topic/login', {\n        token: this.shareToken,\n        password: this.password\n      }).then(res => {\n        const {\n          access_token,\n          refresh_token\n        } = res.data;\n        this.$token.save(access_token, refresh_token);\n        this.$router.push(`/hubble/shareChat/${this.shareData.topicId}`);\n      });\n    }\n  },\n  created() {\n    this.$http(`/web/hubble/ignore/topic/share-info/${this.shareToken}`).then(res => {\n      this.shareData = res.data;\n    });\n  }\n};", "map": {"version": 3, "names": ["data", "dialogVisible", "shareData", "password", "shareToken", "$route", "params", "token", "methods", "handleSubmit", "$http", "post", "then", "res", "access_token", "refresh_token", "$token", "save", "$router", "push", "topicId", "created"], "sources": ["src/views/agent/share/index.vue"], "sourcesContent": ["<template>\n    <el-dialog\n        :visible.sync=\"dialogVisible\"\n        :modal=\"false\"\n        :close-on-click-modal=\"false\"\n        :close-on-press-escape=\"false\"\n        :show-close=\"false\"\n        class=\"hubble-page__share\"\n    >\n        {{ shareData.shareUserName }} 邀请您参与Hubble智能文档 <br>\n        文档名称：{{ shareData.shareFileName }} <br><br>\n        <p>填写分享码查看文档：</p><br>\n        <el-input v-model=\"password\" placeholder=\"请输入6位数字分享码\"></el-input>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"handleSubmit\">确 认</el-button>\n        </div>\n    </el-dialog>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            dialogVisible: true,\n            shareData: {},\n            password: '',\n            shareToken: this.$route.params.token,\n        };\n    },\n    methods: {\n        handleSubmit() {\n            this.$http.post('/auth-center/user/hubble/topic/login', {\n                token: this.shareToken,\n                password: this.password,\n            }).then((res) => {\n                const { access_token, refresh_token } = res.data;\n                this.$token.save(access_token, refresh_token);\n                this.$router.push(`/hubble/shareChat/${this.shareData.topicId}`);\n            });\n        },\n    },\n    created() {\n        this.$http(`/web/hubble/ignore/topic/share-info/${this.shareToken}`).then((res) => {\n            this.shareData = res.data;\n        });\n    },\n};\n</script>\n\n<style lang=\"scss\">\n.hubble-page__share{\n    .el-dialog{\n        width: 400px;\n        top: 50% !important;\n        transform: translate(-50%, -50%) !important;\n    }\n}\n</style>\n"], "mappings": ";AAoBA;EACAA,KAAA;IACA;MACAC,aAAA;MACAC,SAAA;MACAC,QAAA;MACAC,UAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAC;IACA;EACA;EACAC,OAAA;IACAC,aAAA;MACA,KAAAC,KAAA,CAAAC,IAAA;QACAJ,KAAA,OAAAH,UAAA;QACAD,QAAA,OAAAA;MACA,GAAAS,IAAA,CAAAC,GAAA;QACA;UAAAC,YAAA;UAAAC;QAAA,IAAAF,GAAA,CAAAb,IAAA;QACA,KAAAgB,MAAA,CAAAC,IAAA,CAAAH,YAAA,EAAAC,aAAA;QACA,KAAAG,OAAA,CAAAC,IAAA,2BAAAjB,SAAA,CAAAkB,OAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAX,KAAA,6CAAAN,UAAA,IAAAQ,IAAA,CAAAC,GAAA;MACA,KAAAX,SAAA,GAAAW,GAAA,CAAAb,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}