{"ast": null, "code": "export default {\n  mixin: {\n    createSuccessful: '创建成功',\n    setLabel: '设置标签'\n  }\n};", "map": {"version": 3, "names": ["mixin", "createSuccessful", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/mixins/mixins-zh.js"], "sourcesContent": ["export default {\n    mixin: {\n        createSuccessful: '创建成功',\n        setLabel: '设置标签',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,gBAAgB,EAAE,MAAM;IACxBC,QAAQ,EAAE;EACd;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}