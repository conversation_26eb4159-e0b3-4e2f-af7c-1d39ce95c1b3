{"ast": null, "code": "export default {\n  utils: {\n    faceVerified: 'هل أكملت التحقق من الوجه عبر Alipay؟',\n    unDone: 'غير مكتمل',\n    done: 'مكتمل',\n    changeVerifyMethod: 'نظراً لعدم إكمال التحقق من الوجه عبر Alipay، سيتم استخدام طريقة أخرى للتحقق من الوجه لإكمال توقيع العقد.'\n  }\n};", "map": {"version": 3, "names": ["utils", "faceVerified", "unDone", "done", "changeVerifyMethod"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/utils/utils-ar.js"], "sourcesContent": ["export default {\n    utils: {\n        faceVerified: 'هل أكملت التحقق من الوجه عبر Alipay؟',\n        unDone: 'غير مكتمل',\n        done: 'مكتمل',\n        changeVerifyMethod: 'نظراً لعدم إكمال التحقق من الوجه عبر Alipay، سيتم استخدام طريقة أخرى للتحقق من الوجه لإكمال توقيع العقد.',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,YAAY,EAAE,sCAAsC;IACpDC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,OAAO;IACbC,kBAAkB,EAAE;EACxB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}