{"ast": null, "code": "export default {\n  docSlider: {\n    linkContractMap: {\n      notSupportFuctionTip: 'If your company has not activated this function, you can contact customer service to activate it.',\n      dissolveContractTip: 'Are you sure you want to dissolve the associated contract',\n      inputContractNum: 'Please enter the contract number first',\n      linkSuccess: 'Relate successfully',\n      linkExist: 'Relation of these contracts already exist'\n    }\n  },\n  docContentTable: {\n    catchMap: {\n      download: 'Download',\n      reject: 'Refuse',\n      revoke: 'Revoke',\n      delete: 'Delete',\n      cantOperate: 'Unable to {operate} contract',\n      hybridNetHeader: 'The sender\\'s enterprise uses the contract private storage method, but the current network cannot connect to the sender\\'s contract storage server.',\n      hybridNetMsg: 'I suggest you check if the network has been connected to the sender\\'s intranet and try again.',\n      checkNet: 'Please check if the network is connected to the intranet.',\n      hybridNotConnect: 'Reason: Your company uses contract private storage, but the current network cannot connect to the contract storage server.',\n      hybridSuggest: 'Recommended: (1) Check whether the network is normal; (2) Check whether the contract storage server is running normally'\n    },\n    confirm: 'OK',\n    searchAll: 'Select all',\n    isCheckingNet: 'Checking the hybrid cloud network environment',\n    transferSucess: 'transfer successful'\n  },\n  docDialog: {\n    confirm: 'Confirm',\n    cancel: 'Cancel',\n    notCliam: 'the contract has not been claimed',\n    chooseNewOwner: 'please select the new holder',\n    newOwner: 'new holder',\n    originOwner: 'previous holder',\n    contractTransfer: 'contract transfer'\n  }\n};", "map": {"version": 3, "names": ["doc<PERSON>lider", "linkContractMap", "notSupportFuctionTip", "dissolveContractTip", "inputContractNum", "linkSuccess", "linkExist", "docContentTable", "catchMap", "download", "reject", "revoke", "delete", "cantOperate", "hybridNetHeader", "hybridNetMsg", "checkNet", "hybridNotConnect", "hybridSuggest", "confirm", "searchAll", "isCheckingNet", "transferSucess", "docDialog", "cancel", "notCliam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new<PERSON>wner", "origin<PERSON><PERSON>er", "contractTransfer"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/docList/docList-en.js"], "sourcesContent": ["export default {\n    docSlider: {\n        linkContractMap: {\n            notSupportFuctionTip: 'If your company has not activated this function, you can contact customer service to activate it.',\n            dissolveContractTip: 'Are you sure you want to dissolve the associated contract',\n            inputContractNum: 'Please enter the contract number first',\n            linkSuccess: 'Relate successfully',\n            linkExist: 'Relation of these contracts already exist',\n        },\n    },\n    docContentTable: {\n        catchMap: {\n            download: 'Download',\n            reject: 'Refuse',\n            revoke: 'Revoke',\n            delete: 'Delete',\n            cantOperate: 'Unable to {operate} contract',\n            hybridNetHeader: 'The sender\\'s enterprise uses the contract private storage method, but the current network cannot connect to the sender\\'s contract storage server.',\n            hybridNetMsg: 'I suggest you check if the network has been connected to the sender\\'s intranet and try again.',\n            checkNet: 'Please check if the network is connected to the intranet.',\n            hybridNotConnect: 'Reason: Your company uses contract private storage, but the current network cannot connect to the contract storage server.',\n            hybridSuggest: 'Recommended: (1) Check whether the network is normal; (2) Check whether the contract storage server is running normally',\n        },\n        confirm: 'OK',\n        searchAll: 'Select all',\n        isCheckingNet: 'Checking the hybrid cloud network environment',\n        transferSucess: 'transfer successful',\n    },\n    docDialog: {\n        confirm: 'Confirm',\n        cancel: 'Cancel',\n        notCliam: 'the contract has not been claimed',\n        chooseNewOwner: 'please select the new holder',\n        newOwner: 'new holder',\n        originOwner: 'previous holder',\n        contractTransfer: 'contract transfer',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,SAAS,EAAE;IACPC,eAAe,EAAE;MACbC,oBAAoB,EAAE,mGAAmG;MACzHC,mBAAmB,EAAE,2DAA2D;MAChFC,gBAAgB,EAAE,wCAAwC;MAC1DC,WAAW,EAAE,qBAAqB;MAClCC,SAAS,EAAE;IACf;EACJ,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE;MACNC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,8BAA8B;MAC3CC,eAAe,EAAE,qJAAqJ;MACtKC,YAAY,EAAE,gGAAgG;MAC9GC,QAAQ,EAAE,2DAA2D;MACrEC,gBAAgB,EAAE,4HAA4H;MAC9IC,aAAa,EAAE;IACnB,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,+CAA+C;IAC9DC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPJ,OAAO,EAAE,SAAS;IAClBK,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,mCAAmC;IAC7CC,cAAc,EAAE,8BAA8B;IAC9CC,QAAQ,EAAE,YAAY;IACtBC,WAAW,EAAE,iBAAiB;IAC9BC,gBAAgB,EAAE;EACtB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}