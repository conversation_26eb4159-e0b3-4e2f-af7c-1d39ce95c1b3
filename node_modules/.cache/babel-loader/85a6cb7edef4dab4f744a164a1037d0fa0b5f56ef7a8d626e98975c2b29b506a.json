{"ast": null, "code": "export default {\n  consts: {\n    contractAlias: {\n      doc: 'مستند',\n      letter: 'خطاب استفسار',\n      proof: 'تفويض',\n      contract: 'عقد',\n      agreement: 'موافقة',\n      service_report: 'تقرير خدمة'\n    }\n  },\n  pointPositionDoc: {\n    pageTip: 'الصفحة {pageNum} من {pageSize}',\n    nextDoc: 'الانتقال إلى المستند التالي',\n    deleteTip: 'تم الحذف بنجاح'\n  },\n  pointPositionMiniDoc: {\n    document: 'مستند',\n    documentsLength: '{documentsLength} مستندات',\n    pager: 'رقم الصفحة',\n    page: 'صفحة'\n  },\n  pointPositionSite: {\n    watermarkTip: 'سيتم استبدال المعلومات تلقائياً بالمعلومات الحقيقية بعد إرسال العقد',\n    singlePageRidingSealTip: 'لا يمكن إضافة ختم عبر الصفحات للمستند ذو الصفحة الواحدة'\n  }\n};", "map": {"version": 3, "names": ["consts", "contractAlias", "doc", "letter", "proof", "contract", "agreement", "service_report", "pointPositionDoc", "pageTip", "nextDoc", "deleteTip", "pointPositionMiniDoc", "document", "documentsLength", "pager", "page", "pointPositionSite", "watermarkTip", "singlePageRidingSealTip"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/consts/ar.js"], "sourcesContent": ["export default {\n    consts: {\n        contractAlias: {\n            doc: 'مستند',\n            letter: 'خطاب استفسار',\n            proof: 'تفويض',\n            contract: 'عقد',\n            agreement: 'موافقة',\n            service_report: 'تقرير خدمة',\n        },\n    },\n    pointPositionDoc: {\n        pageTip: 'الصفحة {pageNum} من {pageSize}',\n        nextDoc: 'الانتقال إلى المستند التالي',\n        deleteTip: 'تم الحذف بنجاح',\n    },\n    pointPositionMiniDoc: {\n        document: 'مستند',\n        documentsLength: '{documentsLength} مستندات',\n        pager: 'رقم الصفحة',\n        page: 'صفحة',\n    },\n    pointPositionSite: {\n        watermarkTip: 'سيتم استبدال المعلومات تلقائياً بالمعلومات الحقيقية بعد إرسال العقد',\n        singlePageRidingSealTip: 'لا يمكن إضافة ختم عبر الصفحات للمستند ذو الصفحة الواحدة',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,MAAM,EAAE;IACJC,aAAa,EAAE;MACXC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,QAAQ;MACnBC,cAAc,EAAE;IACpB;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdC,OAAO,EAAE,gCAAgC;IACzCC,OAAO,EAAE,6BAA6B;IACtCC,SAAS,EAAE;EACf,CAAC;EACDC,oBAAoB,EAAE;IAClBC,QAAQ,EAAE,OAAO;IACjBC,eAAe,EAAE,2BAA2B;IAC5CC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACfC,YAAY,EAAE,qEAAqE;IACnFC,uBAAuB,EAAE;EAC7B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}