{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm.topicId ? _c(\"div\", {\n    staticClass: \"hubble-page__content\"\n  }, [_c(\"Guide\", {\n    model: {\n      value: _vm.showGuide,\n      callback: function ($$v) {\n        _vm.showGuide = $$v;\n      },\n      expression: \"showGuide\"\n    }\n  }), _c(\"Document\", {\n    staticClass: \"hubble-page__content-document\",\n    attrs: {\n      topicId: _vm.topicId\n    }\n  }), _c(\"ChatView\", {\n    key: _vm.topicId,\n    staticClass: \"hubble-page__content-chat\",\n    attrs: {\n      topicId: _vm.topicId\n    }\n  })], 1) : _vm._e();\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "topicId", "staticClass", "model", "value", "showGuide", "callback", "$$v", "expression", "attrs", "key", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/chat/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.topicId\n    ? _c(\n        \"div\",\n        { staticClass: \"hubble-page__content\" },\n        [\n          _c(\"Guide\", {\n            model: {\n              value: _vm.showGuide,\n              callback: function ($$v) {\n                _vm.showGuide = $$v\n              },\n              expression: \"showGuide\",\n            },\n          }),\n          _c(\"Document\", {\n            staticClass: \"hubble-page__content-document\",\n            attrs: { topicId: _vm.topicId },\n          }),\n          _c(\"ChatView\", {\n            key: _vm.topicId,\n            staticClass: \"hubble-page__content-chat\",\n            attrs: { topicId: _vm.topicId },\n          }),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,OAAO,GACdF,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEH,EAAE,CAAC,OAAO,EAAE;IACVI,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACO,SAAS;MACpBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBT,GAAG,CAACO,SAAS,GAAGE,GAAG;MACrB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFT,EAAE,CAAC,UAAU,EAAE;IACbG,WAAW,EAAE,+BAA+B;IAC5CO,KAAK,EAAE;MAA<PERSON>,OAAO,EAAEH,GAAG,CAACG;IAAQ;EAChC,CAAC,CAAC,EACFF,EAAE,CAAC,UAAU,EAAE;IACbW,GAAG,EAAEZ,GAAG,CAACG,OAAO;IAChBC,WAAW,EAAE,2BAA2B;IACxCO,KAAK,EAAE;MAAER,OAAO,EAAEH,GAAG,CAACG;IAAQ;EAChC,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDH,GAAG,CAACa,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}