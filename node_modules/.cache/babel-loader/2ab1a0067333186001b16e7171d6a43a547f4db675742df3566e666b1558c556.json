{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport dayjs from 'dayjs';\nimport io from 'socket.io-client';\nimport { mapState, mapMutations, mapGetters } from 'vuex';\nimport ChatList from './chatList';\nimport { download } from 'src/common/utils/download.js';\nexport default {\n  components: {\n    ChatList\n  },\n  props: {\n    topicId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      chatInitLoading: true,\n      messages: [],\n      continuousMessages: [],\n      showContinuousChat: false,\n      startChatId: '',\n      startContinuousChatId: '',\n      multiTurnChatBeginId: '',\n      loadChatSize: 20,\n      noMore: false,\n      noMoreContinuous: false,\n      isLoadingMore: false,\n      input: '',\n      typing: false,\n      showDialog: false,\n      prepareTyping: false,\n      typingInterval: null,\n      hasInit: false,\n      initTimeout: null,\n      isEn: false,\n      showSuggestion: false,\n      loadSuggestion: false,\n      suggestions: [],\n      currentSuggestion: {},\n      promptPlugins: [],\n      inputActive: false,\n      currentPrompt: {},\n      currentPromptIndex: 0,\n      languageSwitchVisible: false\n    };\n  },\n  computed: {\n    ...mapGetters('hubble', ['documentId']),\n    ...mapState('hubble', ['preQuote']),\n    btnDisabled() {\n      return this.typing || this.prepareTyping;\n    },\n    currentSuggestions() {\n      return this.suggestions.filter(el => el.questionScope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));\n    },\n    filterPromptList() {\n      if (this.input[0] !== '/') {\n        return [];\n      }\n      const input = this.input.slice(1);\n      const list = this.promptPlugins.filter(el => el.scope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));\n      return input ? list.filter(el => this.containsAllChars(input, el.shortName) || this.containsAllChars(input, el.displayName)) : list;\n    },\n    showPrompt() {\n      return !this.currentPrompt.shortName && this.inputActive && this.filterPromptList.length > 0;\n    },\n    currentMessages() {\n      return this.showContinuousChat ? this.continuousMessages : this.messages;\n    }\n  },\n  watch: {\n    'preQuote.content': {\n      handler(val) {\n        if (val) {\n          this.currentPrompt = {};\n        }\n      }\n    },\n    'currentPrompt.shortName': {\n      handler(val) {\n        this.$nextTick(() => {\n          const input = document.querySelector('.hubble-chat__input').querySelector('.el-input__inner');\n          if (val) {\n            const width = this.$refs.inputPlugin.offsetWidth;\n            input.style.paddingLeft = `${width}px`;\n          } else {\n            input.style.paddingLeft = '10px';\n          }\n        });\n      }\n    },\n    messages: {\n      handler() {\n        this.$nextTick(() => {\n          this.scrollList();\n        });\n      },\n      immediate: true,\n      deep: true\n    },\n    prepareTyping: {\n      handler(val) {\n        const message = this.currentMessages[0] || {};\n        if (val) {\n          this.typingInterval = setInterval(() => {\n            if (message.answer.length === 3) {\n              message.answer = '.';\n            } else {\n              message.answer += '.';\n            }\n          }, 500);\n        } else {\n          clearInterval(this.typingInterval);\n        }\n      },\n      immediate: true\n    },\n    filterPromptList() {\n      this.currentPromptIndex = 0;\n    }\n  },\n  methods: {\n    ...mapMutations('hubble', ['setQuote']),\n    switchEnLanguage(bool) {\n      this.isEn = bool;\n      this.languageSwitchVisible = false;\n    },\n    handleDownloadChat(withChat = true) {\n      const downloadUrl = `/web/hubble/topic/${this.topicId}/documents/${this.documentId}/download${withChat ? '-with-quote-chats' : ''}`;\n      download(downloadUrl);\n    },\n    handleShowContinuousChat(chatId) {\n      this.continuousMessages = [];\n      this.noMoreContinuous = false;\n      this.multiTurnChatBeginId = chatId;\n      this.startContinuousChatId = '';\n      this.showContinuousChat = true;\n      this.getContinuousChat();\n    },\n    getContinuousChat() {\n      this.isLoadingMore = true;\n      this.$http(`/web/hubble/topic/${this.topicId}/${this.multiTurnChatBeginId}/multi-turn-chat-history?startChatId=${this.startContinuousChatId}&number=${this.loadChatSize}`).then(({\n        data: {\n          historyChats\n        }\n      }) => {\n        const topicLength = historyChats.length;\n        this.startContinuousChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';\n        this.noMoreContinuous = topicLength < this.loadChatSize;\n        this.continuousMessages = this.continuousMessages.concat(historyChats.map(el => {\n          return {\n            ...el,\n            chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n            quote: el.questionDocumentQuote?.content || ''\n          };\n        }));\n        // !topicLength && this.continuousMessages.push(this.messages.filter(el => el.chatId === this.multiTurnChatBeginId)[0]);\n      }).finally(() => {\n        this.isLoadingMore = false;\n      });\n    },\n    handleLoadMoreContinuousChat(event) {\n      if (this.noMoreContinuous || this.isLoadingMore) {\n        return;\n      }\n      const list = event.target;\n      if (list.scrollTop + list.scrollHeight === list.clientHeight) {\n        this.getHistoryChats();\n      }\n    },\n    handleKeyDown(event) {\n      if (event.key === 'Enter') {\n        if (this.showPrompt) {\n          event.preventDefault();\n          return this.handleSelectPlugin(this.filterPromptList[this.currentPromptIndex]);\n        }\n        !event.shiftKey && this.handleSendMessage();\n      } else if (event.keyCode === 8 && !this.input && this.currentPrompt.displayName) {\n        this.currentPrompt = {};\n        setTimeout(() => {\n          this.input = '/';\n        }, 50);\n      } else if (event.key === 'ArrowDown') {\n        if (this.showPrompt) {\n          this.currentPromptIndex = (this.currentPromptIndex + 1) % this.filterPromptList.length;\n        }\n      } else if (event.key === 'ArrowUp') {\n        if (this.showPrompt) {\n          this.currentPromptIndex = (this.currentPromptIndex - 1 + this.filterPromptList.length) % this.filterPromptList.length;\n        }\n      }\n    },\n    handleSelectPlugin(plugin) {\n      this.currentPrompt = plugin;\n      this.input = '';\n    },\n    inputBlur() {\n      setTimeout(() => {\n        this.inputActive = false;\n      }, 100);\n    },\n    containsAllChars(str, target) {\n      const regex = new RegExp([...str.toLowerCase()].map(c => `${c}.*`).join(''));\n      return regex.test(target.toLowerCase());\n    },\n    scrollList() {\n      const element = document.querySelector('.hubble-chat__body');\n      element.scrollTop = element.scrollHeight;\n    },\n    clearQuote() {\n      this.setQuote({});\n    },\n    async handleSendMessage() {\n      if (this.input.trim()) {\n        const messages = this.currentMessages;\n        messages.unshift({\n          question: this.input.trim(),\n          questionDocumentQuote: this.preQuote,\n          chatTime: new Date().toLocaleString(),\n          quote: this.preQuote.content,\n          answer: ''\n        });\n        this.sendMessage();\n        this.input = '';\n      }\n    },\n    handleScopeAndPlugin() {\n      if (this.currentPrompt.pluginName) {\n        return {\n          questionScope: this.currentPrompt.scope,\n          enablePlugin: this.currentPrompt.pluginName\n        };\n      } else if (this.currentSuggestion.pluginName) {\n        return {\n          questionScope: this.currentSuggestion.questionScope,\n          enablePlugin: this.currentSuggestion.pluginName\n        };\n      }\n      return {\n        questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT',\n        enablePlugin: this.preQuote.content ? 'FREE_CHAT_QUOTE' : 'FREE_CHAT_DOCUMENT'\n      };\n    },\n    sendMessage() {\n      const scopeAndPlugin = this.handleScopeAndPlugin();\n      this.currentPrompt = {};\n      this.currentSuggestion = {};\n      this.prepareTyping = true;\n      setTimeout(() => {\n        this.clearQuote();\n      }, 50);\n      // 监听连接成功事件\n      // this.socket.emit('ask-pdf-event', {\n      //     ...scopeAndPlugin,\n      //     question: this.input.trim(),\n      //     questionDocumentQuote: this.preQuote,\n      //     chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',\n      //     multiTurnChatBeginId: this.multiTurnChatBeginId,\n      //     ifMultiTurnChat: this.showContinuousChat,\n      // })\n      // return;\n      return this.$http.post(`/web/hubble/topic/${this.topicId}/ask-document`, {\n        ...scopeAndPlugin,\n        question: this.input.trim(),\n        questionDocumentQuote: this.preQuote,\n        chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',\n        multiTurnChatBeginId: this.multiTurnChatBeginId,\n        ifMultiTurnChat: this.showContinuousChat\n      }).then(res => {\n        this.$nextTick(() => {\n          const answer = res.data.answer || '系统繁忙，请稍后再试。';\n          const messages = this.currentMessages;\n          const message = messages[0];\n          this.$nextTick(() => {\n            message.sendContent = answer;\n            this.animateMessage().then(() => {\n              this.$set(messages, 0, {\n                ...messages[0],\n                ...res.data,\n                chatTime: dayjs(res.data.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n                quote: res.data.questionDocumentQuote?.content || ''\n              });\n            });\n          });\n        });\n      }).catch(err => {\n        const message = this.currentMessages[0];\n        this.$nextTick(() => {\n          message.sendContent = err.response?.data.message || '系统繁忙，请稍后再试。';\n          this.animateMessage();\n        });\n      }).finally(() => {\n        this.prepareTyping = false;\n      });\n    },\n    animateMessage() {\n      this.typing = true;\n      const message = this.currentMessages[0];\n      const content = message.sendContent;\n      let i = 0;\n      return new Promise(resolve => {\n        const interval = setInterval(() => {\n          message.answer = content.slice(0, i + 1);\n          this.scrollList();\n          i++;\n          if (i === content.length) {\n            clearInterval(interval);\n            this.typing = false;\n            resolve();\n          }\n        }, 50);\n      });\n    },\n    initSuggestions() {\n      this.loadSuggestion = true;\n      this.$http(`/web/hubble/topic/${this.topicId}/suggest-questions`).then(res => {\n        this.suggestions = res.data.suggestQuestions;\n      }).finally(() => {\n        this.loadSuggestion = false;\n      });\n    },\n    handleSendSuggestion(suggestion) {\n      this.input = suggestion.suggestQuestionContent;\n      this.currentSuggestion = suggestion;\n      this.showSuggestion = false;\n      this.handleSendMessage();\n    },\n    handleSelectQuote(quote) {\n      const {\n        quoteCoordinate,\n        pageNumber\n      } = quote;\n      this.$store.state.hubble.quotePage = pageNumber;\n      this.$store.state.hubble.quoteCoordinate = quoteCoordinate;\n      this.$store.state.hubble.startSelectQuote = true;\n    },\n    initMove() {\n      const chatHandle = this.$refs.move;\n      const chat = this.$refs.chat;\n      let isDragging = false;\n      let lastX;\n      chatHandle.addEventListener('mousedown', e => {\n        isDragging = true;\n        lastX = e.clientX;\n        document.body.style.userSelect = 'none';\n      });\n      document.addEventListener('mousemove', e => {\n        const chatWidth = chat.offsetWidth;\n        const delta = lastX - e.clientX;\n        if (!isDragging || chatWidth <= 450 && delta < 0) {\n          return;\n        }\n        chat.style.width = `${chatWidth + delta}px`;\n        lastX = e.clientX;\n      });\n      document.addEventListener('mouseup', () => {\n        isDragging = false;\n        document.body.style.userSelect = 'unset';\n      });\n    },\n    initPrompt() {\n      const iconMap = {\n        contractDetail: 'el-icon-ssq-Hubblehetongxiangqing',\n        contractContent: 'el-icon-ssq-Hubblehetongneirong'\n      };\n      this.$http(`/web/hubble/topic/${this.topicId}/chat-plugins`).then(res => {\n        this.promptPlugins = res.data.plugins.map(el => {\n          return {\n            ...el,\n            icon: iconMap[el.shortName]\n          };\n        });\n      });\n    },\n    waitChatInit() {\n      return new Promise((resolve, reject) => {\n        this.$http(`/web/hubble/topic/${this.topicId}/if-inited`).then(res => {\n          if (res.data.ifInitSuccess) {\n            this.chatInitLoading = false;\n            resolve();\n          } else {\n            this.initTimeout = setTimeout(() => {\n              this.waitChatInit().then(resolve);\n            }, 3000);\n          }\n        }).catch(() => {\n          reject();\n        });\n      });\n    },\n    async handleScroll(event) {\n      if (this.noMore || this.isLoadingMore) {\n        return;\n      }\n      const list = event.target;\n      if (list.scrollTop + list.scrollHeight === list.clientHeight) {\n        this.getHistoryChats();\n      }\n    },\n    getHistoryChats() {\n      this.isLoadingMore = true;\n      this.$http(`/web/hubble/topic/${this.topicId}/history-chats?startChatId=${this.startChatId}&number=${this.loadChatSize}`).then(({\n        data: {\n          historyChats\n        }\n      }) => {\n        const topicLength = historyChats.length;\n        this.startChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';\n        this.noMore = topicLength < this.loadChatSize;\n        this.messages = this.messages.concat(historyChats.map(el => {\n          return {\n            ...el,\n            chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n            quote: el.questionDocumentQuote?.content || ''\n          };\n        }));\n      }).finally(() => {\n        this.isLoadingMore = false;\n      });\n    },\n    initMessages() {\n      this.startChatId = '';\n      this.messages = [];\n      this.getHistoryChats();\n    },\n    async init() {\n      // !process.env.NODE_ENV.includes('development') && this.initSocket();\n      await this.waitChatInit();\n      this.initMove();\n      this.initPrompt();\n      this.initSuggestions();\n      await this.initMessages();\n    },\n    handleShowSuggestion() {\n      this.showSuggestion = !this.showSuggestion;\n      // if (this.showSuggestion) {\n      //     return (this.showSuggestion = false);\n      // }\n      // this.suggestions = [];\n      // this.loadSuggestion = true;\n      // this.showSuggestion = true;\n      // this.socket.emit('suggest-question-event', {\n      //     quoteContent: this.preQuote.content, // 非必填，引用内容\n      //     leastMultiTurnChatId: this.showContinuousChat ? this.continuousMessages[this.continuousMessages.length - 1]?.chatId || this.multiTurnChatBeginId : '', // 非必填， 最近的连续对话编号\n      //     questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT', // 必填，QUOTE:引用, FULL_DOCUMENT:全文\n      //     chatType: this.showContinuousChat ? 'MULTI_TURN_CHAT' : 'CHAT', // 必填，CHAT:非连续对话, MULTI_TURN_CHAT:连续对话\n      // });\n    },\n    initSocket() {\n      this.socket = io('/web-hubble/ask-pdf', {\n        path: '/web-hubble',\n        reconnectionDelay: 5000,\n        query: {\n          topicId: this.topicId\n        },\n        transports: ['polling', 'websocket'],\n        extraHeaders: {\n          Authorization: 'bearer ' + this.$cookie.get('access_token')\n        }\n      });\n      // 监听连接成功事件\n      this.socket.on('connect', () => {\n        console.log('连接成功');\n      });\n      this.socket.on('connect_error', error => {\n        console.log('连接失败', error);\n      });\n      // this.socket.emit('ask-pdf-event', {});\n      // const _this = this;\n      // this.socket.on('ask-pdf-event', (data) => {\n      //     console.log(data);\n      //     _this.prepareTyping = false;\n      //     _this.$nextTick(() => {\n      //         const answer = data;\n      //         const message = _this.currentMessages[0];\n      //         _this.$nextTick(() => {\n      //             message.answer += answer;\n      //         });\n      //     });\n      // });\n\n      this.socket.on('suggest-question-event', data => {\n        this.loadSuggestion = false;\n        this.suggestions = data.suggestQuestions;\n      });\n    }\n  },\n  mounted() {\n    this.init();\n  },\n  beforeDestroy() {\n    this.clearQuote();\n    this.initTimeout && clearTimeout(this.initTimeout);\n  }\n};", "map": {"version": 3, "names": ["dayjs", "io", "mapState", "mapMutations", "mapGetters", "ChatList", "download", "components", "props", "topicId", "type", "String", "default", "data", "chatInitLoading", "messages", "continuousMessages", "showContinuousChat", "startChatId", "startContinuousChatId", "multiTurnChatBeginId", "loadChatSize", "noMore", "noMoreContinuous", "isLoadingMore", "input", "typing", "showDialog", "prepareTyping", "typingInterval", "hasInit", "initTimeout", "isEn", "showSuggestion", "loadSuggestion", "suggestions", "currentSuggestion", "promptPlugins", "inputActive", "currentPrompt", "currentPromptIndex", "languageSwitchVisible", "computed", "btnDisabled", "currentSuggestions", "filter", "el", "questionScope", "preQuote", "content", "filterPromptList", "slice", "list", "scope", "containsAllChars", "shortName", "displayName", "showPrompt", "length", "currentMessages", "watch", "handler", "val", "$nextTick", "document", "querySelector", "width", "$refs", "inputPlugin", "offsetWidth", "style", "paddingLeft", "scrollList", "immediate", "deep", "message", "setInterval", "answer", "clearInterval", "methods", "switchEnLanguage", "bool", "handleDownloadChat", "with<PERSON><PERSON>", "downloadUrl", "documentId", "handleShowContinuousChat", "chatId", "getContinuousChat", "$http", "then", "historyChats", "topic<PERSON><PERSON>th", "concat", "map", "chatTime", "format", "quote", "questionDocumentQuote", "finally", "handleLoadMoreContinuousChat", "event", "target", "scrollTop", "scrollHeight", "clientHeight", "getHistoryChats", "handleKeyDown", "key", "preventDefault", "handleSelectPlugin", "shift<PERSON>ey", "handleSendMessage", "keyCode", "setTimeout", "plugin", "inputBlur", "str", "regex", "RegExp", "toLowerCase", "c", "join", "test", "element", "clearQuote", "setQuote", "trim", "unshift", "question", "Date", "toLocaleString", "sendMessage", "handleScopeAndPlugin", "pluginName", "enablePlugin", "scopeAndPlugin", "post", "chatLanguage", "ifMultiTurnChat", "res", "send<PERSON><PERSON><PERSON>", "animateMessage", "$set", "catch", "err", "response", "i", "Promise", "resolve", "interval", "initSuggestions", "suggestQuestions", "handleSendSuggestion", "suggestion", "suggest<PERSON><PERSON>ionContent", "handleSelectQuote", "quoteCoordinate", "pageNumber", "$store", "state", "hubble", "quotePage", "startSelectQuote", "initMove", "chat<PERSON><PERSON>le", "move", "chat", "isDragging", "lastX", "addEventListener", "e", "clientX", "body", "userSelect", "chat<PERSON><PERSON><PERSON>", "delta", "initPrompt", "iconMap", "contractDetail", "contractContent", "plugins", "icon", "waitChatInit", "reject", "ifInitSuccess", "handleScroll", "initMessages", "init", "handleShowSuggestion", "initSocket", "socket", "path", "reconnectionDelay", "query", "transports", "extraHeaders", "Authorization", "$cookie", "get", "on", "console", "log", "error", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout"], "sources": ["src/views/agent/chat/chatView/index.vue"], "sourcesContent": ["<template>\n    <div ref=\"chat\" class=\"hubble-chat\">\n        <div class=\"hubble-chat__header\">\n            <span>\n                <img src=\"~views/agent/img/AIBot.png\" class=\"avatar\" alt=\"\">\n                Hubble\n            </span>\n            <div class=\"operate\">\n                <el-popover\n                    trigger=\"click\"\n                    popper-class=\"download-popover\"\n                    v-model=\"languageSwitchVisible\"\n                >\n                    <el-tooltip slot=\"reference\" :open-delay=\"500\" effect=\"dark\" content=\"输出语言切换\" placement=\"top\">\n                        <i class=\"el-icon-ssq-diqiu\" id=\"guide-lang\"></i>\n                    </el-tooltip>\n                    <ul>\n                        <li :class=\"isEn ? '' : 'active'\" @click=\"switchEnLanguage(false)\">中文语言</li>\n                        <li :class=\"isEn ? 'active' : ''\" @click=\"switchEnLanguage(true)\">英文语言</li>\n                    </ul>\n                </el-popover>\n                <el-popover\n                    trigger=\"click\"\n                    popper-class=\"download-popover\"\n                >\n                    <el-tooltip slot=\"reference\" :open-delay=\"500\" effect=\"dark\" content=\"文档下载\" placement=\"top\">\n                        <i class=\"el-icon-ssq--bs-xiazai\"></i>\n                    </el-tooltip>\n                    <ul>\n                        <li @click=\"handleDownloadChat(false)\">源文件</li>\n                        <li @click=\"handleDownloadChat\">源文件 (携带对话)</li>\n                    </ul>\n                </el-popover>\n            </div>\n        </div>\n        <div class=\"hubble-chat__body\" @scroll=\"handleScroll\">\n            <div class=\"hubble-chat__loading\" v-if=\"chatInitLoading\">\n                <img src=\"~views/agent/img/loading2.gif\" alt=\"\"><br>\n                <span>文档解析中</span>\n            </div>\n            <template v-else>\n                <ChatList\n                    :messages=\"messages\"\n                    @selectQuote=\"handleSelectQuote\"\n                    @showContinuousChat=\"handleShowContinuousChat\"\n                ></ChatList>\n                <transition name=\"fade\">\n                    <div v-show=\"showContinuousChat\" class=\"continuous-chat\">\n                        <ChatList\n                            isContinuousChat\n                            :messages=\"continuousMessages\"\n                            @selectQuote=\"handleSelectQuote\"\n                            @loadMore=\"handleLoadMoreContinuousChat\"\n                        ></ChatList>\n                        <div class=\"continuous-exit\">\n                            <span>连续对话模式：</span>\n                            <span class=\"exit\" @click=\"showContinuousChat = false\">退出</span>\n                        </div>\n                    </div>\n                </transition>\n            </template>\n        </div>\n        <div class=\"hubble-chat__dialog\">\n            <transition name=\"slide\">\n                <div class=\"hubble-chat__quote\" v-show=\"!!preQuote.content\">\n                    <div class=\"hubble-chat__quote-header\">\n                        <span>引用内容：</span>\n                        <i class=\"el-icon-ssq-guanbi\" @click=\"clearQuote\"></i>\n                    </div>\n                    <div class=\"hubble-chat__quote-body chat-quote\" @click=\"handleSelectQuote(preQuote)\">{{ preQuote.content }}</div>\n                </div>\n            </transition>\n            <transition name=\"slide\">\n                <div :class=\"`hubble-chat__suggestion ${!preQuote.content ? 'without-quote' : ''}`\" v-show=\"showSuggestion\">\n                    <div class=\"hubble-chat__suggestion-header\">\n                        <i class=\"el-icon-ssq-gaoliangtishiH5\"></i>\n                        建议问题：\n                        <i class=\"el-icon-ssq-guanbi\" @click=\"showSuggestion = false\"></i>\n                    </div>\n                    <ul class=\"hubble-chat__suggestion-list\">\n                        <li\n                            class=\"hubble-chat__suggestion-item\"\n                            v-for=\"(suggestion, i) in currentSuggestions\"\n                            :key=\"i\"\n                            @click=\"handleSendSuggestion(suggestion)\"\n                        >\n                            <span>{{ suggestion.suggestQuestionContent }}</span>\n                            <i class=\"el-icon-ssq-fajianxiang\"></i>\n                        </li>\n                        <li class=\"hubble-chat__suggestion-item empty\" v-if=\"!currentSuggestions.length\">\n                            <img src=\"~views/agent/img/loading.gif\" v-if=\"loadSuggestion\" alt=\"\">\n                            <span v-else>没有数据</span>\n                        </li>\n                    </ul>\n                </div>\n            </transition>\n        </div>\n        <div class=\"hubble-chat__footer\" v-if=\"!chatInitLoading\">\n            <div :class=\"`hubble-chat__input ${currentPrompt.displayName ? 'line-feed' : ''}`\" @keydown=\"handleKeyDown\">\n                <ul class=\"hubble-chat__prompt\" v-show=\"showPrompt\">\n                    <li\n                        v-for=\"(plugin, index) in filterPromptList\"\n                        :class=\"`hubble-chat__prompt-plugin ${ index === currentPromptIndex ? 'active' : '' }`\"\n                        @click=\"handleSelectPlugin(plugin)\"\n                        :key=\"plugin.pluginName\"\n                    >\n                        <i :class=\"plugin.icon\" v-if=\"plugin.icon\"></i>\n                        <div>\n                            <p>\n                                {{ plugin.displayName }}\n                                <span>（{{ plugin.shortName }}）</span>\n                            </p>\n                            <span>{{ plugin.desc }}</span>\n                        </div>\n                    </li>\n                </ul>\n                <span ref=\"inputPlugin\" class=\"hubble-chat__input-plugin\" v-show=\"!!currentPrompt.displayName\">/ {{ currentPrompt.displayName }}</span>\n                <el-input\n                    id=\"guide-text\"\n                    ref=\"inputItem\"\n                    type=\"textarea\"\n                    :autosize=\"{ minRows: 1, maxRows: 6}\"\n                    resize=\"none\"\n                    placeholder=\"请输入您的问题\"\n                    v-model=\"input\"\n                    :disabled=\"btnDisabled\"\n                    @blur=\"inputBlur\"\n                    @focus=\"inputActive = true\"\n                ></el-input>\n            </div>\n            <div class=\"hubble-chat__footer-operate\">\n                <el-tooltip :open-delay=\"500\" effect=\"dark\" content=\"建议问题\" placement=\"top\">\n                    <i id=\"guide-suggestion\" class=\"operate-icon el-icon-ssq-Hubbletishi\" @click=\"handleShowSuggestion\"></i>\n                </el-tooltip>\n                <el-button type=\"primary\" :disabled=\"btnDisabled\" @click=\"handleSendMessage\">发送</el-button>\n            </div>\n        </div>\n        <i ref=\"move\" class=\"hubble-chat__move el-icon-ssq-yidongbiaoqian\"></i>\n    </div>\n</template>\n<script>\nimport dayjs from 'dayjs';\nimport io from 'socket.io-client';\nimport { mapState, mapMutations, mapGetters } from 'vuex';\nimport ChatList from './chatList';\nimport { download } from 'src/common/utils/download.js';\nexport default {\n    components: {\n        ChatList,\n    },\n    props: {\n        topicId: {\n            type: String,\n            default: '',\n        },\n    },\n    data() {\n        return {\n            chatInitLoading: true,\n            messages: [],\n            continuousMessages: [],\n            showContinuousChat: false,\n            startChatId: '',\n            startContinuousChatId: '',\n            multiTurnChatBeginId: '',\n            loadChatSize: 20,\n            noMore: false,\n            noMoreContinuous: false,\n            isLoadingMore: false,\n            input: '',\n            typing: false,\n            showDialog: false,\n            prepareTyping: false,\n            typingInterval: null,\n            hasInit: false,\n            initTimeout: null,\n            isEn: false,\n            showSuggestion: false,\n            loadSuggestion: false,\n            suggestions: [],\n            currentSuggestion: {},\n            promptPlugins: [],\n            inputActive: false,\n            currentPrompt: {},\n            currentPromptIndex: 0,\n            languageSwitchVisible: false,\n        };\n    },\n    computed: {\n        ...mapGetters('hubble', ['documentId']),\n        ...mapState('hubble', ['preQuote']),\n        btnDisabled() {\n            return this.typing || this.prepareTyping;\n        },\n        currentSuggestions() {\n            return this.suggestions.filter(el => el.questionScope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));\n        },\n        filterPromptList() {\n            if (this.input[0] !== '/') {\n                return [];\n            }\n            const input = this.input.slice(1);\n            const list = this.promptPlugins.filter(el => el.scope === (this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT'));\n            return input ? list.filter(el => this.containsAllChars(input, el.shortName) || this.containsAllChars(input, el.displayName)) : list;\n        },\n        showPrompt() {\n            return !this.currentPrompt.shortName && this.inputActive && this.filterPromptList.length > 0;\n        },\n        currentMessages() {\n            return this.showContinuousChat ? this.continuousMessages : this.messages;\n        },\n    },\n    watch: {\n        'preQuote.content': {\n            handler(val) {\n                if (val) {\n                    this.currentPrompt = {};\n                }\n            },\n        },\n        'currentPrompt.shortName': {\n            handler(val) {\n                this.$nextTick(() => {\n                    const input = document.querySelector('.hubble-chat__input').querySelector('.el-input__inner');\n                    if (val) {\n                        const width = this.$refs.inputPlugin.offsetWidth;\n                        input.style.paddingLeft = `${width}px`;\n                    } else {\n                        input.style.paddingLeft = '10px';\n                    }\n                });\n            },\n        },\n        messages: {\n            handler() {\n                this.$nextTick(() => {\n                    this.scrollList();\n                });\n            },\n            immediate: true,\n            deep: true,\n        },\n        prepareTyping: {\n            handler(val) {\n                const message = this.currentMessages[0] || {};\n                if (val) {\n                    this.typingInterval = setInterval(() => {\n                        if (message.answer.length === 3) {\n                            message.answer = '.';\n                        } else {\n                            message.answer += '.';\n                        }\n                    }, 500);\n                } else {\n                    clearInterval(this.typingInterval);\n                }\n            },\n            immediate: true,\n        },\n        filterPromptList() {\n            this.currentPromptIndex = 0;\n        },\n    },\n    methods: {\n        ...mapMutations('hubble', ['setQuote']),\n        switchEnLanguage(bool) {\n            this.isEn = bool;\n            this.languageSwitchVisible = false;\n        },\n        handleDownloadChat(withChat = true) {\n            const downloadUrl = `/web/hubble/topic/${this.topicId}/documents/${this.documentId}/download${withChat ? '-with-quote-chats' : ''}`;\n            download(downloadUrl);\n        },\n        handleShowContinuousChat(chatId) {\n            this.continuousMessages = [];\n            this.noMoreContinuous = false;\n            this.multiTurnChatBeginId = chatId;\n            this.startContinuousChatId = '';\n            this.showContinuousChat = true;\n            this.getContinuousChat();\n        },\n        getContinuousChat() {\n            this.isLoadingMore = true;\n            this.$http(`/web/hubble/topic/${this.topicId}/${this.multiTurnChatBeginId}/multi-turn-chat-history?startChatId=${this.startContinuousChatId}&number=${this.loadChatSize}`).then(({ data: { historyChats } }) => {\n                const topicLength = historyChats.length;\n                this.startContinuousChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';\n                this.noMoreContinuous = topicLength < this.loadChatSize;\n                this.continuousMessages = this.continuousMessages.concat(historyChats.map(el => {\n                    return {\n                        ...el,\n                        chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n                        quote: el.questionDocumentQuote?.content || '',\n                    };\n                }));\n                // !topicLength && this.continuousMessages.push(this.messages.filter(el => el.chatId === this.multiTurnChatBeginId)[0]);\n            }).finally(() => {\n                this.isLoadingMore = false;\n            });\n        },\n        handleLoadMoreContinuousChat(event) {\n            if (this.noMoreContinuous || this.isLoadingMore) {\n                return;\n            }\n            const list = event.target;\n            if (list.scrollTop + list.scrollHeight === list.clientHeight) {\n                this.getHistoryChats();\n            }\n        },\n        handleKeyDown(event) {\n            if (event.key === 'Enter') {\n                if (this.showPrompt) {\n                    event.preventDefault();\n                    return this.handleSelectPlugin(this.filterPromptList[this.currentPromptIndex]);\n                }\n                !event.shiftKey && this.handleSendMessage();\n            } else if (event.keyCode === 8 && !this.input && this.currentPrompt.displayName) {\n                this.currentPrompt = {};\n                setTimeout(() => {\n                    this.input = '/';\n                }, 50);\n            } else if (event.key === 'ArrowDown') {\n                if (this.showPrompt) {\n                    this.currentPromptIndex = (this.currentPromptIndex + 1) % this.filterPromptList.length;\n                }\n            } else if (event.key === 'ArrowUp') {\n                if (this.showPrompt) {\n                    this.currentPromptIndex = (this.currentPromptIndex - 1 + this.filterPromptList.length) % this.filterPromptList.length;\n                }\n            }\n        },\n        handleSelectPlugin(plugin) {\n            this.currentPrompt = plugin;\n            this.input = '';\n        },\n        inputBlur() {\n            setTimeout(() => {\n                this.inputActive = false;\n            }, 100);\n        },\n        containsAllChars(str, target) {\n            const regex = new RegExp([...str.toLowerCase()].map(c => `${c}.*`).join(''));\n            return regex.test(target.toLowerCase());\n        },\n        scrollList() {\n            const element = document.querySelector('.hubble-chat__body');\n            element.scrollTop = element.scrollHeight;\n        },\n        clearQuote() {\n            this.setQuote({});\n        },\n        async handleSendMessage() {\n            if (this.input.trim()) {\n                const messages = this.currentMessages;\n                messages.unshift({\n                    question: this.input.trim(),\n                    questionDocumentQuote: this.preQuote,\n                    chatTime: new Date().toLocaleString(),\n                    quote: this.preQuote.content,\n                    answer: '',\n                });\n                this.sendMessage();\n                this.input = '';\n            }\n        },\n        handleScopeAndPlugin() {\n            if (this.currentPrompt.pluginName) {\n                return {\n                    questionScope: this.currentPrompt.scope,\n                    enablePlugin: this.currentPrompt.pluginName,\n                };\n            } else if (this.currentSuggestion.pluginName) {\n                return {\n                    questionScope: this.currentSuggestion.questionScope,\n                    enablePlugin: this.currentSuggestion.pluginName,\n                };\n            }\n            return {\n                questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT',\n                enablePlugin: this.preQuote.content ? 'FREE_CHAT_QUOTE' : 'FREE_CHAT_DOCUMENT',\n            };\n        },\n        sendMessage() {\n            const scopeAndPlugin = this.handleScopeAndPlugin();\n            this.currentPrompt = {};\n            this.currentSuggestion = {};\n            this.prepareTyping = true;\n            setTimeout(() => {\n                this.clearQuote();\n            }, 50);\n            // 监听连接成功事件\n            // this.socket.emit('ask-pdf-event', {\n            //     ...scopeAndPlugin,\n            //     question: this.input.trim(),\n            //     questionDocumentQuote: this.preQuote,\n            //     chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',\n            //     multiTurnChatBeginId: this.multiTurnChatBeginId,\n            //     ifMultiTurnChat: this.showContinuousChat,\n            // })\n            // return;\n            return this.$http.post(`/web/hubble/topic/${this.topicId}/ask-document`, {\n                ...scopeAndPlugin,\n                question: this.input.trim(),\n                questionDocumentQuote: this.preQuote,\n                chatLanguage: this.isEn ? 'ENGLISH' : 'CHINESE',\n                multiTurnChatBeginId: this.multiTurnChatBeginId,\n                ifMultiTurnChat: this.showContinuousChat,\n            }).then(res => {\n                this.$nextTick(() => {\n                    const answer = res.data.answer || '系统繁忙，请稍后再试。';\n                    const messages = this.currentMessages;\n                    const message = messages[0];\n                    this.$nextTick(() => {\n                        message.sendContent = answer;\n                        this.animateMessage().then(() => {\n                            this.$set(messages, 0, {\n                                ...messages[0],\n                                ...res.data,\n                                chatTime: dayjs(res.data.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n                                quote: res.data.questionDocumentQuote?.content || '',\n                            });\n                        });\n                    });\n                });\n            }).catch(err => {\n                const message = this.currentMessages[0];\n                this.$nextTick(() => {\n                    message.sendContent = err.response?.data.message || '系统繁忙，请稍后再试。';\n                    this.animateMessage();\n                });\n            }).finally(() => {\n                this.prepareTyping = false;\n            });\n        },\n        animateMessage() {\n            this.typing = true;\n            const message = this.currentMessages[0];\n            const content = message.sendContent;\n            let i = 0;\n            return new Promise(resolve => {\n                const interval = setInterval(() => {\n                    message.answer = content.slice(0, i + 1);\n                    this.scrollList();\n                    i++;\n                    if (i === content.length) {\n                        clearInterval(interval);\n                        this.typing = false;\n                        resolve();\n                    }\n                }, 50);\n            });\n        },\n        initSuggestions() {\n            this.loadSuggestion = true;\n            this.$http(`/web/hubble/topic/${this.topicId}/suggest-questions`).then(res => {\n                this.suggestions = res.data.suggestQuestions;\n            }).finally(() => {\n                this.loadSuggestion = false;\n            });\n        },\n        handleSendSuggestion(suggestion) {\n            this.input = suggestion.suggestQuestionContent;\n            this.currentSuggestion = suggestion;\n            this.showSuggestion = false;\n            this.handleSendMessage();\n        },\n        handleSelectQuote(quote) {\n            const { quoteCoordinate, pageNumber } = quote;\n            this.$store.state.hubble.quotePage = pageNumber;\n            this.$store.state.hubble.quoteCoordinate = quoteCoordinate;\n            this.$store.state.hubble.startSelectQuote = true;\n        },\n        initMove() {\n            const chatHandle = this.$refs.move;\n            const chat = this.$refs.chat;\n            let isDragging = false;\n            let lastX;\n\n            chatHandle.addEventListener('mousedown', (e) => {\n                isDragging = true;\n                lastX = e.clientX;\n                document.body.style.userSelect = 'none';\n            });\n            document.addEventListener('mousemove', (e) => {\n                const chatWidth = chat.offsetWidth;\n                const delta = lastX - e.clientX;\n                if (!isDragging || (chatWidth <= 450 && delta < 0)) {\n                    return;\n                }\n                chat.style.width = `${chatWidth + delta}px`;\n                lastX = e.clientX;\n            });\n\n            document.addEventListener('mouseup', () => {\n                isDragging = false;\n                document.body.style.userSelect = 'unset';\n            });\n        },\n        initPrompt() {\n            const iconMap = {\n                contractDetail: 'el-icon-ssq-Hubblehetongxiangqing',\n                contractContent: 'el-icon-ssq-Hubblehetongneirong',\n            };\n            this.$http(`/web/hubble/topic/${this.topicId}/chat-plugins`).then(res => {\n                this.promptPlugins = res.data.plugins.map(el => {\n                    return {\n                        ...el,\n                        icon: iconMap[el.shortName],\n                    };\n                });\n            });\n        },\n        waitChatInit() {\n            return new Promise((resolve, reject) => {\n                this.$http(`/web/hubble/topic/${this.topicId}/if-inited`).then(res => {\n                    if (res.data.ifInitSuccess) {\n                        this.chatInitLoading = false;\n                        resolve();\n                    } else {\n                        this.initTimeout = setTimeout(() => {\n                            this.waitChatInit().then(resolve);\n                        }, 3000);\n                    }\n                }).catch(() => {\n                    reject();\n                });\n            });\n        },\n        async handleScroll(event) {\n            if (this.noMore || this.isLoadingMore) {\n                return;\n            }\n            const list = event.target;\n            if (list.scrollTop + list.scrollHeight === list.clientHeight) {\n                this.getHistoryChats();\n            }\n        },\n        getHistoryChats() {\n            this.isLoadingMore = true;\n            this.$http(`/web/hubble/topic/${this.topicId}/history-chats?startChatId=${this.startChatId}&number=${this.loadChatSize}`).then(({ data: { historyChats } }) => {\n                const topicLength = historyChats.length;\n                this.startChatId = topicLength ? historyChats[historyChats.length - 1].chatId : '';\n                this.noMore = topicLength < this.loadChatSize;\n                this.messages = this.messages.concat(historyChats.map(el => {\n                    return {\n                        ...el,\n                        chatTime: dayjs(el.chatTime).format('YYYY-MM-DD HH:mm:ss'),\n                        quote: el.questionDocumentQuote?.content || '',\n                    };\n                }));\n            }).finally(() => {\n                this.isLoadingMore = false;\n            });\n        },\n        initMessages() {\n            this.startChatId = '';\n            this.messages = [];\n            this.getHistoryChats();\n        },\n        async init() {\n            // !process.env.NODE_ENV.includes('development') && this.initSocket();\n            await this.waitChatInit();\n            this.initMove();\n            this.initPrompt();\n            this.initSuggestions();\n            await this.initMessages();\n        },\n        handleShowSuggestion() {\n            this.showSuggestion = !this.showSuggestion;\n            // if (this.showSuggestion) {\n            //     return (this.showSuggestion = false);\n            // }\n            // this.suggestions = [];\n            // this.loadSuggestion = true;\n            // this.showSuggestion = true;\n            // this.socket.emit('suggest-question-event', {\n            //     quoteContent: this.preQuote.content, // 非必填，引用内容\n            //     leastMultiTurnChatId: this.showContinuousChat ? this.continuousMessages[this.continuousMessages.length - 1]?.chatId || this.multiTurnChatBeginId : '', // 非必填， 最近的连续对话编号\n            //     questionScope: this.preQuote.content ? 'QUOTE' : 'FULL_DOCUMENT', // 必填，QUOTE:引用, FULL_DOCUMENT:全文\n            //     chatType: this.showContinuousChat ? 'MULTI_TURN_CHAT' : 'CHAT', // 必填，CHAT:非连续对话, MULTI_TURN_CHAT:连续对话\n            // });\n        },\n        initSocket() {\n            this.socket = io('/web-hubble/ask-pdf', {\n                path: '/web-hubble',\n                reconnectionDelay: 5000,\n                query: {\n                    topicId: this.topicId,\n                },\n                transports: ['polling', 'websocket'],\n                extraHeaders: {\n                    Authorization: 'bearer ' + this.$cookie.get('access_token'),\n                },\n            });\n            // 监听连接成功事件\n            this.socket.on('connect', () => {\n                console.log('连接成功');\n            });\n            this.socket.on('connect_error', (error) => {\n                console.log('连接失败', error);\n            });\n            // this.socket.emit('ask-pdf-event', {});\n            // const _this = this;\n            // this.socket.on('ask-pdf-event', (data) => {\n            //     console.log(data);\n            //     _this.prepareTyping = false;\n            //     _this.$nextTick(() => {\n            //         const answer = data;\n            //         const message = _this.currentMessages[0];\n            //         _this.$nextTick(() => {\n            //             message.answer += answer;\n            //         });\n            //     });\n            // });\n\n            this.socket.on('suggest-question-event', (data) => {\n                this.loadSuggestion = false;\n                this.suggestions = data.suggestQuestions;\n            });\n        },\n    },\n    mounted() {\n        this.init();\n    },\n    beforeDestroy() {\n        this.clearQuote();\n        this.initTimeout && clearTimeout(this.initTimeout);\n    },\n};\n</script>\n\n<style lang=\"scss\">\n@import './index.scss'\n</style>\n"], "mappings": ";;;AA6IA,OAAAA,KAAA;AACA,OAAAC,EAAA;AACA,SAAAC,QAAA,EAAAC,YAAA,EAAAC,UAAA;AACA,OAAAC,QAAA;AACA,SAAAC,QAAA;AACA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,eAAA;MACAC,QAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,qBAAA;MACAC,oBAAA;MACAC,YAAA;MACAC,MAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,KAAA;MACAC,MAAA;MACAC,UAAA;MACAC,aAAA;MACAC,cAAA;MACAC,OAAA;MACAC,WAAA;MACAC,IAAA;MACAC,cAAA;MACAC,cAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,qBAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAtC,UAAA;IACA,GAAAF,QAAA;IACAyC,YAAA;MACA,YAAAjB,MAAA,SAAAE,aAAA;IACA;IACAgB,mBAAA;MACA,YAAAT,WAAA,CAAAU,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAAC,aAAA,WAAAC,QAAA,CAAAC,OAAA;IACA;IACAC,iBAAA;MACA,SAAAzB,KAAA;QACA;MACA;MACA,MAAAA,KAAA,QAAAA,KAAA,CAAA0B,KAAA;MACA,MAAAC,IAAA,QAAAf,aAAA,CAAAQ,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAAO,KAAA,WAAAL,QAAA,CAAAC,OAAA;MACA,OAAAxB,KAAA,GAAA2B,IAAA,CAAAP,MAAA,CAAAC,EAAA,SAAAQ,gBAAA,CAAA7B,KAAA,EAAAqB,EAAA,CAAAS,SAAA,UAAAD,gBAAA,CAAA7B,KAAA,EAAAqB,EAAA,CAAAU,WAAA,KAAAJ,IAAA;IACA;IACAK,WAAA;MACA,aAAAlB,aAAA,CAAAgB,SAAA,SAAAjB,WAAA,SAAAY,gBAAA,CAAAQ,MAAA;IACA;IACAC,gBAAA;MACA,YAAA1C,kBAAA,QAAAD,kBAAA,QAAAD,QAAA;IACA;EACA;EACA6C,KAAA;IACA;MACAC,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,KAAAvB,aAAA;QACA;MACA;IACA;IACA;MACAsB,QAAAC,GAAA;QACA,KAAAC,SAAA;UACA,MAAAtC,KAAA,GAAAuC,QAAA,CAAAC,aAAA,wBAAAA,aAAA;UACA,IAAAH,GAAA;YACA,MAAAI,KAAA,QAAAC,KAAA,CAAAC,WAAA,CAAAC,WAAA;YACA5C,KAAA,CAAA6C,KAAA,CAAAC,WAAA,MAAAL,KAAA;UACA;YACAzC,KAAA,CAAA6C,KAAA,CAAAC,WAAA;UACA;QACA;MACA;IACA;IACAxD,QAAA;MACA8C,QAAA;QACA,KAAAE,SAAA;UACA,KAAAS,UAAA;QACA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;IACA9C,aAAA;MACAiC,QAAAC,GAAA;QACA,MAAAa,OAAA,QAAAhB,eAAA;QACA,IAAAG,GAAA;UACA,KAAAjC,cAAA,GAAA+C,WAAA;YACA,IAAAD,OAAA,CAAAE,MAAA,CAAAnB,MAAA;cACAiB,OAAA,CAAAE,MAAA;YACA;cACAF,OAAA,CAAAE,MAAA;YACA;UACA;QACA;UACAC,aAAA,MAAAjD,cAAA;QACA;MACA;MACA4C,SAAA;IACA;IACAvB,iBAAA;MACA,KAAAV,kBAAA;IACA;EACA;EACAuC,OAAA;IACA,GAAA5E,YAAA;IACA6E,iBAAAC,IAAA;MACA,KAAAjD,IAAA,GAAAiD,IAAA;MACA,KAAAxC,qBAAA;IACA;IACAyC,mBAAAC,QAAA;MACA,MAAAC,WAAA,6BAAA3E,OAAA,mBAAA4E,UAAA,YAAAF,QAAA;MACA7E,QAAA,CAAA8E,WAAA;IACA;IACAE,yBAAAC,MAAA;MACA,KAAAvE,kBAAA;MACA,KAAAO,gBAAA;MACA,KAAAH,oBAAA,GAAAmE,MAAA;MACA,KAAApE,qBAAA;MACA,KAAAF,kBAAA;MACA,KAAAuE,iBAAA;IACA;IACAA,kBAAA;MACA,KAAAhE,aAAA;MACA,KAAAiE,KAAA,2BAAAhF,OAAA,SAAAW,oBAAA,6CAAAD,qBAAA,gBAAAE,YAAA,IAAAqE,IAAA;QAAA7E,IAAA;UAAA8E;QAAA;MAAA;QACA,MAAAC,WAAA,GAAAD,YAAA,CAAAjC,MAAA;QACA,KAAAvC,qBAAA,GAAAyE,WAAA,GAAAD,YAAA,CAAAA,YAAA,CAAAjC,MAAA,MAAA6B,MAAA;QACA,KAAAhE,gBAAA,GAAAqE,WAAA,QAAAvE,YAAA;QACA,KAAAL,kBAAA,QAAAA,kBAAA,CAAA6E,MAAA,CAAAF,YAAA,CAAAG,GAAA,CAAAhD,EAAA;UACA;YACA,GAAAA,EAAA;YACAiD,QAAA,EAAA/F,KAAA,CAAA8C,EAAA,CAAAiD,QAAA,EAAAC,MAAA;YACAC,KAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EAAAjD,OAAA;UACA;QACA;QACA;MACA,GAAAkD,OAAA;QACA,KAAA3E,aAAA;MACA;IACA;IACA4E,6BAAAC,KAAA;MACA,SAAA9E,gBAAA,SAAAC,aAAA;QACA;MACA;MACA,MAAA4B,IAAA,GAAAiD,KAAA,CAAAC,MAAA;MACA,IAAAlD,IAAA,CAAAmD,SAAA,GAAAnD,IAAA,CAAAoD,YAAA,KAAApD,IAAA,CAAAqD,YAAA;QACA,KAAAC,eAAA;MACA;IACA;IACAC,cAAAN,KAAA;MACA,IAAAA,KAAA,CAAAO,GAAA;QACA,SAAAnD,UAAA;UACA4C,KAAA,CAAAQ,cAAA;UACA,YAAAC,kBAAA,MAAA5D,gBAAA,MAAAV,kBAAA;QACA;QACA,CAAA6D,KAAA,CAAAU,QAAA,SAAAC,iBAAA;MACA,WAAAX,KAAA,CAAAY,OAAA,gBAAAxF,KAAA,SAAAc,aAAA,CAAAiB,WAAA;QACA,KAAAjB,aAAA;QACA2E,UAAA;UACA,KAAAzF,KAAA;QACA;MACA,WAAA4E,KAAA,CAAAO,GAAA;QACA,SAAAnD,UAAA;UACA,KAAAjB,kBAAA,SAAAA,kBAAA,aAAAU,gBAAA,CAAAQ,MAAA;QACA;MACA,WAAA2C,KAAA,CAAAO,GAAA;QACA,SAAAnD,UAAA;UACA,KAAAjB,kBAAA,SAAAA,kBAAA,YAAAU,gBAAA,CAAAQ,MAAA,SAAAR,gBAAA,CAAAQ,MAAA;QACA;MACA;IACA;IACAoD,mBAAAK,MAAA;MACA,KAAA5E,aAAA,GAAA4E,MAAA;MACA,KAAA1F,KAAA;IACA;IACA2F,UAAA;MACAF,UAAA;QACA,KAAA5E,WAAA;MACA;IACA;IACAgB,iBAAA+D,GAAA,EAAAf,MAAA;MACA,MAAAgB,KAAA,OAAAC,MAAA,KAAAF,GAAA,CAAAG,WAAA,IAAA1B,GAAA,CAAA2B,CAAA,OAAAA,CAAA,MAAAC,IAAA;MACA,OAAAJ,KAAA,CAAAK,IAAA,CAAArB,MAAA,CAAAkB,WAAA;IACA;IACAhD,WAAA;MACA,MAAAoD,OAAA,GAAA5D,QAAA,CAAAC,aAAA;MACA2D,OAAA,CAAArB,SAAA,GAAAqB,OAAA,CAAApB,YAAA;IACA;IACAqB,WAAA;MACA,KAAAC,QAAA;IACA;IACA,MAAAd,kBAAA;MACA,SAAAvF,KAAA,CAAAsG,IAAA;QACA,MAAAhH,QAAA,QAAA4C,eAAA;QACA5C,QAAA,CAAAiH,OAAA;UACAC,QAAA,OAAAxG,KAAA,CAAAsG,IAAA;UACA7B,qBAAA,OAAAlD,QAAA;UACA+C,QAAA,MAAAmC,IAAA,GAAAC,cAAA;UACAlC,KAAA,OAAAjD,QAAA,CAAAC,OAAA;UACA4B,MAAA;QACA;QACA,KAAAuD,WAAA;QACA,KAAA3G,KAAA;MACA;IACA;IACA4G,qBAAA;MACA,SAAA9F,aAAA,CAAA+F,UAAA;QACA;UACAvF,aAAA,OAAAR,aAAA,CAAAc,KAAA;UACAkF,YAAA,OAAAhG,aAAA,CAAA+F;QACA;MACA,gBAAAlG,iBAAA,CAAAkG,UAAA;QACA;UACAvF,aAAA,OAAAX,iBAAA,CAAAW,aAAA;UACAwF,YAAA,OAAAnG,iBAAA,CAAAkG;QACA;MACA;MACA;QACAvF,aAAA,OAAAC,QAAA,CAAAC,OAAA;QACAsF,YAAA,OAAAvF,QAAA,CAAAC,OAAA;MACA;IACA;IACAmF,YAAA;MACA,MAAAI,cAAA,QAAAH,oBAAA;MACA,KAAA9F,aAAA;MACA,KAAAH,iBAAA;MACA,KAAAR,aAAA;MACAsF,UAAA;QACA,KAAAW,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,YAAApC,KAAA,CAAAgD,IAAA,2BAAAhI,OAAA;QACA,GAAA+H,cAAA;QACAP,QAAA,OAAAxG,KAAA,CAAAsG,IAAA;QACA7B,qBAAA,OAAAlD,QAAA;QACA0F,YAAA,OAAA1G,IAAA;QACAZ,oBAAA,OAAAA,oBAAA;QACAuH,eAAA,OAAA1H;MACA,GAAAyE,IAAA,CAAAkD,GAAA;QACA,KAAA7E,SAAA;UACA,MAAAc,MAAA,GAAA+D,GAAA,CAAA/H,IAAA,CAAAgE,MAAA;UACA,MAAA9D,QAAA,QAAA4C,eAAA;UACA,MAAAgB,OAAA,GAAA5D,QAAA;UACA,KAAAgD,SAAA;YACAY,OAAA,CAAAkE,WAAA,GAAAhE,MAAA;YACA,KAAAiE,cAAA,GAAApD,IAAA;cACA,KAAAqD,IAAA,CAAAhI,QAAA;gBACA,GAAAA,QAAA;gBACA,GAAA6H,GAAA,CAAA/H,IAAA;gBACAkF,QAAA,EAAA/F,KAAA,CAAA4I,GAAA,CAAA/H,IAAA,CAAAkF,QAAA,EAAAC,MAAA;gBACAC,KAAA,EAAA2C,GAAA,CAAA/H,IAAA,CAAAqF,qBAAA,EAAAjD,OAAA;cACA;YACA;UACA;QACA;MACA,GAAA+F,KAAA,CAAAC,GAAA;QACA,MAAAtE,OAAA,QAAAhB,eAAA;QACA,KAAAI,SAAA;UACAY,OAAA,CAAAkE,WAAA,GAAAI,GAAA,CAAAC,QAAA,EAAArI,IAAA,CAAA8D,OAAA;UACA,KAAAmE,cAAA;QACA;MACA,GAAA3C,OAAA;QACA,KAAAvE,aAAA;MACA;IACA;IACAkH,eAAA;MACA,KAAApH,MAAA;MACA,MAAAiD,OAAA,QAAAhB,eAAA;MACA,MAAAV,OAAA,GAAA0B,OAAA,CAAAkE,WAAA;MACA,IAAAM,CAAA;MACA,WAAAC,OAAA,CAAAC,OAAA;QACA,MAAAC,QAAA,GAAA1E,WAAA;UACAD,OAAA,CAAAE,MAAA,GAAA5B,OAAA,CAAAE,KAAA,IAAAgG,CAAA;UACA,KAAA3E,UAAA;UACA2E,CAAA;UACA,IAAAA,CAAA,KAAAlG,OAAA,CAAAS,MAAA;YACAoB,aAAA,CAAAwE,QAAA;YACA,KAAA5H,MAAA;YACA2H,OAAA;UACA;QACA;MACA;IACA;IACAE,gBAAA;MACA,KAAArH,cAAA;MACA,KAAAuD,KAAA,2BAAAhF,OAAA,sBAAAiF,IAAA,CAAAkD,GAAA;QACA,KAAAzG,WAAA,GAAAyG,GAAA,CAAA/H,IAAA,CAAA2I,gBAAA;MACA,GAAArD,OAAA;QACA,KAAAjE,cAAA;MACA;IACA;IACAuH,qBAAAC,UAAA;MACA,KAAAjI,KAAA,GAAAiI,UAAA,CAAAC,sBAAA;MACA,KAAAvH,iBAAA,GAAAsH,UAAA;MACA,KAAAzH,cAAA;MACA,KAAA+E,iBAAA;IACA;IACA4C,kBAAA3D,KAAA;MACA;QAAA4D,eAAA;QAAAC;MAAA,IAAA7D,KAAA;MACA,KAAA8D,MAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA,GAAAJ,UAAA;MACA,KAAAC,MAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAJ,eAAA,GAAAA,eAAA;MACA,KAAAE,MAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAE,gBAAA;IACA;IACAC,SAAA;MACA,MAAAC,UAAA,QAAAlG,KAAA,CAAAmG,IAAA;MACA,MAAAC,IAAA,QAAApG,KAAA,CAAAoG,IAAA;MACA,IAAAC,UAAA;MACA,IAAAC,KAAA;MAEAJ,UAAA,CAAAK,gBAAA,cAAAC,CAAA;QACAH,UAAA;QACAC,KAAA,GAAAE,CAAA,CAAAC,OAAA;QACA5G,QAAA,CAAA6G,IAAA,CAAAvG,KAAA,CAAAwG,UAAA;MACA;MACA9G,QAAA,CAAA0G,gBAAA,cAAAC,CAAA;QACA,MAAAI,SAAA,GAAAR,IAAA,CAAAlG,WAAA;QACA,MAAA2G,KAAA,GAAAP,KAAA,GAAAE,CAAA,CAAAC,OAAA;QACA,KAAAJ,UAAA,IAAAO,SAAA,WAAAC,KAAA;UACA;QACA;QACAT,IAAA,CAAAjG,KAAA,CAAAJ,KAAA,MAAA6G,SAAA,GAAAC,KAAA;QACAP,KAAA,GAAAE,CAAA,CAAAC,OAAA;MACA;MAEA5G,QAAA,CAAA0G,gBAAA;QACAF,UAAA;QACAxG,QAAA,CAAA6G,IAAA,CAAAvG,KAAA,CAAAwG,UAAA;MACA;IACA;IACAG,WAAA;MACA,MAAAC,OAAA;QACAC,cAAA;QACAC,eAAA;MACA;MACA,KAAA3F,KAAA,2BAAAhF,OAAA,iBAAAiF,IAAA,CAAAkD,GAAA;QACA,KAAAvG,aAAA,GAAAuG,GAAA,CAAA/H,IAAA,CAAAwK,OAAA,CAAAvF,GAAA,CAAAhD,EAAA;UACA;YACA,GAAAA,EAAA;YACAwI,IAAA,EAAAJ,OAAA,CAAApI,EAAA,CAAAS,SAAA;UACA;QACA;MACA;IACA;IACAgI,aAAA;MACA,WAAAnC,OAAA,EAAAC,OAAA,EAAAmC,MAAA;QACA,KAAA/F,KAAA,2BAAAhF,OAAA,cAAAiF,IAAA,CAAAkD,GAAA;UACA,IAAAA,GAAA,CAAA/H,IAAA,CAAA4K,aAAA;YACA,KAAA3K,eAAA;YACAuI,OAAA;UACA;YACA,KAAAtH,WAAA,GAAAmF,UAAA;cACA,KAAAqE,YAAA,GAAA7F,IAAA,CAAA2D,OAAA;YACA;UACA;QACA,GAAAL,KAAA;UACAwC,MAAA;QACA;MACA;IACA;IACA,MAAAE,aAAArF,KAAA;MACA,SAAA/E,MAAA,SAAAE,aAAA;QACA;MACA;MACA,MAAA4B,IAAA,GAAAiD,KAAA,CAAAC,MAAA;MACA,IAAAlD,IAAA,CAAAmD,SAAA,GAAAnD,IAAA,CAAAoD,YAAA,KAAApD,IAAA,CAAAqD,YAAA;QACA,KAAAC,eAAA;MACA;IACA;IACAA,gBAAA;MACA,KAAAlF,aAAA;MACA,KAAAiE,KAAA,2BAAAhF,OAAA,mCAAAS,WAAA,gBAAAG,YAAA,IAAAqE,IAAA;QAAA7E,IAAA;UAAA8E;QAAA;MAAA;QACA,MAAAC,WAAA,GAAAD,YAAA,CAAAjC,MAAA;QACA,KAAAxC,WAAA,GAAA0E,WAAA,GAAAD,YAAA,CAAAA,YAAA,CAAAjC,MAAA,MAAA6B,MAAA;QACA,KAAAjE,MAAA,GAAAsE,WAAA,QAAAvE,YAAA;QACA,KAAAN,QAAA,QAAAA,QAAA,CAAA8E,MAAA,CAAAF,YAAA,CAAAG,GAAA,CAAAhD,EAAA;UACA;YACA,GAAAA,EAAA;YACAiD,QAAA,EAAA/F,KAAA,CAAA8C,EAAA,CAAAiD,QAAA,EAAAC,MAAA;YACAC,KAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EAAAjD,OAAA;UACA;QACA;MACA,GAAAkD,OAAA;QACA,KAAA3E,aAAA;MACA;IACA;IACAmK,aAAA;MACA,KAAAzK,WAAA;MACA,KAAAH,QAAA;MACA,KAAA2F,eAAA;IACA;IACA,MAAAkF,KAAA;MACA;MACA,WAAAL,YAAA;MACA,KAAAnB,QAAA;MACA,KAAAa,UAAA;MACA,KAAA1B,eAAA;MACA,WAAAoC,YAAA;IACA;IACAE,qBAAA;MACA,KAAA5J,cAAA,SAAAA,cAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA6J,WAAA;MACA,KAAAC,MAAA,GAAA9L,EAAA;QACA+L,IAAA;QACAC,iBAAA;QACAC,KAAA;UACAzL,OAAA,OAAAA;QACA;QACA0L,UAAA;QACAC,YAAA;UACAC,aAAA,mBAAAC,OAAA,CAAAC,GAAA;QACA;MACA;MACA;MACA,KAAAR,MAAA,CAAAS,EAAA;QACAC,OAAA,CAAAC,GAAA;MACA;MACA,KAAAX,MAAA,CAAAS,EAAA,kBAAAG,KAAA;QACAF,OAAA,CAAAC,GAAA,SAAAC,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAZ,MAAA,CAAAS,EAAA,2BAAA3L,IAAA;QACA,KAAAqB,cAAA;QACA,KAAAC,WAAA,GAAAtB,IAAA,CAAA2I,gBAAA;MACA;IACA;EACA;EACAoD,QAAA;IACA,KAAAhB,IAAA;EACA;EACAiB,cAAA;IACA,KAAAhF,UAAA;IACA,KAAA9F,WAAA,IAAA+K,YAAA,MAAA/K,WAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}