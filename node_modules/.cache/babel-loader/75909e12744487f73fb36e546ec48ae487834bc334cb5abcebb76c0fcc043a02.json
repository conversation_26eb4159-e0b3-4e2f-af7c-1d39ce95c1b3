{"ast": null, "code": "export default {\n  ssoLoginConfig: {\n    notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',\n    operationStep: {\n      one: '第一步 点击继续后，返回登录页面',\n      two: '第二步 输入密码，进入上上签平台',\n      three: '第三步 发送合同（或管理模板）'\n    },\n    continue: '继续',\n    cancel: '取消',\n    tip: '提示'\n  },\n  sign: {\n    sealLabelsTip: '您需要在合同上盖{sealLabelslen}个章。{personStr}将为您盖{otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已在页面上展示。请确认是否继续。',\n    continue: '继续',\n    nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n    reselect: '重选',\n    approvalFeatures: {\n      dialogTitle: '新功能介绍',\n      understand: '我知道了',\n      feature1: '划句批注',\n      feature2: '字段高亮',\n      tip1: '点击按钮将合同中的所有“模板内容字段”高亮，方便抓取关键信息。',\n      tip2: '点击左下角提示按钮，开启模板内容字段高亮。',\n      tip3: '通过高亮，快速定位合同的内容填写字段，高效完成审批。',\n      tip4: '按住鼠标圈选一个段落后松开鼠标，点击批注按钮可添加批注文本，完成后点击修改或删除。批注的内容可在合同详情页-公司内部操作日志中查看。',\n      tip5: '第一步：选中所需批注文本字段，添加批注；',\n      tip6: '第二步：点击编辑或删除批注。',\n      annotate: '批注',\n      delete: '删除',\n      edit: '修改',\n      operateTitle: '添加审批批注',\n      placeholder: '不超过255个字'\n    },\n    needRemark: '您还需要填写备注',\n    notNeedRemark: '您不需要填写备注',\n    switchToReceiver: '已为您切换至{receiver}',\n    notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',\n    contractPartiesYouChoose: '您可以选择的签约主体:',\n    contractPartyFilled: '发件人填写的签约主体为:',\n    certifyOtherCompanies: '认证其他企业',\n    youCanAlso: '您也可以：',\n    needVerification: '您需要实名认证后才能签署',\n    prompt: '提示',\n    submit: '确定',\n    cancel: '取消',\n    sign: '立即签约',\n    addSeal: '请使用电脑登录上上签官网添加印章',\n    noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',\n    memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',\n    noticeAdminFoSeal: '发通知给主管理员',\n    requestSomeone: '请求他人认证',\n    requestOthersToContinue: '通知主管理员补充实名认证',\n    requestOthersToContinueSucceed: '已向管理员发送通知',\n    requestSomeoneList: '请求以下人员完成实名认证：',\n    electronicSeal: '电子公章',\n    changeTheSeal: '不想使用该印章？实名认证后可更换印章',\n    goToVerify: '去实名认证',\n    noSealToChoose: '没有可切换的印章，如果需要管理印章，请先进行实名认证',\n    goVerify: '去认证',\n    goToVerifyEnt: '去认证企业',\n    digitalCertificateTip: '上上签正在调用您的数字证书',\n    signDes: '您在安全签约环境中，请放心签署！',\n    signAgain: '继续签署',\n    send: '发送',\n    person: '个人',\n    ent: '企业',\n    entName: '企业名称',\n    account: '账号',\n    accountPH: '手机或邮箱',\n    approved: '审批',\n    signVerification: '签署',\n    cannotReview: '无法查看合同',\n    connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',\n    connectFailTip: '您可以尝试以下方法解决问题：',\n    connectFailTip1: '1、刷新页面。',\n    connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n    connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n    personalMaterials: '发件人要求您补充更多认证材料',\n    noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',\n    lackEntName: '请填写企业名称',\n    errAccount: '请填写正确的邮箱或手机号',\n    noticeAdmin: '申请加入',\n    signDone: '签署完成',\n    signDoneTip: '您已签署该合同',\n    approveDone: '审批完成',\n    approveDoneTip: '您已审批该合同',\n    completeSign: '请先点击“盖章处”或“签字处”完成签署',\n    fillFirst: '请先在输入框中填写合同内容',\n    stillSignTip: '在您签署此{alias}后，仍有其他签署方可能更改{alias}内容，是否继续签署？',\n    signHighLightTip: '可被新增或修改的{alias}内容共{count}处',\n    riskDetails: '风险详情',\n    noviewDifference: '由于发起方开启了签署方可以填写本{alias}固定字段的功能，本{alias}其他签署方仍可能更改发起方指定的合同内容，上上签不对本{alias}的签署前版本与生效版本之间的内容差异进行审核，当您签署完本{alias}后，视为您同意其它签署方对{alias}内容中固定字段内容的增加或修改，并认可本{alias}各签署方均签署完成后的生效版本。\\n' + '如果您不同意在您签署后其他签署方仍可以变更本{alias}字段，您可以拒绝本次签署，并与发件方协商（即要求发起方关闭\"签署人填写字段\"功能，规避您的相应风险）。',\n    highLightTip: '这些有风险的内容将会被呈现为“高亮”效果，请仔细核对。重新刷新页面可取消高亮效果。',\n    commonTip: '提示',\n    understand: '我知道了',\n    view: '查看',\n    start: '开始',\n    nextStep: '下一步',\n    help: '帮助',\n    faceFailed: '非常抱歉，您的人脸比对失败',\n    faceFailedtips: '提示',\n    dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',\n    verifyTry: '请核实身份信息后重试',\n    faceLimit: '今天的人脸比对次数已达到上限',\n    upSignReq: '今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求',\n    reqFace: '发件人要求你进行刷脸校验',\n    signAfterFace: '刷脸通过后即可完成合同签署',\n    qrcodeInvalid: '二维码信息已过期，请刷新',\n    faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',\n    date: '日期',\n    chooseSeal: '选择印章',\n    seal: '印章',\n    signature: '签名',\n    handwrite: '手写',\n    mysign: '我的签名',\n    approvePlace: '审批留言，可不填',\n    approvePlace_1: '审批留言',\n    approvePlace_2: '选填，不超过255字。',\n    approveAgree: '审批结果：同意',\n    approveReject: '审批结果：驳回',\n    signBy: '由',\n    signByEnd: '盖章',\n    sealBy: '由',\n    sealByEnd: '签名',\n    coverBy: '需盖',\n    applicant: '申请人',\n    continueVeri: '继续认证',\n    registerAndReal: '请注册并实名',\n    goToResiter: '请注册并认证',\n    sureToUse: '确定使用',\n    toSign: '签约吗?',\n    pleaseComplete: '请先完成',\n    confirmSign: '再确认签署',\n    admin: '管理员',\n    contratAdmin: '请联系管理员将您的账号',\n    addToEnt: '添加为企业成员',\n    alreadyExists: '在上上签已存在',\n    sendMsg: '上上签将以短信形式给管理员发以下内容：',\n    applyJoin: '申请加入',\n    title: '标题',\n    viewImg: '查看图片',\n    priLetter: '私信',\n    priLetterFromSomeone: '来自{name}的私信',\n    readLetter: '我知道了',\n    approve: '同意',\n    disapprove: '驳回',\n    refuseSign: '拒签',\n    paperSign: '改用纸质签署',\n    refuseTip: '请选择拒绝理由',\n    refuseReason: '填写拒签理由有助于对方了解你的问题，加快合同流程',\n    reasonWriteTip: '请填写拒签理由',\n    refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',\n    refuseConfirm: '拒签',\n    refuseConfirmTip: '您以\"{reason}\"理由拒绝签署，是否继续？确定后将不可再次签署。',\n    waitAndThink: '我再想想',\n    signValidationTitle: '签署校验',\n    email: '邮箱',\n    phoneNumber: '手机号',\n    password: '密码',\n    verificationCode: '验证码',\n    mailVerificationCode: '验证码',\n    forgetPsw: '忘记密码',\n    if: '，是否',\n    forgetPassword: '忘记密码',\n    rejectionVer: '拒签校验',\n    msgTip: '一直收不到信息？试试',\n    voiceVerCode: '语音验证码',\n    SMSVerCode: '短信验证码',\n    or: '或',\n    emailVerCode: '邮箱验证码',\n    SentSuccessfully: '发送成功！',\n    intervalTip: '发送时间间隔过短',\n    signPsw: '签约密码',\n    useSignPsw: '使用签约密码校验',\n    setSignPsw: '设置签约密码校验',\n    useVerCode: '使用验证码校验',\n    inputVerifyCodeTip: '请输入验证码',\n    inputSignPwdTip: '请输入签约密码',\n    signConfirmTip: {\n      1: '您是否确定要签署此{contract}？',\n      2: '点击确定按钮将立即签署此{contract}',\n      confirm: '确认签署'\n    },\n    signSuc: '签署成功',\n    refuseSuc: '拒签成功',\n    approveSuc: '审批成功',\n    hdFile: '查看高清文件',\n    otherOperations: '其他操作',\n    reviewDetails: '审批详情',\n    close: '关 闭',\n    submitter: '提交人',\n    signatory: '签署人',\n    reviewSchedule: '审批进度',\n    signByPc: '由{name}签名',\n    signPageDescription: '第{index}页, 共{total}页',\n    sealBySomeone: '由{name}盖章',\n    signDate: '签署日期',\n    download: '下载',\n    signPage: '页数：{page}页',\n    signNow: '立即签署',\n    sender: '发件方',\n    signer: '签约方',\n    startSignTime: '发起签约时间',\n    signDeadLine: '签约截止时间',\n    authGuide: {\n      goToHome: '回到首页',\n      tip_1: '认证完成后，可查看并签署合同。',\n      tip_2: '请使用身份 | 进行认证。',\n      tip_3: '发来合同',\n      tip_4: '请联系合同发起者 | 更改收件人。',\n      tip_5: '您认证的 | 无法查看合同',\n      new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',\n      new_tip_2: '基于发件方的合规要求，您需要以：',\n      new_tip_3: '完成以下步骤。',\n      new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',\n      entUserName: '姓名：',\n      idNumberForVerify: '身份证号：',\n      realNameAuth: '实名认证',\n      applySeal: '申请印章',\n      signContract: '签署合同'\n    },\n    switch: '切换',\n    rejectReasonList: {\n      // authReason: '不想/不会做实名认证',\n      signOperateReason: '对签署操作/校验操作有疑问，需要进一步沟通',\n      termReason: '对合同条款/内容有疑议，需要进一步沟通',\n      explainReason: '对合同内容不知情，请提前告知',\n      otherReason: '其他（请填写理由）'\n    },\n    selectSignature: '选择签名',\n    selectSigner: '选择签名人',\n    pleaseScanToSign: '请用支付宝或微信扫一扫签署',\n    pleaseScanAliPay: '请使用支付宝app扫描二维码签署',\n    pleaseScanWechat: '请使用微信app扫描二维码签署',\n    requiredFaceSign: '合同发件人要求您刷脸签署',\n    requiredDualSign: '合同发件人要求你使用双录校验',\n    verCodeVerify: '验证码校验',\n    applyToSign: '申请签署合同',\n    autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',\n    cannotSignBeforeApproval: '审批未完成，暂不能签署！',\n    finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',\n    uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',\n    cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',\n    cannotOtherSealReason: '合同开启了刷脸签署校验，不支持他人盖章',\n    unlimitedNotice: '该合同计费不限量使用',\n    units: '{num}份',\n    contractToPrivate: '对私合同',\n    contractToPublic: '对公合同',\n    paySum: '共{sum}需要您支付',\n    payTotal: '共计{total}元。',\n    fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值。',\n    contactToRecharge: '请联系主管理员充值。',\n    deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',\n    needSignerPay: '合同发送方设置了相对方到付，并指定由您来支付合同费用。',\n    recharge: '充值',\n    toSubmit: '提交',\n    appliedSeal: '用印申请已提交',\n    noSeal: '无印章',\n    noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',\n    viewApproveProcess: '查看审批流程',\n    approveProcess: '审批流程',\n    noApproveContent: '未提交审批资料',\n    knew: '知道了',\n    noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',\n    hadAutoSet: '已在另外{num}处自动',\n    setThatSignature: '放置该签名',\n    setThatSeal: '放置该印章',\n    applyThatSeal: '申请该印章',\n    hasSetTip: '已在另外{index}处自动放置',\n    hasSetSealTip: '已在另外{index}处自动放置该印章',\n    hasSetSignatureTip: '已在另外{index}处自动放置该签名',\n    hasApplyForSealTip: '已在另外{index}处自动申请该印章',\n    savedOnLeftSite: '已保存到左侧签名栏',\n    ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',\n    ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',\n    ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',\n    noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',\n    noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',\n    gotoAppendSeal: '去添加印章',\n    approvalFlowSuccessfulSet: '审批流设置成功',\n    mandate: '同意授权',\n    loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',\n    signIdentityAs: '当前正在以{person}的名义签署合同',\n    enterNextContract: '进入下一份合同',\n    fileList: '文件列表',\n    addSignerFile: '添加附属资料',\n    signatureFinish: '已全部盖章/签名',\n    dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',\n    noticeToManager: '给管理员发通知',\n    gotoAuthPerson: '去认证个人',\n    senderRequire: '发件方要求您',\n    senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',\n    suggestToAuth: '您还未实名认证，建议您实名认证后签署',\n    contactEntAdmin: '请联系企业主管理员',\n    setYourAccount: '将您的账号',\n    authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',\n    noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',\n    pleaseUse: '请使用',\n    me: '我',\n    myself: '本人，',\n    reAuthBtnTip: '我是当前手机号的实际使用者，',\n    reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n    authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    authInfo2: '检测到您当前账号的基础身份信息为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '是否继续以当前账号签署？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n    confirmOk: '确认',\n    goOnAuth: {\n      0: '进行认证，',\n      1: '请进行实名认证，',\n      2: '进行实名认证，'\n    },\n    signContractAfterAuth: {\n      0: '认证完成后，可签署合同。',\n      1: '完成认证后即可签署合同。'\n    },\n    useIdentity: '以{name}的身份',\n    inTheName: '以',\n    of: '的',\n    identity: '身份',\n    nameIs: '姓名为',\n    IDNumIs: '身份证号为',\n    provideMoreAuthData: '补充更多认证材料',\n    leadToAuthBeforeSign: '继续认证后即可签署合同',\n    groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',\n    contactSender: '如有疑问请联系发件方。',\n    note: '注:',\n    identityInfo: '身份信息',\n    signNeedCoincidenceInfo: '完全一致才能签署合同。',\n    needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',\n    iHadReadContract: '已阅读，本人已知晓{alias}内容',\n    scrollToBottomTip: '需滑动到最后一页',\n    getVerCodeFirst: '请先获取验证码',\n    appScanVerify: '上上签APP扫码校验',\n    downloadBSApp: '下载上上签APP',\n    scanned: '扫码成功',\n    confirmInBSApp: '请在上上签APP中确认签署',\n    qrCodeExpired: '二维码已失效，请刷新重试',\n    appKey: 'APP安全校验',\n    goToScan: '去扫码',\n    setNotificationInUserCenter: '请先到用户中心设置通知方式',\n    doNotWantUseVerCode: '不想用验证码',\n    try: '试试',\n    retry: '重试',\n    goToFaceVerify: '去刷脸',\n    faceExceedTimes: '当日刷脸次数已达上线',\n    returnBack: '返回',\n    switchTo: '切换至',\n    youCanChooseIdentityBlow: '您可以选择以下签约主体',\n    needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',\n    lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',\n    manageSeal: '管理印章',\n    needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',\n    chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',\n    appendDrawSignature: '添加手绘签名',\n    senderUnFill: '（发件人未填写）',\n    declare: '说明',\n    fileLessThan: '请上传小于{num}M的文件',\n    fileNeedUploadImg: '上传时请使用支持的附件格式',\n    serverError: '服务器开了点小差，请稍后再试',\n    oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n    fileLimitFormatAndSize: '单个资料图片数量不超过10张。',\n    fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',\n    fileFormatFile: '支持pdf、excel、word、txt、zip、xml格式，单份文件大小不超过10M',\n    signNeedKnow: '签约须知',\n    signNeedKnowFrom: '来自{sender}的签约须知',\n    approvalInfo: '审批须知',\n    approveNeedKnowFrom: '来自{sender}-{sendEmployeeName}提交的审批资料（{approvalType}）',\n    approveBeforeSend: '合同发送前审批',\n    approveBeforeSign: '合同签署前审批',\n    approveOperator: '审批人',\n    approvalOpinion: '审批留言',\n    employeeDefault: '员工',\n    setLabel: '设置标签',\n    addRidingSeal: '添加骑缝章',\n    delRidingSeal: '删除骑缝章',\n    file: '附件文件',\n    compressedFile: '压缩文件',\n    attachmentContent: '附件内容',\n    pleaseClickView: '（请点击下载查看）',\n    downloadFile: '下载源文件',\n    noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',\n    archiveTo: '归档到',\n    hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',\n    pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',\n    save: '保存',\n    remind: '提醒',\n    riskTip: '风险提醒',\n    chooseApplyPerson: '选择盖章执行人',\n    chooseAdminSign: '选择印章管理员',\n    useSealByOther: '他人盖章',\n    getSeal: '获取印章',\n    nowApplySealList: '您正在请求以下印章',\n    nowAdminSealList: '你正在申请获得以下印章',\n    chooseApplyPersonToDeal: '请选择盖章执行人，合同将由所选人员来处理（你仍能继续查看、跟进此合同）',\n    chooseTransferPerson: '转交他人签署',\n    chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',\n    contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',\n    sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',\n    successfulSent: '发送成功',\n    authTip: {\n      t2: ['注：', '完全一致才能签署合同。', '企业名称', '身份信息', '完全一致才能查看和签署合同。'],\n      t3: '{x}要求您{text}进行实名认证。',\n      tCommon1: '以{entName}的身份',\n      tCommon2_1: '以姓名为{name}，身份证号为{idCard}',\n      tCommon2_2: '以姓名为{name}',\n      tCommon2_3: '以身份证号为{idCard}',\n      viewAndSign1: '完成认证后即可查看和签署合同。',\n      viewAndSignConflict: '{x}要求您{text}进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。'\n    },\n    needSomeoneToSignature: '由{x}盖{y}',\n    needToSet: '需盖',\n    approver: '申请人：',\n    clickToSignature: '点击此处签名',\n    transferToOtherToSign: '转给其他人签',\n    signatureBy: '由{x}签名',\n    tipRightNumber: '请输入正确的数字',\n    tipRightIdCard: '请正确填写18位大陆居民身份证',\n    tipRightPhoneNumber: '请正确填写11位手机号码',\n    tip: '提示',\n    tipRequired: '必填值不可为空',\n    confirm: '确定',\n    viewContractDetail: '查看合同详情',\n    required: '必填',\n    optional: '选填',\n    decimalLimit: '限小数点后{x}位',\n    intLimit: '要求整数',\n    invalidContract: '签署此合同视为您同意将以下合同作废：',\n    No: '编号',\n    chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',\n    crossPlatformCofirm: {\n      message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',\n      title: '数据授权',\n      confirmButtonText: '同意授权',\n      cancelButtonText: '取消'\n    },\n    sealScope: '印章使用范围',\n    currentContract: '当前合同',\n    allContract: '所有合同',\n    docView: '合同预览',\n    fixTextDisplay: '纠正页面乱码',\n    allPage: '共{num}页',\n    notJoinTip: '请联系管理员添加为企业成员后再签署'\n  },\n  signJa: {\n    beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',\n    beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n    beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n    beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n    beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n    beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n    beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n    entNamePlaceholder: '请输入企业名称',\n    corporateNumberPlaceholder: '请输入法人番号',\n    corporateNumber: '法人番号',\n    singerNamePlaceholder: '请输入签署人姓名',\n    singerName: '签署人姓名',\n    businessPic: '印鉴证明书',\n    waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',\n    itsMe: '是我本人',\n    wrongInformation: '信息有误',\n    confirmChange: '确认更换',\n    communicateSender1: '不更换, 与甲方沟通',\n    communicateSender2: '取消, 去与发件方沟通',\n    createSeal: {\n      title: '输入姓名',\n      tip: '请输入您的姓名（空格可以进行换行）',\n      emptyErr: '请输入姓名'\n    },\n    areaRegister: '企业注册地',\n    jp: '日本',\n    cn: '中国大陆',\n    are: '阿拉伯联合酋长国',\n    other: '其他',\n    plsSelect: '请选择',\n    tip1: '注册地为中国大陆地区的企业，需在 ent.bestsign.cn 中完成实名注册。在与中国大陆地区以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',\n    tip2: '若您的企业已在上上签中国大陆版完成实名认证，可直接登录 ent.bestsign.cn，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与中国大陆版是完全独立隔离的。',\n    tip3: '请提供您从当地商业监管机构获取的证件编号',\n    tip4: '请按照以下步骤操作',\n    tip5: '1、请您与您的专属客户经理联系，引导您完成企业实名。',\n    tip6: '点击“充值管理”。',\n    tip7: '2、请上传贵司与上上签的商务合同截图或与专属客户经理的业务往来邮件。',\n    tip8: '至少购买一份合同，并保存购买记录的截图。',\n    tip9: '3、非日本、中国大陆地区的企业才可以使用此方式。',\n    tip10: '4、提交后上上签在三个工作日内进行审核。',\n    tip11: '重要提示',\n    tip12: '购买人必须为企业用户。',\n    tip13: '付款账户中的企业全称必须与您已填写的“企业名称”完全一致。',\n    tip14: '非日本、中国大陆地区以外的企业才可以使用此方式。',\n    comNum: '企业证件号',\n    buyRecord: '证明材料',\n    selectArea: '请选择企业注册地',\n    uaeTip1: '注册地为阿联酋的企业，需在 uae.bestsign.com中完成实名注册。在与阿联酋以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',\n    uaeTip2: '若您的企业已在上上签阿联酋版完成实名认证，可直接登录 uae.bestsign.com，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与阿联酋版是完全独立隔离的。',\n    uaeTip3: '注册地为阿联酋和中国大陆以外的企业，需在 ent.bestsign.com中完成实名注册。在与阿联酋的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。'\n  },\n  signPC: {\n    commonSign: '确认签署',\n    contractVerification: '签约校验',\n    VerCodeVerify: '验证码校验',\n    QrCodeVerify: '二维码校验',\n    verifyTip: '上上签正在调用您的安全数字证书，您正在安全签约环境中，请放心签署！',\n    verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',\n    selectSeal: '选择印章',\n    adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n    toAddSealWithConsole: '电子公章待启用，添加其他印章可前往控制台操作。',\n    use: '使用',\n    toAddSeal: '去添加印章',\n    mySeal: '我的印章',\n    operationCompleted: '操作完成',\n    FDASign: {\n      date: '签署时间',\n      signerAdd: '新增',\n      signerEdit: '修改',\n      editTip: '提示：中文姓名请输入拼音，如San Zhang（张三）',\n      inputNameTip: '请输入您的姓名',\n      inputName: '请输入英文或中文拼音',\n      signerNameFillTip: '您还需要填写签字姓名',\n      plsInput: '请输入',\n      plsSelect: '请选择',\n      customInput: '自行输入'\n    },\n    signPlaceBySigner: {\n      signGuide: '签署指导',\n      howDragSeal: '如何拖章',\n      howDragSignature: '如何拖签名',\n      iKnow: '我知道了',\n      step: {\n        one: '第一步：阅读合同',\n        two1: '第二步：点击“拖章”',\n        two2: '第二步：点击“拖签名”',\n        three: '第三步：点击“签署”按钮'\n      },\n      dragSeal: '拖章',\n      continueDragSeal: '继续拖章',\n      dragSignature: '拖签名',\n      continueDragSignature: '继续拖签名',\n      dragPlace: '按住此处拖动',\n      notRemind: '不再提醒',\n      signTip: {\n        one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',\n        two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。'\n      },\n      finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署'\n    },\n    continueOperation: {\n      success: '操作成功',\n      exitApproval: '退出审批',\n      continueApproval: '继续审批',\n      next: '下一份：',\n      none: '没有了',\n      tip: '提示',\n      approvalProcess: '共需{totalNum}人审批，当前已有{passNum}人审批通过',\n      receiver: '接收方：'\n    }\n  },\n  signTip: {\n    contractDetail: '合同详情',\n    downloadBtn: '下载APP',\n    tips: '提示',\n    submit: '确定',\n    SigningCompleted: '签署成功',\n    submitCompleted: '等待他人处理',\n    noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n    noRightSign: '合同正在签署中，当前用户不允许签署操作',\n    noNeedSign: '内部决议合同，已无需签署',\n    ApprovalCompleted: '审批成功',\n    contractRevoked: '该{alias}已被撤销',\n    contractRefused: '该{alias}已被拒签',\n    linkExpired: '该链接已失效',\n    contractClosed: '该{alias}已截止签约',\n    approvalReject: '该{alias}审批已被驳回',\n    approving: '{alias}正在审批中',\n    viewContract: '查看{alias}详情',\n    viewContractList: '查看合同列表',\n    needMeSign: '（{num}份待签署）',\n    downloadContract: '下载合同',\n    sign: '签署',\n    signed: '签署',\n    approved: '审批',\n    approval: '审批',\n    person: '人',\n    personHas: '已',\n    personHave: '已',\n    personHasnot: '未',\n    personsHavenot: '未',\n    headsTaskDone: '{num}{has}{done}',\n    headsTaskNotDone: '{num}{not}{done}',\n    taskStatusBetween: '，',\n    cannotReview: '无法查看合同',\n    cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',\n    privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n    beenDeleted: '您的账号已被企业管理员删除',\n    unActive: '无法继续激活账',\n    back: '返回',\n    contratStatusDes: '{key}状态：',\n    contractConditionDes: '{key}情况：',\n    contractIng: '{alias}{key}中',\n    contractComplete: '{alias}{key}完成',\n    dataProduct: {\n      tip1: '{entName}致各位优质经销商/供应商企业负责人：',\n      tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',\n      btnText: '去向老板分享这个喜讯'\n    },\n    signOnGoing: '合同{status}中',\n    operate: '合同操作',\n    freeContract: '完成首次合同发送，可免费再获取合同份数',\n    sendContract: '去发合同',\n    congratulations: '恭喜{name}已完成{num}份合同签署，',\n    carbonSaving: '预估节碳{num}g',\n    signGift: '上上签赠送您{num}份对公合同（使用期限至{limit}）',\n    followPublic: '关注微信公众号，随时接收合同消息',\n    congratulationsSingle: '恭喜{name}完成合同签署，',\n    carbonSavingSingle: '预估新增节碳量2002.4g',\n    viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮',\n    congratulationsCn: '感谢选择电子签！',\n    carbonSavingSingleCn: '您为地球减碳{num}gCO2e',\n    carbonVerification: '*经「碳阻迹」科学核算'\n  },\n  view: {\n    title: '查看合同',\n    ok: '完成',\n    cannotReview: '无法查看合同',\n    privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器'\n  },\n  prepare: {\n    sealArea: '盖章处',\n    senderNotice: '当前合同发送主体为：{entName}',\n    preSetDialogConfirm: '我知道了',\n    preSetDialogContact: '马上联系上上签销售人员开通',\n    preSetDialogInfo: '合同预置成功后，系统根据模板自动填写相应的签约方信息、签署要求、签署位置、合同描述字段等',\n    preSetDialogTitle: '什么是合同预置模板？',\n    initialValues: '根据合同内容预置初始值',\n    proxyUpload: '上传本地文件后，可选择合同发起方',\n    signHeaderTitle: '添加文件和签约方',\n    step1: '第一步',\n    confirmSender: '确认发起方',\n    step2: '第二步',\n    uploadFile: '上传文件',\n    step3: '第三步',\n    addSigner: '添加签约方',\n    actionDemo: '操作演示',\n    next: '下一步',\n    isUploadingErr: '文件还未上传完成，请在完成后继续操作',\n    noUploadFileErr: '未上传文件，请上传后继续操作',\n    noContractTitleErr: '未填写合同名称，请填写后继续',\n    contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n    expiredDateErr: '签署截止时间有误，请修改后继续',\n    noExpiredDateErr: '请填写签署截止时间后继续',\n    describeFieldsErr: '请填写必填内容字段后继续',\n    noRecipientsErr: '至少添加一个签约方',\n    noAccountErr: '账号不能为空',\n    noUserNameErr: '姓名不能为空',\n    noIDNumberErr: '身份证号码不能为空',\n    accountFormatErr: '格式不正确，请输入正确的手机号或邮箱',\n    userNameFormatErr: '格式不正确，请输入正确的姓名',\n    enterpriseNameErr: '请填写正确的企业名称',\n    idNumberForVerifyErr: '格式不正确，请输入正确的身份证',\n    signerErr: '签约方有误',\n    noSignerErr: '请至少添加一个签署人',\n    lackAttachmentNameErr: '请填写附件名称',\n    repeatRecipientsErr: '非顺序签署时不能重复添加签约方',\n    innerContact: '内部联系人',\n    outerContact: '外部联系人',\n    search: '搜索',\n    accountSelected: '已选账号',\n    groupNameAll: '全部',\n    unclassified: '未分类',\n    fileLessThan: '请上传小于{num}M的文件',\n    beExcel: '请上传Excel文件',\n    usePdf: '上传时请使用PDF文件或图片',\n    usePdfFile: '上传时请使用PDF文件',\n    fileNameMoreThan: '文件名称长度超过{num}，已为您自动截取',\n    needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',\n    addSender: '添加为签约方',\n    tip: '提示',\n    cancel: '取消'\n  },\n  addReceiver: {\n    English: '英语',\n    Japanese: '日语',\n    Chinese: '中文',\n    Arabic: '阿拉伯语',\n    setNoticelang: '设置通知语言',\n    limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',\n    individual: '签约个人',\n    enterprise: '签约企业',\n    addInstructions: '添加签约须知',\n    instructionsContent: '提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交',\n    addContractingInfo: '提交签约主体资料',\n    contractingInfoContent: '提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交',\n    payer: '付费方',\n    handWriting: '开启手写笔迹识别',\n    realName: '经办人需要实名',\n    sameTip: '提示：签约方的企业名称完全一致才能签署',\n    proxy: '对方前台代收',\n    aboradTip: '提示：该签约方为境外人士，实名认证有风险，请先核对该人员的身份',\n    busRole: '业务角色',\n    busRoleTip: '能帮助您识别签约方，方便管理',\n    busRolePlaceholder: '如员工/经销商',\n    handWritingTip: '该用户需要在签署时手写清晰可辨的姓名才能完成签署',\n    instructions: '添加签约须知 | （限255字）',\n    contractingParty: '签约主体资料',\n    signerPay: '本合同由该签署方付费',\n    afterReadingTitle: '阅读完毕再签署',\n    afterReading: '签署人必须阅读，并且知晓合同内容才可进行后续操作。',\n    handWritingTips: '该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署',\n    SsTitle: '盖章并签字',\n    SsTip: '使用企业印章签署时，需同时添加个人签名完成签署。签名前需完成个人实名认证',\n    signature: '签字',\n    stamp: '盖章',\n    Ss: '盖章并签字',\n    mutexError: '已设置“{msg}”，请先删除“{msg}”的设置后再选择',\n    handWriteNotAllowed: '不允许手写签名',\n    forceHandWrite: '必须手写签名',\n    faceFirst: '优先刷脸，备用验证码签署',\n    faceVerify: '必须刷脸签署',\n    attachmentRequired: '添加合同附属资料',\n    newAttachmentRequired: '提交签约主体资料',\n    attachmentError: '合同附属资料名称不能相同',\n    receiver: '接收手机/邮箱 |（最多支持5个，可用分号隔开）',\n    receiverJa: '接收邮箱 |（最多支持5个，可用分号隔开）',\n    orderSignLabel: '顺序签署',\n    contactAddress: '联系人地址簿',\n    signOrder: '签署顺序',\n    account: '账号',\n    accountPlaceholder: '手机/邮箱（必填）',\n    accountPlaceholderJa: '邮箱（必填）',\n    accountReceptionCollection: '前台代收',\n    accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',\n    accountReceptionCollectionTip2: '请选择前台代收',\n    signSubjectPerson: '签约主体：个人',\n    nameTips: '姓名（选填，用于签约身份核对）',\n    requiredNameTips: '姓名（必填，用于签约身份核对）',\n    entOperatorNameTips: '姓名（选填）',\n    needAuth: '需要实名',\n    operatorNeedAuth: '经办人需要实名',\n    signSubjectEnt: '签约主体：公司',\n    entNameTips: '企业名称（必填，用于签约身份核对）',\n    operator: '经办人',\n    sign: '签署',\n    more: '更多',\n    faceFirstTips: '签署时系统默认采用刷脸校验，当刷脸不通过的次数达到当日上限时自动切换为验证码校验',\n    mustFace: '必须刷脸签署',\n    mustHandWrite: '必须手写签名',\n    fillIDNumber: '身份证号',\n    fillNoticeCall: '通知手机',\n    fillNoticeCallTips: '请填写通知手机',\n    addNotice: '添加私信',\n    attachTips: '添加合同附属资料',\n    faceSign: '必须刷脸签署',\n    faceSignTips: '该用户需要通过刷脸认证才能完成签署（刷脸签署暂只支持大陆居民使用）',\n    handWriteNotAllowedTips: '该用户只能选择已经设置的签名或者使用默认字体签名才能完成签署',\n    handWriteTips: '该用户需要手写签名才能完成签署',\n    idNumberTips: '用于签约身份核对',\n    verifyBefore: '查看文件前验证身份',\n    verify: '验证身份',\n    verifyTips: '最多20字',\n    verifyTips2: '您须将此验证信息提供给该用户',\n    sendToThirdPlatform: '发送给第三方平台',\n    platFormName: '平台名称',\n    fillThirdPlatFormName: '请输入第三方平台名称',\n    attach: '资料',\n    attachName: '资料名称',\n    exampleID: '例：身份证照片',\n    attachInfo: '备注',\n    attachInfoTips: '例：请上传本人的身份证照片',\n    addAttachRequire: '增加资料',\n    addSignEnt: '添加签约企业',\n    addSignPerson: '添加签约个人',\n    selectContact: '选择联系人',\n    save: '保 存',\n    searchVerify: '查询校验',\n    fillImageContentTips: '请填写图片内容',\n    ok: '确定',\n    findContact: '从合同中找到以下签约方',\n    signer: '签约方',\n    signerTips: '小提示：选择签约方后，平台可以帮助定位签字及盖章位置。',\n    add: '添加',\n    notAdd: '不添加',\n    cc: '抄送',\n    notNeedAuth: '不需要实名',\n    operatorNotNeedAuth: '经办人不需要实名',\n    extracting: '提取中',\n    autoFill: '自动填写签署人',\n    failExtracting: '未提取到签约方',\n    idNumberForVerifyErr: '请输入正确的身份证',\n    noAccountErr: '账号不能为空',\n    noUserNameErr: '姓名不能为空',\n    noIDNumberErr: '身份证号码不能为空',\n    noEntNameErr: '企业名称不能为空',\n    accountFormatErr: '请输入正确的手机号或邮箱',\n    enterpriseNameErr: '请输入正确的公司名称',\n    userNameFormatErr: '请输入正确的姓名',\n    riskCues: '风险提示',\n    riskCuesMsg: '如果签约方未实名签署，在文件发生纠纷时，需要您自己提供该签约方身份认定的证据。如需避免风险，请选择需要实名。',\n    confirmBtnText: '选择需要实名',\n    cancelBtnText: '选择不需要实名',\n    attachLengthErr: '您最多只能为单个签署人添加50个附件要求',\n    collapse: '收起',\n    expand: '展开',\n    delete: '删除',\n    saySomething: '说点什么吧',\n    addImage: '添加文档',\n    addImageTips: '（支持word、pdf以及图片，不超过3份文档）',\n    give: '给',\n    fileMax: '上传数量超过数量上限!',\n    signerLimit: '您当前的版本不支持超过{limit}个相对签署/抄送方。',\n    showExamle: '查看示例图片',\n    downloadExamle: '下载示例文件'\n  },\n  addReceiverGuide: {\n    guideTitle: '如何添加新的签署人',\n    receiverType: '您需要选择签署人参与合同的方式（六选一）：',\n    asEntSign: '代表企业签署：',\n    signatureSub: '法人或高管在合同上代表企业签字。签字完成后的合同是可以被企业收走的',\n    vipOnly: '高级版本可用',\n    sealSub: '签署人需在合同上加盖公章或合同专用章等',\n    stampSub: '签署人既要盖章，也要代表企业签字',\n    confirmSeal: '代表企业使用业务核对章',\n    confirmSealSub: '财务对账单、询证函等文件先核实再盖章',\n    asPersonSign: '代表个人签署：',\n    asPersonSignTip: '仅代表个人签字，不代表任何企业',\n    asPersonSignDesc: '签署人的私人合同，如借贷合同、入职离职协议等',\n    scanSign: '扫码签字',\n    scanSignDesc: '发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景',\n    selectSignTypeTip: '请先选择签署方参与合同的方式',\n    notRemind: '下次不再提醒',\n    sign: '签字',\n    entSign: '企业签字',\n    stamp: '盖章',\n    stampSign: '盖章并签字',\n    requestSeal: '业务核对章'\n  },\n  linkContract: {\n    title: '关联合同',\n    connectMore: '关联更多合同',\n    placeholder: '请输入合同编号',\n    revoke: '合同已撤销',\n    overdue: '逾期未签',\n    approvalNotPassed: '审批被驳回',\n    reject: '合同已拒签',\n    signing: '签署中',\n    complete: '已完成',\n    approvaling: '审批中',\n    disconnect: '解除关联',\n    disconnectSuccess: '解除关联成功',\n    connectLimit: '关联合同数量上限为100份'\n  },\n  field: {\n    fieldTip: {\n      title: '缺少签署位置',\n      error: '没有在以下合同指定签署位置（{type}）',\n      add: '添加字段',\n      continue: '继续发送'\n    },\n    accountCharge: {\n      notice: '该合同按参与账号数计费',\n      able: '可以正常发送',\n      unable: '可用账号数不足，请联系上上签客服',\n      notify: '该合同给所有签约方发英文通知',\n      noNotify: {\n        1: '该合同不发签约相关通知',\n        2: '（包括签署、审批、抄送、截止签署等通知短信和邮件）'\n      }\n    },\n    ridingStamp: '骑缝章',\n    watermark: '水印',\n    senderSignature: '盖章人签字',\n    optional: '选填',\n    clickDecoration: '点击合同装饰',\n    decoration: '合同装饰',\n    sysError: '系统繁忙，请稍后再试',\n    partedMarkedError: '指定了“盖章并签字”的签约方，必须同时指定盖章和签字',\n    fieldTitle: '共有{length}份合同需要指定签署位置',\n    send: '发送',\n    contractDispatchApply: '申请发送合同',\n    contractNeedYouSign: '该文件需要您签署',\n    ifSignRightNow: '是否马上签署',\n    signRightNow: '马上签署',\n    signLater: '稍后签署',\n    signaturePositionErr: '请为每个签署方指定签署位置',\n    sendSucceed: '发送成功',\n    confirm: '确定',\n    cancel: '取消',\n    qrCodeTips: '签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改',\n    pagesField: '第{currentPage}页，共{totalPages}页',\n    suitableWidth: '适合宽度',\n    signCheck: '签名查验',\n    locateSignaturePosition: '定位签署位置',\n    locateTips: '可以帮助快速定位到签署位置。当前仅支持定位每个签署方第一个签署位置',\n    step1: '第一步',\n    selectSigner: '选择签约方',\n    step2: '第二步',\n    dragSignaturePosition: '拖动签署位置',\n    signingField: '签署字段',\n    docTitle: '文档',\n    totalPages: '页数：{totalPages}页',\n    receiver: '接收方',\n    delete: '删除',\n    deductPublicNotice: '对私合同可用份数不足时会扣除对公合同',\n    unlimitedNotice: '该合同计费不限量使用',\n    charge: '计费',\n    units: '{num}份',\n    contractToPrivate: '对私合同',\n    contractToPublic: '对公合同',\n    costTips: {\n      1: '对公合同：签署人（不包含发件人）中有企业账户的合同',\n      2: '对私合同：签署人（不包含发件人）中没有企业账户的合同',\n      3: '计费份数根据文件份数计算',\n      4: '计费份数 = 文件份数 × 批量导入用户组（行）数'\n    },\n    costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',\n    toCharge: '去充值',\n    contractNeedCharge: {\n      1: '可用合同份数不足，无法发送',\n      2: '可用合同份数不足，请联系主管理员充值'\n    },\n    chooseApprover: '选择审批人：',\n    nextStep: '下一步',\n    submitApproval: '提交审批',\n    autoSendAfterApproval: '*审批通过后，自动发送合同',\n    chooseApprovalFlow: '请选择一个审批流',\n    completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',\n    viewPrivateLetter: '查看私信',\n    addPrivateLetter: '添加私信',\n    append: '添加',\n    privateLetter: '私信',\n    signNeedKnow: '签约须知',\n    maximum5M: '请上传小于5M的文档',\n    uploadServerFailure: '上传到服务器失败',\n    uploadFailure: '上传失败',\n    pager: '页码',\n    seal: '盖章',\n    signature: '签名',\n    signDate: '签署日期',\n    text: '文本',\n    date: '日期',\n    qrCode: '二维码',\n    number: '数字',\n    dynamicTable: '动态表格',\n    terms: '合同条款',\n    checkBox: '复选框',\n    radioBox: '单选框',\n    image: '图片'\n  },\n  addressBook: {\n    innerMember: {\n      title: '企业内部成员',\n      tips: '调整企业成员信息，让发件人都能更快找到内部联系人',\n      operation: '去控制台'\n    },\n    outerContacts: {\n      title: '外部企业联系人',\n      tips: '邀请您的合作伙伴提前注册实名，以便与您顺利开展业务',\n      operation: '邀请您的合作伙伴'\n    },\n    myContacts: {\n      title: '我的联系人',\n      tips: '修改联系人，确保签署人信息准确无误',\n      operation: '去用户中心'\n    },\n    selected: '已选账号',\n    search: '搜索',\n    loadMore: '加载更多',\n    end: '全部加载完成'\n  },\n  dataBoxInvite: {\n    title: '邀请您的合作伙伴',\n    step1: '分享链接给您的合作伙伴提前创建企业',\n    step2: '通过链接/二维码授权后的合作伙伴会出现在地址簿',\n    step3: '在“档案+”对您的合作伙伴做更多管理',\n    imgName: '分享采集二维码',\n    saveQrcode: '保存二维码到本地',\n    copy: '复制',\n    copySuccess: '复制成功',\n    copyFailed: '复制失败'\n  },\n  shareView: {\n    title: '转发审阅',\n    account: '手机号/邮箱',\n    role: '审阅人角色',\n    note: '备注',\n    link: '链接：',\n    signerMessage: '签署人留言',\n    rolePlaceholder: '如公司法务、部门领导等',\n    notePlaceholder: '给审阅人留言，200字以内',\n    generateLink: '生成链接',\n    saveQrcode: '保存微信小程序码',\n    regenerateLink: '重新生成链接',\n    inputAccount: '请输入手机号码或者邮箱',\n    inputCorrectAccount: '请输入正确的手机号码或者邮箱',\n    accountInputTip: '为确保链接正常打开，请准确输入合同审阅人信息',\n    shareLinkTip1: '保存微信小程序码或',\n    shareLinkTip: '复制链接，分享给审阅人',\n    linkTip1: '合同正文属于保密内容，非必要情况下请勿外泄',\n    linkTip2: '链接有效期为2天；重新生成链接后，历史链接自动失效'\n  },\n  recoverSpecialSeal: {\n    title: '印章无法使用',\n    description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',\n    description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',\n    postRecover: '申请恢复印章',\n    note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',\n    requestSend: '恢复申请提交成功'\n  },\n  paperSign: {\n    title: '使用纸质方式签署',\n    stepText: ['下一步', '确认纸质签', '确定'],\n    needUploadFile: '请先上传扫描件',\n    uploadError: '上传失败',\n    cancel: '取消',\n    downloadPaperFile: '获取纸质签文件',\n    step0: {\n      title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n      address: '邮寄地址：',\n      contactName: '接收人姓名：',\n      contactPhone: '接收人联系方式：',\n      defaultValue: '请通过线下方式向发件方索取'\n    },\n    step1: {\n      title0: '第一步：下载&打印纸质合同',\n      title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n      title1: '第二步：加盖印章',\n      title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n      title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n      title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。']\n    },\n    step2: {\n      title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n      uploadFile: '上传扫描件',\n      getCodeVerify: '获取合同签署校验',\n      isUploading: '上传中...'\n    }\n  },\n  allowPaperSignDialog: {\n    title: '允许纸质签',\n    content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',\n    tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',\n    icon: '转纸质签署 >>',\n    goSign: '去电子签',\n    cancel: '取消'\n  },\n  sealInconformityDialog: {\n    errorSeal: {\n      title: '印章提示',\n      tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n      tip1: '检测到有企业印章与企业名称：',\n      tip2: '是否要继续使用当前印章图片？',\n      tip3: '根据发件方要求，您需要使用企业名称为：',\n      tip4: '的印章',\n      tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n      tip6: '不匹配，请确保印章符合发件方要求。',\n      guide: '如何上传正确的印章 >>',\n      next: '继续使用',\n      tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',\n      tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？'\n    },\n    exampleSeal: {\n      title: '上传印章图案方式',\n      way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n      way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n      errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照']\n    },\n    confirm: '确认',\n    cancel: '取消'\n  },\n  addSealDialog: {\n    title: '添加印章图片',\n    dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNG等），由系统将此印章图片合并进入当前合同中。',\n    dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',\n    updateNewSeal: '上传新章'\n  }\n};", "map": {"version": 3, "names": ["ssoLoginConfig", "notBelongToEntTip", "operationStep", "one", "two", "three", "continue", "cancel", "tip", "sign", "sealLabelsTip", "nonMainlandCARenewalTip", "reselect", "approvalFeatures", "dialogTitle", "understand", "feature1", "feature2", "tip1", "tip2", "tip3", "tip4", "tip5", "tip6", "annotate", "delete", "edit", "operateTitle", "placeholder", "needRemark", "notNeedRemark", "switchToReceiver", "notAddEntTip", "contractPartiesYouChoose", "contractPartyFilled", "certifyOtherCompanies", "youCanAlso", "needVerification", "prompt", "submit", "addSeal", "noSealAvailable", "memberNoSealAvailable", "noticeAdminFoSeal", "requestSomeone", "requestOthersToContinue", "requestOthersToContinueSucceed", "requestSomeoneList", "electronicSeal", "changeTheSeal", "goToVerify", "noSealToC<PERSON>ose", "goVerify", "goToVerifyEnt", "digitalCertificateTip", "signDes", "signAgain", "send", "person", "ent", "entName", "account", "accountPH", "approved", "signVerification", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFail", "connectFailTip", "connectFailTip1", "connectFailTip2", "connectFailTip3", "personalMaterials", "noSupportface", "lackEntName", "errAccount", "noticeAdmin", "signDone", "signDoneTip", "approveDone", "approveDoneTip", "completeSign", "<PERSON><PERSON><PERSON><PERSON>", "stillSignTip", "signHighLightTip", "riskDetails", "noviewDifference", "highLightTip", "commonTip", "view", "start", "nextStep", "help", "faceFailed", "faceFailedtips", "dualFailed", "verifyTry", "faceLimit", "upSignReq", "reqFace", "signAfterFace", "qrcodeInvalid", "faceFirstExceed", "date", "chooseSeal", "seal", "signature", "handwrite", "mysign", "approve<PERSON>lace", "approvePlace_1", "approvePlace_2", "approveAgree", "approveReject", "signBy", "signByEnd", "sealBy", "sealByEnd", "coverBy", "applicant", "continueVeri", "registerAndReal", "goToResiter", "sureToUse", "toSign", "pleaseComplete", "confirmSign", "admin", "contratAdmin", "addToEnt", "alreadyExists", "sendMsg", "<PERSON><PERSON><PERSON><PERSON>", "title", "viewImg", "priLetter", "priLetterFromSomeone", "readLetter", "approve", "disapprove", "refuseSign", "paperSign", "refuseTip", "refuseReason", "reasonWriteTip", "refuseReasonOther", "refuseConfirm", "refuseConfirmTip", "waitAndThink", "signValidationTitle", "email", "phoneNumber", "password", "verificationCode", "mailVerificationCode", "forgetPsw", "if", "forgetPassword", "rejectionVer", "msgTip", "voiceVerCode", "SMSVerCode", "or", "emailVerCode", "SentSuccessfully", "intervalTip", "signPsw", "useSignPsw", "setSignPsw", "useVerCode", "inputVerifyCodeTip", "inputSignPwdTip", "signConfirmTip", "confirm", "signSuc", "refuseSuc", "approveSuc", "hdFile", "otherOperations", "reviewDetails", "close", "submitter", "signatory", "reviewSchedule", "signByPc", "signPageDescription", "sealBySomeone", "signDate", "download", "signPage", "signNow", "sender", "signer", "startSignTime", "signDeadLine", "authGuide", "goToHome", "tip_1", "tip_2", "tip_3", "tip_4", "tip_5", "new_tip_1", "new_tip_2", "new_tip_3", "new_tip_4", "entUserName", "idNumberForVerify", "realNameAuth", "applySeal", "signContract", "switch", "rejectReasonList", "signOperateReason", "termReason", "explainReason", "otherReason", "selectSignature", "selectS<PERSON>er", "pleaseScanToSign", "pleaseScanAliPay", "pleaseScanWechat", "requiredFaceSign", "requiredDualSign", "verCodeVerify", "applyToSign", "autoRemindAfterApproval", "cannotSignBeforeApproval", "finishSignatureBeforeSign", "uploadFileOnRightSite", "cannotApplySealNeedPay", "cannotOtherSealReason", "unlimitedNotice", "units", "contractToPrivate", "contractToPublic", "paySum", "payTotal", "fundsLack", "contactToRecharge", "deductPublicNotice", "needSignerPay", "recharge", "toSubmit", "appliedSeal", "noSeal", "noSwitchSealNeedDistribute", "viewApproveProcess", "approveProcess", "noApprove<PERSON><PERSON>nt", "knew", "noSwitchSealNeedAppend", "hadAutoSet", "setThatSignature", "setThatSeal", "applyThatSeal", "hasSetTip", "hasSetSealTip", "hasSetSignatureTip", "hasApplyForSealTip", "savedOnLeftSite", "ridingSealMinLimit", "ridingSealMaxLimit", "ridingSealMinOrMaxLimit", "noSealForRiding", "noSwitchSealNeedAppendBySelf", "gotoAppendSeal", "approvalFlowSuccessfulSet", "mandate", "loginToAppendSeal", "signIdentityAs", "enterNextContract", "fileList", "addSignerFile", "signatureFinish", "dragSignatureTip", "noticeToManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senderRequire", "senderRequireUseFollowIdentity", "suggestToAuth", "contactEntAdmin", "set<PERSON>ourAccount", "authInfoUnMatchNeedResend", "noEntNameNeedResend", "pleaseUse", "me", "myself", "reAuthBtnTip", "reAuthBtnContent", "descNoSame1", "descNoSame2", "authInfoNoSame", "authInfoNoSame2", "goHome", "authInfo", "authInfo2", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmOk", "goOnAuth", "signContractAfterAuth", "useIdentity", "inTheName", "of", "identity", "nameIs", "IDNumIs", "provideMoreAuthData", "leadToAuthBeforeSign", "groupProxyAuthNeedMore", "contactSender", "note", "identityInfo", "signNeedCoincidenceInfo", "needAuthPermissionContactAdmin", "iHadReadContract", "scrollToBottomTip", "getVerCodeFirst", "appScanVerify", "downloadBSApp", "scanned", "confirmInBSApp", "qrCodeExpired", "appKey", "goToScan", "setNotificationInUserCenter", "doNotWantUseVerCode", "try", "retry", "goToFaceVerify", "faceExceedTimes", "returnBack", "switchTo", "youCanChooseIdentityBlow", "needDrawSignatureFirst", "lacksSealNeedAppend", "manageSeal", "needDistributeSealToSelf", "chooseSealAfterAuth", "appendDrawSignature", "senderUnFill", "declare", "fileLessThan", "fileNeedUploadImg", "serverError", "oldFormatTip", "fileLimitFormatAndSize", "fileFormatImage", "fileFormatFile", "signNeedKnow", "signNeedKnowFrom", "approvalInfo", "approveNeedKnowFrom", "approveBeforeSend", "approveBeforeSign", "approveOperator", "approvalOpinion", "employeeDefault", "<PERSON><PERSON><PERSON><PERSON>", "addRidingSeal", "delRidingSeal", "file", "compressedFile", "attachmentContent", "pleaseClickView", "downloadFile", "noLabelPleaseAppend", "archiveTo", "hadArchivedToFolder", "pleaseScanToHandleWrite", "save", "remind", "riskTip", "chooseApp<PERSON><PERSON><PERSON>", "chooseAdminSign", "useSealByOther", "getSeal", "nowApplySealList", "nowAdminSealList", "chooseApplyPersonToDeal", "chooseTransferPerson", "chooseApplyPersonToMandate", "contactGroupAdminToDistributeSeal", "sealApplySentPleaseWait", "successfulSent", "authTip", "t2", "t3", "tCommon1", "tCommon2_1", "tCommon2_2", "tCommon2_3", "viewAndSign1", "viewAndSignConflict", "needSomeoneToSignature", "needToSet", "approver", "clickToSignature", "transferToOtherToSign", "signatureBy", "tipRightNumber", "tipRightIdCard", "tipRightPhoneNumber", "tipRequired", "viewContractDetail", "required", "optional", "decimalLimit", "intLimit", "invalidContract", "No", "chooseFrom2", "crossPlatformCofirm", "message", "confirmButtonText", "cancelButtonText", "sealScope", "currentContract", "allContract", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixTextDisplay", "allPage", "notJoinTip", "signJa", "beforeSignTip1", "beforeSignTip2", "beforeSignTip3", "beforeSignTip4", "beforeSignTip5", "beforeSignTip6", "beforeSignTip7", "entNamePlaceholder", "corporateNumberPlaceholder", "corporateNumber", "singerNamePlaceholder", "<PERSON><PERSON><PERSON>", "businessPic", "waitApprove", "itsMe", "wrongInformation", "confirmChange", "communicateSender1", "communicateSender2", "createSeal", "emptyErr", "areaRegister", "jp", "cn", "are", "other", "plsSelect", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "comNum", "buyRecord", "selectArea", "uaeTip1", "uaeTip2", "uaeTip3", "signPC", "commonSign", "contractVerification", "VerCodeVerify", "QrCodeVerify", "verifyTip", "verifyAllTip", "selectSeal", "adminGuideTip", "toAddSealWithConsole", "use", "toAddSeal", "mySeal", "operationCompleted", "FDASign", "signer<PERSON>dd", "signerEdit", "editTip", "inputNameTip", "inputName", "signerNameFillTip", "plsInput", "customInput", "signPlace<PERSON>y<PERSON><PERSON><PERSON>", "signGuide", "howDragSeal", "howDragSignature", "iKnow", "step", "two1", "two2", "dragSeal", "continueDragSeal", "dragSignature", "continueDragSignature", "dragPlace", "notR<PERSON>ind", "signTip", "continueOperation", "success", "exitApproval", "continueApproval", "next", "none", "approvalProcess", "receiver", "contractDetail", "downloadBtn", "tips", "SigningCompleted", "submitCompleted", "noTurnSign", "noRightSign", "noNeedSign", "ApprovalCompleted", "contractRevoked", "contractRefused", "linkExpired", "contractClosed", "approvalReject", "approving", "viewContract", "viewContractList", "needMeSign", "downloadContract", "signed", "approval", "personHas", "personHave", "person<PERSON>asnot", "<PERSON><PERSON><PERSON><PERSON>", "headsTaskDone", "headsTaskNotDone", "taskStatusBetween", "cannotDownload", "privateStorage", "beenDeleted", "unActive", "back", "contratStatusDes", "contractConditionDes", "contractIng", "contractComplete", "dataProduct", "btnText", "signOnGoing", "operate", "freeContract", "sendContract", "congratulations", "carbonSaving", "signGift", "followPublic", "congratulations<PERSON><PERSON><PERSON>", "carbonSavingSingle", "viewContractTip", "congratulationsCn", "carbonSavingSingleCn", "carbonVerification", "ok", "prepare", "sealArea", "senderNotice", "preSetDialogConfirm", "preSetDialogContact", "preSetDialogInfo", "preSetDialogTitle", "initialValues", "proxyUpload", "signHeaderTitle", "step1", "confirmSender", "step2", "uploadFile", "step3", "addSigner", "actionDemo", "isUploadingErr", "noUploadFileErr", "noContractTitleErr", "contractTypeErr", "expiredDateErr", "noExpiredDateErr", "<PERSON><PERSON><PERSON><PERSON><PERSON>rr", "noRecipientsErr", "noAccountErr", "noUserNameErr", "noIDNumberErr", "accountFormatErr", "userNameFormatErr", "enterpriseNameErr", "idNumberForVerifyErr", "signer<PERSON>rr", "noSignerErr", "lackAttachmentNameErr", "repeatRecipientsErr", "innerContact", "outerContact", "search", "accountSelected", "groupNameAll", "unclassified", "beExcel", "usePdf", "usePdfFile", "fileNameMoreThan", "need<PERSON>dd<PERSON><PERSON>", "addSender", "addReceiver", "English", "Japanese", "Chinese", "Arabic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limitFaceConfigTip", "individual", "enterprise", "addInstructions", "instructionsContent", "addContractingInfo", "contractingInfoContent", "payer", "handWriting", "realName", "sameTip", "proxy", "aboradTip", "busRole", "busRoleTip", "busRolePlaceholder", "handWritingTip", "instructions", "contractingParty", "signer<PERSON><PERSON>", "afterReadingTitle", "afterReading", "handWritingTips", "SsTitle", "SsTip", "stamp", "Ss", "mutexError", "handWriteNotAllowed", "forceHandWrite", "faceFirst", "faceVerify", "attachmentRequired", "newAttachmentRequired", "attachmentError", "<PERSON><PERSON><PERSON>", "orderSignLabel", "contactAddress", "signOrder", "accountPlaceholder", "accountPlaceholderJa", "accountReceptionCollection", "accountReceptionCollectionTip1", "accountReceptionCollectionTip2", "signSub<PERSON><PERSON>erson", "nameTips", "requiredNameTips", "entOperatorNameTips", "needAuth", "operatorNeedAuth", "signSubjectEnt", "entNameTips", "operator", "more", "faceFirstTips", "mustFace", "mustHandWrite", "fillIDNumber", "fillNoticeCall", "fillNoticeCallTips", "addNotice", "attachTips", "faceSign", "faceSignTips", "handWriteNotAllowedTips", "handWriteTips", "idNumberTips", "verifyBefore", "verify", "verifyTips", "verifyTips2", "sendToThirdPlatform", "platFormName", "fillThirdPlatFormName", "attach", "attachName", "exampleID", "attachInfo", "attachInfoTips", "addAttachRequire", "addSignEnt", "addSign<PERSON>erson", "selectContact", "searchVerify", "fillImageContentTips", "findContact", "signer<PERSON><PERSON>s", "add", "notAdd", "cc", "notNeedAuth", "operatorNotNeedAuth", "extracting", "autoFill", "failExtracting", "noEntNameErr", "riskCues", "riskCuesMsg", "confirmBtnText", "cancelBtnText", "attachLengthErr", "collapse", "expand", "saySomething", "addImage", "addImageTips", "give", "fileMax", "signerLimit", "showExamle", "downloadExamle", "addReceiverGuide", "guideTitle", "receiverType", "asEntSign", "signatureSub", "vipOnly", "sealSub", "stampSub", "confirmSeal", "confirmSealSub", "asPersonSign", "asPersonSignTip", "asPersonSignDesc", "scanSign", "scanSignDesc", "selectSignTypeTip", "entSign", "stampSign", "requestSeal", "linkContract", "connectMore", "revoke", "overdue", "approvalNotPassed", "reject", "signing", "complete", "approvaling", "disconnect", "disconnectSuccess", "connectLimit", "field", "fieldTip", "error", "accountCharge", "notice", "able", "unable", "notify", "noNotify", "ridingStamp", "watermark", "senderSignature", "clickDecoration", "decoration", "sysError", "partedMarkedError", "fieldTitle", "contractDispatchApply", "contractNeedYouSign", "ifSignRightNow", "signRightNow", "signLater", "signaturePositionErr", "sendSucceed", "qrCodeTips", "pagesField", "suitableWidth", "signCheck", "locateSignaturePosition", "locateTips", "dragSignaturePosition", "<PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "totalPages", "charge", "costTips", "costInfo", "to<PERSON>harge", "contractNeedCharge", "chooseApprover", "submitApproval", "autoSendAfterApproval", "chooseApprovalFlow", "completeApprovalFlow", "viewPrivateLetter", "addPrivateLetter", "append", "privateLetter", "maximum5M", "uploadServerFailure", "uploadFailure", "pager", "text", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "addressBook", "innerMember", "operation", "outerContacts", "myContacts", "selected", "loadMore", "end", "dataBoxInvite", "imgName", "saveQrcode", "copy", "copySuccess", "copyFailed", "shareView", "role", "link", "signerMessage", "rolePlaceholder", "notePlaceholder", "generateLink", "regenerateLink", "inputAccount", "inputCorrectAccount", "accountInputTip", "shareLinkTip1", "shareLinkTip", "linkTip1", "linkTip2", "recoverSpecialSeal", "description1", "description2", "postRecover", "requestSend", "stepText", "needUploadFile", "uploadError", "downloadPaperFile", "step0", "address", "contactName", "contactPhone", "defaultValue", "title0", "title0Desc", "title1", "title1Desc", "title2", "title2Desc", "getCodeVerify", "isUploading", "allowPaperSignDialog", "content", "icon", "goSign", "sealInconformityDialog", "errorSeal", "guide", "exampleSeal", "way1", "way2", "errorWay", "addSealDialog", "dec1", "dec2", "updateNewSeal"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/sign/sign-zh.js"], "sourcesContent": ["export default {\n    ssoLoginConfig: {\n        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',\n        operationStep: {\n            one: '第一步 点击继续后，返回登录页面',\n            two: '第二步 输入密码，进入上上签平台',\n            three: '第三步 发送合同（或管理模板）',\n        },\n        continue: '继续',\n        cancel: '取消',\n        tip: '提示',\n    },\n    sign: {\n        sealLabelsTip: '您需要在合同上盖{sealLabelslen}个章。{personStr}将为您盖{otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已在页面上展示。请确认是否继续。',\n        continue: '继续',\n        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n        reselect: '重选',\n        approvalFeatures: {\n            dialogTitle: '新功能介绍',\n            understand: '我知道了',\n            feature1: '划句批注',\n            feature2: '字段高亮',\n            tip1: '点击按钮将合同中的所有“模板内容字段”高亮，方便抓取关键信息。',\n            tip2: '点击左下角提示按钮，开启模板内容字段高亮。',\n            tip3: '通过高亮，快速定位合同的内容填写字段，高效完成审批。',\n            tip4: '按住鼠标圈选一个段落后松开鼠标，点击批注按钮可添加批注文本，完成后点击修改或删除。批注的内容可在合同详情页-公司内部操作日志中查看。',\n            tip5: '第一步：选中所需批注文本字段，添加批注；',\n            tip6: '第二步：点击编辑或删除批注。',\n            annotate: '批注',\n            delete: '删除',\n            edit: '修改',\n            operateTitle: '添加审批批注',\n            placeholder: '不超过255个字',\n        },\n        needRemark: '您还需要填写备注',\n        notNeedRemark: '您不需要填写备注',\n        switchToReceiver: '已为您切换至{receiver}',\n        notAddEntTip: '当前用户不是该企业成员，请联系主管理员加入企业。',\n        contractPartiesYouChoose: '您可以选择的签约主体:',\n        contractPartyFilled: '发件人填写的签约主体为:',\n        certifyOtherCompanies: '认证其他企业',\n        youCanAlso: '您也可以：',\n        needVerification: '您需要实名认证后才能签署',\n        prompt: '提示',\n        submit: '确定',\n        cancel: '取消',\n        sign: '立即签约',\n        addSeal: '请使用电脑登录上上签官网添加印章',\n        noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',\n        memberNoSealAvailable: '当前无可用印章，请联系管理员配置后再签署。或者线下联系主管理员配置。',\n        noticeAdminFoSeal: '发通知给主管理员',\n        requestSomeone: '请求他人认证',\n        requestOthersToContinue: '通知主管理员补充实名认证',\n        requestOthersToContinueSucceed: '已向管理员发送通知',\n        requestSomeoneList: '请求以下人员完成实名认证：',\n        electronicSeal: '电子公章',\n        changeTheSeal: '不想使用该印章？实名认证后可更换印章',\n        goToVerify: '去实名认证',\n        noSealToChoose: '没有可切换的印章，如果需要管理印章，请先进行实名认证',\n        goVerify: '去认证',\n        goToVerifyEnt: '去认证企业',\n        digitalCertificateTip: '上上签正在调用您的数字证书',\n        signDes: '您在安全签约环境中，请放心签署！',\n        signAgain: '继续签署',\n        send: '发送',\n        person: '个人',\n        ent: '企业',\n        entName: '企业名称',\n        account: '账号',\n        accountPH: '手机或邮箱',\n        approved: '审批',\n        signVerification: '签署',\n        cannotReview: '无法查看合同',\n        connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',\n        connectFailTip: '您可以尝试以下方法解决问题：',\n        connectFailTip1: '1、刷新页面。',\n        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n        personalMaterials: '发件人要求您补充更多认证材料',\n        noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',\n        lackEntName: '请填写企业名称',\n        errAccount: '请填写正确的邮箱或手机号',\n        noticeAdmin: '申请加入',\n        signDone: '签署完成',\n        signDoneTip: '您已签署该合同',\n        approveDone: '审批完成',\n        approveDoneTip: '您已审批该合同',\n        completeSign: '请先点击“盖章处”或“签字处”完成签署',\n        fillFirst: '请先在输入框中填写合同内容',\n        stillSignTip: '在您签署此{alias}后，仍有其他签署方可能更改{alias}内容，是否继续签署？',\n        signHighLightTip: '可被新增或修改的{alias}内容共{count}处',\n        riskDetails: '风险详情',\n        noviewDifference: '由于发起方开启了签署方可以填写本{alias}固定字段的功能，本{alias}其他签署方仍可能更改发起方指定的合同内容，上上签不对本{alias}的签署前版本与生效版本之间的内容差异进行审核，当您签署完本{alias}后，视为您同意其它签署方对{alias}内容中固定字段内容的增加或修改，并认可本{alias}各签署方均签署完成后的生效版本。\\n' +\n            '如果您不同意在您签署后其他签署方仍可以变更本{alias}字段，您可以拒绝本次签署，并与发件方协商（即要求发起方关闭\"签署人填写字段\"功能，规避您的相应风险）。',\n        highLightTip: '这些有风险的内容将会被呈现为“高亮”效果，请仔细核对。重新刷新页面可取消高亮效果。',\n        commonTip: '提示',\n        understand: '我知道了',\n        view: '查看',\n        start: '开始',\n        nextStep: '下一步',\n        help: '帮助',\n        faceFailed: '非常抱歉，您的人脸比对失败',\n        faceFailedtips: '提示',\n        dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',\n        verifyTry: '请核实身份信息后重试',\n        faceLimit: '今天的人脸比对次数已达到上限',\n        upSignReq: '今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求',\n        reqFace: '发件人要求你进行刷脸校验',\n        signAfterFace: '刷脸通过后即可完成合同签署',\n        qrcodeInvalid: '二维码信息已过期，请刷新',\n        faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',\n        date: '日期',\n        chooseSeal: '选择印章',\n        seal: '印章',\n        signature: '签名',\n        handwrite: '手写',\n        mysign: '我的签名',\n        approvePlace: '审批留言，可不填',\n        approvePlace_1: '审批留言',\n        approvePlace_2: '选填，不超过255字。',\n        approveAgree: '审批结果：同意',\n        approveReject: '审批结果：驳回',\n        signBy: '由',\n        signByEnd: '盖章',\n        sealBy: '由',\n        sealByEnd: '签名',\n        coverBy: '需盖',\n        applicant: '申请人',\n        continueVeri: '继续认证',\n        registerAndReal: '请注册并实名',\n        goToResiter: '请注册并认证',\n        sureToUse: '确定使用',\n        toSign: '签约吗?',\n        pleaseComplete: '请先完成',\n        confirmSign: '再确认签署',\n        admin: '管理员',\n        contratAdmin: '请联系管理员将您的账号',\n        addToEnt: '添加为企业成员',\n        alreadyExists: '在上上签已存在',\n        sendMsg: '上上签将以短信形式给管理员发以下内容：',\n        applyJoin: '申请加入',\n        title: '标题',\n        viewImg: '查看图片',\n        priLetter: '私信',\n        priLetterFromSomeone: '来自{name}的私信',\n        readLetter: '我知道了',\n        approve: '同意',\n        disapprove: '驳回',\n        refuseSign: '拒签',\n        paperSign: '改用纸质签署',\n        refuseTip: '请选择拒绝理由',\n        refuseReason: '填写拒签理由有助于对方了解你的问题，加快合同流程',\n        reasonWriteTip: '请填写拒签理由',\n        refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',\n        refuseConfirm: '拒签',\n        refuseConfirmTip: '您以\"{reason}\"理由拒绝签署，是否继续？确定后将不可再次签署。',\n        waitAndThink: '我再想想',\n        signValidationTitle: '签署校验',\n        email: '邮箱',\n        phoneNumber: '手机号',\n        password: '密码',\n        verificationCode: '验证码',\n        mailVerificationCode: '验证码',\n        forgetPsw: '忘记密码',\n        if: '，是否',\n        forgetPassword: '忘记密码',\n        rejectionVer: '拒签校验',\n        msgTip: '一直收不到信息？试试',\n        voiceVerCode: '语音验证码',\n        SMSVerCode: '短信验证码',\n        or: '或',\n        emailVerCode: '邮箱验证码',\n        SentSuccessfully: '发送成功！',\n        intervalTip: '发送时间间隔过短',\n        signPsw: '签约密码',\n        useSignPsw: '使用签约密码校验',\n        setSignPsw: '设置签约密码校验',\n        useVerCode: '使用验证码校验',\n        inputVerifyCodeTip: '请输入验证码',\n        inputSignPwdTip: '请输入签约密码',\n        signConfirmTip: {\n            1: '您是否确定要签署此{contract}？',\n            2: '点击确定按钮将立即签署此{contract}',\n            confirm: '确认签署',\n        },\n        signSuc: '签署成功',\n        refuseSuc: '拒签成功',\n        approveSuc: '审批成功',\n        hdFile: '查看高清文件',\n        otherOperations: '其他操作',\n        reviewDetails: '审批详情',\n        close: '关 闭',\n        submitter: '提交人',\n        signatory: '签署人',\n        reviewSchedule: '审批进度',\n        signByPc: '由{name}签名',\n        signPageDescription: '第{index}页, 共{total}页',\n        sealBySomeone: '由{name}盖章',\n        signDate: '签署日期',\n        download: '下载',\n        signPage: '页数：{page}页',\n        signNow: '立即签署',\n        sender: '发件方',\n        signer: '签约方',\n        startSignTime: '发起签约时间',\n        signDeadLine: '签约截止时间',\n        authGuide: {\n            goToHome: '回到首页',\n            tip_1: '认证完成后，可查看并签署合同。',\n            tip_2: '请使用身份 | 进行认证。',\n            tip_3: '发来合同',\n            tip_4: '请联系合同发起者 | 更改收件人。',\n            tip_5: '您认证的 | 无法查看合同',\n            new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',\n            new_tip_2: '基于发件方的合规要求，您需要以：',\n            new_tip_3: '完成以下步骤。',\n            new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',\n            entUserName: '姓名：',\n            idNumberForVerify: '身份证号：',\n            realNameAuth: '实名认证',\n            applySeal: '申请印章',\n            signContract: '签署合同',\n        },\n        switch: '切换',\n        rejectReasonList: {\n            // authReason: '不想/不会做实名认证',\n            signOperateReason: '对签署操作/校验操作有疑问，需要进一步沟通',\n            termReason: '对合同条款/内容有疑议，需要进一步沟通',\n            explainReason: '对合同内容不知情，请提前告知',\n            otherReason: '其他（请填写理由）',\n        },\n        selectSignature: '选择签名',\n        selectSigner: '选择签名人',\n        pleaseScanToSign: '请用支付宝或微信扫一扫签署',\n        pleaseScanAliPay: '请使用支付宝app扫描二维码签署',\n        pleaseScanWechat: '请使用微信app扫描二维码签署',\n        requiredFaceSign: '合同发件人要求您刷脸签署',\n        requiredDualSign: '合同发件人要求你使用双录校验',\n        verCodeVerify: '验证码校验',\n        applyToSign: '申请签署合同',\n        autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',\n        cannotSignBeforeApproval: '审批未完成，暂不能签署！',\n        finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',\n        uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',\n        cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',\n        cannotOtherSealReason: '合同开启了刷脸签署校验，不支持他人盖章',\n        unlimitedNotice: '该合同计费不限量使用',\n        units: '{num}份',\n        contractToPrivate: '对私合同',\n        contractToPublic: '对公合同',\n        paySum: '共{sum}需要您支付',\n        payTotal: '共计{total}元。',\n        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值。',\n        contactToRecharge: '请联系主管理员充值。',\n        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',\n        needSignerPay: '合同发送方设置了相对方到付，并指定由您来支付合同费用。',\n        recharge: '充值',\n        toSubmit: '提交',\n        appliedSeal: '用印申请已提交',\n        noSeal: '无印章',\n        noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',\n        viewApproveProcess: '查看审批流程',\n        approveProcess: '审批流程',\n        noApproveContent: '未提交审批资料',\n        knew: '知道了',\n        noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',\n        hadAutoSet: '已在另外{num}处自动',\n        setThatSignature: '放置该签名',\n        setThatSeal: '放置该印章',\n        applyThatSeal: '申请该印章',\n        hasSetTip: '已在另外{index}处自动放置',\n        hasSetSealTip: '已在另外{index}处自动放置该印章',\n        hasSetSignatureTip: '已在另外{index}处自动放置该签名',\n        hasApplyForSealTip: '已在另外{index}处自动申请该印章',\n        savedOnLeftSite: '已保存到左侧签名栏',\n        ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',\n        ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',\n        ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',\n        noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',\n        noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',\n        gotoAppendSeal: '去添加印章',\n        approvalFlowSuccessfulSet: '审批流设置成功',\n        mandate: '同意授权',\n        loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',\n        signIdentityAs: '当前正在以{person}的名义签署合同',\n        enterNextContract: '进入下一份合同',\n        fileList: '文件列表',\n        addSignerFile: '添加附属资料',\n        signatureFinish: '已全部盖章/签名',\n        dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',\n        noticeToManager: '给管理员发通知',\n        gotoAuthPerson: '去认证个人',\n        senderRequire: '发件方要求您',\n        senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',\n        suggestToAuth: '您还未实名认证，建议您实名认证后签署',\n        contactEntAdmin: '请联系企业主管理员',\n        setYourAccount: '将您的账号',\n        authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',\n        noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',\n        pleaseUse: '请使用',\n        me: '我',\n        myself: '本人，',\n        reAuthBtnTip: '我是当前手机号的实际使用者，',\n        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',\n        authInfoNoSame2: '的身份签署合同。这与您当前登录账号的基础身份信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        authInfo2: '检测到您当前账号的基础身份信息为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '是否继续以当前账号签署？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n        confirmOk: '确认',\n        goOnAuth: {\n            0: '进行认证，',\n            1: '请进行实名认证，',\n            2: '进行实名认证，',\n        },\n        signContractAfterAuth: {\n            0: '认证完成后，可签署合同。',\n            1: '完成认证后即可签署合同。',\n        },\n        useIdentity: '以{name}的身份',\n        inTheName: '以',\n        of: '的',\n        identity: '身份',\n        nameIs: '姓名为',\n        IDNumIs: '身份证号为',\n        provideMoreAuthData: '补充更多认证材料',\n        leadToAuthBeforeSign: '继续认证后即可签署合同',\n        groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',\n        contactSender: '如有疑问请联系发件方。',\n        note: '注:',\n        identityInfo: '身份信息',\n        signNeedCoincidenceInfo: '完全一致才能签署合同。',\n        needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',\n        iHadReadContract: '已阅读，本人已知晓{alias}内容',\n        scrollToBottomTip: '需滑动到最后一页',\n        getVerCodeFirst: '请先获取验证码',\n        appScanVerify: '上上签APP扫码校验',\n        downloadBSApp: '下载上上签APP',\n        scanned: '扫码成功',\n        confirmInBSApp: '请在上上签APP中确认签署',\n        qrCodeExpired: '二维码已失效，请刷新重试',\n        appKey: 'APP安全校验',\n        goToScan: '去扫码',\n        setNotificationInUserCenter: '请先到用户中心设置通知方式',\n        doNotWantUseVerCode: '不想用验证码',\n        try: '试试',\n        retry: '重试',\n        goToFaceVerify: '去刷脸',\n        faceExceedTimes: '当日刷脸次数已达上线',\n        returnBack: '返回',\n        switchTo: '切换至',\n        youCanChooseIdentityBlow: '您可以选择以下签约主体',\n        needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',\n        lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',\n        manageSeal: '管理印章',\n        needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',\n        chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',\n        appendDrawSignature: '添加手绘签名',\n        senderUnFill: '（发件人未填写）',\n        declare: '说明',\n        fileLessThan: '请上传小于{num}M的文件',\n        fileNeedUploadImg: '上传时请使用支持的附件格式',\n        serverError: '服务器开了点小差，请稍后再试',\n        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',\n        fileLimitFormatAndSize: '单个资料图片数量不超过10张。',\n        fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',\n        fileFormatFile: '支持pdf、excel、word、txt、zip、xml格式，单份文件大小不超过10M',\n        signNeedKnow: '签约须知',\n        signNeedKnowFrom: '来自{sender}的签约须知',\n        approvalInfo: '审批须知',\n        approveNeedKnowFrom: '来自{sender}-{sendEmployeeName}提交的审批资料（{approvalType}）',\n        approveBeforeSend: '合同发送前审批',\n        approveBeforeSign: '合同签署前审批',\n        approveOperator: '审批人',\n        approvalOpinion: '审批留言',\n        employeeDefault: '员工',\n        setLabel: '设置标签',\n        addRidingSeal: '添加骑缝章',\n        delRidingSeal: '删除骑缝章',\n        file: '附件文件',\n        compressedFile: '压缩文件',\n        attachmentContent: '附件内容',\n        pleaseClickView: '（请点击下载查看）',\n        downloadFile: '下载源文件',\n        noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',\n        archiveTo: '归档到',\n        hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',\n        pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',\n        save: '保存',\n        remind: '提醒',\n        riskTip: '风险提醒',\n        chooseApplyPerson: '选择盖章执行人',\n        chooseAdminSign: '选择印章管理员',\n        useSealByOther: '他人盖章',\n        getSeal: '获取印章',\n        nowApplySealList: '您正在请求以下印章',\n        nowAdminSealList: '你正在申请获得以下印章',\n        chooseApplyPersonToDeal: '请选择盖章执行人，合同将由所选人员来处理（你仍能继续查看、跟进此合同）',\n        chooseTransferPerson: '转交他人签署',\n        chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',\n        contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',\n        sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',\n        successfulSent: '发送成功',\n        authTip: {\n            t2: ['注：', '完全一致才能签署合同。', '企业名称', '身份信息', '完全一致才能查看和签署合同。'],\n            t3: '{x}要求您{text}进行实名认证。',\n            tCommon1: '以{entName}的身份',\n            tCommon2_1: '以姓名为{name}，身份证号为{idCard}',\n            tCommon2_2: '以姓名为{name}',\n            tCommon2_3: '以身份证号为{idCard}',\n            viewAndSign1: '完成认证后即可查看和签署合同。',\n            viewAndSignConflict: '{x}要求您{text}进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',\n        },\n        needSomeoneToSignature: '由{x}盖{y}',\n        needToSet: '需盖',\n        approver: '申请人：',\n        clickToSignature: '点击此处签名',\n        transferToOtherToSign: '转给其他人签',\n        signatureBy: '由{x}签名',\n        tipRightNumber: '请输入正确的数字',\n        tipRightIdCard: '请正确填写18位大陆居民身份证',\n        tipRightPhoneNumber: '请正确填写11位手机号码',\n        tip: '提示',\n        tipRequired: '必填值不可为空',\n        confirm: '确定',\n        viewContractDetail: '查看合同详情',\n        required: '必填',\n        optional: '选填',\n        decimalLimit: '限小数点后{x}位',\n        intLimit: '要求整数',\n        invalidContract: '签署此合同视为您同意将以下合同作废：',\n        No: '编号',\n        chooseFrom2: '发件方设置了二选一盖章，请选择一处盖章',\n        crossPlatformCofirm: {\n            message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',\n            title: '数据授权',\n            confirmButtonText: '同意授权',\n            cancelButtonText: '取消',\n        },\n        sealScope: '印章使用范围',\n        currentContract: '当前合同',\n        allContract: '所有合同',\n        docView: '合同预览',\n        fixTextDisplay: '纠正页面乱码',\n        allPage: '共{num}页',\n        notJoinTip: '请联系管理员添加为企业成员后再签署',\n    },\n    signJa: {\n        beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',\n        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',\n        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',\n        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',\n        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',\n        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',\n        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',\n        entNamePlaceholder: '请输入企业名称',\n        corporateNumberPlaceholder: '请输入法人番号',\n        corporateNumber: '法人番号',\n        singerNamePlaceholder: '请输入签署人姓名',\n        singerName: '签署人姓名',\n        businessPic: '印鉴证明书',\n        waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',\n        itsMe: '是我本人',\n        wrongInformation: '信息有误',\n        confirmChange: '确认更换',\n        communicateSender1: '不更换, 与甲方沟通',\n        communicateSender2: '取消, 去与发件方沟通',\n        createSeal: {\n            title: '输入姓名',\n            tip: '请输入您的姓名（空格可以进行换行）',\n            emptyErr: '请输入姓名',\n        },\n        areaRegister: '企业注册地',\n        jp: '日本',\n        cn: '中国大陆',\n        are: '阿拉伯联合酋长国',\n        other: '其他',\n        plsSelect: '请选择',\n        tip1: '注册地为中国大陆地区的企业，需在 ent.bestsign.cn 中完成实名注册。在与中国大陆地区以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',\n        tip2: '若您的企业已在上上签中国大陆版完成实名认证，可直接登录 ent.bestsign.cn，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与中国大陆版是完全独立隔离的。',\n        tip3: '请提供您从当地商业监管机构获取的证件编号',\n        tip4: '请按照以下步骤操作',\n        tip5: '1、请您与您的专属客户经理联系，引导您完成企业实名。',\n        tip6: '点击“充值管理”。',\n        tip7: '2、请上传贵司与上上签的商务合同截图或与专属客户经理的业务往来邮件。',\n        tip8: '至少购买一份合同，并保存购买记录的截图。',\n        tip9: '3、非日本、中国大陆地区的企业才可以使用此方式。',\n        tip10: '4、提交后上上签在三个工作日内进行审核。',\n        tip11: '重要提示',\n        tip12: '购买人必须为企业用户。',\n        tip13: '付款账户中的企业全称必须与您已填写的“企业名称”完全一致。',\n        tip14: '非日本、中国大陆地区以外的企业才可以使用此方式。',\n        comNum: '企业证件号',\n        buyRecord: '证明材料',\n        selectArea: '请选择企业注册地',\n        uaeTip1: '注册地为阿联酋的企业，需在 uae.bestsign.com中完成实名注册。在与阿联酋以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',\n        uaeTip2: '若您的企业已在上上签阿联酋版完成实名认证，可直接登录 uae.bestsign.com，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与阿联酋版是完全独立隔离的。',\n        uaeTip3: '注册地为阿联酋和中国大陆以外的企业，需在 ent.bestsign.com中完成实名注册。在与阿联酋的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',\n    },\n    signPC: {\n        commonSign: '确认签署',\n        contractVerification: '签约校验',\n        VerCodeVerify: '验证码校验',\n        QrCodeVerify: '二维码校验',\n        verifyTip: '上上签正在调用您的安全数字证书，您正在安全签约环境中，请放心签署！',\n        verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',\n        selectSeal: '选择印章',\n        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n        toAddSealWithConsole: '电子公章待启用，添加其他印章可前往控制台操作。',\n        use: '使用',\n        toAddSeal: '去添加印章',\n        mySeal: '我的印章',\n        operationCompleted: '操作完成',\n        FDASign: {\n            date: '签署时间',\n            signerAdd: '新增',\n            signerEdit: '修改',\n            editTip: '提示：中文姓名请输入拼音，如San Zhang（张三）',\n            inputNameTip: '请输入您的姓名',\n            inputName: '请输入英文或中文拼音',\n            signerNameFillTip: '您还需要填写签字姓名',\n            plsInput: '请输入',\n            plsSelect: '请选择',\n            customInput: '自行输入',\n        },\n        signPlaceBySigner: {\n            signGuide: '签署指导',\n            howDragSeal: '如何拖章',\n            howDragSignature: '如何拖签名',\n            iKnow: '我知道了',\n            step: {\n                one: '第一步：阅读合同',\n                two1: '第二步：点击“拖章”',\n                two2: '第二步：点击“拖签名”',\n                three: '第三步：点击“签署”按钮',\n            },\n            dragSeal: '拖章',\n            continueDragSeal: '继续拖章',\n            dragSignature: '拖签名',\n            continueDragSignature: '继续拖签名',\n            dragPlace: '按住此处拖动',\n            notRemind: '不再提醒',\n            signTip: {\n                one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',\n                two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。',\n            },\n            finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署',\n        },\n        continueOperation: {\n            success: '操作成功',\n            exitApproval: '退出审批',\n            continueApproval: '继续审批',\n            next: '下一份：',\n            none: '没有了',\n            tip: '提示',\n            approvalProcess: '共需{totalNum}人审批，当前已有{passNum}人审批通过',\n            receiver: '接收方：',\n        },\n    },\n    signTip: {\n        contractDetail: '合同详情',\n        downloadBtn: '下载APP',\n        tips: '提示',\n        submit: '确定',\n        SigningCompleted: '签署成功',\n        submitCompleted: '等待他人处理',\n        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',\n        noRightSign: '合同正在签署中，当前用户不允许签署操作',\n        noNeedSign: '内部决议合同，已无需签署',\n        ApprovalCompleted: '审批成功',\n        contractRevoked: '该{alias}已被撤销',\n        contractRefused: '该{alias}已被拒签',\n        linkExpired: '该链接已失效',\n        contractClosed: '该{alias}已截止签约',\n        approvalReject: '该{alias}审批已被驳回',\n        approving: '{alias}正在审批中',\n        viewContract: '查看{alias}详情',\n        viewContractList: '查看合同列表',\n        needMeSign: '（{num}份待签署）',\n        downloadContract: '下载合同',\n        sign: '签署',\n        signed: '签署',\n        approved: '审批',\n        approval: '审批',\n        person: '人',\n        personHas: '已',\n        personHave: '已',\n        personHasnot: '未',\n        personsHavenot: '未',\n        headsTaskDone: '{num}{has}{done}',\n        headsTaskNotDone: '{num}{not}{done}',\n        taskStatusBetween: '，',\n        cannotReview: '无法查看合同',\n        cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',\n        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n        beenDeleted: '您的账号已被企业管理员删除',\n        unActive: '无法继续激活账',\n        back: '返回',\n        contratStatusDes: '{key}状态：',\n        contractConditionDes: '{key}情况：',\n        contractIng: '{alias}{key}中',\n        contractComplete: '{alias}{key}完成',\n        dataProduct: {\n            tip1: '{entName}致各位优质经销商/供应商企业负责人：',\n            tip2: '为答谢您为{entName}的稳定发展作出的贡献，特此联合{bankName}推出供应链金融服务，助力您的企业加速发展！',\n            btnText: '去向老板分享这个喜讯',\n        },\n        signOnGoing: '合同{status}中',\n        operate: '合同操作',\n        freeContract: '完成首次合同发送，可免费再获取合同份数',\n        sendContract: '去发合同',\n        congratulations: '恭喜{name}已完成{num}份合同签署，',\n        carbonSaving: '预估节碳{num}g',\n        signGift: '上上签赠送您{num}份对公合同（使用期限至{limit}）',\n        followPublic: '关注微信公众号，随时接收合同消息',\n        congratulationsSingle: '恭喜{name}完成合同签署，',\n        carbonSavingSingle: '预估新增节碳量2002.4g',\n        viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮',\n        congratulationsCn: '感谢选择电子签！',\n        carbonSavingSingleCn: '您为地球减碳{num}gCO2e',\n        carbonVerification: '*经「碳阻迹」科学核算',\n    },\n    view: {\n        title: '查看合同',\n        ok: '完成',\n        cannotReview: '无法查看合同',\n        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',\n    },\n    prepare: {\n        sealArea: '盖章处',\n        senderNotice: '当前合同发送主体为：{entName}',\n        preSetDialogConfirm: '我知道了',\n        preSetDialogContact: '马上联系上上签销售人员开通',\n        preSetDialogInfo: '合同预置成功后，系统根据模板自动填写相应的签约方信息、签署要求、签署位置、合同描述字段等',\n        preSetDialogTitle: '什么是合同预置模板？',\n        initialValues: '根据合同内容预置初始值',\n        proxyUpload: '上传本地文件后，可选择合同发起方',\n        signHeaderTitle: '添加文件和签约方',\n        step1: '第一步',\n        confirmSender: '确认发起方',\n        step2: '第二步',\n        uploadFile: '上传文件',\n        step3: '第三步',\n        addSigner: '添加签约方',\n        actionDemo: '操作演示',\n        next: '下一步',\n        isUploadingErr: '文件还未上传完成，请在完成后继续操作',\n        noUploadFileErr: '未上传文件，请上传后继续操作',\n        noContractTitleErr: '未填写合同名称，请填写后继续',\n        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n        expiredDateErr: '签署截止时间有误，请修改后继续',\n        noExpiredDateErr: '请填写签署截止时间后继续',\n        describeFieldsErr: '请填写必填内容字段后继续',\n        noRecipientsErr: '至少添加一个签约方',\n        noAccountErr: '账号不能为空',\n        noUserNameErr: '姓名不能为空',\n        noIDNumberErr: '身份证号码不能为空',\n        accountFormatErr: '格式不正确，请输入正确的手机号或邮箱',\n        userNameFormatErr: '格式不正确，请输入正确的姓名',\n        enterpriseNameErr: '请填写正确的企业名称',\n        idNumberForVerifyErr: '格式不正确，请输入正确的身份证',\n        signerErr: '签约方有误',\n        noSignerErr: '请至少添加一个签署人',\n        lackAttachmentNameErr: '请填写附件名称',\n        repeatRecipientsErr: '非顺序签署时不能重复添加签约方',\n        innerContact: '内部联系人',\n        outerContact: '外部联系人',\n        search: '搜索',\n        accountSelected: '已选账号',\n        groupNameAll: '全部',\n        unclassified: '未分类',\n        fileLessThan: '请上传小于{num}M的文件',\n        beExcel: '请上传Excel文件',\n        usePdf: '上传时请使用PDF文件或图片',\n        usePdfFile: '上传时请使用PDF文件',\n        fileNameMoreThan: '文件名称长度超过{num}，已为您自动截取',\n        needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',\n        addSender: '添加为签约方',\n        tip: '提示',\n        cancel: '取消',\n    },\n    addReceiver: {\n        English: '英语',\n        Japanese: '日语',\n        Chinese: '中文',\n        Arabic: '阿拉伯语',\n        setNoticelang: '设置通知语言',\n        limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',\n        individual: '签约个人',\n        enterprise: '签约企业',\n        addInstructions: '添加签约须知',\n        instructionsContent: '提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交',\n        addContractingInfo: '提交签约主体资料',\n        contractingInfoContent: '提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交',\n        payer: '付费方',\n        handWriting: '开启手写笔迹识别',\n        realName: '经办人需要实名',\n        sameTip: '提示：签约方的企业名称完全一致才能签署',\n        proxy: '对方前台代收',\n        aboradTip: '提示：该签约方为境外人士，实名认证有风险，请先核对该人员的身份',\n\n        busRole: '业务角色',\n        busRoleTip: '能帮助您识别签约方，方便管理',\n        busRolePlaceholder: '如员工/经销商',\n        handWritingTip: '该用户需要在签署时手写清晰可辨的姓名才能完成签署',\n        instructions: '添加签约须知 | （限255字）',\n        contractingParty: '签约主体资料',\n        signerPay: '本合同由该签署方付费',\n        afterReadingTitle: '阅读完毕再签署',\n        afterReading: '签署人必须阅读，并且知晓合同内容才可进行后续操作。',\n        handWritingTips: '该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署',\n        SsTitle: '盖章并签字',\n        SsTip: '使用企业印章签署时，需同时添加个人签名完成签署。签名前需完成个人实名认证',\n        signature: '签字',\n        stamp: '盖章',\n        Ss: '盖章并签字',\n        mutexError: '已设置“{msg}”，请先删除“{msg}”的设置后再选择',\n        handWriteNotAllowed: '不允许手写签名',\n        forceHandWrite: '必须手写签名',\n        faceFirst: '优先刷脸，备用验证码签署',\n        faceVerify: '必须刷脸签署',\n        attachmentRequired: '添加合同附属资料',\n        newAttachmentRequired: '提交签约主体资料',\n        attachmentError: '合同附属资料名称不能相同',\n        receiver: '接收手机/邮箱 |（最多支持5个，可用分号隔开）',\n        receiverJa: '接收邮箱 |（最多支持5个，可用分号隔开）',\n        orderSignLabel: '顺序签署',\n        contactAddress: '联系人地址簿',\n        signOrder: '签署顺序',\n        account: '账号',\n        accountPlaceholder: '手机/邮箱（必填）',\n        accountPlaceholderJa: '邮箱（必填）',\n        accountReceptionCollection: '前台代收',\n        accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',\n        accountReceptionCollectionTip2: '请选择前台代收',\n        signSubjectPerson: '签约主体：个人',\n        nameTips: '姓名（选填，用于签约身份核对）',\n        requiredNameTips: '姓名（必填，用于签约身份核对）',\n        entOperatorNameTips: '姓名（选填）',\n        needAuth: '需要实名',\n        operatorNeedAuth: '经办人需要实名',\n        signSubjectEnt: '签约主体：公司',\n        entNameTips: '企业名称（必填，用于签约身份核对）',\n        operator: '经办人',\n        sign: '签署',\n        more: '更多',\n        faceFirstTips: '签署时系统默认采用刷脸校验，当刷脸不通过的次数达到当日上限时自动切换为验证码校验',\n        mustFace: '必须刷脸签署',\n        mustHandWrite: '必须手写签名',\n        fillIDNumber: '身份证号',\n        fillNoticeCall: '通知手机',\n        fillNoticeCallTips: '请填写通知手机',\n        addNotice: '添加私信',\n        attachTips: '添加合同附属资料',\n        faceSign: '必须刷脸签署',\n        faceSignTips: '该用户需要通过刷脸认证才能完成签署（刷脸签署暂只支持大陆居民使用）',\n        handWriteNotAllowedTips: '该用户只能选择已经设置的签名或者使用默认字体签名才能完成签署',\n        handWriteTips: '该用户需要手写签名才能完成签署',\n        idNumberTips: '用于签约身份核对',\n        verifyBefore: '查看文件前验证身份',\n        verify: '验证身份',\n        verifyTips: '最多20字',\n        verifyTips2: '您须将此验证信息提供给该用户',\n        sendToThirdPlatform: '发送给第三方平台',\n        platFormName: '平台名称',\n        fillThirdPlatFormName: '请输入第三方平台名称',\n        attach: '资料',\n        attachName: '资料名称',\n        exampleID: '例：身份证照片',\n        attachInfo: '备注',\n        attachInfoTips: '例：请上传本人的身份证照片',\n        addAttachRequire: '增加资料',\n        addSignEnt: '添加签约企业',\n        addSignPerson: '添加签约个人',\n        selectContact: '选择联系人',\n        save: '保 存',\n        searchVerify: '查询校验',\n        fillImageContentTips: '请填写图片内容',\n        ok: '确定',\n        findContact: '从合同中找到以下签约方',\n        signer: '签约方',\n        signerTips: '小提示：选择签约方后，平台可以帮助定位签字及盖章位置。',\n        add: '添加',\n        notAdd: '不添加',\n        cc: '抄送',\n        notNeedAuth: '不需要实名',\n        operatorNotNeedAuth: '经办人不需要实名',\n        extracting: '提取中',\n        autoFill: '自动填写签署人',\n        failExtracting: '未提取到签约方',\n        idNumberForVerifyErr: '请输入正确的身份证',\n        noAccountErr: '账号不能为空',\n        noUserNameErr: '姓名不能为空',\n        noIDNumberErr: '身份证号码不能为空',\n        noEntNameErr: '企业名称不能为空',\n        accountFormatErr: '请输入正确的手机号或邮箱',\n        enterpriseNameErr: '请输入正确的公司名称',\n        userNameFormatErr: '请输入正确的姓名',\n        riskCues: '风险提示',\n        riskCuesMsg: '如果签约方未实名签署，在文件发生纠纷时，需要您自己提供该签约方身份认定的证据。如需避免风险，请选择需要实名。',\n        confirmBtnText: '选择需要实名',\n        cancelBtnText: '选择不需要实名',\n        attachLengthErr: '您最多只能为单个签署人添加50个附件要求',\n        collapse: '收起',\n        expand: '展开',\n        delete: '删除',\n        saySomething: '说点什么吧',\n        addImage: '添加文档',\n        addImageTips: '（支持word、pdf以及图片，不超过3份文档）',\n        give: '给',\n        fileMax: '上传数量超过数量上限!',\n        signerLimit: '您当前的版本不支持超过{limit}个相对签署/抄送方。',\n        showExamle: '查看示例图片',\n        downloadExamle: '下载示例文件',\n    },\n    addReceiverGuide: {\n        guideTitle: '如何添加新的签署人',\n        receiverType: '您需要选择签署人参与合同的方式（六选一）：',\n        asEntSign: '代表企业签署：',\n        signatureSub: '法人或高管在合同上代表企业签字。签字完成后的合同是可以被企业收走的',\n        vipOnly: '高级版本可用',\n        sealSub: '签署人需在合同上加盖公章或合同专用章等',\n        stampSub: '签署人既要盖章，也要代表企业签字',\n        confirmSeal: '代表企业使用业务核对章',\n        confirmSealSub: '财务对账单、询证函等文件先核实再盖章',\n        asPersonSign: '代表个人签署：',\n        asPersonSignTip: '仅代表个人签字，不代表任何企业',\n        asPersonSignDesc: '签署人的私人合同，如借贷合同、入职离职协议等',\n        scanSign: '扫码签字',\n        scanSignDesc: '发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景',\n        selectSignTypeTip: '请先选择签署方参与合同的方式',\n        notRemind: '下次不再提醒',\n        sign: '签字',\n        entSign: '企业签字',\n        stamp: '盖章',\n        stampSign: '盖章并签字',\n        requestSeal: '业务核对章',\n    },\n    linkContract: {\n        title: '关联合同',\n        connectMore: '关联更多合同',\n        placeholder: '请输入合同编号',\n        revoke: '合同已撤销',\n        overdue: '逾期未签',\n        approvalNotPassed: '审批被驳回',\n        reject: '合同已拒签',\n        signing: '签署中',\n        complete: '已完成',\n        approvaling: '审批中',\n        disconnect: '解除关联',\n        disconnectSuccess: '解除关联成功',\n        connectLimit: '关联合同数量上限为100份',\n    },\n    field: {\n        fieldTip: {\n            title: '缺少签署位置',\n            error: '没有在以下合同指定签署位置（{type}）',\n            add: '添加字段',\n            continue: '继续发送',\n        },\n        accountCharge: {\n            notice: '该合同按参与账号数计费',\n            able: '可以正常发送',\n            unable: '可用账号数不足，请联系上上签客服',\n            notify: '该合同给所有签约方发英文通知',\n            noNotify: {\n                1: '该合同不发签约相关通知',\n                2: '（包括签署、审批、抄送、截止签署等通知短信和邮件）',\n            },\n        },\n        ridingStamp: '骑缝章',\n        watermark: '水印',\n        senderSignature: '盖章人签字',\n        optional: '选填',\n        clickDecoration: '点击合同装饰',\n        decoration: '合同装饰',\n        sysError: '系统繁忙，请稍后再试',\n        partedMarkedError: '指定了“盖章并签字”的签约方，必须同时指定盖章和签字',\n        fieldTitle: '共有{length}份合同需要指定签署位置',\n        send: '发送',\n        contractDispatchApply: '申请发送合同',\n        contractNeedYouSign: '该文件需要您签署',\n        ifSignRightNow: '是否马上签署',\n        signRightNow: '马上签署',\n        signLater: '稍后签署',\n        signaturePositionErr: '请为每个签署方指定签署位置',\n        sendSucceed: '发送成功',\n        confirm: '确定',\n        cancel: '取消',\n        qrCodeTips: '签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改',\n        pagesField: '第{currentPage}页，共{totalPages}页',\n        suitableWidth: '适合宽度',\n        signCheck: '签名查验',\n        locateSignaturePosition: '定位签署位置',\n        locateTips: '可以帮助快速定位到签署位置。当前仅支持定位每个签署方第一个签署位置',\n        step1: '第一步',\n        selectSigner: '选择签约方',\n        step2: '第二步',\n        dragSignaturePosition: '拖动签署位置',\n        signingField: '签署字段',\n        docTitle: '文档',\n        totalPages: '页数：{totalPages}页',\n        receiver: '接收方',\n        delete: '删除',\n        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同',\n        unlimitedNotice: '该合同计费不限量使用',\n        charge: '计费',\n        units: '{num}份',\n        contractToPrivate: '对私合同',\n        contractToPublic: '对公合同',\n        costTips: {\n            1: '对公合同：签署人（不包含发件人）中有企业账户的合同',\n            2: '对私合同：签署人（不包含发件人）中没有企业账户的合同',\n            3: '计费份数根据文件份数计算',\n            4: '计费份数 = 文件份数 × 批量导入用户组（行）数',\n        },\n        costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',\n        toCharge: '去充值',\n        contractNeedCharge: {\n            1: '可用合同份数不足，无法发送',\n            2: '可用合同份数不足，请联系主管理员充值',\n        },\n        chooseApprover: '选择审批人：',\n        nextStep: '下一步',\n        submitApproval: '提交审批',\n        autoSendAfterApproval: '*审批通过后，自动发送合同',\n        chooseApprovalFlow: '请选择一个审批流',\n        completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',\n        viewPrivateLetter: '查看私信',\n        addPrivateLetter: '添加私信',\n        append: '添加',\n        privateLetter: '私信',\n        signNeedKnow: '签约须知',\n        maximum5M: '请上传小于5M的文档',\n        uploadServerFailure: '上传到服务器失败',\n        uploadFailure: '上传失败',\n        pager: '页码',\n        seal: '盖章',\n        signature: '签名',\n        signDate: '签署日期',\n        text: '文本',\n        date: '日期',\n        qrCode: '二维码',\n        number: '数字',\n        dynamicTable: '动态表格',\n        terms: '合同条款',\n        checkBox: '复选框',\n        radioBox: '单选框',\n        image: '图片',\n    },\n    addressBook: {\n        innerMember: {\n            title: '企业内部成员',\n            tips: '调整企业成员信息，让发件人都能更快找到内部联系人',\n            operation: '去控制台',\n        },\n        outerContacts: {\n            title: '外部企业联系人',\n            tips: '邀请您的合作伙伴提前注册实名，以便与您顺利开展业务',\n            operation: '邀请您的合作伙伴',\n        },\n        myContacts: {\n            title: '我的联系人',\n            tips: '修改联系人，确保签署人信息准确无误',\n            operation: '去用户中心',\n        },\n        selected: '已选账号',\n        search: '搜索',\n        loadMore: '加载更多',\n        end: '全部加载完成',\n    },\n    dataBoxInvite: {\n        title: '邀请您的合作伙伴',\n        step1: '分享链接给您的合作伙伴提前创建企业',\n        step2: '通过链接/二维码授权后的合作伙伴会出现在地址簿',\n        step3: '在“档案+”对您的合作伙伴做更多管理',\n        imgName: '分享采集二维码',\n        saveQrcode: '保存二维码到本地',\n        copy: '复制',\n        copySuccess: '复制成功',\n        copyFailed: '复制失败',\n    },\n    shareView: {\n        title: '转发审阅',\n        account: '手机号/邮箱',\n        role: '审阅人角色',\n        note: '备注',\n        link: '链接：',\n        signerMessage: '签署人留言',\n        rolePlaceholder: '如公司法务、部门领导等',\n        notePlaceholder: '给审阅人留言，200字以内',\n        generateLink: '生成链接',\n        saveQrcode: '保存微信小程序码',\n        regenerateLink: '重新生成链接',\n        inputAccount: '请输入手机号码或者邮箱',\n        inputCorrectAccount: '请输入正确的手机号码或者邮箱',\n        accountInputTip: '为确保链接正常打开，请准确输入合同审阅人信息',\n        shareLinkTip1: '保存微信小程序码或',\n        shareLinkTip: '复制链接，分享给审阅人',\n        linkTip1: '合同正文属于保密内容，非必要情况下请勿外泄',\n        linkTip2: '链接有效期为2天；重新生成链接后，历史链接自动失效',\n    },\n    recoverSpecialSeal: {\n        title: '印章无法使用',\n        description1: '发件方要求您需使用该印章才能签署合同，但贵公司已删除该印章。为确保签署顺利进行，请向管理员恢复该印章。',\n        description2: '如果该印章确实不合适被继续使用，可联系发件方修改对印章图案的要求后，再签署合同。',\n        postRecover: '申请恢复印章',\n        note: '点击后管理员将收到恢复印章申请的短信/邮件，同时在印章管理页面时也能看到申请。',\n        requestSend: '恢复申请提交成功',\n    },\n    paperSign: {\n        title: '使用纸质方式签署',\n        stepText: ['下一步', '确认纸质签', '确定'],\n        needUploadFile: '请先上传扫描件',\n        uploadError: '上传失败',\n        cancel: '取消',\n        downloadPaperFile: '获取纸质签文件',\n        step0: {\n            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',\n            address: '邮寄地址：',\n            contactName: '接收人姓名：',\n            contactPhone: '接收人联系方式：',\n            defaultValue: '请通过线下方式向发件方索取',\n        },\n        step1: {\n            title0: '第一步：下载&打印纸质合同',\n            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],\n            title1: '第二步：加盖印章',\n            title1Desc: '在纸质合同上加盖合同有效的公司印章。',\n            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],\n            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],\n        },\n        step2: {\n            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],\n            uploadFile: '上传扫描件',\n            getCodeVerify: '获取合同签署校验',\n            isUploading: '上传中...',\n        },\n    },\n    allowPaperSignDialog: {\n        title: '允许纸质签',\n        content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',\n        tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',\n        icon: '转纸质签署 >>',\n        goSign: '去电子签',\n        cancel: '取消',\n    },\n    sealInconformityDialog: {\n        errorSeal: {\n            title: '印章提示',\n            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',\n            tip1: '检测到有企业印章与企业名称：',\n            tip2: '是否要继续使用当前印章图片？',\n            tip3: '根据发件方要求，您需要使用企业名称为：',\n            tip4: '的印章',\n            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',\n            tip6: '不匹配，请确保印章符合发件方要求。',\n            guide: '如何上传正确的印章 >>',\n            next: '继续使用',\n            tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',\n            tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？',\n        },\n        exampleSeal: {\n            title: '上传印章图案方式',\n            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],\n            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],\n            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],\n        },\n        confirm: '确认',\n        cancel: '取消',\n    },\n    addSealDialog: {\n        title: '添加印章图片',\n        dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNG等），由系统将此印章图片合并进入当前合同中。',\n        dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',\n        updateNewSeal: '上传新章',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,cAAc,EAAE;IACZC,iBAAiB,EAAE,0BAA0B;IAC7CC,aAAa,EAAE;MACXC,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE;EACT,CAAC;EACDC,IAAI,EAAE;IACFC,aAAa,EAAE,wGAAwG;IACvHJ,QAAQ,EAAE,IAAI;IACdK,uBAAuB,EAAE,6BAA6B;IACtDC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE;MACdC,WAAW,EAAE,OAAO;MACpBC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,4BAA4B;MAClCC,IAAI,EAAE,oEAAoE;MAC1EC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,gBAAgB;MACtBC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,QAAQ;MACtBC,WAAW,EAAE;IACjB,CAAC;IACDC,UAAU,EAAE,UAAU;IACtBC,aAAa,EAAE,UAAU;IACzBC,gBAAgB,EAAE,kBAAkB;IACpCC,YAAY,EAAE,0BAA0B;IACxCC,wBAAwB,EAAE,aAAa;IACvCC,mBAAmB,EAAE,cAAc;IACnCC,qBAAqB,EAAE,QAAQ;IAC/BC,UAAU,EAAE,OAAO;IACnBC,gBAAgB,EAAE,cAAc;IAChCC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZhC,MAAM,EAAE,IAAI;IACZE,IAAI,EAAE,MAAM;IACZ+B,OAAO,EAAE,kBAAkB;IAC3BC,eAAe,EAAE,mCAAmC;IACpDC,qBAAqB,EAAE,oCAAoC;IAC3DC,iBAAiB,EAAE,UAAU;IAC7BC,cAAc,EAAE,QAAQ;IACxBC,uBAAuB,EAAE,cAAc;IACvCC,8BAA8B,EAAE,WAAW;IAC3CC,kBAAkB,EAAE,eAAe;IACnCC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE,oBAAoB;IACnCC,UAAU,EAAE,OAAO;IACnBC,cAAc,EAAE,4BAA4B;IAC5CC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,OAAO;IACtBC,qBAAqB,EAAE,eAAe;IACtCC,OAAO,EAAE,kBAAkB;IAC3BC,SAAS,EAAE,MAAM;IACjBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,sCAAsC;IACnDC,cAAc,EAAE,gBAAgB;IAChCC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,uDAAuD;IACxEC,eAAe,EAAE,gEAAgE;IACjFC,iBAAiB,EAAE,gBAAgB;IACnCC,aAAa,EAAE,yCAAyC;IACxDC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,cAAc;IAC1BC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,SAAS;IACzBC,YAAY,EAAE,qBAAqB;IACnCC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,4CAA4C;IAC1DC,gBAAgB,EAAE,4BAA4B;IAC9CC,WAAW,EAAE,MAAM;IACnBC,gBAAgB,EAAE,gLAAgL,GAC9L,kFAAkF;IACtFC,YAAY,EAAE,2CAA2C;IACzDC,SAAS,EAAE,IAAI;IACfxE,UAAU,EAAE,MAAM;IAClByE,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,eAAe;IAC3BC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,gBAAgB;IAC3BC,SAAS,EAAE,oCAAoC;IAC/CC,OAAO,EAAE,cAAc;IACvBC,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE,cAAc;IAC7BC,eAAe,EAAE,mBAAmB;IACpCC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,MAAM;IAClBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,aAAa;IAC7BC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,SAAS;IACxBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,QAAQ;IACrBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,MAAM;IACtBC,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE,KAAK;IACZC,YAAY,EAAE,aAAa;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,SAAS;IACxBC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,IAAI;IACfC,oBAAoB,EAAE,aAAa;IACnCC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,0BAA0B;IACxCC,cAAc,EAAE,SAAS;IACzBC,iBAAiB,EAAE,0BAA0B;IAC7CC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,qCAAqC;IACvDC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,MAAM;IAC3BC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,MAAM;IACjBC,EAAE,EAAE,KAAK;IACTC,cAAc,EAAE,MAAM;IACtBC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE,YAAY;IACpBC,YAAY,EAAE,OAAO;IACrBC,UAAU,EAAE,OAAO;IACnBC,EAAE,EAAE,GAAG;IACPC,YAAY,EAAE,OAAO;IACrBC,gBAAgB,EAAE,OAAO;IACzBC,WAAW,EAAE,UAAU;IACvBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,SAAS;IACrBC,kBAAkB,EAAE,QAAQ;IAC5BC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE;MACZ,CAAC,EAAE,sBAAsB;MACzB,CAAC,EAAE,wBAAwB;MAC3BC,OAAO,EAAE;IACb,CAAC;IACDC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,QAAQ;IAChBC,eAAe,EAAE,MAAM;IACvBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,WAAW;IACrBC,mBAAmB,EAAE,sBAAsB;IAC3CC,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,QAAQ;IACtBC,SAAS,EAAE;MACPC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE,eAAe;MACtBC,SAAS,EAAE,uBAAuB;MAClCC,SAAS,EAAE,kBAAkB;MAC7BC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,sBAAsB;MACjCC,WAAW,EAAE,KAAK;MAClBC,iBAAiB,EAAE,OAAO;MAC1BC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,MAAM;MACjBC,YAAY,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,IAAI;IACZC,gBAAgB,EAAE;MACd;MACAC,iBAAiB,EAAE,uBAAuB;MAC1CC,UAAU,EAAE,qBAAqB;MACjCC,aAAa,EAAE,gBAAgB;MAC/BC,WAAW,EAAE;IACjB,CAAC;IACDC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,OAAO;IACrBC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,gBAAgB,EAAE,iBAAiB;IACnCC,gBAAgB,EAAE,cAAc;IAChCC,gBAAgB,EAAE,gBAAgB;IAClCC,aAAa,EAAE,OAAO;IACtBC,WAAW,EAAE,QAAQ;IACrBC,uBAAuB,EAAE,qBAAqB;IAC9CC,wBAAwB,EAAE,cAAc;IACxCC,yBAAyB,EAAE,gBAAgB;IAC3CC,qBAAqB,EAAE,qBAAqB;IAC5CC,sBAAsB,EAAE,qBAAqB;IAC7CC,qBAAqB,EAAE,qBAAqB;IAC5CC,eAAe,EAAE,YAAY;IAC7BC,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,8BAA8B;IACzCC,iBAAiB,EAAE,YAAY;IAC/BC,kBAAkB,EAAE,qBAAqB;IACzCC,aAAa,EAAE,6BAA6B;IAC5CC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,KAAK;IACbC,0BAA0B,EAAE,2BAA2B;IACvDC,kBAAkB,EAAE,QAAQ;IAC5BC,cAAc,EAAE,MAAM;IACtBC,gBAAgB,EAAE,SAAS;IAC3BC,IAAI,EAAE,KAAK;IACXC,sBAAsB,EAAE,qBAAqB;IAC7CC,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,OAAO;IACzBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,OAAO;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,aAAa,EAAE,qBAAqB;IACpCC,kBAAkB,EAAE,qBAAqB;IACzCC,kBAAkB,EAAE,qBAAqB;IACzCC,eAAe,EAAE,WAAW;IAC5BC,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,iBAAiB;IACrCC,uBAAuB,EAAE,yBAAyB;IAClDC,eAAe,EAAE,mBAAmB;IACpCC,4BAA4B,EAAE,yBAAyB;IACvDC,cAAc,EAAE,OAAO;IACvBC,yBAAyB,EAAE,SAAS;IACpCC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE,yBAAyB;IAC5CC,cAAc,EAAE,sBAAsB;IACtCC,iBAAiB,EAAE,SAAS;IAC5BC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE,UAAU;IAC3BC,gBAAgB,EAAE,uBAAuB;IACzCC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,OAAO;IACvBC,aAAa,EAAE,QAAQ;IACvBC,8BAA8B,EAAE,gBAAgB;IAChDC,aAAa,EAAE,oBAAoB;IACnCC,eAAe,EAAE,WAAW;IAC5BC,cAAc,EAAE,OAAO;IACvBC,yBAAyB,EAAE,+CAA+C;IAC1EC,mBAAmB,EAAE,iCAAiC;IACtDC,SAAS,EAAE,KAAK;IAChBC,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,gBAAgB;IAC9BC,gBAAgB,EAAE,wBAAwB;IAC1CC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,cAAc,EAAE,+BAA+B;IAC/CC,eAAe,EAAE,6BAA6B;IAC9CC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,cAAc;IACnBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,0BAA0B;IAC3CC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;MACN,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,UAAU;MACb,CAAC,EAAE;IACP,CAAC;IACDC,qBAAqB,EAAE;MACnB,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,GAAG;IACdC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,OAAO;IAChBC,mBAAmB,EAAE,UAAU;IAC/BC,oBAAoB,EAAE,aAAa;IACnCC,sBAAsB,EAAE,iCAAiC;IACzDC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,MAAM;IACpBC,uBAAuB,EAAE,aAAa;IACtCC,8BAA8B,EAAE,oBAAoB;IACpDC,gBAAgB,EAAE,oBAAoB;IACtCC,iBAAiB,EAAE,UAAU;IAC7BC,eAAe,EAAE,SAAS;IAC1BC,aAAa,EAAE,YAAY;IAC3BC,aAAa,EAAE,UAAU;IACzBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,eAAe;IAC/BC,aAAa,EAAE,cAAc;IAC7BC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,KAAK;IACfC,2BAA2B,EAAE,eAAe;IAC5CC,mBAAmB,EAAE,QAAQ;IAC7BC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,cAAc,EAAE,KAAK;IACrBC,eAAe,EAAE,YAAY;IAC7BC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,KAAK;IACfC,wBAAwB,EAAE,aAAa;IACvCC,sBAAsB,EAAE,iBAAiB;IACzCC,mBAAmB,EAAE,oBAAoB;IACzCC,UAAU,EAAE,MAAM;IAClBC,wBAAwB,EAAE,sBAAsB;IAChDC,mBAAmB,EAAE,sBAAsB;IAC3CC,mBAAmB,EAAE,QAAQ;IAC7BC,YAAY,EAAE,UAAU;IACxBC,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,gBAAgB;IAC9BC,iBAAiB,EAAE,eAAe;IAClCC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE,+CAA+C;IAC7DC,sBAAsB,EAAE,iBAAiB;IACzCC,eAAe,EAAE,uCAAuC;IACxDC,cAAc,EAAE,6CAA6C;IAC7DC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,iBAAiB;IACnCC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,sDAAsD;IAC3EC,iBAAiB,EAAE,SAAS;IAC5BC,iBAAiB,EAAE,SAAS;IAC5BC,eAAe,EAAE,KAAK;IACtBC,eAAe,EAAE,MAAM;IACvBC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE,OAAO;IACtBC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,MAAM;IACtBC,iBAAiB,EAAE,MAAM;IACzBC,eAAe,EAAE,WAAW;IAC5BC,YAAY,EAAE,OAAO;IACrBC,mBAAmB,EAAE,kBAAkB;IACvCC,SAAS,EAAE,KAAK;IAChBC,mBAAmB,EAAE,iCAAiC;IACtDC,uBAAuB,EAAE,0BAA0B;IACnDC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE,SAAS;IAC5BC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,MAAM;IACtBC,OAAO,EAAE,MAAM;IACfC,gBAAgB,EAAE,WAAW;IAC7BC,gBAAgB,EAAE,aAAa;IAC/BC,uBAAuB,EAAE,qCAAqC;IAC9DC,oBAAoB,EAAE,QAAQ;IAC9BC,0BAA0B,EAAE,uDAAuD;IACnFC,iCAAiC,EAAE,cAAc;IACjDC,uBAAuB,EAAE,iCAAiC;IAC1DC,cAAc,EAAE,MAAM;IACtBC,OAAO,EAAE;MACLC,EAAE,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC;MAC3DC,EAAE,EAAE,qBAAqB;MACzBC,QAAQ,EAAE,eAAe;MACzBC,UAAU,EAAE,0BAA0B;MACtCC,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE,gBAAgB;MAC5BC,YAAY,EAAE,iBAAiB;MAC/BC,mBAAmB,EAAE;IACzB,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,MAAM;IAChBC,gBAAgB,EAAE,QAAQ;IAC1BC,qBAAqB,EAAE,QAAQ;IAC/BC,WAAW,EAAE,QAAQ;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,iBAAiB;IACjCC,mBAAmB,EAAE,cAAc;IACnC/Y,GAAG,EAAE,IAAI;IACTgZ,WAAW,EAAE,SAAS;IACtB5O,OAAO,EAAE,IAAI;IACb6O,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,WAAW;IACzBC,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE,oBAAoB;IACrCC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,qBAAqB;IAClCC,mBAAmB,EAAE;MACjBC,OAAO,EAAE,oCAAoC;MAC7C9R,KAAK,EAAE,MAAM;MACb+R,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE;IACtB,CAAC;IACDC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,MAAM;IACnBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE;EAChB,CAAC;EACDC,MAAM,EAAE;IACJC,cAAc,EAAE,uBAAuB;IACvCC,cAAc,EAAE,sCAAsC;IACtDC,cAAc,EAAE,6BAA6B;IAC7CC,cAAc,EAAE,wEAAwE;IACxFC,cAAc,EAAE,uDAAuD;IACvEC,cAAc,EAAE,mCAAmC;IACnDC,cAAc,EAAE,oBAAoB;IACpCC,kBAAkB,EAAE,SAAS;IAC7BC,0BAA0B,EAAE,SAAS;IACrCC,eAAe,EAAE,MAAM;IACvBC,qBAAqB,EAAE,UAAU;IACjCC,UAAU,EAAE,OAAO;IACnBC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,gDAAgD;IAC7DC,KAAK,EAAE,MAAM;IACbC,gBAAgB,EAAE,MAAM;IACxBC,aAAa,EAAE,MAAM;IACrBC,kBAAkB,EAAE,YAAY;IAChCC,kBAAkB,EAAE,aAAa;IACjCC,UAAU,EAAE;MACR5T,KAAK,EAAE,MAAM;MACb5H,GAAG,EAAE,mBAAmB;MACxByb,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE,OAAO;IACrBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,KAAK;IAChBrb,IAAI,EAAE,oGAAoG;IAC1GC,IAAI,EAAE,2FAA2F;IACjGC,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,4BAA4B;IAClCC,IAAI,EAAE,WAAW;IACjBib,IAAI,EAAE,oCAAoC;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,0BAA0B;IAChCC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,KAAK,EAAE,0BAA0B;IACjCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,UAAU;IACtBC,OAAO,EAAE,8FAA8F;IACvGC,OAAO,EAAE,0FAA0F;IACnGC,OAAO,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACJC,UAAU,EAAE,MAAM;IAClBC,oBAAoB,EAAE,MAAM;IAC5BC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,OAAO;IACrBC,SAAS,EAAE,mCAAmC;IAC9CC,YAAY,EAAE,0CAA0C;IACxDC,UAAU,EAAE,MAAM;IAClBC,aAAa,EAAE,2BAA2B;IAC1CC,oBAAoB,EAAE,yBAAyB;IAC/CC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,MAAM;IACdC,kBAAkB,EAAE,MAAM;IAC1BC,OAAO,EAAE;MACL9X,IAAI,EAAE,MAAM;MACZ+X,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,6BAA6B;MACtCC,YAAY,EAAE,SAAS;MACvBC,SAAS,EAAE,YAAY;MACvBC,iBAAiB,EAAE,YAAY;MAC/BC,QAAQ,EAAE,KAAK;MACfpC,SAAS,EAAE,KAAK;MAChBqC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACfC,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE,MAAM;MACnBC,gBAAgB,EAAE,OAAO;MACzBC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;QACF/e,GAAG,EAAE,UAAU;QACfgf,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,aAAa;QACnB/e,KAAK,EAAE;MACX,CAAC;MACDgf,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,MAAM;MACxBC,aAAa,EAAE,KAAK;MACpBC,qBAAqB,EAAE,OAAO;MAC9BC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE;QACLxf,GAAG,EAAE,2BAA2B;QAChCC,GAAG,EAAE;MACT,CAAC;MACD+N,yBAAyB,EAAE;IAC/B,CAAC;IACDyR,iBAAiB,EAAE;MACfC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,gBAAgB,EAAE,MAAM;MACxBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,KAAK;MACXzf,GAAG,EAAE,IAAI;MACT0f,eAAe,EAAE,oCAAoC;MACrDC,QAAQ,EAAE;IACd;EACJ,CAAC;EACDR,OAAO,EAAE;IACLS,cAAc,EAAE,MAAM;IACtBC,WAAW,EAAE,OAAO;IACpBC,IAAI,EAAE,IAAI;IACV/d,MAAM,EAAE,IAAI;IACZge,gBAAgB,EAAE,MAAM;IACxBC,eAAe,EAAE,QAAQ;IACzBC,UAAU,EAAE,uBAAuB;IACnCC,WAAW,EAAE,qBAAqB;IAClCC,UAAU,EAAE,cAAc;IAC1BC,iBAAiB,EAAE,MAAM;IACzBC,eAAe,EAAE,cAAc;IAC/BC,eAAe,EAAE,cAAc;IAC/BC,WAAW,EAAE,QAAQ;IACrBC,cAAc,EAAE,eAAe;IAC/BC,cAAc,EAAE,gBAAgB;IAChCC,SAAS,EAAE,cAAc;IACzBC,YAAY,EAAE,aAAa;IAC3BC,gBAAgB,EAAE,QAAQ;IAC1BC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,MAAM;IACxB7gB,IAAI,EAAE,IAAI;IACV8gB,MAAM,EAAE,IAAI;IACZxd,QAAQ,EAAE,IAAI;IACdyd,QAAQ,EAAE,IAAI;IACd9d,MAAM,EAAE,GAAG;IACX+d,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,GAAG;IACfC,YAAY,EAAE,GAAG;IACjBC,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,kBAAkB;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,GAAG;IACtB9d,YAAY,EAAE,QAAQ;IACtB+d,cAAc,EAAE,oCAAoC;IACpDC,cAAc,EAAE,qCAAqC;IACrDC,WAAW,EAAE,eAAe;IAC5BC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,IAAI;IACVC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,UAAU;IAChCC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE,gBAAgB;IAClCC,WAAW,EAAE;MACTvhB,IAAI,EAAE,6BAA6B;MACnCC,IAAI,EAAE,8DAA8D;MACpEuhB,OAAO,EAAE;IACb,CAAC;IACDC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,qBAAqB;IACnCC,YAAY,EAAE,MAAM;IACpBC,eAAe,EAAE,wBAAwB;IACzCC,YAAY,EAAE,YAAY;IAC1BC,QAAQ,EAAE,gCAAgC;IAC1CC,YAAY,EAAE,kBAAkB;IAChCC,qBAAqB,EAAE,iBAAiB;IACxCC,kBAAkB,EAAE,gBAAgB;IACpCC,eAAe,EAAE,yCAAyC;IAC1DC,iBAAiB,EAAE,UAAU;IAC7BC,oBAAoB,EAAE,kBAAkB;IACxCC,kBAAkB,EAAE;EACxB,CAAC;EACDhe,IAAI,EAAE;IACF4C,KAAK,EAAE,MAAM;IACbqb,EAAE,EAAE,IAAI;IACRxf,YAAY,EAAE,QAAQ;IACtBge,cAAc,EAAE;EACpB,CAAC;EACDyB,OAAO,EAAE;IACLC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,qBAAqB;IACnCC,mBAAmB,EAAE,MAAM;IAC3BC,mBAAmB,EAAE,eAAe;IACpCC,gBAAgB,EAAE,8CAA8C;IAChEC,iBAAiB,EAAE,YAAY;IAC/BC,aAAa,EAAE,aAAa;IAC5BC,WAAW,EAAE,kBAAkB;IAC/BC,eAAe,EAAE,UAAU;IAC3BC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,OAAO;IACtBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE,MAAM;IAClB1E,IAAI,EAAE,KAAK;IACX2E,cAAc,EAAE,oBAAoB;IACpCC,eAAe,EAAE,gBAAgB;IACjCC,kBAAkB,EAAE,gBAAgB;IACpCC,eAAe,EAAE,qBAAqB;IACtCC,cAAc,EAAE,iBAAiB;IACjCC,gBAAgB,EAAE,cAAc;IAChCC,iBAAiB,EAAE,cAAc;IACjCC,eAAe,EAAE,WAAW;IAC5BC,YAAY,EAAE,QAAQ;IACtBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,WAAW;IAC1BC,gBAAgB,EAAE,oBAAoB;IACtCC,iBAAiB,EAAE,gBAAgB;IACnCC,iBAAiB,EAAE,YAAY;IAC/BC,oBAAoB,EAAE,iBAAiB;IACvCC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE,YAAY;IACzBC,qBAAqB,EAAE,SAAS;IAChCC,mBAAmB,EAAE,iBAAiB;IACtCC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,OAAO;IACrBC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,KAAK;IACnBxQ,YAAY,EAAE,gBAAgB;IAC9ByQ,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,gBAAgB;IACxBC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,uBAAuB;IACzCC,aAAa,EAAE,+CAA+C;IAC9DC,SAAS,EAAE,QAAQ;IACnBjmB,GAAG,EAAE,IAAI;IACTD,MAAM,EAAE;EACZ,CAAC;EACDmmB,WAAW,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,MAAM;IACdC,aAAa,EAAE,QAAQ;IACvBC,kBAAkB,EAAE,0BAA0B;IAC9CC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,MAAM;IAClBC,eAAe,EAAE,QAAQ;IACzBC,mBAAmB,EAAE,+CAA+C;IACpEC,kBAAkB,EAAE,UAAU;IAC9BC,sBAAsB,EAAE,+DAA+D;IACvFC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,UAAU;IACvBC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,iCAAiC;IAE5CC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,gBAAgB;IAC5BC,kBAAkB,EAAE,SAAS;IAC7BC,cAAc,EAAE,0BAA0B;IAC1CC,YAAY,EAAE,kBAAkB;IAChCC,gBAAgB,EAAE,QAAQ;IAC1BC,SAAS,EAAE,YAAY;IACvBC,iBAAiB,EAAE,SAAS;IAC5BC,YAAY,EAAE,2BAA2B;IACzCC,eAAe,EAAE,0CAA0C;IAC3DC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,sCAAsC;IAC7C/hB,SAAS,EAAE,IAAI;IACfgiB,KAAK,EAAE,IAAI;IACXC,EAAE,EAAE,OAAO;IACXC,UAAU,EAAE,+BAA+B;IAC3CC,mBAAmB,EAAE,SAAS;IAC9BC,cAAc,EAAE,QAAQ;IACxBC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,QAAQ;IACpBC,kBAAkB,EAAE,UAAU;IAC9BC,qBAAqB,EAAE,UAAU;IACjCC,eAAe,EAAE,cAAc;IAC/B/I,QAAQ,EAAE,0BAA0B;IACpCgJ,UAAU,EAAE,uBAAuB;IACnCC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,QAAQ;IACxBC,SAAS,EAAE,MAAM;IACjBzlB,OAAO,EAAE,IAAI;IACb0lB,kBAAkB,EAAE,WAAW;IAC/BC,oBAAoB,EAAE,QAAQ;IAC9BC,0BAA0B,EAAE,MAAM;IAClCC,8BAA8B,EAAE,mBAAmB;IACnDC,8BAA8B,EAAE,SAAS;IACzCC,iBAAiB,EAAE,SAAS;IAC5BC,QAAQ,EAAE,iBAAiB;IAC3BC,gBAAgB,EAAE,iBAAiB;IACnCC,mBAAmB,EAAE,QAAQ;IAC7BC,QAAQ,EAAE,MAAM;IAChBC,gBAAgB,EAAE,SAAS;IAC3BC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,mBAAmB;IAChCC,QAAQ,EAAE,KAAK;IACf3pB,IAAI,EAAE,IAAI;IACV4pB,IAAI,EAAE,IAAI;IACVC,aAAa,EAAE,0CAA0C;IACzDC,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,MAAM;IACpBC,cAAc,EAAE,MAAM;IACtBC,kBAAkB,EAAE,SAAS;IAC7BC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,mCAAmC;IACjDC,uBAAuB,EAAE,gCAAgC;IACzDC,aAAa,EAAE,iBAAiB;IAChCC,YAAY,EAAE,UAAU;IACxBC,YAAY,EAAE,WAAW;IACzBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,OAAO;IACnBC,WAAW,EAAE,gBAAgB;IAC7BC,mBAAmB,EAAE,UAAU;IAC/BC,YAAY,EAAE,MAAM;IACpBC,qBAAqB,EAAE,YAAY;IACnCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,eAAe;IAC/BC,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE,QAAQ;IACpBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,OAAO;IACtB3U,IAAI,EAAE,KAAK;IACX4U,YAAY,EAAE,MAAM;IACpBC,oBAAoB,EAAE,SAAS;IAC/B3I,EAAE,EAAE,IAAI;IACR4I,WAAW,EAAE,aAAa;IAC1BtgB,MAAM,EAAE,KAAK;IACbugB,UAAU,EAAE,6BAA6B;IACzCC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,KAAK;IACbC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,OAAO;IACpBC,mBAAmB,EAAE,UAAU;IAC/BC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,SAAS;IACnBC,cAAc,EAAE,SAAS;IACzBrH,oBAAoB,EAAE,WAAW;IACjCN,YAAY,EAAE,QAAQ;IACtBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,WAAW;IAC1B0H,YAAY,EAAE,UAAU;IACxBzH,gBAAgB,EAAE,cAAc;IAChCE,iBAAiB,EAAE,YAAY;IAC/BD,iBAAiB,EAAE,UAAU;IAC7ByH,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,wDAAwD;IACrEC,cAAc,EAAE,QAAQ;IACxBC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,sBAAsB;IACvCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZ7rB,MAAM,EAAE,IAAI;IACZ8rB,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,0BAA0B;IACxCC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,aAAa;IACtBC,WAAW,EAAE,8BAA8B;IAC3CC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EACpB,CAAC;EACDC,gBAAgB,EAAE;IACdC,UAAU,EAAE,WAAW;IACvBC,YAAY,EAAE,uBAAuB;IACrCC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,mCAAmC;IACjDC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,cAAc,EAAE,oBAAoB;IACpCC,YAAY,EAAE,SAAS;IACvBC,eAAe,EAAE,iBAAiB;IAClCC,gBAAgB,EAAE,wBAAwB;IAC1CC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,4CAA4C;IAC1DC,iBAAiB,EAAE,gBAAgB;IACnCpP,SAAS,EAAE,QAAQ;IACnBjf,IAAI,EAAE,IAAI;IACVsuB,OAAO,EAAE,MAAM;IACftG,KAAK,EAAE,IAAI;IACXuG,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACV9mB,KAAK,EAAE,MAAM;IACb+mB,WAAW,EAAE,QAAQ;IACrBvtB,WAAW,EAAE,SAAS;IACtBwtB,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE,OAAO;IAC1BC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,QAAQ;IAC3BC,YAAY,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE;MACN3nB,KAAK,EAAE,QAAQ;MACf4nB,KAAK,EAAE,uBAAuB;MAC9BzD,GAAG,EAAE,MAAM;MACXjsB,QAAQ,EAAE;IACd,CAAC;IACD2vB,aAAa,EAAE;MACXC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,kBAAkB;MAC1BC,MAAM,EAAE,gBAAgB;MACxBC,QAAQ,EAAE;QACN,CAAC,EAAE,aAAa;QAChB,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,OAAO;IACxB9W,QAAQ,EAAE,IAAI;IACd+W,eAAe,EAAE,QAAQ;IACzBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,YAAY;IACtBC,iBAAiB,EAAE,4BAA4B;IAC/CC,UAAU,EAAE,uBAAuB;IACnCrtB,IAAI,EAAE,IAAI;IACVstB,qBAAqB,EAAE,QAAQ;IAC/BC,mBAAmB,EAAE,UAAU;IAC/BC,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,MAAM;IACjBC,oBAAoB,EAAE,eAAe;IACrCC,WAAW,EAAE,MAAM;IACnBzmB,OAAO,EAAE,IAAI;IACbrK,MAAM,EAAE,IAAI;IACZ+wB,UAAU,EAAE,iCAAiC;IAC7CC,UAAU,EAAE,gCAAgC;IAC5CC,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,MAAM;IACjBC,uBAAuB,EAAE,QAAQ;IACjCC,UAAU,EAAE,mCAAmC;IAC/CvN,KAAK,EAAE,KAAK;IACZ3W,YAAY,EAAE,OAAO;IACrB6W,KAAK,EAAE,KAAK;IACZsN,qBAAqB,EAAE,QAAQ;IAC/BC,YAAY,EAAE,MAAM;IACpBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9B5R,QAAQ,EAAE,KAAK;IACf1e,MAAM,EAAE,IAAI;IACZsN,kBAAkB,EAAE,oBAAoB;IACxCR,eAAe,EAAE,YAAY;IAC7ByjB,MAAM,EAAE,IAAI;IACZxjB,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,MAAM;IACxBujB,QAAQ,EAAE;MACN,CAAC,EAAE,2BAA2B;MAC9B,CAAC,EAAE,4BAA4B;MAC/B,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE,mCAAmC;IAC7CC,QAAQ,EAAE,KAAK;IACfC,kBAAkB,EAAE;MAChB,CAAC,EAAE,eAAe;MAClB,CAAC,EAAE;IACP,CAAC;IACDC,cAAc,EAAE,QAAQ;IACxB3sB,QAAQ,EAAE,KAAK;IACf4sB,cAAc,EAAE,MAAM;IACtBC,qBAAqB,EAAE,eAAe;IACtCC,kBAAkB,EAAE,UAAU;IAC9BC,oBAAoB,EAAE,sBAAsB;IAC5CC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnB3c,YAAY,EAAE,MAAM;IACpB4c,SAAS,EAAE,YAAY;IACvBC,mBAAmB,EAAE,UAAU;IAC/BC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,IAAI;IACXzsB,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfiF,QAAQ,EAAE,MAAM;IAChBwnB,IAAI,EAAE,IAAI;IACV5sB,IAAI,EAAE,IAAI;IACV6sB,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,MAAM;IACpBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACTC,WAAW,EAAE;MACTvrB,KAAK,EAAE,QAAQ;MACfkY,IAAI,EAAE,0BAA0B;MAChCsT,SAAS,EAAE;IACf,CAAC;IACDC,aAAa,EAAE;MACXzrB,KAAK,EAAE,SAAS;MAChBkY,IAAI,EAAE,2BAA2B;MACjCsT,SAAS,EAAE;IACf,CAAC;IACDE,UAAU,EAAE;MACR1rB,KAAK,EAAE,OAAO;MACdkY,IAAI,EAAE,mBAAmB;MACzBsT,SAAS,EAAE;IACf,CAAC;IACDG,QAAQ,EAAE,MAAM;IAChB/N,MAAM,EAAE,IAAI;IACZgO,QAAQ,EAAE,MAAM;IAChBC,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE;IACX9rB,KAAK,EAAE,UAAU;IACjBgc,KAAK,EAAE,mBAAmB;IAC1BE,KAAK,EAAE,yBAAyB;IAChCE,KAAK,EAAE,oBAAoB;IAC3B2P,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACPpsB,KAAK,EAAE,MAAM;IACbvE,OAAO,EAAE,QAAQ;IACjB4wB,IAAI,EAAE,OAAO;IACb7gB,IAAI,EAAE,IAAI;IACV8gB,IAAI,EAAE,KAAK;IACXC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE,aAAa;IAC9BC,eAAe,EAAE,eAAe;IAChCC,YAAY,EAAE,MAAM;IACpBV,UAAU,EAAE,UAAU;IACtBW,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,aAAa;IAC3BC,mBAAmB,EAAE,gBAAgB;IACrCC,eAAe,EAAE,wBAAwB;IACzCC,aAAa,EAAE,WAAW;IAC1BC,YAAY,EAAE,aAAa;IAC3BC,QAAQ,EAAE,uBAAuB;IACjCC,QAAQ,EAAE;EACd,CAAC;EACDC,kBAAkB,EAAE;IAChBntB,KAAK,EAAE,QAAQ;IACfotB,YAAY,EAAE,qDAAqD;IACnEC,YAAY,EAAE,0CAA0C;IACxDC,WAAW,EAAE,QAAQ;IACrB9hB,IAAI,EAAE,yCAAyC;IAC/C+hB,WAAW,EAAE;EACjB,CAAC;EACD/sB,SAAS,EAAE;IACPR,KAAK,EAAE,UAAU;IACjBwtB,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;IAChCC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,MAAM;IACnBv1B,MAAM,EAAE,IAAI;IACZw1B,iBAAiB,EAAE,SAAS;IAC5BC,KAAK,EAAE;MACH5tB,KAAK,EAAE,2BAA2B;MAClC6tB,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAE;IAClB,CAAC;IACDhS,KAAK,EAAE;MACHiS,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,CAAC,wBAAwB,EAAE,UAAU,CAAC;MAClDC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,oBAAoB;MAChCC,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACjDC,UAAU,EAAE,CAAC,8BAA8B,EAAE,6BAA6B;IAC9E,CAAC;IACDpS,KAAK,EAAE;MACHlc,KAAK,EAAE,CAAC,qBAAqB,EAAE,kCAAkC,CAAC;MAClEmc,UAAU,EAAE,OAAO;MACnBoS,aAAa,EAAE,UAAU;MACzBC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,oBAAoB,EAAE;IAClBzuB,KAAK,EAAE,OAAO;IACd0uB,OAAO,EAAE,kDAAkD;IAC3Dt2B,GAAG,EAAE,kCAAkC;IACvCu2B,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,MAAM;IACdz2B,MAAM,EAAE;EACZ,CAAC;EACD02B,sBAAsB,EAAE;IACpBC,SAAS,EAAE;MACP9uB,KAAK,EAAE,MAAM;MACb5H,GAAG,EAAE,iCAAiC;MACtCU,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,mBAAmB;MACzB41B,KAAK,EAAE,cAAc;MACrBnX,IAAI,EAAE,MAAM;MACZxD,IAAI,EAAE,+BAA+B;MACrCC,IAAI,EAAE;IACV,CAAC;IACD2a,WAAW,EAAE;MACThvB,KAAK,EAAE,UAAU;MACjBivB,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;MACrDC,IAAI,EAAE,CAAC,MAAM,EAAE,oBAAoB,CAAC;MACpCC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC9C,CAAC;IACD3sB,OAAO,EAAE,IAAI;IACbrK,MAAM,EAAE;EACZ,CAAC;EACDi3B,aAAa,EAAE;IACXpvB,KAAK,EAAE,QAAQ;IACfqvB,IAAI,EAAE,wDAAwD;IAC9DC,IAAI,EAAE,8BAA8B;IACpCC,aAAa,EAAE;EACnB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}