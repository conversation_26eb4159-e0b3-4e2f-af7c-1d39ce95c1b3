{"ast": null, "code": "import Vue from 'vue';\nimport VueI18n from 'vue-i18n';\nVue.use(VueI18n);\nimport Cookies from 'js-cookie';\nimport { isServer } from '../common/plugins/cookie/cookie';\n// element-ui的多语言路径信息\nconst ELEMENT_LOCALE_PATH_MAP = {\n  en: 'en',\n  zh: 'zh-CN',\n  ru: 'ru-RU',\n  ja: 'ja',\n  ar: 'ar'\n};\nexport async function getLocale(language, path) {\n  // 加载自定义字段\n  const customLocalePromise = import(/* webpackChunkName: \"locale-[request]\" */`src/lang/${language}`);\n  // 加载element-ui\n  const elementUILocalePromise = import(/* webpackChunkName: \"locale-[request]\" */`element-ui/lib/locale/lang/${ELEMENT_LOCALE_PATH_MAP[language]}`);\n  const res = await Promise.all([customLocalePromise, elementUILocalePromise]);\n  return {\n    ...res[0].default,\n    ...res[1].default\n  };\n}\nexport async function loadLanguageAsync({\n  language,\n  path = '.js'\n} = {}) {\n  const lang = language || getDefaultLang();\n  const filePath = `${lang}${path}`;\n  const messages = await getLocale(lang, filePath);\n  i18n.setLocaleMessage(lang, messages);\n  return i18n;\n}\n\n// 用户语言偏好en 英文，zh中文，账号信息 > Cookie > 浏览器默认，账号信息在head-info获取后设置\nfunction getDefaultLang() {\n  let lang = Cookies.get('language') || (navigator.language || navigator.browserLanguage || navigator.systemLanguage).toLowerCase().substr(0, 2); // 常规浏览器语言和IE浏览器;\n\n  if (!['en', 'zh', 'ru', 'ja', 'ar'].includes(lang)) {\n    lang = 'en';\n  }\n  Cookies.set('language', lang, {\n    secure: isServer() ? true : null\n  }); // 配置cookie内默认的语言偏好\n  return lang;\n}\nconst i18n = new VueI18n({\n  locale: getDefaultLang(),\n  messages: {}\n});\nexport default i18n;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "VueI18n", "use", "Cookies", "isServer", "ELEMENT_LOCALE_PATH_MAP", "en", "zh", "ru", "ja", "ar", "getLocale", "language", "path", "customLocalePromise", "elementUILocalePromise", "res", "Promise", "all", "default", "loadLanguageAsync", "lang", "getDefaultLang", "filePath", "messages", "i18n", "setLocaleMessage", "get", "navigator", "browserLanguage", "systemLanguage", "toLowerCase", "substr", "includes", "set", "secure", "locale"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/index.js"], "sourcesContent": ["import Vue from 'vue';\nimport VueI18n from 'vue-i18n';\nVue.use(VueI18n);\nimport Cookies from 'js-cookie';\nimport { isServer } from '../common/plugins/cookie/cookie';\n// element-ui的多语言路径信息\nconst ELEMENT_LOCALE_PATH_MAP = {\n    en: 'en',\n    zh: 'zh-CN',\n    ru: 'ru-RU',\n    ja: 'ja',\n    ar: 'ar'\n};\nexport async function getLocale(language, path) {\n    // 加载自定义字段\n    const customLocalePromise = import(\n        /* webpackChunkName: \"locale-[request]\" */ `src/lang/${language}`\n    );\n    // 加载element-ui\n    const elementUILocalePromise = import(\n        /* webpackChunkName: \"locale-[request]\" */ `element-ui/lib/locale/lang/${ELEMENT_LOCALE_PATH_MAP[language]}`\n    );\n    const res = await Promise.all([customLocalePromise, elementUILocalePromise]);\n    return {\n        ...res[0].default,\n        ...res[1].default,\n    };\n}\nexport async function loadLanguageAsync({ language, path = '.js' } = {}) {\n    const lang = language || getDefaultLang();\n    const filePath = `${lang}${path}`;\n    const messages = await getLocale(lang, filePath);\n    i18n.setLocaleMessage(lang, messages);\n    return i18n;\n}\n\n// 用户语言偏好en 英文，zh中文，账号信息 > Cookie > 浏览器默认，账号信息在head-info获取后设置\nfunction getDefaultLang() {\n    let lang = Cookies.get('language') || (navigator.language || navigator.browserLanguage || navigator.systemLanguage).toLowerCase().substr(0, 2); // 常规浏览器语言和IE浏览器;\n\n    if (!['en', 'zh', 'ru', 'ja', 'ar'].includes(lang)) {\n        lang = 'en';\n    }\n    Cookies.set('language', lang, { secure: isServer() ? true : null }); // 配置cookie内默认的语言偏好\n    return lang;\n}\n\nconst i18n = new VueI18n({\n    locale: getDefaultLang(),\n    messages: {},\n});\n\nexport default i18n;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,OAAO,MAAM,UAAU;AAC9BD,GAAG,CAACE,GAAG,CAACD,OAAO,CAAC;AAChB,OAAOE,OAAO,MAAM,WAAW;AAC/B,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D;AACA,MAAMC,uBAAuB,GAAG;EAC5BC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE;AACR,CAAC;AACD,OAAO,eAAeC,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC5C;EACA,MAAMC,mBAAmB,GAAG,MAAM,CAC9B,0CAA2C,YAAYF,QAAQ,EACnE,CAAC;EACD;EACA,MAAMG,sBAAsB,GAAG,MAAM,CACjC,0CAA2C,8BAA8BV,uBAAuB,CAACO,QAAQ,CAAC,EAC9G,CAAC;EACD,MAAMI,GAAG,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAACJ,mBAAmB,EAAEC,sBAAsB,CAAC,CAAC;EAC5E,OAAO;IACH,GAAGC,GAAG,CAAC,CAAC,CAAC,CAACG,OAAO;IACjB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACG;EACd,CAAC;AACL;AACA,OAAO,eAAeC,iBAAiBA,CAAC;EAAER,QAAQ;EAAEC,IAAI,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EACrE,MAAMQ,IAAI,GAAGT,QAAQ,IAAIU,cAAc,CAAC,CAAC;EACzC,MAAMC,QAAQ,GAAG,GAAGF,IAAI,GAAGR,IAAI,EAAE;EACjC,MAAMW,QAAQ,GAAG,MAAMb,SAAS,CAACU,IAAI,EAAEE,QAAQ,CAAC;EAChDE,IAAI,CAACC,gBAAgB,CAACL,IAAI,EAAEG,QAAQ,CAAC;EACrC,OAAOC,IAAI;AACf;;AAEA;AACA,SAASH,cAAcA,CAAA,EAAG;EACtB,IAAID,IAAI,GAAGlB,OAAO,CAACwB,GAAG,CAAC,UAAU,CAAC,IAAI,CAACC,SAAS,CAAChB,QAAQ,IAAIgB,SAAS,CAACC,eAAe,IAAID,SAAS,CAACE,cAAc,EAAEC,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhJ,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACZ,IAAI,CAAC,EAAE;IAChDA,IAAI,GAAG,IAAI;EACf;EACAlB,OAAO,CAAC+B,GAAG,CAAC,UAAU,EAAEb,IAAI,EAAE;IAAEc,MAAM,EAAE/B,QAAQ,CAAC,CAAC,GAAG,IAAI,GAAG;EAAK,CAAC,CAAC,CAAC,CAAC;EACrE,OAAOiB,IAAI;AACf;AAEA,MAAMI,IAAI,GAAG,IAAIxB,OAAO,CAAC;EACrBmC,MAAM,EAAEd,cAAc,CAAC,CAAC;EACxBE,QAAQ,EAAE,CAAC;AACf,CAAC,CAAC;AAEF,eAAeC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}