{"ast": null, "code": "import { mapGetters } from 'vuex';\nexport default {\n  name: 'LangSwitch',\n  props: {\n    cacheOnly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      visible: false\n    };\n  },\n  computed: {\n    ...mapGetters(['getIsUae', 'getIsJa']),\n    lang() {\n      return this.$i18n.locale;\n    },\n    currentLanguage() {\n      return {\n        zh: '语言',\n        en: 'Switch language',\n        ja: '言語',\n        ru: 'Переключение языков',\n        ar: 'بالعربية'\n      };\n    },\n    languageList() {\n      let languageList = [{\n        lang: 'zh',\n        text: '中文'\n      }, {\n        lang: 'en',\n        text: 'English'\n      }, {\n        lang: 'ja',\n        text: '日本語'\n      }];\n      if (this.getIsUae) {\n        languageList = [{\n          lang: 'en',\n          text: 'English'\n        }, {\n          lang: 'ar',\n          text: 'بالعربية'\n        }, {\n          lang: 'zh',\n          text: '中文'\n        }];\n      } else if (this.getIsJa) {\n        languageList = [{\n          lang: 'en',\n          text: 'English'\n        }, {\n          lang: 'ja',\n          text: '日本語'\n        }, {\n          lang: 'zh',\n          text: '中文'\n        }];\n      }\n      return languageList;\n    }\n  },\n  methods: {\n    switchLang(item) {\n      const targetLang = item.lang; // 语言切换\n      if (this.cacheOnly) {\n        // 仅切换\n        this.$i18n.locale = targetLang;\n        this.$cookie.set('language', targetLang);\n        this.visible = false;\n        return;\n      }\n      this.$http.post('/users/configs/VIEW_LANGUAGE', {\n        name: 'VIEW_LANGUAGE',\n        value: targetLang\n      }).then(() => {\n        // 更新cookie，后端根据cookie做翻译\n        this.$cookie.set('language', targetLang);\n        this.visible = false;\n        location.reload(); // 部分文案在代码逻辑中确定，直接修改locale逻辑不会重新执行，刷新界面\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "name", "props", "cacheOnly", "type", "Boolean", "default", "data", "visible", "computed", "lang", "$i18n", "locale", "currentLanguage", "zh", "en", "ja", "ru", "ar", "languageList", "text", "getIsUae", "getIsJa", "methods", "switchLang", "item", "targetLang", "$cookie", "set", "$http", "post", "value", "then", "location", "reload"], "sources": ["src/components/langSwitch/index.vue"], "sourcesContent": ["<template>\n    <el-popover\n        placement=\"top\"\n        width=\"80\"\n        v-model=\"visible\"\n        popper-class=\"lang-switch-popover\"\n    >\n        <ul class=\"language-list\">\n            <li\n                v-for=\"item in languageList\"\n                :key=\"item.lang\"\n                @click=\"switchLang(item)\"\n                class=\"list-item\"\n            >\n                <span class=\"item-text\">{{ item.text }}</span>\n                <i v-if=\"lang === item.lang\" class=\"iconfont el-icon-ssq-xuanzhong\"></i>\n            </li>\n        </ul>\n        <span slot=\"reference\" class=\"lang-switch cur-pointer\">\n            <i class=\"el-icon-ssq-diqiu\"></i>\n            {{ currentLanguage[lang] }}\n        </span>\n    </el-popover>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex';\nexport default {\n    name: 'LangSwitch',\n    props: {\n        cacheOnly: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    data() {\n        return {\n            visible: false,\n        };\n    },\n    computed: {\n        ...mapGetters(['getIsUae', 'getIsJa']),\n        lang() {\n            return this.$i18n.locale;\n        },\n        currentLanguage() {\n            return {\n                zh: '语言',\n                en: 'Switch language',\n                ja: '言語',\n                ru: 'Переключение языков',\n                ar: 'بالعربية',\n            };\n        },\n        languageList() {\n            let languageList = [\n                { lang: 'zh', text: '中文' },\n                { lang: 'en', text: 'English' },\n                { lang: 'ja', text: '日本語' },\n            ];\n            if (this.getIsUae) {\n                languageList = [\n                    { lang: 'en', text: 'English' },\n                    { lang: 'ar', text: 'بالعربية' },\n                    { lang: 'zh', text: '中文' },\n                ];\n            } else if (this.getIsJa) {\n                languageList = [\n                    { lang: 'en', text: 'English' },\n                    { lang: 'ja', text: '日本語' },\n                    { lang: 'zh', text: '中文' },\n                ];\n            }\n            return languageList;\n        },\n    },\n    methods: {\n        switchLang(item) {\n            const targetLang = item.lang; // 语言切换\n            if (this.cacheOnly) { // 仅切换\n                this.$i18n.locale = targetLang;\n                this.$cookie.set('language', targetLang);\n                this.visible = false;\n                return;\n            }\n            this.$http.post('/users/configs/VIEW_LANGUAGE', {\n                name: 'VIEW_LANGUAGE',\n                value: targetLang,\n            }).then(() => {\n                // 更新cookie，后端根据cookie做翻译\n                this.$cookie.set('language', targetLang);\n                this.visible = false;\n                location.reload(); // 部分文案在代码逻辑中确定，直接修改locale逻辑不会重新执行，刷新界面\n            });\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\">\n.lang-switch-popover{\n    padding: 12px 10px;\n    width: 80px;\n    min-width: inherit;\n    background: #303133;\n    color: #fff;\n    border: 1px solid #303133;\n    .language-list{\n        .list-item{\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            width: 100%;\n            font-size: 14px;\n            font-weight: 300;\n            cursor: pointer;\n            &:nth-child(2){\n                margin: 12px 0;\n            }\n            &:hover{\n                color: $theme-color;\n            }\n            .iconfont{\n                color: $theme-color;\n            }\n        }\n    }\n}\n.lang-switch-popover.el-popper[x-placement^=top] .popper__arrow{\n    border-top-color: #303133;\n}\n.lang-switch-popover.el-popper[x-placement^=top] .popper__arrow::after {\n    border-top-color: #303133;\n}\n.lang-switch-popover.el-popper[x-placement^=bottom] .popper__arrow{\n    border-bottom-color: #303133;\n}\n.lang-switch-popover.el-popper[x-placement^=bottom] .popper__arrow::after {\n    border-bottom-color: #303133;\n}\n.lang-switch {\n    display: inline-block;\n    margin-right: 5px;\n    margin-left: 5px;\n    font-size: 14px;\n    color: #fff;\n    i {\n        font-size: 16px;\n        padding-right: 2px;\n        vertical-align: text-bottom;\n    }\n}\n</style>\n"], "mappings": "AA0BA,SAAAA,UAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAT,UAAA;IACAU,KAAA;MACA,YAAAC,KAAA,CAAAC,MAAA;IACA;IACAC,gBAAA;MACA;QACAC,EAAA;QACAC,EAAA;QACAC,EAAA;QACAC,EAAA;QACAC,EAAA;MACA;IACA;IACAC,aAAA;MACA,IAAAA,YAAA,IACA;QAAAT,IAAA;QAAAU,IAAA;MAAA,GACA;QAAAV,IAAA;QAAAU,IAAA;MAAA,GACA;QAAAV,IAAA;QAAAU,IAAA;MAAA,EACA;MACA,SAAAC,QAAA;QACAF,YAAA,IACA;UAAAT,IAAA;UAAAU,IAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,IAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,IAAA;QAAA,EACA;MACA,gBAAAE,OAAA;QACAH,YAAA,IACA;UAAAT,IAAA;UAAAU,IAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,IAAA;QAAA,GACA;UAAAV,IAAA;UAAAU,IAAA;QAAA,EACA;MACA;MACA,OAAAD,YAAA;IACA;EACA;EACAI,OAAA;IACAC,WAAAC,IAAA;MACA,MAAAC,UAAA,GAAAD,IAAA,CAAAf,IAAA;MACA,SAAAP,SAAA;QAAA;QACA,KAAAQ,KAAA,CAAAC,MAAA,GAAAc,UAAA;QACA,KAAAC,OAAA,CAAAC,GAAA,aAAAF,UAAA;QACA,KAAAlB,OAAA;QACA;MACA;MACA,KAAAqB,KAAA,CAAAC,IAAA;QACA7B,IAAA;QACA8B,KAAA,EAAAL;MACA,GAAAM,IAAA;QACA;QACA,KAAAL,OAAA,CAAAC,GAAA,aAAAF,UAAA;QACA,KAAAlB,OAAA;QACAyB,QAAA,CAAAC,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}