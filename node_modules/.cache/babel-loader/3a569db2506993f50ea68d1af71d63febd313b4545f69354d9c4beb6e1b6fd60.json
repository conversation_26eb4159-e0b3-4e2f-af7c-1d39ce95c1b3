{"ast": null, "code": "export default {\n  utils: {\n    faceVerified: 'Has <PERSON><PERSON><PERSON>\\'s face verification completed?',\n    unDone: 'No',\n    done: 'Yes',\n    changeVerifyMethod: 'Due to the failure to complete <PERSON><PERSON>y\\'s face verification, other face verification methods will continue to be used to complete the contract signing.'\n  }\n};", "map": {"version": 3, "names": ["utils", "faceVerified", "unDone", "done", "changeVerifyMethod"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/utils/utils-en.js"], "sourcesContent": ["export default {\n    utils: {\n        faceVerified: 'Has <PERSON><PERSON><PERSON>\\'s face verification completed?',\n        unDone: 'No',\n        done: 'Yes',\n        changeVerifyMethod: 'Due to the failure to complete <PERSON><PERSON>y\\'s face verification, other face verification methods will continue to be used to complete the contract signing.',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,YAAY,EAAE,4CAA4C;IAC1DC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,KAAK;IACXC,kBAAkB,EAAE;EACxB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}