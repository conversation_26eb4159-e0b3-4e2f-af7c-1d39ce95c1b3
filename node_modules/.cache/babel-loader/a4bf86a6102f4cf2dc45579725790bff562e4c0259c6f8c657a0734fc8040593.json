{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hubble-document\"\n  }, [_c(\"div\", {\n    staticClass: \"hubble-document__header\"\n  }, [_c(\"div\", {\n    staticClass: \"hubble-document__title\"\n  }, [_vm._v(\" \" + _vm._s(_vm.documentInfo.fileName) + \" \")]), _c(\"div\", {\n    staticClass: \"hubble-document__scale-box\"\n  }, [_c(\"i\", {\n    staticClass: \"iconfont el-icon-ssq-suoxiaojing scale\",\n    on: {\n      click: function ($event) {\n        return _vm.handleScale(\"minus\");\n      }\n    }\n  }), _c(\"i\", {\n    staticClass: \"iconfont el-icon-ssq-fangdajing1 scale\",\n    on: {\n      click: function ($event) {\n        return _vm.handleScale(\"plus\");\n      }\n    }\n  }), _c(\"i\", {\n    staticClass: \"el-icon-ssq-jiantou1\",\n    on: {\n      click: function ($event) {\n        return _vm.handlePage(\"pre\");\n      }\n    }\n  }), _c(\"el-input\", {\n    on: {\n      blur: _vm.scrollTo\n    },\n    nativeOn: {\n      keyup: function ($event) {\n        if (!$event.type.indexOf(\"key\") && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        return _vm.scrollTo.apply(null, arguments);\n      }\n    },\n    model: {\n      value: _vm.currentPage,\n      callback: function ($$v) {\n        _vm.currentPage = $$v;\n      },\n      expression: \"currentPage\"\n    }\n  }), _c(\"span\", [_c(\"i\", [_vm._v(\"/\")]), _vm._v(\" \"), _c(\"em\", [_vm._v(_vm._s(_vm.documentInfo.pageSize))])]), _c(\"i\", {\n    staticClass: \"el-icon-ssq-jiantou1\",\n    on: {\n      click: function ($event) {\n        return _vm.handlePage(\"next\");\n      }\n    }\n  })], 1)]), _c(\"el-tooltip\", {\n    attrs: {\n      \"open-delay\": 500,\n      effect: \"dark\",\n      content: \"选中文字后点击提问\",\n      placement: \"top\"\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: !_vm.showQuote,\n      expression: \"!showQuote\"\n    }],\n    staticClass: \"hubble-document__quote fixed\",\n    attrs: {\n      id: _vm.showQuote ? \"\" : \"guide-quote\"\n    }\n  })]), _c(\"div\", {\n    ref: \"documentContent\",\n    staticClass: \"hubble-document__content\",\n    on: {\n      scroll: _vm.handleScroll\n    }\n  }, [_vm.pdfLoading ? _c(\"div\", {\n    staticClass: \"hubble-document__content-loading\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"views/agent/img/loading.gif\"),\n      alt: \"\"\n    }\n  })]) : _vm._e(), _c(\"div\", {\n    ref: \"pdfContainer\",\n    staticClass: \"hubble-document__content-box\"\n  }, [_c(\"el-tooltip\", {\n    attrs: {\n      \"open-delay\": 500,\n      effect: \"dark\",\n      content: \"选中文字后点击提问\",\n      placement: \"top\"\n    }\n  }, [_c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showQuote,\n      expression: \"showQuote\"\n    }],\n    staticClass: \"hubble-document__quote\",\n    style: _vm.quoteStyle,\n    attrs: {\n      id: !_vm.showQuote ? \"\" : \"guide-quote\"\n    },\n    on: {\n      click: function ($event) {\n        $event.stopPropagation();\n        return _vm.handleQuote.apply(null, arguments);\n      }\n    }\n  })])], 1)])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "documentInfo", "fileName", "on", "click", "$event", "handleScale", "handlePage", "blur", "scrollTo", "nativeOn", "keyup", "type", "indexOf", "_k", "keyCode", "key", "apply", "arguments", "model", "value", "currentPage", "callback", "$$v", "expression", "pageSize", "attrs", "effect", "content", "placement", "directives", "name", "rawName", "showQuote", "id", "ref", "scroll", "handleScroll", "pdfLoading", "src", "require", "alt", "_e", "style", "quoteStyle", "stopPropagation", "handleQuote", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/chat/document/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hubble-document\" },\n    [\n      _c(\"div\", { staticClass: \"hubble-document__header\" }, [\n        _c(\"div\", { staticClass: \"hubble-document__title\" }, [\n          _vm._v(\" \" + _vm._s(_vm.documentInfo.fileName) + \" \"),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"hubble-document__scale-box\" },\n          [\n            _c(\"i\", {\n              staticClass: \"iconfont el-icon-ssq-suoxiaojing scale\",\n              on: {\n                click: function ($event) {\n                  return _vm.handleScale(\"minus\")\n                },\n              },\n            }),\n            _c(\"i\", {\n              staticClass: \"iconfont el-icon-ssq-fangdajing1 scale\",\n              on: {\n                click: function ($event) {\n                  return _vm.handleScale(\"plus\")\n                },\n              },\n            }),\n            _c(\"i\", {\n              staticClass: \"el-icon-ssq-jiantou1\",\n              on: {\n                click: function ($event) {\n                  return _vm.handlePage(\"pre\")\n                },\n              },\n            }),\n            _c(\"el-input\", {\n              on: { blur: _vm.scrollTo },\n              nativeOn: {\n                keyup: function ($event) {\n                  if (\n                    !$event.type.indexOf(\"key\") &&\n                    _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                  )\n                    return null\n                  return _vm.scrollTo.apply(null, arguments)\n                },\n              },\n              model: {\n                value: _vm.currentPage,\n                callback: function ($$v) {\n                  _vm.currentPage = $$v\n                },\n                expression: \"currentPage\",\n              },\n            }),\n            _c(\"span\", [\n              _c(\"i\", [_vm._v(\"/\")]),\n              _vm._v(\" \"),\n              _c(\"em\", [_vm._v(_vm._s(_vm.documentInfo.pageSize))]),\n            ]),\n            _c(\"i\", {\n              staticClass: \"el-icon-ssq-jiantou1\",\n              on: {\n                click: function ($event) {\n                  return _vm.handlePage(\"next\")\n                },\n              },\n            }),\n          ],\n          1\n        ),\n      ]),\n      _c(\n        \"el-tooltip\",\n        {\n          attrs: {\n            \"open-delay\": 500,\n            effect: \"dark\",\n            content: \"选中文字后点击提问\",\n            placement: \"top\",\n          },\n        },\n        [\n          _c(\"div\", {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: !_vm.showQuote,\n                expression: \"!showQuote\",\n              },\n            ],\n            staticClass: \"hubble-document__quote fixed\",\n            attrs: { id: _vm.showQuote ? \"\" : \"guide-quote\" },\n          }),\n        ]\n      ),\n      _c(\n        \"div\",\n        {\n          ref: \"documentContent\",\n          staticClass: \"hubble-document__content\",\n          on: { scroll: _vm.handleScroll },\n        },\n        [\n          _vm.pdfLoading\n            ? _c(\"div\", { staticClass: \"hubble-document__content-loading\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: require(\"views/agent/img/loading.gif\"),\n                    alt: \"\",\n                  },\n                }),\n              ])\n            : _vm._e(),\n          _c(\n            \"div\",\n            {\n              ref: \"pdfContainer\",\n              staticClass: \"hubble-document__content-box\",\n            },\n            [\n              _c(\n                \"el-tooltip\",\n                {\n                  attrs: {\n                    \"open-delay\": 500,\n                    effect: \"dark\",\n                    content: \"选中文字后点击提问\",\n                    placement: \"top\",\n                  },\n                },\n                [\n                  _c(\"div\", {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.showQuote,\n                        expression: \"showQuote\",\n                      },\n                    ],\n                    staticClass: \"hubble-document__quote\",\n                    style: _vm.quoteStyle,\n                    attrs: { id: !_vm.showQuote ? \"\" : \"guide-quote\" },\n                    on: {\n                      click: function ($event) {\n                        $event.stopPropagation()\n                        return _vm.handleQuote.apply(null, arguments)\n                      },\n                    },\n                  }),\n                ]\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA0B,CAAC,EAAE,CACpDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,EAAE,CACnDH,GAAG,CAACI,EAAE,CAAC,GAAG,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,YAAY,CAACC,QAAQ,CAAC,GAAG,GAAG,CAAC,CACtD,CAAC,EACFN,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAC7C,CACEF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,wCAAwC;IACrDK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACW,WAAW,CAAC,OAAO,CAAC;MACjC;IACF;EACF,CAAC,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,wCAAwC;IACrDK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACW,WAAW,CAAC,MAAM,CAAC;MAChC;IACF;EACF,CAAC,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,sBAAsB;IACnCK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACY,UAAU,CAAC,KAAK,CAAC;MAC9B;IACF;EACF,CAAC,CAAC,EACFX,EAAE,CAAC,UAAU,EAAE;IACbO,EAAE,EAAE;MAAEK,IAAI,EAAEb,GAAG,CAACc;IAAS,CAAC;IAC1BC,QAAQ,EAAE;MACRC,KAAK,EAAE,SAAAA,CAAUN,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACO,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC,IAC3BlB,GAAG,CAACmB,EAAE,CAACT,MAAM,CAACU,OAAO,EAAE,OAAO,EAAE,EAAE,EAAEV,MAAM,CAACW,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAOrB,GAAG,CAACc,QAAQ,CAACQ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC5C;IACF,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAEzB,GAAG,CAAC0B,WAAW;MACtBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB5B,GAAG,CAAC0B,WAAW,GAAGE,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACF5B,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACtBJ,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,EACXH,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,YAAY,CAACwB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtD,CAAC,EACF7B,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,sBAAsB;IACnCK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOV,GAAG,CAACY,UAAU,CAAC,MAAM,CAAC;MAC/B;IACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFX,EAAE,CACA,YAAY,EACZ;IACE8B,KAAK,EAAE;MACL,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,WAAW;MACpBC,SAAS,EAAE;IACb;EACF,CAAC,EACD,CACEjC,EAAE,CAAC,KAAK,EAAE;IACRkC,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBZ,KAAK,EAAE,CAACzB,GAAG,CAACsC,SAAS;MACrBT,UAAU,EAAE;IACd,CAAC,CACF;IACD1B,WAAW,EAAE,8BAA8B;IAC3C4B,KAAK,EAAE;MAAEQ,EAAE,EAAEvC,GAAG,CAACsC,SAAS,GAAG,EAAE,GAAG;IAAc;EAClD,CAAC,CAAC,CAEN,CAAC,EACDrC,EAAE,CACA,KAAK,EACL;IACEuC,GAAG,EAAE,iBAAiB;IACtBrC,WAAW,EAAE,0BAA0B;IACvCK,EAAE,EAAE;MAAEiC,MAAM,EAAEzC,GAAG,CAAC0C;IAAa;EACjC,CAAC,EACD,CACE1C,GAAG,CAAC2C,UAAU,GACV1C,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmC,CAAC,EAAE,CAC7DF,EAAE,CAAC,KAAK,EAAE;IACR8B,KAAK,EAAE;MACLa,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAC;MAC3CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,GACF9C,GAAG,CAAC+C,EAAE,CAAC,CAAC,EACZ9C,EAAE,CACA,KAAK,EACL;IACEuC,GAAG,EAAE,cAAc;IACnBrC,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CACA,YAAY,EACZ;IACE8B,KAAK,EAAE;MACL,YAAY,EAAE,GAAG;MACjBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,WAAW;MACpBC,SAAS,EAAE;IACb;EACF,CAAC,EACD,CACEjC,EAAE,CAAC,KAAK,EAAE;IACRkC,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBZ,KAAK,EAAEzB,GAAG,CAACsC,SAAS;MACpBT,UAAU,EAAE;IACd,CAAC,CACF;IACD1B,WAAW,EAAE,wBAAwB;IACrC6C,KAAK,EAAEhD,GAAG,CAACiD,UAAU;IACrBlB,KAAK,EAAE;MAAEQ,EAAE,EAAE,CAACvC,GAAG,CAACsC,SAAS,GAAG,EAAE,GAAG;IAAc,CAAC;IAClD9B,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvBA,MAAM,CAACwC,eAAe,CAAC,CAAC;QACxB,OAAOlD,GAAG,CAACmD,WAAW,CAAC7B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C;IACF;EACF,CAAC,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI6B,eAAe,GAAG,EAAE;AACxBrD,MAAM,CAACsD,aAAa,GAAG,IAAI;AAE3B,SAAStD,MAAM,EAAEqD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}