{"ast": null, "code": "import LangSwitch from 'components/langSwitch';\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {};\n  },\n  computed: {\n    lang() {\n      return this.$i18n.locale;\n    }\n  }\n};", "map": {"version": 3, "names": ["LangSwitch", "components", "data", "computed", "lang", "$i18n", "locale"], "sources": ["src/components/customFooter/CustomFooter.vue"], "sourcesContent": ["<!-- 定制二级域名展示的特定footer -->\n<template>\n    <footer class=\"custom-footer\">\n        <ul class=\"clear font-size-zero\">\n            <li><img src=\"~img/customFooterLogo.png\" width=\"142\" alt=\"$t('commonFooter.ssqLogo')\"></li>\n            <li>\n                <span><!-- 电子签约服务由 -->{{ $t('commonFooter.provideTip') }}<strong><!-- 上上签 -->{{ $t('commonFooter.ssq') }}</strong><!-- 提供 -->{{ $t('commonFooter.provide') }}</span>\n                <i>|</i>\n            </li>\n            <li class=\"lang-switch-btn\">\n                <LangSwitch></LangSwitch>\n                <i>|</i>\n            </li>\n            <li v-if=\"lang === 'zh'\">\n                <span><!-- 签约服务热线 -->{{ $t('commonFooter.signHotline') }}：400-993-6665</span>\n                <i>|</i>\n            </li>\n            <li>\n                <span>{{ $t('commonFooter.record') }}</span>\n            </li>\n        </ul>\n    </footer>\n</template>\n<script>\nimport LangSwitch from 'components/langSwitch';\nexport default {\n    components: {\n        LangSwitch,\n    },\n    data() {\n        return {};\n    },\n    computed: {\n        lang() {\n            return this.$i18n.locale;\n        },\n    },\n};\n</script>\n<style lang=\"scss\">\n$border-color: #ddd;\nfooter.custom-footer {\n    box-sizing: border-box;\n    width: 100%;\n    height: 35px;\n    // line-height: 35px;\n    padding-top: 7px;\n    // padding-bottom: 15px;\n    border-top: 1px solid $border-color;\n    background-color: #f6f6f6;\n    ul {\n        display: block;\n        width: 100%;\n        text-align: center;\n        white-space: nowrap;\n        overflow: hidden;\n        li {\n            display: inline-block;\n            vertical-align: text-bottom;\n            font-size: 12px;\n            color: #666;\n            img{\n                margin-right: 15px;\n                vertical-align: middle;\n                [dir=\"rtl\"] & {\n                    margin-right: 0;\n                    margin-left: 15px;\n                }\n            }\n            i {\n                display: inline-block;\n                margin: 0 10px;\n                color: #d6d6d6;\n            }\n            strong{\n                color: #333;\n            }\n            &.lang-switch-btn {\n                span {\n                    font-size: 12px;\n                    color: #999999;\n                }\n                i.el-icon-ssq-diqiu {\n                    margin: 0;\n                    padding-right: 5px;\n                    vertical-align: bottom;\n                    [dir=\"rtl\"] & {\n                        padding-right: 0;\n                        padding-left: 5px;\n                    }\n                }\n            }\n        }\n    }\n}\n</style>\n"], "mappings": "AAwBA,OAAAA,UAAA;AACA;EACAC,UAAA;IACAD;EACA;EACAE,KAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA;MACA,YAAAC,KAAA,CAAAC,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}