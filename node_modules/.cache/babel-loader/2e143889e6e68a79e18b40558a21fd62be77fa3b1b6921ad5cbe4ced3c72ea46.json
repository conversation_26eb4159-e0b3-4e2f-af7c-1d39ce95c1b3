{"ast": null, "code": "export default {\n  docSlider: {\n    linkContractMap: {\n      notSupportFuctionTip: 'هذه الميزة غير مفعلة لمؤسستك، يمكنك التواصل مع خدمة العملاء لتفعيلها.',\n      dissolveContractTip: 'هل أنت متأكد من إلغاء ربط العقد',\n      inputContractNum: 'يرجى إدخال رقم العقد أولاً',\n      linkSuccess: 'تم الربط بنجاح',\n      linkExist: 'الربط موجود بالفعل'\n    }\n  },\n  docContentTable: {\n    catchMap: {\n      download: 'تحميل',\n      reject: 'رفض',\n      revoke: 'إلغاء',\n      delete: 'حذف',\n      cantOperate: 'لا يمكن {operate} العقد',\n      hybridNetHeader: 'يستخدم المرسل تخزين خاص للعقود، ولكن لا يمكن الاتصال بخادم تخزين العقود.',\n      hybridNetMsg: 'نقترح: التحقق من اتصال الشبكة',\n      checkNet: 'يرجى التحقق من اتصال الشبكة',\n      hybridNotConnect: 'السبب: مؤسستك تستخدم تخزين خاص للعقود، ولكن لا يمكن الاتصال بخادم التخزين.',\n      hybridSuggest: 'نقترح: (1) التحقق من اتصال الشبكة؛ (2) التحقق من حالة خادم تخزين العقود'\n    },\n    confirm: 'تأكيد',\n    searchAll: 'تحديد الكل',\n    isCheckingNet: 'جاري فحص بيئة شبكة السحابة الهجينة',\n    transferSucess: 'تم النقل بنجاح'\n  },\n  docDialog: {\n    confirm: 'تأكيد',\n    cancel: 'إلغاء',\n    notCliam: 'لم يتم المطالبة بالعقد',\n    chooseNewOwner: 'يرجى اختيار المالك الجديد',\n    newOwner: 'المالك الجديد',\n    originOwner: 'المالك الأصلي',\n    contractTransfer: 'نقل العقد'\n  }\n};", "map": {"version": 3, "names": ["doc<PERSON>lider", "linkContractMap", "notSupportFuctionTip", "dissolveContractTip", "inputContractNum", "linkSuccess", "linkExist", "docContentTable", "catchMap", "download", "reject", "revoke", "delete", "cantOperate", "hybridNetHeader", "hybridNetMsg", "checkNet", "hybridNotConnect", "hybridSuggest", "confirm", "searchAll", "isCheckingNet", "transferSucess", "docDialog", "cancel", "notCliam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new<PERSON>wner", "origin<PERSON><PERSON>er", "contractTransfer"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/docList/docList-ar.js"], "sourcesContent": ["export default {\n    docSlider: {\n        linkContractMap: {\n            notSupportFuctionTip: 'هذه الميزة غير مفعلة لمؤسستك، يمكنك التواصل مع خدمة العملاء لتفعيلها.',\n            dissolveContractTip: 'هل أنت متأكد من إلغاء ربط العقد',\n            inputContractNum: 'يرجى إدخال رقم العقد أولاً',\n            linkSuccess: 'تم الربط بنجاح',\n            linkExist: 'الربط موجود بالفعل',\n        },\n    },\n    docContentTable: {\n        catchMap: {\n            download: 'تحميل',\n            reject: 'رفض',\n            revoke: 'إلغاء',\n            delete: 'حذف',\n            cantOperate: 'لا يمكن {operate} العقد',\n            hybridNetHeader: 'يستخدم المرسل تخزين خاص للعقود، ولكن لا يمكن الاتصال بخادم تخزين العقود.',\n            hybridNetMsg: 'نقترح: التحقق من اتصال الشبكة',\n            checkNet: 'يرجى التحقق من اتصال الشبكة',\n            hybridNotConnect: 'السبب: مؤسستك تستخدم تخزين خاص للعقود، ولكن لا يمكن الاتصال بخادم التخزين.',\n            hybridSuggest: 'نقترح: (1) التحقق من اتصال الشبكة؛ (2) التحقق من حالة خادم تخزين العقود',\n        },\n        confirm: 'تأكيد',\n        searchAll: 'تحديد الكل',\n        isCheckingNet: 'جاري فحص بيئة شبكة السحابة الهجينة',\n        transferSucess: 'تم النقل بنجاح',\n    },\n    docDialog: {\n        confirm: 'تأكيد',\n        cancel: 'إلغاء',\n        notCliam: 'لم يتم المطالبة بالعقد',\n        chooseNewOwner: 'يرجى اختيار المالك الجديد',\n        newOwner: 'المالك الجديد',\n        originOwner: 'المالك الأصلي',\n        contractTransfer: 'نقل العقد',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,SAAS,EAAE;IACPC,eAAe,EAAE;MACbC,oBAAoB,EAAE,uEAAuE;MAC7FC,mBAAmB,EAAE,iCAAiC;MACtDC,gBAAgB,EAAE,4BAA4B;MAC9CC,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE;IACf;EACJ,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE;MACNC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,KAAK;MACbC,WAAW,EAAE,yBAAyB;MACtCC,eAAe,EAAE,0EAA0E;MAC3FC,YAAY,EAAE,+BAA+B;MAC7CC,QAAQ,EAAE,6BAA6B;MACvCC,gBAAgB,EAAE,4EAA4E;MAC9FC,aAAa,EAAE;IACnB,CAAC;IACDC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,oCAAoC;IACnDC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPJ,OAAO,EAAE,OAAO;IAChBK,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,wBAAwB;IAClCC,cAAc,EAAE,2BAA2B;IAC3CC,QAAQ,EAAE,eAAe;IACzBC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE;EACtB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}