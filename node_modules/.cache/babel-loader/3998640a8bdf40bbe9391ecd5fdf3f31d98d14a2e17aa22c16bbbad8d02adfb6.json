{"ast": null, "code": "export default {\n  consts: {\n    contractAlias: {\n      doc: '文件',\n      letter: '询征函',\n      proof: '授权书',\n      contract: '契約書',\n      agreement: '同意書',\n      service_report: '作業指示書'\n    }\n  },\n  pointPositionDoc: {\n    pageTip: '第{pageNum}页共{pageSize}页',\n    nextDoc: '进入下一份文档',\n    deleteTip: '删除成功'\n  },\n  pointPositionMiniDoc: {\n    document: '文档',\n    documentsLength: '共{documentsLength}份',\n    pager: '页码',\n    page: '页'\n  },\n  pointPositionSite: {\n    watermarkTip: '发送合同后自动替换为真实信息',\n    singlePageRidingSealTip: '单页文档无法添加骑缝章'\n  }\n};", "map": {"version": 3, "names": ["consts", "contractAlias", "doc", "letter", "proof", "contract", "agreement", "service_report", "pointPositionDoc", "pageTip", "nextDoc", "deleteTip", "pointPositionMiniDoc", "document", "documentsLength", "pager", "page", "pointPositionSite", "watermarkTip", "singlePageRidingSealTip"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/consts/ja.js"], "sourcesContent": ["export default {\n    consts: {\n        contractAlias: {\n            doc: '文件',\n            letter: '询征函',\n            proof: '授权书',\n            contract: '契約書',\n            agreement: '同意書',\n            service_report: '作業指示書',\n        },\n    },\n    pointPositionDoc: {\n        pageTip: '第{pageNum}页共{pageSize}页',\n        nextDoc: '进入下一份文档',\n        deleteTip: '删除成功',\n    },\n    pointPositionMiniDoc: {\n        document: '文档',\n        documentsLength: '共{documentsLength}份',\n        pager: '页码',\n        page: '页',\n    },\n    pointPositionSite: {\n        watermarkTip: '发送合同后自动替换为真实信息',\n        singlePageRidingSealTip: '单页文档无法添加骑缝章',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,MAAM,EAAE;IACJC,aAAa,EAAE;MACXC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE;IACpB;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdC,OAAO,EAAE,yBAAyB;IAClCC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE;EACf,CAAC;EACDC,oBAAoB,EAAE;IAClBC,QAAQ,EAAE,IAAI;IACdC,eAAe,EAAE,qBAAqB;IACtCC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACfC,YAAY,EAAE,gBAAgB;IAC9BC,uBAAuB,EAAE;EAC7B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}