{"ast": null, "code": "export default {\n  props: {\n    messages: {\n      type: Array,\n      default: () => []\n    },\n    isContinuousChat: {\n      type: <PERSON><PERSON><PERSON>,\n      default: false\n    }\n  },\n  computed: {\n    currentMessageList() {\n      return [...this.messages].reverse();\n    }\n  },\n  watch: {\n    messages: {\n      handler() {\n        this.$nextTick(() => {\n          this.scrollList();\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    showAnswerDocumentQuotes(topic) {\n      return !!topic.answerDocumentQuotes?.length;\n    },\n    showCursor(index) {\n      return index === 0 && this.typing;\n    },\n    scrollList() {\n      const element = document.querySelector('.continuous-chat')?.querySelector('.hubble-chat__body-box');\n      element && (element.scrollTop = element.scrollHeight);\n    },\n    handleDelete(index) {\n      this.$confirm('确认删除该对话吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消'\n      }).then(() => {\n        this.$http.delete(`/web/hubble/topic/${this.$route.params.topicId}/${this.currentMessageList[index].chatId}`).then(() => {\n          this.$MessageToast('删除成功');\n          this.messages.splice(this.messages.length - index - 1, 1);\n        });\n      }).catch(() => {});\n    }\n  }\n};", "map": {"version": 3, "names": ["props", "messages", "type", "Array", "default", "isContinuousChat", "Boolean", "computed", "currentMessageList", "reverse", "watch", "handler", "$nextTick", "scrollList", "deep", "immediate", "methods", "showAnswerDocumentQuotes", "topic", "answerDocumentQuotes", "length", "showCursor", "index", "typing", "element", "document", "querySelector", "scrollTop", "scrollHeight", "handleDelete", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$http", "delete", "$route", "params", "topicId", "chatId", "$MessageToast", "splice", "catch"], "sources": ["src/views/agent/chat/chatView/chatList/index.vue"], "sourcesContent": ["<template>\n    <ul class=\"hubble-chat__body-box\" @scroll=\"$emit('loadMore', $event)\">\n        <li class=\"hubble-chat__body-empty\" v-if=\"!messages.length\">\n            <img src=\"~views/agent/img/empty.png\" alt=\"\">\n            <p>在下方输入或在合同上选取后点击“Hubble”，进行提问</p>\n        </li>\n        <li v-for=\"(topic, index) in currentMessageList\" :key=\"index\" class=\"message\">\n            <div class=\"question\" v-if=\"topic.question\">\n                <img src=\"~views/agent/img/avatar.png\" class=\"avatar\" alt=\"\">\n                <div class=\"message-content\">\n                    <div class=\"quote chat-quote\" v-if=\"topic.quote\" @click=\"$emit('selectQuote', topic.questionDocumentQuote)\">\n                        {{ topic.quote }}\n                    </div>\n                    问题：{{ topic.question }}\n                </div>\n            </div>\n            <span class=\"time\">{{ topic.chatTime }}</span>\n            <div class=\"answer\" v-if=\"topic.answer\">\n                <img src=\"~views/agent/img/AIAvatar.png\" class=\"avatar\" alt=\"\">\n                <div class=\"message-content\">\n                    {{ topic.answer }}\n                    <span class=\"cursor\" v-if=\"showCursor(index)\"></span><br>\n                    <span class=\"explain\">~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记</span>\n                </div>\n            </div>\n            <div class=\"related\" v-show=\"topic.chatId\">\n                <template v-if=\"showAnswerDocumentQuotes(topic)\">\n                    相关内容：\n                    <span v-for=\"(quote, i) in topic.answerDocumentQuotes\" @click=\"$emit('selectQuote', quote)\" :key=\"i\">{{ quote.pageNumber }}</span>\n                </template>\n                <el-tooltip :open-delay=\"500\" effect=\"dark\" content=\"删除对话\" placement=\"top\">\n                    <i class=\"operate-icon el-icon-ssq-Hubbleshanchu\" @click=\"handleDelete(index)\"></i>\n                </el-tooltip>\n                <el-tooltip :open-delay=\"500\" effect=\"dark\" content=\"开启连续对话\" placement=\"top\">\n                    <i class=\"operate-icon el-icon-ssq-Hubblelianxuduihua\" v-if=\"!isContinuousChat\" @click=\"$emit('showContinuousChat', topic.chatId)\"></i>\n                </el-tooltip>\n            </div>\n        </li>\n    </ul>\n</template>\n\n<script>\nexport default {\n    props: {\n        messages: {\n            type: Array,\n            default: () => [],\n        },\n        isContinuousChat: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    computed: {\n        currentMessageList() {\n            return [...this.messages].reverse();\n        },\n    },\n    watch: {\n        messages: {\n            handler() {\n                this.$nextTick(() => {\n                    this.scrollList();\n                });\n            },\n            deep: true,\n            immediate: true,\n        },\n    },\n    methods: {\n        showAnswerDocumentQuotes(topic) {\n            return !!topic.answerDocumentQuotes?.length;\n        },\n        showCursor(index) {\n            return index === 0 && this.typing;\n        },\n        scrollList() {\n            const element = document.querySelector('.continuous-chat')?.querySelector('.hubble-chat__body-box');\n            element && (element.scrollTop = element.scrollHeight);\n        },\n        handleDelete(index) {\n            this.$confirm('确认删除该对话吗？', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n            }).then(() => {\n                this.$http.delete(`/web/hubble/topic/${this.$route.params.topicId}/${this.currentMessageList[index].chatId}`).then(() => {\n                    this.$MessageToast('删除成功');\n                    this.messages.splice(this.messages.length - index - 1, 1);\n                });\n            }).catch(() => {});\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\">\n</style>\n"], "mappings": "AA0CA;EACAA,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAC,gBAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,QAAA;IACAC,mBAAA;MACA,gBAAAP,QAAA,EAAAQ,OAAA;IACA;EACA;EACAC,KAAA;IACAT,QAAA;MACAU,QAAA;QACA,KAAAC,SAAA;UACA,KAAAC,UAAA;QACA;MACA;MACAC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,yBAAAC,KAAA;MACA,SAAAA,KAAA,CAAAC,oBAAA,EAAAC,MAAA;IACA;IACAC,WAAAC,KAAA;MACA,OAAAA,KAAA,eAAAC,MAAA;IACA;IACAV,WAAA;MACA,MAAAW,OAAA,GAAAC,QAAA,CAAAC,aAAA,sBAAAA,aAAA;MACAF,OAAA,KAAAA,OAAA,CAAAG,SAAA,GAAAH,OAAA,CAAAI,YAAA;IACA;IACAC,aAAAP,KAAA;MACA,KAAAQ,QAAA;QACAC,iBAAA;QACAC,gBAAA;MACA,GAAAC,IAAA;QACA,KAAAC,KAAA,CAAAC,MAAA,2BAAAC,MAAA,CAAAC,MAAA,CAAAC,OAAA,SAAA9B,kBAAA,CAAAc,KAAA,EAAAiB,MAAA,IAAAN,IAAA;UACA,KAAAO,aAAA;UACA,KAAAvC,QAAA,CAAAwC,MAAA,MAAAxC,QAAA,CAAAmB,MAAA,GAAAE,KAAA;QACA;MACA,GAAAoB,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}