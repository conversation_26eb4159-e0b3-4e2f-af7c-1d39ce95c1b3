{"ast": null, "code": "import i18n from 'src/lang';\nimport store from 'src/store';\nexport const A4_PAGE_WIDTH = 595;\nexport const A4_PAGE_HEIGHT = 841;\nexport const ORIGIN_TEMPLATE_PIC_WIDTH = 793;\nexport const FLOAT_TYPES = ['SIGNATURE', 'SEAL', 'DATE'];\nexport const SELECT_TYPES = ['SINGLE_BOX', 'MULTIPLE_BOX'];\nexport const COLOR_TYPES = ['SIGNATURE', 'SEAL', 'DATE', 'WATERMARK', 'DECORATE_RIDING_SEAL'];\nconst SCALE_VAL = A4_PAGE_WIDTH / ORIGIN_TEMPLATE_PIC_WIDTH;\n\n/**\n * 返回标签信息的常量\n * 指定位置页、模板指定位置页、签署页均有引用\n * @param  {[String]} type [description]\n * @return {[Object]}\n */\nconst sealStyle = store.getters.getIsForeignVersion ? {\n  type: 'el-icon-ssq-gaizhang',\n  width: 133,\n  height: 133,\n  name: i18n.t('localCommon.seal')\n} : {\n  type: 'el-icon-ssq-gaizhang',\n  width: 222,\n  // 226\n  height: 203,\n  // 211\n  name: i18n.t('localCommon.seal')\n};\nconst signatureStyle =\n// store.getters.getIsForeignVersion ? {\n//     type: 'el-icon-ssq-gaizhang',\n//     width: 96,\n//     height: 96,\n//     name: i18n.t('localCommon.seal'),\n// } :\n{\n  type: 'el-icon-ssq-qianming',\n  width: 134,\n  // 140\n  height: 71,\n  // 74\n  name: i18n.t('localCommon.signature')\n};\nexport function markInfo(type) {\n  // 96dpi\n  let style = null;\n  switch (type) {\n    case 'SEAL':\n      style = sealStyle;\n      break;\n    case 'SIGNATURE':\n      style = signatureStyle;\n      break;\n    case 'DATE':\n      style = {\n        width: 134,\n        // 140\n        height: 34,\n        // 35\n        name: i18n.t('field.signDate')\n      };\n      break;\n    case 'TEXT':\n      style = {\n        width: 77,\n        // 80\n        height: 20,\n        // 20\n        name: i18n.t('field.text')\n      };\n      break;\n    case 'NUMERIC_VALUE':\n      // 数值\n      style = {\n        width: 77,\n        // 80\n        height: 20,\n        // 20\n        name: i18n.t('field.number')\n      };\n      break;\n    case 'BIZ_DATE':\n      style = {\n        width: 77,\n        // 80\n        height: 20,\n        // 20\n        name: i18n.t('field.date')\n      };\n      break;\n    case 'QR_CODE':\n      style = {\n        width: 110,\n        // 110\n        height: 127,\n        // 127\n        name: i18n.t('field.qrCode')\n      };\n      break;\n    case 'TEXT_NUMERIC':\n      style = {\n        width: 77,\n        // 110\n        height: 22,\n        // 127\n        name: i18n.t('field.number')\n      };\n      break;\n    case 'DYNAMIC_TABLE':\n      style = {\n        width: 134,\n        height: 24,\n        name: i18n.t('field.dynamicTable')\n      };\n      break;\n    case 'TERM':\n      style = {\n        width: 134,\n        height: 24,\n        name: i18n.t('field.terms')\n      };\n      break;\n    case 'MULTIPLE_BOX':\n      style = {\n        width: 46,\n        height: 76,\n        name: i18n.t('field.checkBox'),\n        button: {\n          width: 26,\n          height: 26,\n          split: 5,\n          initSplit: 10\n        }\n      };\n      break;\n    case 'SINGLE_BOX':\n      style = {\n        width: 46,\n        height: 76,\n        name: i18n.t('field.radioBox'),\n        button: {\n          width: 26,\n          height: 26,\n          split: 5,\n          initSplit: 10\n        }\n      };\n      break;\n    case 'PICTURE':\n      {\n        style = {\n          width: 65,\n          height: 26,\n          name: i18n.t('field.image')\n        };\n        break;\n      }\n    case 'CONFIRMATION_REQUEST_SEAL':\n      style = {\n        width: 46,\n        height: 76,\n        name: '询证章',\n        button: {\n          width: 222,\n          // 226\n          height: 203,\n          // 211\n          split: 5,\n          initSplit: 10\n        }\n      };\n      break;\n    default:\n      style = {\n        width: 0,\n        height: 0,\n        name: ''\n      };\n  }\n  return style;\n}\n\n/**\n * saas-513\n * 暂时仅在 tempFieldDoc.vue 中使用\n * 用于拖拽自定义标签时，自适应宽高\n * @param  {[Object]} mark [标签对象]\n * @return {[Object]}\n */\nexport function textMarkInfo(mark) {\n  if (mark.type !== 'TEXT' && mark.type !== 'DATE' && mark.type !== 'BIZ_DATE' && mark.type !== 'TEXT_NUMERIC') {\n    return {\n      width: 0,\n      height: 0,\n      name: ''\n    };\n  }\n\n  // 标签真实宽高，字体大小 * 字数\n  let realMarkWidth = mark.fontSize * (mark.name ? mark.name.length : 4);\n  // 高度暂时不知道为什么要加 6px ，只不过加上 6px 就等于原来的值了\n  const realMarkHeight = mark.fontSize + 6;\n  if (mark.type === 'DATE') {\n    // 日期标签\n    realMarkWidth = mark.fontSize * 6;\n  }\n  return {\n    // 最小宽高为 77 20\n    width: realMarkWidth > 77 ? realMarkWidth : 77,\n    // 80\n    height: realMarkHeight > 20 ? realMarkHeight : 20,\n    // 20\n    name: i18n.t('field.text')\n  };\n}\nexport function markIconInfo(type) {\n  let style = null;\n  switch (type) {\n    case 'SEAL':\n      style = {\n        type: i18n.t('lang') === 'zh' ? 'el-icon-ssq-gaizhang1' : i18n.t('lang') === 'en' ? 'el-icon-ssq-seal' : 'el-icon-ssq-icon-test1'\n      };\n      break;\n    case 'SIGNATURE':\n      style = {\n        type: {\n          zh: 'el-icon-ssq-qianzi',\n          en: 'el-icon-ssq-qianziEn',\n          ja: 'el-icon-ssq-qianziJP'\n        }[i18n.locale]\n      };\n      break;\n  }\n  return style;\n}\n\n/*\n * markInfo中的宽高是基于图片宽度793px设定的，最终转换为pdf时，是基于A4的宽高展示。\n * 当前计算的字段宽高像素值会保存在后端，用到最终的展示当中，所以这里计算出来的宽高实际上是按最终pdf的宽高做比例计算出来的。\n */\nexport function newMarkSizeInfo(type) {\n  return {\n    width: markInfo(type).width * SCALE_VAL,\n    height: markInfo(type).height * SCALE_VAL\n  };\n}", "map": {"version": 3, "names": ["i18n", "store", "A4_PAGE_WIDTH", "A4_PAGE_HEIGHT", "ORIGIN_TEMPLATE_PIC_WIDTH", "FLOAT_TYPES", "SELECT_TYPES", "COLOR_TYPES", "SCALE_VAL", "sealStyle", "getters", "getIsForeignVersion", "type", "width", "height", "name", "t", "signatureStyle", "markInfo", "style", "button", "split", "initSplit", "textMarkInfo", "mark", "realMarkWidth", "fontSize", "length", "realMarkHeight", "markIconInfo", "zh", "en", "ja", "locale", "newMarkSizeInfo"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/utils/info.js"], "sourcesContent": ["import i18n from 'src/lang';\nimport store from 'src/store';\n\nexport const A4_PAGE_WIDTH = 595;\nexport const A4_PAGE_HEIGHT = 841;\nexport const ORIGIN_TEMPLATE_PIC_WIDTH = 793;\n\nexport const FLOAT_TYPES = ['SIGNATURE', 'SEAL', 'DATE'];\nexport const SELECT_TYPES = ['SINGLE_BOX', 'MULTIPLE_BOX'];\nexport const COLOR_TYPES = ['SIGNATURE', 'SEAL', 'DATE', 'WATERMARK', 'DECORATE_RIDING_SEAL'];\n\nconst SCALE_VAL = A4_PAGE_WIDTH / ORIGIN_TEMPLATE_PIC_WIDTH;\n\n/**\n * 返回标签信息的常量\n * 指定位置页、模板指定位置页、签署页均有引用\n * @param  {[String]} type [description]\n * @return {[Object]}\n */\nconst sealStyle = store.getters.getIsForeignVersion ? {\n    type: 'el-icon-ssq-gaizhang',\n    width: 133,\n    height: 133,\n    name: i18n.t('localCommon.seal'),\n} : {\n    type: 'el-icon-ssq-gaizhang',\n    width: 222, // 226\n    height: 203, // 211\n    name: i18n.t('localCommon.seal'),\n};\nconst signatureStyle =\n// store.getters.getIsForeignVersion ? {\n//     type: 'el-icon-ssq-gaizhang',\n//     width: 96,\n//     height: 96,\n//     name: i18n.t('localCommon.seal'),\n// } :\n {\n     type: 'el-icon-ssq-qianming',\n     width: 134, // 140\n     height: 71, // 74\n     name: i18n.t('localCommon.signature'),\n };\nexport function markInfo(type) { // 96dpi\n    let style = null;\n    switch (type) {\n        case 'SEAL':\n            style = sealStyle;\n            break;\n        case 'SIGNATURE':\n            style = signatureStyle;\n            break;\n        case 'DATE':\n            style = {\n                width: 134, // 140\n                height: 34, // 35\n                name: i18n.t('field.signDate'),\n            };\n            break;\n        case 'TEXT':\n            style = {\n                width: 77, // 80\n                height: 20, // 20\n                name: i18n.t('field.text'),\n            };\n            break;\n        case 'NUMERIC_VALUE': // 数值\n            style = {\n                width: 77, // 80\n                height: 20, // 20\n                name: i18n.t('field.number'),\n            };\n            break;\n        case 'BIZ_DATE':\n            style = {\n                width: 77, // 80\n                height: 20, // 20\n                name: i18n.t('field.date'),\n            };\n            break;\n        case 'QR_CODE':\n            style = {\n                width: 110, // 110\n                height: 127, // 127\n                name: i18n.t('field.qrCode'),\n            };\n            break;\n        case 'TEXT_NUMERIC':\n            style = {\n                width: 77, // 110\n                height: 22, // 127\n                name: i18n.t('field.number'),\n            };\n            break;\n        case 'DYNAMIC_TABLE':\n            style = {\n                width: 134,\n                height: 24,\n                name: i18n.t('field.dynamicTable'),\n            };\n            break;\n        case 'TERM':\n            style = {\n                width: 134,\n                height: 24,\n                name: i18n.t('field.terms'),\n            };\n            break;\n        case 'MULTIPLE_BOX':\n            style = {\n                width: 46,\n                height: 76,\n                name: i18n.t('field.checkBox'),\n                button: {\n                    width: 26,\n                    height: 26,\n                    split: 5,\n                    initSplit: 10,\n                },\n            };\n            break;\n        case 'SINGLE_BOX':\n            style = {\n                width: 46,\n                height: 76,\n                name: i18n.t('field.radioBox'),\n                button: {\n                    width: 26,\n                    height: 26,\n                    split: 5,\n                    initSplit: 10,\n                },\n            };\n            break;\n        case 'PICTURE': {\n            style = {\n                width: 65,\n                height: 26,\n                name: i18n.t('field.image'),\n            };\n            break;\n        }\n        case 'CONFIRMATION_REQUEST_SEAL':\n            style = {\n                width: 46,\n                height: 76,\n                name: '询证章',\n                button: {\n                    width: 222, // 226\n                    height: 203, // 211\n                    split: 5,\n                    initSplit: 10,\n                },\n            };\n            break;\n        default:\n            style = {\n                width: 0,\n                height: 0,\n                name: '',\n            };\n    }\n    return style;\n}\n\n/**\n * saas-513\n * 暂时仅在 tempFieldDoc.vue 中使用\n * 用于拖拽自定义标签时，自适应宽高\n * @param  {[Object]} mark [标签对象]\n * @return {[Object]}\n */\nexport function textMarkInfo(mark) {\n    if (mark.type !== 'TEXT' && mark.type !== 'DATE' && mark.type !== 'BIZ_DATE' && mark.type !== 'TEXT_NUMERIC') {\n        return {\n            width: 0,\n            height: 0,\n            name: '',\n        };\n    }\n\n    // 标签真实宽高，字体大小 * 字数\n    let realMarkWidth = mark.fontSize * (mark.name ? mark.name.length : 4);\n    // 高度暂时不知道为什么要加 6px ，只不过加上 6px 就等于原来的值了\n    const realMarkHeight = mark.fontSize + 6;\n\n    if (mark.type === 'DATE') {\n        // 日期标签\n        realMarkWidth = mark.fontSize * 6;\n    }\n    return {\n        // 最小宽高为 77 20\n        width: (realMarkWidth > 77) ? realMarkWidth : 77, // 80\n        height: (realMarkHeight > 20) ? realMarkHeight : 20, // 20\n        name: i18n.t('field.text'),\n    };\n}\n\nexport function markIconInfo(type) {\n    let style = null;\n    switch (type) {\n        case 'SEAL':\n            style =  {\n                type: i18n.t('lang') === 'zh' ? 'el-icon-ssq-gaizhang1' : (i18n.t('lang') === 'en' ? 'el-icon-ssq-seal' : 'el-icon-ssq-icon-test1'),\n            };\n            break;\n        case 'SIGNATURE':\n            style = {\n                type: {\n                    zh: 'el-icon-ssq-qianzi',\n                    en: 'el-icon-ssq-qianziEn',\n                    ja: 'el-icon-ssq-qianziJP',\n                }[i18n.locale],\n            };\n            break;\n    }\n    return style;\n}\n\n/*\n * markInfo中的宽高是基于图片宽度793px设定的，最终转换为pdf时，是基于A4的宽高展示。\n * 当前计算的字段宽高像素值会保存在后端，用到最终的展示当中，所以这里计算出来的宽高实际上是按最终pdf的宽高做比例计算出来的。\n */\nexport function newMarkSizeInfo(type) {\n    return {\n        width: markInfo(type).width * SCALE_VAL,\n        height: markInfo(type).height * SCALE_VAL,\n    };\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,UAAU;AAC3B,OAAOC,KAAK,MAAM,WAAW;AAE7B,OAAO,MAAMC,aAAa,GAAG,GAAG;AAChC,OAAO,MAAMC,cAAc,GAAG,GAAG;AACjC,OAAO,MAAMC,yBAAyB,GAAG,GAAG;AAE5C,OAAO,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC;AACxD,OAAO,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC;AAC1D,OAAO,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,sBAAsB,CAAC;AAE7F,MAAMC,SAAS,GAAGN,aAAa,GAAGE,yBAAyB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,SAAS,GAAGR,KAAK,CAACS,OAAO,CAACC,mBAAmB,GAAG;EAClDC,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,kBAAkB;AACnC,CAAC,GAAG;EACAJ,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,GAAG;EAAE;EACZC,MAAM,EAAE,GAAG;EAAE;EACbC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,kBAAkB;AACnC,CAAC;AACD,MAAMC,cAAc;AACpB;AACA;AACA;AACA;AACA;AACA;AACC;EACIL,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,GAAG;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,uBAAuB;AACxC,CAAC;AACF,OAAO,SAASE,QAAQA,CAACN,IAAI,EAAE;EAAE;EAC7B,IAAIO,KAAK,GAAG,IAAI;EAChB,QAAQP,IAAI;IACR,KAAK,MAAM;MACPO,KAAK,GAAGV,SAAS;MACjB;IACJ,KAAK,WAAW;MACZU,KAAK,GAAGF,cAAc;MACtB;IACJ,KAAK,MAAM;MACPE,KAAK,GAAG;QACJN,KAAK,EAAE,GAAG;QAAE;QACZC,MAAM,EAAE,EAAE;QAAE;QACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,gBAAgB;MACjC,CAAC;MACD;IACJ,KAAK,MAAM;MACPG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QAAE;QACXC,MAAM,EAAE,EAAE;QAAE;QACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,YAAY;MAC7B,CAAC;MACD;IACJ,KAAK,eAAe;MAAE;MAClBG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QAAE;QACXC,MAAM,EAAE,EAAE;QAAE;QACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,cAAc;MAC/B,CAAC;MACD;IACJ,KAAK,UAAU;MACXG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QAAE;QACXC,MAAM,EAAE,EAAE;QAAE;QACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,YAAY;MAC7B,CAAC;MACD;IACJ,KAAK,SAAS;MACVG,KAAK,GAAG;QACJN,KAAK,EAAE,GAAG;QAAE;QACZC,MAAM,EAAE,GAAG;QAAE;QACbC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,cAAc;MAC/B,CAAC;MACD;IACJ,KAAK,cAAc;MACfG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QAAE;QACXC,MAAM,EAAE,EAAE;QAAE;QACZC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,cAAc;MAC/B,CAAC;MACD;IACJ,KAAK,eAAe;MAChBG,KAAK,GAAG;QACJN,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,oBAAoB;MACrC,CAAC;MACD;IACJ,KAAK,MAAM;MACPG,KAAK,GAAG;QACJN,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,aAAa;MAC9B,CAAC;MACD;IACJ,KAAK,cAAc;MACfG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,gBAAgB,CAAC;QAC9BI,MAAM,EAAE;UACJP,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVO,KAAK,EAAE,CAAC;UACRC,SAAS,EAAE;QACf;MACJ,CAAC;MACD;IACJ,KAAK,YAAY;MACbH,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,gBAAgB,CAAC;QAC9BI,MAAM,EAAE;UACJP,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVO,KAAK,EAAE,CAAC;UACRC,SAAS,EAAE;QACf;MACJ,CAAC;MACD;IACJ,KAAK,SAAS;MAAE;QACZH,KAAK,GAAG;UACJN,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,aAAa;QAC9B,CAAC;QACD;MACJ;IACA,KAAK,2BAA2B;MAC5BG,KAAK,GAAG;QACJN,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,KAAK;QACXK,MAAM,EAAE;UACJP,KAAK,EAAE,GAAG;UAAE;UACZC,MAAM,EAAE,GAAG;UAAE;UACbO,KAAK,EAAE,CAAC;UACRC,SAAS,EAAE;QACf;MACJ,CAAC;MACD;IACJ;MACIH,KAAK,GAAG;QACJN,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE;MACV,CAAC;EACT;EACA,OAAOI,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAACC,IAAI,EAAE;EAC/B,IAAIA,IAAI,CAACZ,IAAI,KAAK,MAAM,IAAIY,IAAI,CAACZ,IAAI,KAAK,MAAM,IAAIY,IAAI,CAACZ,IAAI,KAAK,UAAU,IAAIY,IAAI,CAACZ,IAAI,KAAK,cAAc,EAAE;IAC1G,OAAO;MACHC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;IACV,CAAC;EACL;;EAEA;EACA,IAAIU,aAAa,GAAGD,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACT,IAAI,GAAGS,IAAI,CAACT,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC;EACtE;EACA,MAAMC,cAAc,GAAGJ,IAAI,CAACE,QAAQ,GAAG,CAAC;EAExC,IAAIF,IAAI,CAACZ,IAAI,KAAK,MAAM,EAAE;IACtB;IACAa,aAAa,GAAGD,IAAI,CAACE,QAAQ,GAAG,CAAC;EACrC;EACA,OAAO;IACH;IACAb,KAAK,EAAGY,aAAa,GAAG,EAAE,GAAIA,aAAa,GAAG,EAAE;IAAE;IAClDX,MAAM,EAAGc,cAAc,GAAG,EAAE,GAAIA,cAAc,GAAG,EAAE;IAAE;IACrDb,IAAI,EAAEf,IAAI,CAACgB,CAAC,CAAC,YAAY;EAC7B,CAAC;AACL;AAEA,OAAO,SAASa,YAAYA,CAACjB,IAAI,EAAE;EAC/B,IAAIO,KAAK,GAAG,IAAI;EAChB,QAAQP,IAAI;IACR,KAAK,MAAM;MACPO,KAAK,GAAI;QACLP,IAAI,EAAEZ,IAAI,CAACgB,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,uBAAuB,GAAIhB,IAAI,CAACgB,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,kBAAkB,GAAG;MAC9G,CAAC;MACD;IACJ,KAAK,WAAW;MACZG,KAAK,GAAG;QACJP,IAAI,EAAE;UACFkB,EAAE,EAAE,oBAAoB;UACxBC,EAAE,EAAE,sBAAsB;UAC1BC,EAAE,EAAE;QACR,CAAC,CAAChC,IAAI,CAACiC,MAAM;MACjB,CAAC;MACD;EACR;EACA,OAAOd,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASe,eAAeA,CAACtB,IAAI,EAAE;EAClC,OAAO;IACHC,KAAK,EAAEK,QAAQ,CAACN,IAAI,CAAC,CAACC,KAAK,GAAGL,SAAS;IACvCM,MAAM,EAAEI,QAAQ,CAACN,IAAI,CAAC,CAACE,MAAM,GAAGN;EACpC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}