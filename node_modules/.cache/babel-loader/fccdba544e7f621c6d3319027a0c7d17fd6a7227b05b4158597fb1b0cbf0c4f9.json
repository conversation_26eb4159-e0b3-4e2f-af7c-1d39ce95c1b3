{"ast": null, "code": "export default {\n  sign: {\n    sealLabelsTip: '契約書に{sealLabelslen}つの押印が必要です。{personStr}が{otherSealLen}つの押印を行い、残りの{mySealLen}つはあなたが押印します。印章はページ上に表示されています。続行するかどうかご確認ください。',\n    continue: '続行',\n    nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n    reselect: '再選',\n    approvalFeatures: {\n      dialogTitle: '新機能紹介',\n      understand: 'わかりました',\n      feature1: '文の強調注釈',\n      feature2: 'フィールドのハイライト',\n      tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',\n      tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',\n      tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。',\n      tip4: 'テキストフィールドを選択後、注釈ボタンをクリックして注釈テキストを追加できます。完了したら、修正または削除をクリックします。注釈の内容は、契約詳細ページ - 会社内部操作ログで確認できます。-公司内部操作日志中查看。',\n      tip5: 'ステップ1：注釈を追加するテキストフィールドを選択します。',\n      tip6: 'ステップ2：注釈を編集または削除します。',\n      annotate: '注釈',\n      delete: '削除',\n      edit: '修正',\n      operateTitle: '承認注釈の追加',\n      placeholder: '255語以内'\n    },\n    contractHighLight: {\n      dialogTitle: '契約書ハイライト表示',\n      understand: 'わかりました',\n      tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',\n      tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',\n      tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。'\n    },\n    needRemark: '備考を記入する必要があります',\n    notNeedRemark: '備考を記入しなくても問題ありません',\n    switchToReceiver: 'お客様は{receiver}に切り替えています',\n    notAddEntTip: '現在のユーザはこの企業のメンバーではありません、プライマリ管理者に連絡して企業に加入してください。',\n    contractPartiesYouChoose: '選択できる契約主体は',\n    contractPartyFilled: '発信者の記入した契約主体は',\n    certifyOtherCompanies: 'その他企業の認証',\n    youCanAlso: 'することもできます。',\n    needVerification: '署名するには実名認証した後でなければなりません',\n    prompt: '注意',\n    submit: '確定',\n    cancel: 'キャンセル',\n    sign: 'すぐに契約',\n    addSeal: 'パソコンを使用してベストサインのオフィシャルページにログインして印章を追加してください。',\n    noSealAvailable: '申し訳ありません。現在使用可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',\n    memberNoSealAvailable: '現在使用可能な印章がありません。管理者に連絡して構成した後再署名してください。もしくはオフラインにて管理者主任に連絡して構成してください。',\n    noticeAdminFoSeal: '管理者主任に通知する',\n    requestSomeone: '他人の認証を要求する',\n    requestOthersToContinue: '管理者主任に通知して実名認証を追加します',\n    requestOthersToContinueSucceed: '管理者に通知を送信しました',\n    requestSomeoneList: '以下のスタッフに実名認証を要求する',\n    electronicSeal: '電子公印',\n    changeTheSeal: 'この印章を使いたくありません。実名認証後印章を変更できます',\n    goToVerify: '実名認証を行う',\n    noSealToChoose: '切替可能な印章がありません。管理印章が必要であれば先に実名認証を行ってください',\n    goVerify: '認証を行う',\n    goToVerifyEnt: '企業認証を行う',\n    digitalCertificateTip: 'ベストサインがお客様のデジタル証明書をコールしています',\n    signDes: 'お客様の契約環境は安全です。安心して署名してください。',\n    signAgain: '署名を続ける',\n    send: '送信',\n    person: '個人',\n    ent: '企業',\n    entName: '企業名',\n    account: 'アカウント',\n    accountPH: '携帯電話またはメールアドレス',\n    approved: '審査',\n    signVerification: '署名',\n    cannotReview: '契約書が確認できません',\n    connectFail: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません.',\n    connectFailTip: '您可以尝试以下方法解决问题：',\n    connectFailTip1: '1、刷新页面。',\n    connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n    connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n    personalMaterials: '発信者は更に認証資料の追加を要求しています',\n    noSupportface: '契約発起側がお客様の顔認証署名を要求しています。中国大陸者以外は顔認証署名をサポートしていませんので、発起側に署名要求を修正するよう連絡してください。',\n    lackEntName: '企業名を入力してください',\n    errAccount: '正確なメールアドレスもしくは携帯番号を記入してください',\n    noticeAdmin: '参加の申請',\n    signDone: '署名完了',\n    signDoneTip: 'すでに契約書に署名しています',\n    approveDone: '審査完了',\n    approveDoneTip: 'すでに契約書を審査しています',\n    completeSign: 'まず先に「捺印箇所」もしくは「署名箇所」をタップして署名を完了してください',\n    fillFirst: 'まず先に入力ボックスに契約内容を記入してください',\n    stillSignTip: 'この{alias}に署名した後、{alias}の内容を変更する可能性のある他の署名者がいますが、署名を続けますか？',\n    signHighLightTip: '追加または変更が可能な{alias}内容は計{count}箇所',\n    riskDetails: 'リスク詳細',\n    noviewDifference: '他の署名者は依然として発信者が指定した{alias}の内容を変更することができるため、ベストサインでは現在の契約書と有効なバージョンの違いの内容に審査を行わず、デフォルトで有効なバージョンに署名することを認め、同意するものとします。',\n    highLightTip: 'こうしたリスクのある内容は「ハイライト表示」されます、詳細に確認してください。ページを更新するとハイライト表示を取り消せます。',\n    commonTip: '注意',\n    understand: 'わかりました',\n    view: '表示',\n    start: '開始',\n    nextStep: '次へ',\n    help: 'ヘルプ',\n    faceFailed: '申し訳ありません。顔認証に失敗しました',\n    dualFailed: '録音・録画認証に失敗しました。お客様の情報をご確認の上、再度お試しください。',\n    faceFailedtips: '注意',\n    verifyTry: 'ID情報を確認した後お試しください',\n    faceLimit: '今日の顔認証回数が上限に達しています',\n    upSignReq: '今日の顔認証回数が上限に達しています。明日再度行うか契約送信元に署名要求の修正を依頼してください。',\n    reqFace: '送信元がお客様に顔認証の検証を要請しています',\n    signAfterFace: '顔認証通過後すぐに契約書の署名が完了します',\n    qrcodeInvalid: '二次元コード情報が失効しています。リロードしてください',\n    faceFirstExceed: '顔スキャン失敗、続いて認証コードを使用して認証を行います',\n    date: '日付',\n    chooseSeal: '印章の選択',\n    seal: '印章',\n    signature: '署名',\n    handwrite: '手書き',\n    mysign: 'マイサイン',\n    approvePlace: '承認メッセージ（任意）',\n    approvePlace_1: '承認メッセージ',\n    approvePlace_2: '任意、255文字以下',\n    approveAgree: '審査結果：同意',\n    approveReject: '審査結果：却下',\n    signBy: 'より',\n    signByEnd: '捺印',\n    sealBy: 'より',\n    sealByEnd: '署名',\n    coverBy: '捺印必要',\n    applicant: '申請者',\n    continueVeri: '認証を続ける',\n    registerAndReal: '実名を新規登録してください',\n    goToResiter: '新規登録及び認証をしてください',\n    sureToUse: '使用の確定',\n    toSign: '契約しますか？',\n    pleaseComplete: 'まずは完了してください',\n    confirmSign: '署名の再確認',\n    admin: '管理者',\n    contratAdmin: '管理者にお客様のアカウントを連絡してください',\n    addToEnt: '企業メンバーとして追加',\n    alreadyExists: 'ベストサインに存在しています',\n    sendMsg: 'ベストサインはSMS形式で管理者に下記の内容を送信します：',\n    applyJoin: '参加の申請',\n    title: '表題',\n    viewImg: '画像の確認',\n    priLetter: 'メッセージ',\n    priLetterFromSomeone: '{name}からのメッセージ',\n    readLetter: 'わかりました。',\n    approve: '同意',\n    disapprove: '却下',\n    refuseSign: '拒否',\n    paperSign: '紙媒体の署名に変更',\n    refuseTip: '拒否理由を選択してください',\n    refuseReason: '拒否理由を記入することで相手方にお客様の問題を理解する手助けになり、契約手続きを早くします',\n    reasonWriteTip: '拒否理由を記入してください',\n    refuseReasonOther: '拒否理由の詳細(オプション) | 拒否理由の詳細(必須)',\n    refuseConfirm: '拒否',\n    refuseConfirmTip: 'あなたは「{reason}」という理由でこの契約書への署名を拒否します。続行したら、この契約書に署名できなくなります。続行しますか？',\n    waitAndThink: 'キャンセル',\n    signValidationTitle: '署名検証',\n    email: 'メールアドレス',\n    phoneNumber: '携帯電話',\n    password: 'パスワード',\n    verificationCode: '認証コード',\n    mailVerificationCode: '認証コード',\n    forgetPsw: 'パスワードをお忘れの場合',\n    if: '、どうか',\n    forgetPassword: 'パスワードをお忘れの場合',\n    rejectionVer: '検証の拒否',\n    msgTip: 'SMSを受け取っていない場合、',\n    voiceVerCode: '音声認証コード',\n    SMSVerCode: 'SMS認証コード',\n    or: 'または',\n    tryMore: 'を試してください',\n    emailVerCode: '電子メール認証コード',\n    SentSuccessfully: '送信完了',\n    intervalTip: '送信時間間隔が短すぎます',\n    signPsw: '署名パスワード',\n    useSignPsw: '契約パスワードで認証',\n    setSignPsw: '署名パスワードの設定',\n    useVerCode: '認証コードで認証',\n    inputVerifyCodeTip: '認証コードを入力してください',\n    inputSignPwdTip: '契約パスワードを入力してください',\n    signConfirmTip: {\n      1: '本当にこの契約書に署名しますか？',\n      2: '確定ボタンをタップしてすぐにこの契約書に署名する',\n      confirm: '署名の確認'\n    },\n    signSuc: '署名成功',\n    refuseSuc: '拒否成功',\n    approveSuc: '承認完了',\n    hdFile: '高解像度ファイルの確認',\n    otherOperations: 'その他の操作',\n    reviewDetails: '審査詳細',\n    close: '閉じる',\n    submitter: '提出者',\n    signatory: '署名者',\n    reviewSchedule: '審査進度',\n    signByPc: '{name}による署名',\n    signPageDescription: '第{index}ページ、計{total}ページ',\n    sealBySomeone: '{name}による捺印',\n    signDate: '署名日時',\n    download: 'ダウンロード',\n    signPage: 'ページ数：{page}ページ',\n    signNow: 'すぐ署名',\n    sender: '発信者',\n    signer: '契約者',\n    startSignTime: '契約発信日時',\n    signDeadLine: '契約期限',\n    authGuide: {\n      goToHome: 'トップページに戻る',\n      tip_1: '認証の完了後、契約書を閲覧し、署名ができるようになります。',\n      tip_2: '身分を使用して | 認証を行ってください。',\n      tip_3: '契約書の送付',\n      tip_4: '契約書の送付者に | 受け取り者を変更するように連絡してください。',\n      tip_5: 'お客様の認証した | 契約書が確認できません',\n      new_tip_1: '発信者のコンプライアンス要件に基づき、下記の手順を完了する必要があります：',\n      new_tip_2: '発信者のコンプライアンス要件に基づき、必要な書類は：',\n      new_tip_3: '下記の手順を完了してください。',\n      new_tip_4: '印章権限をお持ちであれば、自動で第2ステップにジャンプします',\n      entUserName: '氏名：',\n      idNumberForVerify: '身分証明書：',\n      realNameAuth: '実名認証',\n      applySeal: '印章の申請',\n      signContract: '契約書の署名'\n    },\n    switch: '切り替え',\n    rejectReasonList: {\n      // authReason: '実名認証をしたくありません/できません',\n      signOperateReason: '署名操作/検証操作に対して疑問をお持ちであれば、更に交流を進める必要があります',\n      termReason: '契約条件/内容に異議があれば、更に交流を進める必要があります',\n      explainReason: '契約内容に対してわからないことがあれば、事前に告知してください',\n      otherReason: 'その他（理由を記載してください）'\n    },\n    selectSignature: '署名の選択',\n    selectSigner: '署名者の選択',\n    pleaseScanToSign: 'AlipayもしくはWechatスキャンを使用して署名してください',\n    pleaseScanAliPay: 'AlipayアプリでQRコードを読み取り、署名してください',\n    pleaseScanWechat: 'WeChatアプリでQRコードを読み取り、署名してください',\n    requiredFaceSign: '契約書送信元がお客様に顔認証署名を要請しています',\n    requiredDualSign: '契約送信者が録音・録画認証を要求しています。',\n    verCodeVerify: '認証コードによる検証',\n    applyToSign: '契約書の署名申請',\n    autoRemindAfterApproval: '*審査に合格後、署名者に自動的にリマインダーを送信',\n    cannotSignBeforeApproval: '審査がまだ終わっていないため、署名できません。',\n    finishSignatureBeforeSign: '先に捺印/サインをしてから再度署名を確認してください',\n    uploadFileOnRightSite: 'アップロードしていない添付書類がある場合、まずは右側の欄で添付書類をアップロードしてください',\n    cannotApplySealNeedPay: '当該契約書はお客様が支払うものですので、他の人の捺印での申請を認めていません',\n    cannotOtherSealReason: 'この契約は顔認証による本人確認が必要なため、代理での押印はできません',\n    unlimitedNotice: 'この契約書の費用は使い放題です',\n    units: '{num}部',\n    contractToPrivate: '個人向け契約書',\n    contractToPublic: '企業向け契約書',\n    paySum: '共{sum}需要您支付',\n    payTotal: '共计{total}元.',\n    fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',\n    contactToRecharge: '管理者主任に連絡してチャージしてください.',\n    deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます。',\n    needSignerPay: '契約送信者により着払い設定がされており、あなたは契約書送信料金の支払者に指定されています。',\n    recharge: 'リチャージ',\n    toSubmit: '提出',\n    appliedSeal: '印章使用申請書を提出しています',\n    noSeal: '印章がありません',\n    noSwitchSealNeedDistribute: '切り替え可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',\n    viewApproveProcess: '承認フローを確認',\n    approveProcess: '承認フロー',\n    noApproveContent: '承認書類が提出されていません',\n    knew: 'わかりました',\n    noSwitchSealNeedAppend: '現在切り替え可能な印章がありません。管理者主任に連絡して印章を追加してください。',\n    hadAutoSet: '別の{num}箇所で自動的に',\n    setThatSignature: '当該署名を置きます',\n    setThatSeal: '当該印章を置きます',\n    applyThatSeal: '当該印章を申請',\n    hasSetTip: '他の{index}箇所に自動配置',\n    hasSetSealTip: 'この印鑑は他の{index}箇所に自動的に置かれている',\n    hasSetSignatureTip: '署名は他の{index}か所に自動的に配置されました',\n    hasApplyForSealTip: 'この印鑑は他の{index}か所で自動申請されています',\n    savedOnLeftSite: '左側の署名欄に保存されました',\n    ridingSealMinLimit: '書類は1ページのみのため、割り印が押せません',\n    ridingSealMaxLimit: '146ページを超えているため、割り印をサポートしていません',\n    ridingSealMinOrMaxLimit: '書類は1ページのみか146ページを超えているため、割り印が押せません',\n    noSealForRiding: '使用可能な印章が登録されていないため、割り印を押せません',\n    noSwitchSealNeedAppendBySelf: '切り替え可能な印章が登録されていません。企業の管理コンソールで印章を追加してください。',\n    gotoAppendSeal: '印章を追加しにいく',\n    approvalFlowSuccessfulSet: '承認フロー設定完了',\n    mandate: '授権の同意',\n    loginToAppendSeal: 'パソコンを使用してベストサインにログインして、企業の管理コンソールで印章を追加してください',\n    signIdentityAs: '現在{person}の名義で契約書に署名しています',\n    enterNextContract: '次の契約書に入る',\n    fileList: 'ファイルリスト',\n    addSignerFile: '付属資料の追加',\n    signatureFinish: 'すでに全部捺印/署名しています',\n    dragSignatureTip: '下記の捺印/日時をファイルにドラッグ＆ドロップしてください。複数回可能です',\n    noticeToManager: '管理者に通知しました',\n    gotoAuthPerson: '個人認証を行う',\n    senderRequire: '発信者がお客様に要求しています',\n    senderRequireUseFollowIdentity: '発信者がお客様に下記の身分証明の一つを要求しています',\n    suggestToAuth: 'お客様は実名認証を行っていません。実名認証をした後署名するようにしてください',\n    contactEntAdmin: '企業の管理者主任に連絡してください',\n    setYourAccount: 'お客様のアカウントで',\n    authInfoUnMatchNeedResend: '契約書の署名を行ってください。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください',\n    noEntNameNeedResend: '契約企業名が指定されておらず、この契約書を署名することはできません。送信者に連絡して再度契約書の起稿を要求してください',\n    pleaseUse: '使用してください',\n    me: '私',\n    myself: '本人',\n    reAuthBtnTip: '私が現在の携帯番号の実質使用者です。',\n    reAuthBtnContent: '再実名登録後、このアカウントの元の実名での利用は却下されますので、ご確認ください。',\n    descNoSame1: ' の身分で契約書を署名',\n    descNoSame2: 'これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n    authInfoNoSame: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n    authInfoNoSame2: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n    goHome: '契約書リストページに戻る>>',\n    authInfo: '現在検出しているアカウントの実名IDは ',\n    authInfo2: '現在検出しているアカウントの実名IDは ',\n    in: 'に',\n    finishAuth: '実名が完了しています。コンプライアンスに則した契約書の署名に用いられます',\n    ask: '現在のアカウントで署名を続けますか？',\n    reAuthBtnText: 'はい。このアカウントで再度実名署名を行います',\n    changePhoneText: 'いいえ。発送者に連絡して署名の携帯番号を変更します',\n    changePhoneTip1: '発信者の要求で、連絡してください',\n    changePhoneTip2: '署名情報（携帯番号/氏名）を変更し、お客様による署名が指示されています。',\n    confirmOk: '確認',\n    goOnAuth: {\n      0: '認証の実行',\n      1: '実名認証を実行してください',\n      2: '実名認証を実行します'\n    },\n    signContractAfterAuth: {\n      0: '認証の完了後、契約書の署名ができるようになります。',\n      1: '認証が完了した後契約書に署名ができるようになります。'\n    },\n    useIdentity: '{name}の身分で',\n    inTheName: 'XX',\n    of: 'の',\n    identity: '身分',\n    nameIs: '氏名は',\n    IDNumIs: '身分証明番号は',\n    provideMoreAuthData: '更に認証資料を追加する',\n    leadToAuthBeforeSign: '認証が続けた後契約書に署名ができるようになります',\n    groupProxyAuthNeedMore: '現在の認証状態はグループが代理認証しています。もしも単独で契約書に署名する必要があれば実名認証資料を追加してください',\n    contactSender: '疑問がある場合発行者に連絡してください。',\n    note: '注意：',\n    identityInfo: 'ID情報',\n    signNeedCoincidenceInfo: '完全一致しないと契約書の署名はできません。',\n    needAuthPermissionContactAdmin: '実名認証権限がありません。管理者に連絡してください',\n    iHadReadContract: '閲読し、{alias}内容を熟知しました',\n    scrollToBottomTip: '最後のページまでスクロールする必要があります',\n    getVerCodeFirst: '先に認証コードを取得してください',\n    appScanVerify: 'ベストサインアプリの二次元コード検証',\n    downloadBSApp: 'ベストサインアプリをダウンロード',\n    scanned: '二次元コード成功',\n    confirmInBSApp: 'ベストサインアプリの中で署名を確認してください',\n    qrCodeExpired: '二次元コードが失効しています。リロードしてください',\n    appKey: 'アプリセキュリティ検証',\n    goToScan: '読み取る',\n    setNotificationInUserCenter: 'ユーザーセンターで通知方法を設定してください',\n    doNotWantUseVerCode: '認証コードを使いたくありません',\n    try: '試す',\n    retry: '再接続',\n    goToFaceVerify: '顔認証に入る',\n    faceExceedTimes: '当日の顔認証回数が上限に達しています',\n    returnBack: '戻る',\n    switchTo: '切り替え',\n    youCanChooseIdentityBlow: '以下の契約主体を選択することができます',\n    needDrawSignatureFirst: 'まだサインしていません。先に手書きサインを追加してください',\n    lacksSealNeedAppend: 'まだ印章を何も追加していません。先に印章を追加してください。',\n    manageSeal: '印章の管理',\n    needDistributeSealToSelf: '現在使用可能な印章がありません。先に自分が印章の所有者になるよう設定してください',\n    chooseSealAfterAuth: '上の印章を使いたくありません。実名認証後印章を変更できます',\n    appendDrawSignature: '手書きサインの追加',\n    senderUnFill: '（発信者が記入されていません)',\n    declare: '説明',\n    fileLessThan: '{num}M以下のファイルをアップロードしてください',\n    fileNeedUploadImg: 'アップロードする際はサポートしているフォーマットを使ってください',\n    serverError: 'サーバーエラーが発生しています。しばらくしてから試してください',\n    oldFormatTip: 'jpg、png、jpeg、pdf、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下としてください',\n    fileLimitFormatAndSize: '1つの資料画像は10枚を超えないようにしてください。',\n    fileFormatImage: 'jpg、png、jpegフォーマットをサポートし、1画像当たりの容量は20M以下とし、10枚までアップロードできます',\n    fileFormatFile: 'pdf、excel、word、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下とします',\n    signNeedKnow: '契約注意事項',\n    signNeedKnowFrom: '{sender}からの契約注意事項',\n    approvalInfo: '審査注意事項',\n    approveNeedKnowFrom: '{sender}-{sendEmployeeName}により提出された審査資料',\n    approveBeforeSend: '合同发送前审批',\n    approveBeforeSign: '合同签署前审批',\n    approveOperator: '承認者',\n    approvalOpinion: '承認メッセージ',\n    employeeDefault: '従業員',\n    setLabel: '設定タグ',\n    addRidingSeal: '割り印の追加',\n    delRidingSeal: '割り印の削除',\n    file: 'ファイル',\n    compressedFile: '圧縮ファイル',\n    attachmentContent: '付属内容',\n    pleaseClickView: '（クリックしてダウンロードしてください）',\n    downloadFile: 'ソースファイルをダウンロード',\n    noLabelPleaseAppend: 'タグがありません。企業の管理コンソールで追加してください。',\n    archiveTo: 'ファイリング先',\n    hadArchivedToFolder: '{who}の{folderName}フォルダーへの契約書の移動が完了しました',\n    pleaseScanToHandleWrite: 'WechatまたはスマホのQRコードリーダーで読み取り',\n    save: '保存',\n    remind: 'リマインダー',\n    riskTip: 'リスクに関するリマインダー',\n    chooseApplyPerson: '押印者の設定',\n    useSealByOther: '押印を委任',\n    getSeal: '押印権限を申請',\n    nowApplySealList: '以下の印章を要求しています',\n    nowAdminSealList: '以下の印章取得を申請中です',\n    chooseApplyPersonToDeal: '押印者を選択して下さい（この契約を引き続き閲覧し、フォローすることができます）。',\n    chooseAdminSign: '印章管理者の設定\t',\n    chooseTransferPerson: '他の人に署名を渡す',\n    chooseApplyPersonToMandate: '印章管理者を選択してください。この管理者による審査が完了したら、この印章を契約書に押印できるようになります。',\n    contactGroupAdminToDistributeSeal: '集団の管理者に印章を分配するよう連絡してください',\n    sealApplySentPleaseWait: '印章の分配申請を送信しました。審査が通るまでしばらくお待ちください。もしくはその他捺印の方法を選択することができます',\n    successfulSent: '送信完了',\n    authTip: {\n      t2: ['注意：', '完全一致しないと契約書の署名はできません。', '企業名', ' ID情報', 'が完全一致しないと契約書の署名はできません。'],\n      t3: '{x}はお客様に{text}の実名認証を実行するよう要求しています',\n      tCommon1: '{entName}の身分で',\n      tCommon2_1: '名前を{name}、身分証番号を{idCard}とします。',\n      tCommon2_2: '名前を{name}とします',\n      tCommon2_3: '身分証番号を{idCard}とします。',\n      viewAndSign1: '認証が完了した後契約書の閲覧及び署名ができるようになります。',\n      viewAndSignConflict: '{x}はお客様の{text}で契約書の確認と署名を行うよう要求しています。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください'\n    },\n    needSomeoneToSignature: '{x}により{y}が捺印されました',\n    needToSet: '捺印必要',\n    approver: '申請者：',\n    clickToSignature: 'ここをタップして署名',\n    transferToOtherToSign: '他の人に転送して署名',\n    signatureBy: '{x}による署名',\n    tipRightNumber: '正確な数字を入力してください',\n    tipRightIdCard: '18桁の中国本土居民身分証番号を正しく入力してください',\n    tipRightPhoneNumber: '11桁の携帯電話番号を正しく入力してください',\n    tip: '提示',\n    tipRequired: '値は必ず入力し空欄にできません',\n    confirm: '確定',\n    viewContractDetail: '契約内容の確認',\n    required: '必須項目',\n    optional: 'オプション',\n    decimalLimit: '小数点以下{x}桁まで',\n    intLimit: '整数の要求',\n    invalidContract: 'この契約ご同意すると以下の契約を無効にし：',\n    No: 'ナンバリング',\n    chooseFrom2: '差出人は2種類のスタンプを設定しておりますので、1種類お選びください',\n    crossPlatformCofirm: {\n      message: 'この契約書はクロスプラットフォーム署名が必要です。契約書データを国外に送信することに同意しますか',\n      title: 'データ承認',\n      confirmButtonText: '同意する',\n      cancelButtonText: 'キャンセル'\n    },\n    sealScope: '印章使用範囲',\n    currentContract: '現在の契約書',\n    allContract: 'すべての契約書',\n    docView: '契約プレビュー',\n    fixTextDisplay: 'ページの文字化けを修正',\n    allPage: '合計{num}ページ',\n    notJoinTip: '管理者に連絡して、企業のメンバーとして追加してもらってから署名してください'\n  },\n  signJa: {\n    beforeSignTip1: '送信者のリクエストにより、この企業名義で署名してください：',\n    beforeSignTip2: '送信者は{signer}を署名者に指定しています。情報が正しいことを確認したら署名できます。',\n    beforeSignTip3: '情報が正しくなければ、送信者に連絡して、指定署名者の情報を変更してください。',\n    beforeSignTip4: 'このアカウントで登録されている氏名は{currentUser}であり、送信者がリクエストした {signer} と異なります、 {signer} に変更しますか？',\n    beforeSignTip5: '現在のアカウントに紐付けされた氏名は{currentUser}であり、甲の指定する{signer}と一致しません',\n    beforeSignTip6: '実際の状況に基づき、甲の指定する {signer} に変更することを確認してください',\n    beforeSignTip7: 'または甲と相談の上、指定署名者を変更します',\n    entNamePlaceholder: '企業名を入力してください',\n    corporateNumberPlaceholder: '法人番号を入力してください',\n    corporateNumber: '法人番号',\n    singerNamePlaceholder: '署名者の氏名を入力してください',\n    singerName: '署名者氏名',\n    itsMe: 'は私本人です',\n    businessPic: '印鑑証明書',\n    waitApprove: 'レビュー中。レビューの進行状況を理解する必要がある場合は、メールでお問い合わせください：<EMAIL>',\n    wrongInformation: '情報が正しくありません',\n    confirmChange: '変更する',\n    communicateSender1: '変更せず、甲と相談する',\n    communicateSender2: 'キャンセルし、送信者と相談する',\n    createSeal: {\n      title: '氏名を入力',\n      tip: 'あなたの氏名を入力してください（スペースは改行で行えます）',\n      emptyErr: '氏名を入力してください'\n    },\n    areaRegister: '企業登録地',\n    jp: '日本',\n    cn: '中国大陸',\n    are: 'アラブ首長国連邦',\n    other: 'その他',\n    plsSelect: '選択してください',\n    tip1: '登録先は中国大陸部の企業で、ent.bestsign.cnで実名登録を完了する必要があります。中国大陸部以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',\n    tip2: '企業が中国大陸版に署名して実名認証を完了している場合は、ent.bestsign.cnに直接ログインし、関連サービスを簡単に利用できます。海外版に署名したデータは、中国大陸版とは完全に独立していることに注意してください。',\n    tip3: '現地の商業規制当局から取得した証明書番号を提供してください',\n    tip4: '次の手順に従います',\n    tip5: '1. 御社の担当カスタマーマネージャーに連絡し、企業実名認証の完了までご案内させていただきます。',\n    tip6: '「チャージ管理」をクリックします。',\n    tip7: '2. 御社とベストサインとの契約書のキャプチャ、または、カスタマーマネージャーとやり取りしたメールのキャプチャをアップロードしてください。',\n    tip8: '少なくとも1つの契約を購入し、購入履歴のスクリーンショットを保存します。',\n    tip9: '3. この方式は、日本および中国本土以外の企業のみ、ご利用いただけます。',\n    tip10: '4. 資料提出後、ベストサインは3営業日以内に認証を完了します。',\n    tip11: '重要なヒント',\n    tip12: '購入者はエンタープライズユーザーでなければなりません。',\n    tip13: '支払口座の企業フルネームは、記入した企業名と完全に一致している必要があります。',\n    tip14: '日本、中国大陸部以外の企業でなければ使用できません。',\n    comNum: '企業証明書番号',\n    buyRecord: '証明書資料',\n    selectArea: '企業登録先を選択してください',\n    uaeTip1: '登録先はアラブ首長国連邦の企業で、uae.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',\n    uaeTip2: '企業がアラブ首長国連邦版に署名して実名認証を完了している場合は、uae.bestsign.comに直接ログインして、関連サービスを簡単に利用できます。注意する必要があるのは、海外版に署名したデータは、アラブ首長国連邦版とは完全に独立しています。',\n    uaeTip3: '登録先はアラブ首長国連邦と中国大陸以外の企業で、ent.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。'\n  },\n  signPC: {\n    commonSign: '署名の確認',\n    contractVerification: '署名認証',\n    VerCodeVerify: '認証コードによる検証',\n    QrCodeVerify: '二次元コード検証',\n    verifyTip: 'ベストサインでは現在お客様の安全なデジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',\n    verifyAllTip: 'ベストサインでは企業デジタル証書とお客様の個人デジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',\n    selectSeal: '印章の選択',\n    adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n    toAddSealWithConsole: '電子公印は使用開始待ちです。他の印鑑を追加するには、コンソールへ移動してください。',\n    use: '使用',\n    toAddSeal: '印章を追加しにいく',\n    mySeal: '私の印章',\n    operationCompleted: '操作完了',\n    FDASign: {\n      date: '署名時間',\n      signerAdd: '追加',\n      signerEdit: '変更',\n      editTip: '注意：中国語氏名はピンイン入力してください。例：San Zhang（張三）',\n      inputNameTip: 'あなたの氏名を入力してください',\n      inputName: '英語または中国語ピンインを入力してください',\n      signerNameFillTip: '氏名のサインも記入する必要があります',\n      plsInput: '入力してください',\n      plsSelect: '選択してください',\n      customInput: '自由入力'\n    },\n    signPlaceBySigner: {\n      signGuide: '签署指导',\n      howDragSeal: '如何拖章',\n      howDragSignature: '如何拖签名',\n      iKnow: '我知道了',\n      step: {\n        one: '第一步：阅读合同',\n        two1: '第二步：点击“拖章”',\n        two2: '第二步：点击“拖签名”',\n        three: '第三步：点击“签署”按钮'\n      },\n      dragSeal: '拖章',\n      continueDragSeal: '继续拖章',\n      dragSignature: '拖签名',\n      continueDragSignature: '继续拖签名',\n      dragPlace: '按住此处拖动',\n      notRemind: '不再提醒',\n      signTip: {\n        one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',\n        two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。'\n      },\n      finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署'\n    },\n    continueOperation: {\n      success: '操作成功',\n      exitApproval: '承認を終了',\n      continueApproval: '承認を継続',\n      next: '次へ ',\n      none: 'ないです',\n      tip: '注意',\n      approvalProcess: '{totalNum}人の承認が必要で、そのうちの{passNum}人が承認済みです',\n      receiver: '受信先：'\n    }\n  },\n  signTip: {\n    contractDetail: '契約状態',\n    downloadBtn: 'アプリのダウンロード',\n    tips: '注意',\n    submit: '確定',\n    SigningCompleted: '署名成功',\n    submitCompleted: '他の人の処理待ち',\n    noTurnSign: '署名の順番が来ていないか、署名権限がないか、ログインステータスが期限切れです',\n    noRightSign: '契約書は署名中です、現在のユーザは署名操作が許可されていません',\n    noNeedSign: '内部決議契約書、署名は不要',\n    ApprovalCompleted: '承認完了',\n    contractRevoked: '当該{alias}は取り下げられました',\n    contractRefused: '当該{alias}は署名拒否されました',\n    linkExpired: '当該リンクは無効です',\n    contractClosed: '当該{alias}は契約期限切れです',\n    approvalReject: '当該{alias}の審査は却下されました',\n    approving: '{alias}は審査中です',\n    viewContract: '{alias}を確認',\n    viewContractList: '契約リストを見る',\n    needMeSign: '（署名待ち{num}件）',\n    downloadContract: '契約書のダウンロード',\n    sign: '署名',\n    signed: '署名',\n    approved: '審査',\n    approval: '審査',\n    person: '人',\n    personHas: '済み',\n    personHave: '済み',\n    personHasnot: '未',\n    personsHavenot: '未',\n    headsTaskDone: '{num}{done}{has}',\n    headsTaskNotDone: '{num}{not}{done}',\n    taskStatusBetween: '、',\n    cannotReview: '契約書が確認できません',\n    cannotDownload: '当該契約書は携帯電話でのダウンロードをサポートしていません。契約書が発信側の個人契約ストレージを使用しているため、ベストサインでは契約書を取得できません。',\n    privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません',\n    beenDeleted: 'お客様のアカウントはすでに企業管理者から削除されています',\n    unActive: 'アカウントの有効化を継続できません',\n    back: '戻る',\n    contratStatusDes: '{key}状況：',\n    contractConditionDes: '詳細：',\n    contractIng: '{alias}{key}中',\n    contractComplete: '{alias}{key}完了',\n    dataProduct: {\n      tip1: '{entName}各優良販売業者様/サプライヤー企業担当者様：',\n      tip2: 'この度、｛entName｝の安定した発展への貢献に感謝し、｛bankName｝と共同で、お客様のビジネスの発展を加速させるサプライチェーン金融サービスを開始することになりました。',\n      btnText: '社長にこの嬉しい知らせを伝えに行く'\n    },\n    signOnGoing: '署名{status}中',\n    operate: '契約操作',\n    freeContract: '初回の契約書送信を完了すると、更に無料の契約書部数を取得できます。',\n    sendContract: '契約書を送る',\n    congratulations: '{name}が{num}件の契約締結を完了したことをお祝いします，',\n    carbonSaving: '推定で{num}gのカーボン削減が達成されました.',\n    signGift: 'ベストサインより、{limit}まで利用可能な法人契約を{num}件贈呈いたします。',\n    followPublic: 'WeChat公式アカウントをフォローし、契約の最新情報を迅速に受け取ってください。',\n    congratulationsSingle: '{name}様、署名完了しました。',\n    carbonSavingSingle: '凡そ2002.4gの二酸化炭素排出量が削減されました。',\n    viewContractTip: '捺印者を変更する場合は、「詳細の確認」ボタンをクリックして契約詳細ページを開き、その後「捺印申請」ボタンをクリックしてください。',\n    congratulationsCn: '電子署名を選択していただきありがとうございます！',\n    carbonSavingSingleCn: '地球のために{num}gCO2eのカーボンを削減しました',\n    carbonVerification: '*「カーボンストップ」による科学的計算'\n  },\n  view: {\n    title: '契約書の確認',\n    ok: '完了',\n    cannotReview: '契約書が確認できません',\n    privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません'\n  },\n  prepare: {\n    sealArea: '捺印所',\n    senderNotice: '現在の契約書送信主体は：{entName}です。',\n    preSetDialogConfirm: 'わかりました。',\n    preSetDialogContact: '開通するようすぐにベストサインの販売スタッフに連絡する',\n    preSetDialogInfo: '契約書の事前設定に成功すると、システムはテンプレートに基づき対応する署名者情報、署名要件、署名位置、契約内容フィールドなどを自動的に入力します',\n    preSetDialogTitle: '契約書事前設定テンプレートとは何ですか？',\n    initialValues: '契約内容を基に事前設定する初期値',\n    proxyUpload: 'ローカル文書のアップロード後、契約発起側は選択する事ができます',\n    signHeaderTitle: '文書と契約者の追加',\n    step1: '手順1',\n    confirmSender: '発起側の確認',\n    step2: '手順2',\n    uploadFile: 'ファイルのアップロード',\n    step3: '手順3',\n    addSigner: '契約者の追加',\n    actionDemo: '操作デモ',\n    next: '次へ',\n    isUploadingErr: '文書のアップロードが完了していません。完了後継続して操作してください',\n    noUploadFileErr: 'アップロードが完了していません。アップロード後継続して操作してください',\n    noContractTitleErr: '契約書名称が未記入です。記入した後継続してください',\n    contractTypeErr: '現在の契約タイプは削除されています。契約タイプを再選択してください',\n    expiredDateErr: '契約有効時間に誤りがあります。修正後継続してください',\n    noExpiredDateErr: '署名期限を記入した後継続してください',\n    describeFieldsErr: '必須内容フィールドを記入した後継続してください',\n    noRecipientsErr: '少なくとも契約者1つを追加',\n    noAccountErr: 'アカウントは空欄に出来ません',\n    noUserNameErr: '氏名は空欄に出来ません',\n    noIDNumberErr: '身分証番号は空欄にできません',\n    accountFormatErr: 'フォーマットが正確でありません。正しいメールアドレスを入力してください',\n    userNameFormatErr: 'フォーマットが正確でありません。正確な氏名を入力してください',\n    enterpriseNameErr: '正確な企業名を入力してください',\n    idNumberForVerifyErr: 'フォーマットが正確でありません。正確な身分証を入力してください',\n    signerErr: '契約者に誤りがあります',\n    noSignerErr: '少なくとも署名者1名を追加',\n    lackAttachmentNameErr: '付属書類名を入力してください',\n    repeatRecipientsErr: '順番に署名しない場合、重複して契約者を追加することはできません',\n    innerContact: '内部担当者',\n    outerContact: '外部担当者',\n    search: '検索',\n    accountSelected: 'すでにアカウント選択済み',\n    groupNameAll: '全部',\n    unclassified: '未分類',\n    fileLessThan: '{num}M以下のファイルをアップロードしてください',\n    beExcel: 'Excelファイルをアップロードしてください',\n    usePdf: 'アップロードする際PDFファイルもしくは画像を使用してください',\n    usePdfFile: 'アップロードの際はPDFファイルをご使用ください',\n    fileNameMoreThan: 'ファイル名の長さが{num}を超えると、自動で切り取ります',\n    needAddSender: '本企業/本人が署名者に設定されていないので、契約書が送信されたら、あなたは契約書に署名しません。署名者として追加しますか？',\n    addSender: '署名者として追加',\n    tip: '注意',\n    cancel: 'キャンセル'\n  },\n  addReceiver: {\n    English: '英語',\n    Japanese: '日本語',\n    Chinese: '中国語',\n    Arabic: 'アラビア語',\n    setNoticelang: '署名通知の言語設定',\n    limitFaceConfigTip: '契約単価が低いため、この機能は利用できません。ベストサインにご連絡ください',\n    individual: '契約個人',\n    enterprise: '契約企業',\n    addInstructions: '契約注意事項の追加',\n    instructionsContent: '提出した資料はお客様が契約履行状態を追跡する助けを行い、業務の執行が正常かどうか判断します。設定後、署名者は必ず要求に基づき提出しなければなりません',\n    addContractingInfo: '契約主体の資料を提出',\n    contractingInfoContent: '提出した資料はお客様が契約者の主体資質を確認する助けを行い、業務の展開を開始/継続するかの判断をします。契約者がすでに同様の資料を提出している場合、再提出しなくてもかまいません',\n    payer: '支払い者',\n    handWriting: '手書き筆跡の識別のオン',\n    realName: '担当者は実名が必要です',\n    sameTip: '注意：契約者の企業名が完全一致している状態でのみ署名が可能です',\n    proxy: '相手側受付での受け取り',\n    aboradTip: '注意：当該契約者は域外の人であり、実名認証にはリスクがあります。まず先に当該者の身分を確認してください。',\n    busRole: '業務の役割',\n    busRoleTip: '契約者を識別する助けとなり、管理に便宜を図ります',\n    busRolePlaceholder: 'もしも従業員/販売業者であれば',\n    handWritingTip: 'この使用者は署名する際はっきりと識別可能な氏名を手書きしてもらうことで署名を完了できます',\n    instructions: '契約注意事項の追加　|　（255字以内）',\n    contractingParty: '契約主体資料',\n    signerPay: '本契約は当該署名者が支払います',\n    afterReadingTitle: '閲読完了後再署名する',\n    afterReading: '署名者は必ず閲読し、契約書内容を理解したうえで操作を続けることができます',\n    handWritingTips: 'この使用者の手書きの氏名と発信者指定もしくは実名情報の中の氏名と比較し、一致した段階で署名が完了します',\n    SsTitle: '捺印及び署名',\n    SsTip: '企業の印章を使用して署名する際、同時に追加する個人サインで署名を完了する必要があります。署名前に個人実名認証を完了する必要があります',\n    signature: '署名',\n    stamp: '捺印',\n    Ss: '捺印及び署名',\n    mutexError: '「{msg}」を設定済みです。先に「{msg}」の設定を削除した後再選択してください',\n    handWriteNotAllowed: '手書き署名は許されていません',\n    forceHandWrite: '手書き署名が必須です',\n    faceFirst: '顔認証が優先で、予備で認証コードの署名になります',\n    faceVerify: '顔認証署名が必須です',\n    attachmentRequired: '契約書付属資料の追加',\n    newAttachmentRequired: '契約主体資料を提出',\n    attachmentError: '契約付属資料名称は同じものにできません',\n    receiver: '携帯電話/メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',\n    receiverJa: 'メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',\n    orderSignLabel: '署名の順序',\n    contactAddress: '担当者アドレス帳',\n    signOrder: '順序で署名する',\n    account: 'アカウント',\n    accountPlaceholder: '携帯電話/メールアドレス（必須項目）',\n    accountPlaceholderJa: 'メールアドレス（必須項目）',\n    accountReceptionCollection: '受付での受け取り',\n    accountReceptionCollectionTip1: '相手方の具体的なアカウントを知らないか相手方がアカウントを持っていません',\n    accountReceptionCollectionTip2: '受付での受け取りを選択してください',\n    signSubjectPerson: '契約主体：個人',\n    nameTips: '氏名（オプション、契約身分の確認で用います）',\n    requiredNameTips: '氏名（必須項目、契約身分の確認で用います）',\n    entOperatorNameTips: '氏名（オプション）',\n    needAuth: '実名が必要です',\n    operatorNeedAuth: '担当者は実名が必要です',\n    signSubjectEnt: '契約主体：企業',\n    entNameTips: '企業名（必須項目、契約身分の確認で用います）',\n    operator: '担当者',\n    sign: '署名',\n    more: 'もっと',\n    faceFirstTips: '署名する際システムは初期設定で顔認証による検証を採用しています。顔認証が通らない回数が1日の上限を超えた場合自動で認証コードによる検証に切り替わります',\n    mustFace: '顔認証署名が必須です',\n    mustHandWrite: '手書き署名が必須です',\n    fillIDNumber: '身分証明書番号',\n    fillNoticeCall: '通知携帯電話',\n    fillNoticeCallTips: '通知携帯電話を記入してください',\n    addNotice: 'メッセージの追加',\n    attachTips: '契約書付属資料の追加',\n    faceSign: '顔認証署名が必須です',\n    faceSignTips: 'この使用者が署名を完了するには、顔認証する必要があります（顔認証署名は当面の間、中国大陸の住民のみ利用可能です）',\n    handWriteNotAllowedTips: 'この使用者はすでに設定しているサインもしくは初期設定の字体サインを選択して署名を完了する必要があります',\n    handWriteTips: 'この使用者は手書きサインで署名を完了する必要があります',\n    idNumberTips: '契約身分の確認で用います',\n    verifyBefore: '文書を確認する前に身分を認証',\n    verify: '身分の認証',\n    verifyTips: '最多20文字',\n    verifyTips2: 'お客様はこの認証情報をこの使用者に提供しなければなりません',\n    sendToThirdPlatform: '第三者プラットフォームに送信',\n    platFormName: 'プラットフォーム名',\n    fillThirdPlatFormName: 'プラットフォーム名を入力してください',\n    attach: '資料',\n    attachName: '資材名',\n    exampleID: '例：身分証写真',\n    attachInfo: '備考',\n    attachInfoTips: '例：本人の身分証写真をアップロードしてください',\n    addAttachRequire: '資料の追加',\n    addSignEnt: '契約企業の追加',\n    addSignPerson: '契約個人の追加',\n    selectContact: '担当者の選択',\n    save: '保存',\n    searchVerify: '認証の確認',\n    fillImageContentTips: '画像の内容を入力してください',\n    ok: '確定',\n    findContact: '契約書の中から下記の契約者を探します',\n    signer: '契約者',\n    signerTips: 'ヒント：契約者の選択後、プラットフォームはサイン及び捺印の位置決めを補助します。',\n    add: '追加',\n    notAdd: '追加なし',\n    cc: '副本発信',\n    notNeedAuth: '実名は必要ありません',\n    operatorNotNeedAuth: '担当者の実名は必要ありません',\n    extracting: '抽出中',\n    autoFill: '署名者の自動記入',\n    failExtracting: '契約者が抽出できません',\n    idNumberForVerifyErr: '正確な身分証を入力してください',\n    noAccountErr: 'アカウントは空欄に出来ません',\n    noUserNameErr: '氏名は空欄に出来ません',\n    noIDNumberErr: '身分証番号は空欄にできません',\n    noEntNameErr: '企業名は空欄にできません',\n    accountFormatErr: '正しいメールアドレスを入力してください',\n    enterpriseNameErr: '正確な会社名を入力してください',\n    userNameFormatErr: '正確な氏名を入力してください',\n    riskCues: 'リスクに関して',\n    riskCuesMsg: 'もしも契約者が実名署名をせず、文書において紛糾が発生した場合、署名者の身分を証明する証拠を自分で用意する必要があります。リスクを避ける必要がある場合、「実名が必要です」を選択してください。',\n    confirmBtnText: '「実名が必要です」を選択',\n    cancelBtnText: '「実名は必要ありません」を選択',\n    attachLengthErr: 'お客様は1人の署名者に対し、最大50件の添付ファイル要求しか追加できません',\n    collapse: '折りたたむ',\n    expand: '展開する',\n    delete: '削除',\n    saySomething: 'なにか話しましょう',\n    addImage: 'ファイルの追加',\n    addImageTips: '（Word/PDF及び画像をサポート、ファイル3つ以下とします）',\n    give: '先',\n    fileMax: 'アップロード数量が上限を超えています',\n    signerLimit: '現在のバージョンは{limit}個を超える相対的署名/副本発信先をサポートしていません。',\n    showExamle: 'サンプル画像を確認',\n    downloadExamle: 'サンプルファイルをダウンロード'\n  },\n  addReceiverGuide: {\n    notRemind: '次回はお知らせしないでください',\n    sign: '自署',\n    entSign: '企業の自署',\n    stamp: '捺印',\n    stampSign: '捺印及び署名',\n    requestSeal: '業務照合印',\n    'guideTitle': '新しい署名者を追加する方法',\n    'receiverType': '署名者が契約に参加する方法を選択する必要があります (6 つのうちの 1 つを選択してください)：',\n    'asEntSign': '企業を代表してサインオンします：',\n    'sealSub': '署名者は、契約書に公印または契約書用特別印鑑等を押印する必要があります',\n    'signatureSub': '法人または役員が、企業に代わって契約に署名します。企業は、署名者が契約を閲覧できないように契約を譲渡する権利を有します',\n    'vipOnly': 'プレミアムバージョンが利用可能',\n    'stampSub': '署名者は、印鑑を押すだけでなく、企業を代表して署名する必要があります',\n    'confirmSeal': '企業を代表して業務用チェックスタンプを使用する',\n    'confirmSealSub': '財務諸表や確認書などの書類は、最初に確認されてから押印されます',\n    'asPersonSign': '個人に代わって署名するには:',\n    'asPersonSignTip': 'ビジネスを代表するものではなく、個人のみを代表して署名されています',\n    'asPersonSignDesc': 'ローン契約、参入および退出などの署名者の私的な契約',\n    'scanSign': 'コードをスキャンして署名する',\n    'scanSignDesc': '契約書を発行する際に署名者を書く必要はありません. 契約書が発行された後、誰でもコードをスキャンするか、検査ページのリンクをクリックして署名することができます. 物流書類の受領シナリオに適用できます',\n    'selectSignTypeTip': '最初に署名者が契約に参加する方法を選択してください'\n  },\n  linkContract: {\n    title: '契約書の関連付け',\n    connectMore: '関連するその他の契約書',\n    placeholder: '契約書番号を入力してください',\n    revoke: '署名抹消済み',\n    overdue: '署名期限切れ',\n    approvalNotPassed: '審査却下',\n    reject: '署名拒否済み',\n    signing: '署名中',\n    complete: '完了済み',\n    approvaling: '審査中',\n    disconnect: '関連付けの解除',\n    disconnectSuccess: '関連付けの解除完了',\n    connectLimit: '関連付け契約書数の上限は100部です'\n  },\n  field: {\n    fieldTip: {\n      title: '署名位置の欠落',\n      error: '下記の契約書指定署名位置がありません（{type}）',\n      add: 'フィールドの追加',\n      continue: '送信を継続'\n    },\n    accountCharge: {\n      notice: 'この契約書は参加アカウント数に基づき課金します',\n      able: '正常に送信できます',\n      unable: '使用アカウント数が不足しています。ベストサインのカスタマーサービスに連絡してください',\n      notify: 'この契約書はすべての契約書先に英語で通知を送信します',\n      noNotify: {\n        1: 'この契約書は契約の関連通知を発信しません',\n        2: '（署名・審査・副本発信・契約有効期限などの通知SMS及びメールを含む）'\n      }\n    },\n    ridingStamp: '割り印',\n    watermark: 'すかし',\n    senderSignature: '捺印者署名',\n    optional: 'オプション',\n    clickDecoration: '契約書装飾をタップ',\n    decoration: '契約書装飾',\n    sysError: 'システムビジーです。時間をおいてから試してください',\n    partedMarkedError: '「捺印とサイン」の契約者を指定しました。必ず同時に捺印とサインを指定してください',\n    fieldTitle: '全部で{length}部の契約書に署名位置指定が必要です',\n    send: '発信',\n    contractDispatchApply: '契約書の送信申請',\n    contractNeedYouSign: 'このファイルはお客様の署名が必要です',\n    ifSignRightNow: 'すぐに署名しますか',\n    signRightNow: 'すぐに署名します',\n    signLater: '後で署名します',\n    signaturePositionErr: '各署名者に署名位置を指定してください',\n    sendSucceed: '送信完了',\n    confirm: '確定',\n    cancel: 'キャンセル',\n    qrCodeTips: '署名後読み取り、すぐに署名詳細・署名の有効性及びこの契約書が改ざんされているかどうかの検証を確認できます',\n    pagesField: '第{currentPage}ページ、計{totalPages}}ページ',\n    suitableWidth: '幅の調整',\n    signCheck: '署名の確認',\n    locateSignaturePosition: '署名位置指定',\n    locateTips: '素早く署名位置を指定することができます。現在、各署名者の最初の署名位置の指定のみをサポートしています',\n    step1: '手順1',\n    selectSigner: '契約者を選択',\n    step2: '手順2',\n    dragSignaturePosition: '署名位置をドラッグ',\n    signingField: '署名フィールド',\n    docTitle: 'ファイル',\n    totalPages: 'ページ数：{totalPages}ページ',\n    receiver: '受信先',\n    delete: '削除',\n    deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます',\n    unlimitedNotice: 'この契約書の費用は使い放題です',\n    charge: '課金',\n    units: '{num}部',\n    contractToPrivate: '個人向け契約書',\n    contractToPublic: '企業向け契約書',\n    costTips: {\n      1: '企業向け契約書：署名者（送信者を含まず）の中に企業アカウントがある契約書',\n      2: '個人向け契約書：署名者（送信者を含まず）の中に企業アカウントがない契約書',\n      3: '課金部数はファイル数に基づき計算します',\n      4: '課金部数 = ファイル部数 X ユーザーグループ（列）の一括インポート'\n    },\n    costInfo: '契約の送信に成功すると、直ちに費用が控除され、契約の完了、期限超過、撤回、または署名拒否は返金されません。',\n    toCharge: 'リチャージする',\n    contractNeedCharge: {\n      1: '使用可能な契約部数が不足しているため、送信できません',\n      2: '使用可能な契約部数が不足しています。管理者主任にリチャージするよう連絡してください'\n    },\n    chooseApprover: '審査者の選択：',\n    nextStep: '次へ',\n    submitApproval: '審査提出',\n    autoSendAfterApproval: '*審査に合格後、契約書を自動で送信します',\n    chooseApprovalFlow: '審査フロー1つを選択してください',\n    completeApprovalFlow: 'お客様の提出した審査フローに不備があります。追加してから再度提出してください。',\n    viewPrivateLetter: 'メッセージの確認',\n    addPrivateLetter: 'メッセージの追加',\n    append: '追加',\n    privateLetter: 'メッセージ',\n    signNeedKnow: '契約注意事項',\n    maximum5M: '5M以下のファイルをアップロードしてください',\n    uploadServerFailure: 'サーバーへのアップロードに失敗しました',\n    uploadFailure: 'アップロードエラーです',\n    pager: 'ページ番号',\n    seal: '捺印',\n    signature: '署名',\n    signDate: '署名日時',\n    text: 'テキスト',\n    date: '日付',\n    qrCode: '二次元コード',\n    number: '数字',\n    dynamicTable: '動的フォーマット',\n    terms: '契約条項',\n    checkBox: 'チェックボックス',\n    radioBox: 'ラジオボタン',\n    image: '画像'\n  },\n  addressBook: {\n    innerMember: {\n      title: '企業内部メンバー',\n      tips: '送信者が素早く内部の担当者を探せるように企業メンバー情報を調整します',\n      operation: '管理コンソールに入る'\n    },\n    outerContacts: {\n      title: '外部企業担当者',\n      tips: '業務の展開がうまくいくよう、お客様の協力パートナーを招待し、事前に実名登録をしてください',\n      operation: '協力パートナーを招待する'\n    },\n    myContacts: {\n      title: '私の担当者',\n      tips: '署名者の情報が正確かつ間違いがないように、担当者を修正します',\n      operation: 'ユーザーセンターに入る'\n    },\n    selected: 'すでにアカウント選択済み',\n    search: '検索',\n    loadMore: 'もっと読む',\n    end: '全ロード完了'\n  },\n  dataBoxInvite: {\n    title: '協力パートナーを招待する',\n    step1: 'お客様の協力パートナーとのリンクを共有し、事前に企業を構築',\n    step2: 'リンク/二次元コードの授権後の協力パートナーがアドレス帳に表示されます',\n    step3: '「ファイル＋」の中で協力パートナーをさらに管理します',\n    imgName: '採集した二次元コードを共有',\n    saveQrcode: '二次元コードをローカルに保存',\n    copy: 'コピー',\n    copySuccess: 'コピー完了',\n    copyFailed: 'コピー失敗'\n  },\n  shareView: {\n    title: 'レビューのために転送',\n    account: '電話番号/メールアドレス',\n    role: 'レビュー担当者の役割',\n    note: '備考',\n    link: 'リンクを：',\n    signerMessage: '署名者のメッセージ',\n    rolePlaceholder: '例：会社の法務、部門のリーダーなど',\n    notePlaceholder: 'レビュー担当者へのメッセージ、200文字以内',\n    generateLink: 'リンクを生成',\n    regenerateLink: 'リンクを再生成',\n    saveQrcode: 'ミニプログラムコードを保存',\n    inputAccount: '電話番号またはメールアドレスを入力してください',\n    inputCorrectAccount: '正しい携帯電話番号またはメールアドレスを入力してください',\n    accountInputTip: 'リンクが正常に開かれるようにするために、契約審査者の情報を正確に入力してください',\n    shareLinkTip1: 'ミニプログラムコードを保存するか',\n    shareLinkTip: 'リンクをコピーして、レビュー担当者と共有してください',\n    linkTip1: '契約の本文は機密情報ですので、必要のない限り外部に漏らさないでください',\n    linkTip2: 'リンクの有効期限は2日間です；リンクを再生成した後、以前のリンクは自動的に無効になります'\n  },\n  recoverSpecialSeal: {\n    title: '印章が使用できません',\n    description1: '送信者は契約時にこの印章を使用して契約書に署名するよう要求していますが、御社はこの印章をすでに削除しています。確実に署名が行えるよう、管理者にこの印章の復元を要求してください',\n    description2: 'もし、この印章が本当に今後の使用に適さない場合、送信者に連絡して印章のデザインの修正を要求した後、再度契約書に署名するようにしてください。',\n    postRecover: '印章の復元申請',\n    note: 'タップ後管理者が印章復元申請のSMS/メールを受け取ると同時に、同時に印章の管理ページで申請を見ることができます。',\n    requestSend: '復元申請の提出完了'\n  },\n  paperSign: {\n    title: '紙媒体の署名を使用',\n    stepText: ['次へ', '紙媒体署名確認', '確定'],\n    needUploadFile: '先にスキャンファイルをアップロードしてください',\n    uploadError: 'アップロードエラーです',\n    cancel: 'キャンセル',\n    downloadPaperFile: '紙媒体署名ファイルの取得',\n    step0: {\n      title: '先に契約書をダウンロード印刷し、物理的な捺印をした上で、送信者へ郵送する必要があります',\n      address: '郵送住所：',\n      contactName: '受取者氏名：',\n      contactPhone: '受取者連絡方法：',\n      defaultValue: 'オフライン方法で送信者から受け取ってください'\n    },\n    step1: {\n      title0: '手順1：ダウンロード及び紙媒体契約書の印刷',\n      title0Desc: ['ダウンロードし印刷した契約書にはすでに署名している電子印章の図案が含まれていなければなりません。紙の署名書類を入手してください。', '获取纸质签文件。'],\n      title1: '手順2：印章の捺印',\n      title1Desc: '紙媒体契約書に契約書に有効な会社の印章を捺印してください。',\n      title2: ['手順3：', 'スキャンデータのアップロード，', '署名ページに戻り、署名ボタンをタップして、紙媒体署名を完了してください'],\n      title2Desc: ['紙媒体契約書をスキャンしデータ（PDFファイル）に変換後アップロードしてください。', '電子契約書内にはお客様の印章図案は表示されませんが、この操作を行ったことは記録されます。']\n    },\n    step2: {\n      title: ['紙媒体契約書のスキャンデータ（PDFファイル）をアップロードしてください', '紙媒体契約書をダウンロードし署名した後、再度確定ボタンをタップして、紙媒体契約書のプロセスを終了します。'],\n      uploadFile: 'スキャンファイルのアップロード',\n      getCodeVerify: '契約署名検証の取得',\n      isUploading: 'アップロード中...'\n    }\n  },\n  allowPaperSignDialog: {\n    title: '紙契約書への署名を許可する',\n    content: 'この契約書は{senderName}が{receiverName}へ送信したもので、紙形式での署名が許可されています。',\n    tip: '契約書を印刷して、会社の責任者が紙の契約書に押印することも選択できます。',\n    icon: '紙契約書へ署名に変更 >>',\n    goSign: '電子署名する',\n    cancel: 'キャンセル'\n  },\n  sealInconformityDialog: {\n    errorSeal: {\n      title: '印章の提示',\n      tip: '現在の印章画像がお客様の企業身分と適合していないことが検出されました。現在の印章画像識別の結果は：',\n      tip1: '検出された企業印章と企業名：',\n      tip2: 'このまま現在の印章画像を使用しますか？',\n      tip3: '発信者の要求に基づき、お客様が使用する企業名は：',\n      tip4: 'の印章です',\n      tip5: '印章が要件に適合していることを確認してください。さもなければ契約書の有効性に影響を及ぼす恐れがあります。',\n      tip6: '適合していません。印章が発信者の要件に適合しているかどうか確認してください。',\n      guide: '正確な印章をアップロードするには >>',\n      next: '続けて使用',\n      tip7: 'また、あなたの印鑑名は規範に合わず、「{keyWord}」という文字が付いています。',\n      tip8: '印鑑名が規範に合わないことが検出されました。「{keyWord}」という文字が付いていますが、引き続き使用しますか？'\n    },\n    exampleSeal: {\n      title: '印章図案のアップロード方法',\n      way1: ['方法1：', '1、白い紙の上で実物の印章図案を捺印します。', '2、撮影し、画像をプラットフォームにアップロードします'],\n      way2: ['方法2：', '図のように直接プラットフォームの電子印章機能で生成します：'],\n      errorWay: ['間違った方法：', '印章を手で持つ', '関係ない画像', '営業許可証']\n    },\n    confirm: '確認',\n    cancel: 'キャンセル'\n  },\n  addSealDialog: {\n    title: '印章画像を追加',\n    dec1: 'ローカルフォルダから印章画像を1枚選択してください（形式：JPG、JPEG、PNG等）。システムがこの印章画像を現在の契約書に合成します。',\n    dec2: 'その後、「署名」ボタンをクリックして署名検証を通過すると、押印が完了します。',\n    updateNewSeal: '新しい印章をアップロード'\n  }\n};", "map": {"version": 3, "names": ["sign", "sealLabelsTip", "continue", "nonMainlandCARenewalTip", "reselect", "approvalFeatures", "dialogTitle", "understand", "feature1", "feature2", "tip1", "tip2", "tip3", "tip4", "tip5", "tip6", "annotate", "delete", "edit", "operateTitle", "placeholder", "contractHighLight", "needRemark", "notNeedRemark", "switchToReceiver", "notAddEntTip", "contractPartiesYouChoose", "contractPartyFilled", "certifyOtherCompanies", "youCanAlso", "needVerification", "prompt", "submit", "cancel", "addSeal", "noSealAvailable", "memberNoSealAvailable", "noticeAdminFoSeal", "requestSomeone", "requestOthersToContinue", "requestOthersToContinueSucceed", "requestSomeoneList", "electronicSeal", "changeTheSeal", "goToVerify", "noSealToC<PERSON>ose", "goVerify", "goToVerifyEnt", "digitalCertificateTip", "signDes", "signAgain", "send", "person", "ent", "entName", "account", "accountPH", "approved", "signVerification", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectFail", "connectFailTip", "connectFailTip1", "connectFailTip2", "connectFailTip3", "personalMaterials", "noSupportface", "lackEntName", "errAccount", "noticeAdmin", "signDone", "signDoneTip", "approveDone", "approveDoneTip", "completeSign", "<PERSON><PERSON><PERSON><PERSON>", "stillSignTip", "signHighLightTip", "riskDetails", "noviewDifference", "highLightTip", "commonTip", "view", "start", "nextStep", "help", "faceFailed", "dualFailed", "faceFailedtips", "verifyTry", "faceLimit", "upSignReq", "reqFace", "signAfterFace", "qrcodeInvalid", "faceFirstExceed", "date", "chooseSeal", "seal", "signature", "handwrite", "mysign", "approve<PERSON>lace", "approvePlace_1", "approvePlace_2", "approveAgree", "approveReject", "signBy", "signByEnd", "sealBy", "sealByEnd", "coverBy", "applicant", "continueVeri", "registerAndReal", "goToResiter", "sureToUse", "toSign", "pleaseComplete", "confirmSign", "admin", "contratAdmin", "addToEnt", "alreadyExists", "sendMsg", "<PERSON><PERSON><PERSON><PERSON>", "title", "viewImg", "priLetter", "priLetterFromSomeone", "readLetter", "approve", "disapprove", "refuseSign", "paperSign", "refuseTip", "refuseReason", "reasonWriteTip", "refuseReasonOther", "refuseConfirm", "refuseConfirmTip", "waitAndThink", "signValidationTitle", "email", "phoneNumber", "password", "verificationCode", "mailVerificationCode", "forgetPsw", "if", "forgetPassword", "rejectionVer", "msgTip", "voiceVerCode", "SMSVerCode", "or", "tryMore", "emailVerCode", "SentSuccessfully", "intervalTip", "signPsw", "useSignPsw", "setSignPsw", "useVerCode", "inputVerifyCodeTip", "inputSignPwdTip", "signConfirmTip", "confirm", "signSuc", "refuseSuc", "approveSuc", "hdFile", "otherOperations", "reviewDetails", "close", "submitter", "signatory", "reviewSchedule", "signByPc", "signPageDescription", "sealBySomeone", "signDate", "download", "signPage", "signNow", "sender", "signer", "startSignTime", "signDeadLine", "authGuide", "goToHome", "tip_1", "tip_2", "tip_3", "tip_4", "tip_5", "new_tip_1", "new_tip_2", "new_tip_3", "new_tip_4", "entUserName", "idNumberForVerify", "realNameAuth", "applySeal", "signContract", "switch", "rejectReasonList", "signOperateReason", "termReason", "explainReason", "otherReason", "selectSignature", "selectS<PERSON>er", "pleaseScanToSign", "pleaseScanAliPay", "pleaseScanWechat", "requiredFaceSign", "requiredDualSign", "verCodeVerify", "applyToSign", "autoRemindAfterApproval", "cannotSignBeforeApproval", "finishSignatureBeforeSign", "uploadFileOnRightSite", "cannotApplySealNeedPay", "cannotOtherSealReason", "unlimitedNotice", "units", "contractToPrivate", "contractToPublic", "paySum", "payTotal", "fundsLack", "contactToRecharge", "deductPublicNotice", "needSignerPay", "recharge", "toSubmit", "appliedSeal", "noSeal", "noSwitchSealNeedDistribute", "viewApproveProcess", "approveProcess", "noApprove<PERSON><PERSON>nt", "knew", "noSwitchSealNeedAppend", "hadAutoSet", "setThatSignature", "setThatSeal", "applyThatSeal", "hasSetTip", "hasSetSealTip", "hasSetSignatureTip", "hasApplyForSealTip", "savedOnLeftSite", "ridingSealMinLimit", "ridingSealMaxLimit", "ridingSealMinOrMaxLimit", "noSealForRiding", "noSwitchSealNeedAppendBySelf", "gotoAppendSeal", "approvalFlowSuccessfulSet", "mandate", "loginToAppendSeal", "signIdentityAs", "enterNextContract", "fileList", "addSignerFile", "signatureFinish", "dragSignatureTip", "noticeToManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senderRequire", "senderRequireUseFollowIdentity", "suggestToAuth", "contactEntAdmin", "set<PERSON>ourAccount", "authInfoUnMatchNeedResend", "noEntNameNeedResend", "pleaseUse", "me", "myself", "reAuthBtnTip", "reAuthBtnContent", "descNoSame1", "descNoSame2", "authInfoNoSame", "authInfoNoSame2", "goHome", "authInfo", "authInfo2", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmOk", "goOnAuth", "signContractAfterAuth", "useIdentity", "inTheName", "of", "identity", "nameIs", "IDNumIs", "provideMoreAuthData", "leadToAuthBeforeSign", "groupProxyAuthNeedMore", "contactSender", "note", "identityInfo", "signNeedCoincidenceInfo", "needAuthPermissionContactAdmin", "iHadReadContract", "scrollToBottomTip", "getVerCodeFirst", "appScanVerify", "downloadBSApp", "scanned", "confirmInBSApp", "qrCodeExpired", "appKey", "goToScan", "setNotificationInUserCenter", "doNotWantUseVerCode", "try", "retry", "goToFaceVerify", "faceExceedTimes", "returnBack", "switchTo", "youCanChooseIdentityBlow", "needDrawSignatureFirst", "lacksSealNeedAppend", "manageSeal", "needDistributeSealToSelf", "chooseSealAfterAuth", "appendDrawSignature", "senderUnFill", "declare", "fileLessThan", "fileNeedUploadImg", "serverError", "oldFormatTip", "fileLimitFormatAndSize", "fileFormatImage", "fileFormatFile", "signNeedKnow", "signNeedKnowFrom", "approvalInfo", "approveNeedKnowFrom", "approveBeforeSend", "approveBeforeSign", "approveOperator", "approvalOpinion", "employeeDefault", "<PERSON><PERSON><PERSON><PERSON>", "addRidingSeal", "delRidingSeal", "file", "compressedFile", "attachmentContent", "pleaseClickView", "downloadFile", "noLabelPleaseAppend", "archiveTo", "hadArchivedToFolder", "pleaseScanToHandleWrite", "save", "remind", "riskTip", "chooseApp<PERSON><PERSON><PERSON>", "useSealByOther", "getSeal", "nowApplySealList", "nowAdminSealList", "chooseApplyPersonToDeal", "chooseAdminSign", "chooseTransferPerson", "chooseApplyPersonToMandate", "contactGroupAdminToDistributeSeal", "sealApplySentPleaseWait", "successfulSent", "authTip", "t2", "t3", "tCommon1", "tCommon2_1", "tCommon2_2", "tCommon2_3", "viewAndSign1", "viewAndSignConflict", "needSomeoneToSignature", "needToSet", "approver", "clickToSignature", "transferToOtherToSign", "signatureBy", "tipRightNumber", "tipRightIdCard", "tipRightPhoneNumber", "tip", "tipRequired", "viewContractDetail", "required", "optional", "decimalLimit", "intLimit", "invalidContract", "No", "chooseFrom2", "crossPlatformCofirm", "message", "confirmButtonText", "cancelButtonText", "sealScope", "currentContract", "allContract", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fixTextDisplay", "allPage", "notJoinTip", "signJa", "beforeSignTip1", "beforeSignTip2", "beforeSignTip3", "beforeSignTip4", "beforeSignTip5", "beforeSignTip6", "beforeSignTip7", "entNamePlaceholder", "corporateNumberPlaceholder", "corporateNumber", "singerNamePlaceholder", "<PERSON><PERSON><PERSON>", "itsMe", "businessPic", "waitApprove", "wrongInformation", "confirmChange", "communicateSender1", "communicateSender2", "createSeal", "emptyErr", "areaRegister", "jp", "cn", "are", "other", "plsSelect", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "comNum", "buyRecord", "selectArea", "uaeTip1", "uaeTip2", "uaeTip3", "signPC", "commonSign", "contractVerification", "VerCodeVerify", "QrCodeVerify", "verifyTip", "verifyAllTip", "selectSeal", "adminGuideTip", "toAddSealWithConsole", "use", "toAddSeal", "mySeal", "operationCompleted", "FDASign", "signer<PERSON>dd", "signerEdit", "editTip", "inputNameTip", "inputName", "signerNameFillTip", "plsInput", "customInput", "signPlace<PERSON>y<PERSON><PERSON><PERSON>", "signGuide", "howDragSeal", "howDragSignature", "iKnow", "step", "one", "two1", "two2", "three", "dragSeal", "continueDragSeal", "dragSignature", "continueDragSignature", "dragPlace", "notR<PERSON>ind", "signTip", "two", "continueOperation", "success", "exitApproval", "continueApproval", "next", "none", "approvalProcess", "receiver", "contractDetail", "downloadBtn", "tips", "SigningCompleted", "submitCompleted", "noTurnSign", "noRightSign", "noNeedSign", "ApprovalCompleted", "contractRevoked", "contractRefused", "linkExpired", "contractClosed", "approvalReject", "approving", "viewContract", "viewContractList", "needMeSign", "downloadContract", "signed", "approval", "personHas", "personHave", "person<PERSON>asnot", "<PERSON><PERSON><PERSON><PERSON>", "headsTaskDone", "headsTaskNotDone", "taskStatusBetween", "cannotDownload", "privateStorage", "beenDeleted", "unActive", "back", "contratStatusDes", "contractConditionDes", "contractIng", "contractComplete", "dataProduct", "btnText", "signOnGoing", "operate", "freeContract", "sendContract", "congratulations", "carbonSaving", "signGift", "followPublic", "congratulations<PERSON><PERSON><PERSON>", "carbonSavingSingle", "viewContractTip", "congratulationsCn", "carbonSavingSingleCn", "carbonVerification", "ok", "prepare", "sealArea", "senderNotice", "preSetDialogConfirm", "preSetDialogContact", "preSetDialogInfo", "preSetDialogTitle", "initialValues", "proxyUpload", "signHeaderTitle", "step1", "confirmSender", "step2", "uploadFile", "step3", "addSigner", "actionDemo", "isUploadingErr", "noUploadFileErr", "noContractTitleErr", "contractTypeErr", "expiredDateErr", "noExpiredDateErr", "<PERSON><PERSON><PERSON><PERSON><PERSON>rr", "noRecipientsErr", "noAccountErr", "noUserNameErr", "noIDNumberErr", "accountFormatErr", "userNameFormatErr", "enterpriseNameErr", "idNumberForVerifyErr", "signer<PERSON>rr", "noSignerErr", "lackAttachmentNameErr", "repeatRecipientsErr", "innerContact", "outerContact", "search", "accountSelected", "groupNameAll", "unclassified", "beExcel", "usePdf", "usePdfFile", "fileNameMoreThan", "need<PERSON>dd<PERSON><PERSON>", "addSender", "addReceiver", "English", "Japanese", "Chinese", "Arabic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limitFaceConfigTip", "individual", "enterprise", "addInstructions", "instructionsContent", "addContractingInfo", "contractingInfoContent", "payer", "handWriting", "realName", "sameTip", "proxy", "aboradTip", "busRole", "busRoleTip", "busRolePlaceholder", "handWritingTip", "instructions", "contractingParty", "signer<PERSON><PERSON>", "afterReadingTitle", "afterReading", "handWritingTips", "SsTitle", "SsTip", "stamp", "Ss", "mutexError", "handWriteNotAllowed", "forceHandWrite", "faceFirst", "faceVerify", "attachmentRequired", "newAttachmentRequired", "attachmentError", "<PERSON><PERSON><PERSON>", "orderSignLabel", "contactAddress", "signOrder", "accountPlaceholder", "accountPlaceholderJa", "accountReceptionCollection", "accountReceptionCollectionTip1", "accountReceptionCollectionTip2", "signSub<PERSON><PERSON>erson", "nameTips", "requiredNameTips", "entOperatorNameTips", "needAuth", "operatorNeedAuth", "signSubjectEnt", "entNameTips", "operator", "more", "faceFirstTips", "mustFace", "mustHandWrite", "fillIDNumber", "fillNoticeCall", "fillNoticeCallTips", "addNotice", "attachTips", "faceSign", "faceSignTips", "handWriteNotAllowedTips", "handWriteTips", "idNumberTips", "verifyBefore", "verify", "verifyTips", "verifyTips2", "sendToThirdPlatform", "platFormName", "fillThirdPlatFormName", "attach", "attachName", "exampleID", "attachInfo", "attachInfoTips", "addAttachRequire", "addSignEnt", "addSign<PERSON>erson", "selectContact", "searchVerify", "fillImageContentTips", "findContact", "signer<PERSON><PERSON>s", "add", "notAdd", "cc", "notNeedAuth", "operatorNotNeedAuth", "extracting", "autoFill", "failExtracting", "noEntNameErr", "riskCues", "riskCuesMsg", "confirmBtnText", "cancelBtnText", "attachLengthErr", "collapse", "expand", "saySomething", "addImage", "addImageTips", "give", "fileMax", "signerLimit", "showExamle", "downloadExamle", "addReceiverGuide", "entSign", "stampSign", "requestSeal", "linkContract", "connectMore", "revoke", "overdue", "approvalNotPassed", "reject", "signing", "complete", "approvaling", "disconnect", "disconnectSuccess", "connectLimit", "field", "fieldTip", "error", "accountCharge", "notice", "able", "unable", "notify", "noNotify", "ridingStamp", "watermark", "senderSignature", "clickDecoration", "decoration", "sysError", "partedMarkedError", "fieldTitle", "contractDispatchApply", "contractNeedYouSign", "ifSignRightNow", "signRightNow", "signLater", "signaturePositionErr", "sendSucceed", "qrCodeTips", "pagesField", "suitableWidth", "signCheck", "locateSignaturePosition", "locateTips", "dragSignaturePosition", "<PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "totalPages", "charge", "costTips", "costInfo", "to<PERSON>harge", "contractNeedCharge", "chooseApprover", "submitApproval", "autoSendAfterApproval", "chooseApprovalFlow", "completeApprovalFlow", "viewPrivateLetter", "addPrivateLetter", "append", "privateLetter", "maximum5M", "uploadServerFailure", "uploadFailure", "pager", "text", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "addressBook", "innerMember", "operation", "outerContacts", "myContacts", "selected", "loadMore", "end", "dataBoxInvite", "imgName", "saveQrcode", "copy", "copySuccess", "copyFailed", "shareView", "role", "link", "signerMessage", "rolePlaceholder", "notePlaceholder", "generateLink", "regenerateLink", "inputAccount", "inputCorrectAccount", "accountInputTip", "shareLinkTip1", "shareLinkTip", "linkTip1", "linkTip2", "recoverSpecialSeal", "description1", "description2", "postRecover", "requestSend", "stepText", "needUploadFile", "uploadError", "downloadPaperFile", "step0", "address", "contactName", "contactPhone", "defaultValue", "title0", "title0Desc", "title1", "title1Desc", "title2", "title2Desc", "getCodeVerify", "isUploading", "allowPaperSignDialog", "content", "icon", "goSign", "sealInconformityDialog", "errorSeal", "guide", "exampleSeal", "way1", "way2", "errorWay", "addSealDialog", "dec1", "dec2", "updateNewSeal"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/sign/sign-ja.js"], "sourcesContent": ["export default {\n    sign: {\n        sealLabelsTip: '契約書に{sealLabelslen}つの押印が必要です。{personStr}が{otherSealLen}つの押印を行い、残りの{mySealLen}つはあなたが押印します。印章はページ上に表示されています。続行するかどうかご確認ください。',\n        continue: '続行',\n        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',\n        reselect: '再選',\n        approvalFeatures: {\n            dialogTitle: '新機能紹介',\n            understand: 'わかりました',\n            feature1: '文の強調注釈',\n            feature2: 'フィールドのハイライト',\n            tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',\n            tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',\n            tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。',\n            tip4: 'テキストフィールドを選択後、注釈ボタンをクリックして注釈テキストを追加できます。完了したら、修正または削除をクリックします。注釈の内容は、契約詳細ページ - 会社内部操作ログで確認できます。-公司内部操作日志中查看。',\n            tip5: 'ステップ1：注釈を追加するテキストフィールドを選択します。',\n            tip6: 'ステップ2：注釈を編集または削除します。',\n            annotate: '注釈',\n            delete: '削除',\n            edit: '修正',\n            operateTitle: '承認注釈の追加',\n            placeholder: '255語以内',\n        },\n        contractHighLight: {\n            dialogTitle: '契約書ハイライト表示',\n            understand: 'わかりました',\n            tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',\n            tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',\n            tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。',\n        },\n        needRemark: '備考を記入する必要があります',\n        notNeedRemark: '備考を記入しなくても問題ありません',\n        switchToReceiver: 'お客様は{receiver}に切り替えています',\n        notAddEntTip: '現在のユーザはこの企業のメンバーではありません、プライマリ管理者に連絡して企業に加入してください。',\n        contractPartiesYouChoose: '選択できる契約主体は',\n        contractPartyFilled: '発信者の記入した契約主体は',\n        certifyOtherCompanies: 'その他企業の認証',\n        youCanAlso: 'することもできます。',\n        needVerification: '署名するには実名認証した後でなければなりません',\n        prompt: '注意',\n        submit: '確定',\n        cancel: 'キャンセル',\n        sign: 'すぐに契約',\n        addSeal: 'パソコンを使用してベストサインのオフィシャルページにログインして印章を追加してください。',\n        noSealAvailable: '申し訳ありません。現在使用可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',\n        memberNoSealAvailable: '現在使用可能な印章がありません。管理者に連絡して構成した後再署名してください。もしくはオフラインにて管理者主任に連絡して構成してください。',\n        noticeAdminFoSeal: '管理者主任に通知する',\n        requestSomeone: '他人の認証を要求する',\n        requestOthersToContinue: '管理者主任に通知して実名認証を追加します',\n        requestOthersToContinueSucceed: '管理者に通知を送信しました',\n        requestSomeoneList: '以下のスタッフに実名認証を要求する',\n        electronicSeal: '電子公印',\n        changeTheSeal: 'この印章を使いたくありません。実名認証後印章を変更できます',\n        goToVerify: '実名認証を行う',\n        noSealToChoose: '切替可能な印章がありません。管理印章が必要であれば先に実名認証を行ってください',\n        goVerify: '認証を行う',\n        goToVerifyEnt: '企業認証を行う',\n        digitalCertificateTip: 'ベストサインがお客様のデジタル証明書をコールしています',\n        signDes: 'お客様の契約環境は安全です。安心して署名してください。',\n        signAgain: '署名を続ける',\n        send: '送信',\n        person: '個人',\n        ent: '企業',\n        entName: '企業名',\n        account: 'アカウント',\n        accountPH: '携帯電話またはメールアドレス',\n        approved: '審査',\n        signVerification: '署名',\n        cannotReview: '契約書が確認できません',\n        connectFail: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません.',\n        connectFailTip: '您可以尝试以下方法解决问题：',\n        connectFailTip1: '1、刷新页面。',\n        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',\n        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',\n        personalMaterials: '発信者は更に認証資料の追加を要求しています',\n        noSupportface: '契約発起側がお客様の顔認証署名を要求しています。中国大陸者以外は顔認証署名をサポートしていませんので、発起側に署名要求を修正するよう連絡してください。',\n        lackEntName: '企業名を入力してください',\n        errAccount: '正確なメールアドレスもしくは携帯番号を記入してください',\n        noticeAdmin: '参加の申請',\n        signDone: '署名完了',\n        signDoneTip: 'すでに契約書に署名しています',\n        approveDone: '審査完了',\n        approveDoneTip: 'すでに契約書を審査しています',\n        completeSign: 'まず先に「捺印箇所」もしくは「署名箇所」をタップして署名を完了してください',\n        fillFirst: 'まず先に入力ボックスに契約内容を記入してください',\n        stillSignTip: 'この{alias}に署名した後、{alias}の内容を変更する可能性のある他の署名者がいますが、署名を続けますか？',\n        signHighLightTip: '追加または変更が可能な{alias}内容は計{count}箇所',\n        riskDetails: 'リスク詳細',\n        noviewDifference: '他の署名者は依然として発信者が指定した{alias}の内容を変更することができるため、ベストサインでは現在の契約書と有効なバージョンの違いの内容に審査を行わず、デフォルトで有効なバージョンに署名することを認め、同意するものとします。',\n        highLightTip: 'こうしたリスクのある内容は「ハイライト表示」されます、詳細に確認してください。ページを更新するとハイライト表示を取り消せます。',\n        commonTip: '注意',\n        understand: 'わかりました',\n        view: '表示',\n        start: '開始',\n        nextStep: '次へ',\n        help: 'ヘルプ',\n        faceFailed: '申し訳ありません。顔認証に失敗しました',\n        dualFailed: '録音・録画認証に失敗しました。お客様の情報をご確認の上、再度お試しください。',\n        faceFailedtips: '注意',\n        verifyTry: 'ID情報を確認した後お試しください',\n        faceLimit: '今日の顔認証回数が上限に達しています',\n        upSignReq: '今日の顔認証回数が上限に達しています。明日再度行うか契約送信元に署名要求の修正を依頼してください。',\n        reqFace: '送信元がお客様に顔認証の検証を要請しています',\n        signAfterFace: '顔認証通過後すぐに契約書の署名が完了します',\n        qrcodeInvalid: '二次元コード情報が失効しています。リロードしてください',\n        faceFirstExceed: '顔スキャン失敗、続いて認証コードを使用して認証を行います',\n        date: '日付',\n        chooseSeal: '印章の選択',\n        seal: '印章',\n        signature: '署名',\n        handwrite: '手書き',\n        mysign: 'マイサイン',\n        approvePlace: '承認メッセージ（任意）',\n        approvePlace_1: '承認メッセージ',\n        approvePlace_2: '任意、255文字以下',\n        approveAgree: '審査結果：同意',\n        approveReject: '審査結果：却下',\n        signBy: 'より',\n        signByEnd: '捺印',\n        sealBy: 'より',\n        sealByEnd: '署名',\n        coverBy: '捺印必要',\n        applicant: '申請者',\n        continueVeri: '認証を続ける',\n        registerAndReal: '実名を新規登録してください',\n        goToResiter: '新規登録及び認証をしてください',\n        sureToUse: '使用の確定',\n        toSign: '契約しますか？',\n        pleaseComplete: 'まずは完了してください',\n        confirmSign: '署名の再確認',\n        admin: '管理者',\n        contratAdmin: '管理者にお客様のアカウントを連絡してください',\n        addToEnt: '企業メンバーとして追加',\n        alreadyExists: 'ベストサインに存在しています',\n        sendMsg: 'ベストサインはSMS形式で管理者に下記の内容を送信します：',\n        applyJoin: '参加の申請',\n        title: '表題',\n        viewImg: '画像の確認',\n        priLetter: 'メッセージ',\n        priLetterFromSomeone: '{name}からのメッセージ',\n        readLetter: 'わかりました。',\n        approve: '同意',\n        disapprove: '却下',\n        refuseSign: '拒否',\n        paperSign: '紙媒体の署名に変更',\n        refuseTip: '拒否理由を選択してください',\n        refuseReason: '拒否理由を記入することで相手方にお客様の問題を理解する手助けになり、契約手続きを早くします',\n        reasonWriteTip: '拒否理由を記入してください',\n        refuseReasonOther: '拒否理由の詳細(オプション) | 拒否理由の詳細(必須)',\n        refuseConfirm: '拒否',\n        refuseConfirmTip: 'あなたは「{reason}」という理由でこの契約書への署名を拒否します。続行したら、この契約書に署名できなくなります。続行しますか？',\n        waitAndThink: 'キャンセル',\n        signValidationTitle: '署名検証',\n        email: 'メールアドレス',\n        phoneNumber: '携帯電話',\n        password: 'パスワード',\n        verificationCode: '認証コード',\n        mailVerificationCode: '認証コード',\n        forgetPsw: 'パスワードをお忘れの場合',\n        if: '、どうか',\n        forgetPassword: 'パスワードをお忘れの場合',\n        rejectionVer: '検証の拒否',\n        msgTip: 'SMSを受け取っていない場合、',\n        voiceVerCode: '音声認証コード',\n        SMSVerCode: 'SMS認証コード',\n        or: 'または',\n        tryMore: 'を試してください',\n        emailVerCode: '電子メール認証コード',\n        SentSuccessfully: '送信完了',\n        intervalTip: '送信時間間隔が短すぎます',\n        signPsw: '署名パスワード',\n        useSignPsw: '契約パスワードで認証',\n        setSignPsw: '署名パスワードの設定',\n        useVerCode: '認証コードで認証',\n        inputVerifyCodeTip: '認証コードを入力してください',\n        inputSignPwdTip: '契約パスワードを入力してください',\n        signConfirmTip: {\n            1: '本当にこの契約書に署名しますか？',\n            2: '確定ボタンをタップしてすぐにこの契約書に署名する',\n            confirm: '署名の確認',\n        },\n        signSuc: '署名成功',\n        refuseSuc: '拒否成功',\n        approveSuc: '承認完了',\n        hdFile: '高解像度ファイルの確認',\n        otherOperations: 'その他の操作',\n        reviewDetails: '審査詳細',\n        close: '閉じる',\n        submitter: '提出者',\n        signatory: '署名者',\n        reviewSchedule: '審査進度',\n        signByPc: '{name}による署名',\n        signPageDescription: '第{index}ページ、計{total}ページ',\n        sealBySomeone: '{name}による捺印',\n        signDate: '署名日時',\n        download: 'ダウンロード',\n        signPage: 'ページ数：{page}ページ',\n        signNow: 'すぐ署名',\n        sender: '発信者',\n        signer: '契約者',\n        startSignTime: '契約発信日時',\n        signDeadLine: '契約期限',\n        authGuide: {\n            goToHome: 'トップページに戻る',\n            tip_1: '認証の完了後、契約書を閲覧し、署名ができるようになります。',\n            tip_2: '身分を使用して | 認証を行ってください。',\n            tip_3: '契約書の送付',\n            tip_4: '契約書の送付者に | 受け取り者を変更するように連絡してください。',\n            tip_5: 'お客様の認証した | 契約書が確認できません',\n            new_tip_1: '発信者のコンプライアンス要件に基づき、下記の手順を完了する必要があります：',\n            new_tip_2: '発信者のコンプライアンス要件に基づき、必要な書類は：',\n            new_tip_3: '下記の手順を完了してください。',\n            new_tip_4: '印章権限をお持ちであれば、自動で第2ステップにジャンプします',\n            entUserName: '氏名：',\n            idNumberForVerify: '身分証明書：',\n            realNameAuth: '実名認証',\n            applySeal: '印章の申請',\n            signContract: '契約書の署名',\n        },\n        switch: '切り替え',\n        rejectReasonList: {\n            // authReason: '実名認証をしたくありません/できません',\n            signOperateReason: '署名操作/検証操作に対して疑問をお持ちであれば、更に交流を進める必要があります',\n            termReason: '契約条件/内容に異議があれば、更に交流を進める必要があります',\n            explainReason: '契約内容に対してわからないことがあれば、事前に告知してください',\n            otherReason: 'その他（理由を記載してください）',\n        },\n        selectSignature: '署名の選択',\n        selectSigner: '署名者の選択',\n        pleaseScanToSign: 'AlipayもしくはWechatスキャンを使用して署名してください',\n        pleaseScanAliPay: 'AlipayアプリでQRコードを読み取り、署名してください',\n        pleaseScanWechat: 'WeChatアプリでQRコードを読み取り、署名してください',\n        requiredFaceSign: '契約書送信元がお客様に顔認証署名を要請しています',\n        requiredDualSign: '契約送信者が録音・録画認証を要求しています。',\n        verCodeVerify: '認証コードによる検証',\n        applyToSign: '契約書の署名申請',\n        autoRemindAfterApproval: '*審査に合格後、署名者に自動的にリマインダーを送信',\n        cannotSignBeforeApproval: '審査がまだ終わっていないため、署名できません。',\n        finishSignatureBeforeSign: '先に捺印/サインをしてから再度署名を確認してください',\n        uploadFileOnRightSite: 'アップロードしていない添付書類がある場合、まずは右側の欄で添付書類をアップロードしてください',\n        cannotApplySealNeedPay: '当該契約書はお客様が支払うものですので、他の人の捺印での申請を認めていません',\n        cannotOtherSealReason: 'この契約は顔認証による本人確認が必要なため、代理での押印はできません',\n        unlimitedNotice: 'この契約書の費用は使い放題です',\n        units: '{num}部',\n        contractToPrivate: '個人向け契約書',\n        contractToPublic: '企業向け契約書',\n        paySum: '共{sum}需要您支付',\n        payTotal: '共计{total}元.',\n        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',\n        contactToRecharge: '管理者主任に連絡してチャージしてください.',\n        deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます。',\n        needSignerPay: '契約送信者により着払い設定がされており、あなたは契約書送信料金の支払者に指定されています。',\n        recharge: 'リチャージ',\n        toSubmit: '提出',\n        appliedSeal: '印章使用申請書を提出しています',\n        noSeal: '印章がありません',\n        noSwitchSealNeedDistribute: '切り替え可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',\n        viewApproveProcess: '承認フローを確認',\n        approveProcess: '承認フロー',\n        noApproveContent: '承認書類が提出されていません',\n        knew: 'わかりました',\n        noSwitchSealNeedAppend: '現在切り替え可能な印章がありません。管理者主任に連絡して印章を追加してください。',\n        hadAutoSet: '別の{num}箇所で自動的に',\n        setThatSignature: '当該署名を置きます',\n        setThatSeal: '当該印章を置きます',\n        applyThatSeal: '当該印章を申請',\n        hasSetTip: '他の{index}箇所に自動配置',\n        hasSetSealTip: 'この印鑑は他の{index}箇所に自動的に置かれている',\n        hasSetSignatureTip: '署名は他の{index}か所に自動的に配置されました',\n        hasApplyForSealTip: 'この印鑑は他の{index}か所で自動申請されています',\n        savedOnLeftSite: '左側の署名欄に保存されました',\n        ridingSealMinLimit: '書類は1ページのみのため、割り印が押せません',\n        ridingSealMaxLimit: '146ページを超えているため、割り印をサポートしていません',\n        ridingSealMinOrMaxLimit: '書類は1ページのみか146ページを超えているため、割り印が押せません',\n        noSealForRiding: '使用可能な印章が登録されていないため、割り印を押せません',\n        noSwitchSealNeedAppendBySelf: '切り替え可能な印章が登録されていません。企業の管理コンソールで印章を追加してください。',\n        gotoAppendSeal: '印章を追加しにいく',\n        approvalFlowSuccessfulSet: '承認フロー設定完了',\n        mandate: '授権の同意',\n        loginToAppendSeal: 'パソコンを使用してベストサインにログインして、企業の管理コンソールで印章を追加してください',\n        signIdentityAs: '現在{person}の名義で契約書に署名しています',\n        enterNextContract: '次の契約書に入る',\n        fileList: 'ファイルリスト',\n        addSignerFile: '付属資料の追加',\n        signatureFinish: 'すでに全部捺印/署名しています',\n        dragSignatureTip: '下記の捺印/日時をファイルにドラッグ＆ドロップしてください。複数回可能です',\n        noticeToManager: '管理者に通知しました',\n        gotoAuthPerson: '個人認証を行う',\n        senderRequire: '発信者がお客様に要求しています',\n        senderRequireUseFollowIdentity: '発信者がお客様に下記の身分証明の一つを要求しています',\n        suggestToAuth: 'お客様は実名認証を行っていません。実名認証をした後署名するようにしてください',\n        contactEntAdmin: '企業の管理者主任に連絡してください',\n        setYourAccount: 'お客様のアカウントで',\n        authInfoUnMatchNeedResend: '契約書の署名を行ってください。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください',\n        noEntNameNeedResend: '契約企業名が指定されておらず、この契約書を署名することはできません。送信者に連絡して再度契約書の起稿を要求してください',\n        pleaseUse: '使用してください',\n        me: '私',\n        myself: '本人',\n        reAuthBtnTip: '私が現在の携帯番号の実質使用者です。',\n        reAuthBtnContent: '再実名登録後、このアカウントの元の実名での利用は却下されますので、ご確認ください。',\n        descNoSame1: ' の身分で契約書を署名',\n        descNoSame2: 'これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n        authInfoNoSame: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n        authInfoNoSame2: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',\n        goHome: '契約書リストページに戻る>>',\n        authInfo: '現在検出しているアカウントの実名IDは ',\n        authInfo2: '現在検出しているアカウントの実名IDは ',\n        in: 'に',\n        finishAuth: '実名が完了しています。コンプライアンスに則した契約書の署名に用いられます',\n        ask: '現在のアカウントで署名を続けますか？',\n        reAuthBtnText: 'はい。このアカウントで再度実名署名を行います',\n        changePhoneText: 'いいえ。発送者に連絡して署名の携帯番号を変更します',\n        changePhoneTip1: '発信者の要求で、連絡してください',\n        changePhoneTip2: '署名情報（携帯番号/氏名）を変更し、お客様による署名が指示されています。',\n        confirmOk: '確認',\n        goOnAuth: {\n            0: '認証の実行',\n            1: '実名認証を実行してください',\n            2: '実名認証を実行します',\n        },\n        signContractAfterAuth: {\n            0: '認証の完了後、契約書の署名ができるようになります。',\n            1: '認証が完了した後契約書に署名ができるようになります。',\n        },\n        useIdentity: '{name}の身分で',\n        inTheName: 'XX',\n        of: 'の',\n        identity: '身分',\n        nameIs: '氏名は',\n        IDNumIs: '身分証明番号は',\n        provideMoreAuthData: '更に認証資料を追加する',\n        leadToAuthBeforeSign: '認証が続けた後契約書に署名ができるようになります',\n        groupProxyAuthNeedMore: '現在の認証状態はグループが代理認証しています。もしも単独で契約書に署名する必要があれば実名認証資料を追加してください',\n        contactSender: '疑問がある場合発行者に連絡してください。',\n        note: '注意：',\n        identityInfo: 'ID情報',\n        signNeedCoincidenceInfo: '完全一致しないと契約書の署名はできません。',\n        needAuthPermissionContactAdmin: '実名認証権限がありません。管理者に連絡してください',\n        iHadReadContract: '閲読し、{alias}内容を熟知しました',\n        scrollToBottomTip: '最後のページまでスクロールする必要があります',\n        getVerCodeFirst: '先に認証コードを取得してください',\n        appScanVerify: 'ベストサインアプリの二次元コード検証',\n        downloadBSApp: 'ベストサインアプリをダウンロード',\n        scanned: '二次元コード成功',\n        confirmInBSApp: 'ベストサインアプリの中で署名を確認してください',\n        qrCodeExpired: '二次元コードが失効しています。リロードしてください',\n        appKey: 'アプリセキュリティ検証',\n        goToScan: '読み取る',\n        setNotificationInUserCenter: 'ユーザーセンターで通知方法を設定してください',\n        doNotWantUseVerCode: '認証コードを使いたくありません',\n        try: '試す',\n        retry: '再接続',\n        goToFaceVerify: '顔認証に入る',\n        faceExceedTimes: '当日の顔認証回数が上限に達しています',\n        returnBack: '戻る',\n        switchTo: '切り替え',\n        youCanChooseIdentityBlow: '以下の契約主体を選択することができます',\n        needDrawSignatureFirst: 'まだサインしていません。先に手書きサインを追加してください',\n        lacksSealNeedAppend: 'まだ印章を何も追加していません。先に印章を追加してください。',\n        manageSeal: '印章の管理',\n        needDistributeSealToSelf: '現在使用可能な印章がありません。先に自分が印章の所有者になるよう設定してください',\n        chooseSealAfterAuth: '上の印章を使いたくありません。実名認証後印章を変更できます',\n        appendDrawSignature: '手書きサインの追加',\n        senderUnFill: '（発信者が記入されていません)',\n        declare: '説明',\n        fileLessThan: '{num}M以下のファイルをアップロードしてください',\n        fileNeedUploadImg: 'アップロードする際はサポートしているフォーマットを使ってください',\n        serverError: 'サーバーエラーが発生しています。しばらくしてから試してください',\n        oldFormatTip: 'jpg、png、jpeg、pdf、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下としてください',\n        fileLimitFormatAndSize: '1つの資料画像は10枚を超えないようにしてください。',\n        fileFormatImage: 'jpg、png、jpegフォーマットをサポートし、1画像当たりの容量は20M以下とし、10枚までアップロードできます',\n        fileFormatFile: 'pdf、excel、word、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下とします',\n        signNeedKnow: '契約注意事項',\n        signNeedKnowFrom: '{sender}からの契約注意事項',\n        approvalInfo: '審査注意事項',\n        approveNeedKnowFrom: '{sender}-{sendEmployeeName}により提出された審査資料',\n        approveBeforeSend: '合同发送前审批',\n        approveBeforeSign: '合同签署前审批',\n        approveOperator: '承認者',\n        approvalOpinion: '承認メッセージ',\n        employeeDefault: '従業員',\n        setLabel: '設定タグ',\n        addRidingSeal: '割り印の追加',\n        delRidingSeal: '割り印の削除',\n        file: 'ファイル',\n        compressedFile: '圧縮ファイル',\n        attachmentContent: '付属内容',\n        pleaseClickView: '（クリックしてダウンロードしてください）',\n        downloadFile: 'ソースファイルをダウンロード',\n        noLabelPleaseAppend: 'タグがありません。企業の管理コンソールで追加してください。',\n        archiveTo: 'ファイリング先',\n        hadArchivedToFolder: '{who}の{folderName}フォルダーへの契約書の移動が完了しました',\n        pleaseScanToHandleWrite: 'WechatまたはスマホのQRコードリーダーで読み取り',\n        save: '保存',\n        remind: 'リマインダー',\n        riskTip: 'リスクに関するリマインダー',\n        chooseApplyPerson: '押印者の設定',\n        useSealByOther: '押印を委任',\n        getSeal: '押印権限を申請',\n        nowApplySealList: '以下の印章を要求しています',\n        nowAdminSealList: '以下の印章取得を申請中です',\n        chooseApplyPersonToDeal: '押印者を選択して下さい（この契約を引き続き閲覧し、フォローすることができます）。',\n        chooseAdminSign: '印章管理者の設定\t',\n        chooseTransferPerson: '他の人に署名を渡す',\n        chooseApplyPersonToMandate: '印章管理者を選択してください。この管理者による審査が完了したら、この印章を契約書に押印できるようになります。',\n        contactGroupAdminToDistributeSeal: '集団の管理者に印章を分配するよう連絡してください',\n        sealApplySentPleaseWait: '印章の分配申請を送信しました。審査が通るまでしばらくお待ちください。もしくはその他捺印の方法を選択することができます',\n        successfulSent: '送信完了',\n        authTip: {\n            t2: ['注意：', '完全一致しないと契約書の署名はできません。', '企業名', ' ID情報', 'が完全一致しないと契約書の署名はできません。'],\n            t3: '{x}はお客様に{text}の実名認証を実行するよう要求しています',\n            tCommon1: '{entName}の身分で',\n            tCommon2_1: '名前を{name}、身分証番号を{idCard}とします。',\n            tCommon2_2: '名前を{name}とします',\n            tCommon2_3: '身分証番号を{idCard}とします。',\n            viewAndSign1: '認証が完了した後契約書の閲覧及び署名ができるようになります。',\n            viewAndSignConflict: '{x}はお客様の{text}で契約書の確認と署名を行うよう要求しています。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください',\n        },\n        needSomeoneToSignature: '{x}により{y}が捺印されました',\n        needToSet: '捺印必要',\n        approver: '申請者：',\n        clickToSignature: 'ここをタップして署名',\n        transferToOtherToSign: '他の人に転送して署名',\n        signatureBy: '{x}による署名',\n        tipRightNumber: '正確な数字を入力してください',\n        tipRightIdCard: '18桁の中国本土居民身分証番号を正しく入力してください',\n        tipRightPhoneNumber: '11桁の携帯電話番号を正しく入力してください',\n        tip: '提示',\n        tipRequired: '値は必ず入力し空欄にできません',\n        confirm: '確定',\n        viewContractDetail: '契約内容の確認',\n        required: '必須項目',\n        optional: 'オプション',\n        decimalLimit: '小数点以下{x}桁まで',\n        intLimit: '整数の要求',\n        invalidContract: 'この契約ご同意すると以下の契約を無効にし：',\n        No: 'ナンバリング',\n        chooseFrom2: '差出人は2種類のスタンプを設定しておりますので、1種類お選びください',\n        crossPlatformCofirm: {\n            message: 'この契約書はクロスプラットフォーム署名が必要です。契約書データを国外に送信することに同意しますか',\n            title: 'データ承認',\n            confirmButtonText: '同意する',\n            cancelButtonText: 'キャンセル',\n        },\n        sealScope: '印章使用範囲',\n        currentContract: '現在の契約書',\n        allContract: 'すべての契約書',\n        docView: '契約プレビュー',\n        fixTextDisplay: 'ページの文字化けを修正',\n        allPage: '合計{num}ページ',\n        notJoinTip: '管理者に連絡して、企業のメンバーとして追加してもらってから署名してください',\n    },\n    signJa: {\n        beforeSignTip1: '送信者のリクエストにより、この企業名義で署名してください：',\n        beforeSignTip2: '送信者は{signer}を署名者に指定しています。情報が正しいことを確認したら署名できます。',\n        beforeSignTip3: '情報が正しくなければ、送信者に連絡して、指定署名者の情報を変更してください。',\n        beforeSignTip4: 'このアカウントで登録されている氏名は{currentUser}であり、送信者がリクエストした {signer} と異なります、 {signer} に変更しますか？',\n        beforeSignTip5: '現在のアカウントに紐付けされた氏名は{currentUser}であり、甲の指定する{signer}と一致しません',\n        beforeSignTip6: '実際の状況に基づき、甲の指定する {signer} に変更することを確認してください',\n        beforeSignTip7: 'または甲と相談の上、指定署名者を変更します',\n        entNamePlaceholder: '企業名を入力してください',\n        corporateNumberPlaceholder: '法人番号を入力してください',\n        corporateNumber: '法人番号',\n        singerNamePlaceholder: '署名者の氏名を入力してください',\n        singerName: '署名者氏名',\n        itsMe: 'は私本人です',\n        businessPic: '印鑑証明書',\n        waitApprove: 'レビュー中。レビューの進行状況を理解する必要がある場合は、メールでお問い合わせください：<EMAIL>',\n        wrongInformation: '情報が正しくありません',\n        confirmChange: '変更する',\n        communicateSender1: '変更せず、甲と相談する',\n        communicateSender2: 'キャンセルし、送信者と相談する',\n        createSeal: {\n            title: '氏名を入力',\n            tip: 'あなたの氏名を入力してください（スペースは改行で行えます）',\n            emptyErr: '氏名を入力してください',\n        },\n        areaRegister: '企業登録地',\n        jp: '日本',\n        cn: '中国大陸',\n        are: 'アラブ首長国連邦',\n        other: 'その他',\n        plsSelect: '選択してください',\n        tip1: '登録先は中国大陸部の企業で、ent.bestsign.cnで実名登録を完了する必要があります。中国大陸部以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',\n        tip2: '企業が中国大陸版に署名して実名認証を完了している場合は、ent.bestsign.cnに直接ログインし、関連サービスを簡単に利用できます。海外版に署名したデータは、中国大陸版とは完全に独立していることに注意してください。',\n        tip3: '現地の商業規制当局から取得した証明書番号を提供してください',\n        tip4: '次の手順に従います',\n        tip5: '1. 御社の担当カスタマーマネージャーに連絡し、企業実名認証の完了までご案内させていただきます。',\n        tip6: '「チャージ管理」をクリックします。',\n        tip7: '2. 御社とベストサインとの契約書のキャプチャ、または、カスタマーマネージャーとやり取りしたメールのキャプチャをアップロードしてください。',\n        tip8: '少なくとも1つの契約を購入し、購入履歴のスクリーンショットを保存します。',\n        tip9: '3. この方式は、日本および中国本土以外の企業のみ、ご利用いただけます。',\n        tip10: '4. 資料提出後、ベストサインは3営業日以内に認証を完了します。',\n        tip11: '重要なヒント',\n        tip12: '購入者はエンタープライズユーザーでなければなりません。',\n        tip13: '支払口座の企業フルネームは、記入した企業名と完全に一致している必要があります。',\n        tip14: '日本、中国大陸部以外の企業でなければ使用できません。',\n        comNum: '企業証明書番号',\n        buyRecord: '証明書資料',\n        selectArea: '企業登録先を選択してください',\n        uaeTip1: '登録先はアラブ首長国連邦の企業で、uae.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',\n        uaeTip2: '企業がアラブ首長国連邦版に署名して実名認証を完了している場合は、uae.bestsign.comに直接ログインして、関連サービスを簡単に利用できます。注意する必要があるのは、海外版に署名したデータは、アラブ首長国連邦版とは完全に独立しています。',\n        uaeTip3: '登録先はアラブ首長国連邦と中国大陸以外の企業で、ent.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',\n    },\n    signPC: {\n        commonSign: '署名の確認',\n        contractVerification: '署名認証',\n        VerCodeVerify: '認証コードによる検証',\n        QrCodeVerify: '二次元コード検証',\n        verifyTip: 'ベストサインでは現在お客様の安全なデジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',\n        verifyAllTip: 'ベストサインでは企業デジタル証書とお客様の個人デジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',\n        selectSeal: '印章の選択',\n        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',\n        toAddSealWithConsole: '電子公印は使用開始待ちです。他の印鑑を追加するには、コンソールへ移動してください。',\n        use: '使用',\n        toAddSeal: '印章を追加しにいく',\n        mySeal: '私の印章',\n        operationCompleted: '操作完了',\n        FDASign: {\n            date: '署名時間',\n            signerAdd: '追加',\n            signerEdit: '変更',\n            editTip: '注意：中国語氏名はピンイン入力してください。例：San Zhang（張三）',\n            inputNameTip: 'あなたの氏名を入力してください',\n            inputName: '英語または中国語ピンインを入力してください',\n            signerNameFillTip: '氏名のサインも記入する必要があります',\n            plsInput: '入力してください',\n            plsSelect: '選択してください',\n            customInput: '自由入力',\n        },\n        signPlaceBySigner: {\n            signGuide: '签署指导',\n            howDragSeal: '如何拖章',\n            howDragSignature: '如何拖签名',\n            iKnow: '我知道了',\n            step: {\n                one: '第一步：阅读合同',\n                two1: '第二步：点击“拖章”',\n                two2: '第二步：点击“拖签名”',\n                three: '第三步：点击“签署”按钮',\n            },\n            dragSeal: '拖章',\n            continueDragSeal: '继续拖章',\n            dragSignature: '拖签名',\n            continueDragSignature: '继续拖签名',\n            dragPlace: '按住此处拖动',\n            notRemind: '不再提醒',\n            signTip: {\n                one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',\n                two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。',\n            },\n            finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署',\n        },\n        continueOperation: {\n            success: '操作成功',\n            exitApproval: '承認を終了',\n            continueApproval: '承認を継続',\n            next: '次へ ',\n            none: 'ないです',\n            tip: '注意',\n            approvalProcess: '{totalNum}人の承認が必要で、そのうちの{passNum}人が承認済みです',\n            receiver: '受信先：',\n        },\n    },\n    signTip: {\n        contractDetail: '契約状態',\n        downloadBtn: 'アプリのダウンロード',\n        tips: '注意',\n        submit: '確定',\n        SigningCompleted: '署名成功',\n        submitCompleted: '他の人の処理待ち',\n        noTurnSign: '署名の順番が来ていないか、署名権限がないか、ログインステータスが期限切れです',\n        noRightSign: '契約書は署名中です、現在のユーザは署名操作が許可されていません',\n        noNeedSign: '内部決議契約書、署名は不要',\n        ApprovalCompleted: '承認完了',\n        contractRevoked: '当該{alias}は取り下げられました',\n        contractRefused: '当該{alias}は署名拒否されました',\n        linkExpired: '当該リンクは無効です',\n        contractClosed: '当該{alias}は契約期限切れです',\n        approvalReject: '当該{alias}の審査は却下されました',\n        approving: '{alias}は審査中です',\n        viewContract: '{alias}を確認',\n        viewContractList: '契約リストを見る',\n        needMeSign: '（署名待ち{num}件）',\n        downloadContract: '契約書のダウンロード',\n        sign: '署名',\n        signed: '署名',\n        approved: '審査',\n        approval: '審査',\n        person: '人',\n        personHas: '済み',\n        personHave: '済み',\n        personHasnot: '未',\n        personsHavenot: '未',\n        headsTaskDone: '{num}{done}{has}',\n        headsTaskNotDone: '{num}{not}{done}',\n        taskStatusBetween: '、',\n        cannotReview: '契約書が確認できません',\n        cannotDownload: '当該契約書は携帯電話でのダウンロードをサポートしていません。契約書が発信側の個人契約ストレージを使用しているため、ベストサインでは契約書を取得できません。',\n        privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません',\n        beenDeleted: 'お客様のアカウントはすでに企業管理者から削除されています',\n        unActive: 'アカウントの有効化を継続できません',\n        back: '戻る',\n        contratStatusDes: '{key}状況：',\n        contractConditionDes: '詳細：',\n        contractIng: '{alias}{key}中',\n        contractComplete: '{alias}{key}完了',\n        dataProduct: {\n            tip1: '{entName}各優良販売業者様/サプライヤー企業担当者様：',\n            tip2: 'この度、｛entName｝の安定した発展への貢献に感謝し、｛bankName｝と共同で、お客様のビジネスの発展を加速させるサプライチェーン金融サービスを開始することになりました。',\n            btnText: '社長にこの嬉しい知らせを伝えに行く',\n        },\n        signOnGoing: '署名{status}中',\n        operate: '契約操作',\n        freeContract: '初回の契約書送信を完了すると、更に無料の契約書部数を取得できます。',\n        sendContract: '契約書を送る',\n        congratulations: '{name}が{num}件の契約締結を完了したことをお祝いします，',\n        carbonSaving: '推定で{num}gのカーボン削減が達成されました.',\n        signGift: 'ベストサインより、{limit}まで利用可能な法人契約を{num}件贈呈いたします。',\n        followPublic: 'WeChat公式アカウントをフォローし、契約の最新情報を迅速に受け取ってください。',\n        congratulationsSingle: '{name}様、署名完了しました。',\n        carbonSavingSingle: '凡そ2002.4gの二酸化炭素排出量が削減されました。',\n        viewContractTip: '捺印者を変更する場合は、「詳細の確認」ボタンをクリックして契約詳細ページを開き、その後「捺印申請」ボタンをクリックしてください。',\n        congratulationsCn: '電子署名を選択していただきありがとうございます！',\n        carbonSavingSingleCn: '地球のために{num}gCO2eのカーボンを削減しました',\n        carbonVerification: '*「カーボンストップ」による科学的計算',\n    },\n    view: {\n        title: '契約書の確認',\n        ok: '完了',\n        cannotReview: '契約書が確認できません',\n        privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません',\n    },\n    prepare: {\n        sealArea: '捺印所',\n        senderNotice: '現在の契約書送信主体は：{entName}です。',\n        preSetDialogConfirm: 'わかりました。',\n        preSetDialogContact: '開通するようすぐにベストサインの販売スタッフに連絡する',\n        preSetDialogInfo: '契約書の事前設定に成功すると、システムはテンプレートに基づき対応する署名者情報、署名要件、署名位置、契約内容フィールドなどを自動的に入力します',\n        preSetDialogTitle: '契約書事前設定テンプレートとは何ですか？',\n        initialValues: '契約内容を基に事前設定する初期値',\n        proxyUpload: 'ローカル文書のアップロード後、契約発起側は選択する事ができます',\n        signHeaderTitle: '文書と契約者の追加',\n        step1: '手順1',\n        confirmSender: '発起側の確認',\n        step2: '手順2',\n        uploadFile: 'ファイルのアップロード',\n        step3: '手順3',\n        addSigner: '契約者の追加',\n        actionDemo: '操作デモ',\n        next: '次へ',\n        isUploadingErr: '文書のアップロードが完了していません。完了後継続して操作してください',\n        noUploadFileErr: 'アップロードが完了していません。アップロード後継続して操作してください',\n        noContractTitleErr: '契約書名称が未記入です。記入した後継続してください',\n        contractTypeErr: '現在の契約タイプは削除されています。契約タイプを再選択してください',\n        expiredDateErr: '契約有効時間に誤りがあります。修正後継続してください',\n        noExpiredDateErr: '署名期限を記入した後継続してください',\n        describeFieldsErr: '必須内容フィールドを記入した後継続してください',\n        noRecipientsErr: '少なくとも契約者1つを追加',\n        noAccountErr: 'アカウントは空欄に出来ません',\n        noUserNameErr: '氏名は空欄に出来ません',\n        noIDNumberErr: '身分証番号は空欄にできません',\n        accountFormatErr: 'フォーマットが正確でありません。正しいメールアドレスを入力してください',\n        userNameFormatErr: 'フォーマットが正確でありません。正確な氏名を入力してください',\n        enterpriseNameErr: '正確な企業名を入力してください',\n        idNumberForVerifyErr: 'フォーマットが正確でありません。正確な身分証を入力してください',\n        signerErr: '契約者に誤りがあります',\n        noSignerErr: '少なくとも署名者1名を追加',\n        lackAttachmentNameErr: '付属書類名を入力してください',\n        repeatRecipientsErr: '順番に署名しない場合、重複して契約者を追加することはできません',\n        innerContact: '内部担当者',\n        outerContact: '外部担当者',\n        search: '検索',\n        accountSelected: 'すでにアカウント選択済み',\n        groupNameAll: '全部',\n        unclassified: '未分類',\n        fileLessThan: '{num}M以下のファイルをアップロードしてください',\n        beExcel: 'Excelファイルをアップロードしてください',\n        usePdf: 'アップロードする際PDFファイルもしくは画像を使用してください',\n        usePdfFile: 'アップロードの際はPDFファイルをご使用ください',\n        fileNameMoreThan: 'ファイル名の長さが{num}を超えると、自動で切り取ります',\n        needAddSender: '本企業/本人が署名者に設定されていないので、契約書が送信されたら、あなたは契約書に署名しません。署名者として追加しますか？',\n        addSender: '署名者として追加',\n        tip: '注意',\n        cancel: 'キャンセル',\n    },\n    addReceiver: {\n        English: '英語',\n        Japanese: '日本語',\n        Chinese: '中国語',\n        Arabic: 'アラビア語',\n        setNoticelang: '署名通知の言語設定',\n        limitFaceConfigTip: '契約単価が低いため、この機能は利用できません。ベストサインにご連絡ください',\n        individual: '契約個人',\n        enterprise: '契約企業',\n        addInstructions: '契約注意事項の追加',\n        instructionsContent: '提出した資料はお客様が契約履行状態を追跡する助けを行い、業務の執行が正常かどうか判断します。設定後、署名者は必ず要求に基づき提出しなければなりません',\n        addContractingInfo: '契約主体の資料を提出',\n        contractingInfoContent: '提出した資料はお客様が契約者の主体資質を確認する助けを行い、業務の展開を開始/継続するかの判断をします。契約者がすでに同様の資料を提出している場合、再提出しなくてもかまいません',\n        payer: '支払い者',\n        handWriting: '手書き筆跡の識別のオン',\n        realName: '担当者は実名が必要です',\n        sameTip: '注意：契約者の企業名が完全一致している状態でのみ署名が可能です',\n        proxy: '相手側受付での受け取り',\n        aboradTip: '注意：当該契約者は域外の人であり、実名認証にはリスクがあります。まず先に当該者の身分を確認してください。',\n\n        busRole: '業務の役割',\n        busRoleTip: '契約者を識別する助けとなり、管理に便宜を図ります',\n        busRolePlaceholder: 'もしも従業員/販売業者であれば',\n        handWritingTip: 'この使用者は署名する際はっきりと識別可能な氏名を手書きしてもらうことで署名を完了できます',\n        instructions: '契約注意事項の追加　|　（255字以内）',\n        contractingParty: '契約主体資料',\n        signerPay: '本契約は当該署名者が支払います',\n        afterReadingTitle: '閲読完了後再署名する',\n        afterReading: '署名者は必ず閲読し、契約書内容を理解したうえで操作を続けることができます',\n        handWritingTips: 'この使用者の手書きの氏名と発信者指定もしくは実名情報の中の氏名と比較し、一致した段階で署名が完了します',\n        SsTitle: '捺印及び署名',\n        SsTip: '企業の印章を使用して署名する際、同時に追加する個人サインで署名を完了する必要があります。署名前に個人実名認証を完了する必要があります',\n        signature: '署名',\n        stamp: '捺印',\n        Ss: '捺印及び署名',\n        mutexError: '「{msg}」を設定済みです。先に「{msg}」の設定を削除した後再選択してください',\n        handWriteNotAllowed: '手書き署名は許されていません',\n        forceHandWrite: '手書き署名が必須です',\n        faceFirst: '顔認証が優先で、予備で認証コードの署名になります',\n        faceVerify: '顔認証署名が必須です',\n        attachmentRequired: '契約書付属資料の追加',\n        newAttachmentRequired: '契約主体資料を提出',\n        attachmentError: '契約付属資料名称は同じものにできません',\n        receiver: '携帯電話/メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',\n        receiverJa: 'メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',\n        orderSignLabel: '署名の順序',\n        contactAddress: '担当者アドレス帳',\n        signOrder: '順序で署名する',\n        account: 'アカウント',\n        accountPlaceholder: '携帯電話/メールアドレス（必須項目）',\n        accountPlaceholderJa: 'メールアドレス（必須項目）',\n        accountReceptionCollection: '受付での受け取り',\n        accountReceptionCollectionTip1: '相手方の具体的なアカウントを知らないか相手方がアカウントを持っていません',\n        accountReceptionCollectionTip2: '受付での受け取りを選択してください',\n        signSubjectPerson: '契約主体：個人',\n        nameTips: '氏名（オプション、契約身分の確認で用います）',\n        requiredNameTips: '氏名（必須項目、契約身分の確認で用います）',\n        entOperatorNameTips: '氏名（オプション）',\n        needAuth: '実名が必要です',\n        operatorNeedAuth: '担当者は実名が必要です',\n        signSubjectEnt: '契約主体：企業',\n        entNameTips: '企業名（必須項目、契約身分の確認で用います）',\n        operator: '担当者',\n        sign: '署名',\n        more: 'もっと',\n        faceFirstTips: '署名する際システムは初期設定で顔認証による検証を採用しています。顔認証が通らない回数が1日の上限を超えた場合自動で認証コードによる検証に切り替わります',\n        mustFace: '顔認証署名が必須です',\n        mustHandWrite: '手書き署名が必須です',\n        fillIDNumber: '身分証明書番号',\n        fillNoticeCall: '通知携帯電話',\n        fillNoticeCallTips: '通知携帯電話を記入してください',\n        addNotice: 'メッセージの追加',\n        attachTips: '契約書付属資料の追加',\n        faceSign: '顔認証署名が必須です',\n        faceSignTips: 'この使用者が署名を完了するには、顔認証する必要があります（顔認証署名は当面の間、中国大陸の住民のみ利用可能です）',\n        handWriteNotAllowedTips: 'この使用者はすでに設定しているサインもしくは初期設定の字体サインを選択して署名を完了する必要があります',\n        handWriteTips: 'この使用者は手書きサインで署名を完了する必要があります',\n        idNumberTips: '契約身分の確認で用います',\n        verifyBefore: '文書を確認する前に身分を認証',\n        verify: '身分の認証',\n        verifyTips: '最多20文字',\n        verifyTips2: 'お客様はこの認証情報をこの使用者に提供しなければなりません',\n        sendToThirdPlatform: '第三者プラットフォームに送信',\n        platFormName: 'プラットフォーム名',\n        fillThirdPlatFormName: 'プラットフォーム名を入力してください',\n        attach: '資料',\n        attachName: '資材名',\n        exampleID: '例：身分証写真',\n        attachInfo: '備考',\n        attachInfoTips: '例：本人の身分証写真をアップロードしてください',\n        addAttachRequire: '資料の追加',\n        addSignEnt: '契約企業の追加',\n        addSignPerson: '契約個人の追加',\n        selectContact: '担当者の選択',\n        save: '保存',\n        searchVerify: '認証の確認',\n        fillImageContentTips: '画像の内容を入力してください',\n        ok: '確定',\n        findContact: '契約書の中から下記の契約者を探します',\n        signer: '契約者',\n        signerTips: 'ヒント：契約者の選択後、プラットフォームはサイン及び捺印の位置決めを補助します。',\n        add: '追加',\n        notAdd: '追加なし',\n        cc: '副本発信',\n        notNeedAuth: '実名は必要ありません',\n        operatorNotNeedAuth: '担当者の実名は必要ありません',\n        extracting: '抽出中',\n        autoFill: '署名者の自動記入',\n        failExtracting: '契約者が抽出できません',\n        idNumberForVerifyErr: '正確な身分証を入力してください',\n        noAccountErr: 'アカウントは空欄に出来ません',\n        noUserNameErr: '氏名は空欄に出来ません',\n        noIDNumberErr: '身分証番号は空欄にできません',\n        noEntNameErr: '企業名は空欄にできません',\n        accountFormatErr: '正しいメールアドレスを入力してください',\n        enterpriseNameErr: '正確な会社名を入力してください',\n        userNameFormatErr: '正確な氏名を入力してください',\n        riskCues: 'リスクに関して',\n        riskCuesMsg: 'もしも契約者が実名署名をせず、文書において紛糾が発生した場合、署名者の身分を証明する証拠を自分で用意する必要があります。リスクを避ける必要がある場合、「実名が必要です」を選択してください。',\n        confirmBtnText: '「実名が必要です」を選択',\n        cancelBtnText: '「実名は必要ありません」を選択',\n        attachLengthErr: 'お客様は1人の署名者に対し、最大50件の添付ファイル要求しか追加できません',\n        collapse: '折りたたむ',\n        expand: '展開する',\n        delete: '削除',\n        saySomething: 'なにか話しましょう',\n        addImage: 'ファイルの追加',\n        addImageTips: '（Word/PDF及び画像をサポート、ファイル3つ以下とします）',\n        give: '先',\n        fileMax: 'アップロード数量が上限を超えています',\n        signerLimit: '現在のバージョンは{limit}個を超える相対的署名/副本発信先をサポートしていません。',\n        showExamle: 'サンプル画像を確認',\n        downloadExamle: 'サンプルファイルをダウンロード',\n    },\n    addReceiverGuide: {\n        notRemind: '次回はお知らせしないでください',\n        sign: '自署',\n        entSign: '企業の自署',\n        stamp: '捺印',\n        stampSign: '捺印及び署名',\n        requestSeal: '業務照合印',\n        'guideTitle': '新しい署名者を追加する方法',\n        'receiverType': '署名者が契約に参加する方法を選択する必要があります (6 つのうちの 1 つを選択してください)：',\n        'asEntSign': '企業を代表してサインオンします：',\n        'sealSub': '署名者は、契約書に公印または契約書用特別印鑑等を押印する必要があります',\n        'signatureSub': '法人または役員が、企業に代わって契約に署名します。企業は、署名者が契約を閲覧できないように契約を譲渡する権利を有します',\n        'vipOnly': 'プレミアムバージョンが利用可能',\n        'stampSub': '署名者は、印鑑を押すだけでなく、企業を代表して署名する必要があります',\n        'confirmSeal': '企業を代表して業務用チェックスタンプを使用する',\n        'confirmSealSub': '財務諸表や確認書などの書類は、最初に確認されてから押印されます',\n        'asPersonSign': '個人に代わって署名するには:',\n        'asPersonSignTip': 'ビジネスを代表するものではなく、個人のみを代表して署名されています',\n        'asPersonSignDesc': 'ローン契約、参入および退出などの署名者の私的な契約',\n        'scanSign': 'コードをスキャンして署名する',\n        'scanSignDesc': '契約書を発行する際に署名者を書く必要はありません. 契約書が発行された後、誰でもコードをスキャンするか、検査ページのリンクをクリックして署名することができます. 物流書類の受領シナリオに適用できます',\n        'selectSignTypeTip': '最初に署名者が契約に参加する方法を選択してください',\n    },\n    linkContract: {\n        title: '契約書の関連付け',\n        connectMore: '関連するその他の契約書',\n        placeholder: '契約書番号を入力してください',\n        revoke: '署名抹消済み',\n        overdue: '署名期限切れ',\n        approvalNotPassed: '審査却下',\n        reject: '署名拒否済み',\n        signing: '署名中',\n        complete: '完了済み',\n        approvaling: '審査中',\n        disconnect: '関連付けの解除',\n        disconnectSuccess: '関連付けの解除完了',\n        connectLimit: '関連付け契約書数の上限は100部です',\n    },\n    field: {\n        fieldTip: {\n            title: '署名位置の欠落',\n            error: '下記の契約書指定署名位置がありません（{type}）',\n            add: 'フィールドの追加',\n            continue: '送信を継続',\n        },\n        accountCharge: {\n            notice: 'この契約書は参加アカウント数に基づき課金します',\n            able: '正常に送信できます',\n            unable: '使用アカウント数が不足しています。ベストサインのカスタマーサービスに連絡してください',\n            notify: 'この契約書はすべての契約書先に英語で通知を送信します',\n            noNotify: {\n                1: 'この契約書は契約の関連通知を発信しません',\n                2: '（署名・審査・副本発信・契約有効期限などの通知SMS及びメールを含む）',\n            },\n        },\n        ridingStamp: '割り印',\n        watermark: 'すかし',\n        senderSignature: '捺印者署名',\n        optional: 'オプション',\n        clickDecoration: '契約書装飾をタップ',\n        decoration: '契約書装飾',\n        sysError: 'システムビジーです。時間をおいてから試してください',\n        partedMarkedError: '「捺印とサイン」の契約者を指定しました。必ず同時に捺印とサインを指定してください',\n        fieldTitle: '全部で{length}部の契約書に署名位置指定が必要です',\n        send: '発信',\n        contractDispatchApply: '契約書の送信申請',\n        contractNeedYouSign: 'このファイルはお客様の署名が必要です',\n        ifSignRightNow: 'すぐに署名しますか',\n        signRightNow: 'すぐに署名します',\n        signLater: '後で署名します',\n        signaturePositionErr: '各署名者に署名位置を指定してください',\n        sendSucceed: '送信完了',\n        confirm: '確定',\n        cancel: 'キャンセル',\n        qrCodeTips: '署名後読み取り、すぐに署名詳細・署名の有効性及びこの契約書が改ざんされているかどうかの検証を確認できます',\n        pagesField: '第{currentPage}ページ、計{totalPages}}ページ',\n        suitableWidth: '幅の調整',\n        signCheck: '署名の確認',\n        locateSignaturePosition: '署名位置指定',\n        locateTips: '素早く署名位置を指定することができます。現在、各署名者の最初の署名位置の指定のみをサポートしています',\n        step1: '手順1',\n        selectSigner: '契約者を選択',\n        step2: '手順2',\n        dragSignaturePosition: '署名位置をドラッグ',\n        signingField: '署名フィールド',\n        docTitle: 'ファイル',\n        totalPages: 'ページ数：{totalPages}ページ',\n        receiver: '受信先',\n        delete: '削除',\n        deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます',\n        unlimitedNotice: 'この契約書の費用は使い放題です',\n        charge: '課金',\n        units: '{num}部',\n        contractToPrivate: '個人向け契約書',\n        contractToPublic: '企業向け契約書',\n        costTips: {\n            1: '企業向け契約書：署名者（送信者を含まず）の中に企業アカウントがある契約書',\n            2: '個人向け契約書：署名者（送信者を含まず）の中に企業アカウントがない契約書',\n            3: '課金部数はファイル数に基づき計算します',\n            4: '課金部数 = ファイル部数 X ユーザーグループ（列）の一括インポート',\n        },\n        costInfo: '契約の送信に成功すると、直ちに費用が控除され、契約の完了、期限超過、撤回、または署名拒否は返金されません。',\n        toCharge: 'リチャージする',\n        contractNeedCharge: {\n            1: '使用可能な契約部数が不足しているため、送信できません',\n            2: '使用可能な契約部数が不足しています。管理者主任にリチャージするよう連絡してください',\n        },\n        chooseApprover: '審査者の選択：',\n        nextStep: '次へ',\n        submitApproval: '審査提出',\n        autoSendAfterApproval: '*審査に合格後、契約書を自動で送信します',\n        chooseApprovalFlow: '審査フロー1つを選択してください',\n        completeApprovalFlow: 'お客様の提出した審査フローに不備があります。追加してから再度提出してください。',\n        viewPrivateLetter: 'メッセージの確認',\n        addPrivateLetter: 'メッセージの追加',\n        append: '追加',\n        privateLetter: 'メッセージ',\n        signNeedKnow: '契約注意事項',\n        maximum5M: '5M以下のファイルをアップロードしてください',\n        uploadServerFailure: 'サーバーへのアップロードに失敗しました',\n        uploadFailure: 'アップロードエラーです',\n        pager: 'ページ番号',\n        seal: '捺印',\n        signature: '署名',\n        signDate: '署名日時',\n        text: 'テキスト',\n        date: '日付',\n        qrCode: '二次元コード',\n        number: '数字',\n        dynamicTable: '動的フォーマット',\n        terms: '契約条項',\n        checkBox: 'チェックボックス',\n        radioBox: 'ラジオボタン',\n        image: '画像',\n    },\n    addressBook: {\n        innerMember: {\n            title: '企業内部メンバー',\n            tips: '送信者が素早く内部の担当者を探せるように企業メンバー情報を調整します',\n            operation: '管理コンソールに入る',\n        },\n        outerContacts: {\n            title: '外部企業担当者',\n            tips: '業務の展開がうまくいくよう、お客様の協力パートナーを招待し、事前に実名登録をしてください',\n            operation: '協力パートナーを招待する',\n        },\n        myContacts: {\n            title: '私の担当者',\n            tips: '署名者の情報が正確かつ間違いがないように、担当者を修正します',\n            operation: 'ユーザーセンターに入る',\n        },\n        selected: 'すでにアカウント選択済み',\n        search: '検索',\n        loadMore: 'もっと読む',\n        end: '全ロード完了',\n    },\n    dataBoxInvite: {\n        title: '協力パートナーを招待する',\n        step1: 'お客様の協力パートナーとのリンクを共有し、事前に企業を構築',\n        step2: 'リンク/二次元コードの授権後の協力パートナーがアドレス帳に表示されます',\n        step3: '「ファイル＋」の中で協力パートナーをさらに管理します',\n        imgName: '採集した二次元コードを共有',\n        saveQrcode: '二次元コードをローカルに保存',\n        copy: 'コピー',\n        copySuccess: 'コピー完了',\n        copyFailed: 'コピー失敗',\n    },\n    shareView: {\n        title: 'レビューのために転送',\n        account: '電話番号/メールアドレス',\n        role: 'レビュー担当者の役割',\n        note: '備考',\n        link: 'リンクを：',\n        signerMessage: '署名者のメッセージ',\n        rolePlaceholder: '例：会社の法務、部門のリーダーなど',\n        notePlaceholder: 'レビュー担当者へのメッセージ、200文字以内',\n        generateLink: 'リンクを生成',\n        regenerateLink: 'リンクを再生成',\n        saveQrcode: 'ミニプログラムコードを保存',\n        inputAccount: '電話番号またはメールアドレスを入力してください',\n        inputCorrectAccount: '正しい携帯電話番号またはメールアドレスを入力してください',\n        accountInputTip: 'リンクが正常に開かれるようにするために、契約審査者の情報を正確に入力してください',\n        shareLinkTip1: 'ミニプログラムコードを保存するか',\n        shareLinkTip: 'リンクをコピーして、レビュー担当者と共有してください',\n        linkTip1: '契約の本文は機密情報ですので、必要のない限り外部に漏らさないでください',\n        linkTip2: 'リンクの有効期限は2日間です；リンクを再生成した後、以前のリンクは自動的に無効になります',\n    },\n    recoverSpecialSeal: {\n        title: '印章が使用できません',\n        description1: '送信者は契約時にこの印章を使用して契約書に署名するよう要求していますが、御社はこの印章をすでに削除しています。確実に署名が行えるよう、管理者にこの印章の復元を要求してください',\n        description2: 'もし、この印章が本当に今後の使用に適さない場合、送信者に連絡して印章のデザインの修正を要求した後、再度契約書に署名するようにしてください。',\n        postRecover: '印章の復元申請',\n        note: 'タップ後管理者が印章復元申請のSMS/メールを受け取ると同時に、同時に印章の管理ページで申請を見ることができます。',\n        requestSend: '復元申請の提出完了',\n    },\n    paperSign: {\n        title: '紙媒体の署名を使用',\n        stepText: ['次へ', '紙媒体署名確認', '確定'],\n        needUploadFile: '先にスキャンファイルをアップロードしてください',\n        uploadError: 'アップロードエラーです',\n        cancel: 'キャンセル',\n        downloadPaperFile: '紙媒体署名ファイルの取得',\n        step0: {\n            title: '先に契約書をダウンロード印刷し、物理的な捺印をした上で、送信者へ郵送する必要があります',\n            address: '郵送住所：',\n            contactName: '受取者氏名：',\n            contactPhone: '受取者連絡方法：',\n            defaultValue: 'オフライン方法で送信者から受け取ってください',\n        },\n        step1: {\n            title0: '手順1：ダウンロード及び紙媒体契約書の印刷',\n            title0Desc: ['ダウンロードし印刷した契約書にはすでに署名している電子印章の図案が含まれていなければなりません。紙の署名書類を入手してください。', '获取纸质签文件。'],\n            title1: '手順2：印章の捺印',\n            title1Desc: '紙媒体契約書に契約書に有効な会社の印章を捺印してください。',\n            title2: ['手順3：', 'スキャンデータのアップロード，', '署名ページに戻り、署名ボタンをタップして、紙媒体署名を完了してください'],\n            title2Desc: ['紙媒体契約書をスキャンしデータ（PDFファイル）に変換後アップロードしてください。', '電子契約書内にはお客様の印章図案は表示されませんが、この操作を行ったことは記録されます。'],\n        },\n        step2: {\n            title: ['紙媒体契約書のスキャンデータ（PDFファイル）をアップロードしてください', '紙媒体契約書をダウンロードし署名した後、再度確定ボタンをタップして、紙媒体契約書のプロセスを終了します。'],\n            uploadFile: 'スキャンファイルのアップロード',\n            getCodeVerify: '契約署名検証の取得',\n            isUploading: 'アップロード中...',\n        },\n    },\n    allowPaperSignDialog: {\n        title: '紙契約書への署名を許可する',\n        content: 'この契約書は{senderName}が{receiverName}へ送信したもので、紙形式での署名が許可されています。',\n        tip: '契約書を印刷して、会社の責任者が紙の契約書に押印することも選択できます。',\n        icon: '紙契約書へ署名に変更 >>',\n        goSign: '電子署名する',\n        cancel: 'キャンセル',\n    },\n    sealInconformityDialog: {\n        errorSeal: {\n            title: '印章の提示',\n            tip: '現在の印章画像がお客様の企業身分と適合していないことが検出されました。現在の印章画像識別の結果は：',\n            tip1: '検出された企業印章と企業名：',\n            tip2: 'このまま現在の印章画像を使用しますか？',\n            tip3: '発信者の要求に基づき、お客様が使用する企業名は：',\n            tip4: 'の印章です',\n            tip5: '印章が要件に適合していることを確認してください。さもなければ契約書の有効性に影響を及ぼす恐れがあります。',\n            tip6: '適合していません。印章が発信者の要件に適合しているかどうか確認してください。',\n            guide: '正確な印章をアップロードするには >>',\n            next: '続けて使用',\n            tip7: 'また、あなたの印鑑名は規範に合わず、「{keyWord}」という文字が付いています。',\n            tip8: '印鑑名が規範に合わないことが検出されました。「{keyWord}」という文字が付いていますが、引き続き使用しますか？',\n        },\n        exampleSeal: {\n            title: '印章図案のアップロード方法',\n            way1: ['方法1：', '1、白い紙の上で実物の印章図案を捺印します。', '2、撮影し、画像をプラットフォームにアップロードします'],\n            way2: ['方法2：', '図のように直接プラットフォームの電子印章機能で生成します：'],\n            errorWay: ['間違った方法：', '印章を手で持つ', '関係ない画像', '営業許可証'],\n        },\n        confirm: '確認',\n        cancel: 'キャンセル',\n    },\n    addSealDialog: {\n        title: '印章画像を追加',\n        dec1: 'ローカルフォルダから印章画像を1枚選択してください（形式：JPG、JPEG、PNG等）。システムがこの印章画像を現在の契約書に合成します。',\n        dec2: 'その後、「署名」ボタンをクリックして署名検証を通過すると、押印が完了します。',\n        updateNewSeal: '新しい印章をアップロード',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE;IACFC,aAAa,EAAE,4HAA4H;IAC3IC,QAAQ,EAAE,IAAI;IACdC,uBAAuB,EAAE,6BAA6B;IACtDC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE;MACdC,WAAW,EAAE,OAAO;MACpBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,aAAa;MACvBC,IAAI,EAAE,8DAA8D;MACpEC,IAAI,EAAE,kDAAkD;MACxDC,IAAI,EAAE,yCAAyC;MAC/CC,IAAI,EAAE,8GAA8G;MACpHC,IAAI,EAAE,+BAA+B;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,SAAS;MACvBC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACff,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,QAAQ;MACpBG,IAAI,EAAE,8DAA8D;MACpEC,IAAI,EAAE,kDAAkD;MACxDC,IAAI,EAAE;IACV,CAAC;IACDU,UAAU,EAAE,gBAAgB;IAC5BC,aAAa,EAAE,mBAAmB;IAClCC,gBAAgB,EAAE,yBAAyB;IAC3CC,YAAY,EAAE,mDAAmD;IACjEC,wBAAwB,EAAE,YAAY;IACtCC,mBAAmB,EAAE,eAAe;IACpCC,qBAAqB,EAAE,UAAU;IACjCC,UAAU,EAAE,YAAY;IACxBC,gBAAgB,EAAE,yBAAyB;IAC3CC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,OAAO;IACfjC,IAAI,EAAE,OAAO;IACbkC,OAAO,EAAE,8CAA8C;IACvDC,eAAe,EAAE,4DAA4D;IAC7EC,qBAAqB,EAAE,uEAAuE;IAC9FC,iBAAiB,EAAE,YAAY;IAC/BC,cAAc,EAAE,YAAY;IAC5BC,uBAAuB,EAAE,sBAAsB;IAC/CC,8BAA8B,EAAE,eAAe;IAC/CC,kBAAkB,EAAE,mBAAmB;IACvCC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE,+BAA+B;IAC9CC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,yCAAyC;IACzDC,QAAQ,EAAE,OAAO;IACjBC,aAAa,EAAE,SAAS;IACxBC,qBAAqB,EAAE,6BAA6B;IACpDC,OAAO,EAAE,6BAA6B;IACtCC,SAAS,EAAE,QAAQ;IACnBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,aAAa;IAC3BC,WAAW,EAAE,yEAAyE;IACtFC,cAAc,EAAE,gBAAgB;IAChCC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,uDAAuD;IACxEC,eAAe,EAAE,gEAAgE;IACjFC,iBAAiB,EAAE,uBAAuB;IAC1CC,aAAa,EAAE,6EAA6E;IAC5FC,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE,6BAA6B;IACzCC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,gBAAgB;IAChCC,YAAY,EAAE,uCAAuC;IACrDC,SAAS,EAAE,0BAA0B;IACrCC,YAAY,EAAE,2DAA2D;IACzEC,gBAAgB,EAAE,iCAAiC;IACnDC,WAAW,EAAE,OAAO;IACpBC,gBAAgB,EAAE,sHAAsH;IACxIC,YAAY,EAAE,iEAAiE;IAC/EC,SAAS,EAAE,IAAI;IACf1E,UAAU,EAAE,QAAQ;IACpB2E,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE,wCAAwC;IACpDC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,mBAAmB;IAC9BC,SAAS,EAAE,oBAAoB;IAC/BC,SAAS,EAAE,mDAAmD;IAC9DC,OAAO,EAAE,wBAAwB;IACjCC,aAAa,EAAE,uBAAuB;IACtCC,aAAa,EAAE,6BAA6B;IAC5CC,eAAe,EAAE,8BAA8B;IAC/CC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,OAAO;IACnBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,aAAa;IAC3BC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,YAAY;IAC5BC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,SAAS;IACxBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,QAAQ;IACtBC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,iBAAiB;IAC9BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,SAAS;IACjBC,cAAc,EAAE,aAAa;IAC7BC,WAAW,EAAE,QAAQ;IACrBC,KAAK,EAAE,KAAK;IACZC,YAAY,EAAE,wBAAwB;IACtCC,QAAQ,EAAE,aAAa;IACvBC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,+BAA+B;IACxCC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,oBAAoB,EAAE,gBAAgB;IACtCC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,+CAA+C;IAC7DC,cAAc,EAAE,eAAe;IAC/BC,iBAAiB,EAAE,8BAA8B;IACjDC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,oEAAoE;IACtFC,YAAY,EAAE,OAAO;IACrBC,mBAAmB,EAAE,MAAM;IAC3BC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,OAAO;IACjBC,gBAAgB,EAAE,OAAO;IACzBC,oBAAoB,EAAE,OAAO;IAC7BC,SAAS,EAAE,cAAc;IACzBC,EAAE,EAAE,MAAM;IACVC,cAAc,EAAE,cAAc;IAC9BC,YAAY,EAAE,OAAO;IACrBC,MAAM,EAAE,iBAAiB;IACzBC,YAAY,EAAE,SAAS;IACvBC,UAAU,EAAE,UAAU;IACtBC,EAAE,EAAE,KAAK;IACTC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,YAAY;IAC1BC,gBAAgB,EAAE,MAAM;IACxBC,WAAW,EAAE,cAAc;IAC3BC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,UAAU;IACtBC,kBAAkB,EAAE,gBAAgB;IACpCC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE;MACZ,CAAC,EAAE,kBAAkB;MACrB,CAAC,EAAE,0BAA0B;MAC7BC,OAAO,EAAE;IACb,CAAC;IACDC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,aAAa;IACrBC,eAAe,EAAE,QAAQ;IACzBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,aAAa;IACvBC,mBAAmB,EAAE,yBAAyB;IAC9CC,aAAa,EAAE,aAAa;IAC5BC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,gBAAgB;IAC1BC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE;MACPC,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,+BAA+B;MACtCC,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,mCAAmC;MAC1CC,KAAK,EAAE,wBAAwB;MAC/BC,SAAS,EAAE,uCAAuC;MAClDC,SAAS,EAAE,4BAA4B;MACvCC,SAAS,EAAE,iBAAiB;MAC5BC,SAAS,EAAE,gCAAgC;MAC3CC,WAAW,EAAE,KAAK;MAClBC,iBAAiB,EAAE,QAAQ;MAC3BC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,OAAO;MAClBC,YAAY,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE,MAAM;IACdC,gBAAgB,EAAE;MACd;MACAC,iBAAiB,EAAE,yCAAyC;MAC5DC,UAAU,EAAE,gCAAgC;MAC5CC,aAAa,EAAE,iCAAiC;MAChDC,WAAW,EAAE;IACjB,CAAC;IACDC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,QAAQ;IACtBC,gBAAgB,EAAE,mCAAmC;IACrDC,gBAAgB,EAAE,+BAA+B;IACjDC,gBAAgB,EAAE,+BAA+B;IACjDC,gBAAgB,EAAE,0BAA0B;IAC5CC,gBAAgB,EAAE,wBAAwB;IAC1CC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,UAAU;IACvBC,uBAAuB,EAAE,2BAA2B;IACpDC,wBAAwB,EAAE,yBAAyB;IACnDC,yBAAyB,EAAE,4BAA4B;IACvDC,qBAAqB,EAAE,gDAAgD;IACvEC,sBAAsB,EAAE,wCAAwC;IAChEC,qBAAqB,EAAE,oCAAoC;IAC3DC,eAAe,EAAE,iBAAiB;IAClCC,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,SAAS;IAC5BC,gBAAgB,EAAE,SAAS;IAC3BC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,8BAA8B;IACzCC,iBAAiB,EAAE,uBAAuB;IAC1CC,kBAAkB,EAAE,yCAAyC;IAC7DC,aAAa,EAAE,+CAA+C;IAC9DC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,iBAAiB;IAC9BC,MAAM,EAAE,UAAU;IAClBC,0BAA0B,EAAE,mDAAmD;IAC/EC,kBAAkB,EAAE,UAAU;IAC9BC,cAAc,EAAE,OAAO;IACvBC,gBAAgB,EAAE,gBAAgB;IAClCC,IAAI,EAAE,QAAQ;IACdC,sBAAsB,EAAE,0CAA0C;IAClEC,UAAU,EAAE,gBAAgB;IAC5BC,gBAAgB,EAAE,WAAW;IAC7BC,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,kBAAkB;IAC7BC,aAAa,EAAE,6BAA6B;IAC5CC,kBAAkB,EAAE,4BAA4B;IAChDC,kBAAkB,EAAE,6BAA6B;IACjDC,eAAe,EAAE,gBAAgB;IACjCC,kBAAkB,EAAE,wBAAwB;IAC5CC,kBAAkB,EAAE,+BAA+B;IACnDC,uBAAuB,EAAE,oCAAoC;IAC7DC,eAAe,EAAE,8BAA8B;IAC/CC,4BAA4B,EAAE,6CAA6C;IAC3EC,cAAc,EAAE,WAAW;IAC3BC,yBAAyB,EAAE,WAAW;IACtCC,OAAO,EAAE,OAAO;IAChBC,iBAAiB,EAAE,+CAA+C;IAClEC,cAAc,EAAE,2BAA2B;IAC3CC,iBAAiB,EAAE,UAAU;IAC7BC,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,iBAAiB;IAClCC,gBAAgB,EAAE,uCAAuC;IACzDC,eAAe,EAAE,YAAY;IAC7BC,cAAc,EAAE,SAAS;IACzBC,aAAa,EAAE,iBAAiB;IAChCC,8BAA8B,EAAE,4BAA4B;IAC5DC,aAAa,EAAE,wCAAwC;IACvDC,eAAe,EAAE,mBAAmB;IACpCC,cAAc,EAAE,YAAY;IAC5BC,yBAAyB,EAAE,4EAA4E;IACvGC,mBAAmB,EAAE,6DAA6D;IAClFC,SAAS,EAAE,UAAU;IACrBC,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,oBAAoB;IAClCC,gBAAgB,EAAE,2CAA2C;IAC7DC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,2CAA2C;IACxDC,cAAc,EAAE,sDAAsD;IACtEC,eAAe,EAAE,sDAAsD;IACvEC,MAAM,EAAE,gBAAgB;IACxBC,QAAQ,EAAE,sBAAsB;IAChCC,SAAS,EAAE,sBAAsB;IACjCC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,sCAAsC;IAClDC,GAAG,EAAE,oBAAoB;IACzBC,aAAa,EAAE,wBAAwB;IACvCC,eAAe,EAAE,2BAA2B;IAC5CC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,sCAAsC;IACvDC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;MACN,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,eAAe;MAClB,CAAC,EAAE;IACP,CAAC;IACDC,qBAAqB,EAAE;MACnB,CAAC,EAAE,2BAA2B;MAC9B,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,IAAI;IACfC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,SAAS;IAClBC,mBAAmB,EAAE,aAAa;IAClCC,oBAAoB,EAAE,0BAA0B;IAChDC,sBAAsB,EAAE,4DAA4D;IACpFC,aAAa,EAAE,sBAAsB;IACrCC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,MAAM;IACpBC,uBAAuB,EAAE,uBAAuB;IAChDC,8BAA8B,EAAE,2BAA2B;IAC3DC,gBAAgB,EAAE,sBAAsB;IACxCC,iBAAiB,EAAE,wBAAwB;IAC3CC,eAAe,EAAE,kBAAkB;IACnCC,aAAa,EAAE,oBAAoB;IACnCC,aAAa,EAAE,kBAAkB;IACjCC,OAAO,EAAE,UAAU;IACnBC,cAAc,EAAE,yBAAyB;IACzCC,aAAa,EAAE,2BAA2B;IAC1CC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBC,2BAA2B,EAAE,wBAAwB;IACrDC,mBAAmB,EAAE,iBAAiB;IACtCC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,KAAK;IACZC,cAAc,EAAE,QAAQ;IACxBC,eAAe,EAAE,oBAAoB;IACrCC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,MAAM;IAChBC,wBAAwB,EAAE,qBAAqB;IAC/CC,sBAAsB,EAAE,+BAA+B;IACvDC,mBAAmB,EAAE,gCAAgC;IACrDC,UAAU,EAAE,OAAO;IACnBC,wBAAwB,EAAE,0CAA0C;IACpEC,mBAAmB,EAAE,+BAA+B;IACpDC,mBAAmB,EAAE,WAAW;IAChCC,YAAY,EAAE,iBAAiB;IAC/BC,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,4BAA4B;IAC1CC,iBAAiB,EAAE,kCAAkC;IACrDC,WAAW,EAAE,iCAAiC;IAC9CC,YAAY,EAAE,+DAA+D;IAC7EC,sBAAsB,EAAE,4BAA4B;IACpDC,eAAe,EAAE,4DAA4D;IAC7EC,cAAc,EAAE,0DAA0D;IAC1EC,YAAY,EAAE,QAAQ;IACtBC,gBAAgB,EAAE,mBAAmB;IACrCC,YAAY,EAAE,QAAQ;IACtBC,mBAAmB,EAAE,yCAAyC;IAC9DC,iBAAiB,EAAE,SAAS;IAC5BC,iBAAiB,EAAE,SAAS;IAC5BC,eAAe,EAAE,KAAK;IACtBC,eAAe,EAAE,SAAS;IAC1BC,eAAe,EAAE,KAAK;IACtBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,QAAQ;IACxBC,iBAAiB,EAAE,MAAM;IACzBC,eAAe,EAAE,sBAAsB;IACvCC,YAAY,EAAE,gBAAgB;IAC9BC,mBAAmB,EAAE,+BAA+B;IACpDC,SAAS,EAAE,SAAS;IACpBC,mBAAmB,EAAE,wCAAwC;IAC7DC,uBAAuB,EAAE,6BAA6B;IACtDC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,eAAe;IACxBC,iBAAiB,EAAE,QAAQ;IAC3BC,cAAc,EAAE,OAAO;IACvBC,OAAO,EAAE,SAAS;IAClBC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,eAAe;IACjCC,uBAAuB,EAAE,0CAA0C;IACnEC,eAAe,EAAE,WAAW;IAC5BC,oBAAoB,EAAE,WAAW;IACjCC,0BAA0B,EAAE,wDAAwD;IACpFC,iCAAiC,EAAE,0BAA0B;IAC7DC,uBAAuB,EAAE,4DAA4D;IACrFC,cAAc,EAAE,MAAM;IACtBC,OAAO,EAAE;MACLC,EAAE,EAAE,CAAC,KAAK,EAAE,uBAAuB,EAAE,KAAK,EAAE,OAAO,EAAE,wBAAwB,CAAC;MAC9EC,EAAE,EAAE,mCAAmC;MACvCC,QAAQ,EAAE,eAAe;MACzBC,UAAU,EAAE,+BAA+B;MAC3CC,UAAU,EAAE,eAAe;MAC3BC,UAAU,EAAE,qBAAqB;MACjCC,YAAY,EAAE,gCAAgC;MAC9CC,mBAAmB,EAAE;IACzB,CAAC;IACDC,sBAAsB,EAAE,mBAAmB;IAC3CC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,gBAAgB,EAAE,YAAY;IAC9BC,qBAAqB,EAAE,YAAY;IACnCC,WAAW,EAAE,UAAU;IACvBC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE,6BAA6B;IAC7CC,mBAAmB,EAAE,wBAAwB;IAC7CC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE,iBAAiB;IAC9B7O,OAAO,EAAE,IAAI;IACb8O,kBAAkB,EAAE,SAAS;IAC7BC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,aAAa;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,eAAe,EAAE,uBAAuB;IACxCC,EAAE,EAAE,QAAQ;IACZC,WAAW,EAAE,oCAAoC;IACjDC,mBAAmB,EAAE;MACjBC,OAAO,EAAE,kDAAkD;MAC3DhS,KAAK,EAAE,OAAO;MACdiS,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE;IACtB,CAAC;IACDC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,aAAa;IAC7BC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EAChB,CAAC;EACDC,MAAM,EAAE;IACJC,cAAc,EAAE,+BAA+B;IAC/CC,cAAc,EAAE,gDAAgD;IAChEC,cAAc,EAAE,wCAAwC;IACxDC,cAAc,EAAE,mFAAmF;IACnGC,cAAc,EAAE,0DAA0D;IAC1EC,cAAc,EAAE,4CAA4C;IAC5DC,cAAc,EAAE,uBAAuB;IACvCC,kBAAkB,EAAE,cAAc;IAClCC,0BAA0B,EAAE,eAAe;IAC3CC,eAAe,EAAE,MAAM;IACvBC,qBAAqB,EAAE,iBAAiB;IACxCC,UAAU,EAAE,OAAO;IACnBC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,qEAAqE;IAClFC,gBAAgB,EAAE,aAAa;IAC/BC,aAAa,EAAE,MAAM;IACrBC,kBAAkB,EAAE,aAAa;IACjCC,kBAAkB,EAAE,iBAAiB;IACrCC,UAAU,EAAE;MACR9T,KAAK,EAAE,OAAO;MACdqR,GAAG,EAAE,+BAA+B;MACpC0C,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE,OAAO;IACrBC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBzb,IAAI,EAAE,4IAA4I;IAClJC,IAAI,EAAE,gHAAgH;IACtHC,IAAI,EAAE,+BAA+B;IACrCC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kDAAkD;IACxDC,IAAI,EAAE,mBAAmB;IACzBqb,IAAI,EAAE,uEAAuE;IAC7EC,IAAI,EAAE,sCAAsC;IAC5CC,IAAI,EAAE,sCAAsC;IAC5CC,KAAK,EAAE,kCAAkC;IACzCC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,6BAA6B;IACpCC,KAAK,EAAE,yCAAyC;IAChDC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,OAAO;IAClBC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE,mJAAmJ;IAC5JC,OAAO,EAAE,4HAA4H;IACrIC,OAAO,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACJC,UAAU,EAAE,OAAO;IACnBC,oBAAoB,EAAE,MAAM;IAC5BC,aAAa,EAAE,YAAY;IAC3BC,YAAY,EAAE,UAAU;IACxBC,SAAS,EAAE,2DAA2D;IACtEC,YAAY,EAAE,iEAAiE;IAC/EC,UAAU,EAAE,OAAO;IACnBC,aAAa,EAAE,2BAA2B;IAC1CC,oBAAoB,EAAE,2CAA2C;IACjEC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,WAAW;IACtBC,MAAM,EAAE,MAAM;IACdC,kBAAkB,EAAE,MAAM;IAC1BC,OAAO,EAAE;MACLhY,IAAI,EAAE,MAAM;MACZiY,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,uCAAuC;MAChDC,YAAY,EAAE,iBAAiB;MAC/BC,SAAS,EAAE,uBAAuB;MAClCC,iBAAiB,EAAE,oBAAoB;MACvCC,QAAQ,EAAE,UAAU;MACpBpC,SAAS,EAAE,UAAU;MACrBqC,WAAW,EAAE;IACjB,CAAC;IACDC,iBAAiB,EAAE;MACfC,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE,MAAM;MACnBC,gBAAgB,EAAE,OAAO;MACzBC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;QACFC,GAAG,EAAE,UAAU;QACfC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE;MACX,CAAC;MACDC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,MAAM;MACxBC,aAAa,EAAE,KAAK;MACpBC,qBAAqB,EAAE,OAAO;MAC9BC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE;QACLV,GAAG,EAAE,2BAA2B;QAChCW,GAAG,EAAE;MACT,CAAC;MACD5R,yBAAyB,EAAE;IAC/B,CAAC;IACD6R,iBAAiB,EAAE;MACfC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,OAAO;MACrBC,gBAAgB,EAAE,OAAO;MACzBC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,MAAM;MACZ7G,GAAG,EAAE,IAAI;MACT8G,eAAe,EAAE,2CAA2C;MAC5DC,QAAQ,EAAE;IACd;EACJ,CAAC;EACDT,OAAO,EAAE;IACLU,cAAc,EAAE,MAAM;IACtBC,WAAW,EAAE,YAAY;IACzBC,IAAI,EAAE,IAAI;IACVre,MAAM,EAAE,IAAI;IACZse,gBAAgB,EAAE,MAAM;IACxBC,eAAe,EAAE,UAAU;IAC3BC,UAAU,EAAE,wCAAwC;IACpDC,WAAW,EAAE,iCAAiC;IAC9CC,UAAU,EAAE,eAAe;IAC3BC,iBAAiB,EAAE,MAAM;IACzBC,eAAe,EAAE,qBAAqB;IACtCC,eAAe,EAAE,qBAAqB;IACtCC,WAAW,EAAE,YAAY;IACzBC,cAAc,EAAE,oBAAoB;IACpCC,cAAc,EAAE,sBAAsB;IACtCC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,YAAY;IAC1BC,gBAAgB,EAAE,UAAU;IAC5BC,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,YAAY;IAC9BrhB,IAAI,EAAE,IAAI;IACVshB,MAAM,EAAE,IAAI;IACZ7d,QAAQ,EAAE,IAAI;IACd8d,QAAQ,EAAE,IAAI;IACdne,MAAM,EAAE,GAAG;IACXoe,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,GAAG;IACjBC,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,kBAAkB;IACjCC,gBAAgB,EAAE,kBAAkB;IACpCC,iBAAiB,EAAE,GAAG;IACtBne,YAAY,EAAE,aAAa;IAC3Boe,cAAc,EAAE,+EAA+E;IAC/FC,cAAc,EAAE,wEAAwE;IACxFC,WAAW,EAAE,8BAA8B;IAC3CC,QAAQ,EAAE,mBAAmB;IAC7BC,IAAI,EAAE,IAAI;IACVC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,KAAK;IAC3BC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE,gBAAgB;IAClCC,WAAW,EAAE;MACT9hB,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,2FAA2F;MACjG8hB,OAAO,EAAE;IACb,CAAC;IACDC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,mCAAmC;IACjDC,YAAY,EAAE,QAAQ;IACtBC,eAAe,EAAE,mCAAmC;IACpDC,YAAY,EAAE,2BAA2B;IACzCC,QAAQ,EAAE,4CAA4C;IACtDC,YAAY,EAAE,2CAA2C;IACzDC,qBAAqB,EAAE,mBAAmB;IAC1CC,kBAAkB,EAAE,6BAA6B;IACjDC,eAAe,EAAE,kEAAkE;IACnFC,iBAAiB,EAAE,0BAA0B;IAC7CC,oBAAoB,EAAE,8BAA8B;IACpDC,kBAAkB,EAAE;EACxB,CAAC;EACDre,IAAI,EAAE;IACF4C,KAAK,EAAE,QAAQ;IACf0b,EAAE,EAAE,IAAI;IACR7f,YAAY,EAAE,aAAa;IAC3Bqe,cAAc,EAAE;EACpB,CAAC;EACDyB,OAAO,EAAE;IACLC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,0BAA0B;IACxCC,mBAAmB,EAAE,SAAS;IAC9BC,mBAAmB,EAAE,6BAA6B;IAClDC,gBAAgB,EAAE,yEAAyE;IAC3FC,iBAAiB,EAAE,sBAAsB;IACzCC,aAAa,EAAE,kBAAkB;IACjCC,WAAW,EAAE,iCAAiC;IAC9CC,eAAe,EAAE,WAAW;IAC5BC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,QAAQ;IACvBC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,MAAM;IAClB1E,IAAI,EAAE,IAAI;IACV2E,cAAc,EAAE,oCAAoC;IACpDC,eAAe,EAAE,qCAAqC;IACtDC,kBAAkB,EAAE,2BAA2B;IAC/CC,eAAe,EAAE,mCAAmC;IACpDC,cAAc,EAAE,4BAA4B;IAC5CC,gBAAgB,EAAE,oBAAoB;IACtCC,iBAAiB,EAAE,yBAAyB;IAC5CC,eAAe,EAAE,eAAe;IAChCC,YAAY,EAAE,gBAAgB;IAC9BC,aAAa,EAAE,aAAa;IAC5BC,aAAa,EAAE,gBAAgB;IAC/BC,gBAAgB,EAAE,qCAAqC;IACvDC,iBAAiB,EAAE,gCAAgC;IACnDC,iBAAiB,EAAE,iBAAiB;IACpCC,oBAAoB,EAAE,iCAAiC;IACvDC,SAAS,EAAE,aAAa;IACxBC,WAAW,EAAE,eAAe;IAC5BC,qBAAqB,EAAE,gBAAgB;IACvCC,mBAAmB,EAAE,iCAAiC;IACtDC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,OAAO;IACrBC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,cAAc;IAC/BC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,KAAK;IACnB5Q,YAAY,EAAE,4BAA4B;IAC1C6Q,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE,iCAAiC;IACzCC,UAAU,EAAE,0BAA0B;IACtCC,gBAAgB,EAAE,+BAA+B;IACjDC,aAAa,EAAE,+DAA+D;IAC9EC,SAAS,EAAE,UAAU;IACrBrN,GAAG,EAAE,IAAI;IACTlX,MAAM,EAAE;EACZ,CAAC;EACDwkB,WAAW,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,OAAO;IACfC,aAAa,EAAE,WAAW;IAC1BC,kBAAkB,EAAE,uCAAuC;IAC3DC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,MAAM;IAClBC,eAAe,EAAE,WAAW;IAC5BC,mBAAmB,EAAE,4EAA4E;IACjGC,kBAAkB,EAAE,YAAY;IAChCC,sBAAsB,EAAE,0FAA0F;IAClHC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,QAAQ,EAAE,aAAa;IACvBC,OAAO,EAAE,iCAAiC;IAC1CC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,sDAAsD;IAEjEC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,0BAA0B;IACtCC,kBAAkB,EAAE,iBAAiB;IACrCC,cAAc,EAAE,8CAA8C;IAC9DC,YAAY,EAAE,sBAAsB;IACpCC,gBAAgB,EAAE,QAAQ;IAC1BC,SAAS,EAAE,iBAAiB;IAC5BC,iBAAiB,EAAE,YAAY;IAC/BC,YAAY,EAAE,sCAAsC;IACpDC,eAAe,EAAE,qDAAqD;IACtEC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,oEAAoE;IAC3EpiB,SAAS,EAAE,IAAI;IACfqiB,KAAK,EAAE,IAAI;IACXC,EAAE,EAAE,QAAQ;IACZC,UAAU,EAAE,4CAA4C;IACxDC,mBAAmB,EAAE,gBAAgB;IACrCC,cAAc,EAAE,YAAY;IAC5BC,SAAS,EAAE,0BAA0B;IACrCC,UAAU,EAAE,YAAY;IACxBC,kBAAkB,EAAE,YAAY;IAChCC,qBAAqB,EAAE,WAAW;IAClCC,eAAe,EAAE,qBAAqB;IACtC/I,QAAQ,EAAE,iDAAiD;IAC3DgJ,UAAU,EAAE,4CAA4C;IACxDC,cAAc,EAAE,OAAO;IACvBC,cAAc,EAAE,UAAU;IAC1BC,SAAS,EAAE,SAAS;IACpB9lB,OAAO,EAAE,OAAO;IAChB+lB,kBAAkB,EAAE,oBAAoB;IACxCC,oBAAoB,EAAE,eAAe;IACrCC,0BAA0B,EAAE,UAAU;IACtCC,8BAA8B,EAAE,sCAAsC;IACtEC,8BAA8B,EAAE,mBAAmB;IACnDC,iBAAiB,EAAE,SAAS;IAC5BC,QAAQ,EAAE,wBAAwB;IAClCC,gBAAgB,EAAE,uBAAuB;IACzCC,mBAAmB,EAAE,WAAW;IAChCC,QAAQ,EAAE,SAAS;IACnBC,gBAAgB,EAAE,aAAa;IAC/BC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,wBAAwB;IACrCC,QAAQ,EAAE,KAAK;IACfnqB,IAAI,EAAE,IAAI;IACVoqB,IAAI,EAAE,KAAK;IACXC,aAAa,EAAE,6EAA6E;IAC5FC,QAAQ,EAAE,YAAY;IACtBC,aAAa,EAAE,YAAY;IAC3BC,YAAY,EAAE,SAAS;IACvBC,cAAc,EAAE,QAAQ;IACxBC,kBAAkB,EAAE,iBAAiB;IACrCC,SAAS,EAAE,UAAU;IACrBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,0DAA0D;IACxEC,uBAAuB,EAAE,qDAAqD;IAC9EC,aAAa,EAAE,6BAA6B;IAC5CC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,gBAAgB;IAC9BC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,+BAA+B;IAC5CC,mBAAmB,EAAE,gBAAgB;IACrCC,YAAY,EAAE,WAAW;IACzBC,qBAAqB,EAAE,oBAAoB;IAC3CC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,yBAAyB;IACzCC,gBAAgB,EAAE,OAAO;IACzBC,UAAU,EAAE,SAAS;IACrBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,QAAQ;IACvB/U,IAAI,EAAE,IAAI;IACVgV,YAAY,EAAE,OAAO;IACrBC,oBAAoB,EAAE,gBAAgB;IACtC3I,EAAE,EAAE,IAAI;IACR4I,WAAW,EAAE,oBAAoB;IACjC1gB,MAAM,EAAE,KAAK;IACb2gB,UAAU,EAAE,0CAA0C;IACtDC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,MAAM;IACdC,EAAE,EAAE,MAAM;IACVC,WAAW,EAAE,YAAY;IACzBC,mBAAmB,EAAE,gBAAgB;IACrCC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,aAAa;IAC7BrH,oBAAoB,EAAE,iBAAiB;IACvCN,YAAY,EAAE,gBAAgB;IAC9BC,aAAa,EAAE,aAAa;IAC5BC,aAAa,EAAE,gBAAgB;IAC/B0H,YAAY,EAAE,cAAc;IAC5BzH,gBAAgB,EAAE,qBAAqB;IACvCE,iBAAiB,EAAE,iBAAiB;IACpCD,iBAAiB,EAAE,gBAAgB;IACnCyH,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,gGAAgG;IAC7GC,cAAc,EAAE,cAAc;IAC9BC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,uCAAuC;IACxDC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdpsB,MAAM,EAAE,IAAI;IACZqsB,YAAY,EAAE,WAAW;IACzBC,QAAQ,EAAE,SAAS;IACnBC,YAAY,EAAE,kCAAkC;IAChDC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,oBAAoB;IAC7BC,WAAW,EAAE,8CAA8C;IAC3DC,UAAU,EAAE,WAAW;IACvBC,cAAc,EAAE;EACpB,CAAC;EACDC,gBAAgB,EAAE;IACdtO,SAAS,EAAE,iBAAiB;IAC5Bxf,IAAI,EAAE,IAAI;IACV+tB,OAAO,EAAE,OAAO;IAChBvF,KAAK,EAAE,IAAI;IACXwF,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE,OAAO;IACpB,YAAY,EAAE,eAAe;IAC7B,cAAc,EAAE,mDAAmD;IACnE,WAAW,EAAE,kBAAkB;IAC/B,SAAS,EAAE,qCAAqC;IAChD,cAAc,EAAE,6DAA6D;IAC7E,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,oCAAoC;IAChD,aAAa,EAAE,yBAAyB;IACxC,gBAAgB,EAAE,iCAAiC;IACnD,cAAc,EAAE,gBAAgB;IAChC,iBAAiB,EAAE,mCAAmC;IACtD,kBAAkB,EAAE,2BAA2B;IAC/C,UAAU,EAAE,gBAAgB;IAC5B,cAAc,EAAE,qGAAqG;IACrH,mBAAmB,EAAE;EACzB,CAAC;EACDC,YAAY,EAAE;IACVpmB,KAAK,EAAE,UAAU;IACjBqmB,WAAW,EAAE,aAAa;IAC1B/sB,WAAW,EAAE,gBAAgB;IAC7BgtB,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,QAAQ;IACjBC,iBAAiB,EAAE,MAAM;IACzBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAE,SAAS;IACrBC,iBAAiB,EAAE,WAAW;IAC9BC,YAAY,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE;MACNjnB,KAAK,EAAE,SAAS;MAChBknB,KAAK,EAAE,4BAA4B;MACnC1C,GAAG,EAAE,UAAU;MACfpsB,QAAQ,EAAE;IACd,CAAC;IACD+uB,aAAa,EAAE;MACXC,MAAM,EAAE,yBAAyB;MACjCC,IAAI,EAAE,WAAW;MACjBC,MAAM,EAAE,4CAA4C;MACpDC,MAAM,EAAE,4BAA4B;MACpCC,QAAQ,EAAE;QACN,CAAC,EAAE,sBAAsB;QACzB,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,eAAe,EAAE,OAAO;IACxBlW,QAAQ,EAAE,OAAO;IACjBmW,eAAe,EAAE,WAAW;IAC5BC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,2BAA2B;IACrCC,iBAAiB,EAAE,0CAA0C;IAC7DC,UAAU,EAAE,8BAA8B;IAC1C3sB,IAAI,EAAE,IAAI;IACV4sB,qBAAqB,EAAE,UAAU;IACjCC,mBAAmB,EAAE,oBAAoB;IACzCC,cAAc,EAAE,WAAW;IAC3BC,YAAY,EAAE,UAAU;IACxBC,SAAS,EAAE,SAAS;IACpBC,oBAAoB,EAAE,oBAAoB;IAC1CC,WAAW,EAAE,MAAM;IACnB9lB,OAAO,EAAE,IAAI;IACbtI,MAAM,EAAE,OAAO;IACfquB,UAAU,EAAE,sDAAsD;IAClEC,UAAU,EAAE,qCAAqC;IACjDC,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,OAAO;IAClBC,uBAAuB,EAAE,QAAQ;IACjCC,UAAU,EAAE,oDAAoD;IAChExM,KAAK,EAAE,KAAK;IACZ/W,YAAY,EAAE,QAAQ;IACtBiX,KAAK,EAAE,KAAK;IACZuM,qBAAqB,EAAE,WAAW;IAClCC,YAAY,EAAE,SAAS;IACvBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,sBAAsB;IAClC7Q,QAAQ,EAAE,KAAK;IACfjf,MAAM,EAAE,IAAI;IACZyN,kBAAkB,EAAE,wCAAwC;IAC5DR,eAAe,EAAE,iBAAiB;IAClC8iB,MAAM,EAAE,IAAI;IACZ7iB,KAAK,EAAE,QAAQ;IACfC,iBAAiB,EAAE,SAAS;IAC5BC,gBAAgB,EAAE,SAAS;IAC3B4iB,QAAQ,EAAE;MACN,CAAC,EAAE,sCAAsC;MACzC,CAAC,EAAE,sCAAsC;MACzC,CAAC,EAAE,qBAAqB;MACxB,CAAC,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE,uDAAuD;IACjEC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;MAChB,CAAC,EAAE,4BAA4B;MAC/B,CAAC,EAAE;IACP,CAAC;IACDC,cAAc,EAAE,SAAS;IACzBjsB,QAAQ,EAAE,IAAI;IACdksB,cAAc,EAAE,MAAM;IACtBC,qBAAqB,EAAE,sBAAsB;IAC7CC,kBAAkB,EAAE,kBAAkB;IACtCC,oBAAoB,EAAE,yCAAyC;IAC/DC,iBAAiB,EAAE,UAAU;IAC7BC,gBAAgB,EAAE,UAAU;IAC5BC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,OAAO;IACtBhc,YAAY,EAAE,QAAQ;IACtBic,SAAS,EAAE,wBAAwB;IACnCC,mBAAmB,EAAE,qBAAqB;IAC1CC,aAAa,EAAE,aAAa;IAC5BC,KAAK,EAAE,OAAO;IACd/rB,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfkF,QAAQ,EAAE,MAAM;IAChB6mB,IAAI,EAAE,MAAM;IACZlsB,IAAI,EAAE,IAAI;IACVmsB,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,UAAU;IACxBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACTC,WAAW,EAAE;MACT7qB,KAAK,EAAE,UAAU;MACjBuY,IAAI,EAAE,oCAAoC;MAC1CuS,SAAS,EAAE;IACf,CAAC;IACDC,aAAa,EAAE;MACX/qB,KAAK,EAAE,SAAS;MAChBuY,IAAI,EAAE,8CAA8C;MACpDuS,SAAS,EAAE;IACf,CAAC;IACDE,UAAU,EAAE;MACRhrB,KAAK,EAAE,OAAO;MACduY,IAAI,EAAE,gCAAgC;MACtCuS,SAAS,EAAE;IACf,CAAC;IACDG,QAAQ,EAAE,cAAc;IACxBhN,MAAM,EAAE,IAAI;IACZiN,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE;IACXprB,KAAK,EAAE,cAAc;IACrBqc,KAAK,EAAE,+BAA+B;IACtCE,KAAK,EAAE,qCAAqC;IAC5CE,KAAK,EAAE,4BAA4B;IACnC4O,OAAO,EAAE,eAAe;IACxBC,UAAU,EAAE,gBAAgB;IAC5BC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE;IACP1rB,KAAK,EAAE,YAAY;IACnBvE,OAAO,EAAE,cAAc;IACvBkwB,IAAI,EAAE,YAAY;IAClBlgB,IAAI,EAAE,IAAI;IACVmgB,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE,WAAW;IAC1BC,eAAe,EAAE,mBAAmB;IACpCC,eAAe,EAAE,wBAAwB;IACzCC,YAAY,EAAE,QAAQ;IACtBC,cAAc,EAAE,SAAS;IACzBX,UAAU,EAAE,eAAe;IAC3BY,YAAY,EAAE,yBAAyB;IACvCC,mBAAmB,EAAE,8BAA8B;IACnDC,eAAe,EAAE,0CAA0C;IAC3DC,aAAa,EAAE,kBAAkB;IACjCC,YAAY,EAAE,4BAA4B;IAC1CC,QAAQ,EAAE,qCAAqC;IAC/CC,QAAQ,EAAE;EACd,CAAC;EACDC,kBAAkB,EAAE;IAChBzsB,KAAK,EAAE,YAAY;IACnB0sB,YAAY,EAAE,yFAAyF;IACvGC,YAAY,EAAE,uEAAuE;IACrFC,WAAW,EAAE,SAAS;IACtBnhB,IAAI,EAAE,2DAA2D;IACjEohB,WAAW,EAAE;EACjB,CAAC;EACDrsB,SAAS,EAAE;IACPR,KAAK,EAAE,WAAW;IAClB8sB,QAAQ,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC;IACjCC,cAAc,EAAE,yBAAyB;IACzCC,WAAW,EAAE,aAAa;IAC1B7yB,MAAM,EAAE,OAAO;IACf8yB,iBAAiB,EAAE,cAAc;IACjCC,KAAK,EAAE;MACHltB,KAAK,EAAE,6CAA6C;MACpDmtB,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,QAAQ;MACrBC,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAE;IAClB,CAAC;IACDjR,KAAK,EAAE;MACHkR,MAAM,EAAE,uBAAuB;MAC/BC,UAAU,EAAE,CAAC,kEAAkE,EAAE,UAAU,CAAC;MAC5FC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,+BAA+B;MAC3CC,MAAM,EAAE,CAAC,MAAM,EAAE,iBAAiB,EAAE,qCAAqC,CAAC;MAC1EC,UAAU,EAAE,CAAC,2CAA2C,EAAE,8CAA8C;IAC5G,CAAC;IACDrR,KAAK,EAAE;MACHvc,KAAK,EAAE,CAAC,sCAAsC,EAAE,sDAAsD,CAAC;MACvGwc,UAAU,EAAE,iBAAiB;MAC7BqR,aAAa,EAAE,WAAW;MAC1BC,WAAW,EAAE;IACjB;EACJ,CAAC;EACDC,oBAAoB,EAAE;IAClB/tB,KAAK,EAAE,eAAe;IACtBguB,OAAO,EAAE,6DAA6D;IACtE3c,GAAG,EAAE,sCAAsC;IAC3C4c,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,QAAQ;IAChB/zB,MAAM,EAAE;EACZ,CAAC;EACDg0B,sBAAsB,EAAE;IACpBC,SAAS,EAAE;MACPpuB,KAAK,EAAE,OAAO;MACdqR,GAAG,EAAE,mDAAmD;MACxDzY,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,sDAAsD;MAC5DC,IAAI,EAAE,wCAAwC;MAC9Co1B,KAAK,EAAE,qBAAqB;MAC5BpW,IAAI,EAAE,OAAO;MACb3D,IAAI,EAAE,4CAA4C;MAClDC,IAAI,EAAE;IACV,CAAC;IACD+Z,WAAW,EAAE;MACTtuB,KAAK,EAAE,eAAe;MACtBuuB,IAAI,EAAE,CAAC,MAAM,EAAE,wBAAwB,EAAE,6BAA6B,CAAC;MACvEC,IAAI,EAAE,CAAC,MAAM,EAAE,+BAA+B,CAAC;MAC/CC,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO;IACtD,CAAC;IACDhsB,OAAO,EAAE,IAAI;IACbtI,MAAM,EAAE;EACZ,CAAC;EACDu0B,aAAa,EAAE;IACX1uB,KAAK,EAAE,SAAS;IAChB2uB,IAAI,EAAE,uEAAuE;IAC7EC,IAAI,EAAE,wCAAwC;IAC9CC,aAAa,EAAE;EACnB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}