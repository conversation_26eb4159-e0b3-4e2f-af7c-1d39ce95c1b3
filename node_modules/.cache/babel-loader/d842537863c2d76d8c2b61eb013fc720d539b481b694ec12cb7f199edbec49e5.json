{"ast": null, "code": "import { mapMutations } from 'vuex';\nexport default {\n  props: {\n    value: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      btnList: [{\n        icon: 'shangchuan1',\n        text: '新建文档对话',\n        type: 'upload'\n      }, {\n        icon: 'wenzi',\n        text: '输入对话内容',\n        type: 'text'\n      }, {\n        icon: 'a-151-app-jiaocaiyinyong',\n        text: '划词引用',\n        type: 'quote'\n      }, {\n        icon: 'qieyingwen',\n        text: '输出内容语言',\n        type: 'lang'\n      }, {\n        icon: 'Hubbletishi',\n        text: '建议问题',\n        type: 'suggestion'\n      }],\n      currentBtn: '',\n      svgPath: '',\n      startPoint: {},\n      endPoint: {}\n    };\n  },\n  computed: {\n    endPointStyle() {\n      const left = this.endPoint.x - 5;\n      let top = this.endPoint.y - 10;\n      if (this.currentBtn === 'upload') {\n        top = this.endPoint.y;\n      }\n      return {\n        left: `${left}px`,\n        top: `${top}px`\n      };\n    }\n  },\n  methods: {\n    ...mapMutations('hubble', ['toggleSlider']),\n    handleValue() {\n      if (!this.value) {\n        this.toggleSlider(true);\n        setTimeout(() => {\n          this.handleGuide('upload');\n        }, 5);\n      } else {\n        this.handleGuide('');\n      }\n      this.$emit('input', !this.value);\n    },\n    handleGuide(type) {\n      this.currentBtn = type;\n      if (!type) {\n        this.svgPath = '';\n        return;\n      }\n      this.handleStartPoint();\n      this.handelEndPoint();\n      this.handelPath();\n    },\n    handleStartPoint() {\n      const startPoint = document.querySelector(`.guide-start-${this.currentBtn}`).getBoundingClientRect();\n      let x = startPoint.x + startPoint.width / 2;\n      let y = startPoint.y + startPoint.height;\n      if (this.currentBtn === 'upload') {\n        x = startPoint.x;\n        y = startPoint.y + startPoint.height / 2;\n      } else if (this.currentBtn === 'suggestion') {\n        x = startPoint.x + startPoint.width;\n        y = startPoint.y + startPoint.height / 2;\n      } else if (this.currentBtn === 'lang') {\n        y = startPoint.y;\n      }\n      this.startPoint = {\n        x,\n        y\n      };\n    },\n    handelEndPoint() {\n      const endPoint = document.querySelector(`#guide-${this.currentBtn}`).getBoundingClientRect();\n      const x = endPoint.x + endPoint.width / 2;\n      let y = endPoint.y;\n      if (this.currentBtn === 'upload') {\n        y = endPoint.y + endPoint.height;\n      }\n      this.endPoint = {\n        x,\n        y\n      };\n    },\n    handelPath() {\n      const {\n        x: x1,\n        y: y1\n      } = this.startPoint;\n      const {\n        x: x2,\n        y: y2\n      } = this.endPoint;\n      let svgPath = '';\n      const middleY = (y1 + y2) / 2;\n      switch (this.currentBtn) {\n        case 'upload':\n          svgPath = `M ${x1} ${y1} L ${x2 + 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 - 30} L ${x2} ${y2}`;\n          break;\n        case 'text':\n        case 'quote':\n          svgPath = `M ${x1} ${y1} L ${x1} ${middleY - 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY + 30} L ${x2} ${y2}`;\n          break;\n        case 'lang':\n          svgPath = `M ${x1} ${y1} L ${x1} ${middleY + 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY - 30} L ${x2} ${y2}`;\n          break;\n        case 'suggestion':\n          svgPath = `M ${x1} ${y1} L ${x2 - 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 + 30} L ${x2} ${y2}`;\n          break;\n      }\n      this.svgPath = svgPath;\n    },\n    handleResize() {\n      this.handleGuide(this.currentBtn);\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.handleResize);\n  }\n  // hubble-page__content-guide\n};", "map": {"version": 3, "names": ["mapMutations", "props", "value", "type", "Boolean", "default", "data", "btnList", "icon", "text", "currentBtn", "svgPath", "startPoint", "endPoint", "computed", "endPointStyle", "left", "x", "top", "y", "methods", "handleValue", "toggleSlider", "setTimeout", "handleGuide", "$emit", "handleStartPoint", "handelEndPoint", "handelPath", "document", "querySelector", "getBoundingClientRect", "width", "height", "x1", "y1", "x2", "y2", "middleY", "handleResize", "mounted", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/views/agent/components/guide/index.vue"], "sourcesContent": ["<template>\n    <div class=\"hubble-page__content-guide\">\n        <div class=\"hubble-page__content-guide__entry\" @click=\"handleValue\">\n            <i class=\"el-icon-ssq-wenhaoxiao\"></i>\n            功能指引\n        </div>\n        <div class=\"hubble-page__content-guide__modal\" v-show=\"value\"></div>\n        <div class=\"hubble-page__content-guide__dialog\" v-show=\"value\">\n            <i class=\"el-icon-ssq-delete close\" @click=\"handleValue\"></i>\n            <b>Hubble，一种全新的工作体验。</b>\n            <p>你好，我是哈勃，你的签约智能助手<br>一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。<br><br>我们准备了必要的使用介绍来帮助快速上手，<br>可以通过下方按钮轻松探索特色功能。<br></p>\n            <ul class=\"hubble-page__content-guide__btn-list\">\n                <li :class=\"`hubble-page__content-guide__btn-item ${currentBtn === btn.type ? 'active' : ''} guide-start-${btn.type}`\" v-for=\"btn in btnList\" :key=\"btn.type\" @click=\"handleGuide(btn.type)\">\n                    <i :class=\"`el-icon-ssq-${btn.icon}`\"></i><br>\n                    <span>{{ btn.text }}</span>\n                </li>\n            </ul>\n            <span class=\"confirm-btn\" @click=\"handleValue\">即刻开启 Hubble 之旅</span>\n        </div>\n        <svg class=\"hubble-page__content-guide__svg\" v-if=\"svgPath\">\n            <path id=\"myPath\" :d=\"svgPath\" fill=\"none\" stroke=\"#127fd2\" stroke-width=\"2\"></path>\n        </svg>\n        <div class=\"hubble-page__content-guide__end-point\" v-if=\"svgPath\" :style=\"endPointStyle\"></div>\n    </div>\n</template>\n\n<script>\nimport { mapMutations } from 'vuex';\n\nexport default {\n    props: {\n        value: {\n            type: Boolean,\n            default: false,\n        },\n    },\n    data() {\n        return {\n            btnList: [\n                {\n                    icon: 'shangchuan1',\n                    text: '新建文档对话',\n                    type: 'upload',\n                },\n                {\n                    icon: 'wenzi',\n                    text: '输入对话内容',\n                    type: 'text',\n                },\n                {\n                    icon: 'a-151-app-jiaocaiyinyong',\n                    text: '划词引用',\n                    type: 'quote',\n                },\n                {\n                    icon: 'qieyingwen',\n                    text: '输出内容语言',\n                    type: 'lang',\n                },\n                {\n                    icon: 'Hubbletishi',\n                    text: '建议问题',\n                    type: 'suggestion',\n                },\n            ],\n            currentBtn: '',\n            svgPath: '',\n            startPoint: {},\n            endPoint: {},\n        };\n    },\n    computed: {\n        endPointStyle() {\n            const left = this.endPoint.x - 5;\n            let top = this.endPoint.y - 10;\n            if (this.currentBtn === 'upload') {\n                top = this.endPoint.y;\n            }\n            return {\n                left: `${left}px`,\n                top: `${top}px`,\n            };\n        },\n    },\n    methods: {\n        ...mapMutations('hubble', ['toggleSlider']),\n        handleValue() {\n            if (!this.value) {\n                this.toggleSlider(true);\n                setTimeout(() => {\n                    this.handleGuide('upload');\n                }, 5);\n            } else {\n                this.handleGuide('');\n            }\n            this.$emit('input', !this.value);\n        },\n        handleGuide(type) {\n            this.currentBtn = type;\n            if (!type) {\n                this.svgPath = '';\n                return;\n            }\n            this.handleStartPoint();\n            this.handelEndPoint();\n            this.handelPath();\n        },\n        handleStartPoint() {\n            const startPoint = document.querySelector(`.guide-start-${this.currentBtn}`).getBoundingClientRect();\n            let x = startPoint.x + startPoint.width / 2;\n            let y = startPoint.y + startPoint.height;\n            if (this.currentBtn === 'upload') {\n                x = startPoint.x;\n                y = startPoint.y + startPoint.height / 2;\n            } else if (this.currentBtn === 'suggestion') {\n                x = startPoint.x + startPoint.width;\n                y = startPoint.y + startPoint.height / 2;\n            } else if (this.currentBtn === 'lang') {\n                y = startPoint.y;\n            }\n            this.startPoint = { x, y };\n        },\n        handelEndPoint() {\n            const endPoint = document.querySelector(`#guide-${this.currentBtn}`).getBoundingClientRect();\n            const x = endPoint.x + endPoint.width / 2;\n            let y = endPoint.y;\n            if (this.currentBtn === 'upload') {\n                y = endPoint.y + endPoint.height;\n            }\n            this.endPoint = { x, y };\n        },\n        handelPath() {\n            const { x: x1, y: y1 } = this.startPoint;\n            const { x: x2, y: y2 } = this.endPoint;\n            let svgPath = '';\n            const middleY = (y1 + y2) / 2;\n            switch (this.currentBtn) {\n                case 'upload':\n                    svgPath = `M ${x1} ${y1} L ${x2 + 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 - 30} L ${x2} ${y2}`;\n                    break;\n                case 'text':\n                case 'quote':\n                    svgPath = `M ${x1} ${y1} L ${x1} ${middleY - 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY + 30} L ${x2} ${y2}`;\n                    break;\n                case 'lang':\n                    svgPath = `M ${x1} ${y1} L ${x1} ${middleY + 30} Q ${x1} ${middleY} ${x1 + 30} ${middleY} L ${x2 - 30} ${middleY} Q ${x2} ${middleY} ${x2} ${middleY - 30} L ${x2} ${y2}`;\n                    break;\n                case 'suggestion':\n                    svgPath = `M ${x1} ${y1} L ${x2 - 30} ${y1} Q ${x2} ${y1} ${x2} ${y1 + 30} L ${x2} ${y2}`;\n                    break;\n            }\n            this.svgPath = svgPath;\n        },\n        handleResize() {\n            this.handleGuide(this.currentBtn);\n        },\n    },\n    mounted() {\n        window.addEventListener('resize', this.handleResize);\n    },\n    beforeDestroy() {\n        window.removeEventListener('resize', this.handleResize);\n    },\n// hubble-page__content-guide\n};\n</script>\n\n<style lang=\"scss\">\n.hubble-page__content-guide{\n    position: relative;\n    z-index: 99999;\n    &__modal{\n        position: fixed;\n        width: 100vw;\n        height: 100vh;\n        background: #000;\n        opacity: .2;\n        left: 0;\n        top: 0;\n    }\n    &__entry{\n        position: fixed;\n        z-index: 10;\n        left: 20px;\n        bottom: 55px;\n        border-radius: 13px;\n        padding: 4px 10px;\n        background: $theme-color;\n        font-size: 12px;\n        color: #fff;\n        cursor: pointer;\n        user-select: none;\n    }\n    &__dialog{\n        width: 560px;\n        height: 500px;\n        background: #fff;\n        border-radius: 30px 0 30px 30px;\n        position: fixed;\n        z-index: 11;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        text-align: center;\n        transition: all .3s;\n        b{\n            display: block;\n            font-size: 20px;\n            margin: 88px 0 30px;\n        }\n        p{\n            font-size: 12px;\n            transform: scale(.9);\n        }\n        .confirm-btn{\n            font-size: 12px;\n            cursor: pointer;\n            &:hover{\n                color: $theme-color;\n            }\n        }\n        i.close{\n            cursor: pointer;\n            position: absolute;\n            right: -20px;\n            background: #000;\n            color: #fff;\n            padding: 2px 2px 2px 2px;\n        }\n    }\n    &__btn{\n        &-list{\n            padding: 0 40px;\n            display: flex;\n            justify-content: space-between;\n            margin: 50px 0 60px;\n        }\n        &-item{\n            width: 80px;\n            height: 80px;\n            text-align: center;\n            font-size: 12px;\n            background: #e9f4fc;\n            color: $theme-color;\n            border-radius: 8px;\n            cursor: pointer;\n            i{\n                font-size: 24px;\n                margin: 20px 0 8px;\n            }\n            span{\n                display: block;\n                white-space: nowrap;\n                transform: scale(.7);\n            }\n            &:hover, &.active{\n                background: $theme-color;\n                color: #fff;\n            }\n        }\n    }\n    &__svg{\n        pointer-events: none;\n        position: fixed;\n        width: 100%;\n        height: 100%;\n        top: 0;\n        left: 0;\n        z-index: 12;\n    }\n    &__end-point{\n        position: fixed;\n        width: 6px;\n        height: 6px;\n        border-radius: 50%;\n        border: 2px solid #127fd2;\n        background: #fff;\n        z-index: 13;\n    }\n}\n</style>\n"], "mappings": "AA2BA,SAAAA,YAAA;AAEA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,GACA;QACAC,IAAA;QACAC,IAAA;QACAN,IAAA;MACA,GACA;QACAK,IAAA;QACAC,IAAA;QACAN,IAAA;MACA,GACA;QACAK,IAAA;QACAC,IAAA;QACAN,IAAA;MACA,GACA;QACAK,IAAA;QACAC,IAAA;QACAN,IAAA;MACA,GACA;QACAK,IAAA;QACAC,IAAA;QACAN,IAAA;MACA,EACA;MACAO,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA;MACA,MAAAC,IAAA,QAAAH,QAAA,CAAAI,CAAA;MACA,IAAAC,GAAA,QAAAL,QAAA,CAAAM,CAAA;MACA,SAAAT,UAAA;QACAQ,GAAA,QAAAL,QAAA,CAAAM,CAAA;MACA;MACA;QACAH,IAAA,KAAAA,IAAA;QACAE,GAAA,KAAAA,GAAA;MACA;IACA;EACA;EACAE,OAAA;IACA,GAAApB,YAAA;IACAqB,YAAA;MACA,UAAAnB,KAAA;QACA,KAAAoB,YAAA;QACAC,UAAA;UACA,KAAAC,WAAA;QACA;MACA;QACA,KAAAA,WAAA;MACA;MACA,KAAAC,KAAA,gBAAAvB,KAAA;IACA;IACAsB,YAAArB,IAAA;MACA,KAAAO,UAAA,GAAAP,IAAA;MACA,KAAAA,IAAA;QACA,KAAAQ,OAAA;QACA;MACA;MACA,KAAAe,gBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,UAAA;IACA;IACAF,iBAAA;MACA,MAAAd,UAAA,GAAAiB,QAAA,CAAAC,aAAA,sBAAApB,UAAA,IAAAqB,qBAAA;MACA,IAAAd,CAAA,GAAAL,UAAA,CAAAK,CAAA,GAAAL,UAAA,CAAAoB,KAAA;MACA,IAAAb,CAAA,GAAAP,UAAA,CAAAO,CAAA,GAAAP,UAAA,CAAAqB,MAAA;MACA,SAAAvB,UAAA;QACAO,CAAA,GAAAL,UAAA,CAAAK,CAAA;QACAE,CAAA,GAAAP,UAAA,CAAAO,CAAA,GAAAP,UAAA,CAAAqB,MAAA;MACA,gBAAAvB,UAAA;QACAO,CAAA,GAAAL,UAAA,CAAAK,CAAA,GAAAL,UAAA,CAAAoB,KAAA;QACAb,CAAA,GAAAP,UAAA,CAAAO,CAAA,GAAAP,UAAA,CAAAqB,MAAA;MACA,gBAAAvB,UAAA;QACAS,CAAA,GAAAP,UAAA,CAAAO,CAAA;MACA;MACA,KAAAP,UAAA;QAAAK,CAAA;QAAAE;MAAA;IACA;IACAQ,eAAA;MACA,MAAAd,QAAA,GAAAgB,QAAA,CAAAC,aAAA,gBAAApB,UAAA,IAAAqB,qBAAA;MACA,MAAAd,CAAA,GAAAJ,QAAA,CAAAI,CAAA,GAAAJ,QAAA,CAAAmB,KAAA;MACA,IAAAb,CAAA,GAAAN,QAAA,CAAAM,CAAA;MACA,SAAAT,UAAA;QACAS,CAAA,GAAAN,QAAA,CAAAM,CAAA,GAAAN,QAAA,CAAAoB,MAAA;MACA;MACA,KAAApB,QAAA;QAAAI,CAAA;QAAAE;MAAA;IACA;IACAS,WAAA;MACA;QAAAX,CAAA,EAAAiB,EAAA;QAAAf,CAAA,EAAAgB;MAAA,SAAAvB,UAAA;MACA;QAAAK,CAAA,EAAAmB,EAAA;QAAAjB,CAAA,EAAAkB;MAAA,SAAAxB,QAAA;MACA,IAAAF,OAAA;MACA,MAAA2B,OAAA,IAAAH,EAAA,GAAAE,EAAA;MACA,aAAA3B,UAAA;QACA;UACAC,OAAA,QAAAuB,EAAA,IAAAC,EAAA,MAAAC,EAAA,SAAAD,EAAA,MAAAC,EAAA,IAAAD,EAAA,IAAAC,EAAA,IAAAD,EAAA,WAAAC,EAAA,IAAAC,EAAA;UACA;QACA;QACA;UACA1B,OAAA,QAAAuB,EAAA,IAAAC,EAAA,MAAAD,EAAA,IAAAI,OAAA,WAAAJ,EAAA,IAAAI,OAAA,IAAAJ,EAAA,SAAAI,OAAA,MAAAF,EAAA,SAAAE,OAAA,MAAAF,EAAA,IAAAE,OAAA,IAAAF,EAAA,IAAAE,OAAA,WAAAF,EAAA,IAAAC,EAAA;UACA;QACA;UACA1B,OAAA,QAAAuB,EAAA,IAAAC,EAAA,MAAAD,EAAA,IAAAI,OAAA,WAAAJ,EAAA,IAAAI,OAAA,IAAAJ,EAAA,SAAAI,OAAA,MAAAF,EAAA,SAAAE,OAAA,MAAAF,EAAA,IAAAE,OAAA,IAAAF,EAAA,IAAAE,OAAA,WAAAF,EAAA,IAAAC,EAAA;UACA;QACA;UACA1B,OAAA,QAAAuB,EAAA,IAAAC,EAAA,MAAAC,EAAA,SAAAD,EAAA,MAAAC,EAAA,IAAAD,EAAA,IAAAC,EAAA,IAAAD,EAAA,WAAAC,EAAA,IAAAC,EAAA;UACA;MACA;MACA,KAAA1B,OAAA,GAAAA,OAAA;IACA;IACA4B,aAAA;MACA,KAAAf,WAAA,MAAAd,UAAA;IACA;EACA;EACA8B,QAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAH,YAAA;EACA;EACAI,cAAA;IACAF,MAAA,CAAAG,mBAAA,gBAAAL,YAAA;EACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}