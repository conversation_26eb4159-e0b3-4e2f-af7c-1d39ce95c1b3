{"ast": null, "code": "export default {\n  home: 'Home',\n  addEnterprise: 'Add new company ',\n  lookoverEnterprise: 'View all companies',\n  chooseEnterprise: 'Choose a company',\n  enterEnterprise: 'Please enter the company name',\n  inApproval: 'Approval…',\n  copyright: 'Аll rights reserved',\n  record: 'ICP main body record number: Zhejiang ICP No. 14031930',\n  chooseSignType: 'Choose contract method',\n  agreeProtocal: 'Terms of use',\n  ok: 'Understood',\n  newVersionTip: 'New version prompt',\n  notice: 'Аnnouncement',\n  clickToLook: 'Click to view',\n  bizManage: 'Support enterprise unified management of business fields',\n  protocalUpdate: 'Registration agreement update',\n  startUse: 'Start using',\n  joinSuccess: 'You have successfully joined',\n  contactus: 'Contact us',\n  askOnline: 'Online consultation',\n  askWeChat: 'WeChat inquiry | WeChat subscription',\n  downLoad: 'Download the user-end mobile app',\n  convenience: 'Whether you are in the office, at home, or on the road, BestSign allows you to access, sign and send documents anywhere, anytime.',\n  news: 'Welcome to follow our WeChat account for our latest news and information.',\n  tip: 'Notice',\n  unverify: 'You have not been certified by real name, and the recipient of the contract will not be able to identify you. We recommend that you first perform real-name certification.',\n  isGroupProxyAuth: 'The current certification status of this company is group certification, and the contract recipient will not be able to identify your identity. It is recommended that you supplement the real-name certification materials first.',\n  createCompnayP: {\n    p1: 'Coming soon to generate a new business for you, please improve the company name',\n    p2: 'Immediately provide the relevant company materials (for identification), and get the name of the company',\n    p3: 'Temporarily not authenticated, fill in manually',\n    p4: 'Uncertified business names show only to themselves,'\n  },\n  plzEnterRightEnt: 'Please enter the correct company name',\n  goAuthenticate: 'Go to authentication',\n  keepLaunch: 'Continue to initiate',\n  enterprise: 'Company',\n  person: 'Person',\n  usercenter: 'Admin',\n  console: 'Organization Management Platform',\n  entAccount: 'Company account ',\n  personAccount: 'Personal account ',\n  viewAllEnt: 'View all companies',\n  addEnt: 'Add new company ',\n  createEnt: 'Create a business',\n  exit: 'Log out',\n  viewDetail: 'Click for details',\n  message: 'Message',\n  datainsure: 'Data protection',\n  video: 'operation video',\n  help: 'Help',\n  dynamic: 'Product dynamic',\n  contractManage: 'Contract',\n  templateManage: 'Template',\n  statisticCharts: 'Report',\n  authenticating: 'Authenticates...',\n  ecology: '来自{developerName}的企业账号',\n  ecologyPerson: '来自{developerName}的个人账号',\n  unAuthenticate: 'Do not authenticate',\n  rejectAuthenticate: 'Authenticate rejected',\n  contactManage: 'Please contact the administrator to assign you permission',\n  noResult: 'no result',\n  confirm: 'the confirmation',\n  cancel: 'Cancel ',\n  createSuccess: 'Created successfully',\n  notifyPhone: 'Fill in the notification phone',\n  phone: 'phone',\n  phonePlaceholder: 'Fill in the notification phone',\n  verifyCode: 'Verification code',\n  sixNumberPlaceholder: 'Please input 6-digit code',\n  submit: 'submit',\n  phoneInputError: 'Please input correct telephone',\n  verCodeInputErr: 'Verification Code Error',\n  setSuccess: 'Success',\n  offlineContractManage: 'Offline Contract',\n  phoneSetTip: 'Submit the notification mobile phone number, which can be used to receive notification messages sent by the system, such as signing notifications, contract verification codes, etc.',\n  noMoreTip: 'No more remind',\n  phoneSetAfterTip: '(If you need to set it later, you can add your mobile phone number in the [Notification] of [User Center])',\n  theEnterprise: 'The enterprise',\n  you: 'you',\n  passAutEntHint: 'Display the company name after passing the company certification',\n  passAutPersonHint: 'Display personal name after passing real-name authentication',\n  createEntNow: 'Create a business now',\n  announcement: {\n    pwReset: 'your password has expired，please go to The User Center to change your password to keep your account safe. '\n  },\n  partAuthSearch: 'Query the real name of the opposite party before issuing the contract?'\n};", "map": {"version": 3, "names": ["home", "addEnterprise", "lookoverEnterprise", "chooseEnterprise", "enterEnterprise", "inApproval", "copyright", "record", "chooseSignType", "agreeProtocal", "ok", "newVersionTip", "notice", "clickToLook", "bizManage", "protocalUpdate", "startUse", "joinSuccess", "contactus", "askOnline", "askWeChat", "downLoad", "convenience", "news", "tip", "unverify", "isGroupProxyAuth", "createCompnayP", "p1", "p2", "p3", "p4", "plzEnterRightEnt", "goAuthenticate", "keepLaunch", "enterprise", "person", "usercenter", "console", "entAccount", "personAccount", "viewAllEnt", "addEnt", "createEnt", "exit", "viewDetail", "message", "datainsure", "video", "help", "dynamic", "contractManage", "templateManage", "statistic<PERSON><PERSON>s", "authenticating", "ecology", "<PERSON><PERSON><PERSON>", "unAuthenticate", "rejectAuthenticate", "contactManage", "noResult", "confirm", "cancel", "createSuccess", "notifyPhone", "phone", "phonePlaceholder", "verifyCode", "sixNumberPlaceholder", "submit", "phoneInputError", "verCodeInputErr", "setSuccess", "offlineContractManage", "phoneSetTip", "noMoreTip", "phoneSetAfterTip", "theEnterprise", "you", "passAutEntHint", "passAutPersonHint", "createEntNow", "announcement", "pw<PERSON><PERSON><PERSON>", "partAuthSearch"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/home/<USER>"], "sourcesContent": ["export default {\n    home: 'Home',\n    addEnterprise: 'Add new company ',\n    lookoverEnterprise: 'View all companies',\n    chooseEnterprise: 'Choose a company',\n    enterEnterprise: 'Please enter the company name',\n    inApproval: 'Approval…',\n    copyright: 'Аll rights reserved',\n    record: 'ICP main body record number: Zhejiang ICP No. 14031930',\n    chooseSignType: 'Choose contract method',\n    agreeProtocal: 'Terms of use',\n    ok: 'Understood',\n    newVersionTip: 'New version prompt',\n    notice: 'Аnnouncement',\n    clickToLook: 'Click to view',\n    bizManage: 'Support enterprise unified management of business fields',\n    protocalUpdate: 'Registration agreement update',\n    startUse: 'Start using',\n    joinSuccess: 'You have successfully joined',\n    contactus: 'Contact us',\n    askOnline: 'Online consultation',\n    askWeChat: 'WeChat inquiry | WeChat subscription',\n    downLoad: 'Download the user-end mobile app',\n    convenience: 'Whether you are in the office, at home, or on the road, BestSign allows you to access, sign and send documents anywhere, anytime.',\n    news: 'Welcome to follow our WeChat account for our latest news and information.',\n    tip: 'Notice',\n    unverify: 'You have not been certified by real name, and the recipient of the contract will not be able to identify you. We recommend that you first perform real-name certification.',\n    isGroupProxyAuth: 'The current certification status of this company is group certification, and the contract recipient will not be able to identify your identity. It is recommended that you supplement the real-name certification materials first.',\n    createCompnayP: {\n        p1: 'Coming soon to generate a new business for you, please improve the company name',\n        p2: 'Immediately provide the relevant company materials (for identification), and get the name of the company',\n        p3: 'Temporarily not authenticated, fill in manually',\n        p4: 'Uncertified business names show only to themselves,',\n    },\n    plzEnterRightEnt: 'Please enter the correct company name',\n    goAuthenticate: 'Go to authentication',\n    keepLaunch: 'Continue to initiate',\n    enterprise: 'Company',\n    person: 'Person',\n    usercenter: 'Admin',\n    console: 'Organization Management Platform',\n    entAccount: 'Company account ',\n    personAccount: 'Personal account ',\n    viewAllEnt: 'View all companies',\n    addEnt: 'Add new company ',\n    createEnt: 'Create a business',\n    exit: 'Log out',\n    viewDetail: 'Click for details',\n    message: 'Message',\n    datainsure: 'Data protection',\n    video: 'operation video',\n    help: 'Help',\n    dynamic: 'Product dynamic',\n    contractManage: 'Contract',\n    templateManage: 'Template',\n    statisticCharts: 'Report',\n    authenticating: 'Authenticates...',\n    ecology: '来自{developerName}的企业账号',\n    ecologyPerson: '来自{developerName}的个人账号',\n    unAuthenticate: 'Do not authenticate',\n    rejectAuthenticate: 'Authenticate rejected',\n    contactManage: 'Please contact the administrator to assign you permission',\n    noResult: 'no result',\n    confirm: 'the confirmation',\n    cancel: 'Cancel ',\n    createSuccess: 'Created successfully',\n    notifyPhone: 'Fill in the notification phone',\n    phone: 'phone',\n    phonePlaceholder: 'Fill in the notification phone',\n    verifyCode: 'Verification code',\n    sixNumberPlaceholder: 'Please input 6-digit code',\n    submit: 'submit',\n    phoneInputError: 'Please input correct telephone',\n    verCodeInputErr: 'Verification Code Error',\n    setSuccess: 'Success',\n    offlineContractManage: 'Offline Contract',\n    phoneSetTip: 'Submit the notification mobile phone number, which can be used to receive notification messages sent by the system, such as signing notifications, contract verification codes, etc.',\n    noMoreTip: 'No more remind',\n    phoneSetAfterTip: '(If you need to set it later, you can add your mobile phone number in the [Notification] of [User Center])',\n    theEnterprise: 'The enterprise',\n    you: 'you',\n    passAutEntHint: 'Display the company name after passing the company certification',\n    passAutPersonHint: 'Display personal name after passing real-name authentication',\n    createEntNow: 'Create a business now',\n\n    announcement: {\n        pwReset: 'your password has expired，please go to The User Center to change your password to keep your account safe. ',\n    },\n    partAuthSearch: 'Query the real name of the opposite party before issuing the contract?',\n};\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,kBAAkB;EACjCC,kBAAkB,EAAE,oBAAoB;EACxCC,gBAAgB,EAAE,kBAAkB;EACpCC,eAAe,EAAE,+BAA+B;EAChDC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE,qBAAqB;EAChCC,MAAM,EAAE,wDAAwD;EAChEC,cAAc,EAAE,wBAAwB;EACxCC,aAAa,EAAE,cAAc;EAC7BC,EAAE,EAAE,YAAY;EAChBC,aAAa,EAAE,oBAAoB;EACnCC,MAAM,EAAE,cAAc;EACtBC,WAAW,EAAE,eAAe;EAC5BC,SAAS,EAAE,0DAA0D;EACrEC,cAAc,EAAE,+BAA+B;EAC/CC,QAAQ,EAAE,aAAa;EACvBC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE,sCAAsC;EACjDC,QAAQ,EAAE,kCAAkC;EAC5CC,WAAW,EAAE,mIAAmI;EAChJC,IAAI,EAAE,2EAA2E;EACjFC,GAAG,EAAE,QAAQ;EACbC,QAAQ,EAAE,4KAA4K;EACtLC,gBAAgB,EAAE,oOAAoO;EACtPC,cAAc,EAAE;IACZC,EAAE,EAAE,iFAAiF;IACrFC,EAAE,EAAE,0GAA0G;IAC9GC,EAAE,EAAE,iDAAiD;IACrDC,EAAE,EAAE;EACR,CAAC;EACDC,gBAAgB,EAAE,uCAAuC;EACzDC,cAAc,EAAE,sBAAsB;EACtCC,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,kCAAkC;EAC3CC,UAAU,EAAE,kBAAkB;EAC9BC,aAAa,EAAE,mBAAmB;EAClCC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,kBAAkB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,mBAAmB;EAC/BC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,iBAAiB;EAC7BC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,iBAAiB;EAC1BC,cAAc,EAAE,UAAU;EAC1BC,cAAc,EAAE,UAAU;EAC1BC,eAAe,EAAE,QAAQ;EACzBC,cAAc,EAAE,kBAAkB;EAClCC,OAAO,EAAE,wBAAwB;EACjCC,aAAa,EAAE,wBAAwB;EACvCC,cAAc,EAAE,qBAAqB;EACrCC,kBAAkB,EAAE,uBAAuB;EAC3CC,aAAa,EAAE,2DAA2D;EAC1EC,QAAQ,EAAE,WAAW;EACrBC,OAAO,EAAE,kBAAkB;EAC3BC,MAAM,EAAE,SAAS;EACjBC,aAAa,EAAE,sBAAsB;EACrCC,WAAW,EAAE,gCAAgC;EAC7CC,KAAK,EAAE,OAAO;EACdC,gBAAgB,EAAE,gCAAgC;EAClDC,UAAU,EAAE,mBAAmB;EAC/BC,oBAAoB,EAAE,2BAA2B;EACjDC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,gCAAgC;EACjDC,eAAe,EAAE,yBAAyB;EAC1CC,UAAU,EAAE,SAAS;EACrBC,qBAAqB,EAAE,kBAAkB;EACzCC,WAAW,EAAE,sLAAsL;EACnMC,SAAS,EAAE,gBAAgB;EAC3BC,gBAAgB,EAAE,4GAA4G;EAC9HC,aAAa,EAAE,gBAAgB;EAC/BC,GAAG,EAAE,KAAK;EACVC,cAAc,EAAE,kEAAkE;EAClFC,iBAAiB,EAAE,8DAA8D;EACjFC,YAAY,EAAE,uBAAuB;EAErCC,YAAY,EAAE;IACVC,OAAO,EAAE;EACb,CAAC;EACDC,cAAc,EAAE;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}