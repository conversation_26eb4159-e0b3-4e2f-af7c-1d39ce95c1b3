{"ast": null, "code": "export default {\n  submitBaiscInfo: 'تقديم المعلومات الأساسية',\n  firstStep: 'الخطوة الأولى',\n  secondStep: 'الخطوة الثانية',\n  thirdStep: 'الخطوة الثالثة',\n  nextStep: 'الخطوة التالية',\n  back: 'عودة',\n  submit: 'تقديم',\n  confirm: 'تأكيد',\n  affirm: 'تأكيد',\n  cancel: 'إلغاء',\n  tip: 'تنبيه',\n  account: 'الحساب',\n  view: 'عرض',\n  iSee: 'فهمت',\n  submitAttorney: 'تقديم تفويض',\n  plsDownloadSSQAttorneyTip: 'يرجى تحميل \"تفويض خدمات BestSign للمؤسسات\"، ووضع ختم المؤسسة عليه ثم التقاط صورة وتحميلها.',\n  ssqAttorney: 'تفويض خدمات BestSign للمؤسسات',\n  downloadAttorney: 'يرجى النقر لتحميل التفويض',\n  imgSupportType: 'يدعم تنسيقات png وjpeg وjpg وbmp وحجم لا يتجاوز 10M.',\n  imgNoWatermarkTip: 'يدعم فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق من هوية BestSign فقط\"',\n  confirmSubmit: 'تأكيد التقديم',\n  backToPreviousPage: 'عودة للصفحة السابقة',\n  uploadImgBeforeSubmit: 'يرجى تحميل الصور قبل التقديم',\n  phoneNumBuyTip: 'يرجى التأكد من أن رقم الهاتف هذا تم شراؤه بشكل رسمي من مشغل الاتصالات، وإلا لن يتمكن من اجتياز التحقق من هوية BestSign.',\n  operatorPhoneVerify: 'التحقق من هاتف المسؤول',\n  operatorName: 'اسم المسؤول',\n  operatorIdNum: 'رقم هوية المسؤول',\n  modifyOperator: 'تعديل المسؤول',\n  changeIdCard: 'تغيير الهوية',\n  operatorPassPhoneVerifyTip: ' اجتاز المسؤول التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n  operatorPhoneNum: 'رقم هاتف المسؤول',\n  verifyCode: 'رمز التحقق',\n  tryFaceVerify: 'جرب التحقق بالوجه',\n  afterPhoneVerifyTip: 'بعد اجتياز التحقق من الهاتف، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',\n  operatorFaceVerify: 'التحقق من وجه المسؤول',\n  operatorPassFaceVerifyTip: ' اجتاز المسؤول التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n  alipayFaceVerify: 'مسح للتحقق من الوجه عبر Alipay',\n  tryPhoneVerify: 'جرب التحقق من الهاتف',\n  scanFaceNow: 'التحقق من الوجه الآن',\n  afterFaceVerifyTip: 'بعد اجتياز التحقق من الوجه، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',\n  plsEnterRightPhoneNum: 'يرجى إدخال رقم هاتف صحيح أولاً',\n  plsGetVerifyCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',\n  agreeSsqProtectMethod: 'أوافق على طريقة BestSign في حماية معلومات هويتي الشخصية',\n  ssqHowToProtectInfo: '\"كيف يحمي BestSign معلوماتك الشخصية\"',\n  noEntNameTip: 'نظراً لعدم وجود اسم مؤسسة لشركتك، لا يدعم التحقق بالتفويض',\n  legalName: 'اسم الممثل القانوني',\n  legalIdCardNum: 'رقم هوية الممثل القانوني',\n  uploadIdCard: 'تحميل بطاقة الهوية',\n  legalPassFaceVerifyTip: 'اجتاز الممثل القانوني التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n  plsEnterLegalIdCardNum: 'يرجى إدخال رقم هوية الممثل القانوني أولاً',\n  plsUploadLegalIdCardImg: 'يرجى تحميل بطاقة هوية الممثل القانوني أولاً',\n  pageExpiredTip: 'انتهت صلاحية الصفحة الحالية، يرجى تحديث الصفحة',\n  faceFunctionNotEnabled: 'خدمة التحقق من الوجه غير متاحة مؤقتاً، يرجى المحاولة لاحقاً',\n  plsSubmitLegalIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني',\n  legalPassPhoneVerifyTip: 'اجتاز الممثل القانوني التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n  legalPhoneNum: 'رقم هاتف الممثل القانوني',\n  noPublicAccount: 'ليس لديك حساب مؤسسي؟',\n  useSpecialMethod: 'أنا فرع لا يملك حساباً عاماً، استخدم قناة خاصة',\n  ssqRemitMoneyTip: 'سيقوم BestSign بتحويل مبلغ (أقل من 0.99 يوان) إلى الحساب المؤسسي أدناه، يمكنك إدخال مبلغ التحويل لاجتياز التحقق من هوية المؤسسة.',\n  accountName: 'اسم الحساب',\n  bank: 'البنك',\n  accountBank: 'البنك المفتوح فيه الحساب',\n  accountBankName: 'اسم البنك المفتوح فيه الحساب',\n  bankAccount: 'الحساب البنكي',\n  plsInputBankNameTip: 'يرجى إدخال اسم البنك، لا داعي لكتابة فرع أو فرع فرعي، مثل \"بنك الصين\"',\n  locationOfBank: 'موقع البنك المفتوح فيه الحساب',\n  plsSelect: 'يرجى الاختيار',\n  province: 'المقاطعة',\n  city: 'المدينة',\n  cityCounty: 'المدينة/المقاطعة',\n  nameOfBank: 'اسم فرع البنك',\n  plsInputZhiBankName: 'ادخل اسم فرع البنك، مثل \"فرع بنك هانغتشو الثقافي\"',\n  canNotFindBank: 'لا يمكن العثور على البنك؟',\n  clickChangeReversePay: 'انقر على \"تغيير طريقة الدفع\" في أسفل الصفحة للدفع العكسي',\n  other: 'أخرى',\n  otherZhiBank: 'اسم فرع البنك (إذا اخترت \"أخرى\" للفرع، يرجى الملء)',\n  bankAccount1: 'رقم الحساب المصرفي',\n  plsInputBankAccount: 'يرجى إدخال رقم الحساب المصرفي',\n  remitNotSuccess: 'التحويل غير ناجح؟',\n  switchPayMethod: 'تغيير طريقة الدفع',\n  whySubmitBankInfo: 'لماذا يجب تقديم معلومات البطاقة المصرفية',\n  submitBankInfoExplan: 'التحقق من معلومات البطاقة المصرفية هو جزء من عملية التحقق من هوية المؤسسة. سنقوم بتحويل مبلغ تحقق إلى هذه البطاقة المصرفية، ولن يتم خصم أي مبلغ من حسابك.',\n  plsSelectBank: 'يرجى اختيار البنك من القائمة المنسدلة',\n  plsSelectArea: 'يرجى اختيار المحافظة والمدينة/المقاطعة من القائمة المنسدلة',\n  plsInputNameOfAccountBank: 'يرجى إدخال اسم البنك',\n  plsInputCorrectBankInfo: 'يرجى إدخال معلومات الحساب المؤسسي الصحيحة',\n  plsInputFullBankName: 'يرجى إدخال الاسم الكامل للبنك (متضمناً اسم الفرع)',\n  area: 'المنطقة',\n  contactCustomerService: 'اتصل بخدمة العملاء',\n  beiJing: 'بكين',\n  dongCheng: 'منطقة دونغتشنغ',\n  fillJointBankNum: 'يحتاج هذا الحساب إلى رقم البنك المشترك لإتمام التحويل',\n  bankType: {\n    type1: 'بنك',\n    type2: 'جمعية ائتمان تعاونية',\n    type3: 'تعاونية ائتمانية',\n    type4: 'جمعية زراعية'\n  },\n  accountBankArea: 'موقع البنك',\n  changeRemitMethod: 'تغيير طريقة التحويل',\n  canNotRemitAuth: 'لا يمكن بدء التحقق بالتحويل لحسابك المؤسسي، يرجى اختيار طريقة تحقق أخرى',\n  bankMaintenanceTip: 'إشعار صيانة النظام المصرفي: بنك غوانغفا (11 يناير 02:30-03:30)، البنك الزراعي (12 يناير 02:00-06:00)، بنك البناء (12 يناير 03:30-05:30)، البنك الصناعي والتجاري (12 يناير 04:00-06:15). يرجى تجنب التحويلات المؤسسية خلال هذه الفترات.',\n  faceVerify: 'التحقق من الوجه',\n  havePublicAccount: 'لدي حساب مؤسسي',\n  ensureInfoSafeTip: 'لضمان أمن معلومات مؤسستك وحمايتها من الاستخدام غير المصرح به، يرجى إكمال التحقق من وجه الممثل القانوني أولاً، ثم إجراء التحقق بالتحويل',\n  submitEntBankInfo: 'تقديم معلومات الحساب المؤسسي',\n  fillBackAmout: 'إدخال مبلغ الإيداع',\n  legalPerson: 'الممثل القانوني',\n  operatorMange: 'المسؤول',\n  unionNum: 'رقم البنك المشترك',\n  getUnionNumTip: 'يرجى التواصل مع المالية في مؤسستك للحصول على رقم البنك المشترك، أو وفقاً لمعلومات الفرع',\n  uinonNumSearchOnline: 'البحث عن رقم البنك المشترك عبر الإنترنت',\n  remitProcessMap: {\n    submit: 'تم تقديم طلب التحويل',\n    accept: 'تم قبول التحويل',\n    success: 'تم التحويل بنجاح',\n    receiveWait: 'تم استلام طلب الدفع، في انتظار رد البنك',\n    successAttention: 'تم التحويل بنجاح، يرجى التحقق من تفاصيل الحساب. يرجى الانتباه:',\n    remarkTip: 'ملاحظة التحويل: المبلغ للتحقق من هوية المؤسسة وطلب شهادة CA',\n    thirdChannel: 'يتم التحويل عبر قناة دفع طرف ثالث، القناة هي:',\n    remitNotSsq: 'يتم التحويل عبر قناة طرف ثالث، اسم المحول ليس المنصة',\n    remitPartySsq: 'المحول هو \"شركة هانغتشو للتكنولوجيا المحدودة\"'\n  },\n  close: 'إغلاق',\n  name: 'الاسم',\n  idCardNum: 'رقم بطاقة الهوية',\n  reversePayMap: {\n    remitTip1: 'قم بتحويل 0.01 يوان إلى حساب المنصة المؤسسي، ثم حدد الخيارات أدناه وانقر على \"تأكيد\" للتحقق',\n    iAgree: 'أوافق وأقر بما يلي:',\n    remitAuthUse: 'سيتم استخدام مبلغ 0.01 يوان المحول إلى حساب المنصة لشراء عقد مؤسسي واحد عند نجاح التحقق؛ وفي حالة الفشل، لا يمكن المطالبة باسترداد المبلغ',\n    authFailure: 'عدم استيفاء المتطلبات التالية سيؤدي إلى فشل التحقق:',\n    authFailureReason1: '(1) يجب أن يكون المحول هو الممثل القانوني الحالي:',\n    authFailureReason2: '(2) يجب استخدام حساب شخصي للتحويل',\n    authFailureReason3: '(1) يجب أن يكون المحول هو المؤسسة الحالية:',\n    authFailureReason4: '(2) يجب استخدام حساب مؤسسي للتحويل',\n    plsInputBankName: 'يرجى إدخال اسم الحساب المصرفي',\n    authFailureReason5: '(3) يجب أن يكون مبلغ التحويل 0.01 يوان بالضبط',\n    authFailureReason6: '(4) يجب أن يكون تاريخ التحويل بعد {date}',\n    authFailureReason7: '(5) معلومات حساب المستلم كالتالي، لا يمكن التحويل لحساب آخر:',\n    authFailureReason8: '(6) يجب استخدام رمز التحقق هذا كملاحظة للتحويل:',\n    remitDelay: 'قد يستغرق وصول التحويل بعض الوقت، يرجى الانتظار',\n    failureReason: 'أسباب فشل التحقق المحتملة:',\n    wait30min: 'بسبب أنظمة البنوك، يمكن الاستعلام عن النتيجة بعد 30 دقيقة من نجاح التحويل',\n    queryProgress: 'استعلام عن التقدم',\n    inputRemitBankName: 'يرجى إدخال اسم الحساب المصرفي للمحول',\n    queryFailureTip: 'فشل الاستعلام، يرجى المحاولة لاحقاً',\n    remitAuthSuccess: 'تم اجتياز التحقق بنجاح، وتم استخدام المبلغ لشراء عقد مؤسسي واحد'\n  },\n  hourMinSec: '{hour} ساعة {min} دقيقة {sec} ثانية',\n  minSec: '{min} دقيقة {sec} ثانية',\n  sec: '{sec} ثانية',\n  ssqRemitTip: 'تم تقديم طلب تحويل بمبلغ أقل من 1 يوان لحسابك، سيصل خلال 1-2 يوم عمل، يرجى تأكيد المبلغ هنا عند وصوله',\n  inputRightRemitAmout: 'يجب مراجعة كشف حسابك وإدخال المبلغ الصحيح لاجتياز التحقق',\n  notGetRemit: 'إذا لم تجد المبلغ في حسابك، انقر هنا',\n  queryRemitProgress: 'للتحقق من حالة التحويل',\n  inputWrongAccount: 'هل أدخلت رقم حساب خاطئ؟',\n  remitNote: 'ملاحظة التحويل:',\n  ssqApplyCaTip: 'المبلغ مخصص للتحقق من هوية المؤسسة وطلب شهادة CA، يرجى إدخال المبلغ على المنصة',\n  remitAmout: 'مبلغ التحويل',\n  yuan: 'يوان',\n  receiveAmout: 'مبلغ الإيداع بين 0.01-0.99 يوان',\n  maxRemitTimeTip: 'يمكنك التحويل أربع مرات فقط! هل تريد تأكيد إعادة تقديم رقم الحساب؟',\n  remitFailure: 'فشل التحويل، سيتم العودة لصفحة الدفع المؤسسي، يجب إعادة طلب التحويل',\n  plsInputRemitAmout: 'يرجى إدخال مبلغ بين 0.01-0.99 يوان',\n  reSubmitBankInfo: 'يجب إعادة تقديم معلومات البطاقة المصرفية',\n  amoutError: 'خطأ في المبلغ',\n  reSubmit: 'إعادة التقديم',\n  amoutInvalid: 'المبلغ غير صالح',\n  authReject: 'تم رفض وثائق التحقق، يرجى إعادة التقديم',\n  authCertificate: 'التحقق بالتفويض',\n  entPayAuth: 'التحقق بالدفع المؤسسي',\n  legalPhoneAuth: 'التحقق من هاتف الممثل القانوني',\n  legalFaceAuth: 'التحقق من وجه الممثل القانوني',\n  sender: 'المرسل',\n  requireYouThrough: 'يطلب {name} منك اجتياز {type}',\n  selectOneAuthMethod: 'يرجى اختيار إحدى طرق التحقق التالية:',\n  entCertificate: 'وثائق المؤسسة',\n  personCertificate: 'وثائق شخصية',\n  iAmLegal: 'أنا الممثل القانوني',\n  iAmNotLegal: 'لست الممثل القانوني، أنا مسؤول المؤسسة',\n  receiveSsqPhone: 'أوافق على تلقي مكالمة من المنصة',\n  plsAgreeSsqPhone: 'يرجى الموافقة على تلقي مكالمة من المنصة',\n  submitInfoError: 'المعلومات الشخصية (الاسم أو رقم الهوية) غير صحيحة، يرجى التحقق',\n  submitIndividualEntAuth: 'أنت تقدم طلب تحقق كمؤسسة فردية، رخصة عملك لا تحتوي على اسم مؤسسة',\n  submitIndividualCorrectIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني الصحيحة',\n  entPersonInfoError: 'معلومات المؤسسة أو المعلومات الشخصية غير صحيحة، يرجى التحقق وإعادة التقديم',\n  noEntName: 'لا يوجد اسم مؤسسة',\n  businessLicenseBlank: 'خانة اسم المؤسسة في الرخصة التجارية: فارغة',\n  addressName: '/العنوان/اسم الممثل القانوني',\n  plsClick: 'يرجى النقر',\n  haveEntName: 'يوجد اسم مؤسسة، يتطلب مراجعة يدوية من خدمة العملاء',\n  checkFollowInfoTip: 'تحقق من المعلومات التالية وانقر \"تأكيد\"، يمكنك متابعة تقديم وثائق التحقق الأخرى. ستتم مراجعة معلومات المؤسسة يدوياً، وإذا كانت المعلومات غير صحيحة سيتم رفض جميع الوثائق المقدمة. تستغرق المراجعة حوالي يوم عمل واحد.',\n  entName: 'اسم المؤسسة',\n  unifySocialCode: 'رقم التعريف الاجتماعي الموحد',\n  uploadColorImgTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',\n  businessLicense: 'الرخصة التجارية',\n  uploadBusinessLicenseTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل',\n  imgLimitTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\"',\n  autoRecognizedTip: 'المعلومات التالية تم التعرف عليها تلقائياً، يرجى التحقق بعناية وتصحيح أي أخطاء',\n  iNoEntName: 'ليس لدي اسم مؤسسة',\n  clickUseSpecialMethod: 'انقر لاستخدام القناة الخاصة',\n  socialCodeBuisnessNum: 'رقم التعريف الاجتماعي الموحد/رقم السجل التجاري',\n  plsSubmitCorrectLegalName: 'يرجى تقديم اسم الممثل القانوني الصحيح',\n  plsSubmitBusinessLicense: 'يرجى تقديم الرخصة التجارية أولاً',\n  noEntUploadBusinessTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\". سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',\n  imgRequireTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\".',\n  noEntNameUse: 'المؤسسات الفردية بدون اسم مؤسسة يجب استخدام',\n  legalPlusCode: 'اسم الممثل القانوني مع رقم التعريف الاجتماعي',\n  codeAsEntName: 'كاسم للمؤسسة',\n  ssq: 'المنصة',\n  entAuth: 'التحقق من المؤسسة',\n  eleContractServiceBy: 'خدمة التوقيع الإلكتروني مقدمة من',\n  spericalProvide: 'توفير',\n  redirectWait: 'جاري التحويل، يرجى الانتظار',\n  businessLicenseImg: 'صورة الرخصة التجارية',\n  idCardNational: 'الجانب الخلفي لبطاقة الهوية',\n  idCardFace: 'الجانب الأمامي لبطاقة الهوية',\n  dueToSignRequire: 'نظراً لمتطلبات التوقيع',\n  authorizeSomeone: 'أفوض {name} للحصول على المعلومات التالية:',\n  authoizedInfo: 'معلومات حسابي الأساسية (رقم الحساب والاسم) ومعلومات المؤسسة الأساسية (اسم المؤسسة، رقم التعريف الاجتماعي الموحد أو رقم التسجيل، اسم الممثل القانوني)',\n  iSubmitInfoToSsq: 'المعلومات التي قدمتها على المنصة',\n  authMaterials: 'وثائق التحقق',\n  sureInfo: 'المعلومات صحيحة',\n  modifyInfo: 'تعديل المعلومات',\n  additionalInfo: 'معلومات إضافية',\n  ssqNotifyMethod: 'طريقة الإشعار على المنصة',\n  phoneNum: 'رقم الهاتف',\n  email: 'البريد الإلكتروني',\n  submitInfoPlsVerify: 'تم تقديم معلومات المؤسسة الأساسية، يرجى التحقق',\n  operatorIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية المسؤول',\n  operatorIdCardNational: 'الجانب الخلفي لبطاقة هوية المسؤول',\n  legalIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية الممثل القانوني',\n  legalIdCardNational: 'الجانب الخلفي لبطاقة هوية الممثل القانوني',\n  plsAgreeAuthorized: 'يرجى الموافقة على التفويض أولاً',\n  finishConfirmInfo: 'تم تأكيد المعلومات، يرجى متابعة عملية التحقق من المؤسسة',\n  entOpenMultipleBusiniss: 'المؤسسة لديها خطوط أعمال متعددة، لا يمكن متابعة العملية',\n  contactSsqDeal: 'يرجى التواصل مع خط الأعمال الذي أكمل التحقق من المؤسسة على المنصة سابقاً',\n  signContract: 'توقيع العقد',\n  faceVerifyFailure: 'فشل التحقق من الوجه',\n  reFaceVerifyAuth: 'إعادة التحقق من الوجه',\n  threeTimesFailure: 'عذراً، لقد فشلت في التحقق من الوجه 3 مرات متتالية اليوم، يرجى اختيار طريقة تحقق أخرى',\n  faceVerifySucess: 'نجح التحقق من الوجه',\n  chooseOtherAuthMethod: 'اختيار طريقة تحقق أخرى',\n  plsContinueOnPc: 'يرجى المتابعة على جهاز الكمبيوتر',\n  accountAppealSuccess: 'نجح استئناف الحساب',\n  faceCompareSuccess: 'نجحت مطابقة الوجه',\n  plsUseWechatScan: 'يرجى استخدام متصفح WeChat لمسح رمز QR',\n  wechatCannotScanFace: 'بسبب تغيير سياسة WeChat، لا يمكن إجراء التحقق من الوجه حالياً',\n  plsUploadClearIdCardImg: 'يرجى رفع صورة واضحة لبطاقة الهوية، سيتم التعرف على معلومات البطاقة تلقائياً. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',\n  uploadImgMap: {\n    tip1: 'يرجى رفع صورة واضحة لبطاقة الهوية،',\n    tip2: 'سيتم التعرف على معلومات البطاقة تلقائياً.',\n    tip3: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',\n    tip4: 'نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\"'\n  },\n  plsSubmitIdCard: 'يرجى تقديم بطاقة الهوية أولاً',\n  noNameNoSupportEntRemit: 'نظراً لعدم وجود اسم مؤسسة، لا يمكن استخدام التحقق بالتحويل المؤسسي',\n  checkMoreVerison: 'عرض المزيد من الإصدارات',\n  viewCertificate: 'عرض الشهادة',\n  continueAuthNewEnt: 'متابعة التحقق من مؤسسة جديدة',\n  continueBuyPackage: 'متابعة شراء الباقة',\n  needSubmitBasicInfo: 'مطلوب فقط تقديم معلومات المؤسسة الأساسية',\n  youFinishEntAuth: 'لقد أكملت التحقق من المؤسسة',\n  loginSsqWebToEntAuth: 'سجل الدخول إلى موقع المنصة لإكمال عملية التحقق الكاملة للمؤسسة للحصول على صلاحيات أعلى',\n  entAuthCertificate: 'شهادة التحقق من المؤسسة',\n  youFinishEntAuthTip: 'لقد أكملت التحقق من هوية المؤسسة',\n  backToHome: 'العودة إلى الصفحة الرئيسية',\n  youNeedMoreOperate: 'تحتاج إلى {operate} لإكمال العمليات التالية:',\n  goPc: 'الذهاب إلى نسخة الكمبيوتر',\n  addEntMemberStepMap: {\n    title: 'خطوات إضافة عضو مؤسسة:',\n    step1: '1- الدخول إلى لوحة تحكم المؤسسة',\n    step2: '2- فتح إدارة الأعضاء',\n    step3: '3- النقر على إضافة عضو جديد',\n    step4: '4- إدخال الحساب والاسم، اختيار الدور',\n    step5: '5- النقر على حفظ لإضافة العضو الجديد'\n  },\n  addEntMember: 'إضافة عضو مؤسسة',\n  addSealStepMap: {\n    title: 'خطوات إضافة ختم:',\n    step1: '1- الدخول إلى لوحة تحكم المؤسسة',\n    step2: '2- فتح قائمة الأختام',\n    step3: '3- النقر على إضافة ختم جديد',\n    step4: '4- إدخال اسم الختم، رفع تصميم الختم أو إنشاء ختم إلكتروني',\n    step5: '5- النقر على حفظ لإضافة الختم',\n    step6: '6- النقر على إضافة حامل',\n    step7: '7- اختيار عضو المؤسسة',\n    step8: '8- النقر على تأكيد لإضافة حامل الختم'\n  },\n  addSeal: 'إضافة ختم',\n  waitApporve: 'في انتظار المراجعة',\n  submitAuthMaterial: 'تقديم وثائق التحقق',\n  authFinish: 'اكتمل التحقق',\n  plsSubmitBeforeTip: 'يرجى إكمال تقديم جميع الوثائق قبل {date}، وإلا ستنتهي صلاحية المعلومات الأساسية.',\n  oneDayFinishApprove: 'سيقوم فريق خدمة العملاء بإكمال المراجعة خلال يوم عمل واحد، يرجى الانتظار',\n  entAuthFinish: 'اكتمل التحقق من المؤسسة',\n  baseInfoInvalid: 'انتهت صلاحية المعلومات الأساسية، يرجى إعادة التقديم',\n  missParams: 'معلمات مفقودة!',\n  illegalLink: 'رابط غير صالح',\n  cookieNotEnabe: 'لا يمكن قراءة/كتابة ملفات تعريف الارتباط، هل قمت بتفعيل وضع التصفح الخفي أو تعطيل ملفات تعريف الارتباط؟',\n  truthSubmitOperatorIdCard: 'يرجى تقديم بطاقة هوية المسؤول الصحيحة',\n  abandonAttorneyAuth: 'التخلي عن التحقق بالتفويض،',\n  modifyCertiInfo: 'تعديل معلومات الوثائق',\n  modifyAttorneyInfo: 'تعديل معلومات التفويض',\n  plsUploadBusinessLicense: 'يرجى رفع الرخصة التجارية!',\n  plsUploadLegalCerti: 'يرجى رفع وثائق الممثل القانوني!',\n  plsUploadOperatorCerti: 'يرجى رفع وثائق المسؤول!',\n  legalIdCardSubmit: 'تقديم بطاقة هوية الممثل القانوني',\n  serviceAttorneryAuth: 'التحقق بتفويض الخدمة',\n  accountAppealMap: {\n    entApeal: 'استئناف حساب المؤسسة',\n    apealSuccess: 'نجح الاستئناف',\n    comName: 'اسم الشركة:',\n    account: 'الحساب:',\n    verifyCode: 'رمز التحقق:',\n    ener6Digtal: 'يرجى إدخال 6 أرقام',\n    beMainManagerTip: 'بعد نجاح طلب حساب المؤسسة، ستصبح المدير الرئيسي للمؤسسة',\n    mainAccount: 'حساب المدير الرئيسي',\n    continueSign: 'متابعة التوقيع',\n    continueConfirm: 'متابعة التحقق',\n    plsEnterComName: 'يرجى إدخال اسم الشركة أولاً',\n    plsEnterCorrentComName: 'يرجى إدخال اسم الشركة الصحيح',\n    sendSuccess: 'تم الإرسال بنجاح!',\n    plsEnterCorrectCode: 'يرجى إدخال رمز التحقق الصحيح'\n  },\n  faceInitLoading: 'جاري تهيئة التحقق من الوجه، يرجى الانتظار',\n  wxFaceVersionTip: 'يتطلب التحقق من الوجه WeChat إصدار 7.0.12 أو أعلى، يرجى الترقية',\n  wxIosFaceVersionTip: 'يتطلب التحقق من الوجه iOS إصدار 10.3 أو أعلى، يرجى الترقية',\n  wxAndroidVersionTip: 'يتطلب التحقق من الوجه Android إصدار 5.0 أو أعلى، يرجى الترقية',\n  faceInitFailure: 'فشل تهيئة التحقق من الوجه: ',\n  entAuthCertificateTip: 'شهادة التحقق من هوية المؤسسة',\n  idCardHandHeld: 'التحقق بحمل بطاقة الهوية',\n  faceAuth: 'التحقق من الوجه',\n  noMainlandAuth: 'التحقق خارج البر الرئيسي',\n  entAuthTip: 'التحقق من هوية المؤسسة',\n  signIntro: 'دليل التوقيع',\n  companySet: 'إعدادات المؤسسة'\n};", "map": {"version": 3, "names": ["submitBaiscInfo", "firstStep", "secondStep", "thirdStep", "nextStep", "back", "submit", "confirm", "affirm", "cancel", "tip", "account", "view", "iSee", "submit<PERSON><PERSON><PERSON><PERSON>", "plsDownloadSSQAttorneyTip", "ssqAttorney", "downloadAttorney", "imgSupportType", "imgNoWatermarkTip", "confirmSubmit", "backToPreviousPage", "uploadImgBeforeSubmit", "phoneNumBuyTip", "operatorPhoneVerify", "operatorName", "operatorIdNum", "modifyOperator", "changeIdCard", "operatorPassPhoneVerifyTip", "operatorPhoneNum", "verifyCode", "tryFaceVerify", "afterPhoneVerifyTip", "operatorFaceVerify", "operatorPassFaceVerifyTip", "alipayFaceVerify", "tryPhoneVerify", "scanFaceNow", "afterFaceVerifyTip", "plsEnterRightPhoneNum", "plsGetVerifyCodeFirst", "agreeSsqProtectMethod", "ssqHowToProtectInfo", "noEntNameTip", "legalName", "legalIdCardNum", "uploadIdCard", "legalPassFaceVerifyTip", "plsEnterLegalIdCardNum", "plsUploadLegalIdCardImg", "pageExpiredTip", "faceFunctionNotEnabled", "plsSubmitLegalIdCard", "legalPassPhoneVerifyTip", "legalPhoneNum", "noPublicAccount", "useSpecialMethod", "ssqRemitMoneyTip", "accountName", "bank", "accountBank", "accountBankName", "bankAccount", "plsInputBankNameTip", "locationOfBank", "plsSelect", "province", "city", "cityCounty", "nameOfBank", "plsInputZhiBankName", "canNotFindBank", "clickChangeReversePay", "other", "otherZhiBank", "bankAccount1", "plsInputBankAccount", "remitNotSuccess", "switchPayMethod", "whySubmitBankInfo", "submitBankInfoExplan", "plsSelectBank", "plsSelectArea", "plsInputNameOfAccountBank", "plsInputCorrectBankInfo", "plsInputFullBankName", "area", "contactCustomerService", "beiJing", "dongCheng", "fillJointBankNum", "bankType", "type1", "type2", "type3", "type4", "accountBankArea", "changeRemitMethod", "canNotRemitAuth", "bankMaintenanceTip", "faceVerify", "havePublicAccount", "ensureInfoSafeTip", "submitEntBankInfo", "fillBackAmout", "legal<PERSON>erson", "operatorMange", "unionNum", "getUnionNumTip", "uinonNumSearchOnline", "remitProcessMap", "accept", "success", "receiveWait", "successAttention", "remarkTip", "thirdChannel", "remitNotSsq", "remitPartySsq", "close", "name", "idCardNum", "reversePayMap", "remitTip1", "iAgree", "remitAuthUse", "authFailure", "authFailureReason1", "authFailureReason2", "authFailureReason3", "authFailureReason4", "plsInputBankName", "authFailureReason5", "authFailureReason6", "authFailureReason7", "authFailureReason8", "remitDelay", "failureReason", "wait30min", "queryProgress", "inputRemitBankName", "queryFailureTip", "remitAuthSuccess", "hourMinSec", "minSec", "sec", "ssqRemitTip", "inputRightRemitAmout", "notGetRemit", "queryRemitProgress", "inputWrongAccount", "remitNote", "ssqApplyCaTip", "remitAmout", "yuan", "receiveAmout", "maxRemitTimeTip", "remitFailure", "plsInputRemitAmout", "reSubmitBankInfo", "amoutError", "reSubmit", "amoutInvalid", "authReject", "authCertificate", "entPayAuth", "legalPhoneAuth", "legalFaceAuth", "sender", "requireYouThrough", "selectOneAuthMethod", "entCertificate", "personCertificate", "iAmLegal", "iAmNotLegal", "receiveSsqPhone", "plsAgreeSsqPhone", "submitInfoError", "submitIndividualEntAuth", "submitIndividualCorrectIdCard", "entPersonInfoError", "noEntName", "businessLicenseBlank", "addressName", "plsClick", "haveEntName", "checkFollowInfoTip", "entName", "unifySocialCode", "uploadColorImgTip", "businessLicense", "uploadBusinessLicenseTip", "imgLimitTip", "autoRecognizedTip", "iNoEntName", "clickUseSpecialMethod", "socialCodeBuisnessNum", "plsSubmitCorrectLegalName", "plsSubmitBusinessLicense", "noEntUploadBusinessTip", "imgRequireTip", "noEntNameUse", "legalPlusCode", "codeAsEntName", "ssq", "entAuth", "eleContractServiceBy", "spericalProvide", "redirectWait", "businessLicenseImg", "idCardNational", "idCardFace", "dueToSignRequire", "authorizeSomeone", "authoizedInfo", "iSubmitInfoToSsq", "authMaterials", "sureInfo", "modifyInfo", "additionalInfo", "ssqNotifyMethod", "phoneNum", "email", "submitInfoPlsVerify", "operatorIdCardFaceImg", "operatorIdCardNational", "legalIdCardFaceImg", "legalIdCardNational", "plsAgreeAuthorized", "finishConfirmInfo", "entOpenMultipleBusiniss", "contactSsqDeal", "signContract", "faceVerifyFailure", "reFaceVerifyAuth", "threeTimesFailure", "faceVerifySucess", "chooseOtherAuthMethod", "plsContinueOnPc", "accountAppealSuccess", "faceCompareSuccess", "plsUseWechatScan", "wechatCannotScanFace", "plsUploadClearIdCardImg", "uploadImgMap", "tip1", "tip2", "tip3", "tip4", "plsSubmitIdCard", "noNameNoSupportEntRemit", "check<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewCertificate", "continueAuthNewEnt", "continueBuyPackage", "needSubmitBasicInfo", "youFinishEntAuth", "loginSsqWebToEntAuth", "entAuthCertificate", "youFinishEntAuthTip", "backToHome", "youNeedMoreOperate", "goPc", "addEntMemberStepMap", "title", "step1", "step2", "step3", "step4", "step5", "addEntMember", "addSealStepMap", "step6", "step7", "step8", "addSeal", "waitApporve", "submitAuthMaterial", "auth<PERSON><PERSON><PERSON>", "plsSubmitBeforeTip", "oneDayFinishApprove", "ent<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseInfoInvalid", "missParams", "illegalLink", "cookieNotEnabe", "truthSubmitOperatorIdCard", "abandonAttorneyAuth", "modifyCertiInfo", "modifyAttorneyInfo", "plsUploadBusinessLicense", "plsUploadLegalCerti", "plsUploadOperatorCerti", "legalIdCardSubmit", "serviceAttorneryAuth", "accountAppealMap", "entApeal", "apealSuccess", "comName", "ener6Digtal", "beMainManagerTip", "mainAccount", "continueSign", "continueConfirm", "plsEnterComName", "plsEnterCorrentComName", "sendSuccess", "plsEnterCorrectCode", "faceInitLoading", "wxFaceVersionTip", "wxIosFaceVersionTip", "wxAndroidVersionTip", "faceInitFailure", "entAuthCertificateTip", "idCardHandHeld", "faceAuth", "noMainland<PERSON>uth", "entAuthTip", "signIntro", "companySet"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/entAuth/entAuth-ar.js"], "sourcesContent": ["export default {\n    submitBaiscInfo: 'تقديم المعلومات الأساسية',\n    firstStep: 'الخطوة الأولى',\n    secondStep: 'الخطوة الثانية',\n    thirdStep: 'الخطوة الثالثة',\n    nextStep: 'الخطوة التالية',\n    back: 'عودة',\n    submit: 'تقديم',\n    confirm: 'تأكيد',\n    affirm: 'تأكيد',\n    cancel: 'إلغاء',\n    tip: 'تنبيه',\n    account: 'الحساب',\n    view: 'عرض',\n    iSee: 'فهمت',\n    submitAttorney: 'تقديم تفويض',\n    plsDownloadSSQAttorneyTip: 'يرجى تحميل \"تفويض خدمات BestSign للمؤسسات\"، ووضع ختم المؤسسة عليه ثم التقاط صورة وتحميلها.',\n    ssqAttorney: 'تفويض خدمات BestSign للمؤسسات',\n    downloadAttorney: 'يرجى النقر لتحميل التفويض',\n    imgSupportType: 'يدعم تنسيقات png وjpeg وjpg وbmp وحجم لا يتجاوز 10M.',\n    imgNoWatermarkTip: 'يدعم فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق من هوية BestSign فقط\"',\n    confirmSubmit: 'تأكيد التقديم',\n    backToPreviousPage: 'عودة للصفحة السابقة',\n    uploadImgBeforeSubmit: 'يرجى تحميل الصور قبل التقديم',\n    phoneNumBuyTip: 'يرجى التأكد من أن رقم الهاتف هذا تم شراؤه بشكل رسمي من مشغل الاتصالات، وإلا لن يتمكن من اجتياز التحقق من هوية BestSign.',\n    operatorPhoneVerify: 'التحقق من هاتف المسؤول',\n    operatorName: 'اسم المسؤول',\n    operatorIdNum: 'رقم هوية المسؤول',\n    modifyOperator: 'تعديل المسؤول',\n    changeIdCard: 'تغيير الهوية',\n    operatorPassPhoneVerifyTip: ' اجتاز المسؤول التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n    operatorPhoneNum: 'رقم هاتف المسؤول',\n    verifyCode: 'رمز التحقق',\n    tryFaceVerify: 'جرب التحقق بالوجه',\n    afterPhoneVerifyTip: 'بعد اجتياز التحقق من الهاتف، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',\n    operatorFaceVerify: 'التحقق من وجه المسؤول',\n    operatorPassFaceVerifyTip: ' اجتاز المسؤول التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n    alipayFaceVerify: 'مسح للتحقق من الوجه عبر Alipay',\n    tryPhoneVerify: 'جرب التحقق من الهاتف',\n    scanFaceNow: 'التحقق من الوجه الآن',\n    afterFaceVerifyTip: 'بعد اجتياز التحقق من الوجه، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',\n    plsEnterRightPhoneNum: 'يرجى إدخال رقم هاتف صحيح أولاً',\n    plsGetVerifyCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',\n    agreeSsqProtectMethod: 'أوافق على طريقة BestSign في حماية معلومات هويتي الشخصية',\n    ssqHowToProtectInfo: '\"كيف يحمي BestSign معلوماتك الشخصية\"',\n    noEntNameTip: 'نظراً لعدم وجود اسم مؤسسة لشركتك، لا يدعم التحقق بالتفويض',\n    legalName: 'اسم الممثل القانوني',\n    legalIdCardNum: 'رقم هوية الممثل القانوني',\n    uploadIdCard: 'تحميل بطاقة الهوية',\n    legalPassFaceVerifyTip: 'اجتاز الممثل القانوني التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n    plsEnterLegalIdCardNum: 'يرجى إدخال رقم هوية الممثل القانوني أولاً',\n    plsUploadLegalIdCardImg: 'يرجى تحميل بطاقة هوية الممثل القانوني أولاً',\n    pageExpiredTip: 'انتهت صلاحية الصفحة الحالية، يرجى تحديث الصفحة',\n    faceFunctionNotEnabled: 'خدمة التحقق من الوجه غير متاحة مؤقتاً، يرجى المحاولة لاحقاً',\n    plsSubmitLegalIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني',\n    legalPassPhoneVerifyTip: 'اجتاز الممثل القانوني التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',\n    legalPhoneNum: 'رقم هاتف الممثل القانوني',\n    noPublicAccount: 'ليس لديك حساب مؤسسي؟',\n    useSpecialMethod: 'أنا فرع لا يملك حساباً عاماً، استخدم قناة خاصة',\n    ssqRemitMoneyTip: 'سيقوم BestSign بتحويل مبلغ (أقل من 0.99 يوان) إلى الحساب المؤسسي أدناه، يمكنك إدخال مبلغ التحويل لاجتياز التحقق من هوية المؤسسة.',\n    accountName: 'اسم الحساب',\n    bank: 'البنك',\n    accountBank: 'البنك المفتوح فيه الحساب',\n    accountBankName: 'اسم البنك المفتوح فيه الحساب',\n    bankAccount: 'الحساب البنكي',\n    plsInputBankNameTip: 'يرجى إدخال اسم البنك، لا داعي لكتابة فرع أو فرع فرعي، مثل \"بنك الصين\"',\n    locationOfBank: 'موقع البنك المفتوح فيه الحساب',\n    plsSelect: 'يرجى الاختيار',\n    province: 'المقاطعة',\n    city: 'المدينة',\n    cityCounty: 'المدينة/المقاطعة',\n    nameOfBank: 'اسم فرع البنك',\n    plsInputZhiBankName: 'ادخل اسم فرع البنك، مثل \"فرع بنك هانغتشو الثقافي\"',\n    canNotFindBank: 'لا يمكن العثور على البنك؟',\n    clickChangeReversePay: 'انقر على \"تغيير طريقة الدفع\" في أسفل الصفحة للدفع العكسي',\n    other: 'أخرى',\n    otherZhiBank: 'اسم فرع البنك (إذا اخترت \"أخرى\" للفرع، يرجى الملء)',\n    bankAccount1: 'رقم الحساب المصرفي',\n    plsInputBankAccount: 'يرجى إدخال رقم الحساب المصرفي',\n    remitNotSuccess: 'التحويل غير ناجح؟',\n    switchPayMethod: 'تغيير طريقة الدفع',\n    whySubmitBankInfo: 'لماذا يجب تقديم معلومات البطاقة المصرفية',\n    submitBankInfoExplan: 'التحقق من معلومات البطاقة المصرفية هو جزء من عملية التحقق من هوية المؤسسة. سنقوم بتحويل مبلغ تحقق إلى هذه البطاقة المصرفية، ولن يتم خصم أي مبلغ من حسابك.',\n    plsSelectBank: 'يرجى اختيار البنك من القائمة المنسدلة',\n    plsSelectArea: 'يرجى اختيار المحافظة والمدينة/المقاطعة من القائمة المنسدلة',\n    plsInputNameOfAccountBank: 'يرجى إدخال اسم البنك',\n    plsInputCorrectBankInfo: 'يرجى إدخال معلومات الحساب المؤسسي الصحيحة',\n    plsInputFullBankName: 'يرجى إدخال الاسم الكامل للبنك (متضمناً اسم الفرع)',\n    area: 'المنطقة',\n    contactCustomerService: 'اتصل بخدمة العملاء',\n    beiJing: 'بكين',\n    dongCheng: 'منطقة دونغتشنغ',\n    fillJointBankNum: 'يحتاج هذا الحساب إلى رقم البنك المشترك لإتمام التحويل',\n    bankType: {\n        type1: 'بنك',\n        type2: 'جمعية ائتمان تعاونية',\n        type3: 'تعاونية ائتمانية',\n        type4: 'جمعية زراعية',\n    },\n    accountBankArea: 'موقع البنك',\n    changeRemitMethod: 'تغيير طريقة التحويل',\n    canNotRemitAuth: 'لا يمكن بدء التحقق بالتحويل لحسابك المؤسسي، يرجى اختيار طريقة تحقق أخرى',\n    bankMaintenanceTip: 'إشعار صيانة النظام المصرفي: بنك غوانغفا (11 يناير 02:30-03:30)، البنك الزراعي (12 يناير 02:00-06:00)، بنك البناء (12 يناير 03:30-05:30)، البنك الصناعي والتجاري (12 يناير 04:00-06:15). يرجى تجنب التحويلات المؤسسية خلال هذه الفترات.',\n    faceVerify: 'التحقق من الوجه',\n    havePublicAccount: 'لدي حساب مؤسسي',\n    ensureInfoSafeTip: 'لضمان أمن معلومات مؤسستك وحمايتها من الاستخدام غير المصرح به، يرجى إكمال التحقق من وجه الممثل القانوني أولاً، ثم إجراء التحقق بالتحويل',\n    submitEntBankInfo: 'تقديم معلومات الحساب المؤسسي',\n    fillBackAmout: 'إدخال مبلغ الإيداع',\n    legalPerson: 'الممثل القانوني',\n    operatorMange: 'المسؤول',\n    unionNum: 'رقم البنك المشترك',\n    getUnionNumTip: 'يرجى التواصل مع المالية في مؤسستك للحصول على رقم البنك المشترك، أو وفقاً لمعلومات الفرع',\n    uinonNumSearchOnline: 'البحث عن رقم البنك المشترك عبر الإنترنت',\n    remitProcessMap: {\n        submit: 'تم تقديم طلب التحويل',\n        accept: 'تم قبول التحويل',\n        success: 'تم التحويل بنجاح',\n        receiveWait: 'تم استلام طلب الدفع، في انتظار رد البنك',\n        successAttention: 'تم التحويل بنجاح، يرجى التحقق من تفاصيل الحساب. يرجى الانتباه:',\n        remarkTip: 'ملاحظة التحويل: المبلغ للتحقق من هوية المؤسسة وطلب شهادة CA',\n        thirdChannel: 'يتم التحويل عبر قناة دفع طرف ثالث، القناة هي:',\n        remitNotSsq: 'يتم التحويل عبر قناة طرف ثالث، اسم المحول ليس المنصة',\n        remitPartySsq: 'المحول هو \"شركة هانغتشو للتكنولوجيا المحدودة\"',\n    },\n    close: 'إغلاق',\n    name: 'الاسم',\n    idCardNum: 'رقم بطاقة الهوية',\n    reversePayMap: {\n        remitTip1: 'قم بتحويل 0.01 يوان إلى حساب المنصة المؤسسي، ثم حدد الخيارات أدناه وانقر على \"تأكيد\" للتحقق',\n        iAgree: 'أوافق وأقر بما يلي:',\n        remitAuthUse: 'سيتم استخدام مبلغ 0.01 يوان المحول إلى حساب المنصة لشراء عقد مؤسسي واحد عند نجاح التحقق؛ وفي حالة الفشل، لا يمكن المطالبة باسترداد المبلغ',\n        authFailure: 'عدم استيفاء المتطلبات التالية سيؤدي إلى فشل التحقق:',\n        authFailureReason1: '(1) يجب أن يكون المحول هو الممثل القانوني الحالي:',\n        authFailureReason2: '(2) يجب استخدام حساب شخصي للتحويل',\n        authFailureReason3: '(1) يجب أن يكون المحول هو المؤسسة الحالية:',\n        authFailureReason4: '(2) يجب استخدام حساب مؤسسي للتحويل',\n        plsInputBankName: 'يرجى إدخال اسم الحساب المصرفي',\n        authFailureReason5: '(3) يجب أن يكون مبلغ التحويل 0.01 يوان بالضبط',\n        authFailureReason6: '(4) يجب أن يكون تاريخ التحويل بعد {date}',\n        authFailureReason7: '(5) معلومات حساب المستلم كالتالي، لا يمكن التحويل لحساب آخر:',\n        authFailureReason8: '(6) يجب استخدام رمز التحقق هذا كملاحظة للتحويل:',\n        remitDelay: 'قد يستغرق وصول التحويل بعض الوقت، يرجى الانتظار',\n        failureReason: 'أسباب فشل التحقق المحتملة:',\n        wait30min: 'بسبب أنظمة البنوك، يمكن الاستعلام عن النتيجة بعد 30 دقيقة من نجاح التحويل',\n        queryProgress: 'استعلام عن التقدم',\n        inputRemitBankName: 'يرجى إدخال اسم الحساب المصرفي للمحول',\n        queryFailureTip: 'فشل الاستعلام، يرجى المحاولة لاحقاً',\n        remitAuthSuccess: 'تم اجتياز التحقق بنجاح، وتم استخدام المبلغ لشراء عقد مؤسسي واحد',\n    },\n    hourMinSec: '{hour} ساعة {min} دقيقة {sec} ثانية',\n    minSec: '{min} دقيقة {sec} ثانية',\n    sec: '{sec} ثانية',\n    ssqRemitTip: 'تم تقديم طلب تحويل بمبلغ أقل من 1 يوان لحسابك، سيصل خلال 1-2 يوم عمل، يرجى تأكيد المبلغ هنا عند وصوله',\n    inputRightRemitAmout: 'يجب مراجعة كشف حسابك وإدخال المبلغ الصحيح لاجتياز التحقق',\n    notGetRemit: 'إذا لم تجد المبلغ في حسابك، انقر هنا',\n    queryRemitProgress: 'للتحقق من حالة التحويل',\n    inputWrongAccount: 'هل أدخلت رقم حساب خاطئ؟',\n    remitNote: 'ملاحظة التحويل:',\n    ssqApplyCaTip: 'المبلغ مخصص للتحقق من هوية المؤسسة وطلب شهادة CA، يرجى إدخال المبلغ على المنصة',\n    remitAmout: 'مبلغ التحويل',\n    yuan: 'يوان',\n    receiveAmout: 'مبلغ الإيداع بين 0.01-0.99 يوان',\n    maxRemitTimeTip: 'يمكنك التحويل أربع مرات فقط! هل تريد تأكيد إعادة تقديم رقم الحساب؟',\n    remitFailure: 'فشل التحويل، سيتم العودة لصفحة الدفع المؤسسي، يجب إعادة طلب التحويل',\n    plsInputRemitAmout: 'يرجى إدخال مبلغ بين 0.01-0.99 يوان',\n    reSubmitBankInfo: 'يجب إعادة تقديم معلومات البطاقة المصرفية',\n    amoutError: 'خطأ في المبلغ',\n    reSubmit: 'إعادة التقديم',\n    amoutInvalid: 'المبلغ غير صالح',\n    authReject: 'تم رفض وثائق التحقق، يرجى إعادة التقديم',\n    authCertificate: 'التحقق بالتفويض',\n    entPayAuth: 'التحقق بالدفع المؤسسي',\n    legalPhoneAuth: 'التحقق من هاتف الممثل القانوني',\n    legalFaceAuth: 'التحقق من وجه الممثل القانوني',\n    sender: 'المرسل',\n    requireYouThrough: 'يطلب {name} منك اجتياز {type}',\n    selectOneAuthMethod: 'يرجى اختيار إحدى طرق التحقق التالية:',\n    entCertificate: 'وثائق المؤسسة',\n    personCertificate: 'وثائق شخصية',\n    iAmLegal: 'أنا الممثل القانوني',\n    iAmNotLegal: 'لست الممثل القانوني، أنا مسؤول المؤسسة',\n    receiveSsqPhone: 'أوافق على تلقي مكالمة من المنصة',\n    plsAgreeSsqPhone: 'يرجى الموافقة على تلقي مكالمة من المنصة',\n    submitInfoError: 'المعلومات الشخصية (الاسم أو رقم الهوية) غير صحيحة، يرجى التحقق',\n    submitIndividualEntAuth: 'أنت تقدم طلب تحقق كمؤسسة فردية، رخصة عملك لا تحتوي على اسم مؤسسة',\n    submitIndividualCorrectIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني الصحيحة',\n    entPersonInfoError: 'معلومات المؤسسة أو المعلومات الشخصية غير صحيحة، يرجى التحقق وإعادة التقديم',\n    noEntName: 'لا يوجد اسم مؤسسة',\n    businessLicenseBlank: 'خانة اسم المؤسسة في الرخصة التجارية: فارغة',\n    addressName: '/العنوان/اسم الممثل القانوني',\n    plsClick: 'يرجى النقر',\n    haveEntName: 'يوجد اسم مؤسسة، يتطلب مراجعة يدوية من خدمة العملاء',\n    checkFollowInfoTip: 'تحقق من المعلومات التالية وانقر \"تأكيد\"، يمكنك متابعة تقديم وثائق التحقق الأخرى. ستتم مراجعة معلومات المؤسسة يدوياً، وإذا كانت المعلومات غير صحيحة سيتم رفض جميع الوثائق المقدمة. تستغرق المراجعة حوالي يوم عمل واحد.',\n    entName: 'اسم المؤسسة',\n    unifySocialCode: 'رقم التعريف الاجتماعي الموحد',\n    uploadColorImgTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',\n    businessLicense: 'الرخصة التجارية',\n    uploadBusinessLicenseTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل',\n    imgLimitTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\"',\n    autoRecognizedTip: 'المعلومات التالية تم التعرف عليها تلقائياً، يرجى التحقق بعناية وتصحيح أي أخطاء',\n    iNoEntName: 'ليس لدي اسم مؤسسة',\n    clickUseSpecialMethod: 'انقر لاستخدام القناة الخاصة',\n    socialCodeBuisnessNum: 'رقم التعريف الاجتماعي الموحد/رقم السجل التجاري',\n    plsSubmitCorrectLegalName: 'يرجى تقديم اسم الممثل القانوني الصحيح',\n    plsSubmitBusinessLicense: 'يرجى تقديم الرخصة التجارية أولاً',\n    noEntUploadBusinessTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\". سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',\n    imgRequireTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\".',\n    noEntNameUse: 'المؤسسات الفردية بدون اسم مؤسسة يجب استخدام',\n    legalPlusCode: 'اسم الممثل القانوني مع رقم التعريف الاجتماعي',\n    codeAsEntName: 'كاسم للمؤسسة',\n    ssq: 'المنصة',\n    entAuth: 'التحقق من المؤسسة',\n    eleContractServiceBy: 'خدمة التوقيع الإلكتروني مقدمة من',\n    spericalProvide: 'توفير',\n    redirectWait: 'جاري التحويل، يرجى الانتظار',\n    businessLicenseImg: 'صورة الرخصة التجارية',\n    idCardNational: 'الجانب الخلفي لبطاقة الهوية',\n    idCardFace: 'الجانب الأمامي لبطاقة الهوية',\n    dueToSignRequire: 'نظراً لمتطلبات التوقيع',\n    authorizeSomeone: 'أفوض {name} للحصول على المعلومات التالية:',\n    authoizedInfo: 'معلومات حسابي الأساسية (رقم الحساب والاسم) ومعلومات المؤسسة الأساسية (اسم المؤسسة، رقم التعريف الاجتماعي الموحد أو رقم التسجيل، اسم الممثل القانوني)',\n    iSubmitInfoToSsq: 'المعلومات التي قدمتها على المنصة',\n    authMaterials: 'وثائق التحقق',\n    sureInfo: 'المعلومات صحيحة',\n    modifyInfo: 'تعديل المعلومات',\n    additionalInfo: 'معلومات إضافية',\n    ssqNotifyMethod: 'طريقة الإشعار على المنصة',\n    phoneNum: 'رقم الهاتف',\n    email: 'البريد الإلكتروني',\n    submitInfoPlsVerify: 'تم تقديم معلومات المؤسسة الأساسية، يرجى التحقق',\n    operatorIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية المسؤول',\n    operatorIdCardNational: 'الجانب الخلفي لبطاقة هوية المسؤول',\n    legalIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية الممثل القانوني',\n    legalIdCardNational: 'الجانب الخلفي لبطاقة هوية الممثل القانوني',\n    plsAgreeAuthorized: 'يرجى الموافقة على التفويض أولاً',\n    finishConfirmInfo: 'تم تأكيد المعلومات، يرجى متابعة عملية التحقق من المؤسسة',\n    entOpenMultipleBusiniss: 'المؤسسة لديها خطوط أعمال متعددة، لا يمكن متابعة العملية',\n    contactSsqDeal: 'يرجى التواصل مع خط الأعمال الذي أكمل التحقق من المؤسسة على المنصة سابقاً',\n    signContract: 'توقيع العقد',\n    faceVerifyFailure: 'فشل التحقق من الوجه',\n    reFaceVerifyAuth: 'إعادة التحقق من الوجه',\n    threeTimesFailure: 'عذراً، لقد فشلت في التحقق من الوجه 3 مرات متتالية اليوم، يرجى اختيار طريقة تحقق أخرى',\n    faceVerifySucess: 'نجح التحقق من الوجه',\n    chooseOtherAuthMethod: 'اختيار طريقة تحقق أخرى',\n    plsContinueOnPc: 'يرجى المتابعة على جهاز الكمبيوتر',\n    accountAppealSuccess: 'نجح استئناف الحساب',\n    faceCompareSuccess: 'نجحت مطابقة الوجه',\n    plsUseWechatScan: 'يرجى استخدام متصفح WeChat لمسح رمز QR',\n    wechatCannotScanFace: 'بسبب تغيير سياسة WeChat، لا يمكن إجراء التحقق من الوجه حالياً',\n    plsUploadClearIdCardImg: 'يرجى رفع صورة واضحة لبطاقة الهوية، سيتم التعرف على معلومات البطاقة تلقائياً. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',\n    uploadImgMap: {\n        tip1: 'يرجى رفع صورة واضحة لبطاقة الهوية،',\n        tip2: 'سيتم التعرف على معلومات البطاقة تلقائياً.',\n        tip3: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',\n        tip4: 'نقبل فقط الصور بدون علامة مائية أو بعلامة مائية \"للتحقق على المنصة فقط\"',\n    },\n    plsSubmitIdCard: 'يرجى تقديم بطاقة الهوية أولاً',\n    noNameNoSupportEntRemit: 'نظراً لعدم وجود اسم مؤسسة، لا يمكن استخدام التحقق بالتحويل المؤسسي',\n    checkMoreVerison: 'عرض المزيد من الإصدارات',\n    viewCertificate: 'عرض الشهادة',\n    continueAuthNewEnt: 'متابعة التحقق من مؤسسة جديدة',\n    continueBuyPackage: 'متابعة شراء الباقة',\n    needSubmitBasicInfo: 'مطلوب فقط تقديم معلومات المؤسسة الأساسية',\n    youFinishEntAuth: 'لقد أكملت التحقق من المؤسسة',\n    loginSsqWebToEntAuth: 'سجل الدخول إلى موقع المنصة لإكمال عملية التحقق الكاملة للمؤسسة للحصول على صلاحيات أعلى',\n    entAuthCertificate: 'شهادة التحقق من المؤسسة',\n    youFinishEntAuthTip: 'لقد أكملت التحقق من هوية المؤسسة',\n    backToHome: 'العودة إلى الصفحة الرئيسية',\n    youNeedMoreOperate: 'تحتاج إلى {operate} لإكمال العمليات التالية:',\n    goPc: 'الذهاب إلى نسخة الكمبيوتر',\n    addEntMemberStepMap: {\n        title: 'خطوات إضافة عضو مؤسسة:',\n        step1: '1- الدخول إلى لوحة تحكم المؤسسة',\n        step2: '2- فتح إدارة الأعضاء',\n        step3: '3- النقر على إضافة عضو جديد',\n        step4: '4- إدخال الحساب والاسم، اختيار الدور',\n        step5: '5- النقر على حفظ لإضافة العضو الجديد',\n    },\n    addEntMember: 'إضافة عضو مؤسسة',\n    addSealStepMap: {\n        title: 'خطوات إضافة ختم:',\n        step1: '1- الدخول إلى لوحة تحكم المؤسسة',\n        step2: '2- فتح قائمة الأختام',\n        step3: '3- النقر على إضافة ختم جديد',\n        step4: '4- إدخال اسم الختم، رفع تصميم الختم أو إنشاء ختم إلكتروني',\n        step5: '5- النقر على حفظ لإضافة الختم',\n        step6: '6- النقر على إضافة حامل',\n        step7: '7- اختيار عضو المؤسسة',\n        step8: '8- النقر على تأكيد لإضافة حامل الختم',\n    },\n    addSeal: 'إضافة ختم',\n    waitApporve: 'في انتظار المراجعة',\n    submitAuthMaterial: 'تقديم وثائق التحقق',\n    authFinish: 'اكتمل التحقق',\n    plsSubmitBeforeTip: 'يرجى إكمال تقديم جميع الوثائق قبل {date}، وإلا ستنتهي صلاحية المعلومات الأساسية.',\n    oneDayFinishApprove: 'سيقوم فريق خدمة العملاء بإكمال المراجعة خلال يوم عمل واحد، يرجى الانتظار',\n    entAuthFinish: 'اكتمل التحقق من المؤسسة',\n    baseInfoInvalid: 'انتهت صلاحية المعلومات الأساسية، يرجى إعادة التقديم',\n    missParams: 'معلمات مفقودة!',\n    illegalLink: 'رابط غير صالح',\n    cookieNotEnabe: 'لا يمكن قراءة/كتابة ملفات تعريف الارتباط، هل قمت بتفعيل وضع التصفح الخفي أو تعطيل ملفات تعريف الارتباط؟',\n    truthSubmitOperatorIdCard: 'يرجى تقديم بطاقة هوية المسؤول الصحيحة',\n    abandonAttorneyAuth: 'التخلي عن التحقق بالتفويض،',\n    modifyCertiInfo: 'تعديل معلومات الوثائق',\n    modifyAttorneyInfo: 'تعديل معلومات التفويض',\n    plsUploadBusinessLicense: 'يرجى رفع الرخصة التجارية!',\n    plsUploadLegalCerti: 'يرجى رفع وثائق الممثل القانوني!',\n    plsUploadOperatorCerti: 'يرجى رفع وثائق المسؤول!',\n    legalIdCardSubmit: 'تقديم بطاقة هوية الممثل القانوني',\n    serviceAttorneryAuth: 'التحقق بتفويض الخدمة',\n    accountAppealMap: {\n        entApeal: 'استئناف حساب المؤسسة',\n        apealSuccess: 'نجح الاستئناف',\n        comName: 'اسم الشركة:',\n        account: 'الحساب:',\n        verifyCode: 'رمز التحقق:',\n        ener6Digtal: 'يرجى إدخال 6 أرقام',\n        beMainManagerTip: 'بعد نجاح طلب حساب المؤسسة، ستصبح المدير الرئيسي للمؤسسة',\n        mainAccount: 'حساب المدير الرئيسي',\n        continueSign: 'متابعة التوقيع',\n        continueConfirm: 'متابعة التحقق',\n        plsEnterComName: 'يرجى إدخال اسم الشركة أولاً',\n        plsEnterCorrentComName: 'يرجى إدخال اسم الشركة الصحيح',\n        sendSuccess: 'تم الإرسال بنجاح!',\n        plsEnterCorrectCode: 'يرجى إدخال رمز التحقق الصحيح',\n    },\n    faceInitLoading: 'جاري تهيئة التحقق من الوجه، يرجى الانتظار',\n    wxFaceVersionTip: 'يتطلب التحقق من الوجه WeChat إصدار 7.0.12 أو أعلى، يرجى الترقية',\n    wxIosFaceVersionTip: 'يتطلب التحقق من الوجه iOS إصدار 10.3 أو أعلى، يرجى الترقية',\n    wxAndroidVersionTip: 'يتطلب التحقق من الوجه Android إصدار 5.0 أو أعلى، يرجى الترقية',\n    faceInitFailure: 'فشل تهيئة التحقق من الوجه: ',\n    entAuthCertificateTip: 'شهادة التحقق من هوية المؤسسة',\n    idCardHandHeld: 'التحقق بحمل بطاقة الهوية',\n    faceAuth: 'التحقق من الوجه',\n    noMainlandAuth: 'التحقق خارج البر الرئيسي',\n    entAuthTip: 'التحقق من هوية المؤسسة',\n    signIntro: 'دليل التوقيع',\n    companySet: 'إعدادات المؤسسة',\n};\n"], "mappings": "AAAA,eAAe;EACXA,eAAe,EAAE,0BAA0B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,UAAU,EAAE,gBAAgB;EAC5BC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,gBAAgB;EAC1BC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,OAAO;EACfC,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,MAAM;EACZC,cAAc,EAAE,aAAa;EAC7BC,yBAAyB,EAAE,4FAA4F;EACvHC,WAAW,EAAE,+BAA+B;EAC5CC,gBAAgB,EAAE,2BAA2B;EAC7CC,cAAc,EAAE,sDAAsD;EACtEC,iBAAiB,EAAE,+EAA+E;EAClGC,aAAa,EAAE,eAAe;EAC9BC,kBAAkB,EAAE,qBAAqB;EACzCC,qBAAqB,EAAE,8BAA8B;EACrDC,cAAc,EAAE,yHAAyH;EACzIC,mBAAmB,EAAE,wBAAwB;EAC7CC,YAAY,EAAE,aAAa;EAC3BC,aAAa,EAAE,kBAAkB;EACjCC,cAAc,EAAE,eAAe;EAC/BC,YAAY,EAAE,cAAc;EAC5BC,0BAA0B,EAAE,6EAA6E;EACzGC,gBAAgB,EAAE,kBAAkB;EACpCC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,mBAAmB;EAClCC,mBAAmB,EAAE,oHAAoH;EACzIC,kBAAkB,EAAE,uBAAuB;EAC3CC,yBAAyB,EAAE,4EAA4E;EACvGC,gBAAgB,EAAE,gCAAgC;EAClDC,cAAc,EAAE,sBAAsB;EACtCC,WAAW,EAAE,sBAAsB;EACnCC,kBAAkB,EAAE,mHAAmH;EACvIC,qBAAqB,EAAE,gCAAgC;EACvDC,qBAAqB,EAAE,kCAAkC;EACzDC,qBAAqB,EAAE,yDAAyD;EAChFC,mBAAmB,EAAE,sCAAsC;EAC3DC,YAAY,EAAE,2DAA2D;EACzEC,SAAS,EAAE,qBAAqB;EAChCC,cAAc,EAAE,0BAA0B;EAC1CC,YAAY,EAAE,oBAAoB;EAClCC,sBAAsB,EAAE,mFAAmF;EAC3GC,sBAAsB,EAAE,2CAA2C;EACnEC,uBAAuB,EAAE,6CAA6C;EACtEC,cAAc,EAAE,gDAAgD;EAChEC,sBAAsB,EAAE,6DAA6D;EACrFC,oBAAoB,EAAE,uCAAuC;EAC7DC,uBAAuB,EAAE,oFAAoF;EAC7GC,aAAa,EAAE,0BAA0B;EACzCC,eAAe,EAAE,sBAAsB;EACvCC,gBAAgB,EAAE,gDAAgD;EAClEC,gBAAgB,EAAE,kIAAkI;EACpJC,WAAW,EAAE,YAAY;EACzBC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,0BAA0B;EACvCC,eAAe,EAAE,8BAA8B;EAC/CC,WAAW,EAAE,eAAe;EAC5BC,mBAAmB,EAAE,uEAAuE;EAC5FC,cAAc,EAAE,+BAA+B;EAC/CC,SAAS,EAAE,eAAe;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,kBAAkB;EAC9BC,UAAU,EAAE,eAAe;EAC3BC,mBAAmB,EAAE,mDAAmD;EACxEC,cAAc,EAAE,2BAA2B;EAC3CC,qBAAqB,EAAE,0DAA0D;EACjFC,KAAK,EAAE,MAAM;EACbC,YAAY,EAAE,oDAAoD;EAClEC,YAAY,EAAE,oBAAoB;EAClCC,mBAAmB,EAAE,+BAA+B;EACpDC,eAAe,EAAE,mBAAmB;EACpCC,eAAe,EAAE,mBAAmB;EACpCC,iBAAiB,EAAE,0CAA0C;EAC7DC,oBAAoB,EAAE,2JAA2J;EACjLC,aAAa,EAAE,uCAAuC;EACtDC,aAAa,EAAE,4DAA4D;EAC3EC,yBAAyB,EAAE,sBAAsB;EACjDC,uBAAuB,EAAE,2CAA2C;EACpEC,oBAAoB,EAAE,mDAAmD;EACzEC,IAAI,EAAE,SAAS;EACfC,sBAAsB,EAAE,oBAAoB;EAC5CC,OAAO,EAAE,MAAM;EACfC,SAAS,EAAE,gBAAgB;EAC3BC,gBAAgB,EAAE,uDAAuD;EACzEC,QAAQ,EAAE;IACNC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE;EACX,CAAC;EACDC,eAAe,EAAE,YAAY;EAC7BC,iBAAiB,EAAE,qBAAqB;EACxCC,eAAe,EAAE,yEAAyE;EAC1FC,kBAAkB,EAAE,wOAAwO;EAC5PC,UAAU,EAAE,iBAAiB;EAC7BC,iBAAiB,EAAE,gBAAgB;EACnCC,iBAAiB,EAAE,wIAAwI;EAC3JC,iBAAiB,EAAE,8BAA8B;EACjDC,aAAa,EAAE,oBAAoB;EACnCC,WAAW,EAAE,iBAAiB;EAC9BC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,mBAAmB;EAC7BC,cAAc,EAAE,yFAAyF;EACzGC,oBAAoB,EAAE,yCAAyC;EAC/DC,eAAe,EAAE;IACbzG,MAAM,EAAE,sBAAsB;IAC9B0G,MAAM,EAAE,iBAAiB;IACzBC,OAAO,EAAE,kBAAkB;IAC3BC,WAAW,EAAE,yCAAyC;IACtDC,gBAAgB,EAAE,gEAAgE;IAClFC,SAAS,EAAE,6DAA6D;IACxEC,YAAY,EAAE,+CAA+C;IAC7DC,WAAW,EAAE,sDAAsD;IACnEC,aAAa,EAAE;EACnB,CAAC;EACDC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,kBAAkB;EAC7BC,aAAa,EAAE;IACXC,SAAS,EAAE,6FAA6F;IACxGC,MAAM,EAAE,qBAAqB;IAC7BC,YAAY,EAAE,2IAA2I;IACzJC,WAAW,EAAE,qDAAqD;IAClEC,kBAAkB,EAAE,mDAAmD;IACvEC,kBAAkB,EAAE,mCAAmC;IACvDC,kBAAkB,EAAE,4CAA4C;IAChEC,kBAAkB,EAAE,oCAAoC;IACxDC,gBAAgB,EAAE,+BAA+B;IACjDC,kBAAkB,EAAE,+CAA+C;IACnEC,kBAAkB,EAAE,0CAA0C;IAC9DC,kBAAkB,EAAE,8DAA8D;IAClFC,kBAAkB,EAAE,iDAAiD;IACrEC,UAAU,EAAE,iDAAiD;IAC7DC,aAAa,EAAE,4BAA4B;IAC3CC,SAAS,EAAE,2EAA2E;IACtFC,aAAa,EAAE,mBAAmB;IAClCC,kBAAkB,EAAE,sCAAsC;IAC1DC,eAAe,EAAE,qCAAqC;IACtDC,gBAAgB,EAAE;EACtB,CAAC;EACDC,UAAU,EAAE,qCAAqC;EACjDC,MAAM,EAAE,yBAAyB;EACjCC,GAAG,EAAE,aAAa;EAClBC,WAAW,EAAE,uGAAuG;EACpHC,oBAAoB,EAAE,0DAA0D;EAChFC,WAAW,EAAE,sCAAsC;EACnDC,kBAAkB,EAAE,wBAAwB;EAC5CC,iBAAiB,EAAE,yBAAyB;EAC5CC,SAAS,EAAE,iBAAiB;EAC5BC,aAAa,EAAE,gFAAgF;EAC/FC,UAAU,EAAE,cAAc;EAC1BC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE,iCAAiC;EAC/CC,eAAe,EAAE,oEAAoE;EACrFC,YAAY,EAAE,qEAAqE;EACnFC,kBAAkB,EAAE,oCAAoC;EACxDC,gBAAgB,EAAE,0CAA0C;EAC5DC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,YAAY,EAAE,iBAAiB;EAC/BC,UAAU,EAAE,yCAAyC;EACrDC,eAAe,EAAE,iBAAiB;EAClCC,UAAU,EAAE,uBAAuB;EACnCC,cAAc,EAAE,gCAAgC;EAChDC,aAAa,EAAE,+BAA+B;EAC9CC,MAAM,EAAE,QAAQ;EAChBC,iBAAiB,EAAE,+BAA+B;EAClDC,mBAAmB,EAAE,sCAAsC;EAC3DC,cAAc,EAAE,eAAe;EAC/BC,iBAAiB,EAAE,aAAa;EAChCC,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,wCAAwC;EACrDC,eAAe,EAAE,iCAAiC;EAClDC,gBAAgB,EAAE,yCAAyC;EAC3DC,eAAe,EAAE,gEAAgE;EACjFC,uBAAuB,EAAE,kEAAkE;EAC3FC,6BAA6B,EAAE,+CAA+C;EAC9EC,kBAAkB,EAAE,4EAA4E;EAChGC,SAAS,EAAE,mBAAmB;EAC9BC,oBAAoB,EAAE,4CAA4C;EAClEC,WAAW,EAAE,8BAA8B;EAC3CC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,oDAAoD;EACjEC,kBAAkB,EAAE,uNAAuN;EAC3OC,OAAO,EAAE,aAAa;EACtBC,eAAe,EAAE,8BAA8B;EAC/CC,iBAAiB,EAAE,qOAAqO;EACxPC,eAAe,EAAE,iBAAiB;EAClCC,wBAAwB,EAAE,0GAA0G;EACpIC,WAAW,EAAE,gJAAgJ;EAC7JC,iBAAiB,EAAE,gFAAgF;EACnGC,UAAU,EAAE,mBAAmB;EAC/BC,qBAAqB,EAAE,6BAA6B;EACpDC,qBAAqB,EAAE,gDAAgD;EACvEC,yBAAyB,EAAE,uCAAuC;EAClEC,wBAAwB,EAAE,kCAAkC;EAC5DC,sBAAsB,EAAE,8SAA8S;EACtUC,aAAa,EAAE,iJAAiJ;EAChKC,YAAY,EAAE,6CAA6C;EAC3DC,aAAa,EAAE,8CAA8C;EAC7DC,aAAa,EAAE,cAAc;EAC7BC,GAAG,EAAE,QAAQ;EACbC,OAAO,EAAE,mBAAmB;EAC5BC,oBAAoB,EAAE,kCAAkC;EACxDC,eAAe,EAAE,OAAO;EACxBC,YAAY,EAAE,6BAA6B;EAC3CC,kBAAkB,EAAE,sBAAsB;EAC1CC,cAAc,EAAE,6BAA6B;EAC7CC,UAAU,EAAE,8BAA8B;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,2CAA2C;EAC7DC,aAAa,EAAE,sJAAsJ;EACrKC,gBAAgB,EAAE,kCAAkC;EACpDC,aAAa,EAAE,cAAc;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,iBAAiB;EAC7BC,cAAc,EAAE,gBAAgB;EAChCC,eAAe,EAAE,0BAA0B;EAC3CC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE,mBAAmB;EAC1BC,mBAAmB,EAAE,gDAAgD;EACrEC,qBAAqB,EAAE,oCAAoC;EAC3DC,sBAAsB,EAAE,mCAAmC;EAC3DC,kBAAkB,EAAE,4CAA4C;EAChEC,mBAAmB,EAAE,2CAA2C;EAChEC,kBAAkB,EAAE,iCAAiC;EACrDC,iBAAiB,EAAE,yDAAyD;EAC5EC,uBAAuB,EAAE,yDAAyD;EAClFC,cAAc,EAAE,0EAA0E;EAC1FC,YAAY,EAAE,aAAa;EAC3BC,iBAAiB,EAAE,qBAAqB;EACxCC,gBAAgB,EAAE,uBAAuB;EACzCC,iBAAiB,EAAE,sFAAsF;EACzGC,gBAAgB,EAAE,qBAAqB;EACvCC,qBAAqB,EAAE,wBAAwB;EAC/CC,eAAe,EAAE,kCAAkC;EACnDC,oBAAoB,EAAE,oBAAoB;EAC1CC,kBAAkB,EAAE,mBAAmB;EACvCC,gBAAgB,EAAE,uCAAuC;EACzDC,oBAAoB,EAAE,+DAA+D;EACrFC,uBAAuB,EAAE,qJAAqJ;EAC9KC,YAAY,EAAE;IACVC,IAAI,EAAE,oCAAoC;IAC1CC,IAAI,EAAE,2CAA2C;IACjDC,IAAI,EAAE,wEAAwE;IAC9EC,IAAI,EAAE;EACV,CAAC;EACDC,eAAe,EAAE,+BAA+B;EAChDC,uBAAuB,EAAE,oEAAoE;EAC7FC,gBAAgB,EAAE,yBAAyB;EAC3CC,eAAe,EAAE,aAAa;EAC9BC,kBAAkB,EAAE,8BAA8B;EAClDC,kBAAkB,EAAE,oBAAoB;EACxCC,mBAAmB,EAAE,0CAA0C;EAC/DC,gBAAgB,EAAE,6BAA6B;EAC/CC,oBAAoB,EAAE,wFAAwF;EAC9GC,kBAAkB,EAAE,yBAAyB;EAC7CC,mBAAmB,EAAE,kCAAkC;EACvDC,UAAU,EAAE,4BAA4B;EACxCC,kBAAkB,EAAE,8CAA8C;EAClEC,IAAI,EAAE,2BAA2B;EACjCC,mBAAmB,EAAE;IACjBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,iCAAiC;IACxCC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,6BAA6B;IACpCC,KAAK,EAAE,sCAAsC;IAC7CC,KAAK,EAAE;EACX,CAAC;EACDC,YAAY,EAAE,iBAAiB;EAC/BC,cAAc,EAAE;IACZP,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,iCAAiC;IACxCC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,6BAA6B;IACpCC,KAAK,EAAE,2DAA2D;IAClEC,KAAK,EAAE,+BAA+B;IACtCG,KAAK,EAAE,yBAAyB;IAChCC,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE;EACX,CAAC;EACDC,OAAO,EAAE,WAAW;EACpBC,WAAW,EAAE,oBAAoB;EACjCC,kBAAkB,EAAE,oBAAoB;EACxCC,UAAU,EAAE,cAAc;EAC1BC,kBAAkB,EAAE,kFAAkF;EACtGC,mBAAmB,EAAE,0EAA0E;EAC/FC,aAAa,EAAE,yBAAyB;EACxCC,eAAe,EAAE,qDAAqD;EACtEC,UAAU,EAAE,gBAAgB;EAC5BC,WAAW,EAAE,eAAe;EAC5BC,cAAc,EAAE,yGAAyG;EACzHC,yBAAyB,EAAE,uCAAuC;EAClEC,mBAAmB,EAAE,4BAA4B;EACjDC,eAAe,EAAE,uBAAuB;EACxCC,kBAAkB,EAAE,uBAAuB;EAC3CC,wBAAwB,EAAE,2BAA2B;EACrDC,mBAAmB,EAAE,iCAAiC;EACtDC,sBAAsB,EAAE,yBAAyB;EACjDC,iBAAiB,EAAE,kCAAkC;EACrDC,oBAAoB,EAAE,sBAAsB;EAC5CC,gBAAgB,EAAE;IACdC,QAAQ,EAAE,sBAAsB;IAChCC,YAAY,EAAE,eAAe;IAC7BC,OAAO,EAAE,aAAa;IACtBhS,OAAO,EAAE,SAAS;IAClBoB,UAAU,EAAE,aAAa;IACzB6Q,WAAW,EAAE,oBAAoB;IACjCC,gBAAgB,EAAE,yDAAyD;IAC3EC,WAAW,EAAE,qBAAqB;IAClCC,YAAY,EAAE,gBAAgB;IAC9BC,eAAe,EAAE,eAAe;IAChCC,eAAe,EAAE,6BAA6B;IAC9CC,sBAAsB,EAAE,8BAA8B;IACtDC,WAAW,EAAE,mBAAmB;IAChCC,mBAAmB,EAAE;EACzB,CAAC;EACDC,eAAe,EAAE,2CAA2C;EAC5DC,gBAAgB,EAAE,iEAAiE;EACnFC,mBAAmB,EAAE,4DAA4D;EACjFC,mBAAmB,EAAE,+DAA+D;EACpFC,eAAe,EAAE,6BAA6B;EAC9CC,qBAAqB,EAAE,8BAA8B;EACrDC,cAAc,EAAE,0BAA0B;EAC1CC,QAAQ,EAAE,iBAAiB;EAC3BC,cAAc,EAAE,0BAA0B;EAC1CC,UAAU,EAAE,wBAAwB;EACpCC,SAAS,EAAE,cAAc;EACzBC,UAAU,EAAE;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}