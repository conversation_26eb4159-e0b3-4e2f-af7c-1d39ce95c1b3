{"ast": null, "code": "import Cookies from 'js-cookie';\n\n// 判断是否是服务端 通过协议为 https/http 来区分\nexport const isServer = () => {\n  return window.location.protocol === 'https:';\n};\nNumber.isInteger = Number.isInteger || function (value) {\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n};\nexport const VueCookie = {\n  defaultOption: {},\n  install: function (Vue) {\n    Vue.prototype.$cookie = this;\n    Vue.$cookie = this;\n  },\n  set: function (name, value, daysOrOptions) {\n    let opts = daysOrOptions === void 0 ? {} : daysOrOptions;\n    if (Number.isInteger(daysOrOptions)) {\n      opts = {\n        ...this.defaultOption,\n        expires: daysOrOptions,\n        secure: isServer() ? true : null\n      };\n    } else {\n      opts = {\n        ...this.defaultOption,\n        ...opts,\n        secure: isServer() ? true : null\n      };\n    }\n    return Cookies.set(name, value, opts);\n  },\n  get: function (name) {\n    return Cookies.get(name);\n  },\n  delete: function (name, options) {\n    let opts = {\n      expires: -1\n    };\n    if (options !== undefined) {\n      opts = Object.assign(options, opts);\n    }\n    this.set(name, '', opts);\n  }\n};", "map": {"version": 3, "names": ["Cookies", "isServer", "window", "location", "protocol", "Number", "isInteger", "value", "isFinite", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultOption", "install", "<PERSON><PERSON>", "prototype", "$cookie", "set", "name", "daysOrOptions", "opts", "expires", "secure", "get", "delete", "options", "undefined", "Object", "assign"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/plugins/cookie/cookie.js"], "sourcesContent": ["import Cookies from 'js-cookie';\n\n// 判断是否是服务端 通过协议为 https/http 来区分\nexport const isServer = () => {\n    return window.location.protocol === 'https:';\n};\n\nNumber.isInteger = Number.isInteger || function(value) {\n    return typeof value === 'number' &&\n        isFinite(value) &&\n        Math.floor(value) === value;\n};\n\nexport const VueCookie = {\n    defaultOption: {},\n\n    install: function(Vue) {\n        Vue.prototype.$cookie = this;\n        Vue.$cookie = this;\n    },\n\n    set: function(name, value, daysOrOptions) {\n        let opts = daysOrOptions === void (0) ? {} : daysOrOptions;\n        if (Number.isInteger(daysOrOptions)) {\n            opts = { ...this.defaultOption, expires: daysOrOptions, secure: isServer() ? true : null };\n        } else {\n            opts = { ...this.defaultOption, ...opts, secure: isServer() ? true : null };\n        }\n        return Cookies.set(name, value, opts);\n    },\n\n    get: function(name) {\n        return Cookies.get(name);\n    },\n\n    delete: function(name, options) {\n        let opts = { expires: -1 };\n        if (options !== undefined) {\n            opts = Object.assign(options, opts);\n        }\n        this.set(name, '', opts);\n    },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;;AAE/B;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC1B,OAAOC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ;AAChD,CAAC;AAEDC,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI,UAASC,KAAK,EAAE;EACnD,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAC5BC,QAAQ,CAACD,KAAK,CAAC,IACfE,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK;AACnC,CAAC;AAED,OAAO,MAAMI,SAAS,GAAG;EACrBC,aAAa,EAAE,CAAC,CAAC;EAEjBC,OAAO,EAAE,SAAAA,CAASC,GAAG,EAAE;IACnBA,GAAG,CAACC,SAAS,CAACC,OAAO,GAAG,IAAI;IAC5BF,GAAG,CAACE,OAAO,GAAG,IAAI;EACtB,CAAC;EAEDC,GAAG,EAAE,SAAAA,CAASC,IAAI,EAAEX,KAAK,EAAEY,aAAa,EAAE;IACtC,IAAIC,IAAI,GAAGD,aAAa,KAAK,KAAM,CAAE,GAAG,CAAC,CAAC,GAAGA,aAAa;IAC1D,IAAId,MAAM,CAACC,SAAS,CAACa,aAAa,CAAC,EAAE;MACjCC,IAAI,GAAG;QAAE,GAAG,IAAI,CAACR,aAAa;QAAES,OAAO,EAAEF,aAAa;QAAEG,MAAM,EAAErB,QAAQ,CAAC,CAAC,GAAG,IAAI,GAAG;MAAK,CAAC;IAC9F,CAAC,MAAM;MACHmB,IAAI,GAAG;QAAE,GAAG,IAAI,CAACR,aAAa;QAAE,GAAGQ,IAAI;QAAEE,MAAM,EAAErB,QAAQ,CAAC,CAAC,GAAG,IAAI,GAAG;MAAK,CAAC;IAC/E;IACA,OAAOD,OAAO,CAACiB,GAAG,CAACC,IAAI,EAAEX,KAAK,EAAEa,IAAI,CAAC;EACzC,CAAC;EAEDG,GAAG,EAAE,SAAAA,CAASL,IAAI,EAAE;IAChB,OAAOlB,OAAO,CAACuB,GAAG,CAACL,IAAI,CAAC;EAC5B,CAAC;EAEDM,MAAM,EAAE,SAAAA,CAASN,IAAI,EAAEO,OAAO,EAAE;IAC5B,IAAIL,IAAI,GAAG;MAAEC,OAAO,EAAE,CAAC;IAAE,CAAC;IAC1B,IAAII,OAAO,KAAKC,SAAS,EAAE;MACvBN,IAAI,GAAGO,MAAM,CAACC,MAAM,CAACH,OAAO,EAAEL,IAAI,CAAC;IACvC;IACA,IAAI,CAACH,GAAG,CAACC,IAAI,EAAE,EAAE,EAAEE,IAAI,CAAC;EAC5B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}