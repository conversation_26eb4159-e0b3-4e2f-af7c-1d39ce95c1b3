{"ast": null, "code": "export default {\n  certificationRenewalDialog: {\n    renewalTitle: 'Digital certificate renewal',\n    renewalTip: 'Your certificate has expired, please renew it in time to avoid document signing',\n    renewalTip2: 'If the entity holding the certificate for this account has changed, please update the real-name authentication of the account.',\n    previousIdentity: 'Subject holding the certificate:',\n    previousCA: 'Original certification authority: ',\n    previousExpiryDate: 'Validity period of the original certificate:',\n    previousId: 'Original certificate serial number:',\n    renewal: 'Agree to renew',\n    reject: 'The personal real-name information is incorrect; I need to make changes.',\n    rejectMessage: \"To proceed with changing the current account's real-name authentication, the existing real-name authentication of '{name}' needs to be rejected first. Would you like to continue?\",\n    rejectConfirm: 'Confirm',\n    renewalTips1: 'Why is this notification displayed?',\n    renewalTips2: 'To ensure the legal validity of your electronic contracts, all contracts you participate in will be signed using your digital certificate, with signatures and timestamps embedded in the final contract document as evidence.Digital certificates can only be used within their validity period. Due to compliance requirements, BestSign will not automatically renew your certificate. If your certificate expires, you will need to complete the renewal process before proceeding with further business operations.',\n    renewalTips3: 'For detailed information, please refer to the',\n    renewalTips4: 'BestSign Digital Certificate Usage Agreement.',\n    renewalTips5: '',\n    tip7: 'Your company\\'s identity verification information in BestSign does not match your company\\'s latest corporate information, resulting in failure to renew your enterprise certificate. After updating your identity verification information to match the latest corporate details, you will be able to successfully renew your enterprise certificate!',\n    tip8: 'Please copy the link below to your company\\'s administrator ({adminEmpName}, {adminAccount}) on the BestSign platform. After completing the identity verification update, you can continue signing this contract!',\n    tip9: 'Copy link',\n    tip10: 'Due to inconsistent company information, your company\\'s certificate renewal has failed. [{currentEmpName}] invites you to complete the company information update. Please copy the link to your browser to proceed: {link}',\n    tip11: 'Copied Successfully',\n    tip12: 'Your company\\'s identity verification information on BestSign is inconsistent with your company\\'s latest information, resulting in the failure of company certificate renewal. After completing the identity verification information update with your latest company information, the company certificate can be successfully renewed!',\n    tip13: 'Your company is the main enterprise of the group \\\"【{groupName}】\\\" and cannot arbitrarily change the identity verification information. Please contact your account manager to guide you through the identity verification change process, after which you can successfully renew your digital certificate!',\n    tip14: 'Go to Change',\n    tip15: 'Close',\n    tip16: 'Your company\\'s identity verification information in BestSign is inconsistent with your company\\'s latest corporate information, resulting in the failure of enterprise certificate renewal. Once you complete the identity verification update with the latest corporate information, you will be able to successfully renew your enterprise certificate!',\n    tip17: 'We regret to inform you that the enterprise certificate renewal cannot be completed due to your company\\'s abnormal business status. If you have verified that your company\\'s business status is normal, please call BestSign customer service (************) for assistance.'\n  },\n  infoProtectDialog: {\n    userAuth: '使用刷脸服务须同意',\n    titleWithSeperator: '《上上签如何保护您的个人信息》',\n    title: '上上签如何保护您的个人信息',\n    auth: '实名认证',\n    faceSign: '刷脸签署',\n    contentDesp: '您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：',\n    detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',\n    detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',\n    detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',\n    know: '知道了'\n  },\n  signIdentityGuide: {\n    title: 'Tip',\n    requestYou: {\n      0: 'requires you',\n      1: 'requires you'\n    },\n    tipToAuth: {\n      0: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication',\n      1: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication.'\n    },\n    needResend: 'to view and sign the contract.This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again.',\n    note: 'Note:',\n    entName: 'name of enterprise',\n    identityInfo: 'Identity Information',\n    signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract',\n    inTheName: 'As',\n    of: '',\n    identity: '',\n    nameIs: 'name as',\n    IDNumIs: 'ID number as'\n  },\n  pdf: {\n    previewFail: 'File preview failed',\n    pager: 'Page {x}，{y} in total',\n    parseFailed: 'Failed to parse the pdf file, please click \"OK\" to try again',\n    confirm: 'Confirm'\n  },\n  tagManage: {\n    title: 'Set label'\n  },\n  dialogApplyJoinEnt: {\n    beenAuthenticated: 'Has been real-named',\n    assignedIdentity: 'The contract subject filled in by the sender is:',\n    entBeenAuthenticated: 'The company has been real-named, and the main administrator information is as follows:',\n    entAdminName: 'Administrator name:',\n    entAdminAccount: 'Administrator account:',\n    applyToBeAdmin: 'I want to appeal',\n    contactToJoin: 'Join the company',\n    applicant: 'Applicant',\n    inputYourName: 'Please enter your name',\n    account: 'Account',\n    send: 'Send',\n    contract: 'contract',\n    sendWishToJoin: 'You can become an administrator through account appeal, or you can send an application to the administrator to join the company',\n    applyToJoin: 'You have not joined the company and cannot sign the {alias}. Do you want to apply for joining?',\n    sentSuccessful: 'Sent successfully',\n    contractAlias: {\n      doc: '文件',\n      letter: '询征函',\n      proof: '授权书'\n    }\n  },\n  selectBizLine: {\n    title: 'Please select line of business'\n  },\n  importOffLineDoc: {\n    importDoc: 'Import contracts',\n    step0Title: 'Step 1: Confirm the name of the imported company',\n    step1Title: 'Step 2: Upload Excel',\n    step2Title: 'Step 3: Upload the contract file',\n    step1Info: 'Please download the Excel template first and fill it out before importing, the number of contracts should not exceed 1000.',\n    next: 'Next step',\n    entName: 'Company Name',\n    archiveFolder: 'Archived folder',\n    downloadExcel: 'Download Excel',\n    uploadExcel: 'Upload Excel',\n    reUploadExcel: 'Re-upload',\n    step2Info: ['1. Contract files can only be PDF or images；', '2. After placing all contract files in a folder, compress the folder into a zip (no more than 150M)；', '3. The file name including the file extension (such as .pdf) should be consistent with the file name in Excel in the second step；'],\n    uploadZip: 'Click Upload Zip',\n    reUploadZip: 'Re-upload Zip',\n    done: 'confirm',\n    back: 'Back',\n    contractTitle: 'Contract name',\n    singerAccount: 'Signer account',\n    singerName: 'Signatory name',\n    uploadSucTip: 'Upload is successful, click \"OK\" button to start importing',\n    outbox: 'Outbox',\n    fileLessThan: 'Please upload files smaller than {num}M',\n    fileTypeValid: 'Only {type} format files can be uploaded!'\n  },\n  download: {\n    contactGetDownloadCodeTip: 'Please contact the contract initiator to obtain the download code, or try to log in to our business system to download.',\n    downloadCode: 'download code',\n    hint: 'tips',\n    download: 'download',\n    plsInput: 'please put in',\n    plsInputDownloadCode: 'Please enter the download code',\n    downloadCodeError: 'wrong download code',\n    allFiles: 'all files',\n    cancel: 'cancel',\n    plsSelectFiles: 'please select a file first',\n    publicCloudDownloadTip: 'The contract to be downloaded contains otherdocuments uploaded by the signatory. Do you want to download it with the contract?',\n    hybridCloudDownloadTip: 'The contract to be downloaded contains other documents uploaded by the signer..',\n    sameTimeDownloadAttachTip: 'download other documents attached to the contract at the same time',\n    downloadContract: 'download contract',\n    downloadAttach: 'download other documents attached to the contract '\n  },\n  commonHeader: {\n    groupCertification: 'group certification',\n    goHomePage: 'Back to homepage',\n    companyPrivateSaveTypeContactTip: 'Your company adopts contract private storage. The current network is connected to the storage server',\n    companyPrivateSaveTypeNoContactTip: 'Your company adopts contract private storage mode and the current network cannot connect to the storage server',\n    advise: 'advise：',\n    checkCompanyInteralNetContact: '① Check whether the current network can access the corporate intranet',\n    checkContactServerNetContact: '② Check the contract storage server for proper operation'\n  },\n  transfer: {\n    list1: 'List 1',\n    list2: 'List 2',\n    maxSelectNum: 'Can only choose at most {maxLength}'\n  },\n  poperCascader: {\n    plsSelect: 'please select',\n    person: 'people',\n    selectNumTip: '{A}/{B} of {C} selected',\n    allSelect: 'select all'\n  },\n  authInfoChange: {\n    title: 'Real name change detection',\n    confirm: 'Confirm',\n    changeAuth: 'Real name update',\n    notifyAdmin: 'Notify the administrator',\n    notifySuccess: 'Success',\n    operateSuccess: 'Success',\n    warningTip: {\n      tip1: \"It is found that the real name information {oldAuthInfo} of your company '{entName}' on BestSign platform is not consistent with the latest information {newAuthInfo} at the Industry and Commerce Bureau. \",\n      tip2: 'To ensure compliance and effectiveness of your signed e-contracts, please use the latest business information to go through real-name authentication again. ',\n      tip3: 'This operation will not affect your current corporate information. '\n    },\n    suggestTip: {\n      tip1: \"If your company has a group structure, please contact your exclusive CSM, or call BestSign's customer service hotline ************ to update information for real-name authentication, after which you can continue to sign.\",\n      tip2: 'By clicking [Notify Administrator{adminInfo}],点击【通知管理员{adminInfo}】，',\n      tip3: ' you can immediately send a notification to the administrator to guide him/her to go through real-name authentication again. You can also notify offline to ensure timely business activities.'\n    }\n  }\n};", "map": {"version": 3, "names": ["certificationRenewalDialog", "renewalTitle", "renewalTip", "renewalTip2", "previousIdentity", "previousCA", "previousExpiryDate", "previousId", "renewal", "reject", "rejectMessage", "rejectConfirm", "renewalTips1", "renewalTips2", "renewalTips3", "renewalTips4", "renewalTips5", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "tip15", "tip16", "tip17", "infoProtectDialog", "userAuth", "titleWithSeperator", "title", "auth", "faceSign", "contentDesp", "detailTip1", "detailTip2", "detailTip3", "know", "signIdentityGuide", "requestYou", "tipToAuth", "needResend", "note", "entName", "identityInfo", "signNeedCoincidenceInfo", "inTheName", "of", "identity", "nameIs", "IDNumIs", "pdf", "previewFail", "pager", "parseFailed", "confirm", "tagManage", "dialogApplyJoinEnt", "beenAuthenticated", "assignedIdentity", "entBeenAuthenticated", "entAdminName", "entAdminAccount", "applyToBeAdmin", "contactToJoin", "applicant", "inputYourName", "account", "send", "contract", "sendWishToJoin", "apply<PERSON>oJoin", "sentSuccessful", "contractAlias", "doc", "letter", "proof", "selectBizLine", "importOffLineDoc", "importDoc", "step0Title", "step1Title", "step2Title", "step1Info", "next", "archiveFolder", "downloadExcel", "uploadExcel", "reUploadExcel", "step2Info", "uploadZip", "reUploadZip", "done", "back", "contractTitle", "singerA<PERSON>unt", "<PERSON><PERSON><PERSON>", "uploadSucTip", "outbox", "fileLessThan", "fileTypeValid", "download", "contactGetDownloadCodeTip", "downloadCode", "hint", "plsInput", "plsInputDownloadCode", "downloadCodeError", "allFiles", "cancel", "plsSelectFiles", "publicCloudDownloadTip", "hybridCloudDownloadTip", "sameTimeDownloadAttachTip", "downloadContract", "downloadAttach", "common<PERSON>eader", "groupCertification", "goHomePage", "companyPrivateSaveTypeContactTip", "companyPrivateSaveTypeNoContactTip", "advise", "checkCompanyInteralNetContact", "checkContactServerNetContact", "transfer", "list1", "list2", "maxSelectNum", "poperCascader", "plsSelect", "person", "selectNumTip", "allSelect", "authInfoChange", "changeAuth", "notifyAdmin", "notifySuccess", "operateSuccess", "warningTip", "tip1", "tip2", "tip3", "suggestTip"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/components/components-en.js"], "sourcesContent": ["export default {\n    certificationRenewalDialog: {\n        renewalTitle: 'Digital certificate renewal',\n        renewalTip: 'Your certificate has expired, please renew it in time to avoid document signing',\n        renewalTip2: 'If the entity holding the certificate for this account has changed, please update the real-name authentication of the account.',\n        previousIdentity: 'Subject holding the certificate:',\n        previousCA: 'Original certification authority: ',\n        previousExpiryDate: 'Validity period of the original certificate:',\n        previousId: 'Original certificate serial number:',\n        renewal: 'Agree to renew',\n        reject: 'The personal real-name information is incorrect; I need to make changes.',\n        rejectMessage: \"To proceed with changing the current account's real-name authentication, the existing real-name authentication of '{name}' needs to be rejected first. Would you like to continue?\",\n        rejectConfirm: 'Confirm',\n        renewalTips1: 'Why is this notification displayed?',\n        renewalTips2: 'To ensure the legal validity of your electronic contracts, all contracts you participate in will be signed using your digital certificate, with signatures and timestamps embedded in the final contract document as evidence.Digital certificates can only be used within their validity period. Due to compliance requirements, BestSign will not automatically renew your certificate. If your certificate expires, you will need to complete the renewal process before proceeding with further business operations.',\n        renewalTips3: 'For detailed information, please refer to the',\n        renewalTips4: 'BestSign Digital Certificate Usage Agreement.',\n        renewalTips5: '',\n        tip7: 'Your company\\'s identity verification information in BestSign does not match your company\\'s latest corporate information, resulting in failure to renew your enterprise certificate. After updating your identity verification information to match the latest corporate details, you will be able to successfully renew your enterprise certificate!',\n        tip8: 'Please copy the link below to your company\\'s administrator ({adminEmpName}, {adminAccount}) on the BestSign platform. After completing the identity verification update, you can continue signing this contract!',\n        tip9: 'Copy link',\n        tip10: 'Due to inconsistent company information, your company\\'s certificate renewal has failed. [{currentEmpName}] invites you to complete the company information update. Please copy the link to your browser to proceed: {link}',\n        tip11: 'Copied Successfully',\n        tip12: 'Your company\\'s identity verification information on BestSign is inconsistent with your company\\'s latest information, resulting in the failure of company certificate renewal. After completing the identity verification information update with your latest company information, the company certificate can be successfully renewed!',\n        tip13: 'Your company is the main enterprise of the group \\\"【{groupName}】\\\" and cannot arbitrarily change the identity verification information. Please contact your account manager to guide you through the identity verification change process, after which you can successfully renew your digital certificate!',\n        tip14: 'Go to Change',\n        tip15: 'Close',\n        tip16: 'Your company\\'s identity verification information in BestSign is inconsistent with your company\\'s latest corporate information, resulting in the failure of enterprise certificate renewal. Once you complete the identity verification update with the latest corporate information, you will be able to successfully renew your enterprise certificate!',\n        tip17: 'We regret to inform you that the enterprise certificate renewal cannot be completed due to your company\\'s abnormal business status. If you have verified that your company\\'s business status is normal, please call BestSign customer service (************) for assistance.',\n    },\n    infoProtectDialog: {\n        userAuth: '使用刷脸服务须同意',\n        titleWithSeperator: '《上上签如何保护您的个人信息》',\n        title: '上上签如何保护您的个人信息',\n        auth: '实名认证',\n        faceSign: '刷脸签署',\n        contentDesp: '您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：',\n        detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',\n        detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',\n        detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',\n        know: '知道了',\n    },\n    signIdentityGuide: {\n        title: 'Tip',\n        requestYou: {\n            0: 'requires you',\n            1: 'requires you',\n        },\n        tipToAuth: {\n            0: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication',\n            1: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication.',\n        },\n        needResend: 'to view and sign the contract.This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again.',\n        note: 'Note:',\n        entName: 'name of enterprise',\n        identityInfo: 'Identity Information',\n        signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract',\n        inTheName: 'As',\n        of: '',\n        identity: '',\n        nameIs: 'name as',\n        IDNumIs: 'ID number as',\n    },\n    pdf: {\n        previewFail: 'File preview failed',\n        pager: 'Page {x}，{y} in total',\n        parseFailed: 'Failed to parse the pdf file, please click \"OK\" to try again',\n        confirm: 'Confirm',\n    },\n    tagManage: {\n        title: 'Set label',\n    },\n    dialogApplyJoinEnt: {\n        beenAuthenticated: 'Has been real-named',\n        assignedIdentity: 'The contract subject filled in by the sender is:',\n        entBeenAuthenticated: 'The company has been real-named, and the main administrator information is as follows:',\n        entAdminName: 'Administrator name:',\n        entAdminAccount: 'Administrator account:',\n        applyToBeAdmin: 'I want to appeal',\n        contactToJoin: 'Join the company',\n        applicant: 'Applicant',\n        inputYourName: 'Please enter your name',\n        account: 'Account',\n        send: 'Send',\n        contract: 'contract',\n        sendWishToJoin: 'You can become an administrator through account appeal, or you can send an application to the administrator to join the company',\n        applyToJoin: 'You have not joined the company and cannot sign the {alias}. Do you want to apply for joining?',\n        sentSuccessful: 'Sent successfully',\n        contractAlias: {\n            doc: '文件',\n            letter: '询征函',\n            proof: '授权书',\n        },\n    },\n    selectBizLine: {\n        title: 'Please select line of business',\n    },\n    importOffLineDoc: {\n        importDoc: 'Import contracts',\n        step0Title: 'Step 1: Confirm the name of the imported company',\n        step1Title: 'Step 2: Upload Excel',\n        step2Title: 'Step 3: Upload the contract file',\n        step1Info: 'Please download the Excel template first and fill it out before importing, the number of contracts should not exceed 1000.',\n        next: 'Next step',\n        entName: 'Company Name',\n        archiveFolder: 'Archived folder',\n        downloadExcel: 'Download Excel',\n        uploadExcel: 'Upload Excel',\n        reUploadExcel: 'Re-upload',\n        step2Info: ['1. Contract files can only be PDF or images；', '2. After placing all contract files in a folder, compress the folder into a zip (no more than 150M)；', '3. The file name including the file extension (such as .pdf) should be consistent with the file name in Excel in the second step；'],\n        uploadZip: 'Click Upload Zip',\n        reUploadZip: 'Re-upload Zip',\n        done: 'confirm',\n        back: 'Back',\n        contractTitle: 'Contract name',\n        singerAccount: 'Signer account',\n        singerName: 'Signatory name',\n        uploadSucTip: 'Upload is successful, click \"OK\" button to start importing',\n        outbox: 'Outbox',\n        fileLessThan: 'Please upload files smaller than {num}M',\n        fileTypeValid: 'Only {type} format files can be uploaded!',\n\n    },\n    download: {\n        contactGetDownloadCodeTip: 'Please contact the contract initiator to obtain the download code, or try to log in to our business system to download.',\n        downloadCode: 'download code',\n        hint: 'tips',\n        download: 'download',\n        plsInput: 'please put in',\n        plsInputDownloadCode: 'Please enter the download code',\n        downloadCodeError: 'wrong download code',\n        allFiles: 'all files',\n        cancel: 'cancel',\n        plsSelectFiles: 'please select a file first',\n        publicCloudDownloadTip: 'The contract to be downloaded contains otherdocuments uploaded by the signatory. Do you want to download it with the contract?',\n        hybridCloudDownloadTip: 'The contract to be downloaded contains other documents uploaded by the signer..',\n        sameTimeDownloadAttachTip: 'download other documents attached to the contract at the same time',\n        downloadContract: 'download contract',\n        downloadAttach: 'download other documents attached to the contract ',\n    },\n    commonHeader: {\n        groupCertification: 'group certification',\n        goHomePage: 'Back to homepage',\n        companyPrivateSaveTypeContactTip: 'Your company adopts contract private storage. The current network is connected to the storage server',\n        companyPrivateSaveTypeNoContactTip: 'Your company adopts contract private storage mode and the current network cannot connect to the storage server',\n        advise: 'advise：',\n        checkCompanyInteralNetContact: '① Check whether the current network can access the corporate intranet',\n        checkContactServerNetContact: '② Check the contract storage server for proper operation',\n    },\n    transfer: {\n        list1: 'List 1',\n        list2: 'List 2',\n        maxSelectNum: 'Can only choose at most {maxLength}',\n    },\n    poperCascader: {\n        plsSelect: 'please select',\n        person: 'people',\n        selectNumTip: '{A}/{B} of {C} selected',\n        allSelect: 'select all',\n    },\n    authInfoChange: {\n        title: 'Real name change detection',\n        confirm: 'Confirm',\n        changeAuth: 'Real name update',\n        notifyAdmin: 'Notify the administrator',\n        notifySuccess: 'Success',\n        operateSuccess: 'Success',\n        warningTip: {\n            tip1: \"It is found that the real name information {oldAuthInfo} of your company '{entName}' on BestSign platform is not consistent with the latest information {newAuthInfo} at the Industry and Commerce Bureau. \",\n            tip2: 'To ensure compliance and effectiveness of your signed e-contracts, please use the latest business information to go through real-name authentication again. ',\n            tip3: 'This operation will not affect your current corporate information. ',\n        },\n        suggestTip: {\n            tip1: \"If your company has a group structure, please contact your exclusive CSM, or call BestSign's customer service hotline ************ to update information for real-name authentication, after which you can continue to sign.\",\n            tip2: 'By clicking [Notify Administrator{adminInfo}],点击【通知管理员{adminInfo}】，',\n            tip3: ' you can immediately send a notification to the administrator to guide him/her to go through real-name authentication again. You can also notify offline to ensure timely business activities.',\n        },\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,0BAA0B,EAAE;IACxBC,YAAY,EAAE,6BAA6B;IAC3CC,UAAU,EAAE,iFAAiF;IAC7FC,WAAW,EAAE,gIAAgI;IAC7IC,gBAAgB,EAAE,kCAAkC;IACpDC,UAAU,EAAE,oCAAoC;IAChDC,kBAAkB,EAAE,8CAA8C;IAClEC,UAAU,EAAE,qCAAqC;IACjDC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE,0EAA0E;IAClFC,aAAa,EAAE,oLAAoL;IACnMC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,qCAAqC;IACnDC,YAAY,EAAE,0fAA0f;IACxgBC,YAAY,EAAE,+CAA+C;IAC7DC,YAAY,EAAE,+CAA+C;IAC7DC,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE,wVAAwV;IAC9VC,IAAI,EAAE,mNAAmN;IACzNC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,6NAA6N;IACpOC,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAE,0UAA0U;IACjVC,KAAK,EAAE,6SAA6S;IACpTC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,4VAA4V;IACnWC,KAAK,EAAE;EACX,CAAC;EACDC,iBAAiB,EAAE;IACfC,QAAQ,EAAE,WAAW;IACrBC,kBAAkB,EAAE,iBAAiB;IACrCC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,mCAAmC;IAChDC,UAAU,EAAE,kFAAkF;IAC9FC,UAAU,EAAE,gJAAgJ;IAC5JC,UAAU,EAAE,uDAAuD;IACnEC,IAAI,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACfR,KAAK,EAAE,KAAK;IACZS,UAAU,EAAE;MACR,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE;MACP,CAAC,EAAE,iHAAiH;MACpH,CAAC,EAAE;IACP,CAAC;IACDC,UAAU,EAAE,+MAA+M;IAC3NC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,oBAAoB;IAC7BC,YAAY,EAAE,sBAAsB;IACpCC,uBAAuB,EAAE,wEAAwE;IACjGC,SAAS,EAAE,IAAI;IACfC,EAAE,EAAE,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE;EACb,CAAC;EACDC,GAAG,EAAE;IACDC,WAAW,EAAE,qBAAqB;IAClCC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,8DAA8D;IAC3EC,OAAO,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACP1B,KAAK,EAAE;EACX,CAAC;EACD2B,kBAAkB,EAAE;IAChBC,iBAAiB,EAAE,qBAAqB;IACxCC,gBAAgB,EAAE,kDAAkD;IACpEC,oBAAoB,EAAE,wFAAwF;IAC9GC,YAAY,EAAE,qBAAqB;IACnCC,eAAe,EAAE,wBAAwB;IACzCC,cAAc,EAAE,kBAAkB;IAClCC,aAAa,EAAE,kBAAkB;IACjCC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,wBAAwB;IACvCC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,iIAAiI;IACjJC,WAAW,EAAE,gGAAgG;IAC7GC,cAAc,EAAE,mBAAmB;IACnCC,aAAa,EAAE;MACXC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE;IACX;EACJ,CAAC;EACDC,aAAa,EAAE;IACX/C,KAAK,EAAE;EACX,CAAC;EACDgD,gBAAgB,EAAE;IACdC,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE,kDAAkD;IAC9DC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE,kCAAkC;IAC9CC,SAAS,EAAE,4HAA4H;IACvIC,IAAI,EAAE,WAAW;IACjBzC,OAAO,EAAE,cAAc;IACvB0C,aAAa,EAAE,iBAAiB;IAChCC,aAAa,EAAE,gBAAgB;IAC/BC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAE,WAAW;IAC1BC,SAAS,EAAE,CAAC,8CAA8C,EAAE,sGAAsG,EAAE,mIAAmI,CAAC;IACxSC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,eAAe;IAC9BC,aAAa,EAAE,gBAAgB;IAC/BC,UAAU,EAAE,gBAAgB;IAC5BC,YAAY,EAAE,4DAA4D;IAC1EC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,yCAAyC;IACvDC,aAAa,EAAE;EAEnB,CAAC;EACDC,QAAQ,EAAE;IACNC,yBAAyB,EAAE,yHAAyH;IACpJC,YAAY,EAAE,eAAe;IAC7BC,IAAI,EAAE,MAAM;IACZH,QAAQ,EAAE,UAAU;IACpBI,QAAQ,EAAE,eAAe;IACzBC,oBAAoB,EAAE,gCAAgC;IACtDC,iBAAiB,EAAE,qBAAqB;IACxCC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,QAAQ;IAChBC,cAAc,EAAE,4BAA4B;IAC5CC,sBAAsB,EAAE,gIAAgI;IACxJC,sBAAsB,EAAE,iFAAiF;IACzGC,yBAAyB,EAAE,oEAAoE;IAC/FC,gBAAgB,EAAE,mBAAmB;IACrCC,cAAc,EAAE;EACpB,CAAC;EACDC,YAAY,EAAE;IACVC,kBAAkB,EAAE,qBAAqB;IACzCC,UAAU,EAAE,kBAAkB;IAC9BC,gCAAgC,EAAE,sGAAsG;IACxIC,kCAAkC,EAAE,gHAAgH;IACpJC,MAAM,EAAE,SAAS;IACjBC,6BAA6B,EAAE,uEAAuE;IACtGC,4BAA4B,EAAE;EAClC,CAAC;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;EAClB,CAAC;EACDC,aAAa,EAAE;IACXC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,yBAAyB;IACvCC,SAAS,EAAE;EACf,CAAC;EACDC,cAAc,EAAE;IACZvG,KAAK,EAAE,4BAA4B;IACnCyB,OAAO,EAAE,SAAS;IAClB+E,UAAU,EAAE,kBAAkB;IAC9BC,WAAW,EAAE,0BAA0B;IACvCC,aAAa,EAAE,SAAS;IACxBC,cAAc,EAAE,SAAS;IACzBC,UAAU,EAAE;MACRC,IAAI,EAAE,6MAA6M;MACnNC,IAAI,EAAE,8JAA8J;MACpKC,IAAI,EAAE;IACV,CAAC;IACDC,UAAU,EAAE;MACRH,IAAI,EAAE,8NAA8N;MACpOC,IAAI,EAAE,qEAAqE;MAC3EC,IAAI,EAAE;IACV;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}