{"ast": null, "code": "// 语言为俄文时的文案\nimport utils from './module/utils/utils-ru.js';\nimport mixin from './module/mixins/mixins-ru.js';\nimport components from './module/components/components-ru.js';\n\n// import docList from './module/docList/docList-ru.js';\n// import console from './module/console/console-ru.js';\n// import userCentral from './module/usercentral/usercentral-ru.js';\n// import home from './module/home/<USER>';\nimport sign from './module/sign/sign-ru.js';\n// import entAuth from './module/entAuth/entAuth-ru.js';\n\nexport default {\n  ...utils,\n  ...mixin,\n  ...components,\n  // ...docList,\n  login: {\n    pswLogin: 'Пароль для входа',\n    usePswLogin: '«Войти с паролем»',\n    verifyLogin: 'Код подтверждения для входа',\n    useVerifyLogin: '«Войти с кодом подтверждения»',\n    scanLogin: 'Сканирование кода входа',\n    scanFailure: 'Срок действия QR-кода истек, пожалуйста, обновите и попробуйте снова',\n    scanSuccess: 'Сканирование кода успешно',\n    scanLoginTip: 'Пожалуйста, используйте приложение BestSign, для входа отсканируйте APP',\n    appLoginTip: 'Пожалуйста, нажмите «Войти в приложение BestSign»',\n    downloadApp: 'Скачать приложение BestSign',\n    forgetPsw: 'Забыли пароль',\n    login: 'Войти',\n    noAccount: 'Нет аккаунта',\n    registerNow: 'Зарегистрируйтесь сейчас',\n    accountPlaceholder: 'Введите пароль',\n    passwordPlaceholder: 'Пожалуйста, введите пароль для входа',\n    pictureVer: 'Пожалуйста, заполните содержание на картинке»',\n    verifyCodePlaceholder: 'Пожалуйста, введите 6-значный код подтверждения',\n    getVerifyCode: 'Получить код подтверждения',\n    noRegister: 'Еще не зарегистрирован',\n    or: 'или',\n    errAccountOrPwdTip: 'Введенный вами пароль не соответствует номеру учетной записи?',\n    errAccountOrPwdTip2: 'Введенный вами пароль не соответствует номеру учетной записи.',\n    errEmailOrTel: 'Пожалуйста, введите правильный E-mail или номер телефона!',\n    errPwd: 'Пожалуйста, введите правильный пароль!',\n    verCodeFormatErr: 'Ошибочный код подтверждения',\n    grapVerCodeErr: 'Ошибка графического кода подтверждения',\n    grapVerCodeFormatErr: 'Ошибка формата графического кода подтверждения',\n    lackAccount: 'Пожалуйста, заполните номер счета и затем получите',\n    lackGrapCode: 'Пожалуйста, сначала заполните графический код подтверждения.',\n    getVerCodeTip: '«Пожалуйста, получите код подтверждения»',\n    loginView: 'Войдите и просмотрите договор',\n    regView: 'Зарегистрируйтесь и просмотрите договор',\n    takeViewBtn: '登录并签署',\n    resendCode: 'Получить заново',\n    regTip: 'После ввода правильного кода подтверждения, BestSign создаvn для вас аккаунт.',\n    haveRead: 'Я прочитал и согласился',\n    bestsignAgreement: 'Сервисное соглашение BestSign',\n    and: 'и',\n    digitalCertificateAgreement: 'Соглашение об использовании цифрового сертификата',\n    privacyPolicy: 'Политика конфиденциальности',\n    sendSuc: 'Успешно отправлен',\n    lackVerCode: 'Пожалуйста, сначала введите код подтверждения',\n    lackPsw: 'Пожалуйста, сначала введите ваш пароль',\n    notMatch: 'Введенный вами пароль и аккаунт не соответствует',\n    cookieTip: 'Не удается прочитать и записать файлы cookie, проверьте, нет ли режима трассировки / инкогнито или других запрещенных файлов cookie',\n    wrongLink: 'Нелегальная ссылка',\n    footerTips: 'Услуга электронной подписи <span> BestSign </span> предоставляет',\n    bestSign: 'BestSign',\n    bestSignDescription: 'Лидер индустрии электронных <br/> контрактов',\n    /** 忘记密码 /forgotPassword start */\n    forgetPswStep: 'Подтвердить зарегестрированный аккаунт | Сбросить пароль',\n    pictureVerCodeInput: 'Графический код подтверждения | Пожалуйста, заполните содержание изображения',\n    accountInput: 'Аккаунт | Пожалуйста, введите ваш аккаунт',\n    smsCodeInput: 'Код подтверждения | Получить код подтверждения',\n    haveRegistereLoginNow: 'Я уже зарегистрировался, | Войдите сейчас',\n    nextStep: 'Следующий шаг | Отправить',\n    setNewPasswordInput: 'Установить новый пароль | 6-18 цифр, прописные и строчные буквы',\n    passwordResetSucceeded: 'Сброс пароля выполнен успешно!',\n    /** 忘记密码 /forgotPassword end */\n    accountNotRegistered: 'Аккаунт не зарегистрирован',\n    loginAndDownload: 'Войдите и скачайте договор',\n    registerAndDownload: 'Зарегистрируйтесь и скачайте договор',\n    inputPhone: 'Пожалуйста, введите номер телефона',\n    readContract: 'Читать контракт',\n    errorPhone: 'Ошибка формата телефона',\n    companyCert: 'Проведение корпоративной аутентификаций',\n    regAndCompanyCert: 'Регистрация и проведение корпоративной аутентификаций'\n  },\n  ...sign,\n  handwrite: {\n    title: 'Ручная подпись',\n    picSubmitTip: '图片签名提交成功',\n    settingDefault: '设置为默认签名',\n    moreTip: '更多签名管理请到上上签电子签约平台（ent.bestsign.cn）用户中心处管理',\n    uploadPic: '上传照片',\n    use: '使用',\n    clickExtend: '点右箭头延长手写区',\n    rewrite: 'Написать заново',\n    upload: '上传签名图片',\n    cancel: 'Не писать',\n    confirm: 'Использовать',\n    upgradeBrowser: 'Ваш браузер не поддерживает для ручной подписи холста, прошу обновить.',\n    submitTip: 'Ручная подпись завершена успешно',\n    title2: 'Подпишите рукой',\n    QRCode: 'Сканировать подпись',\n    needWrite: 'Прошу ручной росписью правильно написать Ф. И. О.',\n    needRewrite: '笔画无法辨认，请重新书写',\n    ok: 'Подтвердить',\n    clearTips: 'Прошу отчетливо написать подпись',\n    isBlank: 'Холст пуст, прошу ручной росписью подписать, затем передать!',\n    success: 'Ручная подпись завершена успешно',\n    signNotMatch: '请正楷书写签名，须与实名证件信息一致。',\n    signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',\n    msg: {\n      successToUser: 'Новая подпись вступила в силу, прошу перейти в( личный кабинет Web-отдел подписи) и проверить',\n      successToSign: 'Новая подпись вступила в силу, прошу перейти на страницу договора подписи и проверить',\n      cantGet: 'Не получается получить подпись, прошу попробовать другой браузер！'\n    }\n  },\n  common: {\n    aboutBestSign: 'О компании',\n    contact: 'Свяжитесь с нами',\n    recruitment: 'Рекрутинг талант',\n    copyright: 'авторское право',\n    advice: 'совет',\n    notEmpty: 'Не должно быть пустым!',\n    enter6to18n: 'Пожалуйста, введите 6-18 цифр, букв',\n    ssqDes: 'Лидер облачной платформы Электронной Цифровой Подписи',\n    openPlatform: 'Открытая платформа',\n    company: 'HangZhou BestSign Ltd.',\n    help: 'Справочный центр'\n  },\n  entAuth: {\n    // ...entAuth,\n    entCertification: 'Подтверждение личности компании',\n    subBaseInfo: 'Отправить основную информацию',\n    corDocuments: 'Свидетельство о регистраций компании',\n    license: 'Лицензия на ведение коммерческой деятельности',\n    upload: 'Нажмите, чтобы загрузить',\n    uploadLimit: 'Изображения ограничены форматом jpeg, jpg, png,  размер не должен превышать 10M',\n    hi: 'Здравствуйте',\n    exit: 'Выход',\n    help: 'Помощь',\n    hotline: 'Горячая линия',\n    acceptProtectingMethod: 'Я принимаю метод защиты личной информаций, предоставленной мной BestSign',\n    comfirmSubmit: 'Подтвердить отправку',\n    cerficated: 'Аутентификация завершена',\n    entName: 'Название компании',\n    serialNumber: 'Целый серийный номер',\n    validity: 'Срок действия',\n    nationalNo: ' Государственный регистрационный номер',\n    corporationName: 'Ф.И.О. законного представителя',\n    city: 'Все города',\n    entCertificate: 'Сертификат настоящей подлинности организаций ',\n    certificationAuthority: 'Центр по выдаче сертификата ',\n    bestsignPlatform: ' Облачная платформа электронной подписи BestSign',\n    notIssued: 'Не отпускается',\n    date: '{day}число{month}месяц{year}год',\n    congratulations: 'Поздравляем, успешно завершили аутентификацию компании',\n    continue: 'Следующий',\n    rejectMessage: ' По следующим причинам проверка данных не удалась, пожалуйста, проверьте еще раз',\n    recertification: 'Пройти заново аутентификацию',\n    waitMessage: 'Проверка клиента будет завершено в течение одного рабочего дня'\n  },\n  home: {\n    // ...home,\n    home: 'Страница',\n    contractDrafting: 'Составить контракт',\n    contractManagement: 'Управление контрактами',\n    userCenter: 'Личный кабинет',\n    service: 'Сервис',\n    enterpriseConsole: 'Платформа управления компаниями',\n    groupConsole: 'Group console',\n    startSigning: 'Начать подписание',\n    countDes: {\n      1: 'Может быть выпущено: ',\n      2: 'корпоративных контрактов и',\n      3: '',\n      4: 'частных контрактов'\n    },\n    // countDes: 'Может быть выпущено对公合同 | 份 | 对私合同 | 份' ,\n    chargeNow: 'Немедленно пополнить счет',\n    statusTip: {\n      1: 'Нужно мне выполнить',\n      2: 'Подпись с другой стороны',\n      3: 'Подписывание скоро завершится',\n      4: 'Подписание завершено'\n    },\n    useTemplate: '使用模板',\n    useLocalFile: 'Загрузить локальные файлы',\n    enterEnterpriseName: '请输入企业名称'\n  },\n  docDetail: {\n    back: 'Назад',\n    contractInfo: 'Информация о контракте',\n    basicInfo: 'Основную информацию',\n    contractNum: 'Номер контракта ',\n    sender: 'Отправитель',\n    personAccount: 'Личный кабинет ',\n    entAccount: 'Аккаунт компаний',\n    operator: 'Исполнитель',\n    signStartTime: 'Отправить время подписания договора',\n    signDeadline: 'Крайний срок подписания',\n    contractExpireDate: 'Окончание срока действия договора',\n    none: 'Ни один ',\n    edit: 'Редактировать ',\n    settings: 'Установка ',\n    from: 'Ресурс ',\n    folder: 'Папка',\n    contractType: 'Тип контракта',\n    reason: 'Причина ',\n    sign: 'Подписать',\n    approval: 'утверждение',\n    // 1期不做审批\n    viewAttach: 'Просмотреть страницу ',\n    downloadContract: 'Скачать договор',\n    downloadAttach: 'Скачать страницу ',\n    print: 'Распечатать ',\n    certificatedTooltip: 'Контракт и связанные с ним доказательства  были задокументированы в судебной цепочке Интернет-суда Ханчжоу.',\n    needMeSign: 'Нужно мне подписать ',\n    needMeApproval: 'Нужно мне утвердить',\n    // 1期不做审批\n    inApproval: 'В процессе рассмотрения..',\n    // 1期不做审批\n    needOthersSign: 'Подпись с другой стороны',\n    signComplete: 'Подписание завершено',\n    signOverdue: 'Просроченный подпись ',\n    rejected: 'Отказано в подписи',\n    revoked: 'Отозвано',\n    contractCompleteTime: 'Время подписания завершено',\n    contractEndTime: 'Время окончания подписи',\n    reject: 'Отказ в подписи',\n    revoke: 'Отменять',\n    download: 'Скачать',\n    viewSignOrders: 'Посмотреть порядок подписания',\n    viewApprovalProcess: 'Проверить процесс утверждения',\n    // 1期不做审批\n    completed: 'Завершено ',\n    cc: 'Отправить копию',\n    ccer: 'Отправляющий',\n    signer: 'Подписант',\n    signSubject: 'Тема подписи',\n    signSubjectTooltip: 'Отправитель должен заполнить подписывающую сторону',\n    user: 'Пользователь',\n    IDNumber: 'Номер паспорта ',\n    state: 'Статус',\n    time: 'Время',\n    notice: 'Напоминать',\n    detail: 'Детально',\n    RealNameCertificationRequired: 'Требуется аутентификация личности',\n    RealNameCertificationNotRequired: 'Не требуется аутентификаций',\n    MustHandwrittenSignature: 'Обязательно подпишите рукой',\n    handWritingRecognition: '开启手写笔迹识别',\n    privateMessage: 'Личное письмо ',\n    attachment: 'Прикрепленный файл ',\n    rejectReason: 'Причина ',\n    notSigned: 'Не подписано',\n    notViewed: 'Не проверено ',\n    viewed: 'Проверено ',\n    signed: 'Подписано',\n    viewedNotSigned: 'Прочтен но не подписан',\n    // todo\n    notApproval: 'Не утвержден ',\n    remindSucceed: 'Напоминание отправлено ',\n    reviewDetails: 'Детальное утверждение',\n    // 1期不做审批\n    close: 'Закрыть',\n    // 1期不做审批\n    entInnerOperateDetail: 'Детали процесса работы внутреннего отдела предприятии',\n    // 1期不做审批\n    approve: '同意',\n    // 1期不做审批\n    disapprove: 'Отклонить',\n    // 1期不做审批\n    applySeal: 'Заявка на испльзование печати',\n    // 1期不做审批\n    applied: 'Зарегистрировано',\n    // 1期不做审批\n    apply: 'Подавать заявление',\n    // 1期不做审批\n    toOtherSign: 'Переслать другому человеку для подписи',\n    // 1期不做审批\n    handOver: 'Передать',\n    // 1期不做审批\n    approvalOpinions: 'Резолюция',\n    // 1期不做审批\n    useSeal: 'Приложить печать',\n    // 1期不做审批\n    signature: 'Подписать',\n    // 1期不做审批\n    use: 'Использовать',\n    // 1期不做审批\n    date: 'Дата',\n    // 1期不做审批\n    fill: 'Заполнить',\n    // 1期不做审批\n    times: 'Раз',\n    // 1期不做审批\n    place: 'Место-пункт',\n    // 1期不做审批\n    contractDetail: 'Детали контракта',\n    viewMore: 'Посмотреть больше',\n    collapse: 'Сложить',\n    signLink: 'Подписать ссылку',\n    saveQRCode: 'Сохранить штрих-код или скопировать ссылку, поделиться со стороной  подписанта',\n    signQRCode: 'Подписать ссылку штрих-кода',\n    copy: 'Копия',\n    copySucc: 'Копия завершена',\n    copyFail: 'Копия не удалась',\n    certified: 'сертифицировано',\n    unCertified: 'неподтвержденный',\n    claimed: '已认领'\n  },\n  uploadFile: {\n    isUploading: 'Загрузки',\n    move: 'Переместить',\n    delete: 'Удалить',\n    replace: 'Заменить',\n    tip: 'подсказк',\n    totalPages: 'всего {page} страниц',\n    uploadFile: 'Загрузить локальные файлы',\n    matchErr: 'На сервере небольшой пробел, повторите попытку позже.',\n    inUploadingDeleteErr: 'После загрузки удалить',\n    timeOutErr: 'Запрос времени ожидается'\n  },\n  contractInfo: {\n    contractName: 'Название контракта',\n    contractNameTooltip: 'Пожалуйста, не включайте специальные символы в название контракта, также длина не должна превышать 100 слов.',\n    contractType: 'Тип контракта ',\n    toSelect: 'Пожалуйста, выберите',\n    contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',\n    signDeadLine: 'Крайний срок подписания',\n    signDeadLineTooltip: 'Если договор не подписан до этой даты, вы не можете продолжать подписывать',\n    selectDate: 'Выбрать дату и время',\n    contractExpireDate: 'Окончание срока действия договора',\n    expireDateTooltip: 'Срок истечения в содержании контракта удобен для последующего управления контрактом',\n    notNecessary: 'Не может быть заполнен',\n    dateTips: 'Дата окончания срока действия контракта была автоматически определена для вас, пожалуйста, подтвердите ',\n    contractTitleErr: 'В названии договора не должно содержаться специальные символы',\n    contractTitleLengthErr: 'Название договора не должно превышать 100 букв'\n  },\n  // userCentral: userCentral,\n  template: {\n    dynamicTemplateUpdate: {\n      title: '动态模板新功能上线',\n      newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n      updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n      connectUs: '如有任何疑问，烦请联系拨打热线400-993-6665或者联系在线客服。'\n    }\n  },\n  style: {\n    signature: {\n      text: {\n        x: '0',\n        fontSize: '17'\n      }\n    }\n  },\n  workspace: {\n    create: 'Создан.',\n    reviewing: 'Цензура',\n    completed: 'Готово'\n  },\n  keyInfoExtract: {\n    operate: 'Extract Information',\n    contractType: 'Predicted Contract Types',\n    tooltips: 'Select the Key Information',\n    predictText: 'Predicting',\n    extractText: 'Extracting',\n    errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n    result: 'result:'\n  },\n  judgeRisk: {\n    title: 'AI Lawyer',\n    deepInference: 'AI-Юрист',\n    showAll: 'Show More',\n    tips: 'Judging',\n    dialogTitle: '“AI Lawyer” Reviews Contracts',\n    aiInterpret: 'ИИ-интерпретация'\n  },\n  contractCompare: {\n    riskJudgement: 'Рисковое оценивание',\n    judgeTargetContract: 'Контракт, подлежащий оценке',\n    interpretTargetContract: 'Контракты с ИИ-анализом',\n    startJudge: 'Начать рисковое оценивание',\n    startInterpret: 'Начать анализ',\n    uploadText: 'Пожалуйста, загрузите документы, которые требуют рискового оценивания.',\n    interpretText: 'Пожалуйста, загрузите файлы для анализа',\n    startTips: 'Теперь мы можем начать оценивать риски.',\n    interpretTips: 'Теперь мы можем начать интерпретацию',\n    infoExtract: 'Извлечение соглашения'\n  },\n  agent: {\n    extractTitle: '信息提取',\n    riskTitle: 'AI律师',\n    feedback: '问卷反馈',\n    toMini: '去小程序查看',\n    otherContract: '让我看看其他合同的隐藏风险?',\n    others: '其他',\n    submit: '发送',\n    autoExtract: '自动进行下一步提取直到提取结束',\n    autoRisk: '自动进行下一步分析直到分析结束',\n    aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',\n    chooseRisk: '请选择需要进行分析的文件',\n    chooseExtract: '请选择需要进行提取的文件',\n    analyzing: '内容分析中',\n    advice: '修改建议生成中',\n    options: '选项生成中',\n    inputTips: '请输入确切内容',\n    chargeTip: '余额不足，请充值',\n    original: '原文',\n    revision: '修改建议',\n    diff: '对比',\n    locate: '获取原文定位中',\n    custom: '请输入自定义审查规则',\n    content: '原文位置',\n    satisfy: '对分析结果满意，继续下一项分析',\n    dissatisfy: '对分析结果不满意，重新进行分析',\n    selectFunc: '请选择你期望使用的功能。',\n    deepInference: 'AI-Юрист',\n    deepThinking: '深度思考中',\n    deepThoughtCompleted: '已深度思考',\n    reJudge: '重新判断',\n    confirm: 'подтверд',\n    tipsContent: 'Повторная проверка уменьшит количество доступных попыток. Продолжить?',\n    exportPDF: 'Экспорт отчёта PDF',\n    defaultExportName: 'export.pdf',\n    exporting: 'Идёт экспорт отчёта... Подождите'\n  },\n  authorize: {\n    title: 'Условия использования',\n    content: 'Умные контракты + ИИ-анализ = эффективная работа!Согласитесь для бесплатного доступа',\n    cancel: 'Позже',\n    confirm: 'Принять и начать',\n    contract: 'Ознакомьтесь с 《Условиями использования продукта Хаббл》'\n  },\n  ...console,\n  hubbleEntry: {\n    smartAdvisor: 'ИИ-эксперт по контрактам',\n    tooltips: 'Функция недоступна. Обратитесь к консультантам BestSign для подключения.',\n    confirm: 'Понятно'\n  },\n  lang: 'ru'\n};", "map": {"version": 3, "names": ["utils", "mixin", "components", "sign", "login", "pswLogin", "usePswLogin", "verifyLogin", "useVerifyLogin", "scanLogin", "scanFailure", "scanSuccess", "scanLoginTip", "appLoginTip", "downloadApp", "forgetPsw", "noAccount", "registerNow", "accountPlaceholder", "passwordPlaceholder", "pictureVer", "verifyCodePlaceholder", "getVerifyCode", "noRegister", "or", "errAccountOrPwdTip", "errAccountOrPwdTip2", "errEmailOrTel", "errPwd", "verCodeFormatErr", "grapVerCodeErr", "grapVerCodeFormatErr", "lackAccount", "lackGrapCode", "getVerCodeTip", "loginView", "reg<PERSON><PERSON><PERSON>", "takeViewBtn", "resendCode", "regTip", "haveRead", "bestsignAgreement", "and", "digitalCertificateAgreement", "privacyPolicy", "sendSuc", "lackVerCode", "lackPsw", "notMatch", "cookieTip", "wrongLink", "footerTips", "bestSign", "bestSignDescription", "forgetPswStep", "pictureVerCodeInput", "accountInput", "smsCodeInput", "haveRegistereLoginNow", "nextStep", "setNewPasswordInput", "passwordResetSucceeded", "accountNotRegistered", "loginAndDownload", "registerAndDownload", "inputPhone", "readContract", "errorPhone", "companyCert", "regAndCompanyCert", "handwrite", "title", "picSubmitTip", "<PERSON><PERSON><PERSON><PERSON>", "moreTip", "uploadPic", "use", "clickExtend", "rewrite", "upload", "cancel", "confirm", "upgradeBrowser", "submitTip", "title2", "QRCode", "needWrite", "needRewrite", "ok", "clearTips", "isBlank", "success", "signNotMatch", "signNotMatchExact", "msg", "successToUser", "successToSign", "cantGet", "common", "aboutBestSign", "contact", "recruitment", "copyright", "advice", "notEmpty", "enter6to18n", "ssqDes", "openPlatform", "company", "help", "entAuth", "entCertification", "subBaseInfo", "corDocuments", "license", "uploadLimit", "hi", "exit", "hotline", "acceptProtectingMethod", "comfirmSubmit", "cerficated", "entName", "serialNumber", "validity", "nationalNo", "corporationName", "city", "entCertificate", "certificationAuthority", "bestsignPlatform", "notIssued", "date", "congratulations", "continue", "rejectMessage", "recertification", "waitMessage", "home", "contractDrafting", "contractManagement", "userCenter", "service", "enterpriseConsole", "groupConsole", "startSigning", "countDes", "chargeNow", "statusTip", "useTemplate", "useLocalFile", "enterEnterpriseName", "docDetail", "back", "contractInfo", "basicInfo", "contractNum", "sender", "personAccount", "entAccount", "operator", "signStartTime", "signDeadline", "contractExpireDate", "none", "edit", "settings", "from", "folder", "contractType", "reason", "approval", "viewAttach", "downloadContract", "downloadAttach", "print", "certificatedTooltip", "needMeSign", "needMeApproval", "inApproval", "needOthersSign", "signComplete", "signOverdue", "rejected", "revoked", "contractCompleteTime", "contractEndTime", "reject", "revoke", "download", "viewSignOrders", "viewApprovalProcess", "completed", "cc", "ccer", "signer", "signSubject", "signSubjectTooltip", "user", "IDNumber", "state", "time", "notice", "detail", "RealNameCertificationRequired", "RealNameCertificationNotRequired", "MustHandwrittenSignature", "handWritingRecognition", "privateMessage", "attachment", "rejectReason", "notSigned", "notViewed", "viewed", "signed", "viewedNotSigned", "notApproval", "remindSucceed", "reviewDetails", "close", "entInnerOperateDetail", "approve", "disapprove", "applySeal", "applied", "apply", "toOtherSign", "handOver", "approvalOpinions", "useSeal", "signature", "fill", "times", "place", "contractDetail", "viewMore", "collapse", "signLink", "saveQRCode", "signQRCode", "copy", "copySucc", "copyFail", "certified", "unCertified", "claimed", "uploadFile", "isUploading", "move", "delete", "replace", "tip", "totalPages", "matchErr", "inUploadingDeleteErr", "timeOutErr", "contractName", "contractNameTooltip", "toSelect", "contractTypeErr", "signDeadLine", "signDeadLineTooltip", "selectDate", "expireDateTooltip", "notNecessary", "dateTips", "contractTitleErr", "contractTitleLengthErr", "template", "dynamicTemplateUpdate", "newVersionDesc", "updateTip", "connectUs", "style", "text", "x", "fontSize", "workspace", "create", "reviewing", "keyInfoExtract", "operate", "tooltips", "predictText", "extractText", "errorMessage", "result", "judgeRisk", "deepInference", "showAll", "tips", "dialogTitle", "aiInterpret", "contractCompare", "riskJudgement", "judge<PERSON><PERSON><PERSON>Contract", "interpretTargetContract", "startJudge", "startInterpret", "uploadText", "interpretText", "startTips", "interpretTips", "infoExtract", "agent", "extractTitle", "riskTitle", "feedback", "to<PERSON><PERSON>", "otherContract", "others", "submit", "autoExtract", "autoRisk", "aiGenerated", "chooseRisk", "chooseExtract", "analyzing", "options", "inputTips", "chargeTip", "original", "revision", "diff", "locate", "custom", "content", "satisfy", "dissatisfy", "selectFunc", "deepThinking", "deepThoughtCompleted", "reJudge", "tipsContent", "exportPDF", "defaultExportName", "exporting", "authorize", "contract", "console", "hubbleEntry", "smartAdvisor", "lang"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/ru-RU.js"], "sourcesContent": ["// 语言为俄文时的文案\nimport utils from './module/utils/utils-ru.js';\nimport mixin from './module/mixins/mixins-ru.js';\nimport components from './module/components/components-ru.js';\n\n// import docList from './module/docList/docList-ru.js';\n// import console from './module/console/console-ru.js';\n// import userCentral from './module/usercentral/usercentral-ru.js';\n// import home from './module/home/<USER>';\nimport sign from './module/sign/sign-ru.js';\n// import entAuth from './module/entAuth/entAuth-ru.js';\n\nexport default {\n    ...utils,\n    ...mixin,\n    ...components,\n    // ...docList,\n    login: {\n        pswLogin: 'Пароль для входа',\n        usePswLogin: '«Войти с паролем»',\n        verifyLogin: 'Код подтверждения для входа',\n        useVerifyLogin: '«Войти с кодом подтверждения»',\n        scanLogin: 'Сканирование кода входа',\n        scanFailure: 'Срок действия QR-кода истек, пожалуйста, обновите и попробуйте снова',\n        scanSuccess: 'Сканирование кода успешно',\n        scanLoginTip: 'Пожалуйста, используйте приложение BestSign, для входа отсканируйте APP',\n        appLoginTip: 'Пожалуйста, нажмите «Войти в приложение BestSign»',\n        downloadApp: 'Скачать приложение BestSign',\n        forgetPsw: 'Забыли пароль',\n        login: 'Войти',\n        noAccount: 'Нет аккаунта',\n        registerNow: 'Зарегистрируйтесь сейчас',\n        accountPlaceholder: 'Введите пароль',\n        passwordPlaceholder: 'Пожалуйста, введите пароль для входа',\n        pictureVer: 'Пожалуйста, заполните содержание на картинке»',\n        verifyCodePlaceholder: 'Пожалуйста, введите 6-значный код подтверждения',\n        getVerifyCode: 'Получить код подтверждения',\n        noRegister: 'Еще не зарегистрирован',\n        or: 'или',\n        errAccountOrPwdTip: 'Введенный вами пароль не соответствует номеру учетной записи?',\n        errAccountOrPwdTip2: 'Введенный вами пароль не соответствует номеру учетной записи.',\n        errEmailOrTel: 'Пожалуйста, введите правильный E-mail или номер телефона!',\n        errPwd: 'Пожалуйста, введите правильный пароль!',\n        verCodeFormatErr: 'Ошибочный код подтверждения',\n        grapVerCodeErr: 'Ошибка графического кода подтверждения',\n        grapVerCodeFormatErr: 'Ошибка формата графического кода подтверждения',\n        lackAccount: 'Пожалуйста, заполните номер счета и затем получите',\n        lackGrapCode: 'Пожалуйста, сначала заполните графический код подтверждения.',\n        getVerCodeTip: '«Пожалуйста, получите код подтверждения»',\n\n        loginView: 'Войдите и просмотрите договор',\n        regView: 'Зарегистрируйтесь и просмотрите договор',\n        takeViewBtn: '登录并签署',\n        resendCode: 'Получить заново',\n        regTip: 'После ввода правильного кода подтверждения, BestSign создаvn для вас аккаунт.',\n        haveRead: 'Я прочитал и согласился',\n        bestsignAgreement: 'Сервисное соглашение BestSign',\n        and: 'и',\n        digitalCertificateAgreement: 'Соглашение об использовании цифрового сертификата',\n        privacyPolicy: 'Политика конфиденциальности',\n        sendSuc: 'Успешно отправлен',\n        lackVerCode: 'Пожалуйста, сначала введите код подтверждения',\n        lackPsw: 'Пожалуйста, сначала введите ваш пароль',\n        notMatch: 'Введенный вами пароль и аккаунт не соответствует',\n        cookieTip: 'Не удается прочитать и записать файлы cookie, проверьте, нет ли режима трассировки / инкогнито или других запрещенных файлов cookie',\n        wrongLink: 'Нелегальная ссылка',\n        footerTips: 'Услуга электронной подписи <span> BestSign </span> предоставляет',\n        bestSign: 'BestSign',\n        bestSignDescription: 'Лидер индустрии электронных <br/> контрактов',\n        /** 忘记密码 /forgotPassword start */\n        forgetPswStep: 'Подтвердить зарегестрированный аккаунт | Сбросить пароль',\n        pictureVerCodeInput: 'Графический код подтверждения | Пожалуйста, заполните содержание изображения',\n        accountInput: 'Аккаунт | Пожалуйста, введите ваш аккаунт',\n        smsCodeInput: 'Код подтверждения | Получить код подтверждения',\n        haveRegistereLoginNow: 'Я уже зарегистрировался, | Войдите сейчас',\n        nextStep: 'Следующий шаг | Отправить',\n        setNewPasswordInput: 'Установить новый пароль | 6-18 цифр, прописные и строчные буквы',\n        passwordResetSucceeded: 'Сброс пароля выполнен успешно!',\n        /** 忘记密码 /forgotPassword end */\n        accountNotRegistered: 'Аккаунт не зарегистрирован',\n        loginAndDownload: 'Войдите и скачайте договор',\n        registerAndDownload: 'Зарегистрируйтесь и скачайте договор',\n        inputPhone: 'Пожалуйста, введите номер телефона',\n        readContract: 'Читать контракт',\n        errorPhone: 'Ошибка формата телефона',\n        companyCert: 'Проведение корпоративной аутентификаций',\n        regAndCompanyCert: 'Регистрация и проведение корпоративной аутентификаций',\n    },\n    ...sign,\n    handwrite: {\n        title: 'Ручная подпись',\n        picSubmitTip: '图片签名提交成功',\n        settingDefault: '设置为默认签名',\n        moreTip: '更多签名管理请到上上签电子签约平台（ent.bestsign.cn）用户中心处管理',\n        uploadPic: '上传照片',\n        use: '使用',\n        clickExtend: '点右箭头延长手写区',\n        rewrite: 'Написать заново',\n        upload: '上传签名图片',\n        cancel: 'Не писать',\n        confirm: 'Использовать',\n        upgradeBrowser: 'Ваш браузер не поддерживает для ручной подписи холста, прошу обновить.',\n        submitTip: 'Ручная подпись завершена успешно',\n        title2: 'Подпишите рукой',\n        QRCode: 'Сканировать подпись',\n        needWrite: 'Прошу ручной росписью правильно написать Ф. И. О.',\n        needRewrite: '笔画无法辨认，请重新书写',\n        ok: 'Подтвердить',\n        clearTips: 'Прошу отчетливо написать подпись',\n        isBlank: 'Холст пуст, прошу ручной росписью подписать, затем передать!',\n        success: 'Ручная подпись завершена успешно',\n        signNotMatch: '请正楷书写签名，须与实名证件信息一致。',\n        signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',\n        msg: {\n            successToUser: 'Новая подпись вступила в силу, прошу перейти в( личный кабинет Web-отдел подписи) и проверить',\n            successToSign: 'Новая подпись вступила в силу, прошу перейти на страницу договора подписи и проверить',\n            cantGet: 'Не получается получить подпись, прошу попробовать другой браузер！',\n        },\n    },\n    common: {\n        aboutBestSign: 'О компании',\n        contact: 'Свяжитесь с нами',\n        recruitment: 'Рекрутинг талант',\n        copyright: 'авторское право',\n        advice: 'совет',\n        notEmpty: 'Не должно быть пустым!',\n        enter6to18n: 'Пожалуйста, введите 6-18 цифр, букв',\n        ssqDes: 'Лидер облачной платформы Электронной Цифровой Подписи',\n        openPlatform: 'Открытая платформа',\n        company: 'HangZhou BestSign Ltd.',\n        help: 'Справочный центр',\n    },\n    entAuth: {\n        // ...entAuth,\n        entCertification: 'Подтверждение личности компании',\n        subBaseInfo: 'Отправить основную информацию',\n        corDocuments: 'Свидетельство о регистраций компании',\n        license: 'Лицензия на ведение коммерческой деятельности',\n        upload: 'Нажмите, чтобы загрузить',\n        uploadLimit: 'Изображения ограничены форматом jpeg, jpg, png,  размер не должен превышать 10M',\n        hi: 'Здравствуйте',\n        exit: 'Выход',\n        help: 'Помощь',\n        hotline: 'Горячая линия',\n        acceptProtectingMethod: 'Я принимаю метод защиты личной информаций, предоставленной мной BestSign',\n        comfirmSubmit: 'Подтвердить отправку',\n        cerficated: 'Аутентификация завершена',\n        entName: 'Название компании',\n        serialNumber: 'Целый серийный номер',\n        validity: 'Срок действия',\n        nationalNo: ' Государственный регистрационный номер',\n        corporationName: 'Ф.И.О. законного представителя',\n        city: 'Все города',\n        entCertificate: 'Сертификат настоящей подлинности организаций ',\n        certificationAuthority: 'Центр по выдаче сертификата ',\n        bestsignPlatform: ' Облачная платформа электронной подписи BestSign',\n        notIssued: 'Не отпускается',\n        date: '{day}число{month}месяц{year}год',\n        congratulations: 'Поздравляем, успешно завершили аутентификацию компании',\n        continue: 'Следующий',\n        rejectMessage: ' По следующим причинам проверка данных не удалась, пожалуйста, проверьте еще раз',\n        recertification: 'Пройти заново аутентификацию',\n        waitMessage: 'Проверка клиента будет завершено в течение одного рабочего дня',\n    },\n    home: {\n        // ...home,\n        home: 'Страница',\n        contractDrafting: 'Составить контракт',\n        contractManagement: 'Управление контрактами',\n        userCenter: 'Личный кабинет',\n        service: 'Сервис',\n        enterpriseConsole: 'Платформа управления компаниями',\n        groupConsole: 'Group console',\n        startSigning: 'Начать подписание',\n        countDes: {\n            1: 'Может быть выпущено: ',\n            2: 'корпоративных контрактов и',\n            3: '',\n            4: 'частных контрактов',\n        },\n        // countDes: 'Может быть выпущено对公合同 | 份 | 对私合同 | 份' ,\n        chargeNow: 'Немедленно пополнить счет',\n        statusTip: {\n            1: 'Нужно мне выполнить',\n            2: 'Подпись с другой стороны',\n            3: 'Подписывание скоро завершится',\n            4: 'Подписание завершено',\n        },\n        useTemplate: '使用模板',\n        useLocalFile: 'Загрузить локальные файлы',\n        enterEnterpriseName: '请输入企业名称',\n    },\n    docDetail: {\n        back: 'Назад',\n        contractInfo: 'Информация о контракте',\n        basicInfo: 'Основную информацию',\n        contractNum: 'Номер контракта ',\n        sender: 'Отправитель',\n        personAccount: 'Личный кабинет ',\n        entAccount: 'Аккаунт компаний',\n        operator: 'Исполнитель',\n        signStartTime: 'Отправить время подписания договора',\n        signDeadline: 'Крайний срок подписания',\n        contractExpireDate: 'Окончание срока действия договора',\n        none: 'Ни один ',\n        edit: 'Редактировать ',\n        settings: 'Установка ',\n        from: 'Ресурс ',\n        folder: 'Папка',\n        contractType: 'Тип контракта',\n        reason: 'Причина ',\n        sign: 'Подписать',\n        approval: 'утверждение', // 1期不做审批\n        viewAttach: 'Просмотреть страницу ',\n        downloadContract: 'Скачать договор',\n        downloadAttach: 'Скачать страницу ',\n        print: 'Распечатать ',\n        certificatedTooltip: 'Контракт и связанные с ним доказательства  были задокументированы в судебной цепочке Интернет-суда Ханчжоу.',\n        needMeSign: 'Нужно мне подписать ',\n        needMeApproval: 'Нужно мне утвердить', // 1期不做审批\n        inApproval: 'В процессе рассмотрения..', // 1期不做审批\n        needOthersSign: 'Подпись с другой стороны',\n        signComplete: 'Подписание завершено',\n        signOverdue: 'Просроченный подпись ',\n        rejected: 'Отказано в подписи',\n        revoked: 'Отозвано',\n        contractCompleteTime: 'Время подписания завершено',\n        contractEndTime: 'Время окончания подписи',\n        reject: 'Отказ в подписи',\n        revoke: 'Отменять',\n        download: 'Скачать',\n        viewSignOrders: 'Посмотреть порядок подписания',\n        viewApprovalProcess: 'Проверить процесс утверждения', // 1期不做审批\n        completed: 'Завершено ',\n        cc: 'Отправить копию',\n        ccer: 'Отправляющий',\n        signer: 'Подписант',\n        signSubject: 'Тема подписи',\n        signSubjectTooltip: 'Отправитель должен заполнить подписывающую сторону',\n        user: 'Пользователь',\n        IDNumber: 'Номер паспорта ',\n        state: 'Статус',\n        time: 'Время',\n        notice: 'Напоминать',\n        detail: 'Детально',\n        RealNameCertificationRequired: 'Требуется аутентификация личности',\n        RealNameCertificationNotRequired: 'Не требуется аутентификаций',\n        MustHandwrittenSignature: 'Обязательно подпишите рукой',\n        handWritingRecognition: '开启手写笔迹识别',\n        privateMessage: 'Личное письмо ',\n        attachment: 'Прикрепленный файл ',\n        rejectReason: 'Причина ',\n        notSigned: 'Не подписано',\n        notViewed: 'Не проверено ',\n        viewed: 'Проверено ',\n        signed: 'Подписано',\n        viewedNotSigned: 'Прочтен но не подписан', // todo\n        notApproval: 'Не утвержден ',\n        remindSucceed: 'Напоминание отправлено ',\n        reviewDetails: 'Детальное утверждение', // 1期不做审批\n        close: 'Закрыть', // 1期不做审批\n        entInnerOperateDetail: 'Детали процесса работы внутреннего отдела предприятии', // 1期不做审批\n        approve: '同意', // 1期不做审批\n        disapprove: 'Отклонить', // 1期不做审批\n        applySeal: 'Заявка на испльзование печати', // 1期不做审批\n        applied: 'Зарегистрировано', // 1期不做审批\n        apply: 'Подавать заявление', // 1期不做审批\n        toOtherSign: 'Переслать другому человеку для подписи', // 1期不做审批\n        handOver: 'Передать', // 1期不做审批\n        approvalOpinions: 'Резолюция', // 1期不做审批\n        useSeal: 'Приложить печать', // 1期不做审批\n        signature: 'Подписать', // 1期不做审批\n        use: 'Использовать', // 1期不做审批\n        date: 'Дата', // 1期不做审批\n        fill: 'Заполнить', // 1期不做审批\n        times: 'Раз', // 1期不做审批\n        place: 'Место-пункт', // 1期不做审批\n        contractDetail: 'Детали контракта',\n        viewMore: 'Посмотреть больше',\n        collapse: 'Сложить',\n        signLink: 'Подписать ссылку',\n        saveQRCode: 'Сохранить штрих-код или скопировать ссылку, поделиться со стороной  подписанта',\n        signQRCode: 'Подписать ссылку штрих-кода',\n        copy: 'Копия',\n        copySucc: 'Копия завершена',\n        copyFail: 'Копия не удалась',\n        certified: 'сертифицировано',\n        unCertified: 'неподтвержденный',\n        claimed: '已认领',\n    },\n    uploadFile: {\n        isUploading: 'Загрузки',\n        move: 'Переместить',\n        delete: 'Удалить',\n        replace: 'Заменить',\n        tip: 'подсказк',\n        totalPages: 'всего {page} страниц',\n        uploadFile: 'Загрузить локальные файлы',\n        matchErr: 'На сервере небольшой пробел, повторите попытку позже.',\n        inUploadingDeleteErr: 'После загрузки удалить',\n        timeOutErr: 'Запрос времени ожидается',\n    },\n    contractInfo: {\n        contractName: 'Название контракта',\n        contractNameTooltip: 'Пожалуйста, не включайте специальные символы в название контракта, также длина не должна превышать 100 слов.',\n        contractType: 'Тип контракта ',\n        toSelect: 'Пожалуйста, выберите',\n        contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',\n        signDeadLine: 'Крайний срок подписания',\n        signDeadLineTooltip: 'Если договор не подписан до этой даты, вы не можете продолжать подписывать',\n        selectDate: 'Выбрать дату и время',\n        contractExpireDate: 'Окончание срока действия договора',\n        expireDateTooltip: 'Срок истечения в содержании контракта удобен для последующего управления контрактом',\n        notNecessary: 'Не может быть заполнен',\n        dateTips: 'Дата окончания срока действия контракта была автоматически определена для вас, пожалуйста, подтвердите ',\n        contractTitleErr: 'В названии договора не должно содержаться специальные символы',\n        contractTitleLengthErr: 'Название договора не должно превышать 100 букв',\n    },\n    // userCentral: userCentral,\n    template: {\n        dynamicTemplateUpdate: {\n            title: '动态模板新功能上线',\n            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n            connectUs: '如有任何疑问，烦请联系拨打热线400-993-6665或者联系在线客服。',\n        },\n    },\n    style: {\n        signature: {\n            text: {\n                x: '0',\n                fontSize: '17',\n            },\n        },\n    },\n    workspace: {\n        create: 'Создан.',\n        reviewing: 'Цензура',\n        completed: 'Готово',\n    },\n    keyInfoExtract: {\n        operate: 'Extract Information',\n        contractType: 'Predicted Contract Types',\n        tooltips: 'Select the Key Information',\n        predictText: 'Predicting',\n        extractText: 'Extracting',\n        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n        result: 'result:',\n    },\n    judgeRisk: {\n        title: 'AI Lawyer',\n        deepInference: 'AI-Юрист',\n        showAll: 'Show More',\n        tips: 'Judging',\n        dialogTitle: '“AI Lawyer” Reviews Contracts',\n        aiInterpret: 'ИИ-интерпретация',\n    },\n    contractCompare: {\n        riskJudgement: 'Рисковое оценивание',\n        judgeTargetContract: 'Контракт, подлежащий оценке',\n        interpretTargetContract: 'Контракты с ИИ-анализом',\n        startJudge: 'Начать рисковое оценивание',\n        startInterpret: 'Начать анализ',\n        uploadText: 'Пожалуйста, загрузите документы, которые требуют рискового оценивания.',\n        interpretText: 'Пожалуйста, загрузите файлы для анализа',\n        startTips: 'Теперь мы можем начать оценивать риски.',\n        interpretTips: 'Теперь мы можем начать интерпретацию',\n        infoExtract: 'Извлечение соглашения',\n    },\n    agent: {\n        extractTitle: '信息提取',\n        riskTitle: 'AI律师',\n        feedback: '问卷反馈',\n        toMini: '去小程序查看',\n        otherContract: '让我看看其他合同的隐藏风险?',\n        others: '其他',\n        submit: '发送',\n        autoExtract: '自动进行下一步提取直到提取结束',\n        autoRisk: '自动进行下一步分析直到分析结束',\n        aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',\n        chooseRisk: '请选择需要进行分析的文件',\n        chooseExtract: '请选择需要进行提取的文件',\n        analyzing: '内容分析中',\n        advice: '修改建议生成中',\n        options: '选项生成中',\n        inputTips: '请输入确切内容',\n        chargeTip: '余额不足，请充值',\n        original: '原文',\n        revision: '修改建议',\n        diff: '对比',\n        locate: '获取原文定位中',\n        custom: '请输入自定义审查规则',\n        content: '原文位置',\n        satisfy: '对分析结果满意，继续下一项分析',\n        dissatisfy: '对分析结果不满意，重新进行分析',\n        selectFunc: '请选择你期望使用的功能。',\n        deepInference: 'AI-Юрист',\n        deepThinking: '深度思考中',\n        deepThoughtCompleted: '已深度思考',\n        reJudge: '重新判断',\n        confirm: 'подтверд',\n        tipsContent: 'Повторная проверка уменьшит количество доступных попыток. Продолжить?',\n        exportPDF: 'Экспорт отчёта PDF',\n        defaultExportName: 'export.pdf',\n        exporting: 'Идёт экспорт отчёта... Подождите',\n    },\n    authorize: {\n        title: 'Условия использования',\n        content: 'Умные контракты + ИИ-анализ = эффективная работа!Согласитесь для бесплатного доступа',\n        cancel: 'Позже',\n        confirm: 'Принять и начать',\n        contract: 'Ознакомьтесь с 《Условиями использования продукта Хаббл》',\n    },\n    ...console,\n    hubbleEntry: {\n        smartAdvisor: 'ИИ-эксперт по контрактам',\n        tooltips: 'Функция недоступна. Обратитесь к консультантам BestSign для подключения.',\n        confirm: 'Понятно',\n    },\n    lang: 'ru',\n};\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,sCAAsC;;AAE7D;AACA;AACA;AACA;AACA,OAAOC,IAAI,MAAM,0BAA0B;AAC3C;;AAEA,eAAe;EACX,GAAGH,KAAK;EACR,GAAGC,KAAK;EACR,GAAGC,UAAU;EACb;EACAE,KAAK,EAAE;IACHC,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE,mBAAmB;IAChCC,WAAW,EAAE,6BAA6B;IAC1CC,cAAc,EAAE,+BAA+B;IAC/CC,SAAS,EAAE,yBAAyB;IACpCC,WAAW,EAAE,sEAAsE;IACnFC,WAAW,EAAE,2BAA2B;IACxCC,YAAY,EAAE,yEAAyE;IACvFC,WAAW,EAAE,mDAAmD;IAChEC,WAAW,EAAE,6BAA6B;IAC1CC,SAAS,EAAE,eAAe;IAC1BX,KAAK,EAAE,OAAO;IACdY,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,0BAA0B;IACvCC,kBAAkB,EAAE,gBAAgB;IACpCC,mBAAmB,EAAE,sCAAsC;IAC3DC,UAAU,EAAE,+CAA+C;IAC3DC,qBAAqB,EAAE,iDAAiD;IACxEC,aAAa,EAAE,4BAA4B;IAC3CC,UAAU,EAAE,wBAAwB;IACpCC,EAAE,EAAE,KAAK;IACTC,kBAAkB,EAAE,+DAA+D;IACnFC,mBAAmB,EAAE,+DAA+D;IACpFC,aAAa,EAAE,2DAA2D;IAC1EC,MAAM,EAAE,wCAAwC;IAChDC,gBAAgB,EAAE,6BAA6B;IAC/CC,cAAc,EAAE,wCAAwC;IACxDC,oBAAoB,EAAE,gDAAgD;IACtEC,WAAW,EAAE,oDAAoD;IACjEC,YAAY,EAAE,8DAA8D;IAC5EC,aAAa,EAAE,0CAA0C;IAEzDC,SAAS,EAAE,+BAA+B;IAC1CC,OAAO,EAAE,yCAAyC;IAClDC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,iBAAiB;IAC7BC,MAAM,EAAE,+EAA+E;IACvFC,QAAQ,EAAE,yBAAyB;IACnCC,iBAAiB,EAAE,+BAA+B;IAClDC,GAAG,EAAE,GAAG;IACRC,2BAA2B,EAAE,mDAAmD;IAChFC,aAAa,EAAE,6BAA6B;IAC5CC,OAAO,EAAE,mBAAmB;IAC5BC,WAAW,EAAE,+CAA+C;IAC5DC,OAAO,EAAE,wCAAwC;IACjDC,QAAQ,EAAE,kDAAkD;IAC5DC,SAAS,EAAE,qIAAqI;IAChJC,SAAS,EAAE,oBAAoB;IAC/BC,UAAU,EAAE,kEAAkE;IAC9EC,QAAQ,EAAE,UAAU;IACpBC,mBAAmB,EAAE,8CAA8C;IACnE;IACAC,aAAa,EAAE,0DAA0D;IACzEC,mBAAmB,EAAE,8EAA8E;IACnGC,YAAY,EAAE,2CAA2C;IACzDC,YAAY,EAAE,gDAAgD;IAC9DC,qBAAqB,EAAE,2CAA2C;IAClEC,QAAQ,EAAE,2BAA2B;IACrCC,mBAAmB,EAAE,iEAAiE;IACtFC,sBAAsB,EAAE,gCAAgC;IACxD;IACAC,oBAAoB,EAAE,4BAA4B;IAClDC,gBAAgB,EAAE,4BAA4B;IAC9CC,mBAAmB,EAAE,sCAAsC;IAC3DC,UAAU,EAAE,oCAAoC;IAChDC,YAAY,EAAE,iBAAiB;IAC/BC,UAAU,EAAE,yBAAyB;IACrCC,WAAW,EAAE,yCAAyC;IACtDC,iBAAiB,EAAE;EACvB,CAAC;EACD,GAAGlE,IAAI;EACPmE,SAAS,EAAE;IACPC,KAAK,EAAE,gBAAgB;IACvBC,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,SAAS;IACzBC,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE,WAAW;IACxBC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,cAAc;IACvBC,cAAc,EAAE,wEAAwE;IACxFC,SAAS,EAAE,kCAAkC;IAC7CC,MAAM,EAAE,iBAAiB;IACzBC,MAAM,EAAE,qBAAqB;IAC7BC,SAAS,EAAE,mDAAmD;IAC9DC,WAAW,EAAE,cAAc;IAC3BC,EAAE,EAAE,aAAa;IACjBC,SAAS,EAAE,kCAAkC;IAC7CC,OAAO,EAAE,8DAA8D;IACvEC,OAAO,EAAE,kCAAkC;IAC3CC,YAAY,EAAE,qBAAqB;IACnCC,iBAAiB,EAAE,sCAAsC;IACzDC,GAAG,EAAE;MACDC,aAAa,EAAE,+FAA+F;MAC9GC,aAAa,EAAE,uFAAuF;MACtGC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,MAAM,EAAE;IACJC,aAAa,EAAE,YAAY;IAC3BC,OAAO,EAAE,kBAAkB;IAC3BC,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE,iBAAiB;IAC5BC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,qCAAqC;IAClDC,MAAM,EAAE,uDAAuD;IAC/DC,YAAY,EAAE,oBAAoB;IAClCC,OAAO,EAAE,wBAAwB;IACjCC,IAAI,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;IACL;IACAC,gBAAgB,EAAE,iCAAiC;IACnDC,WAAW,EAAE,+BAA+B;IAC5CC,YAAY,EAAE,sCAAsC;IACpDC,OAAO,EAAE,+CAA+C;IACxDnC,MAAM,EAAE,0BAA0B;IAClCoC,WAAW,EAAE,iFAAiF;IAC9FC,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,OAAO;IACbR,IAAI,EAAE,QAAQ;IACdS,OAAO,EAAE,eAAe;IACxBC,sBAAsB,EAAE,0EAA0E;IAClGC,aAAa,EAAE,sBAAsB;IACrCC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAE,mBAAmB;IAC5BC,YAAY,EAAE,sBAAsB;IACpCC,QAAQ,EAAE,eAAe;IACzBC,UAAU,EAAE,wCAAwC;IACpDC,eAAe,EAAE,gCAAgC;IACjDC,IAAI,EAAE,YAAY;IAClBC,cAAc,EAAE,+CAA+C;IAC/DC,sBAAsB,EAAE,8BAA8B;IACtDC,gBAAgB,EAAE,kDAAkD;IACpEC,SAAS,EAAE,gBAAgB;IAC3BC,IAAI,EAAE,iCAAiC;IACvCC,eAAe,EAAE,wDAAwD;IACzEC,QAAQ,EAAE,WAAW;IACrBC,aAAa,EAAE,kFAAkF;IACjGC,eAAe,EAAE,8BAA8B;IAC/CC,WAAW,EAAE;EACjB,CAAC;EACDC,IAAI,EAAE;IACF;IACAA,IAAI,EAAE,UAAU;IAChBC,gBAAgB,EAAE,oBAAoB;IACtCC,kBAAkB,EAAE,wBAAwB;IAC5CC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE,QAAQ;IACjBC,iBAAiB,EAAE,iCAAiC;IACpDC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,mBAAmB;IACjCC,QAAQ,EAAE;MACN,CAAC,EAAE,uBAAuB;MAC1B,CAAC,EAAE,4BAA4B;MAC/B,CAAC,EAAE,EAAE;MACL,CAAC,EAAE;IACP,CAAC;IACD;IACAC,SAAS,EAAE,2BAA2B;IACtCC,SAAS,EAAE;MACP,CAAC,EAAE,qBAAqB;MACxB,CAAC,EAAE,0BAA0B;MAC7B,CAAC,EAAE,+BAA+B;MAClC,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,2BAA2B;IACzCC,mBAAmB,EAAE;EACzB,CAAC;EACDC,SAAS,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,YAAY,EAAE,wBAAwB;IACtCC,SAAS,EAAE,qBAAqB;IAChCC,WAAW,EAAE,kBAAkB;IAC/BC,MAAM,EAAE,aAAa;IACrBC,aAAa,EAAE,iBAAiB;IAChCC,UAAU,EAAE,kBAAkB;IAC9BC,QAAQ,EAAE,aAAa;IACvBC,aAAa,EAAE,qCAAqC;IACpDC,YAAY,EAAE,yBAAyB;IACvCC,kBAAkB,EAAE,mCAAmC;IACvDC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,eAAe;IAC7BC,MAAM,EAAE,UAAU;IAClBvK,IAAI,EAAE,WAAW;IACjBwK,QAAQ,EAAE,aAAa;IAAE;IACzBC,UAAU,EAAE,uBAAuB;IACnCC,gBAAgB,EAAE,iBAAiB;IACnCC,cAAc,EAAE,mBAAmB;IACnCC,KAAK,EAAE,cAAc;IACrBC,mBAAmB,EAAE,6GAA6G;IAClIC,UAAU,EAAE,sBAAsB;IAClCC,cAAc,EAAE,qBAAqB;IAAE;IACvCC,UAAU,EAAE,2BAA2B;IAAE;IACzCC,cAAc,EAAE,0BAA0B;IAC1CC,YAAY,EAAE,sBAAsB;IACpCC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,oBAAoB;IAC9BC,OAAO,EAAE,UAAU;IACnBC,oBAAoB,EAAE,4BAA4B;IAClDC,eAAe,EAAE,yBAAyB;IAC1CC,MAAM,EAAE,iBAAiB;IACzBC,MAAM,EAAE,UAAU;IAClBC,QAAQ,EAAE,SAAS;IACnBC,cAAc,EAAE,+BAA+B;IAC/CC,mBAAmB,EAAE,+BAA+B;IAAE;IACtDC,SAAS,EAAE,YAAY;IACvBC,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,cAAc;IAC3BC,kBAAkB,EAAE,oDAAoD;IACxEC,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,iBAAiB;IAC3BC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,UAAU;IAClBC,6BAA6B,EAAE,mCAAmC;IAClEC,gCAAgC,EAAE,6BAA6B;IAC/DC,wBAAwB,EAAE,6BAA6B;IACvDC,sBAAsB,EAAE,UAAU;IAClCC,cAAc,EAAE,gBAAgB;IAChCC,UAAU,EAAE,qBAAqB;IACjCC,YAAY,EAAE,UAAU;IACxBC,SAAS,EAAE,cAAc;IACzBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,WAAW;IACnBC,eAAe,EAAE,wBAAwB;IAAE;IAC3CC,WAAW,EAAE,eAAe;IAC5BC,aAAa,EAAE,yBAAyB;IACxCC,aAAa,EAAE,uBAAuB;IAAE;IACxCC,KAAK,EAAE,SAAS;IAAE;IAClBC,qBAAqB,EAAE,uDAAuD;IAAE;IAChFC,OAAO,EAAE,IAAI;IAAE;IACfC,UAAU,EAAE,WAAW;IAAE;IACzBC,SAAS,EAAE,+BAA+B;IAAE;IAC5CC,OAAO,EAAE,kBAAkB;IAAE;IAC7BC,KAAK,EAAE,oBAAoB;IAAE;IAC7BC,WAAW,EAAE,wCAAwC;IAAE;IACvDC,QAAQ,EAAE,UAAU;IAAE;IACtBC,gBAAgB,EAAE,WAAW;IAAE;IAC/BC,OAAO,EAAE,kBAAkB;IAAE;IAC7BC,SAAS,EAAE,WAAW;IAAE;IACxB1J,GAAG,EAAE,cAAc;IAAE;IACrBwD,IAAI,EAAE,MAAM;IAAE;IACdmG,IAAI,EAAE,WAAW;IAAE;IACnBC,KAAK,EAAE,KAAK;IAAE;IACdC,KAAK,EAAE,aAAa;IAAE;IACtBC,cAAc,EAAE,kBAAkB;IAClCC,QAAQ,EAAE,mBAAmB;IAC7BC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,kBAAkB;IAC5BC,UAAU,EAAE,gFAAgF;IAC5FC,UAAU,EAAE,6BAA6B;IACzCC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,iBAAiB;IAC5BC,WAAW,EAAE,kBAAkB;IAC/BC,OAAO,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACRC,WAAW,EAAE,UAAU;IACvBC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,UAAU;IACnBC,GAAG,EAAE,UAAU;IACfC,UAAU,EAAE,sBAAsB;IAClCN,UAAU,EAAE,2BAA2B;IACvCO,QAAQ,EAAE,uDAAuD;IACjEC,oBAAoB,EAAE,wBAAwB;IAC9CC,UAAU,EAAE;EAChB,CAAC;EACDrG,YAAY,EAAE;IACVsG,YAAY,EAAE,oBAAoB;IAClCC,mBAAmB,EAAE,8GAA8G;IACnIxF,YAAY,EAAE,gBAAgB;IAC9ByF,QAAQ,EAAE,sBAAsB;IAChCC,eAAe,EAAE,kEAAkE;IACnFC,YAAY,EAAE,yBAAyB;IACvCC,mBAAmB,EAAE,4EAA4E;IACjGC,UAAU,EAAE,sBAAsB;IAClCnG,kBAAkB,EAAE,mCAAmC;IACvDoG,iBAAiB,EAAE,qFAAqF;IACxGC,YAAY,EAAE,wBAAwB;IACtCC,QAAQ,EAAE,yGAAyG;IACnHC,gBAAgB,EAAE,+DAA+D;IACjFC,sBAAsB,EAAE;EAC5B,CAAC;EACD;EACAC,QAAQ,EAAE;IACNC,qBAAqB,EAAE;MACnBtM,KAAK,EAAE,WAAW;MAClBuM,cAAc,EAAE,2BAA2B;MAC3CC,SAAS,EAAE,gGAAgG;MAC3GC,SAAS,EAAE;IACf;EACJ,CAAC;EACDC,KAAK,EAAE;IACH3C,SAAS,EAAE;MACP4C,IAAI,EAAE;QACFC,CAAC,EAAE,GAAG;QACNC,QAAQ,EAAE;MACd;IACJ;EACJ,CAAC;EACDC,SAAS,EAAE;IACPC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,SAAS;IACpBvF,SAAS,EAAE;EACf,CAAC;EACDwF,cAAc,EAAE;IACZC,OAAO,EAAE,qBAAqB;IAC9BhH,YAAY,EAAE,0BAA0B;IACxCiH,QAAQ,EAAE,4BAA4B;IACtCC,WAAW,EAAE,YAAY;IACzBC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,yJAAyJ;IACvKC,MAAM,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACPxN,KAAK,EAAE,WAAW;IAClByN,aAAa,EAAE,UAAU;IACzBC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;EACjB,CAAC;EACDC,eAAe,EAAE;IACbC,aAAa,EAAE,qBAAqB;IACpCC,mBAAmB,EAAE,6BAA6B;IAClDC,uBAAuB,EAAE,yBAAyB;IAClDC,UAAU,EAAE,4BAA4B;IACxCC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,wEAAwE;IACpFC,aAAa,EAAE,yCAAyC;IACxDC,SAAS,EAAE,yCAAyC;IACpDC,aAAa,EAAE,sCAAsC;IACrDC,WAAW,EAAE;EACjB,CAAC;EACDC,KAAK,EAAE;IACHC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,gBAAgB;IAC/BC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,iBAAiB;IAC3BC,WAAW,EAAE,gCAAgC;IAC7CC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,cAAc;IAC7BC,SAAS,EAAE,OAAO;IAClBtN,MAAM,EAAE,SAAS;IACjBuN,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,YAAY;IACpBC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,cAAc;IAC1BzC,aAAa,EAAE,UAAU;IACzB0C,YAAY,EAAE,OAAO;IACrBC,oBAAoB,EAAE,OAAO;IAC7BC,OAAO,EAAE,MAAM;IACf3P,OAAO,EAAE,UAAU;IACnB4P,WAAW,EAAE,uEAAuE;IACpFC,SAAS,EAAE,oBAAoB;IAC/BC,iBAAiB,EAAE,YAAY;IAC/BC,SAAS,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACP1Q,KAAK,EAAE,uBAAuB;IAC9B+P,OAAO,EAAE,sFAAsF;IAC/FtP,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,kBAAkB;IAC3BiQ,QAAQ,EAAE;EACd,CAAC;EACD,GAAGC,OAAO;EACVC,WAAW,EAAE;IACTC,YAAY,EAAE,0BAA0B;IACxC3D,QAAQ,EAAE,0EAA0E;IACpFzM,OAAO,EAAE;EACb,CAAC;EACDqQ,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}