{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hubble-page__content-guide\"\n  }, [_c(\"div\", {\n    staticClass: \"hubble-page__content-guide__entry\",\n    on: {\n      click: _vm.handleValue\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-ssq-wenhaoxiao\"\n  }), _vm._v(\" 功能指引 \")]), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.value,\n      expression: \"value\"\n    }],\n    staticClass: \"hubble-page__content-guide__modal\"\n  }), _c(\"div\", {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.value,\n      expression: \"value\"\n    }],\n    staticClass: \"hubble-page__content-guide__dialog\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-ssq-delete close\",\n    on: {\n      click: _vm.handleValue\n    }\n  }), _c(\"b\", [_vm._v(\"Hubble，一种全新的工作体验。\")]), _vm._m(0), _c(\"ul\", {\n    staticClass: \"hubble-page__content-guide__btn-list\"\n  }, _vm._l(_vm.btnList, function (btn) {\n    return _c(\"li\", {\n      key: btn.type,\n      class: `hubble-page__content-guide__btn-item ${_vm.currentBtn === btn.type ? \"active\" : \"\"} guide-start-${btn.type}`,\n      on: {\n        click: function ($event) {\n          return _vm.handleGuide(btn.type);\n        }\n      }\n    }, [_c(\"i\", {\n      class: `el-icon-ssq-${btn.icon}`\n    }), _c(\"br\"), _c(\"span\", [_vm._v(_vm._s(btn.text))])]);\n  }), 0), _c(\"span\", {\n    staticClass: \"confirm-btn\",\n    on: {\n      click: _vm.handleValue\n    }\n  }, [_vm._v(\"即刻开启 Hubble 之旅\")])]), _vm.svgPath ? _c(\"svg\", {\n    staticClass: \"hubble-page__content-guide__svg\"\n  }, [_c(\"path\", {\n    attrs: {\n      id: \"myPath\",\n      d: _vm.svgPath,\n      fill: \"none\",\n      stroke: \"#127fd2\",\n      \"stroke-width\": \"2\"\n    }\n  })]) : _vm._e(), _vm.svgPath ? _c(\"div\", {\n    staticClass: \"hubble-page__content-guide__end-point\",\n    style: _vm.endPointStyle\n  }) : _vm._e()]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"p\", [_vm._v(\"你好，我是哈勃，你的签约智能助手\"), _c(\"br\"), _vm._v(\"一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。\"), _c(\"br\"), _c(\"br\"), _vm._v(\"我们准备了必要的使用介绍来帮助快速上手，\"), _c(\"br\"), _vm._v(\"可以通过下方按钮轻松探索特色功能。\"), _c(\"br\")]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "click", "handleValue", "_v", "directives", "name", "rawName", "value", "expression", "_m", "_l", "btnList", "btn", "key", "type", "class", "currentBtn", "$event", "handleGuide", "icon", "_s", "text", "svgPath", "attrs", "id", "d", "fill", "stroke", "_e", "style", "endPointStyle", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/views/agent/components/guide/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"hubble-page__content-guide\" }, [\n    _c(\n      \"div\",\n      {\n        staticClass: \"hubble-page__content-guide__entry\",\n        on: { click: _vm.handleValue },\n      },\n      [_c(\"i\", { staticClass: \"el-icon-ssq-wenhaoxiao\" }), _vm._v(\" 功能指引 \")]\n    ),\n    _c(\"div\", {\n      directives: [\n        {\n          name: \"show\",\n          rawName: \"v-show\",\n          value: _vm.value,\n          expression: \"value\",\n        },\n      ],\n      staticClass: \"hubble-page__content-guide__modal\",\n    }),\n    _c(\n      \"div\",\n      {\n        directives: [\n          {\n            name: \"show\",\n            rawName: \"v-show\",\n            value: _vm.value,\n            expression: \"value\",\n          },\n        ],\n        staticClass: \"hubble-page__content-guide__dialog\",\n      },\n      [\n        _c(\"i\", {\n          staticClass: \"el-icon-ssq-delete close\",\n          on: { click: _vm.handleValue },\n        }),\n        _c(\"b\", [_vm._v(\"Hubble，一种全新的工作体验。\")]),\n        _vm._m(0),\n        _c(\n          \"ul\",\n          { staticClass: \"hubble-page__content-guide__btn-list\" },\n          _vm._l(_vm.btnList, function (btn) {\n            return _c(\n              \"li\",\n              {\n                key: btn.type,\n                class: `hubble-page__content-guide__btn-item ${\n                  _vm.currentBtn === btn.type ? \"active\" : \"\"\n                } guide-start-${btn.type}`,\n                on: {\n                  click: function ($event) {\n                    return _vm.handleGuide(btn.type)\n                  },\n                },\n              },\n              [\n                _c(\"i\", { class: `el-icon-ssq-${btn.icon}` }),\n                _c(\"br\"),\n                _c(\"span\", [_vm._v(_vm._s(btn.text))]),\n              ]\n            )\n          }),\n          0\n        ),\n        _c(\n          \"span\",\n          { staticClass: \"confirm-btn\", on: { click: _vm.handleValue } },\n          [_vm._v(\"即刻开启 Hubble 之旅\")]\n        ),\n      ]\n    ),\n    _vm.svgPath\n      ? _c(\"svg\", { staticClass: \"hubble-page__content-guide__svg\" }, [\n          _c(\"path\", {\n            attrs: {\n              id: \"myPath\",\n              d: _vm.svgPath,\n              fill: \"none\",\n              stroke: \"#127fd2\",\n              \"stroke-width\": \"2\",\n            },\n          }),\n        ])\n      : _vm._e(),\n    _vm.svgPath\n      ? _c(\"div\", {\n          staticClass: \"hubble-page__content-guide__end-point\",\n          style: _vm.endPointStyle,\n        })\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"p\", [\n      _vm._v(\"你好，我是哈勃，你的签约智能助手\"),\n      _c(\"br\"),\n      _vm._v(\n        \"一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。\"\n      ),\n      _c(\"br\"),\n      _c(\"br\"),\n      _vm._v(\"我们准备了必要的使用介绍来帮助快速上手，\"),\n      _c(\"br\"),\n      _vm._v(\"可以通过下方按钮轻松探索特色功能。\"),\n      _c(\"br\"),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAAE,CAC9DF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,mCAAmC;IAChDC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAY;EAC/B,CAAC,EACD,CAACL,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,CAAC,EAAEH,GAAG,CAACO,EAAE,CAAC,QAAQ,CAAC,CACvE,CAAC,EACDN,EAAE,CAAC,KAAK,EAAE;IACRO,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEX,GAAG,CAACW,KAAK;MAChBC,UAAU,EAAE;IACd,CAAC,CACF;IACDT,WAAW,EAAE;EACf,CAAC,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IACEO,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEX,GAAG,CAACW,KAAK;MAChBC,UAAU,EAAE;IACd,CAAC,CACF;IACDT,WAAW,EAAE;EACf,CAAC,EACD,CACEF,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,0BAA0B;IACvCC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAY;EAC/B,CAAC,CAAC,EACFL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,EACtCP,GAAG,CAACa,EAAE,CAAC,CAAC,CAAC,EACTZ,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAuC,CAAC,EACvDH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,OAAO,EAAE,UAAUC,GAAG,EAAE;IACjC,OAAOf,EAAE,CACP,IAAI,EACJ;MACEgB,GAAG,EAAED,GAAG,CAACE,IAAI;MACbC,KAAK,EAAE,wCACLnB,GAAG,CAACoB,UAAU,KAAKJ,GAAG,CAACE,IAAI,GAAG,QAAQ,GAAG,EAAE,gBAC7BF,GAAG,CAACE,IAAI,EAAE;MAC1Bd,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUgB,MAAM,EAAE;UACvB,OAAOrB,GAAG,CAACsB,WAAW,CAACN,GAAG,CAACE,IAAI,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACEjB,EAAE,CAAC,GAAG,EAAE;MAAEkB,KAAK,EAAE,eAAeH,GAAG,CAACO,IAAI;IAAG,CAAC,CAAC,EAC7CtB,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACO,EAAE,CAACP,GAAG,CAACwB,EAAE,CAACR,GAAG,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,CAE1C,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDxB,EAAE,CACA,MAAM,EACN;IAAEE,WAAW,EAAE,aAAa;IAAEC,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM;IAAY;EAAE,CAAC,EAC9D,CAACN,GAAG,CAACO,EAAE,CAAC,gBAAgB,CAAC,CAC3B,CAAC,CAEL,CAAC,EACDP,GAAG,CAAC0B,OAAO,GACPzB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkC,CAAC,EAAE,CAC5DF,EAAE,CAAC,MAAM,EAAE;IACT0B,KAAK,EAAE;MACLC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE7B,GAAG,CAAC0B,OAAO;MACdI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,SAAS;MACjB,cAAc,EAAE;IAClB;EACF,CAAC,CAAC,CACH,CAAC,GACF/B,GAAG,CAACgC,EAAE,CAAC,CAAC,EACZhC,GAAG,CAAC0B,OAAO,GACPzB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,uCAAuC;IACpD8B,KAAK,EAAEjC,GAAG,CAACkC;EACb,CAAC,CAAC,GACFlC,GAAG,CAACgC,EAAE,CAAC,CAAC,CACb,CAAC;AACJ,CAAC;AACD,IAAIG,eAAe,GAAG,CACpB,YAAY;EACV,IAAInC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,GAAG,EAAE,CACbD,GAAG,CAACO,EAAE,CAAC,kBAAkB,CAAC,EAC1BN,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACO,EAAE,CACJ,0CACF,CAAC,EACDN,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACO,EAAE,CAAC,sBAAsB,CAAC,EAC9BN,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACO,EAAE,CAAC,mBAAmB,CAAC,EAC3BN,EAAE,CAAC,IAAI,CAAC,CACT,CAAC;AACJ,CAAC,CACF;AACDF,MAAM,CAACqC,aAAa,GAAG,IAAI;AAE3B,SAASrC,MAAM,EAAEoC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}