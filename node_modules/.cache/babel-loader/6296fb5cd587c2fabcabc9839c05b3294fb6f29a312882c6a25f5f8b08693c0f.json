{"ast": null, "code": "export default {\n  utils: {\n    faceVerified: '已完成支付宝刷脸校验？',\n    unDone: '未完成',\n    done: '已完成',\n    changeVerifyMethod: '由于未能完成支付宝刷脸校验，将继续采用其他刷脸方式校验以完成合同签署。'\n  }\n};", "map": {"version": 3, "names": ["utils", "faceVerified", "unDone", "done", "changeVerifyMethod"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/utils/utils-ru.js"], "sourcesContent": ["export default {\n    utils: {\n        faceVerified: '已完成支付宝刷脸校验？',\n        unDone: '未完成',\n        done: '已完成',\n        changeVerifyMethod: '由于未能完成支付宝刷脸校验，将继续采用其他刷脸方式校验以完成合同签署。',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,YAAY,EAAE,aAAa;IAC3BC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACXC,kBAAkB,EAAE;EACxB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}