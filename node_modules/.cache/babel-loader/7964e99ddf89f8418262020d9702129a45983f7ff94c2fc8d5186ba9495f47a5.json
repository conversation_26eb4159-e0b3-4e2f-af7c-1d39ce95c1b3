{"ast": null, "code": "export default {\n  submitBaiscInfo: 'Submit basic information',\n  firstStep: 'First step',\n  secondStep: 'Second step',\n  thirdStep: 'Third step',\n  nextStep: 'Next step',\n  back: 'Back',\n  submit: 'Submit',\n  confirm: 'Ok',\n  affirm: 'Confirm',\n  cancel: 'Cancel',\n  tip: 'Tip',\n  account: 'Account number',\n  view: 'View',\n  iSee: 'I See',\n  submitAttorney: 'Submit the authorization letter',\n  plsDownloadSSQAttorneyTip: 'Please download BestSign Corporate Service Authorization Letter, take a picture of it with the corporate seal and then upload the picture.',\n  ssqAttorney: 'BestSign Corporate Service Authorization Letter',\n  downloadAttorney: 'Download authorization letter',\n  imgSupportType: 'Support png, jpeg, jpg, bmp format and the size should not exceed 10M.',\n  imgNoWatermarkTip: 'Only photos with no watermark or with \"only for real name authentication\" watermark can be used.',\n  confirmSubmit: 'Confirm submission',\n  backToPreviousPage: 'Back to previous page',\n  uploadImgBeforeSubmit: 'Please upload photos before submitting',\n  phoneNumBuyTip: 'Please make sure that the cell phone number is purchased under real name from the telecom operator, otherwise it will not pass the real name authentication.',\n  operatorPhoneVerify: 'Verify the cell phone number of the operator',\n  operatorName: 'Operator\\'s name',\n  operatorIdNum: 'Operator\\'s ID card number ',\n  modifyOperator: 'Modify the operator',\n  changeIdCard: 'Change ID documents',\n  operatorPassPhoneVerifyTip: ' The operator has passed the cell phone number authentication on {time}, and the authentication result is allowed to be reused.',\n  operatorPhoneNum: 'Cell phone number of the operator',\n  verifyCode: 'Verification code',\n  tryFaceVerify: 'Try face scan authentication',\n  afterPhoneVerifyTip: 'The system will automatically generate the authorization letter for you after cell phone number authentication. Please download the letter, stamp it and then upload it.',\n  operatorFaceVerify: 'Face scan authentication of the operator',\n  operatorPassFaceVerifyTip: ' The operator has passed the face scan authentication on {time}, and the authentication result is allowed to be reused.',\n  alipayFaceVerify: 'Alipay face scan authentication',\n  tryPhoneVerify: 'Try cell phone number authentication',\n  scanFaceNow: 'Scan your face now',\n  afterFaceVerifyTip: 'The system will automatically generate the authorization letter for you after face scan authentication. Please download the letter, stamp it and then upload it.',\n  plsEnterRightPhoneNum: 'Please fill in the correct cell phone number first',\n  plsGetVerifyCodeFirst: 'Please get the verification code first',\n  agreeSsqProtectMethod: 'I agree to the method provided by BestSign to protect my personal information',\n  ssqHowToProtectInfo: '《How Does BestSign Protect Your Personal Information》',\n  noEntNameTip: 'Since your company does not have a corporate name, it does not support authentication of the authorization letter',\n  legalName: 'Name of the legal representative',\n  legalIdCardNum: 'Legal representative\\'s ID number',\n  uploadIdCard: 'Upload ID',\n  legalPassFaceVerifyTip: 'The legal representative has passed the facial recognition authentication on {time}, and the authentication result is allowed to be reused.',\n  plsEnterLegalIdCardNum: 'Please enter the legal representative\\'s ID number first',\n  plsUploadLegalIdCardImg: 'Please upload the legal representative\\'s ID card first',\n  pageExpiredTip: 'The current page has expired. Please refresh the page',\n  faceFunctionNotEnabled: 'Face scan is temporarily unavailable. Please try again later',\n  plsSubmitLegalIdCard: 'Please submit the ID card of the legal representative',\n  legalPassPhoneVerifyTip: 'The legal representative has passed the mobile phone number authentication on {time}, and the authentication result is allowed to be reused. ',\n  legalPhoneNum: 'Mobile phone number of legal representative',\n  noPublicAccount: 'No corporate account?',\n  useSpecialMethod: 'I am a branch without a corporate account. Use the special channel.',\n  ssqRemitMoneyTip: 'BestSign will remit a sum of money (less than ¥0.99) to the corporate account below, and you can pass the corporate authentication by backfilling the payment amount.',\n  accountName: 'Account name',\n  bank: 'Bank',\n  accountBank: 'Account Bank',\n  accountBankName: 'Bank name',\n  bankAccount: 'Bank Account',\n  plsInputBankNameTip: 'Please enter the name of your bank, e.g. \"Bank of China\". Branch or sub-branch name is not required. ',\n  locationOfBank: 'Bank address',\n  plsSelect: 'Please choose',\n  province: 'Province',\n  city: 'City',\n  cityCounty: 'City / County',\n  nameOfBank: 'Branch name',\n  plsInputZhiBankName: 'Fill in the branch name, e.g. \"Bank of Hangzhou Wenchuang Sub-branch\"',\n  canNotFindBank: 'Can\\'t find the bank name?',\n  clickChangeReversePay: 'Click \"switch payment method\" at the bottom of the page for reverse payment.',\n  other: 'Other',\n  otherZhiBank: 'Branch name (please fill in the brank name if you choose \\'other\\')',\n  bankAccount1: 'Bank Account',\n  plsInputBankAccount: 'Please enter your bank account number',\n  remitNotSuccess: 'Remittance unsuccessful?',\n  switchPayMethod: 'Switch the payment method',\n  whySubmitBankInfo: 'Why do you need to submit your bank card information?',\n  submitBankInfoExplan: 'Bank card information authentication is a verification step in the corporate real name authentication process. BestSign will remit a verification fund to the bank card without debiting your account.',\n  plsSelectBank: 'Please select a bank from the drop-down list',\n  plsSelectArea: 'Please select the province, city/county from the drop-down list',\n  plsInputNameOfAccountBank: 'Please enter the name of your bank',\n  plsInputCorrectBankInfo: 'Please fill in the correct information of the bank used by your company',\n  plsInputFullBankName: 'Please fill in the full name of the bank that opened the account (including the name of the branch)',\n  area: 'Area',\n  contactCustomerService: 'Contact Customer Service',\n  beiJing: 'Beijing',\n  dongCheng: 'Dongcheng District',\n  fillJointBankNum: 'The account needs to fill in the bank\\'s joint bank number in order to send money',\n  bankType: {\n    type1: 'Bank',\n    type2: 'Union of Credit Unions',\n    type3: 'Credit union',\n    type4: 'Agricultural Cooperative'\n  },\n  accountBankArea: 'Bank address',\n  changeRemitMethod: 'Change remittance method',\n  canNotRemitAuth: 'Please choose other authentication methods as payment authentication failed for your corporate account.',\n  bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',\n  faceVerify: 'Face scan verification',\n  havePublicAccount: 'I have a corporate account.',\n  ensureInfoSafeTip: 'In order to protect the security of corporate information and prevent its misuse, please complete face scan of the legal representative first, and then proceed to payment for authentication.',\n  submitEntBankInfo: 'Submit your corporate account information.',\n  fillBackAmout: 'Fill in the backfill amount',\n  legalPerson: 'Legal representative',\n  operatorMange: 'Operator',\n  unionNum: 'Union number',\n  getUnionNumTip: 'Please obtain the inter-bank number from the financial communication of your company/unit, or according to the bank branch information',\n  uinonNumSearchOnline: 'Online inquiry of joint bank number',\n  remitProcessMap: {\n    submit: 'Payment request has been submitted',\n    accept: 'Payment has been accepted',\n    success: 'Payment has been successfully made',\n    receiveWait: 'Payment request has been accepted, and we are waiting for the bank to return the result. Your patience is much appreciated.',\n    successAttention: 'The payment has been successfully made. Please check the account details carefully and please note that:',\n    remarkTip: 'Remittance note: This payment is for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',\n    thirdChannel: 'BestSign remits money through third-party payment channels. The remittance channel is:',\n    remitNotSsq: 'BestSign remits money through third-party payment channels, and the account name of the remitter is not BestSign.',\n    remitPartySsq: 'The remitter is \"Hangzhou BestSign Network Technology Co., Ltd.\"'\n  },\n  close: 'Close',\n  name: 'Name',\n  idCardNum: 'Identity number',\n  reversePayMap: {\n    remitTip1: 'Remit 0.01 RMB to the corporate account of BestSign, then check the following options and click \"OK\" to pass corporate authentication.',\n    iAgree: 'I am informed and agree that: ',\n    remitAuthUse: 'RMB 0.01 remitted to the account designated by BestSign will be used to purchase 1 public contract if authentication is successful. This payment will not be refunded by BestSign if the authentication fails.',\n    authFailure: 'Failure to meet the following requirements will result in authentication failure：',\n    authFailureReason1: '(1) The remitting party is an existing legal entity.',\n    authFailureReason2: '(2) The remitting party must use an account under its own name to make the remittance.',\n    authFailureReason3: '(1) The remitting party\\'s name is the current name of the company：',\n    authFailureReason4: '(2) The remitter must use a corporate account for the remittance.',\n    plsInputBankName: 'Please enter the name of the bank account.',\n    authFailureReason5: '(3) The amount of the single payment remitted by the remitter to the recipient is 0.01 RMB. It shall not exceed 0.01 RMB or in other currencies.',\n    authFailureReason6: '(4) Date of remittance. Please ensure that the remittance is sent after {date}',\n    authFailureReason7: '(5) The recipient\\'s bank account information is as follows. Payment cannot be remitted to other accounts：',\n    authFailureReason8: '(6) When remitting money to the recipient, you must use and only note this verification code:',\n    remitDelay: 'There may be a delay in the arrival of the remittance.',\n    failureReason: 'Possible reasons for real name authentication failure: ',\n    wait30min: 'The bank system allows you to check the result after 30 minutes of successful remittance.',\n    queryProgress: 'Query progress',\n    inputRemitBankName: 'Please fill in the payer\\'s bank account name.',\n    queryFailureTip: 'Query failed. Please try again later...',\n    remitAuthSuccess: 'The remittance has been used to purchase 1 corporate contract.'\n  },\n  hourMinSec: '{hour}hour {min}minute {sec}second',\n  minSec: '{min}minute {sec}second',\n  sec: '{sec}second',\n  ssqRemitTip: 'BestSign has submitted a transfer of less than ¥1 to your account, which will arrive within 1-2 business days. Please confirm the amount here after it arrives.',\n  inputRightRemitAmout: 'You will need to check the details of your account\\'s transactions and enter this amount correctly to pass the authentication.',\n  notGetRemit: 'If you don\\'t see the remittance amount on your account, click here',\n  queryRemitProgress: 'Check the progress of the payment.',\n  inputWrongAccount: 'Wrong account number?',\n  remitNote: 'Remittance notes:',\n  ssqApplyCaTip: 'The amount is used for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',\n  remitAmout: 'Remittance amount',\n  yuan: 'RMB',\n  receiveAmout: 'The remittance amount is between RMB 0.01-0.99',\n  maxRemitTimeTip: 'Payments can be made maximally four times! Are you sure you want to resubmit the account number?',\n  remitFailure: 'The payment is not successful. You will be re-directed to the corporate payment page to re-apply for payment.',\n  plsInputRemitAmout: 'Please enter an amount between RMB 0.01 and RMB0.99.',\n  reSubmitBankInfo: 'You need to resubmit your bank card information.',\n  amoutError: 'Wrong amount',\n  reSubmit: 'Resubmit',\n  amoutInvalid: 'Invalid amount',\n  authReject: 'Information for real name authentication has been rejected. Please resubmit the information.',\n  authCertificate: 'Authorization letter authentication',\n  entPayAuth: 'Corporate payment authentication',\n  legalPhoneAuth: 'Legal representative\\'s cell phone number authentication',\n  legalFaceAuth: 'Legal representative\\'s face scan authentication',\n  sender: 'Sender',\n  requireYouThrough: '{name} requires you to pass {type}',\n  selectOneAuthMethod: 'Please choose any one of the following real name authentication methods:',\n  entCertificate: 'Corporate certificates ',\n  personCertificate: 'Personal ID documents',\n  iAmLegal: 'I am the legal representative',\n  iAmNotLegal: 'I am not a legal representative. I am the operator',\n  receiveSsqPhone: 'I accept follow up by BestSign on the phone',\n  plsAgreeSsqPhone: 'Please agree to BestSign\\'s follow-up call first',\n  submitInfoError: 'The name or ID number on the personal ID documents you submitted is wrong. Please verify again.',\n  submitIndividualEntAuth: 'You are submitting information for corporate authentication as an individual businessperson. There is no corporate name on your business license.',\n  submitIndividualCorrectIdCard: 'Please submit the legal representative\\'s personal ID card. ',\n  entPersonInfoError: 'You submit the wrong corporate / personal documents. Please verify and resubmit.',\n  noEntName: 'No business name',\n  businessLicenseBlank: 'Corporate name on the business license is: blank',\n  addressName: '/address/legal representative\\'s name',\n  plsClick: 'Please click',\n  haveEntName: 'Corporate name found. Request manual review by Customer Service.',\n  checkFollowInfoTip: 'Verify the following information and click \"OK\".  You can continue to submit other information for authentication. Corporate information will be manually reviewed by BestSign. If the information is not correct, all the information you submit will be rejected. It takes about 1 business day for information verification.',\n  entName: 'Company Name',\n  unifySocialCode: 'Unified social credit code',\n  uploadColorImgTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Enterprise information will be used to apply for a digital certificate.',\n  businessLicense: 'Business license',\n  uploadBusinessLicenseTip: 'Please upload the original in color or a copy with the corporate seal. If yours is not a company, please use the registration license.',\n  imgLimitTip: 'Photos are only in jpeg, jpg, png format and shall be no more than 10M in size. Only images with no watermark or with \"only for real name authentication\" watermark are allowed.',\n  autoRecognizedTip: 'The following information will be automatically verified. Please check it carefully and correct it if there is any mistake.',\n  iNoEntName: 'I don\\'t have a business name',\n  clickUseSpecialMethod: 'Click to use the special channel.',\n  socialCodeBuisnessNum: 'Uniform social credit code/business registration number',\n  plsSubmitCorrectLegalName: 'Please submit the correct legal representative\\'s name',\n  plsSubmitBusinessLicense: 'Please submit the business license first',\n  noEntUploadBusinessTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Only photos without watermark or \"only for signing real-name authentication\" watermark are supported. Enterprise information will be used to apply for a digital certificate.',\n  imgRequireTip: 'Photos are only available in jpeg, jpg, and png formats and the size does not exceed 10M. Only photos without watermark or \"only for signing BestSign real-name authentication\" watermark are supported.',\n  noEntNameUse: 'Individual businessperson with a corporate name  must use',\n  legalPlusCode: 'legal representative\\'s name plus the unified social credit code',\n  codeAsEntName: 'Code as corporate name',\n  ssq: 'BestSign',\n  entAuth: 'Enterprise Certification',\n  eleContractServiceBy: 'E-contracting service is provided by',\n  spericalProvide: '',\n  // 英文翻译特意设置为'', 因翻译文案中有<span>标签，英文中provide在上文eleContractServiceBy中有体现\n  redirectWait: 'Please wait for a moment',\n  businessLicenseImg: 'Business license photo',\n  idCardNational: 'National emblem side of the ID card',\n  idCardFace: 'Portrait side of the ID card',\n  dueToSignRequire: 'Due to the need of contract signing',\n  authorizeSomeone: 'I authorize {name} to obtain the following information:',\n  authoizedInfo: 'Basic information of my account (account number and name) and corporate basic information (corporate name, unified social credit code or registration number, legal representative\\'s name)',\n  iSubmitInfoToSsq: 'My submission on BestSign platform',\n  authMaterials: 'Documents for authentication',\n  sureInfo: 'All correct',\n  modifyInfo: 'Revise information',\n  additionalInfo: 'Additional information',\n  ssqNotifyMethod: 'The notication method I use on BestSign platform ',\n  phoneNum: 'Phone number',\n  email: 'Mailbox',\n  submitInfoPlsVerify: 'Corporate basic information has been submitted for you. Please verify',\n  operatorIdCardFaceImg: 'Portrait side of the operator\\'s ID card ',\n  operatorIdCardNational: 'National emblem side of the operator\\'s ID card ',\n  legalIdCardFaceImg: 'Portrait side of the legal representative\\'s ID card',\n  legalIdCardNational: 'National emblem side of the legal representative\\'s ID card ',\n  plsAgreeAuthorized: 'Please agree to authorize first',\n  finishConfirmInfo: 'You have completed information verification. Please continue the corporate authentication process.',\n  entOpenMultipleBusiniss: 'The company has opened multiple business lines, and the operation can not continue.',\n  contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理.',\n  signContract: 'Sign the contract',\n  faceVerifyFailure: 'Face scan authentication failed',\n  reFaceVerifyAuth: 'Scan face again for authentication',\n  threeTimesFailure: 'Sorry, you have failed 3 times in a row today. Please choose another authentication method.',\n  faceVerifySucess: 'Face scan authentication successful',\n  chooseOtherAuthMethod: 'Choose another authentication method',\n  plsContinueOnPc: 'Please continue to operate on the webpage',\n  accountAppealSuccess: 'Account appeal successful',\n  faceCompareSuccess: 'Face scan comparison successful',\n  plsUseWechatScan: 'Please use WeChat browser to scan the QR code.',\n  wechatCannotScanFace: 'Due to the change of WeChat\\'s policy, face scan is currently unavailable.',\n  plsUploadClearIdCardImg: 'Please upload a clear ID photo. The system will automatically identify the ID information. The photo is only in jpeg, jpg, png format and the size should not exceed 10M.',\n  uploadImgMap: {\n    tip1: 'Please upload a clear photo of your ID card.',\n    tip2: 'The system will automatically recognize the ID information.',\n    tip3: 'The photo must be in jpeg, jpg, or png format only and must not exceed 10M in size.',\n    tip4: 'Only pictures with no watermark or with \"only for real name authentication\" watermark are allowed.'\n  },\n  plsSubmitIdCard: 'Please submit your ID card first',\n  noNameNoSupportEntRemit: 'As your company does not have a corporate name, corporate payment authentication method is not supported.',\n  checkMoreVerison: 'View more versions',\n  viewCertificate: 'View certificate',\n  continueAuthNewEnt: 'Proceed with another corporate authentication.',\n  continueBuyPackage: 'Continue to purchase a package',\n  needSubmitBasicInfo: 'You are only required to submit basic information about your business.',\n  youFinishEntAuth: 'You have completed corporate authentication.',\n  loginSsqWebToEntAuth: 'Login to the official website of BestSign to complete the entire corporate authentication process. You can obtain higher level of authorization on BestSign platform.',\n  entAuthCertificate: 'Corporate Real Name Authentication Certificate',\n  youFinishEntAuthTip: 'You have already completed corporate real name authentication.',\n  backToHome: 'Back to homepage',\n  youNeedMoreOperate: 'You also need to {operate} complete the following operations ',\n  goPc: 'Go to PC',\n  addEntMemberStepMap: {\n    title: 'Steps to add corporate members: ',\n    step1: '1、Enter the corporate console',\n    step2: '2、Open \"Member Management\"',\n    step3: '3、Click to add a new member',\n    step4: '4、Enter the account number and name, and select the role',\n    step5: '5、Click \"Save\" and the new member will be added.员'\n  },\n  addEntMember: 'Add corporate members',\n  addSealStepMap: {\n    title: 'Steps to add a seal: ',\n    step1: '1、Enter the corporate console',\n    step2: '2、Open the seal list',\n    step3: '3、Click \"Add Seal\"',\n    step4: '4、Enter the name of the seal, upload the seal pattern or produce electronic signatures',\n    step5: '5、Click \"Save\" and the new seal will be added',\n    step6: '6、Click \"Add Holder\"',\n    step7: '7、Select the corporate member',\n    step8: '8、Click \"OK\" to add the seal holder'\n  },\n  addSeal: 'Add the seal',\n  waitApporve: 'Pending for review',\n  submitAuthMaterial: 'Submit auth information required',\n  authFinish: 'Authentication completed',\n  plsSubmitBeforeTip: 'Please submit all information before {date}, otherwise the basic information will be invalid.',\n  oneDayFinishApprove: 'Customer service will complete the review in one business day. We appreciate your patience.',\n  entAuthFinish: 'Corporate real name authentication completed',\n  baseInfoInvalid: 'Basic information is no longer valid. Please resubmit the information.',\n  missParams: 'Missing parameters!',\n  illegalLink: 'Illegal link',\n  cookieNotEnabe: 'Can not read or write cookies. Please check whether you have initiated the no trace / private mode or other operations that have disabled the cookies.',\n  truthSubmitOperatorIdCard: 'Please submit the personal ID card of the operator.',\n  abandonAttorneyAuth: 'Waiver of authorization letter authentication, ',\n  modifyCertiInfo: 'Revise document information',\n  modifyAttorneyInfo: 'Revise of authorization letter information',\n  plsUploadBusinessLicense: 'Please upload the business license!',\n  plsUploadLegalCerti: 'Please upload the legal entity\\'s documents!',\n  plsUploadOperatorCerti: 'Please upload the operator\\'s documents!',\n  legalIdCardSubmit: 'Legal representative\\'s ID card submitted',\n  serviceAttorneryAuth: 'Service authorization letter authentication',\n  accountAppealMap: {\n    entApeal: 'Business account appeal',\n    apealSuccess: 'Complaint completed',\n    comName: 'Company Name:',\n    account: 'account number:',\n    verifyCode: 'Verification code:',\n    ener6Digtal: 'Please fill in 6 digits',\n    beMainManagerTip: 'After successful business account application, you will become the business owner administrator',\n    mainAccount: 'Primary administrator account',\n    continueSign: 'Continue to sign',\n    continueConfirm: '继续认证',\n    plsEnterComName: 'Please fill in the company name first',\n    plsEnterCorrentComName: 'Please fill in the correct company name',\n    sendSuccess: 'Sent successfully!',\n    plsEnterCorrectCode: 'Please enter correct verfication code'\n  },\n  faceInitLoading: 'Initialize the face tag, please wait',\n  wxFaceVersionTip: 'Face brushing requires WeChat version 7.0.12 and above, please upgrade first',\n  wxIosFaceVersionTip: 'Face brushing requires ios version 10.3 and above, please upgrade first',\n  wxAndroidVersionTip: 'Face brushing requires android version 5.0 and above, please upgrade first',\n  faceInitFailure: 'Failed to initialize face tag: ',\n  entAuthCertificateTip: 'Enterprise real-name certification',\n  idCardHandHeld: 'Handheld ID card authentication',\n  faceAuth: 'Face recognition',\n  noMainlandAuth: 'Non-mainland certification',\n  entAuthTip: 'Enterprise real-name certification',\n  signIntro: 'Guide of sign',\n  companySet: 'Enterprise settings'\n};", "map": {"version": 3, "names": ["submitBaiscInfo", "firstStep", "secondStep", "thirdStep", "nextStep", "back", "submit", "confirm", "affirm", "cancel", "tip", "account", "view", "iSee", "submit<PERSON><PERSON><PERSON><PERSON>", "plsDownloadSSQAttorneyTip", "ssqAttorney", "downloadAttorney", "imgSupportType", "imgNoWatermarkTip", "confirmSubmit", "backToPreviousPage", "uploadImgBeforeSubmit", "phoneNumBuyTip", "operatorPhoneVerify", "operatorName", "operatorIdNum", "modifyOperator", "changeIdCard", "operatorPassPhoneVerifyTip", "operatorPhoneNum", "verifyCode", "tryFaceVerify", "afterPhoneVerifyTip", "operatorFaceVerify", "operatorPassFaceVerifyTip", "alipayFaceVerify", "tryPhoneVerify", "scanFaceNow", "afterFaceVerifyTip", "plsEnterRightPhoneNum", "plsGetVerifyCodeFirst", "agreeSsqProtectMethod", "ssqHowToProtectInfo", "noEntNameTip", "legalName", "legalIdCardNum", "uploadIdCard", "legalPassFaceVerifyTip", "plsEnterLegalIdCardNum", "plsUploadLegalIdCardImg", "pageExpiredTip", "faceFunctionNotEnabled", "plsSubmitLegalIdCard", "legalPassPhoneVerifyTip", "legalPhoneNum", "noPublicAccount", "useSpecialMethod", "ssqRemitMoneyTip", "accountName", "bank", "accountBank", "accountBankName", "bankAccount", "plsInputBankNameTip", "locationOfBank", "plsSelect", "province", "city", "cityCounty", "nameOfBank", "plsInputZhiBankName", "canNotFindBank", "clickChangeReversePay", "other", "otherZhiBank", "bankAccount1", "plsInputBankAccount", "remitNotSuccess", "switchPayMethod", "whySubmitBankInfo", "submitBankInfoExplan", "plsSelectBank", "plsSelectArea", "plsInputNameOfAccountBank", "plsInputCorrectBankInfo", "plsInputFullBankName", "area", "contactCustomerService", "beiJing", "dongCheng", "fillJointBankNum", "bankType", "type1", "type2", "type3", "type4", "accountBankArea", "changeRemitMethod", "canNotRemitAuth", "bankMaintenanceTip", "faceVerify", "havePublicAccount", "ensureInfoSafeTip", "submitEntBankInfo", "fillBackAmout", "legal<PERSON>erson", "operatorMange", "unionNum", "getUnionNumTip", "uinonNumSearchOnline", "remitProcessMap", "accept", "success", "receiveWait", "successAttention", "remarkTip", "thirdChannel", "remitNotSsq", "remitPartySsq", "close", "name", "idCardNum", "reversePayMap", "remitTip1", "iAgree", "remitAuthUse", "authFailure", "authFailureReason1", "authFailureReason2", "authFailureReason3", "authFailureReason4", "plsInputBankName", "authFailureReason5", "authFailureReason6", "authFailureReason7", "authFailureReason8", "remitDelay", "failureReason", "wait30min", "queryProgress", "inputRemitBankName", "queryFailureTip", "remitAuthSuccess", "hourMinSec", "minSec", "sec", "ssqRemitTip", "inputRightRemitAmout", "notGetRemit", "queryRemitProgress", "inputWrongAccount", "remitNote", "ssqApplyCaTip", "remitAmout", "yuan", "receiveAmout", "maxRemitTimeTip", "remitFailure", "plsInputRemitAmout", "reSubmitBankInfo", "amoutError", "reSubmit", "amoutInvalid", "authReject", "authCertificate", "entPayAuth", "legalPhoneAuth", "legalFaceAuth", "sender", "requireYouThrough", "selectOneAuthMethod", "entCertificate", "personCertificate", "iAmLegal", "iAmNotLegal", "receiveSsqPhone", "plsAgreeSsqPhone", "submitInfoError", "submitIndividualEntAuth", "submitIndividualCorrectIdCard", "entPersonInfoError", "noEntName", "businessLicenseBlank", "addressName", "plsClick", "haveEntName", "checkFollowInfoTip", "entName", "unifySocialCode", "uploadColorImgTip", "businessLicense", "uploadBusinessLicenseTip", "imgLimitTip", "autoRecognizedTip", "iNoEntName", "clickUseSpecialMethod", "socialCodeBuisnessNum", "plsSubmitCorrectLegalName", "plsSubmitBusinessLicense", "noEntUploadBusinessTip", "imgRequireTip", "noEntNameUse", "legalPlusCode", "codeAsEntName", "ssq", "entAuth", "eleContractServiceBy", "spericalProvide", "redirectWait", "businessLicenseImg", "idCardNational", "idCardFace", "dueToSignRequire", "authorizeSomeone", "authoizedInfo", "iSubmitInfoToSsq", "authMaterials", "sureInfo", "modifyInfo", "additionalInfo", "ssqNotifyMethod", "phoneNum", "email", "submitInfoPlsVerify", "operatorIdCardFaceImg", "operatorIdCardNational", "legalIdCardFaceImg", "legalIdCardNational", "plsAgreeAuthorized", "finishConfirmInfo", "entOpenMultipleBusiniss", "contactSsqDeal", "signContract", "faceVerifyFailure", "reFaceVerifyAuth", "threeTimesFailure", "faceVerifySucess", "chooseOtherAuthMethod", "plsContinueOnPc", "accountAppealSuccess", "faceCompareSuccess", "plsUseWechatScan", "wechatCannotScanFace", "plsUploadClearIdCardImg", "uploadImgMap", "tip1", "tip2", "tip3", "tip4", "plsSubmitIdCard", "noNameNoSupportEntRemit", "check<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewCertificate", "continueAuthNewEnt", "continueBuyPackage", "needSubmitBasicInfo", "youFinishEntAuth", "loginSsqWebToEntAuth", "entAuthCertificate", "youFinishEntAuthTip", "backToHome", "youNeedMoreOperate", "goPc", "addEntMemberStepMap", "title", "step1", "step2", "step3", "step4", "step5", "addEntMember", "addSealStepMap", "step6", "step7", "step8", "addSeal", "waitApporve", "submitAuthMaterial", "auth<PERSON><PERSON><PERSON>", "plsSubmitBeforeTip", "oneDayFinishApprove", "ent<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseInfoInvalid", "missParams", "illegalLink", "cookieNotEnabe", "truthSubmitOperatorIdCard", "abandonAttorneyAuth", "modifyCertiInfo", "modifyAttorneyInfo", "plsUploadBusinessLicense", "plsUploadLegalCerti", "plsUploadOperatorCerti", "legalIdCardSubmit", "serviceAttorneryAuth", "accountAppealMap", "entApeal", "apealSuccess", "comName", "ener6Digtal", "beMainManagerTip", "mainAccount", "continueSign", "continueConfirm", "plsEnterComName", "plsEnterCorrentComName", "sendSuccess", "plsEnterCorrectCode", "faceInitLoading", "wxFaceVersionTip", "wxIosFaceVersionTip", "wxAndroidVersionTip", "faceInitFailure", "entAuthCertificateTip", "idCardHandHeld", "faceAuth", "noMainland<PERSON>uth", "entAuthTip", "signIntro", "companySet"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/entAuth/entAuth-en.js"], "sourcesContent": ["export default {\n    submitBaiscInfo: 'Submit basic information',\n    firstStep: 'First step',\n    secondStep: 'Second step',\n    thirdStep: 'Third step',\n    nextStep: 'Next step',\n    back: 'Back',\n    submit: 'Submit',\n    confirm: 'Ok',\n    affirm: 'Confirm',\n    cancel: 'Cancel',\n    tip: 'Tip',\n    account: 'Account number',\n    view: 'View',\n    iSee: 'I See',\n    submitAttorney: 'Submit the authorization letter',\n    plsDownloadSSQAttorneyTip: 'Please download BestSign Corporate Service Authorization Letter, take a picture of it with the corporate seal and then upload the picture.',\n    ssqAttorney: 'BestSign Corporate Service Authorization Letter',\n    downloadAttorney: 'Download authorization letter',\n    imgSupportType: 'Support png, jpeg, jpg, bmp format and the size should not exceed 10M.',\n    imgNoWatermarkTip: 'Only photos with no watermark or with \"only for real name authentication\" watermark can be used.',\n    confirmSubmit: 'Confirm submission',\n    backToPreviousPage: 'Back to previous page',\n    uploadImgBeforeSubmit: 'Please upload photos before submitting',\n    phoneNumBuyTip: 'Please make sure that the cell phone number is purchased under real name from the telecom operator, otherwise it will not pass the real name authentication.',\n    operatorPhoneVerify: 'Verify the cell phone number of the operator',\n    operatorName: 'Operator\\'s name',\n    operatorIdNum: 'Operator\\'s ID card number ',\n    modifyOperator: 'Modify the operator',\n    changeIdCard: 'Change ID documents',\n    operatorPassPhoneVerifyTip: ' The operator has passed the cell phone number authentication on {time}, and the authentication result is allowed to be reused.',\n    operatorPhoneNum: 'Cell phone number of the operator',\n    verifyCode: 'Verification code',\n    tryFaceVerify: 'Try face scan authentication',\n    afterPhoneVerifyTip: 'The system will automatically generate the authorization letter for you after cell phone number authentication. Please download the letter, stamp it and then upload it.',\n    operatorFaceVerify: 'Face scan authentication of the operator',\n    operatorPassFaceVerifyTip: ' The operator has passed the face scan authentication on {time}, and the authentication result is allowed to be reused.',\n    alipayFaceVerify: 'Alipay face scan authentication',\n    tryPhoneVerify: 'Try cell phone number authentication',\n    scanFaceNow: 'Scan your face now',\n    afterFaceVerifyTip: 'The system will automatically generate the authorization letter for you after face scan authentication. Please download the letter, stamp it and then upload it.',\n    plsEnterRightPhoneNum: 'Please fill in the correct cell phone number first',\n    plsGetVerifyCodeFirst: 'Please get the verification code first',\n    agreeSsqProtectMethod: 'I agree to the method provided by BestSign to protect my personal information',\n    ssqHowToProtectInfo: '《How Does BestSign Protect Your Personal Information》',\n    noEntNameTip: 'Since your company does not have a corporate name, it does not support authentication of the authorization letter',\n    legalName: 'Name of the legal representative',\n    legalIdCardNum: 'Legal representative\\'s ID number',\n    uploadIdCard: 'Upload ID',\n    legalPassFaceVerifyTip: 'The legal representative has passed the facial recognition authentication on {time}, and the authentication result is allowed to be reused.',\n    plsEnterLegalIdCardNum: 'Please enter the legal representative\\'s ID number first',\n    plsUploadLegalIdCardImg: 'Please upload the legal representative\\'s ID card first',\n    pageExpiredTip: 'The current page has expired. Please refresh the page',\n    faceFunctionNotEnabled: 'Face scan is temporarily unavailable. Please try again later',\n    plsSubmitLegalIdCard: 'Please submit the ID card of the legal representative',\n    legalPassPhoneVerifyTip: 'The legal representative has passed the mobile phone number authentication on {time}, and the authentication result is allowed to be reused. ',\n    legalPhoneNum: 'Mobile phone number of legal representative',\n    noPublicAccount: 'No corporate account?',\n    useSpecialMethod: 'I am a branch without a corporate account. Use the special channel.',\n    ssqRemitMoneyTip: 'BestSign will remit a sum of money (less than ¥0.99) to the corporate account below, and you can pass the corporate authentication by backfilling the payment amount.',\n    accountName: 'Account name',\n    bank: 'Bank',\n    accountBank: 'Account Bank',\n    accountBankName: 'Bank name',\n    bankAccount: 'Bank Account',\n    plsInputBankNameTip: 'Please enter the name of your bank, e.g. \"Bank of China\". Branch or sub-branch name is not required. ',\n    locationOfBank: 'Bank address',\n    plsSelect: 'Please choose',\n    province: 'Province',\n    city: 'City',\n    cityCounty: 'City / County',\n    nameOfBank: 'Branch name',\n    plsInputZhiBankName: 'Fill in the branch name, e.g. \"Bank of Hangzhou Wenchuang Sub-branch\"',\n    canNotFindBank: 'Can\\'t find the bank name?',\n    clickChangeReversePay: 'Click \"switch payment method\" at the bottom of the page for reverse payment.',\n    other: 'Other',\n    otherZhiBank: 'Branch name (please fill in the brank name if you choose \\'other\\')',\n    bankAccount1: 'Bank Account',\n    plsInputBankAccount: 'Please enter your bank account number',\n    remitNotSuccess: 'Remittance unsuccessful?',\n    switchPayMethod: 'Switch the payment method',\n    whySubmitBankInfo: 'Why do you need to submit your bank card information?',\n    submitBankInfoExplan: 'Bank card information authentication is a verification step in the corporate real name authentication process. BestSign will remit a verification fund to the bank card without debiting your account.',\n    plsSelectBank: 'Please select a bank from the drop-down list',\n    plsSelectArea: 'Please select the province, city/county from the drop-down list',\n    plsInputNameOfAccountBank: 'Please enter the name of your bank',\n    plsInputCorrectBankInfo: 'Please fill in the correct information of the bank used by your company',\n    plsInputFullBankName: 'Please fill in the full name of the bank that opened the account (including the name of the branch)',\n    area: 'Area',\n    contactCustomerService: 'Contact Customer Service',\n    beiJing: 'Beijing',\n    dongCheng: 'Dongcheng District',\n    fillJointBankNum: 'The account needs to fill in the bank\\'s joint bank number in order to send money',\n    bankType: {\n        type1: 'Bank',\n        type2: 'Union of Credit Unions',\n        type3: 'Credit union',\n        type4: 'Agricultural Cooperative',\n    },\n    accountBankArea: 'Bank address',\n    changeRemitMethod: 'Change remittance method',\n    canNotRemitAuth: 'Please choose other authentication methods as payment authentication failed for your corporate account.',\n    bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',\n    faceVerify: 'Face scan verification',\n    havePublicAccount: 'I have a corporate account.',\n    ensureInfoSafeTip: 'In order to protect the security of corporate information and prevent its misuse, please complete face scan of the legal representative first, and then proceed to payment for authentication.',\n    submitEntBankInfo: 'Submit your corporate account information.',\n    fillBackAmout: 'Fill in the backfill amount',\n    legalPerson: 'Legal representative',\n    operatorMange: 'Operator',\n    unionNum: 'Union number',\n    getUnionNumTip: 'Please obtain the inter-bank number from the financial communication of your company/unit, or according to the bank branch information',\n    uinonNumSearchOnline: 'Online inquiry of joint bank number',\n    remitProcessMap: {\n        submit: 'Payment request has been submitted',\n        accept: 'Payment has been accepted',\n        success: 'Payment has been successfully made',\n        receiveWait: 'Payment request has been accepted, and we are waiting for the bank to return the result. Your patience is much appreciated.',\n        successAttention: 'The payment has been successfully made. Please check the account details carefully and please note that:',\n        remarkTip: 'Remittance note: This payment is for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',\n        thirdChannel: 'BestSign remits money through third-party payment channels. The remittance channel is:',\n        remitNotSsq: 'BestSign remits money through third-party payment channels, and the account name of the remitter is not BestSign.',\n        remitPartySsq: 'The remitter is \"Hangzhou BestSign Network Technology Co., Ltd.\"',\n    },\n    close: 'Close',\n    name: 'Name',\n    idCardNum: 'Identity number',\n    reversePayMap: {\n        remitTip1: 'Remit 0.01 RMB to the corporate account of BestSign, then check the following options and click \"OK\" to pass corporate authentication.',\n        iAgree: 'I am informed and agree that: ',\n        remitAuthUse: 'RMB 0.01 remitted to the account designated by BestSign will be used to purchase 1 public contract if authentication is successful. This payment will not be refunded by BestSign if the authentication fails.',\n        authFailure: 'Failure to meet the following requirements will result in authentication failure：',\n        authFailureReason1: '(1) The remitting party is an existing legal entity.',\n        authFailureReason2: '(2) The remitting party must use an account under its own name to make the remittance.',\n        authFailureReason3: '(1) The remitting party\\'s name is the current name of the company：',\n        authFailureReason4: '(2) The remitter must use a corporate account for the remittance.',\n        plsInputBankName: 'Please enter the name of the bank account.',\n        authFailureReason5: '(3) The amount of the single payment remitted by the remitter to the recipient is 0.01 RMB. It shall not exceed 0.01 RMB or in other currencies.',\n        authFailureReason6: '(4) Date of remittance. Please ensure that the remittance is sent after {date}',\n        authFailureReason7: '(5) The recipient\\'s bank account information is as follows. Payment cannot be remitted to other accounts：',\n        authFailureReason8: '(6) When remitting money to the recipient, you must use and only note this verification code:',\n        remitDelay: 'There may be a delay in the arrival of the remittance.',\n        failureReason: 'Possible reasons for real name authentication failure: ',\n        wait30min: 'The bank system allows you to check the result after 30 minutes of successful remittance.',\n        queryProgress: 'Query progress',\n        inputRemitBankName: 'Please fill in the payer\\'s bank account name.',\n        queryFailureTip: 'Query failed. Please try again later...',\n        remitAuthSuccess: 'The remittance has been used to purchase 1 corporate contract.',\n    },\n    hourMinSec: '{hour}hour {min}minute {sec}second',\n    minSec: '{min}minute {sec}second',\n    sec: '{sec}second',\n    ssqRemitTip: 'BestSign has submitted a transfer of less than ¥1 to your account, which will arrive within 1-2 business days. Please confirm the amount here after it arrives.',\n    inputRightRemitAmout: 'You will need to check the details of your account\\'s transactions and enter this amount correctly to pass the authentication.',\n    notGetRemit: 'If you don\\'t see the remittance amount on your account, click here',\n    queryRemitProgress: 'Check the progress of the payment.',\n    inputWrongAccount: 'Wrong account number?',\n    remitNote: 'Remittance notes:',\n    ssqApplyCaTip: 'The amount is used for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',\n    remitAmout: 'Remittance amount',\n    yuan: 'RMB',\n    receiveAmout: 'The remittance amount is between RMB 0.01-0.99',\n    maxRemitTimeTip: 'Payments can be made maximally four times! Are you sure you want to resubmit the account number?',\n    remitFailure: 'The payment is not successful. You will be re-directed to the corporate payment page to re-apply for payment.',\n    plsInputRemitAmout: 'Please enter an amount between RMB 0.01 and RMB0.99.',\n    reSubmitBankInfo: 'You need to resubmit your bank card information.',\n    amoutError: 'Wrong amount',\n    reSubmit: 'Resubmit',\n    amoutInvalid: 'Invalid amount',\n    authReject: 'Information for real name authentication has been rejected. Please resubmit the information.',\n    authCertificate: 'Authorization letter authentication',\n    entPayAuth: 'Corporate payment authentication',\n    legalPhoneAuth: 'Legal representative\\'s cell phone number authentication',\n    legalFaceAuth: 'Legal representative\\'s face scan authentication',\n    sender: 'Sender',\n    requireYouThrough: '{name} requires you to pass {type}',\n    selectOneAuthMethod: 'Please choose any one of the following real name authentication methods:',\n    entCertificate: 'Corporate certificates ',\n    personCertificate: 'Personal ID documents',\n    iAmLegal: 'I am the legal representative',\n    iAmNotLegal: 'I am not a legal representative. I am the operator',\n    receiveSsqPhone: 'I accept follow up by BestSign on the phone',\n    plsAgreeSsqPhone: 'Please agree to BestSign\\'s follow-up call first',\n    submitInfoError: 'The name or ID number on the personal ID documents you submitted is wrong. Please verify again.',\n    submitIndividualEntAuth: 'You are submitting information for corporate authentication as an individual businessperson. There is no corporate name on your business license.',\n    submitIndividualCorrectIdCard: 'Please submit the legal representative\\'s personal ID card. ',\n    entPersonInfoError: 'You submit the wrong corporate / personal documents. Please verify and resubmit.',\n    noEntName: 'No business name',\n    businessLicenseBlank: 'Corporate name on the business license is: blank',\n    addressName: '/address/legal representative\\'s name',\n    plsClick: 'Please click',\n    haveEntName: 'Corporate name found. Request manual review by Customer Service.',\n    checkFollowInfoTip: 'Verify the following information and click \"OK\".  You can continue to submit other information for authentication. Corporate information will be manually reviewed by BestSign. If the information is not correct, all the information you submit will be rejected. It takes about 1 business day for information verification.',\n    entName: 'Company Name',\n    unifySocialCode: 'Unified social credit code',\n    uploadColorImgTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Enterprise information will be used to apply for a digital certificate.',\n    businessLicense: 'Business license',\n    uploadBusinessLicenseTip: 'Please upload the original in color or a copy with the corporate seal. If yours is not a company, please use the registration license.',\n    imgLimitTip: 'Photos are only in jpeg, jpg, png format and shall be no more than 10M in size. Only images with no watermark or with \"only for real name authentication\" watermark are allowed.',\n    autoRecognizedTip: 'The following information will be automatically verified. Please check it carefully and correct it if there is any mistake.',\n    iNoEntName: 'I don\\'t have a business name',\n    clickUseSpecialMethod: 'Click to use the special channel.',\n    socialCodeBuisnessNum: 'Uniform social credit code/business registration number',\n    plsSubmitCorrectLegalName: 'Please submit the correct legal representative\\'s name',\n    plsSubmitBusinessLicense: 'Please submit the business license first',\n    noEntUploadBusinessTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Only photos without watermark or \"only for signing real-name authentication\" watermark are supported. Enterprise information will be used to apply for a digital certificate.',\n    imgRequireTip: 'Photos are only available in jpeg, jpg, and png formats and the size does not exceed 10M. Only photos without watermark or \"only for signing BestSign real-name authentication\" watermark are supported.',\n    noEntNameUse: 'Individual businessperson with a corporate name  must use',\n    legalPlusCode: 'legal representative\\'s name plus the unified social credit code',\n    codeAsEntName: 'Code as corporate name',\n    ssq: 'BestSign',\n    entAuth: 'Enterprise Certification',\n    eleContractServiceBy: 'E-contracting service is provided by',\n    spericalProvide: '', // 英文翻译特意设置为'', 因翻译文案中有<span>标签，英文中provide在上文eleContractServiceBy中有体现\n    redirectWait: 'Please wait for a moment',\n    businessLicenseImg: 'Business license photo',\n    idCardNational: 'National emblem side of the ID card',\n    idCardFace: 'Portrait side of the ID card',\n    dueToSignRequire: 'Due to the need of contract signing',\n    authorizeSomeone: 'I authorize {name} to obtain the following information:',\n    authoizedInfo: 'Basic information of my account (account number and name) and corporate basic information (corporate name, unified social credit code or registration number, legal representative\\'s name)',\n    iSubmitInfoToSsq: 'My submission on BestSign platform',\n    authMaterials: 'Documents for authentication',\n    sureInfo: 'All correct',\n    modifyInfo: 'Revise information',\n    additionalInfo: 'Additional information',\n    ssqNotifyMethod: 'The notication method I use on BestSign platform ',\n    phoneNum: 'Phone number',\n    email: 'Mailbox',\n    submitInfoPlsVerify: 'Corporate basic information has been submitted for you. Please verify',\n    operatorIdCardFaceImg: 'Portrait side of the operator\\'s ID card ',\n    operatorIdCardNational: 'National emblem side of the operator\\'s ID card ',\n    legalIdCardFaceImg: 'Portrait side of the legal representative\\'s ID card',\n    legalIdCardNational: 'National emblem side of the legal representative\\'s ID card ',\n    plsAgreeAuthorized: 'Please agree to authorize first',\n    finishConfirmInfo: 'You have completed information verification. Please continue the corporate authentication process.',\n    entOpenMultipleBusiniss: 'The company has opened multiple business lines, and the operation can not continue.',\n    contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理.',\n    signContract: 'Sign the contract',\n    faceVerifyFailure: 'Face scan authentication failed',\n    reFaceVerifyAuth: 'Scan face again for authentication',\n    threeTimesFailure: 'Sorry, you have failed 3 times in a row today. Please choose another authentication method.',\n    faceVerifySucess: 'Face scan authentication successful',\n    chooseOtherAuthMethod: 'Choose another authentication method',\n    plsContinueOnPc: 'Please continue to operate on the webpage',\n    accountAppealSuccess: 'Account appeal successful',\n    faceCompareSuccess: 'Face scan comparison successful',\n    plsUseWechatScan: 'Please use WeChat browser to scan the QR code.',\n    wechatCannotScanFace: 'Due to the change of WeChat\\'s policy, face scan is currently unavailable.',\n    plsUploadClearIdCardImg: 'Please upload a clear ID photo. The system will automatically identify the ID information. The photo is only in jpeg, jpg, png format and the size should not exceed 10M.',\n    uploadImgMap: {\n        tip1: 'Please upload a clear photo of your ID card.',\n        tip2: 'The system will automatically recognize the ID information.',\n        tip3: 'The photo must be in jpeg, jpg, or png format only and must not exceed 10M in size.',\n        tip4: 'Only pictures with no watermark or with \"only for real name authentication\" watermark are allowed.',\n    },\n    plsSubmitIdCard: 'Please submit your ID card first',\n    noNameNoSupportEntRemit: 'As your company does not have a corporate name, corporate payment authentication method is not supported.',\n    checkMoreVerison: 'View more versions',\n    viewCertificate: 'View certificate',\n    continueAuthNewEnt: 'Proceed with another corporate authentication.',\n    continueBuyPackage: 'Continue to purchase a package',\n    needSubmitBasicInfo: 'You are only required to submit basic information about your business.',\n    youFinishEntAuth: 'You have completed corporate authentication.',\n    loginSsqWebToEntAuth: 'Login to the official website of BestSign to complete the entire corporate authentication process. You can obtain higher level of authorization on BestSign platform.',\n    entAuthCertificate: 'Corporate Real Name Authentication Certificate',\n    youFinishEntAuthTip: 'You have already completed corporate real name authentication.',\n    backToHome: 'Back to homepage',\n    youNeedMoreOperate: 'You also need to {operate} complete the following operations ',\n    goPc: 'Go to PC',\n    addEntMemberStepMap: {\n        title: 'Steps to add corporate members: ',\n        step1: '1、Enter the corporate console',\n        step2: '2、Open \"Member Management\"',\n        step3: '3、Click to add a new member',\n        step4: '4、Enter the account number and name, and select the role',\n        step5: '5、Click \"Save\" and the new member will be added.员',\n    },\n    addEntMember: 'Add corporate members',\n    addSealStepMap: {\n        title: 'Steps to add a seal: ',\n        step1: '1、Enter the corporate console',\n        step2: '2、Open the seal list',\n        step3: '3、Click \"Add Seal\"',\n        step4: '4、Enter the name of the seal, upload the seal pattern or produce electronic signatures',\n        step5: '5、Click \"Save\" and the new seal will be added',\n        step6: '6、Click \"Add Holder\"',\n        step7: '7、Select the corporate member',\n        step8: '8、Click \"OK\" to add the seal holder',\n    },\n    addSeal: 'Add the seal',\n    waitApporve: 'Pending for review',\n    submitAuthMaterial: 'Submit auth information required',\n    authFinish: 'Authentication completed',\n    plsSubmitBeforeTip: 'Please submit all information before {date}, otherwise the basic information will be invalid.',\n    oneDayFinishApprove: 'Customer service will complete the review in one business day. We appreciate your patience.',\n    entAuthFinish: 'Corporate real name authentication completed',\n    baseInfoInvalid: 'Basic information is no longer valid. Please resubmit the information.',\n    missParams: 'Missing parameters!',\n    illegalLink: 'Illegal link',\n    cookieNotEnabe: 'Can not read or write cookies. Please check whether you have initiated the no trace / private mode or other operations that have disabled the cookies.',\n    truthSubmitOperatorIdCard: 'Please submit the personal ID card of the operator.',\n    abandonAttorneyAuth: 'Waiver of authorization letter authentication, ',\n    modifyCertiInfo: 'Revise document information',\n    modifyAttorneyInfo: 'Revise of authorization letter information',\n    plsUploadBusinessLicense: 'Please upload the business license!',\n    plsUploadLegalCerti: 'Please upload the legal entity\\'s documents!',\n    plsUploadOperatorCerti: 'Please upload the operator\\'s documents!',\n    legalIdCardSubmit: 'Legal representative\\'s ID card submitted',\n    serviceAttorneryAuth: 'Service authorization letter authentication',\n    accountAppealMap: {\n        entApeal: 'Business account appeal',\n        apealSuccess: 'Complaint completed',\n        comName: 'Company Name:',\n        account: 'account number:',\n        verifyCode: 'Verification code:',\n        ener6Digtal: 'Please fill in 6 digits',\n        beMainManagerTip: 'After successful business account application, you will become the business owner administrator',\n        mainAccount: 'Primary administrator account',\n        continueSign: 'Continue to sign',\n        continueConfirm: '继续认证',\n        plsEnterComName: 'Please fill in the company name first',\n        plsEnterCorrentComName: 'Please fill in the correct company name',\n        sendSuccess: 'Sent successfully!',\n        plsEnterCorrectCode: 'Please enter correct verfication code',\n    },\n    faceInitLoading: 'Initialize the face tag, please wait',\n    wxFaceVersionTip: 'Face brushing requires WeChat version 7.0.12 and above, please upgrade first',\n    wxIosFaceVersionTip: 'Face brushing requires ios version 10.3 and above, please upgrade first',\n    wxAndroidVersionTip: 'Face brushing requires android version 5.0 and above, please upgrade first',\n    faceInitFailure: 'Failed to initialize face tag: ',\n    entAuthCertificateTip: 'Enterprise real-name certification',\n    idCardHandHeld: 'Handheld ID card authentication',\n    faceAuth: 'Face recognition',\n    noMainlandAuth: 'Non-mainland certification',\n    entAuthTip: 'Enterprise real-name certification',\n    signIntro: 'Guide of sign',\n    companySet: 'Enterprise settings',\n\n};\n"], "mappings": "AAAA,eAAe;EACXA,eAAe,EAAE,0BAA0B;EAC3CC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,KAAK;EACVC,OAAO,EAAE,gBAAgB;EACzBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,OAAO;EACbC,cAAc,EAAE,iCAAiC;EACjDC,yBAAyB,EAAE,4IAA4I;EACvKC,WAAW,EAAE,iDAAiD;EAC9DC,gBAAgB,EAAE,+BAA+B;EACjDC,cAAc,EAAE,wEAAwE;EACxFC,iBAAiB,EAAE,kGAAkG;EACrHC,aAAa,EAAE,oBAAoB;EACnCC,kBAAkB,EAAE,uBAAuB;EAC3CC,qBAAqB,EAAE,wCAAwC;EAC/DC,cAAc,EAAE,8JAA8J;EAC9KC,mBAAmB,EAAE,8CAA8C;EACnEC,YAAY,EAAE,kBAAkB;EAChCC,aAAa,EAAE,6BAA6B;EAC5CC,cAAc,EAAE,qBAAqB;EACrCC,YAAY,EAAE,qBAAqB;EACnCC,0BAA0B,EAAE,iIAAiI;EAC7JC,gBAAgB,EAAE,mCAAmC;EACrDC,UAAU,EAAE,mBAAmB;EAC/BC,aAAa,EAAE,8BAA8B;EAC7CC,mBAAmB,EAAE,0KAA0K;EAC/LC,kBAAkB,EAAE,0CAA0C;EAC9DC,yBAAyB,EAAE,yHAAyH;EACpJC,gBAAgB,EAAE,iCAAiC;EACnDC,cAAc,EAAE,sCAAsC;EACtDC,WAAW,EAAE,oBAAoB;EACjCC,kBAAkB,EAAE,kKAAkK;EACtLC,qBAAqB,EAAE,oDAAoD;EAC3EC,qBAAqB,EAAE,wCAAwC;EAC/DC,qBAAqB,EAAE,+EAA+E;EACtGC,mBAAmB,EAAE,uDAAuD;EAC5EC,YAAY,EAAE,mHAAmH;EACjIC,SAAS,EAAE,kCAAkC;EAC7CC,cAAc,EAAE,mCAAmC;EACnDC,YAAY,EAAE,WAAW;EACzBC,sBAAsB,EAAE,6IAA6I;EACrKC,sBAAsB,EAAE,0DAA0D;EAClFC,uBAAuB,EAAE,yDAAyD;EAClFC,cAAc,EAAE,uDAAuD;EACvEC,sBAAsB,EAAE,8DAA8D;EACtFC,oBAAoB,EAAE,uDAAuD;EAC7EC,uBAAuB,EAAE,+IAA+I;EACxKC,aAAa,EAAE,6CAA6C;EAC5DC,eAAe,EAAE,uBAAuB;EACxCC,gBAAgB,EAAE,qEAAqE;EACvFC,gBAAgB,EAAE,uKAAuK;EACzLC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,cAAc;EAC3BC,eAAe,EAAE,WAAW;EAC5BC,WAAW,EAAE,cAAc;EAC3BC,mBAAmB,EAAE,uGAAuG;EAC5HC,cAAc,EAAE,cAAc;EAC9BC,SAAS,EAAE,eAAe;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,eAAe;EAC3BC,UAAU,EAAE,aAAa;EACzBC,mBAAmB,EAAE,uEAAuE;EAC5FC,cAAc,EAAE,4BAA4B;EAC5CC,qBAAqB,EAAE,8EAA8E;EACrGC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE,qEAAqE;EACnFC,YAAY,EAAE,cAAc;EAC5BC,mBAAmB,EAAE,uCAAuC;EAC5DC,eAAe,EAAE,0BAA0B;EAC3CC,eAAe,EAAE,2BAA2B;EAC5CC,iBAAiB,EAAE,uDAAuD;EAC1EC,oBAAoB,EAAE,wMAAwM;EAC9NC,aAAa,EAAE,8CAA8C;EAC7DC,aAAa,EAAE,iEAAiE;EAChFC,yBAAyB,EAAE,oCAAoC;EAC/DC,uBAAuB,EAAE,yEAAyE;EAClGC,oBAAoB,EAAE,qGAAqG;EAC3HC,IAAI,EAAE,MAAM;EACZC,sBAAsB,EAAE,0BAA0B;EAClDC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,oBAAoB;EAC/BC,gBAAgB,EAAE,mFAAmF;EACrGC,QAAQ,EAAE;IACNC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE;EACX,CAAC;EACDC,eAAe,EAAE,cAAc;EAC/BC,iBAAiB,EAAE,0BAA0B;EAC7CC,eAAe,EAAE,yGAAyG;EAC1HC,kBAAkB,EAAE,6JAA6J;EACjLC,UAAU,EAAE,wBAAwB;EACpCC,iBAAiB,EAAE,6BAA6B;EAChDC,iBAAiB,EAAE,gMAAgM;EACnNC,iBAAiB,EAAE,4CAA4C;EAC/DC,aAAa,EAAE,6BAA6B;EAC5CC,WAAW,EAAE,sBAAsB;EACnCC,aAAa,EAAE,UAAU;EACzBC,QAAQ,EAAE,cAAc;EACxBC,cAAc,EAAE,wIAAwI;EACxJC,oBAAoB,EAAE,qCAAqC;EAC3DC,eAAe,EAAE;IACbzG,MAAM,EAAE,oCAAoC;IAC5C0G,MAAM,EAAE,2BAA2B;IACnCC,OAAO,EAAE,oCAAoC;IAC7CC,WAAW,EAAE,6HAA6H;IAC1IC,gBAAgB,EAAE,0GAA0G;IAC5HC,SAAS,EAAE,2KAA2K;IACtLC,YAAY,EAAE,wFAAwF;IACtGC,WAAW,EAAE,mHAAmH;IAChIC,aAAa,EAAE;EACnB,CAAC;EACDC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,iBAAiB;EAC5BC,aAAa,EAAE;IACXC,SAAS,EAAE,wIAAwI;IACnJC,MAAM,EAAE,gCAAgC;IACxCC,YAAY,EAAE,gNAAgN;IAC9NC,WAAW,EAAE,mFAAmF;IAChGC,kBAAkB,EAAE,sDAAsD;IAC1EC,kBAAkB,EAAE,wFAAwF;IAC5GC,kBAAkB,EAAE,qEAAqE;IACzFC,kBAAkB,EAAE,mEAAmE;IACvFC,gBAAgB,EAAE,4CAA4C;IAC9DC,kBAAkB,EAAE,kJAAkJ;IACtKC,kBAAkB,EAAE,gFAAgF;IACpGC,kBAAkB,EAAE,4GAA4G;IAChIC,kBAAkB,EAAE,+FAA+F;IACnHC,UAAU,EAAE,wDAAwD;IACpEC,aAAa,EAAE,yDAAyD;IACxEC,SAAS,EAAE,2FAA2F;IACtGC,aAAa,EAAE,gBAAgB;IAC/BC,kBAAkB,EAAE,gDAAgD;IACpEC,eAAe,EAAE,yCAAyC;IAC1DC,gBAAgB,EAAE;EACtB,CAAC;EACDC,UAAU,EAAE,oCAAoC;EAChDC,MAAM,EAAE,yBAAyB;EACjCC,GAAG,EAAE,aAAa;EAClBC,WAAW,EAAE,iKAAiK;EAC9KC,oBAAoB,EAAE,gIAAgI;EACtJC,WAAW,EAAE,qEAAqE;EAClFC,kBAAkB,EAAE,oCAAoC;EACxDC,iBAAiB,EAAE,uBAAuB;EAC1CC,SAAS,EAAE,mBAAmB;EAC9BC,aAAa,EAAE,6JAA6J;EAC5KC,UAAU,EAAE,mBAAmB;EAC/BC,IAAI,EAAE,KAAK;EACXC,YAAY,EAAE,gDAAgD;EAC9DC,eAAe,EAAE,kGAAkG;EACnHC,YAAY,EAAE,+GAA+G;EAC7HC,kBAAkB,EAAE,sDAAsD;EAC1EC,gBAAgB,EAAE,kDAAkD;EACpEC,UAAU,EAAE,cAAc;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE,gBAAgB;EAC9BC,UAAU,EAAE,8FAA8F;EAC1GC,eAAe,EAAE,qCAAqC;EACtDC,UAAU,EAAE,kCAAkC;EAC9CC,cAAc,EAAE,0DAA0D;EAC1EC,aAAa,EAAE,kDAAkD;EACjEC,MAAM,EAAE,QAAQ;EAChBC,iBAAiB,EAAE,oCAAoC;EACvDC,mBAAmB,EAAE,0EAA0E;EAC/FC,cAAc,EAAE,yBAAyB;EACzCC,iBAAiB,EAAE,uBAAuB;EAC1CC,QAAQ,EAAE,+BAA+B;EACzCC,WAAW,EAAE,oDAAoD;EACjEC,eAAe,EAAE,6CAA6C;EAC9DC,gBAAgB,EAAE,kDAAkD;EACpEC,eAAe,EAAE,iGAAiG;EAClHC,uBAAuB,EAAE,mJAAmJ;EAC5KC,6BAA6B,EAAE,8DAA8D;EAC7FC,kBAAkB,EAAE,kFAAkF;EACtGC,SAAS,EAAE,kBAAkB;EAC7BC,oBAAoB,EAAE,kDAAkD;EACxEC,WAAW,EAAE,uCAAuC;EACpDC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,kEAAkE;EAC/EC,kBAAkB,EAAE,iUAAiU;EACrVC,OAAO,EAAE,cAAc;EACvBC,eAAe,EAAE,4BAA4B;EAC7CC,iBAAiB,EAAE,+SAA+S;EAClUC,eAAe,EAAE,kBAAkB;EACnCC,wBAAwB,EAAE,wIAAwI;EAClKC,WAAW,EAAE,kLAAkL;EAC/LC,iBAAiB,EAAE,6HAA6H;EAChJC,UAAU,EAAE,+BAA+B;EAC3CC,qBAAqB,EAAE,mCAAmC;EAC1DC,qBAAqB,EAAE,yDAAyD;EAChFC,yBAAyB,EAAE,wDAAwD;EACnFC,wBAAwB,EAAE,0CAA0C;EACpEC,sBAAsB,EAAE,qZAAqZ;EAC7aC,aAAa,EAAE,0MAA0M;EACzNC,YAAY,EAAE,2DAA2D;EACzEC,aAAa,EAAE,kEAAkE;EACjFC,aAAa,EAAE,wBAAwB;EACvCC,GAAG,EAAE,UAAU;EACfC,OAAO,EAAE,0BAA0B;EACnCC,oBAAoB,EAAE,sCAAsC;EAC5DC,eAAe,EAAE,EAAE;EAAE;EACrBC,YAAY,EAAE,0BAA0B;EACxCC,kBAAkB,EAAE,wBAAwB;EAC5CC,cAAc,EAAE,qCAAqC;EACrDC,UAAU,EAAE,8BAA8B;EAC1CC,gBAAgB,EAAE,qCAAqC;EACvDC,gBAAgB,EAAE,yDAAyD;EAC3EC,aAAa,EAAE,6LAA6L;EAC5MC,gBAAgB,EAAE,oCAAoC;EACtDC,aAAa,EAAE,8BAA8B;EAC7CC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,oBAAoB;EAChCC,cAAc,EAAE,wBAAwB;EACxCC,eAAe,EAAE,mDAAmD;EACpEC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE,SAAS;EAChBC,mBAAmB,EAAE,uEAAuE;EAC5FC,qBAAqB,EAAE,2CAA2C;EAClEC,sBAAsB,EAAE,kDAAkD;EAC1EC,kBAAkB,EAAE,sDAAsD;EAC1EC,mBAAmB,EAAE,8DAA8D;EACnFC,kBAAkB,EAAE,iCAAiC;EACrDC,iBAAiB,EAAE,oGAAoG;EACvHC,uBAAuB,EAAE,qFAAqF;EAC9GC,cAAc,EAAE,0BAA0B;EAC1CC,YAAY,EAAE,mBAAmB;EACjCC,iBAAiB,EAAE,iCAAiC;EACpDC,gBAAgB,EAAE,oCAAoC;EACtDC,iBAAiB,EAAE,6FAA6F;EAChHC,gBAAgB,EAAE,qCAAqC;EACvDC,qBAAqB,EAAE,sCAAsC;EAC7DC,eAAe,EAAE,2CAA2C;EAC5DC,oBAAoB,EAAE,2BAA2B;EACjDC,kBAAkB,EAAE,iCAAiC;EACrDC,gBAAgB,EAAE,gDAAgD;EAClEC,oBAAoB,EAAE,4EAA4E;EAClGC,uBAAuB,EAAE,2KAA2K;EACpMC,YAAY,EAAE;IACVC,IAAI,EAAE,8CAA8C;IACpDC,IAAI,EAAE,6DAA6D;IACnEC,IAAI,EAAE,qFAAqF;IAC3FC,IAAI,EAAE;EACV,CAAC;EACDC,eAAe,EAAE,kCAAkC;EACnDC,uBAAuB,EAAE,2GAA2G;EACpIC,gBAAgB,EAAE,oBAAoB;EACtCC,eAAe,EAAE,kBAAkB;EACnCC,kBAAkB,EAAE,gDAAgD;EACpEC,kBAAkB,EAAE,gCAAgC;EACpDC,mBAAmB,EAAE,wEAAwE;EAC7FC,gBAAgB,EAAE,8CAA8C;EAChEC,oBAAoB,EAAE,uKAAuK;EAC7LC,kBAAkB,EAAE,gDAAgD;EACpEC,mBAAmB,EAAE,gEAAgE;EACrFC,UAAU,EAAE,kBAAkB;EAC9BC,kBAAkB,EAAE,+DAA+D;EACnFC,IAAI,EAAE,UAAU;EAChBC,mBAAmB,EAAE;IACjBC,KAAK,EAAE,kCAAkC;IACzCC,KAAK,EAAE,+BAA+B;IACtCC,KAAK,EAAE,4BAA4B;IACnCC,KAAK,EAAE,6BAA6B;IACpCC,KAAK,EAAE,0DAA0D;IACjEC,KAAK,EAAE;EACX,CAAC;EACDC,YAAY,EAAE,uBAAuB;EACrCC,cAAc,EAAE;IACZP,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,+BAA+B;IACtCC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,wFAAwF;IAC/FC,KAAK,EAAE,+CAA+C;IACtDG,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,+BAA+B;IACtCC,KAAK,EAAE;EACX,CAAC;EACDC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,oBAAoB;EACjCC,kBAAkB,EAAE,kCAAkC;EACtDC,UAAU,EAAE,0BAA0B;EACtCC,kBAAkB,EAAE,+FAA+F;EACnHC,mBAAmB,EAAE,6FAA6F;EAClHC,aAAa,EAAE,8CAA8C;EAC7DC,eAAe,EAAE,wEAAwE;EACzFC,UAAU,EAAE,qBAAqB;EACjCC,WAAW,EAAE,cAAc;EAC3BC,cAAc,EAAE,wJAAwJ;EACxKC,yBAAyB,EAAE,qDAAqD;EAChFC,mBAAmB,EAAE,iDAAiD;EACtEC,eAAe,EAAE,6BAA6B;EAC9CC,kBAAkB,EAAE,4CAA4C;EAChEC,wBAAwB,EAAE,qCAAqC;EAC/DC,mBAAmB,EAAE,8CAA8C;EACnEC,sBAAsB,EAAE,0CAA0C;EAClEC,iBAAiB,EAAE,2CAA2C;EAC9DC,oBAAoB,EAAE,6CAA6C;EACnEC,gBAAgB,EAAE;IACdC,QAAQ,EAAE,yBAAyB;IACnCC,YAAY,EAAE,qBAAqB;IACnCC,OAAO,EAAE,eAAe;IACxBhS,OAAO,EAAE,iBAAiB;IAC1BoB,UAAU,EAAE,oBAAoB;IAChC6Q,WAAW,EAAE,yBAAyB;IACtCC,gBAAgB,EAAE,iGAAiG;IACnHC,WAAW,EAAE,+BAA+B;IAC5CC,YAAY,EAAE,kBAAkB;IAChCC,eAAe,EAAE,MAAM;IACvBC,eAAe,EAAE,uCAAuC;IACxDC,sBAAsB,EAAE,yCAAyC;IACjEC,WAAW,EAAE,oBAAoB;IACjCC,mBAAmB,EAAE;EACzB,CAAC;EACDC,eAAe,EAAE,sCAAsC;EACvDC,gBAAgB,EAAE,8EAA8E;EAChGC,mBAAmB,EAAE,yEAAyE;EAC9FC,mBAAmB,EAAE,4EAA4E;EACjGC,eAAe,EAAE,iCAAiC;EAClDC,qBAAqB,EAAE,oCAAoC;EAC3DC,cAAc,EAAE,iCAAiC;EACjDC,QAAQ,EAAE,kBAAkB;EAC5BC,cAAc,EAAE,4BAA4B;EAC5CC,UAAU,EAAE,oCAAoC;EAChDC,SAAS,EAAE,eAAe;EAC1BC,UAAU,EAAE;AAEhB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}