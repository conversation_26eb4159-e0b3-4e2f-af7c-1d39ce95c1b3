{"ast": null, "code": "// 语言为英语时的文案，暂只在wap打开短链接时生效\nimport utils from './module/utils/utils-en.js';\nimport mixin from './module/mixins/mixins-en.js';\nimport components from './module/components/components-en.js';\nimport console from './module/console/console-en.js';\nimport userCentral from './module/usercentral/usercentral-en.js';\nimport docList from './module/docList/docList-en.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-en.js';\nimport entAuth from './module/entAuth/entAuth-en.js';\nimport docTranslation from './module/docTranslation/docTranslation-en.js';\nimport consts from '@/lang/module/consts/en';\nexport default {\n  ...utils,\n  ...mixin,\n  ...components,\n  ...docTranslation,\n  footerAd: {\n    title: 'Page jump prompt',\n    content1: 'You are going to visit a third-party promotion page',\n    content2: 'Do you want to continue?',\n    bankContent: '即将进入宁波银行\"容易贷\"企业贷款介绍页面',\n    bankTip1: '让宁波银行主动给我打电话',\n    bankTip2: '向我发送一条短信，介绍如何办理',\n    bankFooter: '加宁波银行专属客服，一对一服务我',\n    cancel: 'Cancel',\n    continue: 'Continue'\n  },\n  commonFooter: {\n    record: 'ICP main body record number: Zhejiang ICP No. ********',\n    hubbleRecordId: '网信算备：330106973391501230011',\n    openPlatform: 'Open platform',\n    aboutBestSign: 'About BestSign',\n    contact: 'Contact us',\n    recruitment: 'Recruitment',\n    help: 'Help Center',\n    copyright: 'Copyright',\n    company: 'HangZhou BestSign Ltd.',\n    ssqLogo: 'BestSign bottom bar logo',\n    provideTip: 'E-signing service is provided by',\n    ssq: ' BestSign',\n    provide: '',\n    signHotline: 'Service hotline',\n    langSwitch: 'Language'\n  },\n  login: {\n    pswLogin: 'Password Login',\n    usePswLogin: 'Log in with password',\n    verifyLogin: 'Verification Code',\n    useVerifyLogin: 'Log in with verification code',\n    scanLogin: 'Scan Code Login',\n    scanFailure: 'QR code has expired, please retry',\n    scanSuccess: 'Scan successfully',\n    scanLoginTip: 'Please use the app on the APP to scan the login',\n    appLoginTip: 'Please click login in the app BestSign',\n    downloadApp: 'Download BestSign APP',\n    forgetPsw: 'Forget?',\n    login: 'Sign in',\n    noAccount: 'No account?',\n    registerNow: 'Sign up now',\n    accountPlaceholder: 'Telephone or Email',\n    passwordPlaceholder: 'Password',\n    pictureVer: 'Please fill in the content in the picture',\n    verifyCodePlaceholder: 'Please input 6-dight code',\n    getVerifyCode: 'Send',\n    noRegister: 'Not registered yet',\n    or: 'or',\n    errAccountOrPwdTip: 'The password you entered does not match the account number?',\n    errAccountOrPwdTip2: 'The password you entered does not match the account',\n    errEmailOrTel: 'Please input correct Email or telephone number!',\n    errPwd: 'Please input correct password!',\n    verCodeFormatErr: 'Verification Code Error',\n    grapVerCodeErr: 'Graphic verification code error',\n    grapVerCodeFormatErr: 'Graphic verification code format error',\n    lackAccount: 'Please input account first',\n    lackGrapCode: 'PPlease fill in the graphic verification code first.',\n    getVerCodeTip: 'Please get verification code',\n    loginView: 'Login and View Contract',\n    regView: 'Register and View Contract',\n    takeViewBtn: 'Login and sign',\n    resendCode: 'Reacquire',\n    regTip: 'After filling in the correct verification code, BestSign will  be to create an account for you',\n    haveRead: 'I have read and agreed',\n    bestsignAgreement: 'BestSign Service Agreement',\n    and: 'and',\n    digitalCertificateAgreement: 'Digital certificate usage agreement',\n    privacyPolicy: 'Privacy Policy',\n    sendSuc: 'Send successfully',\n    lackVerCode: 'Please Enter the Verification Code First',\n    lackPsw: 'Please enter your password first',\n    notMatch: 'The password and account entered do not match',\n    cookieTip: 'Unable to read and write cookies, please check if there is no trace/incognito mode or other cookies disabled operation',\n    wrongLink: 'Illegal link',\n    footerTips: 'Electronic sign-up service provided by <span>BestSign</span>',\n    bestSign: 'BestSign',\n    bestSignDescription: 'Electronic contracting industry leader',\n    /** 忘记密码 /forgotPassword start */\n    forgetPswStep: 'Verify registered account | Reset password',\n    pictureVerCodeInput: 'Graphic verification code | Please fill in the contents of the picture',\n    accountInput: 'Account | Please enter your account',\n    smsCodeInput: 'Verification code | Get verification code',\n    haveRegistereLoginNow: 'Have registered， |  login now',\n    nextStep: 'Next | Submit',\n    setNewPasswordInput: 'Set new password | 6-18-digit number or letter',\n    passwordResetSucceeded: ' Password reset succeeded',\n    /** 忘记密码 /forgotPassword end */\n    accountNotRegistered: 'Account not registered',\n    loginAndDownload: 'login and download contract',\n    registerAndDownload: 'register and download contract',\n    inputPhone: 'please input phone number',\n    readContract: 'read contract',\n    errorPhone: 'Mobile phone format error',\n    companyCert: 'Conducting corporate certification',\n    regAndCompanyCert: 'Register and conduct corporate certification'\n  },\n  ...sign,\n  handwrite: {\n    title: 'Directly on the screen',\n    picSubmitTip: 'Signature image submitted successfully.',\n    settingDefault: 'Set as default',\n    replaceAllSignature: 'Used for all signatures',\n    replaceAllSeal: 'For all seals',\n    canUseSeal: 'My Seals',\n    applyForSeal: 'Apply for using the seals',\n    moreTip: 'Your handwritten signature will be saved as your default signature, used only for contract signing. Management path: [User Center -> Signature Management]',\n    uploadPic: 'Upload image',\n    use: 'Use',\n    clickExtend: 'Click right arrow to extend area',\n    rewrite: 'Rewrite',\n    upload: 'Upload signature image',\n    uploadTip1: 'Tip: When uploading a signature image, please make sure the signature fills the entire image',\n    uploadTip2: 'Please use dark-colored or pure black text for your signature.',\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    upgradeBrowser: 'The browser does not support handwriting, please upgrade your browser.',\n    submitTip: 'Handwritten signature submitted successfully',\n    needWrite: 'Please write your name first',\n    needRewrite: \"Can't identify the signature, please try rewrite\",\n    title2: 'Hand drawn your signature',\n    QRCode: 'By scanning the QR',\n    ok: 'OK',\n    clearTips: 'Please write a clearly identifiable signature',\n    isBlank: 'The canvas is empty, please hand-paint the signature before submitting!',\n    success: 'Handwritten signature submitted successfully',\n    signNotMatch: 'Please write your signature in block letters and keep it consistent with your real name ID information.',\n    signNotMatchExact: 'The {numList} word does not match, please rewrite',\n    msg: {\n      successToUser: 'The new signature has taken effect, please go to the Web User Center - Signature Management',\n      successToSign: 'The new signature has taken effect, please check the contract signing page',\n      cantGet: 'Can\\'t get a signature, try using a different browser!'\n    }\n  },\n  common: {\n    aboutBestSign: 'About BestSign',\n    contact: 'Contact us',\n    recruitment: 'Recruitment',\n    copyright: 'Copyright',\n    advice: 'Advice',\n    notEmpty: 'Can not be empty',\n    enter6to18n: 'Please enter 6-18 digits, letters',\n    ssqDes: 'Leader of electronic signing cloud platform',\n    openPlatform: 'Open platform',\n    company: 'HangZhou BestSign Ltd.',\n    help: 'Help Center',\n    errEmailOrTel: 'Please input correct Email or telephone number!',\n    verCodeFormatErr: 'Verification Code Error',\n    signPwdType: 'Please enter 6 digits',\n    enterActualEntName: 'Please fill in the real business name',\n    enterCorrectName: 'Please enter the correct business name',\n    enterCorrectPhoneNum: 'Please enter the correct cell phone number',\n    enterCorrectEmail: 'Please enter the correct e-mail',\n    imgCodeErr: 'Verification code error',\n    enterCorrectIdNum: 'Please enter the correct ID number',\n    enterCorrectFormat: 'Please enter the correct format',\n    enterCorrectDateFormat: 'Please enter the correct date format'\n  },\n  entAuth: {\n    ...entAuth,\n    entCertification: 'Enterprise real name certification',\n    subBaseInfo: 'Submit basic information',\n    corDocuments: 'Corporate documents',\n    license: 'License',\n    upload: 'Click to upload',\n    uploadLimit: 'Images are only available in jpeg, jpg, png formats and no larger than 10M',\n    hi: 'Hi',\n    exit: 'Exit',\n    help: 'Help',\n    hotline: 'Service hotline',\n    acceptProtectingMethod: 'I accept the method of protecting personally identifiable information I submit',\n    comfirmSubmit: 'Confirm submission',\n    cerficated: 'Certification completed',\n    entName: 'Enterprise name',\n    serialNumber: 'Integer serial number',\n    validity: 'Validity',\n    nationalNo: 'National Registration No.',\n    corporationName: 'Name of legal representative',\n    city: 'City',\n    entCertificate: 'Enterprise Real Name Certificate',\n    certificationAuthority: 'Certification Authority',\n    bestsignPlatform: 'BestSign electronic signing cloud platform',\n    notIssued: 'Not issued',\n    date: '{year}-{month}-{day}',\n    congratulations: 'Congratulations on your successful completion of the enterprise real name certification',\n    continue: 'Continue',\n    rejectMessage: 'For the following reasons, the data audit did not pass, please check',\n    recertification: 'Recertification',\n    waitMessage: 'Customer service will be reviewed within one business day, please be patient'\n  },\n  personalAuth: {\n    info: 'info',\n    submitPicError: '请上传照片后再使用'\n  },\n  home: {\n    ...home,\n    home: 'Home',\n    contractDrafting: 'Contract drafting',\n    contractManagement: 'Contract',\n    userCenter: 'Admin',\n    service: 'Service',\n    enterpriseConsole: 'Enterprise console',\n    groupConsole: 'Group console',\n    startSigning: 'Start signing',\n    contractType: 'Ordinary contract| Template contract',\n    sendContract: 'Start now',\n    shortcuts: 'shortcuts | No shortcuts for any documents',\n    setting: 'Set up immediately | Create more shortcuts',\n    signNum: 'Monthly summary of signatures and deliveries',\n    contractNum: 'contracts sent | contracts signed',\n    contractInFormation: 'You have not sent or signed any contracts this month',\n    type: 'company | person ',\n    basicInformation: 'basic information',\n    more: 'more',\n    certified: 'certified | uncertified',\n    account: 'Account',\n    time: 'Time of creation |Time of registration',\n    day: 'day | month',\n    sendContractNum: 'deliveries | signatures',\n    num: '',\n    realName: 'create a business account with real name now | create an individual account with real name now',\n    update: 'Product update',\n    mark: 'Would you like to recommend BestSign to your friends and colleagues? Please rate your choice from 0 to 10.',\n    countDes: {\n      1: 'Available: for the enterprise contract',\n      2: '',\n      3: 'for the private contract',\n      4: ''\n    },\n    chargeNow: 'Charge now',\n    myRechargeOrder: 'My recharge order',\n    statusTip: {\n      1: 'Need me to operate',\n      2: 'Need  others to sign',\n      3: 'Signing is about to close',\n      4: 'Signing complete'\n    },\n    useTemplate: 'Use template',\n    useLocalFile: 'Upload local file'\n  },\n  docDetail: {\n    canNotOperateTip: 'Unable to {operate} contract',\n    shareSignLink: 'share the signing link',\n    faceSign: 'signing with face scan',\n    faceFirstVerifyCodeSecond: 'Priority sign with facial verification, alternate sign with SMS code verification',\n    contractRecipient: 'contract receiver',\n    personalOperateLog: 'individual contract operation log',\n    recordDialog: {\n      date: 'date',\n      user: 'user',\n      operate: 'operation',\n      view: 'view',\n      download: 'download'\n    },\n    remarks: 'notes',\n    operateRecords: 'record of operation',\n    borrowingRecords: 'borrowing records',\n    currentHolder: 'current holder',\n    currentEnterprise: 'company under the current account',\n    companyInterOperationLog: 'internal operation log of the company',\n    receiverMap: {\n      sender: 'contract sender',\n      signer: 'contract receiver',\n      ccUser: 'contract copied to'\n    },\n    downloadCode: 'contract download code',\n    noTagToAddHint: 'No labels yet; please go to business control panel to add them',\n    requireFieldNotAllowEmpty: 'required fields cannot be empty',\n    modifySuccess: 'modification successful',\n    uncategorized: 'unclassified',\n    notAllowModifyContractType: 'The contract type is not allowed to be modified for the contract under {type}',\n    setTag: 'set labels',\n    contractTag: 'contract labels',\n    plsInput: 'please put in',\n    plsInputCompanyInternalNum: 'please enter the internal business number',\n    companyInternalNum: 'Internal business number',\n    none: 'none',\n    plsSelect: 'Please select',\n    modify: 'modify',\n    contractDetailInfo: 'contract details',\n    slideContentTip: {\n      signNotice: 'signing instructions',\n      contractAncillaryInformation: 'attachments to the contract',\n      content: 'content',\n      document: 'document'\n    },\n    downloadDepositConfirmTip: {\n      title: 'The signing proof page you downloaded is a non-sensitive version, with  private information hidden and not applicable for court proceedings. If you need to use it for court proceedings, please contact us for the full version.',\n      hint: 'tips',\n      confrim: 'continue to download',\n      cancel: 'cancel'\n    },\n    downloadTip: {\n      title: 'As the contract is not yet completed, you are downloading a preview file of the contract that is not yet in effect',\n      hint: 'tips',\n      confirm: 'confirm',\n      cancel: 'cancel'\n    },\n    transferSuccessGoManagePage: 'The transfer is successful and will return to the contract management page',\n    claimSign: 'retrieve and sign',\n    downloadDepositPageTip: 'download signing proof page (non-sensitive version)',\n    resend: 'resend',\n    proxySign: 'entrusted signing',\n    notPassed: 'rejected',\n    approving: 'under review',\n    signning: 'signing in progress',\n    notarized: 'notarized',\n    currentFolder: 'current folder',\n    archive: 'contract filed',\n    deadlineForSigning: 'deadline for signing',\n    endFinishTime: 'signing completed/completion date',\n    contractImportTime: 'contract import time',\n    contractSendTime: 'contract delivery time',\n    back: 'back',\n    contractInfo: 'Contract information',\n    basicInfo: 'Basic information',\n    contractNum: 'Contract No.',\n    sender: 'Sender',\n    personAccount: 'Personal account',\n    entAccount: 'Enterprise account',\n    operator: 'Operator',\n    signStartTime: 'Start signing time',\n    signDeadline: 'Signing deadline',\n    contractExpireDate: 'Contract expiration date',\n    // none: 'None',\n    edit: 'Modify',\n    settings: 'Set up',\n    from: 'Source',\n    folder: 'Folder',\n    contractType: 'Contract type',\n    reason: 'Reason',\n    sign: 'Sign',\n    approval: 'approval',\n    viewAttach: 'View the attached pages',\n    downloadContract: 'Download the contract',\n    downloadAttach: 'Download the attached page',\n    print: 'Print',\n    certificatedTooltip: 'The contract and related evidence have been documented in the judicial chain of the Hangzhou Internet Court',\n    needMeSign: 'Need me to sign',\n    needMeApproval: 'Need my approval',\n    inApproval: 'Sub-judice…',\n    needOthersSign: 'Need others to sign',\n    signComplete: 'Signing complete',\n    signOverdue: 'Overdue signing',\n    rejected: 'Rejected',\n    revoked: 'Revoked',\n    contractCompleteTime: 'Signing complete time',\n    contractEndTime: 'Signing end time',\n    reject: 'Reject',\n    revoke: 'Revoke',\n    download: 'Download',\n    viewSignOrders: 'View the order of signing',\n    viewApprovalProcess: 'View approval process',\n    completed: 'Signing completed',\n    cc: 'Cc',\n    ccer: 'Copy party',\n    signer: 'Signer',\n    signSubject: 'Sign-up subject',\n    signSubjectTooltip: 'The signing subject filled in by the sender is',\n    user: 'User',\n    IDNumber: 'ID number',\n    state: 'State',\n    time: 'Time',\n    notice: 'Remind',\n    detail: 'Details',\n    RealNameCertificationRequired: 'Real-name certification required',\n    RealNameCertificationNotRequired: 'No real name certification required',\n    MustHandwrittenSignature: 'Must sign with handwritten signature',\n    handWritingRecognition: 'Turn on handwriting recognition',\n    privateMessage: 'Message',\n    attachment: 'Attachment',\n    rejectReason: 'Reason',\n    notSigned: 'Not signed',\n    notViewed: 'Not viewed',\n    viewed: 'Viewed',\n    signed: 'Signed',\n    viewedNotSigned: 'Read not signed',\n    notApproval: 'Unapproved',\n    remindSucceed: 'Reminder message sent',\n    reviewDetails: 'Approval details',\n    close: 'Shut Down',\n    entInnerOperateDetail: 'Internal operation details',\n    approve: 'Agree',\n    disapprove: 'Reject',\n    applySeal: 'Application for printing',\n    applied: 'Already applied',\n    apply: 'Application',\n    toOtherSign: 'Transfer to someone else to sign',\n    handOver: 'Transfer',\n    approvalOpinions: 'Approval comments',\n    useSeal: 'Print',\n    signature: 'Signature',\n    use: 'Use',\n    date: 'Date',\n    fill: 'Fil in',\n    times: 'Secondary',\n    place: 'Place',\n    contractDetail: 'Contract details',\n    viewMore: 'View more',\n    collapse: 'Fold',\n    signLink: 'Signing a link',\n    saveQRCode: 'Save the QR code or copy the link and share it with the signatory',\n    signQRCode: 'Sign the link QR code',\n    copy: 'Copy',\n    copySucc: 'Successful copy',\n    copyFail: 'Replication copy',\n    certified: 'Certified',\n    unCertified: 'Uncertified',\n    claimed: 'Claimed'\n  },\n  uploadFile: {\n    thumbnails: 'Thumbnail',\n    isUploading: 'Uploading',\n    move: 'Move',\n    delete: 'Delete',\n    replace: 'Replace',\n    tip: 'Tip',\n    understand: 'Get it',\n    totalPages: '{page} in total',\n    uploadFile: 'Upload local file',\n    matchErr: 'The server has a small gap, please try again later.',\n    inUploadingDeleteErr: 'Please delete after uploading',\n    timeOutErr: 'Request timed out',\n    imgUnqualified: 'Image format does not meet the requirements',\n    imgBiggerThan20M: 'Image size cannot exceed 20MB!',\n    error: 'Error',\n    hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。'\n  },\n  contractInfo: {\n    internalNumber: 'Internal business number',\n    contractName: 'Contract name',\n    contractNameTooltip: 'Contract name please do not contain special characters and is no longer than 100 words long',\n    contractType: 'Contract type',\n    toSelect: 'Please choose',\n    contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',\n    signDeadLine: 'Signing deadline',\n    signDeadLineTooltip: 'If the contract is not signed before this date, it cannot be continued',\n    selectDate: 'Select date and time',\n    contractExpireDate: 'Contract expiration date',\n    expireDateTooltip: 'Expiration time in the contents of the contract for your subsequent contract management',\n    necessary: 'Necessary',\n    notNecessary: 'Optional',\n    dateTips: 'The contract expiration date has been automatically identified for you, please confirm',\n    contractTitleErr: 'Contract name please do not contain special characters',\n    contractTitleLengthErr: 'Please do not exceed 100 words in the length of the contract name.'\n  },\n  userCentral: userCentral,\n  template: {\n    templateList: {\n      linkBoxTip: 'Associated Cabinet ID：'\n    },\n    dynamicTemplateUpdate: {\n      title: 'New function of dynamic template is online',\n      newVersionDesc: 'The new function supports header and footer display and keeps  document page layout to the maximum degree.',\n      updateTip: 'The previous dynamic template feature is not synchronized and compatible. Manual upgrading is required. If dynamic templates created before January 26th are edited, contracts will not be saved nor sent. Contracts can still be sent before March 1, 2021 if templates have not been edited. Upgrading is recommended as soon as possible. Non-dynamic templates are not affected.',\n      connectUs: 'If you have any questions, please contact via the hotline ************ or contact online customer service.'\n    },\n    sendCode: {\n      tip: 'The current template settings do not meet the conditions for generating the send-code. Check whether the following requirements are met:',\n      fail: {\n        1: 'No blank documents are included',\n        2: 'The contracting party has only one variable party (including signature and copy), and the variable party must be the first operator; The signatory must have a signature stamping position',\n        3: ' The fixed account of the contracting party shall not be empty',\n        4: 'Does not trigger pre-shipment approval',\n        5: 'The contents that the sender has to fill are not empty(Include the description field and the contract content field)',\n        6: 'Not template combination'\n      }\n    },\n    sendCodeGuide: {\n      title: '发送码高级功能说明',\n      info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n      tip1: {\n        main: '1. 上上签',\n        sub: '',\n        line1: '向上上签申请开通档案+、合同预审、智能预审',\n        line2: '开通后可以到对应的菜单中操作使用'\n      },\n      tip2: {\n        main: '2. 档案柜管理员',\n        sub: '创建档案柜、配置智能预审',\n        line1: '',\n        line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。'\n      },\n      tip3: {\n        main: '3. 签约方',\n        sub: '扫码填资料、获取合同文件',\n        line1: '',\n        line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署'\n      },\n      tip4: {\n        main: '4. 档案柜管理员',\n        sub: '',\n        line1: '查看签约的相对方、发送的合同情况',\n        line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等'\n      }\n    }\n  },\n  style: {\n    signature: {\n      text: {\n        x: '0',\n        fontSize: '18'\n      }\n    }\n  },\n  resetPwd: {\n    title: 'Safety tips！',\n    notice: 'The security factor of your password is low and there is a security risk. Please reset your password',\n    oldLabel: 'Original password',\n    oldPlaceholder: 'Please enter the original password',\n    newLabel: 'New password',\n    newPlaceholder: '6-18 digits and uppercase and lowercase letters, support special characters',\n    submit: 'Submit',\n    errorMsg: 'Password should contain 6-18 digits and upper - and lowercase letters, please reset',\n    oldRule: 'The original password cannot be empty',\n    newRule: 'The new password cannot be empty',\n    success: 'Success!'\n  },\n  personAuthIntercept: {\n    title: '邀请您以',\n    name: '姓名：',\n    id: '身份证号：',\n    descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    desMore: '根据发起方要求，您还需要补充',\n    descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    tips: '实名认证通过后，可查看并签署合同。',\n    goOn: '是我本人，开始认证',\n    goMore: '去补充认证',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '当前账号是否是您的常用手机号？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n    confirmReject: '是的，我要驳回实名'\n  },\n  authIntercept: {\n    title: '要求您以：',\n    name: '姓名为：',\n    id: '身份证号为：',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    descNoSame1: '签署合同。',\n    descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n    tips: '注：身份信息完全一致才能签署合同',\n    goOn: '是我本人，开始认证',\n    goHome: '我知道了',\n    goMore: '去补充认证',\n    authTip: '进行实名认证。',\n    viewAndSign: '完成认证后即可查看和签署合同',\n    tips2: '注：企业名称完全一致才能查看和签署合同。',\n    requestOtherAnth: 'Request verification by others',\n    goAuth: '去实名认证',\n    requestSomeoneList: '请求以下人员完成实名认证：',\n    ent: '企业',\n    entName: '企业名称',\n    account: '账号',\n    accountPH: '手机或邮箱',\n    send: '发送',\n    lackEntName: '请填写企业名称',\n    errAccount: '请填写正确的邮箱或手机号',\n    successfulSent: '发送成功'\n  },\n  thirdPartApprovalDialog: {\n    title1: 'Pre-signature Approval',\n    title2: 'Approval Process',\n    content1: 'Signing requires approval. Please wait patiently.',\n    content2: '需由第三方平台（非上上签平台）审批合同。',\n    cancelBtnText: 'View Approval Process',\n    confirmBtnText: 'Confirm',\n    iKnow: 'I see'\n  },\n  endSignEarlyPrompt: {\n    cancel: '取消',\n    confirm: '确认',\n    signPrompt: '签署提示',\n    signTotalCountTip: '本次签署共包含{count}份合同文件',\n    signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n    hasSigned: '{count}人已签字',\n    hasNotSigned: '{count}人未签字',\n    noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。'\n  },\n  commonNomal: {\n    yesterday: 'Yesterday',\n    ssq: 'BestSign',\n    ssqPlatform: 'BestSign E-signature cloud platform',\n    ssqTestPlatform: '(For testing purposes only) BestSign E-Signature Cloud Platform',\n    pageExpiredTip: 'The page has expired, please refresh and try again',\n    pswCodeSimpleTip: 'The password must contain 6-18 digits and uppercase and lowercase letters, please reset'\n  },\n  transferAdminDialog: {\n    title: 'Identification',\n    transfer: 'Transfer',\n    confirmAdmin: 'I am the main administrator',\n    content: 'The system administrator is responsible for managing the company seal, contracts, and other personnel permissions, generally belonging to the legal representative, financial manager, legal manager, IT department manager, or business leader of the company.| Please confirm if you meet the above identity requirements. If not, it is recommended to forward it to the relevant personnel.'\n  },\n  choseBoxForReceiver: {\n    dataNeedForReceiver: 'Information to be submitted by the signing party',\n    dataFromDataBox: 'The information to be submitted by the signing party needs to be obtained through document collection from a certain file cabinet.',\n    searchTp: 'Please enter the name or code of the file cabinet.',\n    search: 'Search',\n    boxNotFound: 'The file cabinet cannot be found.',\n    cancel: 'Cancel',\n    confirm: 'Ok'\n  },\n  localCommon: {\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    toSelect: 'Please select',\n    seal: 'Stamp',\n    signature: 'Sign',\n    signDate: 'Date',\n    text: 'Text',\n    date: 'Date',\n    qrCode: 'QR Code',\n    number: 'Digital',\n    dynamicTable: 'Dynamic forms',\n    terms: 'Terms and conditions of the contract',\n    checkBox: 'Checkboxes for multiple options',\n    radioBox: 'Checkbox for single options',\n    image: 'Image',\n    confirmSeal: 'Stamp for inquiries',\n    tip: 'Tips',\n    confirmRemark: 'Remarks regarding seals that do not meet requirements',\n    optional: 'Optional',\n    require: 'Required',\n    comboBox: 'DropDown'\n  },\n  twoFactor: {\n    signTip: 'Signing Prompt',\n    settingTwoFactor: '设置二要素验证器',\n    step1: '1. 安装验证器应用',\n    step1Tip: '二要素身份验证需要您安装一下手机应用程序：',\n    step2: '2.扫描二维码',\n    step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',\n    step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',\n    step3: '3.输入6位验证码',\n    step3Tip: '请输入屏幕上显示的验证码',\n    verifyCode6: '6位验证码',\n    iosAddress: 'iOS下载地址：',\n    androidAddress: 'Android下载地址：',\n    chromeVerify: '谷歌身份验证器',\n    nextBtn: '下一步',\n    confirmSign: 'Confirm to Sign',\n    dynamicCode: '验证器动态码',\n    password: 'Encryption Code',\n    pleaseInput: 'Please input',\n    twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',\n    passwordTip: 'The sender requires encrypted signature for this document. Please complete the signing process accordingly.',\n    twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',\n    passwordTip2: 'Please contact the sender to obtain the encryption signing code. The contract will be ready for signing once you enter the code.',\n    dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。'\n  },\n  functionSupportDialog: {\n    title: 'Function introduction',\n    inputTip: 'If you have relevant use needs, please fill in your needs in the following form. BestSign will arrange professionals to contact you and provide service guidance within 24 hours.',\n    useSence: 'Application scenario',\n    useSenceTip: 'Such as HR, dealers, logistics documents, etc',\n    estimatedOnlineTime: 'Expected launch date',\n    requireContent: 'Requirements',\n    requireContentTip: 'Please describe how your company will use e-signature so that we can provide appropriate solution for you',\n    getSupport: 'Get professional service support',\n    callServiceHotline: 'Hotline：************',\n    useSenceNotEmpty: 'The usage scenario cannot be empty',\n    requrieContentNotEmpty: 'The demand content cannot be empty',\n    oneWeek: 'Within a week',\n    oneMonth: 'Within a month',\n    other: 'Others',\n    submitSuccess: 'Submitted successfully',\n    submitTrial: 'Submit for trial',\n    toTrial: 'To trial',\n    trialTip: 'After submitting a trial application, the current function will be immediately activated and available for trial. In order to better help you use the features, you can fill in more requirements in the form below. BestSign consultant will contact you to provide services.',\n    applyTrial: 'Apply for trial',\n    trialSuccTip: 'The function has been activated. Welcome to try it out',\n    goBuy: 'Buy now',\n    trialTipMap: {\n      title: 'Trial instructions',\n      tip1: '1. Instant use, valid for 7 days；',\n      tip2: '2. During the trial period, the function will not be charged；',\n      tip3: '3. Each company entity has only one trial opportunity for a function；',\n      tip4: '4. Self-service purchase is available during the probation period, and the use is uninterrupted;',\n      tip5: '5. If your trial has ended, you can scan the code and contact the BestSign professional consultant for details:'\n    },\n    contactAdminTip: 'To use, please contact your enterprise administrator {tip} to purchase and open',\n    trialEndTip: 'After the trial period, click to buy',\n    trialRemainDayTip: 'There are {day} days left in the trial period, click to purchase copies',\n    trialEnd: 'Trial function ended',\n    trialEndMap: {\n      deactivateTip: '{feature} feature has been disabled. Please clear the configuration or renew it before continuing to use it.',\n      feature1: 'Contract Collateral',\n      remove1: 'The method for clearing the configuration is: Edit the template - find the configured additional contract attachment data and delete it.',\n      feature2: 'Handwriting handwriting recognition',\n      remove2: 'The method for clearing the configuration is: Edit the template - Find the configured handwriting recognition and delete it.',\n      feature3: 'Contract decoration: riding seal+watermark',\n      remove3: 'The method to clear the configuration is: Edit Template - Find the configured contract decoration and delete it.',\n      feature4: 'Contract sending approval',\n      remove4: 'The method for clearing the configuration is: Enterprise Console - Deactivate all approval processes'\n    }\n  },\n  setSignPwdDialog: {\n    tip: \"After the setting is completed, the signing password will be first used by default. You may log in to BestSign's e-Signature platform and enter 'User Center' or log in to BestSign app and enter 'Account Management' to change the password.\",\n    saveAndReturnSign: 'Save and return to sign',\n    changeEmailVerify: 'Switch to Email Verification',\n    changePhoneVerify: 'Switch to Phone Verification'\n  },\n  contractCompare: {\n    reUpload: 'Re-upload',\n    title: 'Contract Comparison',\n    packagePurchase: 'Plan Purchase',\n    packagePurchaseTitle: '[{title}] Plan Purchase',\n    myPackage: 'My Plan',\n    packageDetail: 'Plan Details',\n    per: '次',\n    packageContent: 'Plan Includes：',\n    num: '{type}次数',\n    limitTime: 'Validity Period',\n    month: 'month',\n    payNow: 'Buy Now',\n    contactUs: 'Contact Us | Scan QR Code to Consult Professional Advisor',\n    compareInfo1: 'Usage Instructions：',\n    compareInfo2: '{index}、The available quota for {type} of purchases is accessible to all members of the enterprise. If you only require it for personal use, you may switch to a personal account by changing the login entity in the upper right corner.',\n    compareInfo3: '{index}、Usage Calculated Based on Number of Uploaded Contract {per}',\n    codePay: 'Please Scan QR Code to Pay',\n    aliPay: 'Alipay Payment',\n    wxPay: 'WeChat Payment',\n    payIno: 'Activated Features | Purchased For | Payment Amount',\n    finishPay: 'Payment Completed',\n    paySuccess: 'Purchase Successful',\n    originFile: 'Original Contract File',\n    compareFile: 'Contract File for Comparison',\n    documentSelect: 'Select File',\n    comparisonResult: 'Comparison Result',\n    history: 'History',\n    currentHistory: 'Document Records',\n    noData: 'No Data Available',\n    differences: '{num} Differences',\n    historyLog: '{num} Records',\n    uploadLimit: 'Drag & Drop Files to Compare | Supported Formats: PDF (including scanned), Word',\n    dragInfo: 'Release Mouse to Upload',\n    uploadError: 'Unsupported File Format',\n    pageNum: '第{page}页',\n    difference: 'Differences {num}',\n    download: 'Download Comparison Result',\n    comparing: 'Comparing Contracts...',\n    tip: 'Notification',\n    confirm: 'Confirm',\n    toBuy: 'Go to Purchase',\n    translate: 'Contract Translation',\n    doCompare: 'Compare',\n    doTranslate: 'Translate',\n    review: 'Contract Review',\n    doReview: 'Review',\n    reviewUploadFile: 'Drag Files to be Reviewed Here',\n    reviewUpload: 'Drag Review Reference Files Here | e.g., \"Distributor Management Policy\", \"Procurement Regulations\" | Supported Formats: PDF, Word',\n    reviewOriginFile: 'Contract Under Review',\n    reviewTargetFile: 'Review Basis',\n    reviewResult: 'Review Result',\n    uploadReviewFile: 'Upload Reference Files for Review',\n    risk: 'Risk Point {num}',\n    risks: '{num} Risk Point(s)',\n    startReview: 'Start Review',\n    reviewing: 'Reviewing Contract...',\n    noRisk: 'Review Completed - No Risks Detected',\n    allowUpload: 'You can upload company regulations (e.g., \"Procurement Management Policy\"), compliance guidelines, or departmental instructions to guide contract review. | Example: \"Party A must complete payment within 5 days after contract signing.\"',\n    notAllowUpload: 'Avoid vague or general statements as review basis. | Example: \"All contract terms must comply with applicable laws and regulations.\"',\n    resumeReview: 'Continue to Next File',\n    close: 'Close',\n    extract: 'Contract Extraction',\n    extractTitle: 'Keywords to Extract',\n    selectKeyword: 'Select Keywords from the List Below',\n    keyword: 'Keywords',\n    addKeyword: 'Add{keyword}',\n    introduce: '{keyword} Definition',\n    startExtract: 'Start Extraction',\n    extractTargetFile: 'Contract for Extraction',\n    extractKeyWord: 'Extract Keywords',\n    extracting: 'Extracting Contract...',\n    extractResult: 'Extraction Result',\n    extractUploadFile: 'Drag & Drop Files for Extraction Here',\n    needExtractKeyword: 'Select Keywords to Extract',\n    summary: 'Contract Summary',\n    keySummary: 'Keyword Summary',\n    deleteKeywordConfirm: 'Confirm Deletion of this Keyword?',\n    keywordPosition: 'Keyword Locations',\n    riskJudgement: 'Risk Assessment',\n    judgeTargetContract: 'Contract Under Assessment',\n    interpretTargetContract: 'AI-Interpreted Contracts',\n    startJudge: 'Start',\n    startInterpret: 'Start Now',\n    uploadText: 'Please upload the documents for risk assessment',\n    interpretText: 'Please upload documents for AI analysis',\n    startTips: 'Now we can start to judge the risks',\n    interpretTips: 'Now we can start to interpret the document',\n    infoExtract: 'Extract information'\n  },\n  batchImport: {\n    iKnow: 'I see'\n  },\n  templateCommon: {\n    tip: 'Tip'\n  },\n  mgapprovenote: {\n    SAQ: 'Questionnaire Survey',\n    analyze: 'Analysis',\n    annotate: 'Annotation',\n    law: 'Legal Clause Search',\n    case: 'Similar Case Search',\n    translate: 'Translate',\n    mark: 'mark',\n    tips: 'The above content is AI-generated and does not represent the position of ShangShangQian. It is for your reference only. Please do not delete or modify this mark.',\n    limit: 'Usage limit reached. If you need continued use, please fill out the form. Our customer service will contact you.',\n    confirmTxt: 'Go to fill',\n    content: 'Related content',\n    experience: 'Professional Experience',\n    datas: 'Relevant Data',\n    terms: 'Analogous Clauses',\n    original: 'Source',\n    export: 'Export',\n    preview: 'Contract Preview',\n    history: 'History'\n  },\n  sealConfirm: {\n    title: 'Confirm Electronic Seal Page',\n    header: 'Confirm Electronic Seal',\n    signerEnt: 'Contracting Company:',\n    abnormalSeal: 'Abnormal Electronic Seal:',\n    sealNormal: 'Seal Normal',\n    tip1: 'Please confirm if the electronic seal is normal and usable. If it is normal, you can click the \"Seal Normal\" button. Subsequently, when this company uses this seal again, the system will no longer send you abnormal notifications.',\n    tip2: 'If there is an issue with the seal, please promptly communicate with the signing party to replace the seal, resend the contract for signing, or reject and re-sign.'\n  },\n  ...console,\n  ...docList,\n  ...consts,\n  keyInfoExtract: {\n    operate: 'Extract Information',\n    contractType: 'Predicted Contract Types',\n    tooltips: 'Select the Key Information',\n    predictText: 'Predicting',\n    extractText: 'Extracting',\n    errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n    result: 'result:'\n  },\n  judgeRisk: {\n    title: 'AI Lawyer',\n    deepInference: 'AI Legal',\n    showAll: 'Show More',\n    tips: 'Judging',\n    dialogTitle: '“AI Lawyer” Reviews Contracts',\n    aiInterpret: 'AI Interpretation'\n  },\n  sealDistribute: {\n    requestSeal: 'Request Corporate Seal Authorization',\n    company: 'Company',\n    applicant: 'Applicant',\n    accountID: 'Account ID',\n    submissionTime: 'Submission Time',\n    status: 'Status',\n    agree: 'Approved',\n    unAgree: 'Rejected',\n    ifAgree: 'If approved,',\n    applyTime: ' applicant\\'s stamp usage period is:',\n    to: 'to',\n    placeHolderTime: 'Year-Month-Day',\n    senderCompany: 'Sender Company',\n    documentTitle: 'Document Title',\n    sealApplicationScope: 'Seal Application Scope',\n    applyforSeal: 'Apply for E-Seal',\n    reject: 'Reject',\n    approve: 'Approve'\n  },\n  sealApproval: {\n    sealRight: 'Seal Permission',\n    requestSeal: 'Request Corporate Seal Authorization',\n    allEntContract: 'Contracts from All Enterprises',\n    partEntContract: 'Contracts from Selected Enterprises: ',\n    pleaseInputRight: 'Please Enter Permission',\n    successTransfer: 'After successful handover,',\n    getRight: 'will obtain the above permissions or can directly edit and assign new signing permissions.',\n    signAllEntContract: 'Sign contracts from all enterprises',\n    sign: 'Sign',\n    sendContract: 'sent contracts',\n    sealUseTime: 'Seal Usage Period: ',\n    currentStatus: 'Current Status: ',\n    takeBackSeal: 'Recall Seal',\n    agree: 'Agree',\n    hasAgree: 'Agreed',\n    hasReject: 'Rejected',\n    hasDone: 'Completed',\n    ask: ' has assigned ',\n    giveYou: \"'s seal to you\",\n    hopeAsk: 'hopes to',\n    hopeGive: 'hand over the seal to',\n    hopeGiveYou: 'hand over the relevant seal to you',\n    noSettingTime: 'No Time Setting',\n    approvalSuccess: 'Approval Successful',\n    getSealSuccess: 'Seal Obtained Successfully'\n  },\n  workspace: {\n    create: 'Created',\n    reviewing: 'Reviewing',\n    completed: 'Completed',\n    noData: 'Empty',\n    introduce: 'Meaning of the {keyword}',\n    termsDetail: 'Details',\n    extractFormat: 'Format',\n    optional: 'Optional',\n    required: 'Required',\n    operate: 'Operation',\n    detail: 'Details',\n    delete: 'Delete',\n    agreement: {\n      uploadError: 'Only PDF, DOC, or DOCX files can be uploaded!',\n      extractionRequest: 'Extraction request has been submitted. Please check the results in the terms list later.',\n      upload: 'Upload',\n      define: 'Defination',\n      extract: 'Extraction',\n      drag: 'Drag the file here or',\n      add: 'click',\n      format: 'Supported file formats are doc,docx,pdf',\n      fileName: 'FileName',\n      status: 'Status',\n      completed: 'Completed',\n      failed: 'Failed',\n      size: 'Size',\n      terms: 'Terms',\n      success: 'The extraction has been completed, totaling {total}',\n      ongoing: 'In progress...totaling {total}',\n      tips: 'Skipping this page does not affect the results of the extraction',\n      others: 'Upload other agreements',\n      result: 'Jump to the download page of the extraction result',\n      curProgress: 'Current progress: ',\n      refresh: 'Refresh',\n      details: 'Loaded {successNum}，totaling {length}',\n      start: 'Start',\n      more: 'Add',\n      skip: 'Skip',\n      tiqu: 'Start',\n      chouqu: 'Start'\n    },\n    review: {\n      distribution: 'Distribution review',\n      Incomplete: 'Incomplete',\n      createReview: 'Create Review',\n      manageReview: 'Review Management',\n      reviewDetail: 'Review Details',\n      reviewId: 'Review ID',\n      reviewStatus: 'Review Status',\n      reviewName: 'Review Name',\n      reviewStartTime: 'Review Start Time',\n      reviewCompleteTime: 'Review Complete Time',\n      reviewDesc: 'Version：V.{reviewVersion} | ReviewId：{reviewId}',\n      distribute: 'Initiate Review',\n      drag: 'Drag the agreement to be reviewed here',\n      content: 'Content',\n      current: 'Current',\n      history: 'History',\n      page: 'Page {x}：',\n      users: 'Users：',\n      message: 'Message',\n      modify: 'Modify',\n      placeholder: 'Please use semicolons to separate multiple users',\n      submit: 'Submit',\n      reupload: 'Reupload',\n      finish: 'Finish',\n      reviewSummary: 'Review Summary',\n      initiator: 'Initiator',\n      versionSummary: 'Version Summary',\n      version: 'Version',\n      versionOrder: 'Version {version}',\n      curReviewStatus: 'Current Version’s Status',\n      curReviewVersion: 'Current Version',\n      curReviewPopulation: 'Current Version’s Review Population',\n      curReviewStartTime: 'Current Version’s Review Start Time',\n      curReviewInitiator: 'Current Version’s Review Initiator',\n      checkComments: 'Check Comments',\n      overview: 'Overview',\n      reviewer: 'Reviewer',\n      reviewResult: 'Review Result',\n      replyTime: 'Reply Time',\n      agreement: 'Agreements',\n      files: 'Files',\n      fileName: 'FileName',\n      numberOfModificationSuggestions: 'Number of modification suggestions',\n      uploadTime: 'Upload Time',\n      download: 'Download',\n      dispatch: 'Dispatch',\n      recent: 'Latest review time：',\n      replyContent: 'Reply Content',\n      advice: 'Advice',\n      noIdea: 'Have no advice',\n      origin: 'Original Content: ',\n      revised: 'Revised Content',\n      suggestion: 'Suggestion: ',\n      dateMark: '{name} written in <span style=\"color: #0988EC\">Version {version}</span> on {date}',\n      unReviewed: 'Not yet reviewed',\n      revisionFiles: 'Revision Files',\n      staffReplyAggregation: 'Overview',\n      staffReply: '{name}’s Comments',\n      tips: 'Tips',\n      tipsContent: 'If you are sure to perform this operation, this review will no longer support distribution and subsequent operations. Do you want to continue?',\n      confirm: 'Confirm',\n      cancel: 'Cancel',\n      successMessage: 'Completed',\n      PASS: 'Pass',\n      REJECT: 'Reject',\n      uploadErrorMessage: 'Currently, only DOCX format files are supported for upload.',\n      successInitiated: 'Review initiated',\n      autoDistribute: 'Intelligent Distribution',\n      requiredUsers: 'Users Requiring Review',\n      contentToReview: 'Content to Review',\n      termDetails: 'Term Details',\n      term: 'Term',\n      aiDistribute: 'AI Intelligent Distribution',\n      noData: 'No Data Available',\n      docIconAlt: 'Document Icon',\n      docxIconAlt: 'DOCX Icon',\n      pdfIconAlt: 'PDF Icon',\n      requiredUsersError: 'Please fill in the users requiring review',\n      selectContentError: 'Please select the content to review',\n      initiateReviewSuccess: 'Review Initiated',\n      syncInitiated: 'Synchronization Initiated'\n    },\n    contentTracing: {\n      title: 'Content Tracing',\n      fieldContent: 'Field Content',\n      originalResult: 'Original Result',\n      contentSource: 'Content Source',\n      page: 'Page'\n    }\n  },\n  hubblePackage: {\n    title: 'My Package',\n    details: 'Package Details',\n    remainingPages: 'Remaining Total Pages',\n    pages: 'Pages',\n    usedPages: 'Used',\n    remaining: 'Available Remaining',\n    total: 'Total',\n    expiryTime: 'Expiry Time',\n    amount: 'Quantity',\n    unitPrice: 'Each',\n    copy: 'Copy',\n    words: 'Thousand Characters'\n  },\n  workspaceIndex: {\n    title: 'Workspace',\n    package: 'Package Usage',\n    agreement: 'Agreement',\n    review: 'Review',\n    term: 'Term',\n    amount: '数目',\n    unitPrice: '单价'\n  },\n  agreement: {\n    title: 'Agreement Management',\n    exportList: 'Export Agreement List',\n    exportAllChecked: 'Excel (All Fields, Selected Agreements)',\n    exportCurrentChecked: 'Excel (Current Fields, Selected Agreements)',\n    exportAllMatched: 'Excel (All Fields, Matched Conditions)',\n    exportCurrentMatched: 'Excel (Current Fields, Matched Conditions)',\n    add: 'Add Agreement',\n    upload: 'Upload Agreement',\n    operation: 'Operation',\n    download: 'Download Agreement',\n    details: 'Details',\n    delete: 'Delete',\n    relatedTaskStatus: 'Related Extraction Task Status',\n    confirmDelete: 'Are you sure to delete the current agreement?',\n    prompt: 'Prompt',\n    booleanYes: 'Yes',\n    booleanNo: 'No',\n    defaultExportName: 'export.xlsx',\n    taskNotStarted: 'Extraction Task Not Started',\n    taskStarted: 'Extraction Task Started (Content Searching)',\n    contentSearchCompleted: 'Content Search Completed (Formatting Results)',\n    resultFormattingCompleted: 'Result Formatting Completed (Verifying Results)',\n    resultVerificationCompleted: 'Result Verification Completed'\n  },\n  filter: {\n    filter: 'Filter',\n    refreshExtraction: 'Refresh Extraction',\n    extractTerms: 'Extract Term Definitions',\n    refreshList: 'Refresh List',\n    currentCondition: 'Displaying agreements under current conditions.',\n    when: 'When',\n    selectCondition: 'Please Select Condition',\n    enterCondition: 'Please Enter Condition',\n    yes: 'Yes',\n    no: 'No',\n    addCondition: 'Add Condition',\n    reset: 'Reset',\n    confirm: 'Confirm',\n    and: 'And',\n    or: 'Or',\n    equals: 'Equals',\n    notEquals: 'Not Equals',\n    contains: 'Contains',\n    notContains: 'Does Not Contain',\n    greaterThan: 'Greater Than',\n    greaterThanOrEquals: 'Greater Than or Equals',\n    lessThan: 'Less Than',\n    lessThanOrEquals: 'Less Than or Equals',\n    emptyCondition: 'Filter condition cannot be empty'\n  },\n  fieldConfig: {\n    button: 'Field Configuration',\n    header: 'Fields to be displayed',\n    submit: 'Submit',\n    cancel: 'Cancel'\n  },\n  agreementDetail: {\n    detail: 'Agreement Details',\n    add: 'Add Agreement',\n    id: 'Agreement ID',\n    file: 'Agreement File',\n    download: 'Download Agreement',\n    replaceFile: 'Replace Agreement File',\n    uploadFile: 'Upload Agreement File',\n    relatedExtractionStatus: 'Related Extraction Task Status',\n    dataSource: 'Data Source',\n    yes: 'Yes',\n    no: 'No',\n    select: 'Please Select',\n    input: 'Please Input',\n    save: 'Save',\n    cancel: 'Cancel',\n    page: 'Page {page}',\n    addDataSource: 'Add Data Source',\n    pageNo: 'Page',\n    pageSuffix: '',\n    submit: 'Submit',\n    inputDataSource: 'Please input data source content',\n    pageFormatError: 'Page number only supports comma-separated numbers or pure numbers',\n    confirmDelete: 'Are you sure to delete the current data source?',\n    tips: 'Tips',\n    uploadSuccess: 'Upload Successful'\n  },\n  termManagement: {\n    title: 'Term Management',\n    batchDelete: 'Batch Delete',\n    import: 'Import Terms',\n    export: 'Export Terms',\n    add: '+ Add Term',\n    name: 'Term Name',\n    definition: 'Term Definition',\n    formatRequirement: 'Extraction Format Requirement',\n    dataFormat: 'Data Format',\n    operation: 'Operation',\n    edit: 'Edit',\n    delete: 'Delete',\n    detail: 'Term Details',\n    addTitle: 'Add Term',\n    namePlaceholder: 'Enter your specialized term',\n    definitionPlaceholder: 'Enter the definition of the term',\n    formatRequirementPlaceholder: 'Enter the extraction format requirement of the term',\n    dataFormatPlaceholder: 'Expected extracted term format',\n    cancel: 'Cancel',\n    confirmEdit: 'Confirm Edit',\n    importTitle: 'Import Terms',\n    uploadTemplate: 'Upload Term Template',\n    downloadTemplate: 'Download Term Template',\n    extractType: {\n      text: 'Text',\n      longText: 'Long Text',\n      date: 'Date',\n      number: 'Number',\n      boolean: 'Yes/No'\n    },\n    importSuccess: 'Import Successful',\n    deleteConfirm: 'Are you sure to delete the current term?',\n    prompt: 'Prompt',\n    nameEmptyError: 'Term name cannot be empty'\n  },\n  agent: {\n    extractTitle: 'Information Extraction',\n    riskTitle: 'AI Lawyer',\n    feedback: 'Questionnaire',\n    toMini: 'Check Details in App',\n    otherContract: 'Analyze latent risks in other contracts?',\n    others: 'Others',\n    submit: 'Submit',\n    autoExtract: 'Automatic Extraction Until Completion',\n    autoRisk: 'Auto-Analysis Process Running',\n    aiGenerated: 'AI Generated - © BestSign',\n    chooseRisk: 'Select File for Analysis',\n    chooseExtract: 'Specify Source File for Extraction',\n    analyzing: 'Analyzing',\n    advice: 'Generating Revision Suggestions',\n    options: 'Generating Options',\n    inputTips: 'Enter Precise Information',\n    chargeTip: 'Insufficient Balance - Top Up Required',\n    original: 'Original',\n    revision: 'Revision',\n    diff: 'Comparison',\n    locate: 'Locating',\n    custom: 'Please enter custom review rules',\n    content: 'Locate Original Text',\n    satisfy: 'Satisfied with the analysis results, continue to the next analysis',\n    dissatisfy: 'Not satisfied with the analysis results, re-analyze',\n    selectFunc: 'Please select the function you wish to use',\n    deepInference: 'AI Legal',\n    deepThinking: 'Deep Thinking',\n    deepThoughtCompleted: 'Deep Thinking Completed',\n    reJudge: 'Re-judge',\n    confirm: 'Confirm',\n    tipsContent: 'Proceeding will deduct from your usage credits. Continue?',\n    useLawyer: 'Use AI Lawyer',\n    interpretFinish: 'AI Analysis Complete',\n    exportPDF: 'Export PDF Report',\n    defaultExportName: 'export.pdf',\n    exporting: 'Exporting report... Please wait'\n  },\n  authorize: {\n    title: 'Terms of Use',\n    content: 'Unlock smart contracts - AI analysis boosts efficiency!Agree to start your free trial',\n    cancel: 'Not Now',\n    confirm: 'Accept & Start',\n    contract: 'View Hubble Product Terms of Use'\n  },\n  hubbleEntry: {\n    smartAdvisor: 'Smart Contract Advisor',\n    tooltips: 'This feature is not enabled. Contact BestSign e-signature advisors to purchase access.',\n    confirm: 'Got it'\n  },\n  lang: 'en'\n};", "map": {"version": 3, "names": ["utils", "mixin", "components", "console", "userCentral", "docList", "home", "sign", "entAuth", "docTranslation", "consts", "footerAd", "title", "content1", "content2", "bankContent", "bankTip1", "bankTip2", "bankFooter", "cancel", "continue", "commonFooter", "record", "hubbleRecordId", "openPlatform", "aboutBestSign", "contact", "recruitment", "help", "copyright", "company", "ssqLogo", "provideTip", "ssq", "provide", "signHotline", "langSwitch", "login", "pswLogin", "usePswLogin", "verifyLogin", "useVerifyLogin", "scanLogin", "scanFailure", "scanSuccess", "scanLoginTip", "appLoginTip", "downloadApp", "forgetPsw", "noAccount", "registerNow", "accountPlaceholder", "passwordPlaceholder", "pictureVer", "verifyCodePlaceholder", "getVerifyCode", "noRegister", "or", "errAccountOrPwdTip", "errAccountOrPwdTip2", "errEmailOrTel", "errPwd", "verCodeFormatErr", "grapVerCodeErr", "grapVerCodeFormatErr", "lackAccount", "lackGrapCode", "getVerCodeTip", "loginView", "reg<PERSON><PERSON><PERSON>", "takeViewBtn", "resendCode", "regTip", "haveRead", "bestsignAgreement", "and", "digitalCertificateAgreement", "privacyPolicy", "sendSuc", "lackVerCode", "lackPsw", "notMatch", "cookieTip", "wrongLink", "footerTips", "bestSign", "bestSignDescription", "forgetPswStep", "pictureVerCodeInput", "accountInput", "smsCodeInput", "haveRegistereLoginNow", "nextStep", "setNewPasswordInput", "passwordResetSucceeded", "accountNotRegistered", "loginAndDownload", "registerAndDownload", "inputPhone", "readContract", "errorPhone", "companyCert", "regAndCompanyCert", "handwrite", "picSubmitTip", "<PERSON><PERSON><PERSON><PERSON>", "replaceAllSignature", "replaceAllSeal", "canUseSeal", "applyForSeal", "moreTip", "uploadPic", "use", "clickExtend", "rewrite", "upload", "uploadTip1", "uploadTip2", "confirm", "upgradeBrowser", "submitTip", "needWrite", "needRewrite", "title2", "QRCode", "ok", "clearTips", "isBlank", "success", "signNotMatch", "signNotMatchExact", "msg", "successToUser", "successToSign", "cantGet", "common", "advice", "notEmpty", "enter6to18n", "ssqDes", "signPwdType", "enterActualEntName", "enterCorrectName", "enterCorrectPhoneNum", "enterCorrectEmail", "imgCodeErr", "enterCorrectIdNum", "enterCorrectFormat", "enterCorrectDateFormat", "entCertification", "subBaseInfo", "corDocuments", "license", "uploadLimit", "hi", "exit", "hotline", "acceptProtectingMethod", "comfirmSubmit", "cerficated", "entName", "serialNumber", "validity", "nationalNo", "corporationName", "city", "entCertificate", "certificationAuthority", "bestsignPlatform", "notIssued", "date", "congratulations", "rejectMessage", "recertification", "waitMessage", "<PERSON><PERSON>uth", "info", "submitPicError", "contractDrafting", "contractManagement", "userCenter", "service", "enterpriseConsole", "groupConsole", "startSigning", "contractType", "sendContract", "shortcuts", "setting", "signNum", "contractNum", "contractInFormation", "type", "basicInformation", "more", "certified", "account", "time", "day", "sendContractNum", "num", "realName", "update", "mark", "countDes", "chargeNow", "myRechargeOrder", "statusTip", "useTemplate", "useLocalFile", "docDetail", "canNotOperateTip", "shareSignLink", "faceSign", "faceFirstVerifyCodeSecond", "contractRecipient", "personalOperateLog", "recordDialog", "user", "operate", "view", "download", "remarks", "operateRecords", "borrowingRecords", "currentHolder", "currentEnterprise", "companyInterOperationLog", "receiverMap", "sender", "signer", "ccUser", "downloadCode", "noTagToAddHint", "requireFieldNotAllowEmpty", "modifySuccess", "uncategorized", "notAllowModifyContractType", "setTag", "contractTag", "plsInput", "plsInputCompanyInternalNum", "companyInternalNum", "none", "plsSelect", "modify", "contractDetailInfo", "slideContentTip", "signNotice", "contractAncillaryInformation", "content", "document", "downloadDepositConfirmTip", "hint", "confrim", "downloadTip", "transferSuccessGoManagePage", "claimSign", "downloadDepositPageTip", "resend", "proxySign", "notPassed", "approving", "signning", "notarized", "currentFolder", "archive", "deadlineForSigning", "endFinishTime", "contractImportTime", "contractSendTime", "back", "contractInfo", "basicInfo", "personAccount", "entAccount", "operator", "signStartTime", "signDeadline", "contractExpireDate", "edit", "settings", "from", "folder", "reason", "approval", "viewAttach", "downloadContract", "downloadAttach", "print", "certificatedTooltip", "needMeSign", "needMeApproval", "inApproval", "needOthersSign", "signComplete", "signOverdue", "rejected", "revoked", "contractCompleteTime", "contractEndTime", "reject", "revoke", "viewSignOrders", "viewApprovalProcess", "completed", "cc", "ccer", "signSubject", "signSubjectTooltip", "IDNumber", "state", "notice", "detail", "RealNameCertificationRequired", "RealNameCertificationNotRequired", "MustHandwrittenSignature", "handWritingRecognition", "privateMessage", "attachment", "rejectReason", "notSigned", "notViewed", "viewed", "signed", "viewedNotSigned", "notApproval", "remindSucceed", "reviewDetails", "close", "entInnerOperateDetail", "approve", "disapprove", "applySeal", "applied", "apply", "toOtherSign", "handOver", "approvalOpinions", "useSeal", "signature", "fill", "times", "place", "contractDetail", "viewMore", "collapse", "signLink", "saveQRCode", "signQRCode", "copy", "copySucc", "copyFail", "unCertified", "claimed", "uploadFile", "thumbnails", "isUploading", "move", "delete", "replace", "tip", "understand", "totalPages", "matchErr", "inUploadingDeleteErr", "timeOutErr", "imgUnqualified", "imgBiggerThan20M", "error", "hasCATip", "internalNumber", "contractName", "contractNameTooltip", "toSelect", "contractTypeErr", "signDeadLine", "signDeadLineTooltip", "selectDate", "expireDateTooltip", "necessary", "notNecessary", "dateTips", "contractTitleErr", "contractTitleLengthErr", "template", "templateList", "linkBoxTip", "dynamicTemplateUpdate", "newVersionDesc", "updateTip", "connectUs", "sendCode", "fail", "sendCodeGuide", "tip1", "main", "sub", "line1", "line2", "tip2", "tip3", "tip4", "style", "text", "x", "fontSize", "resetPwd", "<PERSON><PERSON><PERSON><PERSON>", "oldPlaceholder", "new<PERSON>abel", "newPlaceholder", "submit", "errorMsg", "oldRule", "newRule", "personAuthIntercept", "name", "id", "descNoAuth", "des<PERSON><PERSON>", "descNoSame", "descNoAuth1", "descNoAuth2", "tips", "goOn", "goMore", "descNoSame1", "descNoSame2", "goHome", "authInfo", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmReject", "authIntercept", "authTip", "viewAndSign", "tips2", "requestOtherAnth", "goAuth", "requestSomeoneList", "ent", "accountPH", "send", "lackEntName", "errAccount", "successfulSent", "thirdPartApprovalDialog", "title1", "cancelBtnText", "confirmBtnText", "iKnow", "endSignEarlyPrompt", "signPrompt", "signTotalCountTip", "signatureTip", "hasSigned", "hasNotSigned", "noNeedSealTip", "commonNomal", "yesterday", "ssqPlatform", "ssqTestPlatform", "pageExpiredTip", "pswCodeSimpleTip", "transferAdminDialog", "transfer", "confirmAdmin", "choseBoxForReceiver", "dataNeedForReceiver", "dataFromDataBox", "searchTp", "search", "boxNotFound", "localCommon", "seal", "signDate", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "confirmSeal", "confirmRemark", "optional", "require", "comboBox", "twoFactor", "signTip", "settingTwoFactor", "step1", "step1Tip", "step2", "step2Tip1", "step2Tip2", "step3", "step3Tip", "verifyCode6", "iosAddress", "android<PERSON><PERSON><PERSON>", "chromeVerify", "nextBtn", "confirmSign", "dynamicCode", "password", "pleaseInput", "twoFactorTip", "passwordTip", "twoFactorAndPasswordTip", "passwordTip2", "dynamicVerifyInfo", "functionSupportDialog", "inputTip", "useSence", "useSenceTip", "estimatedOnlineTime", "requireContent", "requireContentTip", "getSupport", "callServiceHotline", "useSenceNotEmpty", "requrieContentNotEmpty", "oneWeek", "oneMonth", "other", "submitSuccess", "submitTrial", "toTrial", "trialTip", "applyTrial", "trialSuccTip", "goBuy", "trialTipMap", "tip5", "contactAdminTip", "trialEndTip", "trialRemainDayTip", "trialEnd", "trialEndMap", "deactivateTip", "feature1", "remove1", "feature2", "remove2", "feature3", "remove3", "feature4", "remove4", "setSignPwdDialog", "saveAndReturnSign", "changeEmailVerify", "changePhoneVerify", "contractCompare", "reUpload", "packagePurchase", "packagePurchaseTitle", "myPackage", "packageDetail", "per", "packageContent", "limitTime", "month", "payNow", "contactUs", "compareInfo1", "compareInfo2", "compareInfo3", "codePay", "ali<PERSON>ay", "wxPay", "payIno", "finishPay", "paySuccess", "originFile", "compareFile", "documentSelect", "comparisonResult", "history", "currentHistory", "noData", "differences", "historyLog", "dragInfo", "uploadError", "pageNum", "difference", "comparing", "<PERSON><PERSON><PERSON>", "translate", "doCompare", "doTranslate", "review", "doReview", "reviewUploadFile", "reviewUpload", "reviewOriginFile", "reviewTargetFile", "reviewResult", "uploadReviewFile", "risk", "risks", "startReview", "reviewing", "noRisk", "allowUpload", "notAllowUpload", "resume<PERSON><PERSON>iew", "extract", "extractTitle", "selectKeyword", "keyword", "addKeyword", "introduce", "startExtract", "extractTargetFile", "extractKeyWord", "extracting", "extractResult", "extractUploadFile", "needExtractKeyword", "summary", "key<PERSON><PERSON><PERSON>y", "deleteKeywordConfirm", "keywordPosition", "riskJudgement", "judge<PERSON><PERSON><PERSON>Contract", "interpretTargetContract", "startJudge", "startInterpret", "uploadText", "interpretText", "startTips", "interpretTips", "infoExtract", "batchImport", "templateCommon", "mgapprovenote", "SAQ", "analyze", "annotate", "law", "case", "limit", "confirmTxt", "experience", "datas", "original", "export", "preview", "sealConfirm", "header", "signerEnt", "abnormalSeal", "sealNormal", "keyInfoExtract", "tooltips", "predictText", "extractText", "errorMessage", "result", "judgeRisk", "deepInference", "showAll", "dialogTitle", "aiInterpret", "sealDistribute", "requestSeal", "applicant", "accountID", "submissionTime", "status", "agree", "unAgree", "ifAgree", "applyTime", "to", "placeHolderTime", "senderCompany", "documentTitle", "sealApplicationScope", "applyforSeal", "sealApproval", "sealRight", "allEntContract", "partEntContract", "pleaseInputRight", "successTransfer", "getRight", "signAllEntContract", "sealUseTime", "currentStatus", "takeBackSeal", "hasAgree", "hasReject", "hasDone", "<PERSON><PERSON><PERSON>", "hopeAsk", "hopeGive", "hopeGiveYou", "noSettingTime", "approvalSuccess", "getSealSuccess", "workspace", "create", "termsDetail", "extractFormat", "required", "agreement", "extractionRequest", "define", "drag", "add", "format", "fileName", "failed", "size", "ongoing", "others", "curProgress", "refresh", "details", "start", "skip", "tiqu", "chouqu", "distribution", "Incomplete", "createReview", "manageReview", "reviewDetail", "reviewId", "reviewStatus", "reviewName", "reviewStartTime", "reviewCompleteTime", "reviewDesc", "distribute", "current", "page", "users", "message", "placeholder", "reupload", "finish", "reviewSummary", "initiator", "versionSummary", "version", "versionOrder", "curRevie<PERSON><PERSON><PERSON><PERSON>", "curReviewV<PERSON><PERSON>", "curReviewPopulation", "curReviewStartTime", "curReviewInitiator", "checkComments", "overview", "reviewer", "replyTime", "files", "numberOfModificationSuggestions", "uploadTime", "dispatch", "recent", "replyContent", "noIdea", "origin", "revised", "suggestion", "dateMark", "unReviewed", "revisionFiles", "staffReplyAggregation", "staffReply", "tipsContent", "successMessage", "PASS", "REJECT", "uploadErrorMessage", "successInitiated", "autoDistribute", "requiredUsers", "contentToReview", "termDetails", "term", "aiDistribute", "docIconAlt", "docxIconAlt", "pdfIconAlt", "requiredUsersError", "selectContentError", "initiateReviewSuccess", "syncInitiated", "contentTracing", "<PERSON><PERSON><PERSON>nt", "originalResult", "contentSource", "hubblePackage", "remainingPages", "pages", "usedPages", "remaining", "total", "expiryTime", "amount", "unitPrice", "words", "workspaceIndex", "package", "exportList", "exportAllChecked", "exportCurrentChecked", "exportAllMatched", "exportCurrentMatched", "operation", "relatedTaskStatus", "confirmDelete", "prompt", "booleanYes", "booleanNo", "defaultExportName", "taskNotStarted", "taskStarted", "contentSearchCompleted", "resultFormattingCompleted", "resultVerificationCompleted", "filter", "refreshExtraction", "extractTerms", "refreshList", "currentCondition", "when", "selectCondition", "enterCondition", "yes", "no", "addCondition", "reset", "equals", "notEquals", "contains", "notContains", "greaterThan", "greaterThanOrEquals", "lessThan", "lessThanOrEquals", "emptyCondition", "fieldConfig", "button", "agreementDetail", "file", "replaceFile", "relatedExtractionStatus", "dataSource", "select", "input", "save", "addDataSource", "pageNo", "pageSuffix", "inputDataSource", "pageFormatError", "uploadSuccess", "termManagement", "batchDelete", "import", "definition", "formatRequirement", "dataFormat", "addTitle", "namePlaceholder", "definitionPlaceholder", "formatRequirementPlaceholder", "dataFormatPlaceholder", "confirmEdit", "importTitle", "uploadTemplate", "downloadTemplate", "extractType", "longText", "boolean", "importSuccess", "deleteConfirm", "nameEmptyError", "agent", "riskTitle", "feedback", "to<PERSON><PERSON>", "otherContract", "autoExtract", "autoRisk", "aiGenerated", "chooseRisk", "chooseExtract", "analyzing", "options", "inputTips", "chargeTip", "revision", "diff", "locate", "custom", "satisfy", "dissatisfy", "selectFunc", "deepThinking", "deepThoughtCompleted", "reJudge", "useLawyer", "interpretFinish", "exportPDF", "exporting", "authorize", "contract", "hubbleEntry", "smartAdvisor", "lang"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/en.js"], "sourcesContent": ["\n// 语言为英语时的文案，暂只在wap打开短链接时生效\nimport utils from './module/utils/utils-en.js';\nimport mixin from './module/mixins/mixins-en.js';\nimport components from './module/components/components-en.js';\n\nimport console from './module/console/console-en.js';\nimport userCentral from './module/usercentral/usercentral-en.js';\nimport docList from './module/docList/docList-en.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-en.js';\nimport entAuth from './module/entAuth/entAuth-en.js';\nimport docTranslation from './module/docTranslation/docTranslation-en.js';\nimport consts from '@/lang/module/consts/en';\n\nexport default {\n    ...utils,\n    ...mixin,\n    ...components,\n    ...docTranslation,\n    footerAd: {\n        title: 'Page jump prompt',\n        content1: 'You are going to visit a third-party promotion page',\n        content2: 'Do you want to continue?',\n        bankContent: '即将进入宁波银行\"容易贷\"企业贷款介绍页面',\n        bankTip1: '让宁波银行主动给我打电话',\n        bankTip2: '向我发送一条短信，介绍如何办理',\n        bankFooter: '加宁波银行专属客服，一对一服务我',\n        cancel: 'Cancel',\n        continue: 'Continue',\n    },\n    commonFooter: {\n        record: 'ICP main body record number: Zhejiang ICP No. ********',\n        hubbleRecordId: '网信算备：330106973391501230011',\n        openPlatform: 'Open platform',\n        aboutBestSign: 'About BestSign',\n        contact: 'Contact us',\n        recruitment: 'Recruitment',\n        help: 'Help Center',\n        copyright: 'Copyright',\n        company: 'HangZhou BestSign Ltd.',\n        ssqLogo: 'BestSign bottom bar logo',\n        provideTip: 'E-signing service is provided by',\n        ssq: ' BestSign',\n        provide: '',\n        signHotline: 'Service hotline',\n        langSwitch: 'Language',\n    },\n    login: {\n        pswLogin: 'Password Login',\n        usePswLogin: 'Log in with password',\n        verifyLogin: 'Verification Code',\n        useVerifyLogin: 'Log in with verification code',\n        scanLogin: 'Scan Code Login',\n        scanFailure: 'QR code has expired, please retry',\n        scanSuccess: 'Scan successfully',\n        scanLoginTip: 'Please use the app on the APP to scan the login',\n        appLoginTip: 'Please click login in the app BestSign',\n        downloadApp: 'Download BestSign APP',\n        forgetPsw: 'Forget?',\n        login: 'Sign in',\n        noAccount: 'No account?',\n        registerNow: 'Sign up now',\n        accountPlaceholder: 'Telephone or Email',\n        passwordPlaceholder: 'Password',\n        pictureVer: 'Please fill in the content in the picture',\n        verifyCodePlaceholder: 'Please input 6-dight code',\n        getVerifyCode: 'Send',\n        noRegister: 'Not registered yet',\n        or: 'or',\n        errAccountOrPwdTip: 'The password you entered does not match the account number?',\n        errAccountOrPwdTip2: 'The password you entered does not match the account',\n        errEmailOrTel: 'Please input correct Email or telephone number!',\n        errPwd: 'Please input correct password!',\n        verCodeFormatErr: 'Verification Code Error',\n        grapVerCodeErr: 'Graphic verification code error',\n        grapVerCodeFormatErr: 'Graphic verification code format error',\n        lackAccount: 'Please input account first',\n        lackGrapCode: 'PPlease fill in the graphic verification code first.',\n        getVerCodeTip: 'Please get verification code',\n\n        loginView: 'Login and View Contract',\n        regView: 'Register and View Contract',\n        takeViewBtn: 'Login and sign',\n        resendCode: 'Reacquire',\n        regTip: 'After filling in the correct verification code, BestSign will  be to create an account for you',\n        haveRead: 'I have read and agreed',\n        bestsignAgreement: 'BestSign Service Agreement',\n        and: 'and',\n        digitalCertificateAgreement: 'Digital certificate usage agreement',\n        privacyPolicy: 'Privacy Policy',\n        sendSuc: 'Send successfully',\n        lackVerCode: 'Please Enter the Verification Code First',\n        lackPsw: 'Please enter your password first',\n        notMatch: 'The password and account entered do not match',\n        cookieTip: 'Unable to read and write cookies, please check if there is no trace/incognito mode or other cookies disabled operation',\n        wrongLink: 'Illegal link',\n        footerTips: 'Electronic sign-up service provided by <span>BestSign</span>',\n        bestSign: 'BestSign',\n        bestSignDescription: 'Electronic contracting industry leader',\n        /** 忘记密码 /forgotPassword start */\n        forgetPswStep: 'Verify registered account | Reset password',\n        pictureVerCodeInput: 'Graphic verification code | Please fill in the contents of the picture',\n        accountInput: 'Account | Please enter your account',\n        smsCodeInput: 'Verification code | Get verification code',\n        haveRegistereLoginNow: 'Have registered， |  login now',\n        nextStep: 'Next | Submit',\n        setNewPasswordInput: 'Set new password | 6-18-digit number or letter',\n        passwordResetSucceeded: ' Password reset succeeded',\n        /** 忘记密码 /forgotPassword end */\n        accountNotRegistered: 'Account not registered',\n        loginAndDownload: 'login and download contract',\n        registerAndDownload: 'register and download contract',\n        inputPhone: 'please input phone number',\n        readContract: 'read contract',\n        errorPhone: 'Mobile phone format error',\n        companyCert: 'Conducting corporate certification',\n        regAndCompanyCert: 'Register and conduct corporate certification',\n    },\n    ...sign,\n    handwrite: {\n        title: 'Directly on the screen',\n        picSubmitTip: 'Signature image submitted successfully.',\n        settingDefault: 'Set as default',\n        replaceAllSignature: 'Used for all signatures',\n        replaceAllSeal: 'For all seals',\n        canUseSeal: 'My Seals',\n        applyForSeal: 'Apply for using the seals',\n        moreTip: 'Your handwritten signature will be saved as your default signature, used only for contract signing. Management path: [User Center -> Signature Management]',\n        uploadPic: 'Upload image',\n        use: 'Use',\n        clickExtend: 'Click right arrow to extend area',\n        rewrite: 'Rewrite',\n        upload: 'Upload signature image',\n        uploadTip1: 'Tip: When uploading a signature image, please make sure the signature fills the entire image',\n        uploadTip2: 'Please use dark-colored or pure black text for your signature.',\n        cancel: 'Cancel',\n        confirm: 'Confirm',\n        upgradeBrowser: 'The browser does not support handwriting, please upgrade your browser.',\n        submitTip: 'Handwritten signature submitted successfully',\n        needWrite: 'Please write your name first',\n        needRewrite: \"Can't identify the signature, please try rewrite\",\n        title2: 'Hand drawn your signature',\n        QRCode: 'By scanning the QR',\n        ok: 'OK',\n        clearTips: 'Please write a clearly identifiable signature',\n        isBlank: 'The canvas is empty, please hand-paint the signature before submitting!',\n        success: 'Handwritten signature submitted successfully',\n        signNotMatch: 'Please write your signature in block letters and keep it consistent with your real name ID information.',\n        signNotMatchExact: 'The {numList} word does not match, please rewrite',\n        msg: {\n            successToUser: 'The new signature has taken effect, please go to the Web User Center - Signature Management',\n            successToSign: 'The new signature has taken effect, please check the contract signing page',\n            cantGet: 'Can\\'t get a signature, try using a different browser!',\n        },\n    },\n    common: {\n        aboutBestSign: 'About BestSign',\n        contact: 'Contact us',\n        recruitment: 'Recruitment',\n        copyright: 'Copyright',\n        advice: 'Advice',\n        notEmpty: 'Can not be empty',\n        enter6to18n: 'Please enter 6-18 digits, letters',\n        ssqDes: 'Leader of electronic signing cloud platform',\n        openPlatform: 'Open platform',\n        company: 'HangZhou BestSign Ltd.',\n        help: 'Help Center',\n\n        errEmailOrTel: 'Please input correct Email or telephone number!',\n        verCodeFormatErr: 'Verification Code Error',\n        signPwdType: 'Please enter 6 digits',\n        enterActualEntName: 'Please fill in the real business name',\n        enterCorrectName: 'Please enter the correct business name',\n        enterCorrectPhoneNum: 'Please enter the correct cell phone number',\n        enterCorrectEmail: 'Please enter the correct e-mail',\n        imgCodeErr: 'Verification code error',\n        enterCorrectIdNum: 'Please enter the correct ID number',\n        enterCorrectFormat: 'Please enter the correct format',\n        enterCorrectDateFormat: 'Please enter the correct date format',\n    },\n    entAuth: {\n        ...entAuth,\n        entCertification: 'Enterprise real name certification',\n        subBaseInfo: 'Submit basic information',\n        corDocuments: 'Corporate documents',\n        license: 'License',\n        upload: 'Click to upload',\n        uploadLimit: 'Images are only available in jpeg, jpg, png formats and no larger than 10M',\n        hi: 'Hi',\n        exit: 'Exit',\n        help: 'Help',\n        hotline: 'Service hotline',\n        acceptProtectingMethod: 'I accept the method of protecting personally identifiable information I submit',\n        comfirmSubmit: 'Confirm submission',\n        cerficated: 'Certification completed',\n        entName: 'Enterprise name',\n        serialNumber: 'Integer serial number',\n        validity: 'Validity',\n        nationalNo: 'National Registration No.',\n        corporationName: 'Name of legal representative',\n        city: 'City',\n        entCertificate: 'Enterprise Real Name Certificate',\n        certificationAuthority: 'Certification Authority',\n        bestsignPlatform: 'BestSign electronic signing cloud platform',\n        notIssued: 'Not issued',\n        date: '{year}-{month}-{day}',\n        congratulations: 'Congratulations on your successful completion of the enterprise real name certification',\n        continue: 'Continue',\n        rejectMessage: 'For the following reasons, the data audit did not pass, please check',\n        recertification: 'Recertification',\n        waitMessage: 'Customer service will be reviewed within one business day, please be patient',\n    },\n    personalAuth: {\n        info: 'info',\n        submitPicError: '请上传照片后再使用',\n    },\n    home: {\n        ...home,\n        home: 'Home',\n        contractDrafting: 'Contract drafting',\n        contractManagement: 'Contract',\n        userCenter: 'Admin',\n        service: 'Service',\n        enterpriseConsole: 'Enterprise console',\n        groupConsole: 'Group console',\n        startSigning: 'Start signing',\n        contractType: 'Ordinary contract| Template contract',\n        sendContract: 'Start now',\n        shortcuts: 'shortcuts | No shortcuts for any documents',\n        setting: 'Set up immediately | Create more shortcuts',\n        signNum: 'Monthly summary of signatures and deliveries',\n        contractNum: 'contracts sent | contracts signed',\n        contractInFormation: 'You have not sent or signed any contracts this month',\n        type: 'company | person ',\n        basicInformation: 'basic information',\n        more: 'more',\n        certified: 'certified | uncertified',\n        account: 'Account',\n        time: 'Time of creation |Time of registration',\n        day: 'day | month',\n        sendContractNum: 'deliveries | signatures',\n        num: '',\n        realName: 'create a business account with real name now | create an individual account with real name now',\n        update: 'Product update',\n        mark: 'Would you like to recommend BestSign to your friends and colleagues? Please rate your choice from 0 to 10.',\n        countDes: {\n            1: 'Available: for the enterprise contract',\n            2: '',\n            3: 'for the private contract',\n            4: '',\n        },\n        chargeNow: 'Charge now',\n        myRechargeOrder: 'My recharge order',\n        statusTip: {\n            1: 'Need me to operate',\n            2: 'Need  others to sign',\n            3: 'Signing is about to close',\n            4: 'Signing complete',\n        },\n        useTemplate: 'Use template',\n        useLocalFile: 'Upload local file',\n    },\n    docDetail: {\n        canNotOperateTip: 'Unable to {operate} contract',\n        shareSignLink: 'share the signing link',\n        faceSign: 'signing with face scan',\n        faceFirstVerifyCodeSecond: 'Priority sign with facial verification, alternate sign with SMS code verification',\n        contractRecipient: 'contract receiver',\n        personalOperateLog: 'individual contract operation log',\n        recordDialog: {\n            date: 'date',\n            user: 'user',\n            operate: 'operation',\n            view: 'view',\n            download: 'download',\n        },\n        remarks: 'notes',\n        operateRecords: 'record of operation',\n        borrowingRecords: 'borrowing records',\n        currentHolder: 'current holder',\n        currentEnterprise: 'company under the current account',\n        companyInterOperationLog: 'internal operation log of the company',\n        receiverMap: {\n            sender: 'contract sender',\n            signer: 'contract receiver',\n            ccUser: 'contract copied to',\n        },\n        downloadCode: 'contract download code',\n        noTagToAddHint: 'No labels yet; please go to business control panel to add them',\n        requireFieldNotAllowEmpty: 'required fields cannot be empty',\n        modifySuccess: 'modification successful',\n        uncategorized: 'unclassified',\n        notAllowModifyContractType: 'The contract type is not allowed to be modified for the contract under {type}',\n        setTag: 'set labels',\n        contractTag: 'contract labels',\n        plsInput: 'please put in',\n        plsInputCompanyInternalNum: 'please enter the internal business number',\n        companyInternalNum: 'Internal business number',\n        none: 'none',\n        plsSelect: 'Please select',\n        modify: 'modify',\n        contractDetailInfo: 'contract details',\n        slideContentTip: {\n            signNotice: 'signing instructions',\n            contractAncillaryInformation: 'attachments to the contract',\n            content: 'content',\n            document: 'document',\n        },\n        downloadDepositConfirmTip: {\n            title: 'The signing proof page you downloaded is a non-sensitive version, with  private information hidden and not applicable for court proceedings. If you need to use it for court proceedings, please contact us for the full version.',\n            hint: 'tips',\n            confrim: 'continue to download',\n            cancel: 'cancel',\n        },\n        downloadTip: {\n            title: 'As the contract is not yet completed, you are downloading a preview file of the contract that is not yet in effect',\n            hint: 'tips',\n            confirm: 'confirm',\n            cancel: 'cancel',\n        },\n        transferSuccessGoManagePage: 'The transfer is successful and will return to the contract management page',\n        claimSign: 'retrieve and sign',\n        downloadDepositPageTip: 'download signing proof page (non-sensitive version)',\n        resend: 'resend',\n        proxySign: 'entrusted signing',\n        notPassed: 'rejected',\n        approving: 'under review',\n        signning: 'signing in progress',\n        notarized: 'notarized',\n        currentFolder: 'current folder',\n        archive: 'contract filed',\n        deadlineForSigning: 'deadline for signing',\n        endFinishTime: 'signing completed/completion date',\n        contractImportTime: 'contract import time',\n        contractSendTime: 'contract delivery time',\n        back: 'back',\n        contractInfo: 'Contract information',\n        basicInfo: 'Basic information',\n        contractNum: 'Contract No.',\n        sender: 'Sender',\n        personAccount: 'Personal account',\n        entAccount: 'Enterprise account',\n        operator: 'Operator',\n        signStartTime: 'Start signing time',\n        signDeadline: 'Signing deadline',\n        contractExpireDate: 'Contract expiration date',\n        // none: 'None',\n        edit: 'Modify',\n        settings: 'Set up',\n        from: 'Source',\n        folder: 'Folder',\n        contractType: 'Contract type',\n        reason: 'Reason',\n        sign: 'Sign',\n        approval: 'approval',\n        viewAttach: 'View the attached pages',\n        downloadContract: 'Download the contract',\n        downloadAttach: 'Download the attached page',\n        print: 'Print',\n        certificatedTooltip: 'The contract and related evidence have been documented in the judicial chain of the Hangzhou Internet Court',\n        needMeSign: 'Need me to sign',\n        needMeApproval: 'Need my approval',\n        inApproval: 'Sub-judice…',\n        needOthersSign: 'Need others to sign',\n        signComplete: 'Signing complete',\n        signOverdue: 'Overdue signing',\n        rejected: 'Rejected',\n        revoked: 'Revoked',\n        contractCompleteTime: 'Signing complete time',\n        contractEndTime: 'Signing end time',\n        reject: 'Reject',\n        revoke: 'Revoke',\n        download: 'Download',\n        viewSignOrders: 'View the order of signing',\n        viewApprovalProcess: 'View approval process',\n        completed: 'Signing completed',\n        cc: 'Cc',\n        ccer: 'Copy party',\n        signer: 'Signer',\n        signSubject: 'Sign-up subject',\n        signSubjectTooltip: 'The signing subject filled in by the sender is',\n        user: 'User',\n        IDNumber: 'ID number',\n        state: 'State',\n        time: 'Time',\n        notice: 'Remind',\n        detail: 'Details',\n        RealNameCertificationRequired: 'Real-name certification required',\n        RealNameCertificationNotRequired: 'No real name certification required',\n        MustHandwrittenSignature: 'Must sign with handwritten signature',\n        handWritingRecognition: 'Turn on handwriting recognition',\n        privateMessage: 'Message',\n        attachment: 'Attachment',\n        rejectReason: 'Reason',\n        notSigned: 'Not signed',\n        notViewed: 'Not viewed',\n        viewed: 'Viewed',\n        signed: 'Signed',\n        viewedNotSigned: 'Read not signed',\n        notApproval: 'Unapproved',\n        remindSucceed: 'Reminder message sent',\n        reviewDetails: 'Approval details',\n        close: 'Shut Down',\n        entInnerOperateDetail: 'Internal operation details',\n        approve: 'Agree',\n        disapprove: 'Reject',\n        applySeal: 'Application for printing',\n        applied: 'Already applied',\n        apply: 'Application',\n        toOtherSign: 'Transfer to someone else to sign',\n        handOver: 'Transfer',\n        approvalOpinions: 'Approval comments',\n        useSeal: 'Print',\n        signature: 'Signature',\n        use: 'Use',\n        date: 'Date',\n        fill: 'Fil in',\n        times: 'Secondary',\n        place: 'Place',\n        contractDetail: 'Contract details',\n        viewMore: 'View more',\n        collapse: 'Fold',\n        signLink: 'Signing a link',\n        saveQRCode: 'Save the QR code or copy the link and share it with the signatory',\n        signQRCode: 'Sign the link QR code',\n        copy: 'Copy',\n        copySucc: 'Successful copy',\n        copyFail: 'Replication copy',\n        certified: 'Certified',\n        unCertified: 'Uncertified',\n        claimed: 'Claimed',\n    },\n    uploadFile: {\n        thumbnails: 'Thumbnail',\n        isUploading: 'Uploading',\n        move: 'Move',\n        delete: 'Delete',\n        replace: 'Replace',\n        tip: 'Tip',\n        understand: 'Get it',\n        totalPages: '{page} in total',\n        uploadFile: 'Upload local file',\n        matchErr: 'The server has a small gap, please try again later.',\n        inUploadingDeleteErr: 'Please delete after uploading',\n        timeOutErr: 'Request timed out',\n        imgUnqualified: 'Image format does not meet the requirements',\n        imgBiggerThan20M: 'Image size cannot exceed 20MB!',\n        error: 'Error',\n        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',\n\n    },\n    contractInfo: {\n        internalNumber: 'Internal business number',\n        contractName: 'Contract name',\n        contractNameTooltip: 'Contract name please do not contain special characters and is no longer than 100 words long',\n        contractType: 'Contract type',\n        toSelect: 'Please choose',\n        contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',\n        signDeadLine: 'Signing deadline',\n        signDeadLineTooltip: 'If the contract is not signed before this date, it cannot be continued',\n        selectDate: 'Select date and time',\n        contractExpireDate: 'Contract expiration date',\n        expireDateTooltip: 'Expiration time in the contents of the contract for your subsequent contract management',\n        necessary: 'Necessary',\n        notNecessary: 'Optional',\n        dateTips: 'The contract expiration date has been automatically identified for you, please confirm',\n        contractTitleErr: 'Contract name please do not contain special characters',\n        contractTitleLengthErr: 'Please do not exceed 100 words in the length of the contract name.',\n    },\n    userCentral: userCentral,\n    template: {\n        templateList: {\n            linkBoxTip: 'Associated Cabinet ID：',\n        },\n        dynamicTemplateUpdate: {\n            title: 'New function of dynamic template is online',\n            newVersionDesc: 'The new function supports header and footer display and keeps  document page layout to the maximum degree.',\n            updateTip: 'The previous dynamic template feature is not synchronized and compatible. Manual upgrading is required. If dynamic templates created before January 26th are edited, contracts will not be saved nor sent. Contracts can still be sent before March 1, 2021 if templates have not been edited. Upgrading is recommended as soon as possible. Non-dynamic templates are not affected.',\n            connectUs: 'If you have any questions, please contact via the hotline ************ or contact online customer service.',\n        },\n        sendCode: {\n            tip: 'The current template settings do not meet the conditions for generating the send-code. Check whether the following requirements are met:',\n            fail: {\n                1: 'No blank documents are included',\n                2: 'The contracting party has only one variable party (including signature and copy), and the variable party must be the first operator; The signatory must have a signature stamping position',\n                3: ' The fixed account of the contracting party shall not be empty',\n                4: 'Does not trigger pre-shipment approval',\n                5: 'The contents that the sender has to fill are not empty(Include the description field and the contract content field)',\n                6: 'Not template combination',\n            },\n        },\n        sendCodeGuide: {\n            title: '发送码高级功能说明',\n            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n            tip1: {\n                main: '1. 上上签',\n                sub: '',\n                line1: '向上上签申请开通档案+、合同预审、智能预审',\n                line2: '开通后可以到对应的菜单中操作使用',\n            },\n            tip2: {\n                main: '2. 档案柜管理员',\n                sub: '创建档案柜、配置智能预审',\n                line1: '',\n                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',\n            },\n            tip3: {\n                main: '3. 签约方',\n                sub: '扫码填资料、获取合同文件',\n                line1: '',\n                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',\n            },\n            tip4: {\n                main: '4. 档案柜管理员',\n                sub: '',\n                line1: '查看签约的相对方、发送的合同情况',\n                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',\n            },\n        },\n    },\n    style: {\n        signature: {\n            text: {\n                x: '0',\n                fontSize: '18',\n            },\n        },\n    },\n    resetPwd: {\n        title: 'Safety tips！',\n        notice: 'The security factor of your password is low and there is a security risk. Please reset your password',\n        oldLabel: 'Original password',\n        oldPlaceholder: 'Please enter the original password',\n        newLabel: 'New password',\n        newPlaceholder: '6-18 digits and uppercase and lowercase letters, support special characters',\n        submit: 'Submit',\n        errorMsg: 'Password should contain 6-18 digits and upper - and lowercase letters, please reset',\n        oldRule: 'The original password cannot be empty',\n        newRule: 'The new password cannot be empty',\n        success: 'Success!',\n    },\n    personAuthIntercept: {\n        title: '邀请您以',\n        name: '姓名：',\n        id: '身份证号：',\n        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        desMore: '根据发起方要求，您还需要补充',\n        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        tips: '实名认证通过后，可查看并签署合同。',\n        goOn: '是我本人，开始认证',\n        goMore: '去补充认证',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '当前账号是否是您的常用手机号？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n        confirmReject: '是的，我要驳回实名',\n    },\n    authIntercept: {\n        title: '要求您以：',\n        name: '姓名为：',\n        id: '身份证号为：',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        descNoSame1: '签署合同。',\n        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n        tips: '注：身份信息完全一致才能签署合同',\n        goOn: '是我本人，开始认证',\n        goHome: '我知道了',\n        goMore: '去补充认证',\n        authTip: '进行实名认证。',\n        viewAndSign: '完成认证后即可查看和签署合同',\n        tips2: '注：企业名称完全一致才能查看和签署合同。',\n        requestOtherAnth: 'Request verification by others',\n        goAuth: '去实名认证',\n        requestSomeoneList: '请求以下人员完成实名认证：',\n        ent: '企业',\n        entName: '企业名称',\n        account: '账号',\n        accountPH: '手机或邮箱',\n        send: '发送',\n        lackEntName: '请填写企业名称',\n        errAccount: '请填写正确的邮箱或手机号',\n        successfulSent: '发送成功',\n    },\n    thirdPartApprovalDialog: {\n        title1: 'Pre-signature Approval',\n        title2: 'Approval Process',\n        content1: 'Signing requires approval. Please wait patiently.',\n        content2: '需由第三方平台（非上上签平台）审批合同。',\n        cancelBtnText: 'View Approval Process',\n        confirmBtnText: 'Confirm',\n        iKnow: 'I see',\n    },\n    endSignEarlyPrompt: {\n        cancel: '取消',\n        confirm: '确认',\n        signPrompt: '签署提示',\n        signTotalCountTip: '本次签署共包含{count}份合同文件',\n        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n        hasSigned: '{count}人已签字',\n        hasNotSigned: '{count}人未签字',\n        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',\n    },\n    commonNomal: {\n        yesterday: 'Yesterday',\n        ssq: 'BestSign',\n        ssqPlatform: 'BestSign E-signature cloud platform',\n        ssqTestPlatform: '(For testing purposes only) BestSign E-Signature Cloud Platform',\n        pageExpiredTip: 'The page has expired, please refresh and try again',\n        pswCodeSimpleTip: 'The password must contain 6-18 digits and uppercase and lowercase letters, please reset',\n    },\n    transferAdminDialog: {\n        title: 'Identification',\n        transfer: 'Transfer',\n        confirmAdmin: 'I am the main administrator',\n        content: 'The system administrator is responsible for managing the company seal, contracts, and other personnel permissions, generally belonging to the legal representative, financial manager, legal manager, IT department manager, or business leader of the company.| Please confirm if you meet the above identity requirements. If not, it is recommended to forward it to the relevant personnel.',\n    },\n    choseBoxForReceiver: {\n        dataNeedForReceiver: 'Information to be submitted by the signing party',\n        dataFromDataBox: 'The information to be submitted by the signing party needs to be obtained through document collection from a certain file cabinet.',\n        searchTp: 'Please enter the name or code of the file cabinet.',\n        search: 'Search',\n        boxNotFound: 'The file cabinet cannot be found.',\n        cancel: 'Cancel',\n        confirm: 'Ok',\n    },\n    localCommon: {\n        cancel: 'Cancel',\n        confirm: 'Confirm',\n        toSelect: 'Please select',\n        seal: 'Stamp',\n        signature: 'Sign',\n        signDate: 'Date',\n        text: 'Text',\n        date: 'Date',\n        qrCode: 'QR Code',\n        number: 'Digital',\n        dynamicTable: 'Dynamic forms',\n        terms: 'Terms and conditions of the contract',\n        checkBox: 'Checkboxes for multiple options',\n        radioBox: 'Checkbox for single options',\n        image: 'Image',\n        confirmSeal: 'Stamp for inquiries',\n        tip: 'Tips',\n        confirmRemark: 'Remarks regarding seals that do not meet requirements',\n        optional: 'Optional',\n        require: 'Required',\n        comboBox: 'DropDown',\n    },\n    twoFactor: {\n        signTip: 'Signing Prompt',\n        settingTwoFactor: '设置二要素验证器',\n        step1: '1. 安装验证器应用',\n        step1Tip: '二要素身份验证需要您安装一下手机应用程序：',\n        step2: '2.扫描二维码',\n        step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',\n        step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',\n        step3: '3.输入6位验证码',\n        step3Tip: '请输入屏幕上显示的验证码',\n        verifyCode6: '6位验证码',\n        iosAddress: 'iOS下载地址：',\n        androidAddress: 'Android下载地址：',\n        chromeVerify: '谷歌身份验证器',\n        nextBtn: '下一步',\n        confirmSign: 'Confirm to Sign',\n        dynamicCode: '验证器动态码',\n        password: 'Encryption Code',\n        pleaseInput: 'Please input',\n        twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',\n        passwordTip: 'The sender requires encrypted signature for this document. Please complete the signing process accordingly.',\n        twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',\n        passwordTip2: 'Please contact the sender to obtain the encryption signing code. The contract will be ready for signing once you enter the code.',\n        dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。',\n    },\n    functionSupportDialog: {\n        title: 'Function introduction',\n        inputTip: 'If you have relevant use needs, please fill in your needs in the following form. BestSign will arrange professionals to contact you and provide service guidance within 24 hours.',\n        useSence: 'Application scenario',\n        useSenceTip: 'Such as HR, dealers, logistics documents, etc',\n        estimatedOnlineTime: 'Expected launch date',\n        requireContent: 'Requirements',\n        requireContentTip: 'Please describe how your company will use e-signature so that we can provide appropriate solution for you',\n        getSupport: 'Get professional service support',\n        callServiceHotline: 'Hotline：************',\n        useSenceNotEmpty: 'The usage scenario cannot be empty',\n        requrieContentNotEmpty: 'The demand content cannot be empty',\n        oneWeek: 'Within a week',\n        oneMonth: 'Within a month',\n        other: 'Others',\n        submitSuccess: 'Submitted successfully',\n        submitTrial: 'Submit for trial',\n        toTrial: 'To trial',\n        trialTip: 'After submitting a trial application, the current function will be immediately activated and available for trial. In order to better help you use the features, you can fill in more requirements in the form below. BestSign consultant will contact you to provide services.',\n        applyTrial: 'Apply for trial',\n        trialSuccTip: 'The function has been activated. Welcome to try it out',\n        goBuy: 'Buy now',\n        trialTipMap: {\n            title: 'Trial instructions',\n            tip1: '1. Instant use, valid for 7 days；',\n            tip2: '2. During the trial period, the function will not be charged；',\n            tip3: '3. Each company entity has only one trial opportunity for a function；',\n            tip4: '4. Self-service purchase is available during the probation period, and the use is uninterrupted;',\n            tip5: '5. If your trial has ended, you can scan the code and contact the BestSign professional consultant for details:',\n        },\n        contactAdminTip: 'To use, please contact your enterprise administrator {tip} to purchase and open',\n        trialEndTip: 'After the trial period, click to buy',\n        trialRemainDayTip: 'There are {day} days left in the trial period, click to purchase copies',\n        trialEnd: 'Trial function ended',\n        trialEndMap: {\n            deactivateTip: '{feature} feature has been disabled. Please clear the configuration or renew it before continuing to use it.',\n            feature1: 'Contract Collateral',\n            remove1: 'The method for clearing the configuration is: Edit the template - find the configured additional contract attachment data and delete it.',\n            feature2: 'Handwriting handwriting recognition',\n            remove2: 'The method for clearing the configuration is: Edit the template - Find the configured handwriting recognition and delete it.',\n            feature3: 'Contract decoration: riding seal+watermark',\n            remove3: 'The method to clear the configuration is: Edit Template - Find the configured contract decoration and delete it.',\n            feature4: 'Contract sending approval',\n            remove4: 'The method for clearing the configuration is: Enterprise Console - Deactivate all approval processes',\n        },\n    },\n    setSignPwdDialog: {\n        tip: \"After the setting is completed, the signing password will be first used by default. You may log in to BestSign's e-Signature platform and enter 'User Center' or log in to BestSign app and enter 'Account Management' to change the password.\",\n        saveAndReturnSign: 'Save and return to sign',\n        changeEmailVerify: 'Switch to Email Verification',\n        changePhoneVerify: 'Switch to Phone Verification',\n    },\n    contractCompare: {\n        reUpload: 'Re-upload',\n        title: 'Contract Comparison',\n        packagePurchase: 'Plan Purchase',\n        packagePurchaseTitle: '[{title}] Plan Purchase',\n        myPackage: 'My Plan',\n        packageDetail: 'Plan Details',\n        per: '次',\n        packageContent: 'Plan Includes：',\n        num: '{type}次数',\n        limitTime: 'Validity Period',\n        month: 'month',\n        payNow: 'Buy Now',\n        contactUs: 'Contact Us | Scan QR Code to Consult Professional Advisor',\n        compareInfo1: 'Usage Instructions：',\n        compareInfo2: '{index}、The available quota for {type} of purchases is accessible to all members of the enterprise. If you only require it for personal use, you may switch to a personal account by changing the login entity in the upper right corner.',\n        compareInfo3: '{index}、Usage Calculated Based on Number of Uploaded Contract {per}',\n        codePay: 'Please Scan QR Code to Pay',\n        aliPay: 'Alipay Payment',\n        wxPay: 'WeChat Payment',\n        payIno: 'Activated Features | Purchased For | Payment Amount',\n        finishPay: 'Payment Completed',\n        paySuccess: 'Purchase Successful',\n        originFile: 'Original Contract File',\n        compareFile: 'Contract File for Comparison',\n        documentSelect: 'Select File',\n        comparisonResult: 'Comparison Result',\n        history: 'History',\n        currentHistory: 'Document Records',\n        noData: 'No Data Available',\n        differences: '{num} Differences',\n        historyLog: '{num} Records',\n        uploadLimit: 'Drag & Drop Files to Compare | Supported Formats: PDF (including scanned), Word',\n        dragInfo: 'Release Mouse to Upload',\n        uploadError: 'Unsupported File Format',\n        pageNum: '第{page}页',\n        difference: 'Differences {num}',\n        download: 'Download Comparison Result',\n        comparing: 'Comparing Contracts...',\n        tip: 'Notification',\n        confirm: 'Confirm',\n        toBuy: 'Go to Purchase',\n        translate: 'Contract Translation',\n        doCompare: 'Compare',\n        doTranslate: 'Translate',\n        review: 'Contract Review',\n        doReview: 'Review',\n        reviewUploadFile: 'Drag Files to be Reviewed Here',\n        reviewUpload: 'Drag Review Reference Files Here | e.g., \"Distributor Management Policy\", \"Procurement Regulations\" | Supported Formats: PDF, Word',\n        reviewOriginFile: 'Contract Under Review',\n        reviewTargetFile: 'Review Basis',\n        reviewResult: 'Review Result',\n        uploadReviewFile: 'Upload Reference Files for Review',\n        risk: 'Risk Point {num}',\n        risks: '{num} Risk Point(s)',\n        startReview: 'Start Review',\n        reviewing: 'Reviewing Contract...',\n        noRisk: 'Review Completed - No Risks Detected',\n        allowUpload: 'You can upload company regulations (e.g., \"Procurement Management Policy\"), compliance guidelines, or departmental instructions to guide contract review. | Example: \"Party A must complete payment within 5 days after contract signing.\"',\n        notAllowUpload: 'Avoid vague or general statements as review basis. | Example: \"All contract terms must comply with applicable laws and regulations.\"',\n        resumeReview: 'Continue to Next File',\n        close: 'Close',\n        extract: 'Contract Extraction',\n        extractTitle: 'Keywords to Extract',\n        selectKeyword: 'Select Keywords from the List Below',\n        keyword: 'Keywords',\n        addKeyword: 'Add{keyword}',\n        introduce: '{keyword} Definition',\n        startExtract: 'Start Extraction',\n        extractTargetFile: 'Contract for Extraction',\n        extractKeyWord: 'Extract Keywords',\n        extracting: 'Extracting Contract...',\n        extractResult: 'Extraction Result',\n        extractUploadFile: 'Drag & Drop Files for Extraction Here',\n        needExtractKeyword: 'Select Keywords to Extract',\n        summary: 'Contract Summary',\n        keySummary: 'Keyword Summary',\n        deleteKeywordConfirm: 'Confirm Deletion of this Keyword?',\n        keywordPosition: 'Keyword Locations',\n        riskJudgement: 'Risk Assessment',\n        judgeTargetContract: 'Contract Under Assessment',\n        interpretTargetContract: 'AI-Interpreted Contracts',\n        startJudge: 'Start',\n        startInterpret: 'Start Now',\n        uploadText: 'Please upload the documents for risk assessment',\n        interpretText: 'Please upload documents for AI analysis',\n        startTips: 'Now we can start to judge the risks',\n        interpretTips: 'Now we can start to interpret the document',\n        infoExtract: 'Extract information',\n    },\n    batchImport: {\n        iKnow: 'I see',\n    },\n    templateCommon: {\n        tip: 'Tip',\n    },\n    mgapprovenote: {\n        SAQ: 'Questionnaire Survey',\n        analyze: 'Analysis',\n        annotate: 'Annotation',\n        law: 'Legal Clause Search',\n        case: 'Similar Case Search',\n        translate: 'Translate',\n        mark: 'mark',\n        tips: 'The above content is AI-generated and does not represent the position of ShangShangQian. It is for your reference only. Please do not delete or modify this mark.',\n        limit: 'Usage limit reached. If you need continued use, please fill out the form. Our customer service will contact you.',\n        confirmTxt: 'Go to fill',\n        content: 'Related content',\n        experience: 'Professional Experience',\n        datas: 'Relevant Data',\n        terms: 'Analogous Clauses',\n        original: 'Source',\n        export: 'Export',\n        preview: 'Contract Preview',\n        history: 'History',\n    },\n    sealConfirm: {\n        title: 'Confirm Electronic Seal Page',\n        header: 'Confirm Electronic Seal',\n        signerEnt: 'Contracting Company:',\n        abnormalSeal: 'Abnormal Electronic Seal:',\n        sealNormal: 'Seal Normal',\n        tip1: 'Please confirm if the electronic seal is normal and usable. If it is normal, you can click the \"Seal Normal\" button. Subsequently, when this company uses this seal again, the system will no longer send you abnormal notifications.',\n        tip2: 'If there is an issue with the seal, please promptly communicate with the signing party to replace the seal, resend the contract for signing, or reject and re-sign.',\n    },\n    ...console,\n    ...docList,\n    ...consts,\n    keyInfoExtract: {\n        operate: 'Extract Information',\n        contractType: 'Predicted Contract Types',\n        tooltips: 'Select the Key Information',\n        predictText: 'Predicting',\n        extractText: 'Extracting',\n        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',\n        result: 'result:',\n    },\n    judgeRisk: {\n        title: 'AI Lawyer',\n        deepInference: 'AI Legal',\n        showAll: 'Show More',\n        tips: 'Judging',\n        dialogTitle: '“AI Lawyer” Reviews Contracts',\n        aiInterpret: 'AI Interpretation',\n    },\n    sealDistribute: {\n        requestSeal: 'Request Corporate Seal Authorization',\n        company: 'Company',\n        applicant: 'Applicant',\n        accountID: 'Account ID',\n        submissionTime: 'Submission Time',\n        status: 'Status',\n        agree: 'Approved',\n        unAgree: 'Rejected',\n        ifAgree: 'If approved,',\n        applyTime: ' applicant\\'s stamp usage period is:',\n        to: 'to',\n        placeHolderTime: 'Year-Month-Day',\n        senderCompany: 'Sender Company',\n        documentTitle: 'Document Title',\n        sealApplicationScope: 'Seal Application Scope',\n        applyforSeal: 'Apply for E-Seal',\n        reject: 'Reject',\n        approve: 'Approve',\n    },\n    sealApproval: {\n        sealRight: 'Seal Permission',\n        requestSeal: 'Request Corporate Seal Authorization',\n        allEntContract: 'Contracts from All Enterprises',\n        partEntContract: 'Contracts from Selected Enterprises: ',\n        pleaseInputRight: 'Please Enter Permission',\n        successTransfer: 'After successful handover,',\n        getRight: 'will obtain the above permissions or can directly edit and assign new signing permissions.',\n        signAllEntContract: 'Sign contracts from all enterprises',\n        sign: 'Sign',\n        sendContract: 'sent contracts',\n        sealUseTime: 'Seal Usage Period: ',\n        currentStatus: 'Current Status: ',\n        takeBackSeal: 'Recall Seal',\n        agree: 'Agree',\n        hasAgree: 'Agreed',\n        hasReject: 'Rejected',\n        hasDone: 'Completed',\n        ask: ' has assigned ',\n        giveYou: \"'s seal to you\",\n        hopeAsk: 'hopes to',\n        hopeGive: 'hand over the seal to',\n        hopeGiveYou: 'hand over the relevant seal to you',\n        noSettingTime: 'No Time Setting',\n        approvalSuccess: 'Approval Successful',\n        getSealSuccess: 'Seal Obtained Successfully',\n    },\n    workspace: {\n        create: 'Created',\n        reviewing: 'Reviewing',\n        completed: 'Completed',\n        noData: 'Empty',\n        introduce: 'Meaning of the {keyword}',\n        termsDetail: 'Details',\n        extractFormat: 'Format',\n        optional: 'Optional',\n        required: 'Required',\n        operate: 'Operation',\n        detail: 'Details',\n        delete: 'Delete',\n        agreement: {\n            uploadError: 'Only PDF, DOC, or DOCX files can be uploaded!',\n            extractionRequest: 'Extraction request has been submitted. Please check the results in the terms list later.',\n            upload: 'Upload',\n            define: 'Defination',\n            extract: 'Extraction',\n            drag: 'Drag the file here or',\n            add: 'click',\n            format: 'Supported file formats are doc,docx,pdf',\n            fileName: 'FileName',\n            status: 'Status',\n            completed: 'Completed',\n            failed: 'Failed',\n            size: 'Size',\n            terms: 'Terms',\n            success: 'The extraction has been completed, totaling {total}',\n            ongoing: 'In progress...totaling {total}',\n            tips: 'Skipping this page does not affect the results of the extraction',\n            others: 'Upload other agreements',\n            result: 'Jump to the download page of the extraction result',\n            curProgress: 'Current progress: ',\n            refresh: 'Refresh',\n            details: 'Loaded {successNum}，totaling {length}',\n            start: 'Start',\n            more: 'Add',\n            skip: 'Skip',\n            tiqu: 'Start',\n            chouqu: 'Start',\n        },\n        review: {\n            distribution: 'Distribution review',\n            Incomplete: 'Incomplete',\n            createReview: 'Create Review',\n            manageReview: 'Review Management',\n            reviewDetail: 'Review Details',\n            reviewId: 'Review ID',\n            reviewStatus: 'Review Status',\n            reviewName: 'Review Name',\n            reviewStartTime: 'Review Start Time',\n            reviewCompleteTime: 'Review Complete Time',\n            reviewDesc: 'Version：V.{reviewVersion} | ReviewId：{reviewId}',\n            distribute: 'Initiate Review',\n            drag: 'Drag the agreement to be reviewed here',\n            content: 'Content',\n            current: 'Current',\n            history: 'History',\n            page: 'Page {x}：',\n            users: 'Users：',\n            message: 'Message',\n            modify: 'Modify',\n            placeholder: 'Please use semicolons to separate multiple users',\n            submit: 'Submit',\n            reupload: 'Reupload',\n            finish: 'Finish',\n            reviewSummary: 'Review Summary',\n            initiator: 'Initiator',\n            versionSummary: 'Version Summary',\n            version: 'Version',\n            versionOrder: 'Version {version}',\n            curReviewStatus: 'Current Version’s Status',\n            curReviewVersion: 'Current Version',\n            curReviewPopulation: 'Current Version’s Review Population',\n            curReviewStartTime: 'Current Version’s Review Start Time',\n            curReviewInitiator: 'Current Version’s Review Initiator',\n            checkComments: 'Check Comments',\n            overview: 'Overview',\n            reviewer: 'Reviewer',\n            reviewResult: 'Review Result',\n            replyTime: 'Reply Time',\n            agreement: 'Agreements',\n            files: 'Files',\n            fileName: 'FileName',\n            numberOfModificationSuggestions: 'Number of modification suggestions',\n            uploadTime: 'Upload Time',\n            download: 'Download',\n            dispatch: 'Dispatch',\n            recent: 'Latest review time：',\n            replyContent: 'Reply Content',\n            advice: 'Advice',\n            noIdea: 'Have no advice',\n            origin: 'Original Content: ',\n            revised: 'Revised Content',\n            suggestion: 'Suggestion: ',\n            dateMark: '{name} written in <span style=\"color: #0988EC\">Version {version}</span> on {date}',\n            unReviewed: 'Not yet reviewed',\n            revisionFiles: 'Revision Files',\n            staffReplyAggregation: 'Overview',\n            staffReply: '{name}’s Comments',\n            tips: 'Tips',\n            tipsContent: 'If you are sure to perform this operation, this review will no longer support distribution and subsequent operations. Do you want to continue?',\n            confirm: 'Confirm',\n            cancel: 'Cancel',\n            successMessage: 'Completed',\n            PASS: 'Pass',\n            REJECT: 'Reject',\n            uploadErrorMessage: 'Currently, only DOCX format files are supported for upload.',\n            successInitiated: 'Review initiated',\n            autoDistribute: 'Intelligent Distribution',\n            requiredUsers: 'Users Requiring Review',\n            contentToReview: 'Content to Review',\n            termDetails: 'Term Details',\n            term: 'Term',\n            aiDistribute: 'AI Intelligent Distribution',\n            noData: 'No Data Available',\n            docIconAlt: 'Document Icon',\n            docxIconAlt: 'DOCX Icon',\n            pdfIconAlt: 'PDF Icon',\n            requiredUsersError: 'Please fill in the users requiring review',\n            selectContentError: 'Please select the content to review',\n            initiateReviewSuccess: 'Review Initiated',\n            syncInitiated: 'Synchronization Initiated',\n        },\n        contentTracing: {\n            title: 'Content Tracing',\n            fieldContent: 'Field Content',\n            originalResult: 'Original Result',\n            contentSource: 'Content Source',\n            page: 'Page',\n        },\n    },\n    hubblePackage: {\n        title: 'My Package',\n        details: 'Package Details',\n        remainingPages: 'Remaining Total Pages',\n        pages: 'Pages',\n        usedPages: 'Used',\n        remaining: 'Available Remaining',\n        total: 'Total',\n        expiryTime: 'Expiry Time',\n        amount: 'Quantity',\n        unitPrice: 'Each',\n        copy: 'Copy',\n        words: 'Thousand Characters',\n    },\n    workspaceIndex: {\n        title: 'Workspace',\n        package: 'Package Usage',\n        agreement: 'Agreement',\n        review: 'Review',\n        term: 'Term',\n        amount: '数目',\n        unitPrice: '单价',\n    },\n    agreement: {\n        title: 'Agreement Management',\n        exportList: 'Export Agreement List',\n        exportAllChecked: 'Excel (All Fields, Selected Agreements)',\n        exportCurrentChecked: 'Excel (Current Fields, Selected Agreements)',\n        exportAllMatched: 'Excel (All Fields, Matched Conditions)',\n        exportCurrentMatched: 'Excel (Current Fields, Matched Conditions)',\n        add: 'Add Agreement',\n        upload: 'Upload Agreement',\n        operation: 'Operation',\n        download: 'Download Agreement',\n        details: 'Details',\n        delete: 'Delete',\n        relatedTaskStatus: 'Related Extraction Task Status',\n        confirmDelete: 'Are you sure to delete the current agreement?',\n        prompt: 'Prompt',\n        booleanYes: 'Yes',\n        booleanNo: 'No',\n        defaultExportName: 'export.xlsx',\n        taskNotStarted: 'Extraction Task Not Started',\n        taskStarted: 'Extraction Task Started (Content Searching)',\n        contentSearchCompleted: 'Content Search Completed (Formatting Results)',\n        resultFormattingCompleted: 'Result Formatting Completed (Verifying Results)',\n        resultVerificationCompleted: 'Result Verification Completed',\n    },\n    filter: {\n        filter: 'Filter',\n        refreshExtraction: 'Refresh Extraction',\n        extractTerms: 'Extract Term Definitions',\n        refreshList: 'Refresh List',\n        currentCondition: 'Displaying agreements under current conditions.',\n        when: 'When',\n        selectCondition: 'Please Select Condition',\n        enterCondition: 'Please Enter Condition',\n        yes: 'Yes',\n        no: 'No',\n        addCondition: 'Add Condition',\n        reset: 'Reset',\n        confirm: 'Confirm',\n        and: 'And',\n        or: 'Or',\n        equals: 'Equals',\n        notEquals: 'Not Equals',\n        contains: 'Contains',\n        notContains: 'Does Not Contain',\n        greaterThan: 'Greater Than',\n        greaterThanOrEquals: 'Greater Than or Equals',\n        lessThan: 'Less Than',\n        lessThanOrEquals: 'Less Than or Equals',\n        emptyCondition: 'Filter condition cannot be empty',\n    },\n    fieldConfig: {\n        button: 'Field Configuration',\n        header: 'Fields to be displayed',\n        submit: 'Submit',\n        cancel: 'Cancel',\n    },\n    agreementDetail: {\n        detail: 'Agreement Details',\n        add: 'Add Agreement',\n        id: 'Agreement ID',\n        file: 'Agreement File',\n        download: 'Download Agreement',\n        replaceFile: 'Replace Agreement File',\n        uploadFile: 'Upload Agreement File',\n        relatedExtractionStatus: 'Related Extraction Task Status',\n        dataSource: 'Data Source',\n        yes: 'Yes',\n        no: 'No',\n        select: 'Please Select',\n        input: 'Please Input',\n        save: 'Save',\n        cancel: 'Cancel',\n        page: 'Page {page}',\n        addDataSource: 'Add Data Source',\n        pageNo: 'Page',\n        pageSuffix: '',\n        submit: 'Submit',\n        inputDataSource: 'Please input data source content',\n        pageFormatError: 'Page number only supports comma-separated numbers or pure numbers',\n        confirmDelete: 'Are you sure to delete the current data source?',\n        tips: 'Tips',\n        uploadSuccess: 'Upload Successful',\n    },\n    termManagement: {\n        title: 'Term Management',\n        batchDelete: 'Batch Delete',\n        import: 'Import Terms',\n        export: 'Export Terms',\n        add: '+ Add Term',\n        name: 'Term Name',\n        definition: 'Term Definition',\n        formatRequirement: 'Extraction Format Requirement',\n        dataFormat: 'Data Format',\n        operation: 'Operation',\n        edit: 'Edit',\n        delete: 'Delete',\n        detail: 'Term Details',\n        addTitle: 'Add Term',\n        namePlaceholder: 'Enter your specialized term',\n        definitionPlaceholder: 'Enter the definition of the term',\n        formatRequirementPlaceholder: 'Enter the extraction format requirement of the term',\n        dataFormatPlaceholder: 'Expected extracted term format',\n        cancel: 'Cancel',\n        confirmEdit: 'Confirm Edit',\n        importTitle: 'Import Terms',\n        uploadTemplate: 'Upload Term Template',\n        downloadTemplate: 'Download Term Template',\n        extractType: {\n            text: 'Text',\n            longText: 'Long Text',\n            date: 'Date',\n            number: 'Number',\n            boolean: 'Yes/No',\n        },\n        importSuccess: 'Import Successful',\n        deleteConfirm: 'Are you sure to delete the current term?',\n        prompt: 'Prompt',\n        nameEmptyError: 'Term name cannot be empty',\n    },\n    agent: {\n        extractTitle: 'Information Extraction',\n        riskTitle: 'AI Lawyer',\n        feedback: 'Questionnaire',\n        toMini: 'Check Details in App',\n        otherContract: 'Analyze latent risks in other contracts?',\n        others: 'Others',\n        submit: 'Submit',\n        autoExtract: 'Automatic Extraction Until Completion',\n        autoRisk: 'Auto-Analysis Process Running',\n        aiGenerated: 'AI Generated - © BestSign',\n        chooseRisk: 'Select File for Analysis',\n        chooseExtract: 'Specify Source File for Extraction',\n        analyzing: 'Analyzing',\n        advice: 'Generating Revision Suggestions',\n        options: 'Generating Options',\n        inputTips: 'Enter Precise Information',\n        chargeTip: 'Insufficient Balance - Top Up Required',\n        original: 'Original',\n        revision: 'Revision',\n        diff: 'Comparison',\n        locate: 'Locating',\n        custom: 'Please enter custom review rules',\n        content: 'Locate Original Text',\n        satisfy: 'Satisfied with the analysis results, continue to the next analysis',\n        dissatisfy: 'Not satisfied with the analysis results, re-analyze',\n        selectFunc: 'Please select the function you wish to use',\n        deepInference: 'AI Legal',\n        deepThinking: 'Deep Thinking',\n        deepThoughtCompleted: 'Deep Thinking Completed',\n        reJudge: 'Re-judge',\n        confirm: 'Confirm',\n        tipsContent: 'Proceeding will deduct from your usage credits. Continue?',\n        useLawyer: 'Use AI Lawyer',\n        interpretFinish: 'AI Analysis Complete',\n        exportPDF: 'Export PDF Report',\n        defaultExportName: 'export.pdf',\n        exporting: 'Exporting report... Please wait',\n    },\n    authorize: {\n        title: 'Terms of Use',\n        content: 'Unlock smart contracts - AI analysis boosts efficiency!Agree to start your free trial',\n        cancel: 'Not Now',\n        confirm: 'Accept & Start',\n        contract: 'View Hubble Product Terms of Use',\n    },\n    hubbleEntry: {\n        smartAdvisor: 'Smart Contract Advisor',\n        tooltips: 'This feature is not enabled. Contact BestSign e-signature advisors to purchase access.',\n        confirm: 'Got it',\n    },\n    lang: 'en',\n};\n"], "mappings": "AACA;AACA,OAAOA,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,sCAAsC;AAE7D,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,cAAc,MAAM,8CAA8C;AACzE,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,eAAe;EACX,GAAGV,KAAK;EACR,GAAGC,KAAK;EACR,GAAGC,UAAU;EACb,GAAGO,cAAc;EACjBE,QAAQ,EAAE;IACNC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,qDAAqD;IAC/DC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,kBAAkB;IAC9BC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACd,CAAC;EACDC,YAAY,EAAE;IACVC,MAAM,EAAE,wDAAwD;IAChEC,cAAc,EAAE,4BAA4B;IAC5CC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,aAAa;IAC1BC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,wBAAwB;IACjCC,OAAO,EAAE,0BAA0B;IACnCC,UAAU,EAAE,kCAAkC;IAC9CC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,iBAAiB;IAC9BC,UAAU,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE,gBAAgB;IAC1BC,WAAW,EAAE,sBAAsB;IACnCC,WAAW,EAAE,mBAAmB;IAChCC,cAAc,EAAE,+BAA+B;IAC/CC,SAAS,EAAE,iBAAiB;IAC5BC,WAAW,EAAE,mCAAmC;IAChDC,WAAW,EAAE,mBAAmB;IAChCC,YAAY,EAAE,iDAAiD;IAC/DC,WAAW,EAAE,wCAAwC;IACrDC,WAAW,EAAE,uBAAuB;IACpCC,SAAS,EAAE,SAAS;IACpBX,KAAK,EAAE,SAAS;IAChBY,SAAS,EAAE,aAAa;IACxBC,WAAW,EAAE,aAAa;IAC1BC,kBAAkB,EAAE,oBAAoB;IACxCC,mBAAmB,EAAE,UAAU;IAC/BC,UAAU,EAAE,2CAA2C;IACvDC,qBAAqB,EAAE,2BAA2B;IAClDC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,oBAAoB;IAChCC,EAAE,EAAE,IAAI;IACRC,kBAAkB,EAAE,6DAA6D;IACjFC,mBAAmB,EAAE,qDAAqD;IAC1EC,aAAa,EAAE,iDAAiD;IAChEC,MAAM,EAAE,gCAAgC;IACxCC,gBAAgB,EAAE,yBAAyB;IAC3CC,cAAc,EAAE,iCAAiC;IACjDC,oBAAoB,EAAE,wCAAwC;IAC9DC,WAAW,EAAE,4BAA4B;IACzCC,YAAY,EAAE,sDAAsD;IACpEC,aAAa,EAAE,8BAA8B;IAE7CC,SAAS,EAAE,yBAAyB;IACpCC,OAAO,EAAE,4BAA4B;IACrCC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE,WAAW;IACvBC,MAAM,EAAE,gGAAgG;IACxGC,QAAQ,EAAE,wBAAwB;IAClCC,iBAAiB,EAAE,4BAA4B;IAC/CC,GAAG,EAAE,KAAK;IACVC,2BAA2B,EAAE,qCAAqC;IAClEC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,mBAAmB;IAC5BC,WAAW,EAAE,0CAA0C;IACvDC,OAAO,EAAE,kCAAkC;IAC3CC,QAAQ,EAAE,+CAA+C;IACzDC,SAAS,EAAE,wHAAwH;IACnIC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,8DAA8D;IAC1EC,QAAQ,EAAE,UAAU;IACpBC,mBAAmB,EAAE,wCAAwC;IAC7D;IACAC,aAAa,EAAE,4CAA4C;IAC3DC,mBAAmB,EAAE,wEAAwE;IAC7FC,YAAY,EAAE,qCAAqC;IACnDC,YAAY,EAAE,2CAA2C;IACzDC,qBAAqB,EAAE,+BAA+B;IACtDC,QAAQ,EAAE,eAAe;IACzBC,mBAAmB,EAAE,gDAAgD;IACrEC,sBAAsB,EAAE,2BAA2B;IACnD;IACAC,oBAAoB,EAAE,wBAAwB;IAC9CC,gBAAgB,EAAE,6BAA6B;IAC/CC,mBAAmB,EAAE,gCAAgC;IACrDC,UAAU,EAAE,2BAA2B;IACvCC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE,2BAA2B;IACvCC,WAAW,EAAE,oCAAoC;IACjDC,iBAAiB,EAAE;EACvB,CAAC;EACD,GAAG/F,IAAI;EACPgG,SAAS,EAAE;IACP3F,KAAK,EAAE,wBAAwB;IAC/B4F,YAAY,EAAE,yCAAyC;IACvDC,cAAc,EAAE,gBAAgB;IAChCC,mBAAmB,EAAE,yBAAyB;IAC9CC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,2BAA2B;IACzCC,OAAO,EAAE,4JAA4J;IACrKC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE,kCAAkC;IAC/CC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,8FAA8F;IAC1GC,UAAU,EAAE,gEAAgE;IAC5ElG,MAAM,EAAE,QAAQ;IAChBmG,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,wEAAwE;IACxFC,SAAS,EAAE,8CAA8C;IACzDC,SAAS,EAAE,8BAA8B;IACzCC,WAAW,EAAE,kDAAkD;IAC/DC,MAAM,EAAE,2BAA2B;IACnCC,MAAM,EAAE,oBAAoB;IAC5BC,EAAE,EAAE,IAAI;IACRC,SAAS,EAAE,+CAA+C;IAC1DC,OAAO,EAAE,yEAAyE;IAClFC,OAAO,EAAE,8CAA8C;IACvDC,YAAY,EAAE,yGAAyG;IACvHC,iBAAiB,EAAE,mDAAmD;IACtEC,GAAG,EAAE;MACDC,aAAa,EAAE,6FAA6F;MAC5GC,aAAa,EAAE,4EAA4E;MAC3FC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,MAAM,EAAE;IACJ9G,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,aAAa;IAC1BE,SAAS,EAAE,WAAW;IACtB2G,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE,mCAAmC;IAChDC,MAAM,EAAE,6CAA6C;IACrDnH,YAAY,EAAE,eAAe;IAC7BM,OAAO,EAAE,wBAAwB;IACjCF,IAAI,EAAE,aAAa;IAEnBgC,aAAa,EAAE,iDAAiD;IAChEE,gBAAgB,EAAE,yBAAyB;IAC3C8E,WAAW,EAAE,uBAAuB;IACpCC,kBAAkB,EAAE,uCAAuC;IAC3DC,gBAAgB,EAAE,wCAAwC;IAC1DC,oBAAoB,EAAE,4CAA4C;IAClEC,iBAAiB,EAAE,iCAAiC;IACpDC,UAAU,EAAE,yBAAyB;IACrCC,iBAAiB,EAAE,oCAAoC;IACvDC,kBAAkB,EAAE,iCAAiC;IACrDC,sBAAsB,EAAE;EAC5B,CAAC;EACD5I,OAAO,EAAE;IACL,GAAGA,OAAO;IACV6I,gBAAgB,EAAE,oCAAoC;IACtDC,WAAW,EAAE,0BAA0B;IACvCC,YAAY,EAAE,qBAAqB;IACnCC,OAAO,EAAE,SAAS;IAClBrC,MAAM,EAAE,iBAAiB;IACzBsC,WAAW,EAAE,4EAA4E;IACzFC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,MAAM;IACZ/H,IAAI,EAAE,MAAM;IACZgI,OAAO,EAAE,iBAAiB;IAC1BC,sBAAsB,EAAE,gFAAgF;IACxGC,aAAa,EAAE,oBAAoB;IACnCC,UAAU,EAAE,yBAAyB;IACrCC,OAAO,EAAE,iBAAiB;IAC1BC,YAAY,EAAE,uBAAuB;IACrCC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,2BAA2B;IACvCC,eAAe,EAAE,8BAA8B;IAC/CC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,kCAAkC;IAClDC,sBAAsB,EAAE,yBAAyB;IACjDC,gBAAgB,EAAE,4CAA4C;IAC9DC,SAAS,EAAE,YAAY;IACvBC,IAAI,EAAE,sBAAsB;IAC5BC,eAAe,EAAE,yFAAyF;IAC1GvJ,QAAQ,EAAE,UAAU;IACpBwJ,aAAa,EAAE,sEAAsE;IACrFC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACVC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE;EACpB,CAAC;EACD3K,IAAI,EAAE;IACF,GAAGA,IAAI;IACPA,IAAI,EAAE,MAAM;IACZ4K,gBAAgB,EAAE,mBAAmB;IACrCC,kBAAkB,EAAE,UAAU;IAC9BC,UAAU,EAAE,OAAO;IACnBC,OAAO,EAAE,SAAS;IAClBC,iBAAiB,EAAE,oBAAoB;IACvCC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,sCAAsC;IACpDC,YAAY,EAAE,WAAW;IACzBC,SAAS,EAAE,4CAA4C;IACvDC,OAAO,EAAE,4CAA4C;IACrDC,OAAO,EAAE,8CAA8C;IACvDC,WAAW,EAAE,mCAAmC;IAChDC,mBAAmB,EAAE,sDAAsD;IAC3EC,IAAI,EAAE,mBAAmB;IACzBC,gBAAgB,EAAE,mBAAmB;IACrCC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,yBAAyB;IACpCC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,wCAAwC;IAC9CC,GAAG,EAAE,aAAa;IAClBC,eAAe,EAAE,yBAAyB;IAC1CC,GAAG,EAAE,EAAE;IACPC,QAAQ,EAAE,gGAAgG;IAC1GC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE,4GAA4G;IAClHC,QAAQ,EAAE;MACN,CAAC,EAAE,wCAAwC;MAC3C,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,0BAA0B;MAC7B,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,mBAAmB;IACpCC,SAAS,EAAE;MACP,CAAC,EAAE,oBAAoB;MACvB,CAAC,EAAE,sBAAsB;MACzB,CAAC,EAAE,2BAA2B;MAC9B,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACPC,gBAAgB,EAAE,8BAA8B;IAChDC,aAAa,EAAE,wBAAwB;IACvCC,QAAQ,EAAE,wBAAwB;IAClCC,yBAAyB,EAAE,mFAAmF;IAC9GC,iBAAiB,EAAE,mBAAmB;IACtCC,kBAAkB,EAAE,mCAAmC;IACvDC,YAAY,EAAE;MACV/C,IAAI,EAAE,MAAM;MACZgD,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;IACd,CAAC;IACDC,OAAO,EAAE,OAAO;IAChBC,cAAc,EAAE,qBAAqB;IACrCC,gBAAgB,EAAE,mBAAmB;IACrCC,aAAa,EAAE,gBAAgB;IAC/BC,iBAAiB,EAAE,mCAAmC;IACtDC,wBAAwB,EAAE,uCAAuC;IACjEC,WAAW,EAAE;MACTC,MAAM,EAAE,iBAAiB;MACzBC,MAAM,EAAE,mBAAmB;MAC3BC,MAAM,EAAE;IACZ,CAAC;IACDC,YAAY,EAAE,wBAAwB;IACtCC,cAAc,EAAE,gEAAgE;IAChFC,yBAAyB,EAAE,iCAAiC;IAC5DC,aAAa,EAAE,yBAAyB;IACxCC,aAAa,EAAE,cAAc;IAC7BC,0BAA0B,EAAE,+EAA+E;IAC3GC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,eAAe;IACzBC,0BAA0B,EAAE,2CAA2C;IACvEC,kBAAkB,EAAE,0BAA0B;IAC9CC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,QAAQ;IAChBC,kBAAkB,EAAE,kBAAkB;IACtCC,eAAe,EAAE;MACbC,UAAU,EAAE,sBAAsB;MAClCC,4BAA4B,EAAE,6BAA6B;MAC3DC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE;IACd,CAAC;IACDC,yBAAyB,EAAE;MACvBhP,KAAK,EAAE,mOAAmO;MAC1OiP,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,sBAAsB;MAC/B3O,MAAM,EAAE;IACZ,CAAC;IACD4O,WAAW,EAAE;MACTnP,KAAK,EAAE,oHAAoH;MAC3HiP,IAAI,EAAE,MAAM;MACZvI,OAAO,EAAE,SAAS;MAClBnG,MAAM,EAAE;IACZ,CAAC;IACD6O,2BAA2B,EAAE,4EAA4E;IACzGC,SAAS,EAAE,mBAAmB;IAC9BC,sBAAsB,EAAE,qDAAqD;IAC7EC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,mBAAmB;IAC9BC,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,cAAc;IACzBC,QAAQ,EAAE,qBAAqB;IAC/BC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,gBAAgB;IAC/BC,OAAO,EAAE,gBAAgB;IACzBC,kBAAkB,EAAE,sBAAsB;IAC1CC,aAAa,EAAE,mCAAmC;IAClDC,kBAAkB,EAAE,sBAAsB;IAC1CC,gBAAgB,EAAE,wBAAwB;IAC1CC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,sBAAsB;IACpCC,SAAS,EAAE,mBAAmB;IAC9BnF,WAAW,EAAE,cAAc;IAC3BuC,MAAM,EAAE,QAAQ;IAChB6C,aAAa,EAAE,kBAAkB;IACjCC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,kBAAkB;IAChCC,kBAAkB,EAAE,0BAA0B;IAC9C;IACAC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBlG,YAAY,EAAE,eAAe;IAC7BmG,MAAM,EAAE,QAAQ;IAChBrR,IAAI,EAAE,MAAM;IACZsR,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,gBAAgB,EAAE,uBAAuB;IACzCC,cAAc,EAAE,4BAA4B;IAC5CC,KAAK,EAAE,OAAO;IACdC,mBAAmB,EAAE,6GAA6G;IAClIC,UAAU,EAAE,iBAAiB;IAC7BC,cAAc,EAAE,kBAAkB;IAClCC,UAAU,EAAE,aAAa;IACzBC,cAAc,EAAE,qBAAqB;IACrCC,YAAY,EAAE,kBAAkB;IAChCC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,oBAAoB,EAAE,uBAAuB;IAC7CC,eAAe,EAAE,kBAAkB;IACnCC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBjF,QAAQ,EAAE,UAAU;IACpBkF,cAAc,EAAE,2BAA2B;IAC3CC,mBAAmB,EAAE,uBAAuB;IAC5CC,SAAS,EAAE,mBAAmB;IAC9BC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,YAAY;IAClB7E,MAAM,EAAE,QAAQ;IAChB8E,WAAW,EAAE,iBAAiB;IAC9BC,kBAAkB,EAAE,gDAAgD;IACpE3F,IAAI,EAAE,MAAM;IACZ4F,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,OAAO;IACdlH,IAAI,EAAE,MAAM;IACZmH,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,SAAS;IACjBC,6BAA6B,EAAE,kCAAkC;IACjEC,gCAAgC,EAAE,qCAAqC;IACvEC,wBAAwB,EAAE,sCAAsC;IAChEC,sBAAsB,EAAE,iCAAiC;IACzDC,cAAc,EAAE,SAAS;IACzBC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,QAAQ;IACtBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,eAAe,EAAE,iBAAiB;IAClCC,WAAW,EAAE,YAAY;IACzBC,aAAa,EAAE,uBAAuB;IACtCC,aAAa,EAAE,kBAAkB;IACjCC,KAAK,EAAE,WAAW;IAClBC,qBAAqB,EAAE,4BAA4B;IACnDC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,QAAQ;IACpBC,SAAS,EAAE,0BAA0B;IACrCC,OAAO,EAAE,iBAAiB;IAC1BC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,mBAAmB;IACrCC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,WAAW;IACtBpO,GAAG,EAAE,KAAK;IACV0D,IAAI,EAAE,MAAM;IACZ2K,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,OAAO;IACdC,cAAc,EAAE,kBAAkB;IAClCC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,gBAAgB;IAC1BC,UAAU,EAAE,mEAAmE;IAC/EC,UAAU,EAAE,uBAAuB;IACnCC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,kBAAkB;IAC5B7J,SAAS,EAAE,WAAW;IACtB8J,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACRC,UAAU,EAAE,WAAW;IACvBC,WAAW,EAAE,WAAW;IACxBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,GAAG,EAAE,KAAK;IACVC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE,iBAAiB;IAC7BR,UAAU,EAAE,mBAAmB;IAC/BS,QAAQ,EAAE,qDAAqD;IAC/DC,oBAAoB,EAAE,+BAA+B;IACrDC,UAAU,EAAE,mBAAmB;IAC/BC,cAAc,EAAE,6CAA6C;IAC7DC,gBAAgB,EAAE,gCAAgC;IAClDC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EAEd,CAAC;EACDlG,YAAY,EAAE;IACVmG,cAAc,EAAE,0BAA0B;IAC1CC,YAAY,EAAE,eAAe;IAC7BC,mBAAmB,EAAE,6FAA6F;IAClH5L,YAAY,EAAE,eAAe;IAC7B6L,QAAQ,EAAE,eAAe;IACzBC,eAAe,EAAE,iFAAiF;IAClGC,YAAY,EAAE,kBAAkB;IAChCC,mBAAmB,EAAE,wEAAwE;IAC7FC,UAAU,EAAE,sBAAsB;IAClCnG,kBAAkB,EAAE,0BAA0B;IAC9CoG,iBAAiB,EAAE,yFAAyF;IAC5GC,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,UAAU;IACxBC,QAAQ,EAAE,wFAAwF;IAClGC,gBAAgB,EAAE,wDAAwD;IAC1EC,sBAAsB,EAAE;EAC5B,CAAC;EACD5X,WAAW,EAAEA,WAAW;EACxB6X,QAAQ,EAAE;IACNC,YAAY,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACDC,qBAAqB,EAAE;MACnBxX,KAAK,EAAE,4CAA4C;MACnDyX,cAAc,EAAE,4GAA4G;MAC5HC,SAAS,EAAE,sXAAsX;MACjYC,SAAS,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACN/B,GAAG,EAAE,0IAA0I;MAC/IgC,IAAI,EAAE;QACF,CAAC,EAAE,iCAAiC;QACpC,CAAC,EAAE,4LAA4L;QAC/L,CAAC,EAAE,gEAAgE;QACnE,CAAC,EAAE,wCAAwC;QAC3C,CAAC,EAAE,sHAAsH;QACzH,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,aAAa,EAAE;MACX9X,KAAK,EAAE,WAAW;MAClBoK,IAAI,EAAE,wMAAwM;MAC9M2N,IAAI,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE;MACX,CAAC;MACDC,IAAI,EAAE;QACFJ,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDE,IAAI,EAAE;QACFL,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDG,IAAI,EAAE;QACFN,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDI,KAAK,EAAE;IACH/D,SAAS,EAAE;MACPgE,IAAI,EAAE;QACFC,CAAC,EAAE,GAAG;QACNC,QAAQ,EAAE;MACd;IACJ;EACJ,CAAC;EACDC,QAAQ,EAAE;IACN3Y,KAAK,EAAE,cAAc;IACrB4S,MAAM,EAAE,sGAAsG;IAC9GgG,QAAQ,EAAE,mBAAmB;IAC7BC,cAAc,EAAE,oCAAoC;IACpDC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6EAA6E;IAC7FC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,qFAAqF;IAC/FC,OAAO,EAAE,uCAAuC;IAChDC,OAAO,EAAE,kCAAkC;IAC3C/R,OAAO,EAAE;EACb,CAAC;EACDgS,mBAAmB,EAAE;IACjBpZ,KAAK,EAAE,MAAM;IACbqZ,IAAI,EAAE,KAAK;IACXC,EAAE,EAAE,OAAO;IACXC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,qCAAqC;IACjDC,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,iBAAiB;IACtBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,0BAA0B;IAC3CC,aAAa,EAAE;EACnB,CAAC;EACDC,aAAa,EAAE;IACX3a,KAAK,EAAE,OAAO;IACdqZ,IAAI,EAAE,MAAM;IACZC,EAAE,EAAE,QAAQ;IACZI,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCI,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,qCAAqC;IAClDJ,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,WAAW;IACjBI,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE,OAAO;IACfc,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE,sBAAsB;IAC7BC,gBAAgB,EAAE,gCAAgC;IAClDC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,eAAe;IACnCC,GAAG,EAAE,IAAI;IACT9R,OAAO,EAAE,MAAM;IACfoC,OAAO,EAAE,IAAI;IACb2P,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,cAAc;IAC1BC,cAAc,EAAE;EACpB,CAAC;EACDC,uBAAuB,EAAE;IACrBC,MAAM,EAAE,wBAAwB;IAChC1U,MAAM,EAAE,kBAAkB;IAC1B9G,QAAQ,EAAE,mDAAmD;IAC7DC,QAAQ,EAAE,sBAAsB;IAChCwb,aAAa,EAAE,uBAAuB;IACtCC,cAAc,EAAE,SAAS;IACzBC,KAAK,EAAE;EACX,CAAC;EACDC,kBAAkB,EAAE;IAChBtb,MAAM,EAAE,IAAI;IACZmG,OAAO,EAAE,IAAI;IACboV,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,qBAAqB;IACxCC,YAAY,EAAE,mCAAmC;IACjDC,SAAS,EAAE,aAAa;IACxBC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE;EACnB,CAAC;EACDC,WAAW,EAAE;IACTC,SAAS,EAAE,WAAW;IACtBhb,GAAG,EAAE,UAAU;IACfib,WAAW,EAAE,qCAAqC;IAClDC,eAAe,EAAE,iEAAiE;IAClFC,cAAc,EAAE,oDAAoD;IACpEC,gBAAgB,EAAE;EACtB,CAAC;EACDC,mBAAmB,EAAE;IACjB1c,KAAK,EAAE,gBAAgB;IACvB2c,QAAQ,EAAE,UAAU;IACpBC,YAAY,EAAE,6BAA6B;IAC3C9N,OAAO,EAAE;EACb,CAAC;EACD+N,mBAAmB,EAAE;IACjBC,mBAAmB,EAAE,kDAAkD;IACvEC,eAAe,EAAE,oIAAoI;IACrJC,QAAQ,EAAE,oDAAoD;IAC9DC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,mCAAmC;IAChD3c,MAAM,EAAE,QAAQ;IAChBmG,OAAO,EAAE;EACb,CAAC;EACDyW,WAAW,EAAE;IACT5c,MAAM,EAAE,QAAQ;IAChBmG,OAAO,EAAE,SAAS;IAClBgQ,QAAQ,EAAE,eAAe;IACzB0G,IAAI,EAAE,OAAO;IACb5I,SAAS,EAAE,MAAM;IACjB6I,QAAQ,EAAE,MAAM;IAChB7E,IAAI,EAAE,MAAM;IACZ1O,IAAI,EAAE,MAAM;IACZwT,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,eAAe;IAC7BC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,iCAAiC;IAC3CC,QAAQ,EAAE,6BAA6B;IACvCC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,qBAAqB;IAClChI,GAAG,EAAE,MAAM;IACXiI,aAAa,EAAE,uDAAuD;IACtEC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE;EACd,CAAC;EACDC,SAAS,EAAE;IACPC,OAAO,EAAE,gBAAgB;IACzBC,gBAAgB,EAAE,UAAU;IAC5BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,uBAAuB;IACjCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,oDAAoD;IAC/DC,SAAS,EAAE,uBAAuB;IAClCC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,UAAU;IACtBC,cAAc,EAAE,cAAc;IAC9BC,YAAY,EAAE,SAAS;IACvBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,iBAAiB;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,2BAA2B;IACzCC,WAAW,EAAE,6GAA6G;IAC1HC,uBAAuB,EAAE,iCAAiC;IAC1DC,YAAY,EAAE,kIAAkI;IAChJC,iBAAiB,EAAE;EACvB,CAAC;EACDC,qBAAqB,EAAE;IACnB1f,KAAK,EAAE,uBAAuB;IAC9B2f,QAAQ,EAAE,mLAAmL;IAC7LC,QAAQ,EAAE,sBAAsB;IAChCC,WAAW,EAAE,+CAA+C;IAC5DC,mBAAmB,EAAE,sBAAsB;IAC3CC,cAAc,EAAE,cAAc;IAC9BC,iBAAiB,EAAE,2GAA2G;IAC9HC,UAAU,EAAE,kCAAkC;IAC9CC,kBAAkB,EAAE,sBAAsB;IAC1CC,gBAAgB,EAAE,oCAAoC;IACtDC,sBAAsB,EAAE,oCAAoC;IAC5DC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE,gBAAgB;IAC1BC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE,wBAAwB;IACvCC,WAAW,EAAE,kBAAkB;IAC/BC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,gRAAgR;IAC1RC,UAAU,EAAE,iBAAiB;IAC7BC,YAAY,EAAE,wDAAwD;IACtEC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE;MACT/gB,KAAK,EAAE,oBAAoB;MAC3B+X,IAAI,EAAE,mCAAmC;MACzCK,IAAI,EAAE,+DAA+D;MACrEC,IAAI,EAAE,uEAAuE;MAC7EC,IAAI,EAAE,kGAAkG;MACxG0I,IAAI,EAAE;IACV,CAAC;IACDC,eAAe,EAAE,iFAAiF;IAClGC,WAAW,EAAE,sCAAsC;IACnDC,iBAAiB,EAAE,yEAAyE;IAC5FC,QAAQ,EAAE,sBAAsB;IAChCC,WAAW,EAAE;MACTC,aAAa,EAAE,8GAA8G;MAC7HC,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE,0IAA0I;MACnJC,QAAQ,EAAE,qCAAqC;MAC/CC,OAAO,EAAE,8HAA8H;MACvIC,QAAQ,EAAE,4CAA4C;MACtDC,OAAO,EAAE,kHAAkH;MAC3HC,QAAQ,EAAE,2BAA2B;MACrCC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdlM,GAAG,EAAE,gPAAgP;IACrPmM,iBAAiB,EAAE,yBAAyB;IAC5CC,iBAAiB,EAAE,8BAA8B;IACjDC,iBAAiB,EAAE;EACvB,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE,WAAW;IACrBpiB,KAAK,EAAE,qBAAqB;IAC5BqiB,eAAe,EAAE,eAAe;IAChCC,oBAAoB,EAAE,yBAAyB;IAC/CC,SAAS,EAAE,SAAS;IACpBC,aAAa,EAAE,cAAc;IAC7BC,GAAG,EAAE,GAAG;IACRC,cAAc,EAAE,gBAAgB;IAChC9W,GAAG,EAAE,UAAU;IACf+W,SAAS,EAAE,iBAAiB;IAC5BC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,2DAA2D;IACtEC,YAAY,EAAE,qBAAqB;IACnCC,YAAY,EAAE,2OAA2O;IACzPC,YAAY,EAAE,qEAAqE;IACnFC,OAAO,EAAE,4BAA4B;IACrCC,MAAM,EAAE,gBAAgB;IACxBC,KAAK,EAAE,gBAAgB;IACvBC,MAAM,EAAE,qDAAqD;IAC7DC,SAAS,EAAE,mBAAmB;IAC9BC,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE,wBAAwB;IACpCC,WAAW,EAAE,8BAA8B;IAC3CC,cAAc,EAAE,aAAa;IAC7BC,gBAAgB,EAAE,mBAAmB;IACrCC,OAAO,EAAE,SAAS;IAClBC,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,mBAAmB;IAC3BC,WAAW,EAAE,mBAAmB;IAChCC,UAAU,EAAE,eAAe;IAC3Bnb,WAAW,EAAE,iFAAiF;IAC9Fob,QAAQ,EAAE,yBAAyB;IACnCC,WAAW,EAAE,yBAAyB;IACtCC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,mBAAmB;IAC/BnX,QAAQ,EAAE,4BAA4B;IACtCoX,SAAS,EAAE,wBAAwB;IACnCxO,GAAG,EAAE,cAAc;IACnBnP,OAAO,EAAE,SAAS;IAClB4d,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,iBAAiB;IACzBC,QAAQ,EAAE,QAAQ;IAClBC,gBAAgB,EAAE,gCAAgC;IAClDC,YAAY,EAAE,oIAAoI;IAClJC,gBAAgB,EAAE,uBAAuB;IACzCC,gBAAgB,EAAE,cAAc;IAChCC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,mCAAmC;IACrDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,uBAAuB;IAClCC,MAAM,EAAE,sCAAsC;IAC9CC,WAAW,EAAE,4OAA4O;IACzPC,cAAc,EAAE,sIAAsI;IACtJC,YAAY,EAAE,uBAAuB;IACrC5R,KAAK,EAAE,OAAO;IACd6R,OAAO,EAAE,qBAAqB;IAC9BC,YAAY,EAAE,qBAAqB;IACnCC,aAAa,EAAE,qCAAqC;IACpDC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,kBAAkB;IAChCC,iBAAiB,EAAE,yBAAyB;IAC5CC,cAAc,EAAE,kBAAkB;IAClCC,UAAU,EAAE,wBAAwB;IACpCC,aAAa,EAAE,mBAAmB;IAClCC,iBAAiB,EAAE,uCAAuC;IAC1DC,kBAAkB,EAAE,4BAA4B;IAChDC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,iBAAiB;IAC7BC,oBAAoB,EAAE,mCAAmC;IACzDC,eAAe,EAAE,mBAAmB;IACpCC,aAAa,EAAE,iBAAiB;IAChCC,mBAAmB,EAAE,2BAA2B;IAChDC,uBAAuB,EAAE,0BAA0B;IACnDC,UAAU,EAAE,OAAO;IACnBC,cAAc,EAAE,WAAW;IAC3BC,UAAU,EAAE,iDAAiD;IAC7DC,aAAa,EAAE,yCAAyC;IACxDC,SAAS,EAAE,qCAAqC;IAChDC,aAAa,EAAE,4CAA4C;IAC3DC,WAAW,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACTzL,KAAK,EAAE;EACX,CAAC;EACD0L,cAAc,EAAE;IACZzR,GAAG,EAAE;EACT,CAAC;EACD0R,aAAa,EAAE;IACXC,GAAG,EAAE,sBAAsB;IAC3BC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,YAAY;IACtBC,GAAG,EAAE,qBAAqB;IAC1BC,IAAI,EAAE,qBAAqB;IAC3BrD,SAAS,EAAE,WAAW;IACtBxY,IAAI,EAAE,MAAM;IACZ6N,IAAI,EAAE,mKAAmK;IACzKiO,KAAK,EAAE,kHAAkH;IACzHC,UAAU,EAAE,YAAY;IACxBhZ,OAAO,EAAE,iBAAiB;IAC1BiZ,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE,eAAe;IACtBvK,KAAK,EAAE,mBAAmB;IAC1BwK,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,kBAAkB;IAC3BvE,OAAO,EAAE;EACb,CAAC;EACDwE,WAAW,EAAE;IACTpoB,KAAK,EAAE,8BAA8B;IACrCqoB,MAAM,EAAE,yBAAyB;IACjCC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,2BAA2B;IACzCC,UAAU,EAAE,aAAa;IACzBzQ,IAAI,EAAE,uOAAuO;IAC7OK,IAAI,EAAE;EACV,CAAC;EACD,GAAG7Y,OAAO;EACV,GAAGE,OAAO;EACV,GAAGK,MAAM;EACT2oB,cAAc,EAAE;IACZ1b,OAAO,EAAE,qBAAqB;IAC9BlC,YAAY,EAAE,0BAA0B;IACxC6d,QAAQ,EAAE,4BAA4B;IACtCC,WAAW,EAAE,YAAY;IACzBC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,yJAAyJ;IACvKC,MAAM,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACP/oB,KAAK,EAAE,WAAW;IAClBgpB,aAAa,EAAE,UAAU;IACzBC,OAAO,EAAE,WAAW;IACpBrP,IAAI,EAAE,SAAS;IACfsP,WAAW,EAAE,+BAA+B;IAC5CC,WAAW,EAAE;EACjB,CAAC;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,sCAAsC;IACnDnoB,OAAO,EAAE,SAAS;IAClBooB,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,iBAAiB;IACjCC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,UAAU;IACnBC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE,sCAAsC;IACjDC,EAAE,EAAE,IAAI;IACRC,eAAe,EAAE,gBAAgB;IACjCC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE,gBAAgB;IAC/BC,oBAAoB,EAAE,wBAAwB;IAC9CC,YAAY,EAAE,kBAAkB;IAChClY,MAAM,EAAE,QAAQ;IAChB8B,OAAO,EAAE;EACb,CAAC;EACDqW,YAAY,EAAE;IACVC,SAAS,EAAE,iBAAiB;IAC5BhB,WAAW,EAAE,sCAAsC;IACnDiB,cAAc,EAAE,gCAAgC;IAChDC,eAAe,EAAE,uCAAuC;IACxDC,gBAAgB,EAAE,yBAAyB;IAC3CC,eAAe,EAAE,4BAA4B;IAC7CC,QAAQ,EAAE,4FAA4F;IACtGC,kBAAkB,EAAE,qCAAqC;IACzDhrB,IAAI,EAAE,MAAM;IACZmL,YAAY,EAAE,gBAAgB;IAC9B8f,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,kBAAkB;IACjCC,YAAY,EAAE,aAAa;IAC3BpB,KAAK,EAAE,OAAO;IACdqB,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,WAAW;IACpB5Q,GAAG,EAAE,gBAAgB;IACrB6Q,OAAO,EAAE,gBAAgB;IACzBC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,uBAAuB;IACjCC,WAAW,EAAE,oCAAoC;IACjDC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,qBAAqB;IACtCC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPC,MAAM,EAAE,SAAS;IACjBrG,SAAS,EAAE,WAAW;IACtBhT,SAAS,EAAE,WAAW;IACtByR,MAAM,EAAE,OAAO;IACfiC,SAAS,EAAE,0BAA0B;IACrC4F,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,QAAQ;IACvB7N,QAAQ,EAAE,UAAU;IACpB8N,QAAQ,EAAE,UAAU;IACpB9e,OAAO,EAAE,WAAW;IACpB8F,MAAM,EAAE,SAAS;IACjB8C,MAAM,EAAE,QAAQ;IAChBmW,SAAS,EAAE;MACP5H,WAAW,EAAE,+CAA+C;MAC5D6H,iBAAiB,EAAE,0FAA0F;MAC7GxlB,MAAM,EAAE,QAAQ;MAChBylB,MAAM,EAAE,YAAY;MACpBtG,OAAO,EAAE,YAAY;MACrBuG,IAAI,EAAE,uBAAuB;MAC7BC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,yCAAyC;MACjDC,QAAQ,EAAE,UAAU;MACpB3C,MAAM,EAAE,QAAQ;MAChBpX,SAAS,EAAE,WAAW;MACtBga,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZ7O,KAAK,EAAE,OAAO;MACdrW,OAAO,EAAE,qDAAqD;MAC9DmlB,OAAO,EAAE,gCAAgC;MACzC3S,IAAI,EAAE,kEAAkE;MACxE4S,MAAM,EAAE,yBAAyB;MACjC1D,MAAM,EAAE,oDAAoD;MAC5D2D,WAAW,EAAE,oBAAoB;MACjCC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE,OAAO;MACdthB,IAAI,EAAE,KAAK;MACXuhB,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE;IACZ,CAAC;IACDrI,MAAM,EAAE;MACJsI,YAAY,EAAE,qBAAqB;MACnCC,UAAU,EAAE,YAAY;MACxBC,YAAY,EAAE,eAAe;MAC7BC,YAAY,EAAE,mBAAmB;MACjCC,YAAY,EAAE,gBAAgB;MAC9BC,QAAQ,EAAE,WAAW;MACrBC,YAAY,EAAE,eAAe;MAC7BC,UAAU,EAAE,aAAa;MACzBC,eAAe,EAAE,mBAAmB;MACpCC,kBAAkB,EAAE,sBAAsB;MAC1CC,UAAU,EAAE,iDAAiD;MAC7DC,UAAU,EAAE,iBAAiB;MAC7B1B,IAAI,EAAE,wCAAwC;MAC9Cnd,OAAO,EAAE,SAAS;MAClB8e,OAAO,EAAE,SAAS;MAClBhK,OAAO,EAAE,SAAS;MAClBiK,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,SAAS;MAClBtf,MAAM,EAAE,QAAQ;MAChBuf,WAAW,EAAE,kDAAkD;MAC/DhV,MAAM,EAAE,QAAQ;MAChBiV,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,QAAQ;MAChBC,aAAa,EAAE,gBAAgB;MAC/BC,SAAS,EAAE,WAAW;MACtBC,cAAc,EAAE,iBAAiB;MACjCC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,mBAAmB;MACjCC,eAAe,EAAE,0BAA0B;MAC3CC,gBAAgB,EAAE,iBAAiB;MACnCC,mBAAmB,EAAE,qCAAqC;MAC1DC,kBAAkB,EAAE,qCAAqC;MACzDC,kBAAkB,EAAE,oCAAoC;MACxDC,aAAa,EAAE,gBAAgB;MAC/BC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpB/J,YAAY,EAAE,eAAe;MAC7BgK,SAAS,EAAE,YAAY;MACvBlD,SAAS,EAAE,YAAY;MACvBmD,KAAK,EAAE,OAAO;MACd7C,QAAQ,EAAE,UAAU;MACpB8C,+BAA+B,EAAE,oCAAoC;MACrEC,UAAU,EAAE,aAAa;MACzBliB,QAAQ,EAAE,UAAU;MACpBmiB,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,qBAAqB;MAC7BC,YAAY,EAAE,eAAe;MAC7B1nB,MAAM,EAAE,QAAQ;MAChB2nB,MAAM,EAAE,gBAAgB;MACxBC,MAAM,EAAE,oBAAoB;MAC5BC,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE,cAAc;MAC1BC,QAAQ,EAAE,mFAAmF;MAC7FC,UAAU,EAAE,kBAAkB;MAC9BC,aAAa,EAAE,gBAAgB;MAC/BC,qBAAqB,EAAE,UAAU;MACjCC,UAAU,EAAE,mBAAmB;MAC/BnW,IAAI,EAAE,MAAM;MACZoW,WAAW,EAAE,gJAAgJ;MAC7JtpB,OAAO,EAAE,SAAS;MAClBnG,MAAM,EAAE,QAAQ;MAChB0vB,cAAc,EAAE,WAAW;MAC3BC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,QAAQ;MAChBC,kBAAkB,EAAE,6DAA6D;MACjFC,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE,0BAA0B;MAC1CC,aAAa,EAAE,wBAAwB;MACvCC,eAAe,EAAE,mBAAmB;MACpCC,WAAW,EAAE,cAAc;MAC3BC,IAAI,EAAE,MAAM;MACZC,YAAY,EAAE,6BAA6B;MAC3C7M,MAAM,EAAE,mBAAmB;MAC3B8M,UAAU,EAAE,eAAe;MAC3BC,WAAW,EAAE,WAAW;MACxBC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,2CAA2C;MAC/DC,kBAAkB,EAAE,qCAAqC;MACzDC,qBAAqB,EAAE,kBAAkB;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDC,cAAc,EAAE;MACZnxB,KAAK,EAAE,iBAAiB;MACxBoxB,YAAY,EAAE,eAAe;MAC7BC,cAAc,EAAE,iBAAiB;MACjCC,aAAa,EAAE,gBAAgB;MAC/BzD,IAAI,EAAE;IACV;EACJ,CAAC;EACD0D,aAAa,EAAE;IACXvxB,KAAK,EAAE,YAAY;IACnB2sB,OAAO,EAAE,iBAAiB;IAC1B6E,cAAc,EAAE,uBAAuB;IACvCC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,qBAAqB;IAChCC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE,MAAM;IACjB7c,IAAI,EAAE,MAAM;IACZ8c,KAAK,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;IACZjyB,KAAK,EAAE,WAAW;IAClBkyB,OAAO,EAAE,eAAe;IACxBpG,SAAS,EAAE,WAAW;IACtBpH,MAAM,EAAE,QAAQ;IAChBgM,IAAI,EAAE,MAAM;IACZoB,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE;EACf,CAAC;EACDjG,SAAS,EAAE;IACP9rB,KAAK,EAAE,sBAAsB;IAC7BmyB,UAAU,EAAE,uBAAuB;IACnCC,gBAAgB,EAAE,yCAAyC;IAC3DC,oBAAoB,EAAE,6CAA6C;IACnEC,gBAAgB,EAAE,wCAAwC;IAC1DC,oBAAoB,EAAE,4CAA4C;IAClErG,GAAG,EAAE,eAAe;IACpB3lB,MAAM,EAAE,kBAAkB;IAC1BisB,SAAS,EAAE,WAAW;IACtBvlB,QAAQ,EAAE,oBAAoB;IAC9B0f,OAAO,EAAE,SAAS;IAClBhX,MAAM,EAAE,QAAQ;IAChB8c,iBAAiB,EAAE,gCAAgC;IACnDC,aAAa,EAAE,+CAA+C;IAC9DC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,aAAa;IAChCC,cAAc,EAAE,6BAA6B;IAC7CC,WAAW,EAAE,6CAA6C;IAC1DC,sBAAsB,EAAE,+CAA+C;IACvEC,yBAAyB,EAAE,iDAAiD;IAC5EC,2BAA2B,EAAE;EACjC,CAAC;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,QAAQ;IAChBC,iBAAiB,EAAE,oBAAoB;IACvCC,YAAY,EAAE,0BAA0B;IACxCC,WAAW,EAAE,cAAc;IAC3BC,gBAAgB,EAAE,iDAAiD;IACnEC,IAAI,EAAE,MAAM;IACZC,eAAe,EAAE,yBAAyB;IAC1CC,cAAc,EAAE,wBAAwB;IACxCC,GAAG,EAAE,KAAK;IACVC,EAAE,EAAE,IAAI;IACRC,YAAY,EAAE,eAAe;IAC7BC,KAAK,EAAE,OAAO;IACdrtB,OAAO,EAAE,SAAS;IAClB3C,GAAG,EAAE,KAAK;IACVlB,EAAE,EAAE,IAAI;IACRmxB,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,kBAAkB;IAC/BC,WAAW,EAAE,cAAc;IAC3BC,mBAAmB,EAAE,wBAAwB;IAC7CC,QAAQ,EAAE,WAAW;IACrBC,gBAAgB,EAAE,qBAAqB;IACvCC,cAAc,EAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACTC,MAAM,EAAE,qBAAqB;IAC7BrM,MAAM,EAAE,wBAAwB;IAChCrP,MAAM,EAAE,QAAQ;IAChBzY,MAAM,EAAE;EACZ,CAAC;EACDo0B,eAAe,EAAE;IACb9hB,MAAM,EAAE,mBAAmB;IAC3BqZ,GAAG,EAAE,eAAe;IACpB5S,EAAE,EAAE,cAAc;IAClBsb,IAAI,EAAE,gBAAgB;IACtB3nB,QAAQ,EAAE,oBAAoB;IAC9B4nB,WAAW,EAAE,wBAAwB;IACrCtf,UAAU,EAAE,uBAAuB;IACnCuf,uBAAuB,EAAE,gCAAgC;IACzDC,UAAU,EAAE,aAAa;IACzBnB,GAAG,EAAE,KAAK;IACVC,EAAE,EAAE,IAAI;IACRmB,MAAM,EAAE,eAAe;IACvBC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,MAAM;IACZ30B,MAAM,EAAE,QAAQ;IAChBstB,IAAI,EAAE,aAAa;IACnBsH,aAAa,EAAE,iBAAiB;IAChCC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,EAAE;IACdrc,MAAM,EAAE,QAAQ;IAChBsc,eAAe,EAAE,kCAAkC;IACnDC,eAAe,EAAE,mEAAmE;IACpF7C,aAAa,EAAE,iDAAiD;IAChE9Y,IAAI,EAAE,MAAM;IACZ4b,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZz1B,KAAK,EAAE,iBAAiB;IACxB01B,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,cAAc;IACtBzN,MAAM,EAAE,cAAc;IACtBgE,GAAG,EAAE,YAAY;IACjB7S,IAAI,EAAE,WAAW;IACjBuc,UAAU,EAAE,iBAAiB;IAC7BC,iBAAiB,EAAE,+BAA+B;IAClDC,UAAU,EAAE,aAAa;IACzBtD,SAAS,EAAE,WAAW;IACtB5hB,IAAI,EAAE,MAAM;IACZ+E,MAAM,EAAE,QAAQ;IAChB9C,MAAM,EAAE,cAAc;IACtBkjB,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,6BAA6B;IAC9CC,qBAAqB,EAAE,kCAAkC;IACzDC,4BAA4B,EAAE,qDAAqD;IACnFC,qBAAqB,EAAE,gCAAgC;IACvD51B,MAAM,EAAE,QAAQ;IAChB61B,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,sBAAsB;IACtCC,gBAAgB,EAAE,wBAAwB;IAC1CC,WAAW,EAAE;MACThe,IAAI,EAAE,MAAM;MACZie,QAAQ,EAAE,WAAW;MACrB3sB,IAAI,EAAE,MAAM;MACZyT,MAAM,EAAE,QAAQ;MAChBmZ,OAAO,EAAE;IACb,CAAC;IACDC,aAAa,EAAE,mBAAmB;IAClCC,aAAa,EAAE,0CAA0C;IACzDjE,MAAM,EAAE,QAAQ;IAChBkE,cAAc,EAAE;EACpB,CAAC;EACDC,KAAK,EAAE;IACHnR,YAAY,EAAE,wBAAwB;IACtCoR,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,eAAe;IACzBC,MAAM,EAAE,sBAAsB;IAC9BC,aAAa,EAAE,0CAA0C;IACzD1K,MAAM,EAAE,QAAQ;IAChBxT,MAAM,EAAE,QAAQ;IAChBme,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,+BAA+B;IACzCC,WAAW,EAAE,2BAA2B;IACxCC,UAAU,EAAE,0BAA0B;IACtCC,aAAa,EAAE,oCAAoC;IACnDC,SAAS,EAAE,WAAW;IACtB5vB,MAAM,EAAE,iCAAiC;IACzC6vB,OAAO,EAAE,oBAAoB;IAC7BC,SAAS,EAAE,2BAA2B;IACtCC,SAAS,EAAE,wCAAwC;IACnD1P,QAAQ,EAAE,UAAU;IACpB2P,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,UAAU;IAClBC,MAAM,EAAE,kCAAkC;IAC1CjpB,OAAO,EAAE,sBAAsB;IAC/BkpB,OAAO,EAAE,oEAAoE;IAC7EC,UAAU,EAAE,qDAAqD;IACjEC,UAAU,EAAE,4CAA4C;IACxDlP,aAAa,EAAE,UAAU;IACzBmP,YAAY,EAAE,eAAe;IAC7BC,oBAAoB,EAAE,yBAAyB;IAC/CC,OAAO,EAAE,UAAU;IACnB3xB,OAAO,EAAE,SAAS;IAClBspB,WAAW,EAAE,2DAA2D;IACxEsI,SAAS,EAAE,eAAe;IAC1BC,eAAe,EAAE,sBAAsB;IACvCC,SAAS,EAAE,mBAAmB;IAC9B1F,iBAAiB,EAAE,YAAY;IAC/B2F,SAAS,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACP14B,KAAK,EAAE,cAAc;IACrB8O,OAAO,EAAE,uFAAuF;IAChGvO,MAAM,EAAE,SAAS;IACjBmG,OAAO,EAAE,gBAAgB;IACzBiyB,QAAQ,EAAE;EACd,CAAC;EACDC,WAAW,EAAE;IACTC,YAAY,EAAE,wBAAwB;IACtCnQ,QAAQ,EAAE,wFAAwF;IAClGhiB,OAAO,EAAE;EACb,CAAC;EACDoyB,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}