{"ast": null, "code": "export default {\n  mixin: {\n    createSuccessful: 'Created successfully',\n    setLabel: 'Set label'\n  }\n};", "map": {"version": 3, "names": ["mixin", "createSuccessful", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/mixins/mixins-en.js"], "sourcesContent": ["export default {\n    mixin: {\n        createSuccessful: 'Created successfully',\n        setLabel: 'Set label',\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,gBAAgB,EAAE,sBAAsB;IACxCC,QAAQ,EAAE;EACd;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}