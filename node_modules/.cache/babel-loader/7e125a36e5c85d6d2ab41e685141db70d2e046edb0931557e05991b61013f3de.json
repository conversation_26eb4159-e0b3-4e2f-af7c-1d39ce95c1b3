{"ast": null, "code": "import ChatView from './chatView/index.vue';\nimport Document from './document/index.vue';\nimport Guide from '../components/guide/index.vue';\nexport default {\n  components: {\n    ChatView,\n    Document,\n    Guide\n  },\n  data() {\n    return {\n      topicId: this.$route.params.topicId,\n      showGuide: false\n    };\n  },\n  created() {\n    this.$http.post(`/web/hubble/topic/${this.topicId}/init-document-free-chat-plugin`);\n  }\n};", "map": {"version": 3, "names": ["ChatView", "Document", "Guide", "components", "data", "topicId", "$route", "params", "showGuide", "created", "$http", "post"], "sources": ["src/views/agent/chat/index.vue"], "sourcesContent": ["<template>\n    <div v-if=\"topicId\" class=\"hubble-page__content\">\n        <Guide v-model=\"showGuide\"></Guide>\n        <Document\n            class=\"hubble-page__content-document\"\n            :topicId=\"topicId\"\n        ></Document>\n        <ChatView :key=\"topicId\" class=\"hubble-page__content-chat\" :topicId=\"topicId\"></ChatView>\n    </div>\n</template>\n<script>\nimport ChatView from './chatView/index.vue';\nimport Document from './document/index.vue';\nimport Guide from '../components/guide/index.vue';\nexport default {\n    components: {\n        ChatView,\n        Document,\n        Guide,\n    },\n    data() {\n        return {\n            topicId: this.$route.params.topicId,\n            showGuide: false,\n        };\n    },\n    created() {\n        this.$http.post(`/web/hubble/topic/${this.topicId}/init-document-free-chat-plugin`);\n    },\n};\n</script>\n"], "mappings": "AAWA,OAAAA,QAAA;AACA,OAAAC,QAAA;AACA,OAAAC,KAAA;AACA;EACAC,UAAA;IACAH,QAAA;IACAC,QAAA;IACAC;EACA;EACAE,KAAA;IACA;MACAC,OAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAF,OAAA;MACAG,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,KAAA,CAAAC,IAAA,2BAAAN,OAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}