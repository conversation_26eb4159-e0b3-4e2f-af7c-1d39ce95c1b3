{"ast": null, "code": "// 语言为中文时的文案\nimport utils from './module/utils/utils-zh.js';\nimport mixin from './module/mixins/mixins-zh.js';\nimport components from './module/components/components-zh.js';\nimport docList from './module/docList/docList-zh.js';\nimport console from './module/console/console-zh.js';\nimport userCentral from './module/usercentral/usercentral-zh.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-zh.js';\nimport entAuth from './module/entAuth/entAuth-zh.js';\nimport consts from './module/consts/zh.js';\nimport docTranslation from './module/docTranslation/docTranslation-zh.js';\nexport default {\n  ...utils,\n  ...mixin,\n  ...components,\n  ...docList,\n  ...docTranslation,\n  footerAd: {\n    title: '跳转提示',\n    content1: '即将跳转到第三方服务页面',\n    content2: '是否继续？',\n    bankContent: '即将进入宁波银行“容易贷”企业贷款介绍页面',\n    bankTip1: '让宁波银行主动给我打电话',\n    bankTip2: '向我发送一条短信，介绍如何办理',\n    bankFooter: '加宁波银行专属客服，一对一服务我',\n    cancel: '取消',\n    continue: '继续'\n  },\n  commonFooter: {\n    record: 'ICP主体备案号：浙ICP备********号',\n    hubbleRecordId: '网信算备：330106973391501230011',\n    openPlatform: '开放平台',\n    aboutBestSign: '关于公司',\n    contact: '联系我们',\n    recruitment: '诚聘英才',\n    help: '帮助中心',\n    copyright: '版权所有',\n    company: '杭州尚尚签网络科技有限公司',\n    ssqLogo: '上上签底栏Logo',\n    provideTip: '电子签约服务由',\n    ssq: '上上签',\n    provide: '提供',\n    signHotline: '签约服务热线',\n    langSwitch: '切换语言'\n  },\n  login: {\n    pswLogin: '密码登录',\n    usePswLogin: '使用密码登录',\n    verifyLogin: '验证码登录',\n    useVerifyLogin: '使用验证码登录',\n    scanLogin: '扫码登录',\n    scanFailure: '二维码已失效,请刷新重试',\n    scanSuccess: '扫码成功',\n    scanLoginTip: '请使用上上签APP扫一扫登录',\n    appLoginTip: '请在上上签APP中点击登录',\n    downloadApp: '下载上上签APP',\n    forgetPsw: '忘记密码',\n    login: '登录',\n    noAccount: '没有账号',\n    registerNow: '马上注册',\n    accountPlaceholder: '请输入手机或邮箱',\n    passwordPlaceholder: '请输入登录密码',\n    pictureVer: '请填写图片中的内容',\n    verifyCodePlaceholder: '请输入6位验证码',\n    getVerifyCode: '获取验证码',\n    noRegister: '尚未注册',\n    or: '或',\n    errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',\n    errAccountOrPwdTip2: '你输入的密码和账号不匹配',\n    errEmailOrTel: '请输入正确的邮箱或手机号!',\n    errPwd: '请输入正确的密码!',\n    verCodeFormatErr: '验证码错误',\n    grapVerCodeErr: '图形验证码错误',\n    grapVerCodeFormatErr: '图形验证码格式错误',\n    lackAccount: '请填写账号后再获取',\n    lackGrapCode: '请先填写图形验证码',\n    getVerCodeTip: '请获取验证码',\n    loginView: '登录并查看合同',\n    regView: '注册并查看合同',\n    takeViewBtn: '登录并签署',\n    resendCode: '重新获取',\n    regTip: '填写正确的验证码后上上签将为您创建账号',\n    haveRead: '我已阅读并同意',\n    bestsignAgreement: '上上签服务协议',\n    and: '和',\n    digitalCertificateAgreement: '数字证书使用协议',\n    privacyPolicy: '隐私政策',\n    sendSuc: '发送成功',\n    lackVerCode: '请先输入验证码',\n    lackPsw: '请先输入密码',\n    notMatch: '您输入的密码和账号不匹配',\n    cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',\n    wrongLink: '非法链接',\n    footerTips: '电子签约服务由<span>上上签</span>提供',\n    bestSign: '上上签',\n    bestSignDescription: '电子签约行业领跑者',\n    /** 忘记密码 /forgotPassword start */\n    forgetPswStep: '验证注册账号 | 重新设置密码',\n    pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',\n    accountInput: '账号 | 请填写您的账号',\n    smsCodeInput: '验证码 | 获取验证码',\n    haveRegistereLoginNow: '我已注册， | 马上登录',\n    nextStep: '下一步 | 提交',\n    setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',\n    passwordResetSucceeded: '密码重置成功!',\n    /** 忘记密码 /forgotPassword end */\n    accountNotRegistered: '账号未注册',\n    loginAndDownload: '登录并下载合同',\n    registerAndDownload: '注册并下载合同',\n    inputPhone: '请输入手机号',\n    readContract: '读取合同',\n    errorPhone: '手机格式错误',\n    companyCert: '进行企业认证',\n    regAndCompanyCert: '注册并进行企业认证'\n  },\n  ...sign,\n  handwrite: {\n    title: '手写签名',\n    picSubmitTip: '图片签名提交成功',\n    settingDefault: '设置为默认签名',\n    replaceAllSignature: '用于所有签名处',\n    replaceAllSeal: '用于所有盖章处',\n    canUseSeal: '我的印章',\n    applyForSeal: '申请用印',\n    moreTip: '将保存您的手写签名作为默认签名，仅用于合同签署。管理路径：【用户中心->签名管理】',\n    uploadPic: '上传照片',\n    use: '使用',\n    clickExtend: '点右箭头延长手写区',\n    upload: '上传签名图片',\n    uploadTip1: '提示：上传签名图片时请将签名充满整个图片',\n    uploadTip2: '签名请使用颜色较深或者纯黑色文字。',\n    rewrite: '重写',\n    cancel: '不写了',\n    confirm: '使用',\n    upgradeBrowser: '您的浏览器不支持画布手绘签名，请升级浏览器。',\n    submitTip: '手绘签名提交成功',\n    title2: '手绘您的签名',\n    QRCode: '扫码签名',\n    needWrite: '请手绘正确的姓名！',\n    needRewrite: '笔画无法辨认，请重新书写',\n    ok: '确定',\n    clearTips: '请书写清晰可辨的签名',\n    isBlank: '画布为空，请先手绘签名再提交！',\n    success: '手绘签名提交成功',\n    signNotMatch: '请正楷书写签名，须与实名证件信息一致。',\n    signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',\n    msg: {\n      successToUser: '已设置好新签名，请在web端点击“保存”按钮。',\n      successToSign: '新签名已生效，请到合同签署页查看。',\n      cantGet: '获取不到签名，请尝试使用其他浏览器！'\n    }\n  },\n  common: {\n    aboutBestSign: '关于公司',\n    contact: '联系我们',\n    recruitment: '诚聘英才',\n    copyright: '版权所有',\n    advice: '咨询建议',\n    notEmpty: '不能为空!',\n    enter6to18n: '请输入6-18位数字、大小写字母',\n    ssqDes: '电子签约云平台领导者',\n    openPlatform: '开放平台',\n    company: '杭州尚尚签网络科技有限公司',\n    help: '帮助中心',\n    errEmailOrTel: '请输入正确的邮箱或手机号!',\n    verCodeFormatErr: '验证码错误',\n    signPwdType: '请输入6位数字',\n    enterActualEntName: '请填写真实的企业名称',\n    enterCorrectName: '请输入正确的姓名',\n    enterCorrectPhoneNum: '请输入正确的手机号',\n    enterCorrectEmail: '请输入正确的邮箱',\n    imgCodeErr: '图形验证码错误',\n    enterCorrectIdNum: '请输入正确的证件号码',\n    enterCorrectFormat: '请输入正确的格式',\n    enterCorrectDateFormat: '请输入正确的日期格式'\n  },\n  entAuth: {\n    ...entAuth,\n    entCertification: '企业实名认证',\n    subBaseInfo: '提交基本信息',\n    corDocuments: '企业证件',\n    license: '营业执照',\n    upload: '点击上传',\n    uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',\n    hi: '你好',\n    exit: '退出',\n    help: '帮助',\n    hotline: '服务热线',\n    acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',\n    comfirmSubmit: '确认提交',\n    cerficated: '认证完成',\n    serialNumber: '证书序列号',\n    validity: '有效期',\n    entName: '企业名称',\n    nationalNo: '国家注册号',\n    corporationName: '法定代表人姓名',\n    city: '所在城市',\n    entCertificate: '企业实名证书',\n    certificationAuthority: '证书颁发机构',\n    bestsignPlatform: '上上签电子签约云平台',\n    notIssued: '未发放',\n    date: '{year}年{month}月{day}日',\n    congratulations: '恭喜您，成功完成企业实名认证',\n    continue: '继续',\n    rejectMessage: '由于如下原因，资料审核不通过，请核对',\n    recertification: '重新认证',\n    waitMessage: '客服将在一个工作日内完成审核，请耐心等待'\n  },\n  personalAuth: {\n    info: '提示',\n    submitPicError: '请上传照片后再使用'\n  },\n  home: {\n    ...home,\n    home: '首页',\n    contractDrafting: '合同起草',\n    contractManagement: '合同管理',\n    userCenter: '用户中心',\n    service: '服务',\n    enterpriseConsole: '企业控制台',\n    groupConsole: '集团控制台',\n    startSigning: '发起签约',\n    contractType: '发送普通合同 | 发送模板合同 ',\n    sendContract: '发送合同',\n    shortcuts: '快捷入口 | 没有任何文件快捷入口',\n    setting: '立即设置 | 设置更多快捷入口',\n    signNum: '签发量月度报表',\n    contractNum: '合同发送量 | 合同签署量',\n    contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',\n    type: '企业 | 个人',\n    basicInformation: '基本信息',\n    more: '更多',\n    certified: '已认证 | 未认证',\n    account: '账号',\n    time: '创建时间 |注册时间',\n    day: '日 | 月',\n    sendContractNum: '发送量 | 签署量',\n    num: '份',\n    realName: '立即企业实名 | 立即个人实名',\n    update: '产品最新公告',\n    mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',\n    countDes: {\n      1: '可发：对公合同',\n      2: '份',\n      3: '对私合同',\n      4: '份'\n    },\n    chargeNow: '立即充值',\n    myRechargeOrder: '我的充值订单',\n    statusTip: {\n      1: '需要我操作',\n      2: '需要他人签署',\n      3: '即将截止签约',\n      4: '签约完成'\n    },\n    useTemplate: '使用模板',\n    useLocalFile: '上传本地文件',\n    enterEnterpriseName: '请输入企业名称'\n  },\n  docDetail: {\n    canNotOperateTip: '无法{operate}合同',\n    shareSignLink: '分享签署链接',\n    faceSign: '刷脸签署',\n    faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',\n    contractRecipient: '合同收件方',\n    personalOperateLog: '个人合同操作日志',\n    recordDialog: {\n      date: '日期',\n      user: '用户',\n      operate: '操作',\n      view: '查看',\n      download: '下载'\n    },\n    remarks: '备注',\n    operateRecords: '操作记录',\n    borrowingRecords: '借阅记录',\n    currentHolder: '当前持有人',\n    currentEnterprise: '当前企业',\n    companyInterOperationLog: '公司内部操作日志',\n    receiverMap: {\n      sender: '合同发件人',\n      signer: '合同接收人',\n      ccUser: '合同抄送人'\n    },\n    downloadCode: '合同下载码',\n    noTagToAddHint: '还没有标签，请前往企业控制台添加',\n    requireFieldNotAllowEmpty: '必填项不能为空',\n    modifySuccess: '修改成功',\n    uncategorized: '未分类',\n    notAllowModifyContractType: '{type}中的合同不允许修改合同类型',\n    setTag: '设置标签',\n    contractTag: '合同标签',\n    plsInput: '请输入',\n    plsInputCompanyInternalNum: '请输入公司内部编号',\n    companyInternalNum: '公司内部编号',\n    none: '无',\n    plsSelect: '请选择',\n    modify: '修改',\n    contractDetailInfo: '合同详细信息',\n    slideContentTip: {\n      signNotice: '签约须知',\n      contractAncillaryInformation: '合同附属资料',\n      content: '内容',\n      document: '文档'\n    },\n    downloadDepositConfirmTip: {\n      title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',\n      hint: '提示',\n      confrim: '继续下载',\n      cancel: '取消'\n    },\n    downloadTip: {\n      title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',\n      hint: '提示',\n      confirm: '确定',\n      cancel: '取消'\n    },\n    transferSuccessGoManagePage: '转交成功，将返回合同管理页面',\n    claimSign: '认领签署',\n    downloadDepositPageTip: '下载签约存证页(脱敏版)',\n    resend: '重新发送',\n    proxySign: '代签署',\n    notPassed: '已驳回',\n    approving: '审批中',\n    signning: '签署中',\n    notarized: '已公正',\n    currentFolder: '当前文件夹',\n    archive: '归档',\n    deadlineForSigning: '截止签约时间',\n    endFinishTime: '签约完成/签约结束时间',\n    contractImportTime: '合同导入时间',\n    contractSendTime: '合同发送时间',\n    back: '返回',\n    contractInfo: '合同信息',\n    basicInfo: '基本信息',\n    contractNum: '合同编号',\n    sender: '发件方',\n    personAccount: '个人账号',\n    entAccount: '企业账号',\n    operator: '经办人',\n    signStartTime: '发起签约时间',\n    signDeadline: '签约截止时间',\n    contractExpireDate: '合同到期时间',\n    // none: '无',\n    edit: '修改',\n    settings: '设置',\n    from: '来源',\n    folder: '文件夹',\n    contractType: '合同类型',\n    reason: '理由',\n    sign: '签署',\n    approval: '审批',\n    viewAttach: '查看附页',\n    downloadContract: '下载合同',\n    downloadAttach: '下载签约存证',\n    print: '打印',\n    certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',\n    needMeSign: '需要我签署',\n    needMeApproval: '需要我审批',\n    inApproval: '审批中',\n    needOthersSign: '需要他人签署',\n    signComplete: '签约完成',\n    signOverdue: '逾期未签',\n    rejected: '已拒签',\n    revoked: '已撤销',\n    contractCompleteTime: '签约完成时间',\n    contractEndTime: '签约结束时间',\n    reject: '拒签',\n    revoke: '撤销',\n    download: '下载',\n    viewSignOrders: '查看签署顺序',\n    viewApprovalProcess: '查看审批流程',\n    completed: '已完成',\n    cc: '抄送',\n    ccer: '抄送方',\n    signer: '签约方',\n    signSubject: '签约主体',\n    signSubjectTooltip: '发件方填写的签约主体为',\n    user: '用户',\n    IDNumber: '身份证号',\n    state: '状态',\n    time: '时间',\n    notice: '提醒',\n    detail: '详情',\n    RealNameCertificationRequired: '需要实名认证',\n    RealNameCertificationNotRequired: '不需要实名认证',\n    MustHandwrittenSignature: '必须手写签名',\n    handWritingRecognition: '开启手写笔迹识别',\n    privateMessage: '私信',\n    attachment: '资料',\n    rejectReason: '原因',\n    notSigned: '未签署',\n    notViewed: '未查看',\n    viewed: '已查看',\n    signed: '已签署',\n    viewedNotSigned: '已读未签',\n    notApproval: '未审批',\n    remindSucceed: '提醒消息已发送',\n    reviewDetails: '审批详情',\n    close: '关 闭',\n    entInnerOperateDetail: '企业内部操作详情',\n    approve: '同意',\n    disapprove: '驳回',\n    applySeal: '申请用印',\n    applied: '已申请',\n    apply: '申请',\n    toOtherSign: '转给其他人签',\n    handOver: '转交',\n    approvalOpinions: '审批意见',\n    useSeal: '用印',\n    signature: '签名',\n    use: '使用',\n    date: '日期',\n    fill: '填写',\n    times: '次',\n    place: '处',\n    contractDetail: '合同明细',\n    viewMore: '查看更多',\n    collapse: '收起',\n    signLink: '签署链接',\n    saveQRCode: '保存二维码或复制链接，分享给签署方',\n    signQRCode: '签署链接二维码',\n    copy: '复制',\n    copySucc: '复制成功',\n    copyFail: '复制失败',\n    certified: '已认证',\n    unCertified: '未认证',\n    claimed: '已认领'\n  },\n  uploadFile: {\n    thumbnails: '缩略图',\n    isUploading: '正在上传',\n    move: '移动',\n    delete: '删除',\n    replace: '替换',\n    tip: '提示',\n    understand: '我知道了',\n    totalPages: '{page}页',\n    uploadFile: '上传本地文件',\n    matchErr: '服务器开了点小差，请稍后再试',\n    inUploadingDeleteErr: '请在上传完毕后删除',\n    timeOutErr: '请求超时',\n    imgUnqualified: '图片格式不符合要求',\n    imgBiggerThan20M: '上传图片大小不能超过 20MB!',\n    error: '出错啦',\n    hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。'\n  },\n  contractInfo: {\n    internalNumber: '公司内部编号',\n    contractName: '合同标题',\n    contractNameTooltip: '合同标题请不要包含特殊字符，且长度不超过100字',\n    contractType: '合同类型',\n    toSelect: '请选择',\n    contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n    signDeadLine: '签约截止时间',\n    signDeadLineTooltip: '如果合同在此日期前未完成签署，则无法继续签署',\n    selectDate: '选择日期时间',\n    contractExpireDate: '合同到期日',\n    expireDateTooltip: '合同内容中的到期时间，便于您后续的合同管理',\n    necessary: '必填',\n    notNecessary: '选填',\n    dateTips: '已为您自动识别合同到期日，请确认',\n    contractTitleErr: '合同名称请不要包含特殊字符',\n    contractTitleLengthErr: '合同名称长度请不要超过100字'\n  },\n  template: {\n    templateList: {\n      linkBoxTip: '关联档案柜ID：'\n    },\n    dynamicTemplateUpdate: {\n      title: '动态模板新功能上线',\n      newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n      updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n      connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。'\n    },\n    sendCode: {\n      tip: '当前模板设置不符合发送码生成条件，请检查是否符合以下要求：',\n      fail: {\n        1: '不包含空白文档',\n        2: '签约方只有一个可变方（包含签署和抄送），且可变方必须是第一操作人；签署人必须设有签字盖章处',\n        3: '签约方中固定方账号不为空',\n        4: '不会触发发送前审批',\n        5: '发件方必填字段不为空（含描述字段和合同内容字段）',\n        6: '非模板组合'\n      }\n    },\n    sendCodeGuide: {\n      title: '发送码高级功能说明',\n      info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n      tip1: {\n        main: '1. 上上签',\n        sub: '',\n        line1: '向上上签申请开通档案+、合同预审、智能预审',\n        line2: '开通后可以到对应的菜单中操作使用'\n      },\n      tip2: {\n        main: '2. 档案柜管理员',\n        sub: '创建档案柜、配置智能预审',\n        line1: '',\n        line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。'\n      },\n      tip3: {\n        main: '3. 签约方',\n        sub: '扫码填资料、获取合同文件',\n        line1: '',\n        line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署'\n      },\n      tip4: {\n        main: '4. 档案柜管理员',\n        sub: '',\n        line1: '查看签约的相对方、发送的合同情况',\n        line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等'\n      }\n    }\n  },\n  style: {\n    signature: {\n      text: {\n        x: '34',\n        fontSize: '18'\n      }\n    }\n  },\n  resetPwd: {\n    title: '安全提示！',\n    notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',\n    oldLabel: '原密码',\n    oldPlaceholder: '请输入原密码',\n    newLabel: '新密码',\n    newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',\n    submit: '确定',\n    errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',\n    oldRule: '原密码不能为空',\n    newRule: '新密码不能为空',\n    success: '修改成功'\n  },\n  personAuthIntercept: {\n    title: '邀请您以',\n    name: '姓名：',\n    id: '身份证号：',\n    descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    desMore: '根据发起方要求，您还需要补充',\n    descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    tips: '实名认证通过后，可查看并签署合同。',\n    goOn: '是我本人，开始认证',\n    goMore: '去补充认证',\n    descNoSame1: ' 的身份签署合同',\n    descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n    goHome: '返回合同列表页>>',\n    authInfo: '检测到您当前账号的实名身份为 ',\n    in: '于',\n    finishAuth: '完成实名，用于合规签署合同',\n    ask: '是否继续以当前账号签署？',\n    reAuthBtnText: '是的，我要用本账号重新实名签署',\n    changePhoneText: '不是，联系发件方更改签署手机号',\n    changePhoneTip1: '应发件方要求，请联系',\n    changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n    confirmReject: '是的，我要驳回实名'\n  },\n  authIntercept: {\n    title: '要求您以：',\n    name: '姓名为：',\n    id: '身份证号为：',\n    descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n    descNoAuth2: '实名认证通过后，可查看并签署合同。',\n    descNoSame1: '签署合同。',\n    descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n    tips: '注：身份信息完全一致才能签署合同',\n    goOn: '是我本人，开始认证',\n    goHome: '我知道了',\n    goMore: '去补充认证',\n    authTip: '进行实名认证。',\n    viewAndSign: '完成认证后即可查看和签署合同',\n    tips2: '注：企业名称完全一致才能查看和签署合同。',\n    requestOtherAnth: '请求他人认证',\n    goAuth: '去实名认证',\n    requestSomeoneList: '请求以下人员完成实名认证：',\n    ent: '企业',\n    entName: '企业名称',\n    account: '账号',\n    accountPH: '手机或邮箱',\n    send: '发送',\n    lackEntName: '请填写企业名称',\n    errAccount: '请填写正确的邮箱或手机号',\n    successfulSent: '发送成功'\n  },\n  thirdPartApprovalDialog: {\n    title1: '签署前审批',\n    title2: '审批流程',\n    content1: '审批通过后您才可以签署，请耐心等待。',\n    content2: '需由第三方平台（非上上签平台）审批合同。',\n    cancelBtnText: '查看审批流程',\n    confirmBtnText: '确认',\n    iKnow: '我知道了'\n  },\n  endSignEarlyPrompt: {\n    cancel: '取消',\n    confirm: '确认',\n    signPrompt: '签署提示',\n    signTotalCountTip: '本次签署共包含{count}份合同文件',\n    signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n    hasSigned: '{count}人已签字',\n    hasNotSigned: '{count}人未签字',\n    noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。'\n  },\n  commonNomal: {\n    yesterday: '昨天',\n    ssq: '上上签',\n    ssqPlatform: '上上签电子签约云平台',\n    ssqTestPlatform: '（限测试用）BestSign电子签约云平台',\n    pageExpiredTip: '页面已过期，请刷新重试',\n    pswCodeSimpleTip: '密码需包含6-18位数字和大小写字母，请重新设置'\n  },\n  transferAdminDialog: {\n    title: '身份确认',\n    transfer: '转交',\n    confirmAdmin: '我是主管理员',\n    content: '系统主管理员需要负责企业印章的管理、合同的管理和其它人员权限的管理，一般归属于企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人。| 请确认您是否符合以上身份，如果不符合，建议转交给相关人员。'\n  },\n  choseBoxForReceiver: {\n    dataNeedForReceiver: '签约主体需要提交的资料',\n    dataFromDataBox: '签约主体提交的资料需要通过某个档案柜的资料采集来获取',\n    searchTp: '请输入档案柜名称或编号',\n    search: '搜索',\n    boxNotFound: '未找到档案柜',\n    cancel: '取 消',\n    confirm: '确 认'\n  },\n  localCommon: {\n    cancel: '取消',\n    confirm: '确认',\n    toSelect: '请选择',\n    seal: '盖章',\n    signature: '签名',\n    signDate: '签署日期',\n    text: '文本',\n    date: '日期',\n    qrCode: '二维码',\n    number: '数字',\n    dynamicTable: '动态表格',\n    terms: '合同条款',\n    checkBox: '复选框',\n    radioBox: '单选框',\n    image: '图片',\n    confirmSeal: '业务核对章',\n    confirmRemark: '不符合章的备注',\n    optional: '选填',\n    require: '必填',\n    tip: '提示',\n    comboBox: '下拉框'\n  },\n  twoFactor: {\n    signTip: '签署提示',\n    settingTwoFactor: '设置二要素验证器',\n    step1: '1. 安装验证器应用',\n    step1Tip: '二要素身份验证需要您安装一下手机应用程序：',\n    step2: '2.扫描二维码',\n    step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',\n    step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',\n    step3: '3.输入6位验证码',\n    step3Tip: '请输入屏幕上显示的验证码',\n    verifyCode6: '6位验证码',\n    iosAddress: 'iOS下载地址：',\n    androidAddress: 'Android下载地址：',\n    chromeVerify: '谷歌身份验证器',\n    nextBtn: '下一步',\n    confirmSign: '确认签署',\n    dynamicCode: '验证器动态码',\n    password: '加密签署码',\n    pleaseInput: '请输入',\n    twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',\n    passwordTip: '应发件方要求，您需要通过加密签署才可以完成签署。',\n    twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',\n    passwordTip2: '请联系发件方索要加密签署码，输入后即可签署合同。',\n    dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。'\n  },\n  functionSupportDialog: {\n    title: '功能介绍',\n    inputTip: '若您有相关使用需求，欢迎在以下表格中填写您的需求，上上签会在24小时内安排专业人士联系您并提供服务指导。',\n    useSence: '使用场景',\n    useSenceTip: '如：人事/经销商/物流单据...',\n    estimatedOnlineTime: '预计上线时间',\n    requireContent: '请详细描述您的需求场景(选填)',\n    requireContentTip: '请描述场景需求与期望的解决方案',\n    getSupport: '获取专业服务支持',\n    callServiceHotline: '立即拨打客服热线：************',\n    useSenceNotEmpty: '使用场景不能为空',\n    requrieContentNotEmpty: '需求内容不能为空',\n    oneWeek: '一周内',\n    oneMonth: '一月内',\n    other: '其他',\n    submitSuccess: '提交成功',\n    submitTrial: '立即试用',\n    toTrial: '去试用',\n    trialTip: '提交试用申请后，当前功能将立即开通。为了便于我们精准了解您的需求，您可以补充填写更多问题场景，我们将尽快与您联系。',\n    applyTrial: '申请试用',\n    trialSuccTip: '功能已开通，欢迎试用',\n    goBuy: '直接购买',\n    trialTipMap: {\n      title: '试用须知',\n      tip1: '1. 即开即用，有效期为7天；',\n      tip2: '2. 试用期间，功能不收费；',\n      tip3: '3. 每个企业主体，一个功能仅一次试用机会；',\n      tip4: '4. 试用期间可自助购买，使用不间断；',\n      tip5: '5. 如您的试用已结束，可扫码联系上上签专业顾问了解详情：'\n    },\n    contactAdminTip: '如需使用，请联系您的企业管理员{tip}购买开通',\n    trialEndTip: '试用期结束，点击购买',\n    trialRemainDayTip: '试用期剩{day}天，点击购买',\n    trialEnd: '试用功能结束',\n    trialEndMap: {\n      deactivateTip: '{feature}功能已停用，请清除配置，或者续费后，方可继续使用。',\n      feature1: '合同附属资料',\n      remove1: '清除配置方法为：编辑模板-找到配置好的添加合同附属资料，将其删除。',\n      feature2: '手写笔迹识别',\n      remove2: '清除配置方法为：编辑模板-找到配置好的笔迹识别，将其删除。',\n      feature3: '合同装饰：骑缝章+水印',\n      remove3: '清除配置方法为：编辑模板-找到配置好的合同装饰，将其删除。',\n      feature4: '合同发送审批',\n      remove4: '清除配置方法为：企业控制台-停用所有审批流'\n    }\n  },\n  setSignPwdDialog: {\n    tip: '设置完成后，默认优先签约密码，如需修改可登录上上签电子签约平台在「用户中心」或者登录上上签小程序在「账号管理」进行配置调整。',\n    saveAndReturnSign: '保存并返回签署',\n    changeEmailVerify: '切换邮箱验证',\n    changePhoneVerify: '切换手机号验证'\n  },\n  contractCompare: {\n    reUpload: '重新上传',\n    title: '合同比对',\n    packagePurchase: '套餐购买',\n    packagePurchaseTitle: '【{title}功能】套餐购买',\n    payOnce: '特惠限购一次',\n    myPackage: '我的套餐',\n    packageDetail: '套餐详情',\n    per: '次',\n    packageContent: '套餐包含内容为：',\n    num: '{type}次数',\n    limitTime: '有效期',\n    month: '月',\n    payNow: '立即购买',\n    contactUs: '联系我们 | 扫码联系上上签专业顾问了解',\n    compareInfo1: '使用说明：',\n    compareInfo2: '{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；',\n    compareInfo3: '{index}、按上传的合同{per}数统计用量',\n    codePay: '请用扫码支付',\n    aliPay: '支付宝支付',\n    wxPay: '微信支付',\n    payIno: '开通功能 | 购买对象 | 支付金额',\n    finishPay: '完成支付',\n    paySuccess: '购买成功',\n    originFile: '原始合同文件',\n    compareFile: '比对合同文件',\n    documentSelect: '选择文件',\n    comparisonResult: '比对结果',\n    history: '历史记录',\n    currentHistory: '文档记录',\n    noData: '暂无数据',\n    differences: '{num}处差异',\n    historyLog: '{num}条记录',\n    uploadLimit: '将要比对的文件拖拽至此上传 | 目前支持PDF（含PDF扫描件）、Word文件',\n    dragInfo: '释放鼠标完成上传',\n    uploadError: '文件格式不支持',\n    pageNum: '第{page}页',\n    difference: '差异{num}',\n    download: '下载比对结果',\n    comparing: '合同比对中...',\n    tip: '提示',\n    confirm: '确定',\n    toBuy: '去购买',\n    translate: '合同翻译',\n    doCompare: '比对',\n    doTranslate: '翻译',\n    review: '合同审查',\n    doReview: '审查',\n    reviewUploadFile: '将被审查的文件拖拽至此上传',\n    reviewUpload: '将审查依据拖拽至此上传 | 如：《经销商管理办法》《公司采购制度文件》等用于审查合同的公司规章制度性文件 | 目前仅支持PDF、Word文件',\n    reviewOriginFile: '被审查合同',\n    reviewTargetFile: '审查依据',\n    reviewResult: '审查结果',\n    uploadReviewFile: '上传审查依据文件',\n    risk: '风险点{num}',\n    risks: '{num}处风险点',\n    startReview: '开始审查',\n    reviewing: '合同审查中...',\n    noRisk: '审查已完成，未发现风险',\n    allowUpload: '可上传《公司采购管理办法》等可指导合同审查的规章制度条例，或上传公司红线规定、部门业务指导等， | 如：甲方需在合同签订后的5日内完成付款。',\n    notAllowUpload: '不要以模糊语句或者原则性描述作为审查依据， | 如：所有合同条款不得违法相关法律法规要求。',\n    resumeReview: '继续下一份',\n    close: '关闭',\n    extract: '合同抽取',\n    extractTitle: '期望抽取的关键词',\n    selectKeyword: '请从下方“关键词”中勾选',\n    keyword: '关键词',\n    addKeyword: '添加{keyword}',\n    introduce: '{keyword}释义',\n    startExtract: '开始抽取',\n    extractTargetFile: '被抽取合同',\n    extractKeyWord: '抽取关键词',\n    extracting: '合同抽取中',\n    extractResult: '抽取结果',\n    extractUploadFile: '将被抽取的文件拖拽至此上传',\n    needExtractKeyword: '请选择期望抽取的关键词',\n    summary: '合同摘要',\n    keySummary: '关键词内容摘要',\n    deleteKeywordConfirm: '确定要删除该关键词吗？',\n    keywordPosition: '关键词相关位置',\n    riskJudgement: '风险判断',\n    judgeTargetContract: '被判断合同',\n    interpretTargetContract: '被解读合同',\n    startJudge: '开始风险判断',\n    startInterpret: '开始解读',\n    uploadText: '请上传需要进行风险判断的文件',\n    interpretText: '请上传需要进行解读的文件',\n    startTips: '现在我们可以开始判断风险了',\n    interpretTips: '现在我们可以开始进行解读了',\n    infoExtract: '协议提取'\n  },\n  batchImport: {\n    iKnow: '我知道了'\n  },\n  templateCommon: {\n    tip: '提示'\n  },\n  mgapprovenote: {\n    SAQ: '问卷调查',\n    analyze: '分析',\n    annotate: '批注',\n    law: '法规',\n    case: '案例',\n    translate: '翻译',\n    mark: '标记',\n    tips: '以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记',\n    limit: '使用次数已达上限，如有持续使用的需求请填写表单，我们客服人员会联系你。',\n    confirmTxt: '去填写',\n    content: '查找相关段落',\n    experience: '业务经验',\n    datas: '相关数据',\n    terms: '类似条款',\n    original: '来源',\n    export: '导出内容',\n    preview: '合同预览',\n    history: '历史记录'\n  },\n  sealConfirm: {\n    title: '印章确认页',\n    header: '确认印章',\n    signerEnt: '签约企业：',\n    abnormalSeal: '异常印章：',\n    sealNormal: '印章正常',\n    tip1: '请确认印章是否正常可用，如果正常，可点击“印章正常”按钮，后续该企业再使用此印章，系统将不再给您推送异常提醒。',\n    tip2: '如果印章有问题，请及时与签约方沟通更换印章，重新发送合同与其签署，或者进行驳回重签。'\n  },\n  userCentral: userCentral,\n  ...console,\n  ...consts,\n  keyInfoExtract: {\n    operate: '合同信息提取',\n    contractType: '预测的合同类型',\n    tooltips: '选择需要提取的关键信息',\n    predictText: '正在预测合同类型',\n    extractText: '正在提取合同信息',\n    errorMessage: '你的使用额度已经用完，如有进一步需求可以填写表单，我们会联系你，为你补充更多的用量。',\n    result: '提取结果：'\n  },\n  judgeRisk: {\n    title: 'AI律师',\n    deepInference: 'AI法务',\n    showAll: '展开全文',\n    tips: '正在判断合同风险',\n    dialogTitle: '“AI律师”审合同',\n    aiInterpret: 'AI解读'\n  },\n  sealDistribute: {\n    requestSeal: '申请印章分配',\n    company: '公司',\n    applicant: '申请人',\n    accountID: '账号',\n    submissionTime: '时间',\n    status: '状态',\n    agree: '已同意',\n    unAgree: '已驳回',\n    ifAgree: '如果同意，',\n    applyTime: '申请人印章时间时间为：',\n    to: '至',\n    placeHolderTime: '年-月-日',\n    senderCompany: '发件方企业',\n    documentTitle: '合同标题',\n    sealApplicationScope: '印章适用范围',\n    applyforSeal: '申请印章',\n    reject: '驳回',\n    approve: '同意'\n  },\n  sealApproval: {\n    requestSeal: '申请印章分配',\n    sealRight: '印章权限',\n    allEntContract: '所有企业发来的合同',\n    partEntContract: '部分企业发来的合同：',\n    pleaseInputRight: '请输入权限',\n    successTransfer: '交接成功后，',\n    getRight: '将可以获得以上权限或可直接编辑分配新的签署权限。',\n    signAllEntContract: '签署所有企业发送的合同',\n    sign: '签署',\n    sendContract: '发送的合同',\n    sealUseTime: '印章使用时间：',\n    currentStatus: '当前状态：',\n    takeBackSeal: '收回印章',\n    agree: '同意',\n    hasAgree: '已同意',\n    hasReject: '已驳回',\n    hasDone: '已完成',\n    ask: '将',\n    giveYou: '的印章分配给你',\n    hopeAsk: '希望将',\n    hopeGive: '的印章交接于',\n    hopeGiveYou: '的相关印章交接与您',\n    noSettingTime: '无时间设置',\n    approvalSuccess: '审批成功',\n    getSealSuccess: '印章获取成功'\n  },\n  workspace: {\n    create: '已创建',\n    reviewing: '审查中',\n    completed: '已完成',\n    noData: '暂无数据',\n    introduce: '{keyword}释义',\n    termsDetail: '术语详情',\n    extractFormat: '提取格式',\n    optional: '选填',\n    required: '必填',\n    operate: '操作',\n    detail: '详情',\n    delete: '删除',\n    agreement: {\n      uploadError: '只能上传PDF、DOC、DOCX文件!',\n      extractionRequest: '已发起提取请求，请稍后至术语列表中查看提取结果',\n      upload: '文件上传',\n      define: '术语定义',\n      extract: '协议抽取',\n      drag: '将文件拖拽到此处，或',\n      add: '点击添加',\n      format: '支持doc、docx、pdf格式',\n      fileName: '文件名称',\n      status: '状态',\n      completed: '上传完成',\n      failed: '上传失败',\n      size: '大小',\n      terms: '术语',\n      success: '文件抽取完成，共{total}个',\n      ongoing: '文件正在抽取中...共{total}个',\n      tips: '跳过该界面不影响抽取结果',\n      others: '继续上传',\n      result: '跳转至抽取结果下载页面',\n      curProgress: '当前进度: ',\n      refresh: '刷新',\n      details: '已加载{successNum}个，共{length}个',\n      start: '开始抽取',\n      more: '添加文件',\n      skip: '跳过抽取，完成上传。',\n      tiqu: '开始提取',\n      chouqu: '开始抽取'\n    },\n    review: {\n      distribution: '分发审查',\n      Incomplete: '未结束',\n      createReview: '创建审查',\n      manageReview: '审查管理',\n      reviewDetail: '审查详情',\n      reviewId: '审查编号',\n      reviewStatus: '审查状态',\n      reviewName: '审查名称',\n      reviewStartTime: '审查发起时间',\n      reviewCompleteTime: '审查结束时间',\n      reviewDesc: '版本：版本{reviewVersion}  |  审阅编号：{reviewId}',\n      distribute: '发起审查',\n      drag: '拖动待审查的协议到当前区域',\n      content: '待审阅的内容',\n      current: '待分发记录',\n      history: '历史记录',\n      page: '第{page}页：',\n      users: '需要审阅的用户',\n      message: '留言',\n      modify: '修改',\n      placeholder: '多个用户请使用分号\";\"进行分隔',\n      submit: '确定',\n      reupload: '重新上传协议审查',\n      finish: '结束审查',\n      reviewSummary: '审查概要',\n      initiator: '审查发起人',\n      versionSummary: '版本概要',\n      version: '版本',\n      versionOrder: '第{version}版',\n      curReviewStatus: '当前版本审查状态',\n      curReviewVersion: '当前版本',\n      curReviewPopulation: '当前版本审查人数',\n      curReviewStartTime: '当前版本审查发起时间',\n      curReviewInitiator: '当前版本审查发起人',\n      checkComments: '聚合查看修订意见',\n      overview: '审查结果速览',\n      reviewer: '审查人',\n      reviewResult: '审查结果',\n      replyTime: '回复时间',\n      agreement: '审查的协议',\n      files: '相关协议',\n      fileName: '协议名称',\n      numberOfModificationSuggestions: '修改意见数',\n      uploadTime: '上传时间',\n      download: '下载',\n      dispatch: '分发',\n      recent: '最新审查时间：',\n      replyContent: '审查回复内容',\n      advice: '协议修改意见',\n      noIdea: '暂无修改意见',\n      origin: '原文内容：',\n      revised: '修改后内容：',\n      suggestion: '修改意见：',\n      dateMark: '{name} 在 <span style=\"color: #0988EC\">版本{version}</span>写于 {date}',\n      unReviewed: '暂未审查',\n      revisionFiles: '修订协议',\n      staffReplyAggregation: '聚合修订信息',\n      staffReply: '{name}的审查信息',\n      tips: '提示',\n      tipsContent: '结束后此次审查将不再支持分发及后续操作，是否继续',\n      confirm: '确定',\n      cancel: '取消',\n      successMessage: '已结束',\n      PASS: '通过',\n      REJECT: '不通过',\n      uploadErrorMessage: '目前只支持上传docx格式的文件',\n      successInitiated: '已发起审查',\n      autoDistribute: '智能分发',\n      requiredUsers: '需要审查的用户',\n      contentToReview: '审查的内容',\n      termDetails: '术语详情',\n      term: '术语',\n      aiDistribute: 'AI智能分发',\n      noData: '暂无数据',\n      docIconAlt: '文档图标',\n      docxIconAlt: 'DOCX图标',\n      pdfIconAlt: 'PDF图标',\n      requiredUsersError: '请填写需要审查的用户',\n      selectContentError: '请选择审查的内容',\n      initiateReviewSuccess: '已发起审查',\n      syncInitiated: '已发起同步'\n    },\n    contentTracing: {\n      title: '内容溯源',\n      fieldContent: '字段内容',\n      originalResult: '原始结果',\n      contentSource: '内容源自',\n      page: '第'\n    }\n  },\n  hubblePackage: {\n    title: '我的套餐',\n    details: '套餐详情',\n    remainingPages: '剩余总页数',\n    pages: '页',\n    usedPages: '已用',\n    remaining: '可用剩余',\n    total: '共',\n    expiryTime: '到期时间',\n    amount: '数目',\n    unitPrice: '单价',\n    copy: '份',\n    words: '千字'\n  },\n  workspaceIndex: {\n    title: '工作空间',\n    package: '套餐用量',\n    agreement: '协议管理',\n    review: '审查管理',\n    term: '术语管理'\n  },\n  agreement: {\n    title: '协议管理',\n    exportList: '导出协议列表',\n    exportAllChecked: 'Excel(全部字段，勾选协议)',\n    exportCurrentChecked: 'Excel(当前字段，勾选协议)',\n    exportAllMatched: 'Excel(全部字段，符合条件)',\n    exportCurrentMatched: 'Excel(当前字段，符合条件)',\n    add: '添加协议',\n    upload: '上传协议',\n    operation: '操作',\n    download: '下载协议',\n    details: '详情',\n    delete: '删除',\n    relatedTaskStatus: '关联提取任务状态',\n    confirmDelete: '是否确认删除当前协议',\n    prompt: '提示',\n    booleanYes: '是',\n    booleanNo: '否',\n    defaultExportName: 'export.xlsx',\n    taskNotStarted: '抽取任务未开始',\n    taskStarted: '抽取任务已开始 (内容检索中)',\n    contentSearchCompleted: '内容检索已完成 (结果格式化中)',\n    resultFormattingCompleted: '结果格式化已完成 (结果校对中)',\n    resultVerificationCompleted: '结果校对已完成'\n  },\n  filter: {\n    filter: '过滤',\n    refreshExtraction: '重新提取',\n    extractTerms: '术语定义提取',\n    refreshList: '刷新列表',\n    currentCondition: '当前条件下，展示的协议。',\n    when: '当',\n    selectCondition: '请选择条件',\n    enterCondition: '请输入条件',\n    yes: '是',\n    no: '否',\n    addCondition: '添加条件',\n    reset: '重置',\n    confirm: '确定',\n    and: '且',\n    or: '或',\n    equals: '等于',\n    notEquals: '不等于',\n    contains: '包含',\n    notContains: '不包含',\n    greaterThan: '大于',\n    greaterThanOrEquals: '大于等于',\n    lessThan: '小于',\n    lessThanOrEquals: '小于等于',\n    emptyCondition: '过滤条件不能为空'\n  },\n  fieldConfig: {\n    button: '字段配置',\n    header: '期望展示的字段',\n    submit: '完成',\n    cancel: '取消'\n  },\n  agreementDetail: {\n    detail: '协议详情',\n    add: '添加协议',\n    id: '协议编号',\n    file: '协议文件',\n    download: '协议下载',\n    replaceFile: '协议文件替换',\n    uploadFile: '协议文件上传',\n    relatedExtractionStatus: '关联提取任务状态',\n    dataSource: '数据来源',\n    yes: '是',\n    no: '否',\n    select: '请选择',\n    input: '请输入',\n    save: '保存',\n    cancel: '取消',\n    page: '第 {page} 页',\n    addDataSource: '添加数据来源',\n    pageNo: '第',\n    pageSuffix: '页',\n    submit: '提交',\n    inputDataSource: '请输入数据来源内容',\n    pageFormatError: '页码只支持英文逗号隔开的数字或者纯数字',\n    confirmDelete: '是否确认删除当前数据来源',\n    tips: '提示',\n    uploadSuccess: '上传成功'\n  },\n  termManagement: {\n    title: '术语管理',\n    batchDelete: '批量删除',\n    import: '术语导入',\n    export: '术语导出',\n    add: '+ 添加术语',\n    name: '术语名称',\n    definition: '术语释义',\n    formatRequirement: '提取格式要求',\n    dataFormat: '数据格式',\n    operation: '操作',\n    edit: '修改',\n    delete: '删除',\n    detail: '术语详情',\n    addTitle: '添加术语',\n    namePlaceholder: '填写你的专业术语',\n    definitionPlaceholder: '填写术语的释义',\n    formatRequirementPlaceholder: '填写术语的提取格式要求',\n    dataFormatPlaceholder: '期望提取的术语格式要求',\n    cancel: '取消',\n    confirmEdit: '确认修改',\n    importTitle: '术语导入',\n    uploadTemplate: '上传术语模板文件',\n    downloadTemplate: '下载术语模板文件',\n    extractType: {\n      text: '文本',\n      longText: '长文本',\n      date: '日期',\n      number: '数值',\n      boolean: '是/否'\n    },\n    importSuccess: '导入成功',\n    deleteConfirm: '是否确认删除当前术语',\n    prompt: '提示',\n    nameEmptyError: '术语名称不能为空'\n  },\n  agent: {\n    extractTitle: '信息提取',\n    riskTitle: 'AI律师',\n    feedback: '问卷反馈',\n    toMini: '去小程序查看',\n    otherContract: '让我看看其他合同的隐藏风险?',\n    others: '其他',\n    submit: '发送',\n    autoExtract: '自动进行下一步提取直到提取结束',\n    autoRisk: '自动进行下一步分析直到分析结束',\n    aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',\n    chooseRisk: '请选择需要进行分析的文件',\n    chooseExtract: '请选择需要进行提取的文件',\n    analyzing: '内容分析中',\n    advice: '修改建议生成中',\n    options: '选项生成中',\n    inputTips: '请输入确切内容',\n    chargeTip: '余额不足，请充值',\n    original: '原文',\n    revision: '修改建议',\n    diff: '对比',\n    locate: '获取原文定位中',\n    custom: '请输入自定义审查规则',\n    content: '原文位置',\n    satisfy: '对分析结果满意，继续下一项分析',\n    dissatisfy: '对分析结果不满意，重新进行分析',\n    selectFunc: '请选择你期望使用的功能。',\n    deepInference: 'AI法务',\n    deepThinking: '深度思考中',\n    deepThoughtCompleted: '已深度思考',\n    reJudge: '重新判断',\n    confirm: '确认',\n    tipsContent: '重新判断将会扣除份数，是否继续?',\n    useLawyer: '使用AI律师',\n    interpretFinish: 'AI解读已完成',\n    exportPDF: '导出PDF报告',\n    defaultExportName: 'export.pdf',\n    exporting: '正在导出报告，请稍后...'\n  },\n  authorize: {\n    title: '使用须知',\n    content: '开启智能合同，AI帮你分析，让你的工作更轻松高效！同意以下内容，即可体验。',\n    cancel: '不用了',\n    confirm: '同意并使用',\n    contract: '查看《哈勃产品使用须知》'\n  },\n  hubbleEntry: {\n    smartAdvisor: '智能签约顾问',\n    tooltips: '您暂未开通此功能，可联系上上签电子签约顾问购买开通。',\n    confirm: '好的'\n  },\n  lang: 'zh'\n};", "map": {"version": 3, "names": ["utils", "mixin", "components", "docList", "console", "userCentral", "home", "sign", "entAuth", "consts", "docTranslation", "footerAd", "title", "content1", "content2", "bankContent", "bankTip1", "bankTip2", "bankFooter", "cancel", "continue", "commonFooter", "record", "hubbleRecordId", "openPlatform", "aboutBestSign", "contact", "recruitment", "help", "copyright", "company", "ssqLogo", "provideTip", "ssq", "provide", "signHotline", "langSwitch", "login", "pswLogin", "usePswLogin", "verifyLogin", "useVerifyLogin", "scanLogin", "scanFailure", "scanSuccess", "scanLoginTip", "appLoginTip", "downloadApp", "forgetPsw", "noAccount", "registerNow", "accountPlaceholder", "passwordPlaceholder", "pictureVer", "verifyCodePlaceholder", "getVerifyCode", "noRegister", "or", "errAccountOrPwdTip", "errAccountOrPwdTip2", "errEmailOrTel", "errPwd", "verCodeFormatErr", "grapVerCodeErr", "grapVerCodeFormatErr", "lackAccount", "lackGrapCode", "getVerCodeTip", "loginView", "reg<PERSON><PERSON><PERSON>", "takeViewBtn", "resendCode", "regTip", "haveRead", "bestsignAgreement", "and", "digitalCertificateAgreement", "privacyPolicy", "sendSuc", "lackVerCode", "lackPsw", "notMatch", "cookieTip", "wrongLink", "footerTips", "bestSign", "bestSignDescription", "forgetPswStep", "pictureVerCodeInput", "accountInput", "smsCodeInput", "haveRegistereLoginNow", "nextStep", "setNewPasswordInput", "passwordResetSucceeded", "accountNotRegistered", "loginAndDownload", "registerAndDownload", "inputPhone", "readContract", "errorPhone", "companyCert", "regAndCompanyCert", "handwrite", "picSubmitTip", "<PERSON><PERSON><PERSON><PERSON>", "replaceAllSignature", "replaceAllSeal", "canUseSeal", "applyForSeal", "moreTip", "uploadPic", "use", "clickExtend", "upload", "uploadTip1", "uploadTip2", "rewrite", "confirm", "upgradeBrowser", "submitTip", "title2", "QRCode", "needWrite", "needRewrite", "ok", "clearTips", "isBlank", "success", "signNotMatch", "signNotMatchExact", "msg", "successToUser", "successToSign", "cantGet", "common", "advice", "notEmpty", "enter6to18n", "ssqDes", "signPwdType", "enterActualEntName", "enterCorrectName", "enterCorrectPhoneNum", "enterCorrectEmail", "imgCodeErr", "enterCorrectIdNum", "enterCorrectFormat", "enterCorrectDateFormat", "entCertification", "subBaseInfo", "corDocuments", "license", "uploadLimit", "hi", "exit", "hotline", "acceptProtectingMethod", "comfirmSubmit", "cerficated", "serialNumber", "validity", "entName", "nationalNo", "corporationName", "city", "entCertificate", "certificationAuthority", "bestsignPlatform", "notIssued", "date", "congratulations", "rejectMessage", "recertification", "waitMessage", "<PERSON><PERSON>uth", "info", "submitPicError", "contractDrafting", "contractManagement", "userCenter", "service", "enterpriseConsole", "groupConsole", "startSigning", "contractType", "sendContract", "shortcuts", "setting", "signNum", "contractNum", "contractInFormation", "type", "basicInformation", "more", "certified", "account", "time", "day", "sendContractNum", "num", "realName", "update", "mark", "countDes", "chargeNow", "myRechargeOrder", "statusTip", "useTemplate", "useLocalFile", "enterEnterpriseName", "docDetail", "canNotOperateTip", "shareSignLink", "faceSign", "faceFirstVerifyCodeSecond", "contractRecipient", "personalOperateLog", "recordDialog", "user", "operate", "view", "download", "remarks", "operateRecords", "borrowingRecords", "currentHolder", "currentEnterprise", "companyInterOperationLog", "receiverMap", "sender", "signer", "ccUser", "downloadCode", "noTagToAddHint", "requireFieldNotAllowEmpty", "modifySuccess", "uncategorized", "notAllowModifyContractType", "setTag", "contractTag", "plsInput", "plsInputCompanyInternalNum", "companyInternalNum", "none", "plsSelect", "modify", "contractDetailInfo", "slideContentTip", "signNotice", "contractAncillaryInformation", "content", "document", "downloadDepositConfirmTip", "hint", "confrim", "downloadTip", "transferSuccessGoManagePage", "claimSign", "downloadDepositPageTip", "resend", "proxySign", "notPassed", "approving", "signning", "notarized", "currentFolder", "archive", "deadlineForSigning", "endFinishTime", "contractImportTime", "contractSendTime", "back", "contractInfo", "basicInfo", "personAccount", "entAccount", "operator", "signStartTime", "signDeadline", "contractExpireDate", "edit", "settings", "from", "folder", "reason", "approval", "viewAttach", "downloadContract", "downloadAttach", "print", "certificatedTooltip", "needMeSign", "needMeApproval", "inApproval", "needOthersSign", "signComplete", "signOverdue", "rejected", "revoked", "contractCompleteTime", "contractEndTime", "reject", "revoke", "viewSignOrders", "viewApprovalProcess", "completed", "cc", "ccer", "signSubject", "signSubjectTooltip", "IDNumber", "state", "notice", "detail", "RealNameCertificationRequired", "RealNameCertificationNotRequired", "MustHandwrittenSignature", "handWritingRecognition", "privateMessage", "attachment", "rejectReason", "notSigned", "notViewed", "viewed", "signed", "viewedNotSigned", "notApproval", "remindSucceed", "reviewDetails", "close", "entInnerOperateDetail", "approve", "disapprove", "applySeal", "applied", "apply", "toOtherSign", "handOver", "approvalOpinions", "useSeal", "signature", "fill", "times", "place", "contractDetail", "viewMore", "collapse", "signLink", "saveQRCode", "signQRCode", "copy", "copySucc", "copyFail", "unCertified", "claimed", "uploadFile", "thumbnails", "isUploading", "move", "delete", "replace", "tip", "understand", "totalPages", "matchErr", "inUploadingDeleteErr", "timeOutErr", "imgUnqualified", "imgBiggerThan20M", "error", "hasCATip", "internalNumber", "contractName", "contractNameTooltip", "toSelect", "contractTypeErr", "signDeadLine", "signDeadLineTooltip", "selectDate", "expireDateTooltip", "necessary", "notNecessary", "dateTips", "contractTitleErr", "contractTitleLengthErr", "template", "templateList", "linkBoxTip", "dynamicTemplateUpdate", "newVersionDesc", "updateTip", "connectUs", "sendCode", "fail", "sendCodeGuide", "tip1", "main", "sub", "line1", "line2", "tip2", "tip3", "tip4", "style", "text", "x", "fontSize", "resetPwd", "<PERSON><PERSON><PERSON><PERSON>", "oldPlaceholder", "new<PERSON>abel", "newPlaceholder", "submit", "errorMsg", "oldRule", "newRule", "personAuthIntercept", "name", "id", "descNoAuth", "des<PERSON><PERSON>", "descNoSame", "descNoAuth1", "descNoAuth2", "tips", "goOn", "goMore", "descNoSame1", "descNoSame2", "goHome", "authInfo", "in", "finishAuth", "ask", "reAuthBtnText", "changePhoneText", "changePhoneTip1", "changePhoneTip2", "confirmReject", "authIntercept", "authTip", "viewAndSign", "tips2", "requestOtherAnth", "goAuth", "requestSomeoneList", "ent", "accountPH", "send", "lackEntName", "errAccount", "successfulSent", "thirdPartApprovalDialog", "title1", "cancelBtnText", "confirmBtnText", "iKnow", "endSignEarlyPrompt", "signPrompt", "signTotalCountTip", "signatureTip", "hasSigned", "hasNotSigned", "noNeedSealTip", "commonNomal", "yesterday", "ssqPlatform", "ssqTestPlatform", "pageExpiredTip", "pswCodeSimpleTip", "transferAdminDialog", "transfer", "confirmAdmin", "choseBoxForReceiver", "dataNeedForReceiver", "dataFromDataBox", "searchTp", "search", "boxNotFound", "localCommon", "seal", "signDate", "qrCode", "number", "dynamicTable", "terms", "checkBox", "radioBox", "image", "confirmSeal", "confirmRemark", "optional", "require", "comboBox", "twoFactor", "signTip", "settingTwoFactor", "step1", "step1Tip", "step2", "step2Tip1", "step2Tip2", "step3", "step3Tip", "verifyCode6", "iosAddress", "android<PERSON><PERSON><PERSON>", "chromeVerify", "nextBtn", "confirmSign", "dynamicCode", "password", "pleaseInput", "twoFactorTip", "passwordTip", "twoFactorAndPasswordTip", "passwordTip2", "dynamicVerifyInfo", "functionSupportDialog", "inputTip", "useSence", "useSenceTip", "estimatedOnlineTime", "requireContent", "requireContentTip", "getSupport", "callServiceHotline", "useSenceNotEmpty", "requrieContentNotEmpty", "oneWeek", "oneMonth", "other", "submitSuccess", "submitTrial", "toTrial", "trialTip", "applyTrial", "trialSuccTip", "goBuy", "trialTipMap", "tip5", "contactAdminTip", "trialEndTip", "trialRemainDayTip", "trialEnd", "trialEndMap", "deactivateTip", "feature1", "remove1", "feature2", "remove2", "feature3", "remove3", "feature4", "remove4", "setSignPwdDialog", "saveAndReturnSign", "changeEmailVerify", "changePhoneVerify", "contractCompare", "reUpload", "packagePurchase", "packagePurchaseTitle", "payOnce", "myPackage", "packageDetail", "per", "packageContent", "limitTime", "month", "payNow", "contactUs", "compareInfo1", "compareInfo2", "compareInfo3", "codePay", "ali<PERSON>ay", "wxPay", "payIno", "finishPay", "paySuccess", "originFile", "compareFile", "documentSelect", "comparisonResult", "history", "currentHistory", "noData", "differences", "historyLog", "dragInfo", "uploadError", "pageNum", "difference", "comparing", "<PERSON><PERSON><PERSON>", "translate", "doCompare", "doTranslate", "review", "doReview", "reviewUploadFile", "reviewUpload", "reviewOriginFile", "reviewTargetFile", "reviewResult", "uploadReviewFile", "risk", "risks", "startReview", "reviewing", "noRisk", "allowUpload", "notAllowUpload", "resume<PERSON><PERSON>iew", "extract", "extractTitle", "selectKeyword", "keyword", "addKeyword", "introduce", "startExtract", "extractTargetFile", "extractKeyWord", "extracting", "extractResult", "extractUploadFile", "needExtractKeyword", "summary", "key<PERSON><PERSON><PERSON>y", "deleteKeywordConfirm", "keywordPosition", "riskJudgement", "judge<PERSON><PERSON><PERSON>Contract", "interpretTargetContract", "startJudge", "startInterpret", "uploadText", "interpretText", "startTips", "interpretTips", "infoExtract", "batchImport", "templateCommon", "mgapprovenote", "SAQ", "analyze", "annotate", "law", "case", "limit", "confirmTxt", "experience", "datas", "original", "export", "preview", "sealConfirm", "header", "signerEnt", "abnormalSeal", "sealNormal", "keyInfoExtract", "tooltips", "predictText", "extractText", "errorMessage", "result", "judgeRisk", "deepInference", "showAll", "dialogTitle", "aiInterpret", "sealDistribute", "requestSeal", "applicant", "accountID", "submissionTime", "status", "agree", "unAgree", "ifAgree", "applyTime", "to", "placeHolderTime", "senderCompany", "documentTitle", "sealApplicationScope", "applyforSeal", "sealApproval", "sealRight", "allEntContract", "partEntContract", "pleaseInputRight", "successTransfer", "getRight", "signAllEntContract", "sealUseTime", "currentStatus", "takeBackSeal", "hasAgree", "hasReject", "hasDone", "<PERSON><PERSON><PERSON>", "hopeAsk", "hopeGive", "hopeGiveYou", "noSettingTime", "approvalSuccess", "getSealSuccess", "workspace", "create", "termsDetail", "extractFormat", "required", "agreement", "extractionRequest", "define", "drag", "add", "format", "fileName", "failed", "size", "ongoing", "others", "curProgress", "refresh", "details", "start", "skip", "tiqu", "chouqu", "distribution", "Incomplete", "createReview", "manageReview", "reviewDetail", "reviewId", "reviewStatus", "reviewName", "reviewStartTime", "reviewCompleteTime", "reviewDesc", "distribute", "current", "page", "users", "message", "placeholder", "reupload", "finish", "reviewSummary", "initiator", "versionSummary", "version", "versionOrder", "curRevie<PERSON><PERSON><PERSON><PERSON>", "curReviewV<PERSON><PERSON>", "curReviewPopulation", "curReviewStartTime", "curReviewInitiator", "checkComments", "overview", "reviewer", "replyTime", "files", "numberOfModificationSuggestions", "uploadTime", "dispatch", "recent", "replyContent", "noIdea", "origin", "revised", "suggestion", "dateMark", "unReviewed", "revisionFiles", "staffReplyAggregation", "staffReply", "tipsContent", "successMessage", "PASS", "REJECT", "uploadErrorMessage", "successInitiated", "autoDistribute", "requiredUsers", "contentToReview", "termDetails", "term", "aiDistribute", "docIconAlt", "docxIconAlt", "pdfIconAlt", "requiredUsersError", "selectContentError", "initiateReviewSuccess", "syncInitiated", "contentTracing", "<PERSON><PERSON><PERSON>nt", "originalResult", "contentSource", "hubblePackage", "remainingPages", "pages", "usedPages", "remaining", "total", "expiryTime", "amount", "unitPrice", "words", "workspaceIndex", "package", "exportList", "exportAllChecked", "exportCurrentChecked", "exportAllMatched", "exportCurrentMatched", "operation", "relatedTaskStatus", "confirmDelete", "prompt", "booleanYes", "booleanNo", "defaultExportName", "taskNotStarted", "taskStarted", "contentSearchCompleted", "resultFormattingCompleted", "resultVerificationCompleted", "filter", "refreshExtraction", "extractTerms", "refreshList", "currentCondition", "when", "selectCondition", "enterCondition", "yes", "no", "addCondition", "reset", "equals", "notEquals", "contains", "notContains", "greaterThan", "greaterThanOrEquals", "lessThan", "lessThanOrEquals", "emptyCondition", "fieldConfig", "button", "agreementDetail", "file", "replaceFile", "relatedExtractionStatus", "dataSource", "select", "input", "save", "addDataSource", "pageNo", "pageSuffix", "inputDataSource", "pageFormatError", "uploadSuccess", "termManagement", "batchDelete", "import", "definition", "formatRequirement", "dataFormat", "addTitle", "namePlaceholder", "definitionPlaceholder", "formatRequirementPlaceholder", "dataFormatPlaceholder", "confirmEdit", "importTitle", "uploadTemplate", "downloadTemplate", "extractType", "longText", "boolean", "importSuccess", "deleteConfirm", "nameEmptyError", "agent", "riskTitle", "feedback", "to<PERSON><PERSON>", "otherContract", "autoExtract", "autoRisk", "aiGenerated", "chooseRisk", "chooseExtract", "analyzing", "options", "inputTips", "chargeTip", "revision", "diff", "locate", "custom", "satisfy", "dissatisfy", "selectFunc", "deepThinking", "deepThoughtCompleted", "reJudge", "useLawyer", "interpretFinish", "exportPDF", "exporting", "authorize", "contract", "hubbleEntry", "smartAdvisor", "lang"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/zh.js"], "sourcesContent": ["// 语言为中文时的文案\nimport utils from './module/utils/utils-zh.js';\nimport mixin from './module/mixins/mixins-zh.js';\nimport components from './module/components/components-zh.js';\n\nimport docList from './module/docList/docList-zh.js';\nimport console from './module/console/console-zh.js';\nimport userCentral from './module/usercentral/usercentral-zh.js';\nimport home from './module/home/<USER>';\nimport sign from './module/sign/sign-zh.js';\nimport entAuth from './module/entAuth/entAuth-zh.js';\nimport consts from './module/consts/zh.js';\nimport docTranslation from './module/docTranslation/docTranslation-zh.js';\n\nexport default {\n    ...utils,\n    ...mixin,\n    ...components,\n    ...docList,\n    ...docTranslation,\n    footerAd: {\n        title: '跳转提示',\n        content1: '即将跳转到第三方服务页面',\n        content2: '是否继续？',\n        bankContent: '即将进入宁波银行“容易贷”企业贷款介绍页面',\n        bankTip1: '让宁波银行主动给我打电话',\n        bankTip2: '向我发送一条短信，介绍如何办理',\n        bankFooter: '加宁波银行专属客服，一对一服务我',\n        cancel: '取消',\n        continue: '继续',\n    },\n    commonFooter: {\n        record: 'ICP主体备案号：浙ICP备********号',\n        hubbleRecordId: '网信算备：330106973391501230011',\n        openPlatform: '开放平台',\n        aboutBestSign: '关于公司',\n        contact: '联系我们',\n        recruitment: '诚聘英才',\n        help: '帮助中心',\n        copyright: '版权所有',\n        company: '杭州尚尚签网络科技有限公司',\n        ssqLogo: '上上签底栏Logo',\n        provideTip: '电子签约服务由',\n        ssq: '上上签',\n        provide: '提供',\n        signHotline: '签约服务热线',\n        langSwitch: '切换语言',\n    },\n    login: {\n        pswLogin: '密码登录',\n        usePswLogin: '使用密码登录',\n        verifyLogin: '验证码登录',\n        useVerifyLogin: '使用验证码登录',\n        scanLogin: '扫码登录',\n        scanFailure: '二维码已失效,请刷新重试',\n        scanSuccess: '扫码成功',\n        scanLoginTip: '请使用上上签APP扫一扫登录',\n        appLoginTip: '请在上上签APP中点击登录',\n        downloadApp: '下载上上签APP',\n        forgetPsw: '忘记密码',\n        login: '登录',\n        noAccount: '没有账号',\n        registerNow: '马上注册',\n        accountPlaceholder: '请输入手机或邮箱',\n        passwordPlaceholder: '请输入登录密码',\n        pictureVer: '请填写图片中的内容',\n        verifyCodePlaceholder: '请输入6位验证码',\n        getVerifyCode: '获取验证码',\n        noRegister: '尚未注册',\n        or: '或',\n        errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',\n        errAccountOrPwdTip2: '你输入的密码和账号不匹配',\n        errEmailOrTel: '请输入正确的邮箱或手机号!',\n        errPwd: '请输入正确的密码!',\n        verCodeFormatErr: '验证码错误',\n        grapVerCodeErr: '图形验证码错误',\n        grapVerCodeFormatErr: '图形验证码格式错误',\n        lackAccount: '请填写账号后再获取',\n        lackGrapCode: '请先填写图形验证码',\n        getVerCodeTip: '请获取验证码',\n\n        loginView: '登录并查看合同',\n        regView: '注册并查看合同',\n        takeViewBtn: '登录并签署',\n        resendCode: '重新获取',\n        regTip: '填写正确的验证码后上上签将为您创建账号',\n        haveRead: '我已阅读并同意',\n        bestsignAgreement: '上上签服务协议',\n        and: '和',\n        digitalCertificateAgreement: '数字证书使用协议',\n        privacyPolicy: '隐私政策',\n        sendSuc: '发送成功',\n        lackVerCode: '请先输入验证码',\n        lackPsw: '请先输入密码',\n        notMatch: '您输入的密码和账号不匹配',\n        cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',\n        wrongLink: '非法链接',\n        footerTips: '电子签约服务由<span>上上签</span>提供',\n        bestSign: '上上签',\n        bestSignDescription: '电子签约行业领跑者',\n        /** 忘记密码 /forgotPassword start */\n        forgetPswStep: '验证注册账号 | 重新设置密码',\n        pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',\n        accountInput: '账号 | 请填写您的账号',\n        smsCodeInput: '验证码 | 获取验证码',\n        haveRegistereLoginNow: '我已注册， | 马上登录',\n        nextStep: '下一步 | 提交',\n        setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',\n        passwordResetSucceeded: '密码重置成功!',\n        /** 忘记密码 /forgotPassword end */\n        accountNotRegistered: '账号未注册',\n        loginAndDownload: '登录并下载合同',\n        registerAndDownload: '注册并下载合同',\n        inputPhone: '请输入手机号',\n        readContract: '读取合同',\n        errorPhone: '手机格式错误',\n        companyCert: '进行企业认证',\n        regAndCompanyCert: '注册并进行企业认证',\n    },\n    ...sign,\n    handwrite: {\n        title: '手写签名',\n        picSubmitTip: '图片签名提交成功',\n        settingDefault: '设置为默认签名',\n        replaceAllSignature: '用于所有签名处',\n        replaceAllSeal: '用于所有盖章处',\n        canUseSeal: '我的印章',\n        applyForSeal: '申请用印',\n        moreTip: '将保存您的手写签名作为默认签名，仅用于合同签署。管理路径：【用户中心->签名管理】',\n        uploadPic: '上传照片',\n        use: '使用',\n        clickExtend: '点右箭头延长手写区',\n        upload: '上传签名图片',\n        uploadTip1: '提示：上传签名图片时请将签名充满整个图片',\n        uploadTip2: '签名请使用颜色较深或者纯黑色文字。',\n        rewrite: '重写',\n        cancel: '不写了',\n        confirm: '使用',\n        upgradeBrowser: '您的浏览器不支持画布手绘签名，请升级浏览器。',\n        submitTip: '手绘签名提交成功',\n        title2: '手绘您的签名',\n        QRCode: '扫码签名',\n        needWrite: '请手绘正确的姓名！',\n        needRewrite: '笔画无法辨认，请重新书写',\n        ok: '确定',\n        clearTips: '请书写清晰可辨的签名',\n        isBlank: '画布为空，请先手绘签名再提交！',\n        success: '手绘签名提交成功',\n        signNotMatch: '请正楷书写签名，须与实名证件信息一致。',\n        signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',\n        msg: {\n            successToUser: '已设置好新签名，请在web端点击“保存”按钮。',\n            successToSign: '新签名已生效，请到合同签署页查看。',\n            cantGet: '获取不到签名，请尝试使用其他浏览器！',\n        },\n    },\n    common: {\n\n        aboutBestSign: '关于公司',\n        contact: '联系我们',\n        recruitment: '诚聘英才',\n        copyright: '版权所有',\n        advice: '咨询建议',\n        notEmpty: '不能为空!',\n        enter6to18n: '请输入6-18位数字、大小写字母',\n        ssqDes: '电子签约云平台领导者',\n        openPlatform: '开放平台',\n        company: '杭州尚尚签网络科技有限公司',\n        help: '帮助中心',\n        errEmailOrTel: '请输入正确的邮箱或手机号!',\n        verCodeFormatErr: '验证码错误',\n        signPwdType: '请输入6位数字',\n        enterActualEntName: '请填写真实的企业名称',\n        enterCorrectName: '请输入正确的姓名',\n        enterCorrectPhoneNum: '请输入正确的手机号',\n        enterCorrectEmail: '请输入正确的邮箱',\n        imgCodeErr: '图形验证码错误',\n        enterCorrectIdNum: '请输入正确的证件号码',\n        enterCorrectFormat: '请输入正确的格式',\n        enterCorrectDateFormat: '请输入正确的日期格式',\n\n    },\n    entAuth: {\n        ...entAuth,\n        entCertification: '企业实名认证',\n        subBaseInfo: '提交基本信息',\n        corDocuments: '企业证件',\n        license: '营业执照',\n        upload: '点击上传',\n        uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',\n        hi: '你好',\n        exit: '退出',\n        help: '帮助',\n        hotline: '服务热线',\n        acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',\n        comfirmSubmit: '确认提交',\n        cerficated: '认证完成',\n        serialNumber: '证书序列号',\n        validity: '有效期',\n        entName: '企业名称',\n        nationalNo: '国家注册号',\n        corporationName: '法定代表人姓名',\n        city: '所在城市',\n        entCertificate: '企业实名证书',\n        certificationAuthority: '证书颁发机构',\n        bestsignPlatform: '上上签电子签约云平台',\n        notIssued: '未发放',\n        date: '{year}年{month}月{day}日',\n        congratulations: '恭喜您，成功完成企业实名认证',\n        continue: '继续',\n        rejectMessage: '由于如下原因，资料审核不通过，请核对',\n        recertification: '重新认证',\n        waitMessage: '客服将在一个工作日内完成审核，请耐心等待',\n    },\n    personalAuth: {\n        info: '提示',\n        submitPicError: '请上传照片后再使用',\n    },\n    home: {\n        ...home,\n        home: '首页',\n        contractDrafting: '合同起草',\n        contractManagement: '合同管理',\n        userCenter: '用户中心',\n        service: '服务',\n        enterpriseConsole: '企业控制台',\n        groupConsole: '集团控制台',\n        startSigning: '发起签约',\n        contractType: '发送普通合同 | 发送模板合同 ',\n        sendContract: '发送合同',\n        shortcuts: '快捷入口 | 没有任何文件快捷入口',\n        setting: '立即设置 | 设置更多快捷入口',\n        signNum: '签发量月度报表',\n        contractNum: '合同发送量 | 合同签署量',\n        contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',\n        type: '企业 | 个人',\n        basicInformation: '基本信息',\n        more: '更多',\n        certified: '已认证 | 未认证',\n        account: '账号',\n        time: '创建时间 |注册时间',\n        day: '日 | 月',\n        sendContractNum: '发送量 | 签署量',\n        num: '份',\n        realName: '立即企业实名 | 立即个人实名',\n        update: '产品最新公告',\n        mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',\n        countDes: {\n            1: '可发：对公合同',\n            2: '份',\n            3: '对私合同',\n            4: '份',\n        },\n        chargeNow: '立即充值',\n        myRechargeOrder: '我的充值订单',\n        statusTip: {\n            1: '需要我操作',\n            2: '需要他人签署',\n            3: '即将截止签约',\n            4: '签约完成',\n        },\n        useTemplate: '使用模板',\n        useLocalFile: '上传本地文件',\n        enterEnterpriseName: '请输入企业名称',\n    },\n    docDetail: {\n        canNotOperateTip: '无法{operate}合同',\n        shareSignLink: '分享签署链接',\n        faceSign: '刷脸签署',\n        faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',\n        contractRecipient: '合同收件方',\n        personalOperateLog: '个人合同操作日志',\n        recordDialog: {\n            date: '日期',\n            user: '用户',\n            operate: '操作',\n            view: '查看',\n            download: '下载',\n        },\n        remarks: '备注',\n        operateRecords: '操作记录',\n        borrowingRecords: '借阅记录',\n        currentHolder: '当前持有人',\n        currentEnterprise: '当前企业',\n        companyInterOperationLog: '公司内部操作日志',\n        receiverMap: {\n            sender: '合同发件人',\n            signer: '合同接收人',\n            ccUser: '合同抄送人',\n        },\n        downloadCode: '合同下载码',\n        noTagToAddHint: '还没有标签，请前往企业控制台添加',\n        requireFieldNotAllowEmpty: '必填项不能为空',\n        modifySuccess: '修改成功',\n        uncategorized: '未分类',\n        notAllowModifyContractType: '{type}中的合同不允许修改合同类型',\n        setTag: '设置标签',\n        contractTag: '合同标签',\n        plsInput: '请输入',\n        plsInputCompanyInternalNum: '请输入公司内部编号',\n        companyInternalNum: '公司内部编号',\n        none: '无',\n        plsSelect: '请选择',\n        modify: '修改',\n        contractDetailInfo: '合同详细信息',\n        slideContentTip: {\n            signNotice: '签约须知',\n            contractAncillaryInformation: '合同附属资料',\n            content: '内容',\n            document: '文档',\n        },\n        downloadDepositConfirmTip: {\n            title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',\n            hint: '提示',\n            confrim: '继续下载',\n            cancel: '取消',\n        },\n        downloadTip: {\n            title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',\n            hint: '提示',\n            confirm: '确定',\n            cancel: '取消',\n        },\n        transferSuccessGoManagePage: '转交成功，将返回合同管理页面',\n        claimSign: '认领签署',\n        downloadDepositPageTip: '下载签约存证页(脱敏版)',\n        resend: '重新发送',\n        proxySign: '代签署',\n        notPassed: '已驳回',\n        approving: '审批中',\n        signning: '签署中',\n        notarized: '已公正',\n        currentFolder: '当前文件夹',\n        archive: '归档',\n        deadlineForSigning: '截止签约时间',\n        endFinishTime: '签约完成/签约结束时间',\n        contractImportTime: '合同导入时间',\n        contractSendTime: '合同发送时间',\n        back: '返回',\n        contractInfo: '合同信息',\n        basicInfo: '基本信息',\n        contractNum: '合同编号',\n        sender: '发件方',\n        personAccount: '个人账号',\n        entAccount: '企业账号',\n        operator: '经办人',\n        signStartTime: '发起签约时间',\n        signDeadline: '签约截止时间',\n        contractExpireDate: '合同到期时间',\n        // none: '无',\n        edit: '修改',\n        settings: '设置',\n        from: '来源',\n        folder: '文件夹',\n        contractType: '合同类型',\n        reason: '理由',\n        sign: '签署',\n        approval: '审批',\n        viewAttach: '查看附页',\n        downloadContract: '下载合同',\n        downloadAttach: '下载签约存证',\n        print: '打印',\n        certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',\n        needMeSign: '需要我签署',\n        needMeApproval: '需要我审批',\n        inApproval: '审批中',\n        needOthersSign: '需要他人签署',\n        signComplete: '签约完成',\n        signOverdue: '逾期未签',\n        rejected: '已拒签',\n        revoked: '已撤销',\n        contractCompleteTime: '签约完成时间',\n        contractEndTime: '签约结束时间',\n        reject: '拒签',\n        revoke: '撤销',\n        download: '下载',\n        viewSignOrders: '查看签署顺序',\n        viewApprovalProcess: '查看审批流程',\n        completed: '已完成',\n        cc: '抄送',\n        ccer: '抄送方',\n        signer: '签约方',\n        signSubject: '签约主体',\n        signSubjectTooltip: '发件方填写的签约主体为',\n        user: '用户',\n        IDNumber: '身份证号',\n        state: '状态',\n        time: '时间',\n        notice: '提醒',\n        detail: '详情',\n        RealNameCertificationRequired: '需要实名认证',\n        RealNameCertificationNotRequired: '不需要实名认证',\n        MustHandwrittenSignature: '必须手写签名',\n        handWritingRecognition: '开启手写笔迹识别',\n        privateMessage: '私信',\n        attachment: '资料',\n        rejectReason: '原因',\n        notSigned: '未签署',\n        notViewed: '未查看',\n        viewed: '已查看',\n        signed: '已签署',\n        viewedNotSigned: '已读未签',\n        notApproval: '未审批',\n        remindSucceed: '提醒消息已发送',\n        reviewDetails: '审批详情',\n        close: '关 闭',\n        entInnerOperateDetail: '企业内部操作详情',\n        approve: '同意',\n        disapprove: '驳回',\n        applySeal: '申请用印',\n        applied: '已申请',\n        apply: '申请',\n        toOtherSign: '转给其他人签',\n        handOver: '转交',\n        approvalOpinions: '审批意见',\n        useSeal: '用印',\n        signature: '签名',\n        use: '使用',\n        date: '日期',\n        fill: '填写',\n        times: '次',\n        place: '处',\n        contractDetail: '合同明细',\n        viewMore: '查看更多',\n        collapse: '收起',\n        signLink: '签署链接',\n        saveQRCode: '保存二维码或复制链接，分享给签署方',\n        signQRCode: '签署链接二维码',\n        copy: '复制',\n        copySucc: '复制成功',\n        copyFail: '复制失败',\n        certified: '已认证',\n        unCertified: '未认证',\n        claimed: '已认领',\n    },\n    uploadFile: {\n        thumbnails: '缩略图',\n        isUploading: '正在上传',\n        move: '移动',\n        delete: '删除',\n        replace: '替换',\n        tip: '提示',\n        understand: '我知道了',\n        totalPages: '{page}页',\n        uploadFile: '上传本地文件',\n        matchErr: '服务器开了点小差，请稍后再试',\n        inUploadingDeleteErr: '请在上传完毕后删除',\n        timeOutErr: '请求超时',\n        imgUnqualified: '图片格式不符合要求',\n        imgBiggerThan20M: '上传图片大小不能超过 20MB!',\n        error: '出错啦',\n        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',\n    },\n    contractInfo: {\n        internalNumber: '公司内部编号',\n        contractName: '合同标题',\n        contractNameTooltip: '合同标题请不要包含特殊字符，且长度不超过100字',\n        contractType: '合同类型',\n        toSelect: '请选择',\n        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',\n        signDeadLine: '签约截止时间',\n        signDeadLineTooltip: '如果合同在此日期前未完成签署，则无法继续签署',\n        selectDate: '选择日期时间',\n        contractExpireDate: '合同到期日',\n        expireDateTooltip: '合同内容中的到期时间，便于您后续的合同管理',\n        necessary: '必填',\n        notNecessary: '选填',\n        dateTips: '已为您自动识别合同到期日，请确认',\n        contractTitleErr: '合同名称请不要包含特殊字符',\n        contractTitleLengthErr: '合同名称长度请不要超过100字',\n    },\n    template: {\n        templateList: {\n            linkBoxTip: '关联档案柜ID：',\n        },\n        dynamicTemplateUpdate: {\n            title: '动态模板新功能上线',\n            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',\n            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',\n            connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。',\n        },\n        sendCode: {\n            tip: '当前模板设置不符合发送码生成条件，请检查是否符合以下要求：',\n            fail: {\n                1: '不包含空白文档',\n                2: '签约方只有一个可变方（包含签署和抄送），且可变方必须是第一操作人；签署人必须设有签字盖章处',\n                3: '签约方中固定方账号不为空',\n                4: '不会触发发送前审批',\n                5: '发件方必填字段不为空（含描述字段和合同内容字段）',\n                6: '非模板组合',\n            },\n        },\n        sendCodeGuide: {\n            title: '发送码高级功能说明',\n            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',\n            tip1: {\n                main: '1. 上上签',\n                sub: '',\n                line1: '向上上签申请开通档案+、合同预审、智能预审',\n                line2: '开通后可以到对应的菜单中操作使用',\n            },\n            tip2: {\n                main: '2. 档案柜管理员',\n                sub: '创建档案柜、配置智能预审',\n                line1: '',\n                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',\n            },\n            tip3: {\n                main: '3. 签约方',\n                sub: '扫码填资料、获取合同文件',\n                line1: '',\n                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',\n            },\n            tip4: {\n                main: '4. 档案柜管理员',\n                sub: '',\n                line1: '查看签约的相对方、发送的合同情况',\n                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',\n            },\n        },\n    },\n    style: {\n        signature: {\n            text: {\n                x: '34',\n                fontSize: '18',\n            },\n        },\n    },\n    resetPwd: {\n        title: '安全提示！',\n        notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',\n        oldLabel: '原密码',\n        oldPlaceholder: '请输入原密码',\n        newLabel: '新密码',\n        newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',\n        submit: '确定',\n        errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',\n        oldRule: '原密码不能为空',\n        newRule: '新密码不能为空',\n        success: '修改成功',\n    },\n    personAuthIntercept: {\n        title: '邀请您以',\n        name: '姓名：',\n        id: '身份证号：',\n        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        desMore: '根据发起方要求，您还需要补充',\n        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        tips: '实名认证通过后，可查看并签署合同。',\n        goOn: '是我本人，开始认证',\n        goMore: '去补充认证',\n        descNoSame1: ' 的身份签署合同',\n        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',\n        goHome: '返回合同列表页>>',\n        authInfo: '检测到您当前账号的实名身份为 ',\n        in: '于',\n        finishAuth: '完成实名，用于合规签署合同',\n        ask: '是否继续以当前账号签署？',\n        reAuthBtnText: '是的，我要用本账号重新实名签署',\n        changePhoneText: '不是，联系发件方更改签署手机号',\n        changePhoneTip1: '应发件方要求，请联系',\n        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',\n        confirmReject: '是的，我要驳回实名',\n    },\n    authIntercept: {\n        title: '要求您以：',\n        name: '姓名为：',\n        id: '身份证号为：',\n        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',\n        descNoAuth2: '实名认证通过后，可查看并签署合同。',\n        descNoSame1: '签署合同。',\n        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',\n        tips: '注：身份信息完全一致才能签署合同',\n        goOn: '是我本人，开始认证',\n        goHome: '我知道了',\n        goMore: '去补充认证',\n        authTip: '进行实名认证。',\n        viewAndSign: '完成认证后即可查看和签署合同',\n        tips2: '注：企业名称完全一致才能查看和签署合同。',\n        requestOtherAnth: '请求他人认证',\n        goAuth: '去实名认证',\n        requestSomeoneList: '请求以下人员完成实名认证：',\n        ent: '企业',\n        entName: '企业名称',\n        account: '账号',\n        accountPH: '手机或邮箱',\n        send: '发送',\n        lackEntName: '请填写企业名称',\n        errAccount: '请填写正确的邮箱或手机号',\n        successfulSent: '发送成功',\n    },\n    thirdPartApprovalDialog: {\n        title1: '签署前审批',\n        title2: '审批流程',\n        content1: '审批通过后您才可以签署，请耐心等待。',\n        content2: '需由第三方平台（非上上签平台）审批合同。',\n        cancelBtnText: '查看审批流程',\n        confirmBtnText: '确认',\n        iKnow: '我知道了',\n    },\n    endSignEarlyPrompt: {\n        cancel: '取消',\n        confirm: '确认',\n        signPrompt: '签署提示',\n        signTotalCountTip: '本次签署共包含{count}份合同文件',\n        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',\n        hasSigned: '{count}人已签字',\n        hasNotSigned: '{count}人未签字',\n        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',\n    },\n    commonNomal: {\n        yesterday: '昨天',\n        ssq: '上上签',\n        ssqPlatform: '上上签电子签约云平台',\n        ssqTestPlatform: '（限测试用）BestSign电子签约云平台',\n        pageExpiredTip: '页面已过期，请刷新重试',\n        pswCodeSimpleTip: '密码需包含6-18位数字和大小写字母，请重新设置',\n    },\n    transferAdminDialog: {\n        title: '身份确认',\n        transfer: '转交',\n        confirmAdmin: '我是主管理员',\n        content: '系统主管理员需要负责企业印章的管理、合同的管理和其它人员权限的管理，一般归属于企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人。| 请确认您是否符合以上身份，如果不符合，建议转交给相关人员。',\n    },\n    choseBoxForReceiver: {\n        dataNeedForReceiver: '签约主体需要提交的资料',\n        dataFromDataBox: '签约主体提交的资料需要通过某个档案柜的资料采集来获取',\n        searchTp: '请输入档案柜名称或编号',\n        search: '搜索',\n        boxNotFound: '未找到档案柜',\n        cancel: '取 消',\n        confirm: '确 认',\n    },\n    localCommon: {\n        cancel: '取消',\n        confirm: '确认',\n        toSelect: '请选择',\n        seal: '盖章',\n        signature: '签名',\n        signDate: '签署日期',\n        text: '文本',\n        date: '日期',\n        qrCode: '二维码',\n        number: '数字',\n        dynamicTable: '动态表格',\n        terms: '合同条款',\n        checkBox: '复选框',\n        radioBox: '单选框',\n        image: '图片',\n        confirmSeal: '业务核对章',\n        confirmRemark: '不符合章的备注',\n        optional: '选填',\n        require: '必填',\n        tip: '提示',\n        comboBox: '下拉框',\n    },\n    twoFactor: {\n        signTip: '签署提示',\n        settingTwoFactor: '设置二要素验证器',\n        step1: '1. 安装验证器应用',\n        step1Tip: '二要素身份验证需要您安装一下手机应用程序：',\n        step2: '2.扫描二维码',\n        step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',\n        step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',\n        step3: '3.输入6位验证码',\n        step3Tip: '请输入屏幕上显示的验证码',\n        verifyCode6: '6位验证码',\n        iosAddress: 'iOS下载地址：',\n        androidAddress: 'Android下载地址：',\n        chromeVerify: '谷歌身份验证器',\n        nextBtn: '下一步',\n        confirmSign: '确认签署',\n        dynamicCode: '验证器动态码',\n        password: '加密签署码',\n        pleaseInput: '请输入',\n        twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',\n        passwordTip: '应发件方要求，您需要通过加密签署才可以完成签署。',\n        twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',\n        passwordTip2: '请联系发件方索要加密签署码，输入后即可签署合同。',\n        dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。',\n    },\n    functionSupportDialog: {\n        title: '功能介绍',\n        inputTip: '若您有相关使用需求，欢迎在以下表格中填写您的需求，上上签会在24小时内安排专业人士联系您并提供服务指导。',\n        useSence: '使用场景',\n        useSenceTip: '如：人事/经销商/物流单据...',\n        estimatedOnlineTime: '预计上线时间',\n        requireContent: '请详细描述您的需求场景(选填)',\n        requireContentTip: '请描述场景需求与期望的解决方案',\n        getSupport: '获取专业服务支持',\n        callServiceHotline: '立即拨打客服热线：************',\n        useSenceNotEmpty: '使用场景不能为空',\n        requrieContentNotEmpty: '需求内容不能为空',\n        oneWeek: '一周内',\n        oneMonth: '一月内',\n        other: '其他',\n        submitSuccess: '提交成功',\n        submitTrial: '立即试用',\n        toTrial: '去试用',\n        trialTip: '提交试用申请后，当前功能将立即开通。为了便于我们精准了解您的需求，您可以补充填写更多问题场景，我们将尽快与您联系。',\n        applyTrial: '申请试用',\n        trialSuccTip: '功能已开通，欢迎试用',\n        goBuy: '直接购买',\n        trialTipMap: {\n            title: '试用须知',\n            tip1: '1. 即开即用，有效期为7天；',\n            tip2: '2. 试用期间，功能不收费；',\n            tip3: '3. 每个企业主体，一个功能仅一次试用机会；',\n            tip4: '4. 试用期间可自助购买，使用不间断；',\n            tip5: '5. 如您的试用已结束，可扫码联系上上签专业顾问了解详情：',\n        },\n        contactAdminTip: '如需使用，请联系您的企业管理员{tip}购买开通',\n        trialEndTip: '试用期结束，点击购买',\n        trialRemainDayTip: '试用期剩{day}天，点击购买',\n        trialEnd: '试用功能结束',\n        trialEndMap: {\n            deactivateTip: '{feature}功能已停用，请清除配置，或者续费后，方可继续使用。',\n            feature1: '合同附属资料',\n            remove1: '清除配置方法为：编辑模板-找到配置好的添加合同附属资料，将其删除。',\n            feature2: '手写笔迹识别',\n            remove2: '清除配置方法为：编辑模板-找到配置好的笔迹识别，将其删除。',\n            feature3: '合同装饰：骑缝章+水印',\n            remove3: '清除配置方法为：编辑模板-找到配置好的合同装饰，将其删除。',\n            feature4: '合同发送审批',\n            remove4: '清除配置方法为：企业控制台-停用所有审批流',\n        },\n    },\n    setSignPwdDialog: {\n        tip: '设置完成后，默认优先签约密码，如需修改可登录上上签电子签约平台在「用户中心」或者登录上上签小程序在「账号管理」进行配置调整。',\n        saveAndReturnSign: '保存并返回签署',\n        changeEmailVerify: '切换邮箱验证',\n        changePhoneVerify: '切换手机号验证',\n    },\n    contractCompare: {\n        reUpload: '重新上传',\n        title: '合同比对',\n        packagePurchase: '套餐购买',\n        packagePurchaseTitle: '【{title}功能】套餐购买',\n        payOnce: '特惠限购一次',\n        myPackage: '我的套餐',\n        packageDetail: '套餐详情',\n        per: '次',\n        packageContent: '套餐包含内容为：',\n        num: '{type}次数',\n        limitTime: '有效期',\n        month: '月',\n        payNow: '立即购买',\n        contactUs: '联系我们 | 扫码联系上上签专业顾问了解',\n        compareInfo1: '使用说明：',\n        compareInfo2: '{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；',\n        compareInfo3: '{index}、按上传的合同{per}数统计用量',\n        codePay: '请用扫码支付',\n        aliPay: '支付宝支付',\n        wxPay: '微信支付',\n        payIno: '开通功能 | 购买对象 | 支付金额',\n        finishPay: '完成支付',\n        paySuccess: '购买成功',\n        originFile: '原始合同文件',\n        compareFile: '比对合同文件',\n        documentSelect: '选择文件',\n        comparisonResult: '比对结果',\n        history: '历史记录',\n        currentHistory: '文档记录',\n        noData: '暂无数据',\n        differences: '{num}处差异',\n        historyLog: '{num}条记录',\n        uploadLimit: '将要比对的文件拖拽至此上传 | 目前支持PDF（含PDF扫描件）、Word文件',\n        dragInfo: '释放鼠标完成上传',\n        uploadError: '文件格式不支持',\n        pageNum: '第{page}页',\n        difference: '差异{num}',\n        download: '下载比对结果',\n        comparing: '合同比对中...',\n        tip: '提示',\n        confirm: '确定',\n        toBuy: '去购买',\n        translate: '合同翻译',\n        doCompare: '比对',\n        doTranslate: '翻译',\n        review: '合同审查',\n        doReview: '审查',\n        reviewUploadFile: '将被审查的文件拖拽至此上传',\n        reviewUpload: '将审查依据拖拽至此上传 | 如：《经销商管理办法》《公司采购制度文件》等用于审查合同的公司规章制度性文件 | 目前仅支持PDF、Word文件',\n        reviewOriginFile: '被审查合同',\n        reviewTargetFile: '审查依据',\n        reviewResult: '审查结果',\n        uploadReviewFile: '上传审查依据文件',\n        risk: '风险点{num}',\n        risks: '{num}处风险点',\n        startReview: '开始审查',\n        reviewing: '合同审查中...',\n        noRisk: '审查已完成，未发现风险',\n        allowUpload: '可上传《公司采购管理办法》等可指导合同审查的规章制度条例，或上传公司红线规定、部门业务指导等， | 如：甲方需在合同签订后的5日内完成付款。',\n        notAllowUpload: '不要以模糊语句或者原则性描述作为审查依据， | 如：所有合同条款不得违法相关法律法规要求。',\n        resumeReview: '继续下一份',\n        close: '关闭',\n        extract: '合同抽取',\n        extractTitle: '期望抽取的关键词',\n        selectKeyword: '请从下方“关键词”中勾选',\n        keyword: '关键词',\n        addKeyword: '添加{keyword}',\n        introduce: '{keyword}释义',\n        startExtract: '开始抽取',\n        extractTargetFile: '被抽取合同',\n        extractKeyWord: '抽取关键词',\n        extracting: '合同抽取中',\n        extractResult: '抽取结果',\n        extractUploadFile: '将被抽取的文件拖拽至此上传',\n        needExtractKeyword: '请选择期望抽取的关键词',\n        summary: '合同摘要',\n        keySummary: '关键词内容摘要',\n        deleteKeywordConfirm: '确定要删除该关键词吗？',\n        keywordPosition: '关键词相关位置',\n        riskJudgement: '风险判断',\n        judgeTargetContract: '被判断合同',\n        interpretTargetContract: '被解读合同',\n        startJudge: '开始风险判断',\n        startInterpret: '开始解读',\n        uploadText: '请上传需要进行风险判断的文件',\n        interpretText: '请上传需要进行解读的文件',\n        startTips: '现在我们可以开始判断风险了',\n        interpretTips: '现在我们可以开始进行解读了',\n        infoExtract: '协议提取',\n    },\n    batchImport: {\n        iKnow: '我知道了',\n    },\n    templateCommon: {\n        tip: '提示',\n    },\n    mgapprovenote: {\n        SAQ: '问卷调查',\n        analyze: '分析',\n        annotate: '批注',\n        law: '法规',\n        case: '案例',\n        translate: '翻译',\n        mark: '标记',\n        tips: '以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记',\n        limit: '使用次数已达上限，如有持续使用的需求请填写表单，我们客服人员会联系你。',\n        confirmTxt: '去填写',\n        content: '查找相关段落',\n        experience: '业务经验',\n        datas: '相关数据',\n        terms: '类似条款',\n        original: '来源',\n        export: '导出内容',\n        preview: '合同预览',\n        history: '历史记录',\n    },\n    sealConfirm: {\n        title: '印章确认页',\n        header: '确认印章',\n        signerEnt: '签约企业：',\n        abnormalSeal: '异常印章：',\n        sealNormal: '印章正常',\n        tip1: '请确认印章是否正常可用，如果正常，可点击“印章正常”按钮，后续该企业再使用此印章，系统将不再给您推送异常提醒。',\n        tip2: '如果印章有问题，请及时与签约方沟通更换印章，重新发送合同与其签署，或者进行驳回重签。',\n    },\n    userCentral: userCentral,\n    ...console,\n    ...consts,\n    keyInfoExtract: {\n        operate: '合同信息提取',\n        contractType: '预测的合同类型',\n        tooltips: '选择需要提取的关键信息',\n        predictText: '正在预测合同类型',\n        extractText: '正在提取合同信息',\n        errorMessage: '你的使用额度已经用完，如有进一步需求可以填写表单，我们会联系你，为你补充更多的用量。',\n        result: '提取结果：',\n    },\n    judgeRisk: {\n        title: 'AI律师',\n        deepInference: 'AI法务',\n        showAll: '展开全文',\n        tips: '正在判断合同风险',\n        dialogTitle: '“AI律师”审合同',\n        aiInterpret: 'AI解读',\n    },\n    sealDistribute: {\n        requestSeal: '申请印章分配',\n        company: '公司',\n        applicant: '申请人',\n        accountID: '账号',\n        submissionTime: '时间',\n        status: '状态',\n        agree: '已同意',\n        unAgree: '已驳回',\n        ifAgree: '如果同意，',\n        applyTime: '申请人印章时间时间为：',\n        to: '至',\n        placeHolderTime: '年-月-日',\n        senderCompany: '发件方企业',\n        documentTitle: '合同标题',\n        sealApplicationScope: '印章适用范围',\n        applyforSeal: '申请印章',\n        reject: '驳回',\n        approve: '同意',\n    },\n    sealApproval: {\n        requestSeal: '申请印章分配',\n        sealRight: '印章权限',\n        allEntContract: '所有企业发来的合同',\n        partEntContract: '部分企业发来的合同：',\n        pleaseInputRight: '请输入权限',\n        successTransfer: '交接成功后，',\n        getRight: '将可以获得以上权限或可直接编辑分配新的签署权限。',\n        signAllEntContract: '签署所有企业发送的合同',\n        sign: '签署',\n        sendContract: '发送的合同',\n        sealUseTime: '印章使用时间：',\n        currentStatus: '当前状态：',\n        takeBackSeal: '收回印章',\n        agree: '同意',\n        hasAgree: '已同意',\n        hasReject: '已驳回',\n        hasDone: '已完成',\n        ask: '将',\n        giveYou: '的印章分配给你',\n        hopeAsk: '希望将',\n        hopeGive: '的印章交接于',\n        hopeGiveYou: '的相关印章交接与您',\n        noSettingTime: '无时间设置',\n        approvalSuccess: '审批成功',\n        getSealSuccess: '印章获取成功',\n    },\n    workspace: {\n        create: '已创建',\n        reviewing: '审查中',\n        completed: '已完成',\n        noData: '暂无数据',\n        introduce: '{keyword}释义',\n        termsDetail: '术语详情',\n        extractFormat: '提取格式',\n        optional: '选填',\n        required: '必填',\n        operate: '操作',\n        detail: '详情',\n        delete: '删除',\n        agreement: {\n            uploadError: '只能上传PDF、DOC、DOCX文件!',\n            extractionRequest: '已发起提取请求，请稍后至术语列表中查看提取结果',\n            upload: '文件上传',\n            define: '术语定义',\n            extract: '协议抽取',\n            drag: '将文件拖拽到此处，或',\n            add: '点击添加',\n            format: '支持doc、docx、pdf格式',\n            fileName: '文件名称',\n            status: '状态',\n            completed: '上传完成',\n            failed: '上传失败',\n            size: '大小',\n            terms: '术语',\n            success: '文件抽取完成，共{total}个',\n            ongoing: '文件正在抽取中...共{total}个',\n            tips: '跳过该界面不影响抽取结果',\n            others: '继续上传',\n            result: '跳转至抽取结果下载页面',\n            curProgress: '当前进度: ',\n            refresh: '刷新',\n            details: '已加载{successNum}个，共{length}个',\n            start: '开始抽取',\n            more: '添加文件',\n            skip: '跳过抽取，完成上传。',\n            tiqu: '开始提取',\n            chouqu: '开始抽取',\n        },\n        review: {\n            distribution: '分发审查',\n            Incomplete: '未结束',\n            createReview: '创建审查',\n            manageReview: '审查管理',\n            reviewDetail: '审查详情',\n            reviewId: '审查编号',\n            reviewStatus: '审查状态',\n            reviewName: '审查名称',\n            reviewStartTime: '审查发起时间',\n            reviewCompleteTime: '审查结束时间',\n            reviewDesc: '版本：版本{reviewVersion}  |  审阅编号：{reviewId}',\n            distribute: '发起审查',\n            drag: '拖动待审查的协议到当前区域',\n            content: '待审阅的内容',\n            current: '待分发记录',\n            history: '历史记录',\n            page: '第{page}页：',\n            users: '需要审阅的用户',\n            message: '留言',\n            modify: '修改',\n            placeholder: '多个用户请使用分号\";\"进行分隔',\n            submit: '确定',\n            reupload: '重新上传协议审查',\n            finish: '结束审查',\n            reviewSummary: '审查概要',\n            initiator: '审查发起人',\n            versionSummary: '版本概要',\n            version: '版本',\n            versionOrder: '第{version}版',\n            curReviewStatus: '当前版本审查状态',\n            curReviewVersion: '当前版本',\n            curReviewPopulation: '当前版本审查人数',\n            curReviewStartTime: '当前版本审查发起时间',\n            curReviewInitiator: '当前版本审查发起人',\n            checkComments: '聚合查看修订意见',\n            overview: '审查结果速览',\n            reviewer: '审查人',\n            reviewResult: '审查结果',\n            replyTime: '回复时间',\n            agreement: '审查的协议',\n            files: '相关协议',\n            fileName: '协议名称',\n            numberOfModificationSuggestions: '修改意见数',\n            uploadTime: '上传时间',\n            download: '下载',\n            dispatch: '分发',\n            recent: '最新审查时间：',\n            replyContent: '审查回复内容',\n            advice: '协议修改意见',\n            noIdea: '暂无修改意见',\n            origin: '原文内容：',\n            revised: '修改后内容：',\n            suggestion: '修改意见：',\n            dateMark: '{name} 在 <span style=\"color: #0988EC\">版本{version}</span>写于 {date}',\n            unReviewed: '暂未审查',\n            revisionFiles: '修订协议',\n            staffReplyAggregation: '聚合修订信息',\n            staffReply: '{name}的审查信息',\n            tips: '提示',\n            tipsContent: '结束后此次审查将不再支持分发及后续操作，是否继续',\n            confirm: '确定',\n            cancel: '取消',\n            successMessage: '已结束',\n            PASS: '通过',\n            REJECT: '不通过',\n            uploadErrorMessage: '目前只支持上传docx格式的文件',\n            successInitiated: '已发起审查',\n            autoDistribute: '智能分发',\n            requiredUsers: '需要审查的用户',\n            contentToReview: '审查的内容',\n            termDetails: '术语详情',\n            term: '术语',\n            aiDistribute: 'AI智能分发',\n            noData: '暂无数据',\n            docIconAlt: '文档图标',\n            docxIconAlt: 'DOCX图标',\n            pdfIconAlt: 'PDF图标',\n            requiredUsersError: '请填写需要审查的用户',\n            selectContentError: '请选择审查的内容',\n            initiateReviewSuccess: '已发起审查',\n            syncInitiated: '已发起同步',\n        },\n        contentTracing: {\n            title: '内容溯源',\n            fieldContent: '字段内容',\n            originalResult: '原始结果',\n            contentSource: '内容源自',\n            page: '第',\n        },\n    },\n    hubblePackage: {\n        title: '我的套餐',\n        details: '套餐详情',\n        remainingPages: '剩余总页数',\n        pages: '页',\n        usedPages: '已用',\n        remaining: '可用剩余',\n        total: '共',\n        expiryTime: '到期时间',\n        amount: '数目',\n        unitPrice: '单价',\n        copy: '份',\n        words: '千字',\n    },\n    workspaceIndex: {\n        title: '工作空间',\n        package: '套餐用量',\n        agreement: '协议管理',\n        review: '审查管理',\n        term: '术语管理',\n    },\n    agreement: {\n        title: '协议管理',\n        exportList: '导出协议列表',\n        exportAllChecked: 'Excel(全部字段，勾选协议)',\n        exportCurrentChecked: 'Excel(当前字段，勾选协议)',\n        exportAllMatched: 'Excel(全部字段，符合条件)',\n        exportCurrentMatched: 'Excel(当前字段，符合条件)',\n        add: '添加协议',\n        upload: '上传协议',\n        operation: '操作',\n        download: '下载协议',\n        details: '详情',\n        delete: '删除',\n        relatedTaskStatus: '关联提取任务状态',\n        confirmDelete: '是否确认删除当前协议',\n        prompt: '提示',\n        booleanYes: '是',\n        booleanNo: '否',\n        defaultExportName: 'export.xlsx',\n        taskNotStarted: '抽取任务未开始',\n        taskStarted: '抽取任务已开始 (内容检索中)',\n        contentSearchCompleted: '内容检索已完成 (结果格式化中)',\n        resultFormattingCompleted: '结果格式化已完成 (结果校对中)',\n        resultVerificationCompleted: '结果校对已完成',\n    },\n    filter: {\n        filter: '过滤',\n        refreshExtraction: '重新提取',\n        extractTerms: '术语定义提取',\n        refreshList: '刷新列表',\n        currentCondition: '当前条件下，展示的协议。',\n        when: '当',\n        selectCondition: '请选择条件',\n        enterCondition: '请输入条件',\n        yes: '是',\n        no: '否',\n        addCondition: '添加条件',\n        reset: '重置',\n        confirm: '确定',\n        and: '且',\n        or: '或',\n        equals: '等于',\n        notEquals: '不等于',\n        contains: '包含',\n        notContains: '不包含',\n        greaterThan: '大于',\n        greaterThanOrEquals: '大于等于',\n        lessThan: '小于',\n        lessThanOrEquals: '小于等于',\n        emptyCondition: '过滤条件不能为空',\n    },\n    fieldConfig: {\n        button: '字段配置',\n        header: '期望展示的字段',\n        submit: '完成',\n        cancel: '取消',\n    },\n    agreementDetail: {\n        detail: '协议详情',\n        add: '添加协议',\n        id: '协议编号',\n        file: '协议文件',\n        download: '协议下载',\n        replaceFile: '协议文件替换',\n        uploadFile: '协议文件上传',\n        relatedExtractionStatus: '关联提取任务状态',\n        dataSource: '数据来源',\n        yes: '是',\n        no: '否',\n        select: '请选择',\n        input: '请输入',\n        save: '保存',\n        cancel: '取消',\n        page: '第 {page} 页',\n        addDataSource: '添加数据来源',\n        pageNo: '第',\n        pageSuffix: '页',\n        submit: '提交',\n        inputDataSource: '请输入数据来源内容',\n        pageFormatError: '页码只支持英文逗号隔开的数字或者纯数字',\n        confirmDelete: '是否确认删除当前数据来源',\n        tips: '提示',\n        uploadSuccess: '上传成功',\n    },\n    termManagement: {\n        title: '术语管理',\n        batchDelete: '批量删除',\n        import: '术语导入',\n        export: '术语导出',\n        add: '+ 添加术语',\n        name: '术语名称',\n        definition: '术语释义',\n        formatRequirement: '提取格式要求',\n        dataFormat: '数据格式',\n        operation: '操作',\n        edit: '修改',\n        delete: '删除',\n        detail: '术语详情',\n        addTitle: '添加术语',\n        namePlaceholder: '填写你的专业术语',\n        definitionPlaceholder: '填写术语的释义',\n        formatRequirementPlaceholder: '填写术语的提取格式要求',\n        dataFormatPlaceholder: '期望提取的术语格式要求',\n        cancel: '取消',\n        confirmEdit: '确认修改',\n        importTitle: '术语导入',\n        uploadTemplate: '上传术语模板文件',\n        downloadTemplate: '下载术语模板文件',\n        extractType: {\n            text: '文本',\n            longText: '长文本',\n            date: '日期',\n            number: '数值',\n            boolean: '是/否',\n        },\n        importSuccess: '导入成功',\n        deleteConfirm: '是否确认删除当前术语',\n        prompt: '提示',\n        nameEmptyError: '术语名称不能为空',\n    },\n    agent: {\n        extractTitle: '信息提取',\n        riskTitle: 'AI律师',\n        feedback: '问卷反馈',\n        toMini: '去小程序查看',\n        otherContract: '让我看看其他合同的隐藏风险?',\n        others: '其他',\n        submit: '发送',\n        autoExtract: '自动进行下一步提取直到提取结束',\n        autoRisk: '自动进行下一步分析直到分析结束',\n        aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',\n        chooseRisk: '请选择需要进行分析的文件',\n        chooseExtract: '请选择需要进行提取的文件',\n        analyzing: '内容分析中',\n        advice: '修改建议生成中',\n        options: '选项生成中',\n        inputTips: '请输入确切内容',\n        chargeTip: '余额不足，请充值',\n        original: '原文',\n        revision: '修改建议',\n        diff: '对比',\n        locate: '获取原文定位中',\n        custom: '请输入自定义审查规则',\n        content: '原文位置',\n        satisfy: '对分析结果满意，继续下一项分析',\n        dissatisfy: '对分析结果不满意，重新进行分析',\n        selectFunc: '请选择你期望使用的功能。',\n        deepInference: 'AI法务',\n        deepThinking: '深度思考中',\n        deepThoughtCompleted: '已深度思考',\n        reJudge: '重新判断',\n        confirm: '确认',\n        tipsContent: '重新判断将会扣除份数，是否继续?',\n        useLawyer: '使用AI律师',\n        interpretFinish: 'AI解读已完成',\n        exportPDF: '导出PDF报告',\n        defaultExportName: 'export.pdf',\n        exporting: '正在导出报告，请稍后...',\n    },\n    authorize: {\n        title: '使用须知',\n        content: '开启智能合同，AI帮你分析，让你的工作更轻松高效！同意以下内容，即可体验。',\n        cancel: '不用了',\n        confirm: '同意并使用',\n        contract: '查看《哈勃产品使用须知》',\n    },\n    hubbleEntry: {\n        smartAdvisor: '智能签约顾问',\n        tooltips: '您暂未开通此功能，可联系上上签电子签约顾问购买开通。',\n        confirm: '好的',\n    },\n    lang: 'zh',\n};\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,sCAAsC;AAE7D,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,cAAc,MAAM,8CAA8C;AAEzE,eAAe;EACX,GAAGV,KAAK;EACR,GAAGC,KAAK;EACR,GAAGC,UAAU;EACb,GAAGC,OAAO;EACV,GAAGO,cAAc;EACjBC,QAAQ,EAAE;IACNC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,kBAAkB;IAC9BC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACd,CAAC;EACDC,YAAY,EAAE;IACVC,MAAM,EAAE,yBAAyB;IACjCC,cAAc,EAAE,4BAA4B;IAC5CC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,eAAe;IACxBC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,SAAS;IACrBC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,QAAQ;IACrBC,UAAU,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACHC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,QAAQ;IACrBC,WAAW,EAAE,OAAO;IACpBC,cAAc,EAAE,SAAS;IACzBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,gBAAgB;IAC9BC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,UAAU;IACvBC,SAAS,EAAE,MAAM;IACjBX,KAAK,EAAE,IAAI;IACXY,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE,UAAU;IAC9BC,mBAAmB,EAAE,SAAS;IAC9BC,UAAU,EAAE,WAAW;IACvBC,qBAAqB,EAAE,UAAU;IACjCC,aAAa,EAAE,OAAO;IACtBC,UAAU,EAAE,MAAM;IAClBC,EAAE,EAAE,GAAG;IACPC,kBAAkB,EAAE,iBAAiB;IACrCC,mBAAmB,EAAE,cAAc;IACnCC,aAAa,EAAE,eAAe;IAC9BC,MAAM,EAAE,WAAW;IACnBC,gBAAgB,EAAE,OAAO;IACzBC,cAAc,EAAE,SAAS;IACzBC,oBAAoB,EAAE,WAAW;IACjCC,WAAW,EAAE,WAAW;IACxBC,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,QAAQ;IAEvBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,qBAAqB;IAC7BC,QAAQ,EAAE,SAAS;IACnBC,iBAAiB,EAAE,SAAS;IAC5BC,GAAG,EAAE,GAAG;IACRC,2BAA2B,EAAE,UAAU;IACvCC,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,QAAQ;IACjBC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,0CAA0C;IACrDC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,2BAA2B;IACvCC,QAAQ,EAAE,KAAK;IACfC,mBAAmB,EAAE,WAAW;IAChC;IACAC,aAAa,EAAE,iBAAiB;IAChCC,mBAAmB,EAAE,mBAAmB;IACxCC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,aAAa;IAC3BC,qBAAqB,EAAE,cAAc;IACrCC,QAAQ,EAAE,UAAU;IACpBC,mBAAmB,EAAE,+BAA+B;IACpDC,sBAAsB,EAAE,SAAS;IACjC;IACAC,oBAAoB,EAAE,OAAO;IAC7BC,gBAAgB,EAAE,SAAS;IAC3BC,mBAAmB,EAAE,SAAS;IAC9BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,QAAQ;IACrBC,iBAAiB,EAAE;EACvB,CAAC;EACD,GAAG/F,IAAI;EACPgG,SAAS,EAAE;IACP3F,KAAK,EAAE,MAAM;IACb4F,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,SAAS;IACzBC,mBAAmB,EAAE,SAAS;IAC9BC,cAAc,EAAE,SAAS;IACzBC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,2CAA2C;IACpDC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,IAAI;IACTC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE,mBAAmB;IAC/BC,OAAO,EAAE,IAAI;IACblG,MAAM,EAAE,KAAK;IACbmG,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,wBAAwB;IACxCC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,cAAc;IAC3BC,EAAE,EAAE,IAAI;IACRC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,qBAAqB;IACnCC,iBAAiB,EAAE,sCAAsC;IACzDC,GAAG,EAAE;MACDC,aAAa,EAAE,yBAAyB;MACxCC,aAAa,EAAE,mBAAmB;MAClCC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,MAAM,EAAE;IAEJ9G,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACfC,WAAW,EAAE,MAAM;IACnBE,SAAS,EAAE,MAAM;IACjB2G,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,kBAAkB;IAC/BC,MAAM,EAAE,YAAY;IACpBnH,YAAY,EAAE,MAAM;IACpBM,OAAO,EAAE,eAAe;IACxBF,IAAI,EAAE,MAAM;IACZgC,aAAa,EAAE,eAAe;IAC9BE,gBAAgB,EAAE,OAAO;IACzB8E,WAAW,EAAE,SAAS;IACtBC,kBAAkB,EAAE,YAAY;IAChCC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,WAAW;IACjCC,iBAAiB,EAAE,UAAU;IAC7BC,UAAU,EAAE,SAAS;IACrBC,iBAAiB,EAAE,YAAY;IAC/BC,kBAAkB,EAAE,UAAU;IAC9BC,sBAAsB,EAAE;EAE5B,CAAC;EACD5I,OAAO,EAAE;IACL,GAAGA,OAAO;IACV6I,gBAAgB,EAAE,QAAQ;IAC1BC,WAAW,EAAE,QAAQ;IACrBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,MAAM;IACftC,MAAM,EAAE,MAAM;IACduC,WAAW,EAAE,8BAA8B;IAC3CC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,IAAI;IACV/H,IAAI,EAAE,IAAI;IACVgI,OAAO,EAAE,MAAM;IACfC,sBAAsB,EAAE,wBAAwB;IAChDC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,OAAO;IACnBC,eAAe,EAAE,SAAS;IAC1BC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,QAAQ;IACxBC,sBAAsB,EAAE,QAAQ;IAChCC,gBAAgB,EAAE,YAAY;IAC9BC,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE,uBAAuB;IAC7BC,eAAe,EAAE,gBAAgB;IACjCvJ,QAAQ,EAAE,IAAI;IACdwJ,aAAa,EAAE,oBAAoB;IACnCC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACVC,IAAI,EAAE,IAAI;IACVC,cAAc,EAAE;EACpB,CAAC;EACD3K,IAAI,EAAE;IACF,GAAGA,IAAI;IACPA,IAAI,EAAE,IAAI;IACV4K,gBAAgB,EAAE,MAAM;IACxBC,kBAAkB,EAAE,MAAM;IAC1BC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE,OAAO;IAC1BC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,kBAAkB;IAChCC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,mBAAmB;IAC9BC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,eAAe;IAC5BC,mBAAmB,EAAE,wBAAwB;IAC7CC,IAAI,EAAE,SAAS;IACfC,gBAAgB,EAAE,MAAM;IACxBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,YAAY;IAClBC,GAAG,EAAE,OAAO;IACZC,eAAe,EAAE,WAAW;IAC5BC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,oCAAoC;IAC1CC,QAAQ,EAAE;MACN,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,MAAM;MACT,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,QAAQ;IACzBC,SAAS,EAAE;MACP,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE;IACP,CAAC;IACDC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE,QAAQ;IACtBC,mBAAmB,EAAE;EACzB,CAAC;EACDC,SAAS,EAAE;IACPC,gBAAgB,EAAE,eAAe;IACjCC,aAAa,EAAE,QAAQ;IACvBC,QAAQ,EAAE,MAAM;IAChBC,yBAAyB,EAAE,cAAc;IACzCC,iBAAiB,EAAE,OAAO;IAC1BC,kBAAkB,EAAE,UAAU;IAC9BC,YAAY,EAAE;MACVhD,IAAI,EAAE,IAAI;MACViD,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACd,CAAC;IACDC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,MAAM;IACtBC,gBAAgB,EAAE,MAAM;IACxBC,aAAa,EAAE,OAAO;IACtBC,iBAAiB,EAAE,MAAM;IACzBC,wBAAwB,EAAE,UAAU;IACpCC,WAAW,EAAE;MACTC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE;IACZ,CAAC;IACDC,YAAY,EAAE,OAAO;IACrBC,cAAc,EAAE,kBAAkB;IAClCC,yBAAyB,EAAE,SAAS;IACpCC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,KAAK;IACpBC,0BAA0B,EAAE,qBAAqB;IACjDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,KAAK;IACfC,0BAA0B,EAAE,WAAW;IACvCC,kBAAkB,EAAE,QAAQ;IAC5BC,IAAI,EAAE,GAAG;IACTC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,IAAI;IACZC,kBAAkB,EAAE,QAAQ;IAC5BC,eAAe,EAAE;MACbC,UAAU,EAAE,MAAM;MAClBC,4BAA4B,EAAE,QAAQ;MACtCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACd,CAAC;IACDC,yBAAyB,EAAE;MACvBjP,KAAK,EAAE,6DAA6D;MACpEkP,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,MAAM;MACf5O,MAAM,EAAE;IACZ,CAAC;IACD6O,WAAW,EAAE;MACTpP,KAAK,EAAE,2BAA2B;MAClCkP,IAAI,EAAE,IAAI;MACVxI,OAAO,EAAE,IAAI;MACbnG,MAAM,EAAE;IACZ,CAAC;IACD8O,2BAA2B,EAAE,gBAAgB;IAC7CC,SAAS,EAAE,MAAM;IACjBC,sBAAsB,EAAE,cAAc;IACtCC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,OAAO;IACtBC,OAAO,EAAE,IAAI;IACbC,kBAAkB,EAAE,QAAQ;IAC5BC,aAAa,EAAE,aAAa;IAC5BC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,QAAQ;IAC1BC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,MAAM;IACpBC,SAAS,EAAE,MAAM;IACjBpF,WAAW,EAAE,MAAM;IACnBwC,MAAM,EAAE,KAAK;IACb6C,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,QAAQ;IACtBC,kBAAkB,EAAE,QAAQ;IAC5B;IACAC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,KAAK;IACbnG,YAAY,EAAE,MAAM;IACpBoG,MAAM,EAAE,IAAI;IACZtR,IAAI,EAAE,IAAI;IACVuR,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE,MAAM;IACxBC,cAAc,EAAE,QAAQ;IACxBC,KAAK,EAAE,IAAI;IACXC,mBAAmB,EAAE,wBAAwB;IAC7CC,UAAU,EAAE,OAAO;IACnBC,cAAc,EAAE,OAAO;IACvBC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,MAAM;IACnBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,oBAAoB,EAAE,QAAQ;IAC9BC,eAAe,EAAE,QAAQ;IACzBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZjF,QAAQ,EAAE,IAAI;IACdkF,cAAc,EAAE,QAAQ;IACxBC,mBAAmB,EAAE,QAAQ;IAC7BC,SAAS,EAAE,KAAK;IAChBC,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,KAAK;IACX7E,MAAM,EAAE,KAAK;IACb8E,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE,aAAa;IACjC3F,IAAI,EAAE,IAAI;IACV4F,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,IAAI;IACXnH,IAAI,EAAE,IAAI;IACVoH,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,6BAA6B,EAAE,QAAQ;IACvCC,gCAAgC,EAAE,SAAS;IAC3CC,wBAAwB,EAAE,QAAQ;IAClCC,sBAAsB,EAAE,UAAU;IAClCC,cAAc,EAAE,IAAI;IACpBC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,SAAS;IACxBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,KAAK;IACZC,qBAAqB,EAAE,UAAU;IACjCC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,MAAM;IACxBC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfrO,GAAG,EAAE,IAAI;IACT0D,IAAI,EAAE,IAAI;IACV4K,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,GAAG;IACVC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,mBAAmB;IAC/BC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,MAAM;IAChB9J,SAAS,EAAE,KAAK;IAChB+J,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACRC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,MAAM;IACnBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,GAAG,EAAE,IAAI;IACTC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,SAAS;IACrBR,UAAU,EAAE,QAAQ;IACpBS,QAAQ,EAAE,gBAAgB;IAC1BC,oBAAoB,EAAE,WAAW;IACjCC,UAAU,EAAE,MAAM;IAClBC,cAAc,EAAE,WAAW;IAC3BC,gBAAgB,EAAE,kBAAkB;IACpCC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;EACd,CAAC;EACDlG,YAAY,EAAE;IACVmG,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,0BAA0B;IAC/C7L,YAAY,EAAE,MAAM;IACpB8L,QAAQ,EAAE,KAAK;IACfC,eAAe,EAAE,qBAAqB;IACtCC,YAAY,EAAE,QAAQ;IACtBC,mBAAmB,EAAE,wBAAwB;IAC7CC,UAAU,EAAE,QAAQ;IACpBnG,kBAAkB,EAAE,OAAO;IAC3BoG,iBAAiB,EAAE,uBAAuB;IAC1CC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,kBAAkB;IAC5BC,gBAAgB,EAAE,eAAe;IACjCC,sBAAsB,EAAE;EAC5B,CAAC;EACDC,QAAQ,EAAE;IACNC,YAAY,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACDC,qBAAqB,EAAE;MACnBzX,KAAK,EAAE,WAAW;MAClB0X,cAAc,EAAE,2BAA2B;MAC3CC,SAAS,EAAE,gGAAgG;MAC3GC,SAAS,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACN/B,GAAG,EAAE,+BAA+B;MACpCgC,IAAI,EAAE;QACF,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE,+CAA+C;QAClD,CAAC,EAAE,cAAc;QACjB,CAAC,EAAE,WAAW;QACd,CAAC,EAAE,0BAA0B;QAC7B,CAAC,EAAE;MACP;IACJ,CAAC;IACDC,aAAa,EAAE;MACX/X,KAAK,EAAE,WAAW;MAClBoK,IAAI,EAAE,wMAAwM;MAC9M4N,IAAI,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE;MACX,CAAC;MACDC,IAAI,EAAE;QACFJ,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDE,IAAI,EAAE;QACFL,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDG,IAAI,EAAE;QACFN,IAAI,EAAE,WAAW;QACjBC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDI,KAAK,EAAE;IACH/D,SAAS,EAAE;MACPgE,IAAI,EAAE;QACFC,CAAC,EAAE,IAAI;QACPC,QAAQ,EAAE;MACd;IACJ;EACJ,CAAC;EACDC,QAAQ,EAAE;IACN5Y,KAAK,EAAE,OAAO;IACd6S,MAAM,EAAE,gCAAgC;IACxCgG,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,sBAAsB;IACtCC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,0BAA0B;IACpCC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBhS,OAAO,EAAE;EACb,CAAC;EACDiS,mBAAmB,EAAE;IACjBrZ,KAAK,EAAE,MAAM;IACbsZ,IAAI,EAAE,KAAK;IACXC,EAAE,EAAE,OAAO;IACXC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,qCAAqC;IACjDC,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,uBAAuB;IACpCC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,iBAAiB;IAC3BC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,eAAe;IAC3BC,GAAG,EAAE,cAAc;IACnBC,aAAa,EAAE,iBAAiB;IAChCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,YAAY;IAC7BC,eAAe,EAAE,0BAA0B;IAC3CC,aAAa,EAAE;EACnB,CAAC;EACDC,aAAa,EAAE;IACX5a,KAAK,EAAE,OAAO;IACdsZ,IAAI,EAAE,MAAM;IACZC,EAAE,EAAE,QAAQ;IACZI,WAAW,EAAE,0BAA0B;IACvCC,WAAW,EAAE,mBAAmB;IAChCI,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,qCAAqC;IAClDJ,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,WAAW;IACjBI,MAAM,EAAE,MAAM;IACdH,MAAM,EAAE,OAAO;IACfc,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE,sBAAsB;IAC7BC,gBAAgB,EAAE,QAAQ;IAC1BC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,eAAe;IACnCC,GAAG,EAAE,IAAI;IACT7R,OAAO,EAAE,MAAM;IACfkC,OAAO,EAAE,IAAI;IACb4P,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,cAAc;IAC1BC,cAAc,EAAE;EACpB,CAAC;EACDC,uBAAuB,EAAE;IACrBC,MAAM,EAAE,OAAO;IACf7U,MAAM,EAAE,MAAM;IACd5G,QAAQ,EAAE,oBAAoB;IAC9BC,QAAQ,EAAE,sBAAsB;IAChCyb,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE;EACX,CAAC;EACDC,kBAAkB,EAAE;IAChBvb,MAAM,EAAE,IAAI;IACZmG,OAAO,EAAE,IAAI;IACbqV,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,qBAAqB;IACxCC,YAAY,EAAE,mCAAmC;IACjDC,SAAS,EAAE,aAAa;IACxBC,YAAY,EAAE,aAAa;IAC3BC,aAAa,EAAE;EACnB,CAAC;EACDC,WAAW,EAAE;IACTC,SAAS,EAAE,IAAI;IACfjb,GAAG,EAAE,KAAK;IACVkb,WAAW,EAAE,YAAY;IACzBC,eAAe,EAAE,uBAAuB;IACxCC,cAAc,EAAE,aAAa;IAC7BC,gBAAgB,EAAE;EACtB,CAAC;EACDC,mBAAmB,EAAE;IACjB3c,KAAK,EAAE,MAAM;IACb4c,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,QAAQ;IACtB9N,OAAO,EAAE;EACb,CAAC;EACD+N,mBAAmB,EAAE;IACjBC,mBAAmB,EAAE,aAAa;IAClCC,eAAe,EAAE,4BAA4B;IAC7CC,QAAQ,EAAE,aAAa;IACvBC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,QAAQ;IACrB5c,MAAM,EAAE,KAAK;IACbmG,OAAO,EAAE;EACb,CAAC;EACD0W,WAAW,EAAE;IACT7c,MAAM,EAAE,IAAI;IACZmG,OAAO,EAAE,IAAI;IACbiQ,QAAQ,EAAE,KAAK;IACf0G,IAAI,EAAE,IAAI;IACV5I,SAAS,EAAE,IAAI;IACf6I,QAAQ,EAAE,MAAM;IAChB7E,IAAI,EAAE,IAAI;IACV3O,IAAI,EAAE,IAAI;IACVyT,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,MAAM;IACpBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbnI,GAAG,EAAE,IAAI;IACToI,QAAQ,EAAE;EACd,CAAC;EACDC,SAAS,EAAE;IACPC,OAAO,EAAE,MAAM;IACfC,gBAAgB,EAAE,UAAU;IAC5BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,uBAAuB;IACjCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,oDAAoD;IAC/DC,SAAS,EAAE,uBAAuB;IAClCC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,UAAU;IACtBC,cAAc,EAAE,cAAc;IAC9BC,YAAY,EAAE,SAAS;IACvBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,OAAO;IACjBC,WAAW,EAAE,KAAK;IAClBC,YAAY,EAAE,2BAA2B;IACzCC,WAAW,EAAE,0BAA0B;IACvCC,uBAAuB,EAAE,iCAAiC;IAC1DC,YAAY,EAAE,0BAA0B;IACxCC,iBAAiB,EAAE;EACvB,CAAC;EACDC,qBAAqB,EAAE;IACnB3f,KAAK,EAAE,MAAM;IACb4f,QAAQ,EAAE,sDAAsD;IAChEC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,kBAAkB;IAC/BC,mBAAmB,EAAE,QAAQ;IAC7BC,cAAc,EAAE,iBAAiB;IACjCC,iBAAiB,EAAE,iBAAiB;IACpCC,UAAU,EAAE,UAAU;IACtBC,kBAAkB,EAAE,uBAAuB;IAC3CC,gBAAgB,EAAE,UAAU;IAC5BC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,IAAI;IACXC,aAAa,EAAE,MAAM;IACrBC,WAAW,EAAE,MAAM;IACnBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,2DAA2D;IACrEC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,YAAY;IAC1BC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE;MACThhB,KAAK,EAAE,MAAM;MACbgY,IAAI,EAAE,iBAAiB;MACvBK,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,wBAAwB;MAC9BC,IAAI,EAAE,qBAAqB;MAC3B0I,IAAI,EAAE;IACV,CAAC;IACDC,eAAe,EAAE,0BAA0B;IAC3CC,WAAW,EAAE,YAAY;IACzBC,iBAAiB,EAAE,iBAAiB;IACpCC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE;MACTC,aAAa,EAAE,oCAAoC;MACnDC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,mCAAmC;MAC5CC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,+BAA+B;MACxCC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,+BAA+B;MACxCC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;IACb;EACJ,CAAC;EACDC,gBAAgB,EAAE;IACdlM,GAAG,EAAE,gEAAgE;IACrEmM,iBAAiB,EAAE,SAAS;IAC5BC,iBAAiB,EAAE,QAAQ;IAC3BC,iBAAiB,EAAE;EACvB,CAAC;EACDC,eAAe,EAAE;IACbC,QAAQ,EAAE,MAAM;IAChBriB,KAAK,EAAE,MAAM;IACbsiB,eAAe,EAAE,MAAM;IACvBC,oBAAoB,EAAE,iBAAiB;IACvCC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,MAAM;IACrBC,GAAG,EAAE,GAAG;IACRC,cAAc,EAAE,UAAU;IAC1BhX,GAAG,EAAE,UAAU;IACfiX,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,sBAAsB;IACjCC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,mEAAmE;IACjFC,YAAY,EAAE,0BAA0B;IACxCC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,oBAAoB;IAC5BC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,QAAQ;IACrBC,cAAc,EAAE,MAAM;IACtBC,gBAAgB,EAAE,MAAM;IACxBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,UAAU;IACtBrb,WAAW,EAAE,yCAAyC;IACtDsb,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,SAAS;IACrBpX,QAAQ,EAAE,QAAQ;IAClBqX,SAAS,EAAE,UAAU;IACrBzO,GAAG,EAAE,IAAI;IACTpP,OAAO,EAAE,IAAI;IACb8d,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,eAAe;IACjCC,YAAY,EAAE,wEAAwE;IACtFC,gBAAgB,EAAE,OAAO;IACzBC,gBAAgB,EAAE,MAAM;IACxBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,UAAU;IAC5BC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAE,aAAa;IACrBC,WAAW,EAAE,wEAAwE;IACrFC,cAAc,EAAE,+CAA+C;IAC/DC,YAAY,EAAE,OAAO;IACrB7R,KAAK,EAAE,IAAI;IACX8R,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,UAAU;IACxBC,aAAa,EAAE,cAAc;IAC7BC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,aAAa;IACxBC,YAAY,EAAE,MAAM;IACpBC,iBAAiB,EAAE,OAAO;IAC1BC,cAAc,EAAE,OAAO;IACvBC,UAAU,EAAE,OAAO;IACnBC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,eAAe;IAClCC,kBAAkB,EAAE,aAAa;IACjCC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,SAAS;IACrBC,oBAAoB,EAAE,aAAa;IACnCC,eAAe,EAAE,SAAS;IAC1BC,aAAa,EAAE,MAAM;IACrBC,mBAAmB,EAAE,OAAO;IAC5BC,uBAAuB,EAAE,OAAO;IAChCC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,MAAM;IACtBC,UAAU,EAAE,gBAAgB;IAC5BC,aAAa,EAAE,cAAc;IAC7BC,SAAS,EAAE,eAAe;IAC1BC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACT1L,KAAK,EAAE;EACX,CAAC;EACD2L,cAAc,EAAE;IACZ1R,GAAG,EAAE;EACT,CAAC;EACD2R,aAAa,EAAE;IACXC,GAAG,EAAE,MAAM;IACXC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,IAAI;IACVrD,SAAS,EAAE,IAAI;IACf1Y,IAAI,EAAE,IAAI;IACV8N,IAAI,EAAE,qCAAqC;IAC3CkO,KAAK,EAAE,qCAAqC;IAC5CC,UAAU,EAAE,KAAK;IACjBjZ,OAAO,EAAE,QAAQ;IACjBkZ,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbxK,KAAK,EAAE,MAAM;IACbyK,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,MAAM;IACfvE,OAAO,EAAE;EACb,CAAC;EACDwE,WAAW,EAAE;IACTtoB,KAAK,EAAE,OAAO;IACduoB,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBC,YAAY,EAAE,OAAO;IACrBC,UAAU,EAAE,MAAM;IAClB1Q,IAAI,EAAE,yDAAyD;IAC/DK,IAAI,EAAE;EACV,CAAC;EACD5Y,WAAW,EAAEA,WAAW;EACxB,GAAGD,OAAO;EACV,GAAGK,MAAM;EACT8oB,cAAc,EAAE;IACZ3b,OAAO,EAAE,QAAQ;IACjBnC,YAAY,EAAE,SAAS;IACvB+d,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,UAAU;IACvBC,WAAW,EAAE,UAAU;IACvBC,YAAY,EAAE,4CAA4C;IAC1DC,MAAM,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACPjpB,KAAK,EAAE,MAAM;IACbkpB,aAAa,EAAE,MAAM;IACrBC,OAAO,EAAE,MAAM;IACftP,IAAI,EAAE,UAAU;IAChBuP,WAAW,EAAE,WAAW;IACxBC,WAAW,EAAE;EACjB,CAAC;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,QAAQ;IACrBroB,OAAO,EAAE,IAAI;IACbsoB,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,aAAa;IACxBC,EAAE,EAAE,GAAG;IACPC,eAAe,EAAE,OAAO;IACxBC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE,MAAM;IACrBC,oBAAoB,EAAE,QAAQ;IAC9BC,YAAY,EAAE,MAAM;IACpBnY,MAAM,EAAE,IAAI;IACZ8B,OAAO,EAAE;EACb,CAAC;EACDsW,YAAY,EAAE;IACVf,WAAW,EAAE,QAAQ;IACrBgB,SAAS,EAAE,MAAM;IACjBC,cAAc,EAAE,WAAW;IAC3BC,eAAe,EAAE,YAAY;IAC7BC,gBAAgB,EAAE,OAAO;IACzBC,eAAe,EAAE,QAAQ;IACzBC,QAAQ,EAAE,0BAA0B;IACpCC,kBAAkB,EAAE,aAAa;IACjClrB,IAAI,EAAE,IAAI;IACVmL,YAAY,EAAE,OAAO;IACrBggB,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,MAAM;IACpBpB,KAAK,EAAE,IAAI;IACXqB,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACd7Q,GAAG,EAAE,GAAG;IACR8Q,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE,MAAM;IACvBC,cAAc,EAAE;EACpB,CAAC;EACDC,SAAS,EAAE;IACPC,MAAM,EAAE,KAAK;IACbrG,SAAS,EAAE,KAAK;IAChBjT,SAAS,EAAE,KAAK;IAChB0R,MAAM,EAAE,MAAM;IACdiC,SAAS,EAAE,aAAa;IACxB4F,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE,MAAM;IACrB9N,QAAQ,EAAE,IAAI;IACd+N,QAAQ,EAAE,IAAI;IACd/e,OAAO,EAAE,IAAI;IACb8F,MAAM,EAAE,IAAI;IACZ8C,MAAM,EAAE,IAAI;IACZoW,SAAS,EAAE;MACP5H,WAAW,EAAE,qBAAqB;MAClC6H,iBAAiB,EAAE,yBAAyB;MAC5C3lB,MAAM,EAAE,MAAM;MACd4lB,MAAM,EAAE,MAAM;MACdtG,OAAO,EAAE,MAAM;MACfuG,IAAI,EAAE,YAAY;MAClBC,GAAG,EAAE,MAAM;MACXC,MAAM,EAAE,kBAAkB;MAC1BC,QAAQ,EAAE,MAAM;MAChB3C,MAAM,EAAE,IAAI;MACZrX,SAAS,EAAE,MAAM;MACjBia,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,IAAI;MACV9O,KAAK,EAAE,IAAI;MACXtW,OAAO,EAAE,kBAAkB;MAC3BqlB,OAAO,EAAE,qBAAqB;MAC9B5S,IAAI,EAAE,cAAc;MACpB6S,MAAM,EAAE,MAAM;MACd1D,MAAM,EAAE,aAAa;MACrB2D,WAAW,EAAE,QAAQ;MACrBC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,MAAM;MACbxhB,IAAI,EAAE,MAAM;MACZyhB,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE;IACZ,CAAC;IACDrI,MAAM,EAAE;MACJsI,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,MAAM;MAClBC,eAAe,EAAE,QAAQ;MACzBC,kBAAkB,EAAE,QAAQ;MAC5BC,UAAU,EAAE,0CAA0C;MACtDC,UAAU,EAAE,MAAM;MAClB1B,IAAI,EAAE,eAAe;MACrBpd,OAAO,EAAE,QAAQ;MACjB+e,OAAO,EAAE,OAAO;MAChBhK,OAAO,EAAE,MAAM;MACfiK,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,IAAI;MACbvf,MAAM,EAAE,IAAI;MACZwf,WAAW,EAAE,kBAAkB;MAC/BjV,MAAM,EAAE,IAAI;MACZkV,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,MAAM;MACdC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,OAAO;MAClBC,cAAc,EAAE,MAAM;MACtBC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE,aAAa;MAC3BC,eAAe,EAAE,UAAU;MAC3BC,gBAAgB,EAAE,MAAM;MACxBC,mBAAmB,EAAE,UAAU;MAC/BC,kBAAkB,EAAE,YAAY;MAChCC,kBAAkB,EAAE,WAAW;MAC/BC,aAAa,EAAE,UAAU;MACzBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACf/J,YAAY,EAAE,MAAM;MACpBgK,SAAS,EAAE,MAAM;MACjBlD,SAAS,EAAE,OAAO;MAClBmD,KAAK,EAAE,MAAM;MACb7C,QAAQ,EAAE,MAAM;MAChB8C,+BAA+B,EAAE,OAAO;MACxCC,UAAU,EAAE,MAAM;MAClBniB,QAAQ,EAAE,IAAI;MACdoiB,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE,QAAQ;MACtB5nB,MAAM,EAAE,QAAQ;MAChB6nB,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,QAAQ;MACjBC,UAAU,EAAE,OAAO;MACnBC,QAAQ,EAAE,mEAAmE;MAC7EC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE,MAAM;MACrBC,qBAAqB,EAAE,QAAQ;MAC/BC,UAAU,EAAE,aAAa;MACzBpW,IAAI,EAAE,IAAI;MACVqW,WAAW,EAAE,0BAA0B;MACvCxpB,OAAO,EAAE,IAAI;MACbnG,MAAM,EAAE,IAAI;MACZ4vB,cAAc,EAAE,KAAK;MACrBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,KAAK;MACbC,kBAAkB,EAAE,kBAAkB;MACtCC,gBAAgB,EAAE,OAAO;MACzBC,cAAc,EAAE,MAAM;MACtBC,aAAa,EAAE,SAAS;MACxBC,eAAe,EAAE,OAAO;MACxBC,WAAW,EAAE,MAAM;MACnBC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,QAAQ;MACtB7M,MAAM,EAAE,MAAM;MACd8M,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,OAAO;MACnBC,kBAAkB,EAAE,YAAY;MAChCC,kBAAkB,EAAE,UAAU;MAC9BC,qBAAqB,EAAE,OAAO;MAC9BC,aAAa,EAAE;IACnB,CAAC;IACDC,cAAc,EAAE;MACZrxB,KAAK,EAAE,MAAM;MACbsxB,YAAY,EAAE,MAAM;MACpBC,cAAc,EAAE,MAAM;MACtBC,aAAa,EAAE,MAAM;MACrBzD,IAAI,EAAE;IACV;EACJ,CAAC;EACD0D,aAAa,EAAE;IACXzxB,KAAK,EAAE,MAAM;IACb6sB,OAAO,EAAE,MAAM;IACf6E,cAAc,EAAE,OAAO;IACvBC,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACf9c,IAAI,EAAE,GAAG;IACT+c,KAAK,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;IACZnyB,KAAK,EAAE,MAAM;IACboyB,OAAO,EAAE,MAAM;IACfpG,SAAS,EAAE,MAAM;IACjBpH,MAAM,EAAE,MAAM;IACdgM,IAAI,EAAE;EACV,CAAC;EACD5E,SAAS,EAAE;IACPhsB,KAAK,EAAE,MAAM;IACbqyB,UAAU,EAAE,QAAQ;IACpBC,gBAAgB,EAAE,kBAAkB;IACpCC,oBAAoB,EAAE,kBAAkB;IACxCC,gBAAgB,EAAE,kBAAkB;IACpCC,oBAAoB,EAAE,kBAAkB;IACxCrG,GAAG,EAAE,MAAM;IACX9lB,MAAM,EAAE,MAAM;IACdosB,SAAS,EAAE,IAAI;IACfxlB,QAAQ,EAAE,MAAM;IAChB2f,OAAO,EAAE,IAAI;IACbjX,MAAM,EAAE,IAAI;IACZ+c,iBAAiB,EAAE,UAAU;IAC7BC,aAAa,EAAE,YAAY;IAC3BC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,GAAG;IACdC,iBAAiB,EAAE,aAAa;IAChCC,cAAc,EAAE,SAAS;IACzBC,WAAW,EAAE,iBAAiB;IAC9BC,sBAAsB,EAAE,kBAAkB;IAC1CC,yBAAyB,EAAE,kBAAkB;IAC7CC,2BAA2B,EAAE;EACjC,CAAC;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,IAAI;IACZC,iBAAiB,EAAE,MAAM;IACzBC,YAAY,EAAE,QAAQ;IACtBC,WAAW,EAAE,MAAM;IACnBC,gBAAgB,EAAE,cAAc;IAChCC,IAAI,EAAE,GAAG;IACTC,eAAe,EAAE,OAAO;IACxBC,cAAc,EAAE,OAAO;IACvBC,GAAG,EAAE,GAAG;IACRC,EAAE,EAAE,GAAG;IACPC,YAAY,EAAE,MAAM;IACpBC,KAAK,EAAE,IAAI;IACXvtB,OAAO,EAAE,IAAI;IACb3C,GAAG,EAAE,GAAG;IACRlB,EAAE,EAAE,GAAG;IACPqxB,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,mBAAmB,EAAE,MAAM;IAC3BC,QAAQ,EAAE,IAAI;IACdC,gBAAgB,EAAE,MAAM;IACxBC,cAAc,EAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACTC,MAAM,EAAE,MAAM;IACdrM,MAAM,EAAE,SAAS;IACjBtP,MAAM,EAAE,IAAI;IACZ1Y,MAAM,EAAE;EACZ,CAAC;EACDs0B,eAAe,EAAE;IACb/hB,MAAM,EAAE,MAAM;IACdsZ,GAAG,EAAE,MAAM;IACX7S,EAAE,EAAE,MAAM;IACVub,IAAI,EAAE,MAAM;IACZ5nB,QAAQ,EAAE,MAAM;IAChB6nB,WAAW,EAAE,QAAQ;IACrBvf,UAAU,EAAE,QAAQ;IACpBwf,uBAAuB,EAAE,UAAU;IACnCC,UAAU,EAAE,MAAM;IAClBnB,GAAG,EAAE,GAAG;IACRC,EAAE,EAAE,GAAG;IACPmB,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,IAAI;IACV70B,MAAM,EAAE,IAAI;IACZwtB,IAAI,EAAE,YAAY;IAClBsH,aAAa,EAAE,QAAQ;IACvBC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACftc,MAAM,EAAE,IAAI;IACZuc,eAAe,EAAE,WAAW;IAC5BC,eAAe,EAAE,qBAAqB;IACtC7C,aAAa,EAAE,cAAc;IAC7B/Y,IAAI,EAAE,IAAI;IACV6b,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZ31B,KAAK,EAAE,MAAM;IACb41B,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE,MAAM;IACdzN,MAAM,EAAE,MAAM;IACdgE,GAAG,EAAE,QAAQ;IACb9S,IAAI,EAAE,MAAM;IACZwc,UAAU,EAAE,MAAM;IAClBC,iBAAiB,EAAE,QAAQ;IAC3BC,UAAU,EAAE,MAAM;IAClBtD,SAAS,EAAE,IAAI;IACf7hB,IAAI,EAAE,IAAI;IACV+E,MAAM,EAAE,IAAI;IACZ9C,MAAM,EAAE,MAAM;IACdmjB,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE,UAAU;IAC3BC,qBAAqB,EAAE,SAAS;IAChCC,4BAA4B,EAAE,aAAa;IAC3CC,qBAAqB,EAAE,aAAa;IACpC91B,MAAM,EAAE,IAAI;IACZ+1B,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,UAAU;IAC1BC,gBAAgB,EAAE,UAAU;IAC5BC,WAAW,EAAE;MACTje,IAAI,EAAE,IAAI;MACVke,QAAQ,EAAE,KAAK;MACf7sB,IAAI,EAAE,IAAI;MACV0T,MAAM,EAAE,IAAI;MACZoZ,OAAO,EAAE;IACb,CAAC;IACDC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,YAAY;IAC3BjE,MAAM,EAAE,IAAI;IACZkE,cAAc,EAAE;EACpB,CAAC;EACDC,KAAK,EAAE;IACHnR,YAAY,EAAE,MAAM;IACpBoR,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,gBAAgB;IAC/B1K,MAAM,EAAE,IAAI;IACZzT,MAAM,EAAE,IAAI;IACZoe,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,iBAAiB;IAC3BC,WAAW,EAAE,gCAAgC;IAC7CC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,cAAc;IAC7BC,SAAS,EAAE,OAAO;IAClB9vB,MAAM,EAAE,SAAS;IACjB+vB,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,UAAU;IACrB1P,QAAQ,EAAE,IAAI;IACd2P,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,YAAY;IACpBlpB,OAAO,EAAE,MAAM;IACfmpB,OAAO,EAAE,iBAAiB;IAC1BC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,cAAc;IAC1BlP,aAAa,EAAE,MAAM;IACrBmP,YAAY,EAAE,OAAO;IACrBC,oBAAoB,EAAE,OAAO;IAC7BC,OAAO,EAAE,MAAM;IACf7xB,OAAO,EAAE,IAAI;IACbwpB,WAAW,EAAE,kBAAkB;IAC/BsI,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE,SAAS;IACpB1F,iBAAiB,EAAE,YAAY;IAC/B2F,SAAS,EAAE;EACf,CAAC;EACDC,SAAS,EAAE;IACP54B,KAAK,EAAE,MAAM;IACb+O,OAAO,EAAE,uCAAuC;IAChDxO,MAAM,EAAE,KAAK;IACbmG,OAAO,EAAE,OAAO;IAChBmyB,QAAQ,EAAE;EACd,CAAC;EACDC,WAAW,EAAE;IACTC,YAAY,EAAE,QAAQ;IACtBnQ,QAAQ,EAAE,4BAA4B;IACtCliB,OAAO,EAAE;EACb,CAAC;EACDsyB,IAAI,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}