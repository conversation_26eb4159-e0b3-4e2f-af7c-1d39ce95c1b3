{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/agent',\n  name: 'agent',\n  // route level code-splitting\n  // this generates a separate chunk (about.[hash].js) for this route\n  // which is lazy-loaded when the route is visited.\n  component: () => import(/* webpackChunkName: \"about\" */'../views/agent/index.vue'),\n  children: [{\n    path: 'chat/:topicId',\n    component: () => import(/* webpackChunkName: \"hubble\" */'views/agent/chat/index.vue'),\n    meta: {\n      hasSlider: true,\n      isHubblePage: true,\n      isHubbleNoLoginPage: true\n    }\n  }, {\n    path: 'shareChat/:topicId',\n    component: () => import(/* webpackChunkName: \"hubble\" */'views/agent/chat/index.vue'),\n    meta: {\n      isSharePage: true,\n      isHubblePage: true,\n      isHubbleNoLoginPage: true\n    }\n  }, {\n    path: 'upload',\n    component: () => import(/* webpackChunkName: \"hubble\" */'views/agent/upload/index.vue'),\n    meta: {\n      isUploadPage: true,\n      hasSlider: true,\n      isHubblePage: true,\n      isHubbleNoLoginPage: true\n    }\n  }, {\n    path: 'share/:token',\n    component: () => import(/* webpackChunkName: \"hubble\" */'views/agent/share/index.vue'),\n    meta: {\n      isSharePage: true,\n      noLogin: true,\n      isHubblePage: true\n    }\n  }]\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "name", "component", "children", "meta", "hasSlider", "isHubblePage", "isHubbleNoLoginPage", "isSharePage", "isUploadPage", "noLogin", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/agent',\n    name: 'agent',\n    // route level code-splitting\n    // this generates a separate chunk (about.[hash].js) for this route\n    // which is lazy-loaded when the route is visited.\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/agent/index.vue'),\n            children: [{\n                path: 'chat/:topicId',\n                component: () => import(/* webpackChunkName: \"hubble\" */ 'views/agent/chat/index.vue'),\n                meta: {\n                    hasSlider: true,\n                    isHubblePage: true,\n                    isHubbleNoLoginPage: true,\n                },\n            }, {\n                path: 'shareChat/:topicId',\n                component: () => import(/* webpackChunkName: \"hubble\" */ 'views/agent/chat/index.vue'),\n                meta: {\n                    isSharePage: true,\n                    isHubblePage: true,\n                    isHubbleNoLoginPage: true,\n                },\n            }, {\n                path: 'upload',\n                component: () => import(/* webpackChunkName: \"hubble\" */ 'views/agent/upload/index.vue'),\n                meta: {\n                    isUploadPage: true,\n                    hasSlider: true,\n                    isHubblePage: true,\n                    isHubbleNoLoginPage: true,\n                },\n            }, {\n                path: 'share/:token',\n                component: () => import(/* webpackChunkName: \"hubble\" */ 'views/agent/share/index.vue'),\n                meta: {\n                    isSharePage: true,\n                    noLogin: true,\n                    isHubblePage: true,\n                },\n            }],\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAElCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAElB,MAAME,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACb;EACA;EACA;EACAC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,0BAA0B,CAAC;EAC3EC,QAAQ,EAAE,CAAC;IACPH,IAAI,EAAE,eAAe;IACrBE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAiC,4BAA4B,CAAC;IACtFE,IAAI,EAAE;MACFC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;IACzB;EACJ,CAAC,EAAE;IACCP,IAAI,EAAE,oBAAoB;IAC1BE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAiC,4BAA4B,CAAC;IACtFE,IAAI,EAAE;MACFI,WAAW,EAAE,IAAI;MACjBF,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;IACzB;EACJ,CAAC,EAAE;IACCP,IAAI,EAAE,QAAQ;IACdE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAiC,8BAA8B,CAAC;IACxFE,IAAI,EAAE;MACFK,YAAY,EAAE,IAAI;MAClBJ,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;IACzB;EACJ,CAAC,EAAE;IACCP,IAAI,EAAE,cAAc;IACpBE,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAiC,6BAA6B,CAAC;IACvFE,IAAI,EAAE;MACFI,WAAW,EAAE,IAAI;MACjBE,OAAO,EAAE,IAAI;MACbJ,YAAY,EAAE;IAClB;EACJ,CAAC;AACX,CAAC,CACF;AAED,MAAMK,MAAM,GAAG,IAAId,SAAS,CAAC;EAC3Be,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BjB;AACF,CAAC,CAAC;AAEF,eAAeY,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}