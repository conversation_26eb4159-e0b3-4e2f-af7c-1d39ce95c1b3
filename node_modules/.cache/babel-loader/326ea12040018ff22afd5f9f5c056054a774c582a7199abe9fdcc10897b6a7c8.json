{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// fix CFD-4940\nimport { markInfo } from '../../pages/foundation/sign/common/info/info';\nexport const throttleScroll = (action, delay) => {\n  let flag = false;\n  return function () {\n    const context = this;\n    const args = arguments;\n    let timer = null;\n    if (flag) {\n      timer = setTimeout(() => {\n        flag = false;\n        action.apply(context, args);\n      }, delay);\n    } else {\n      flag = true;\n      if (!timer) {\n        action.apply(context, args);\n      } else {\n        timer = setTimeout(() => {\n          flag = false;\n          action.apply(context, args);\n        }, delay);\n      }\n    }\n  };\n};\nexport const throttle = (action, delay) => {\n  let flag = false;\n  return function () {\n    const context = this;\n    const args = arguments;\n    if (flag) {\n      return;\n    }\n    flag = true;\n    action.apply(context, args);\n    setTimeout(() => {\n      flag = false;\n    }, delay);\n  };\n};\nexport const debounce = (action, delay) => {\n  let timeout = null;\n  return function () {\n    const context = this;\n    const args = arguments;\n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    timeout = setTimeout(() => {\n      action.apply(context, args);\n      clearTimeout(timeout);\n    }, delay);\n  };\n};\n// 生成格式为'2018年12月05日'的日期\nexport function dateGenerator() {\n  const date = new Date();\n  const dateNum = date.getDate();\n  const monthNum = date.getMonth() + 1;\n  const Y = date.getFullYear();\n  const M = monthNum < 10 ? `0${monthNum}` : `${monthNum}`;\n  const D = dateNum < 10 ? `0${dateNum}` : `${dateNum}`;\n  return `${Y}-${M}-${D}`;\n}\n\n/**\n * @param  {Object} mark 标签对象\n * @param  {Number} pageWidth 页高\n * @param  {Number} pageHeight 页宽\n * @return {Object}  返回标签左下角以页面左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,标签宽高占页面宽高的百分比width,height\n * @desc  标签坐标转换，换成百分比传给服务端\n */\nexport function markCoordinateTransform(mark, pageWidth, pageHeight) {\n  // fix CFD-4940: 签章位置为0校验\n  const markHeight = mark.height || markInfo(mark.type).height;\n  const markWidth = mark.width || markInfo(mark.type).width;\n  let x = mark.x / pageWidth;\n  let y = 1 - (mark.y + Math.round(markHeight)) / pageHeight;\n  const width = Math.round(markWidth) / pageWidth;\n  const height = Math.round(markHeight) / pageHeight;\n  // 有时候计算结果会为负\n  if (y < 0) {\n    y = 0;\n  }\n  if (x < 0) {\n    x = 0;\n  }\n  if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type)) {\n    return {\n      x,\n      y,\n      width,\n      height,\n      buttons: markButtonsCoordinateTransform(mark.buttons, pageWidth, pageHeight, height)\n    };\n  }\n  return {\n    x,\n    y,\n    width,\n    height\n  };\n}\n// 由百分比转为数值，与上面的 markCoordinateTransform 相反\nexport function coordinateReversal(mark, pageWidth, pageHeight) {\n  // 标签宽高如果是百分比的转换为整数数值\n  const width = mark.width <= 1 ? Math.round(mark.width * pageWidth) : mark.width;\n  const height = mark.height <= 1 ? Math.round(mark.height * pageHeight) : mark.height;\n  // 旧数据坐标值还是数值，不做转换\n  const x = mark.x < 1 ? mark.x * pageWidth : mark.x;\n  const y = mark.y < 1 ? (1 - mark.y) * pageHeight - height : mark.y;\n  if (mark.type && ['MULTIPLE_BOX', 'SINGLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(mark.type)) {\n    return {\n      x,\n      y,\n      width,\n      height,\n      buttons: markButtonsCoordinateReversal(mark.buttons, pageWidth, pageHeight, height, mark.type)\n    };\n  }\n  return {\n    x,\n    y,\n    width,\n    height\n  };\n}\n\n/**\n * @param  {Object} button 复选框，单选框按钮\n * @param  {Number} pageWidth 页高\n * @param  {Number} pageHeight 页宽\n * @return {Object}  返回复选框，单选框按钮以标签左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,按钮宽高占页面宽高的百分比width,height\n * @desc  标签坐标转换，换成百分比传给服务端\n */\n\nexport function markButtonsCoordinateTransform(buttons, pageWidth, pageHeight, height) {\n  const newButtons = [];\n  buttons.forEach(item => {\n    newButtons.push({\n      ...item,\n      buttonX: item.buttonX / pageWidth,\n      buttonY: height - (item.buttonY + 28) / pageHeight\n    });\n  });\n  return newButtons;\n}\n\n// 由百分比转为数值，与上面的 markButtonsCoordinateTransform 相反\nexport function markButtonsCoordinateReversal(buttons, pageWidth, pageHeight, height, type) {\n  const buttonHeight = type === 'CONFIRMATION_REQUEST_SEAL' ? 203 : 28;\n  const newButtons = [];\n  buttons.forEach(item => {\n    newButtons.push({\n      ...item,\n      buttonX: item.buttonX * pageWidth,\n      buttonY: height - (item.buttonY * pageHeight + buttonHeight)\n    });\n  });\n  return newButtons;\n}\n\n// 节流队列\nexport const throttleQueue = {\n  queue: [],\n  timer: null,\n  execute: function (param = {\n    func: function () {}\n  }, division) {\n    // 立即执行并清空队列\n    if (division === 0) {\n      param.func();\n      this.reset();\n      return;\n    }\n\n    // 没传参的属于自执行\n    if (!arguments.length) {\n      // 队列中没有更新的任务，重置队列和定时器\n      if (!this.queue.length) {\n        this.reset();\n        return;\n      } else {\n        // 执行最新的任务\n        this.getLastTask().param.func();\n        // 清理历史任务，定时器继续检测\n        this.clearQueue();\n        return;\n      }\n    }\n    this.queue.push({\n      param\n    });\n\n    // 定时器未执行时，立即执行并启动定时器\n    if (!this.timer) {\n      // this.getLastTask().param.func();\n      this.startTimer(division);\n    }\n  },\n  getLastTask: function () {\n    return this.queue.pop();\n  },\n  startTimer: function (division = 500) {\n    // division在队列中不支持多个定时频率\n    this.timer = setInterval(() => {\n      this.execute();\n    }, division);\n  },\n  clearQueue: function () {\n    this.queue = [];\n  },\n  clearTimer: function () {\n    clearInterval(this.timer);\n    this.timer = null;\n  },\n  reset: function () {\n    this.clearQueue();\n    this.clearTimer();\n  }\n};", "map": {"version": 3, "names": ["markInfo", "throttleScroll", "action", "delay", "flag", "context", "args", "arguments", "timer", "setTimeout", "apply", "throttle", "debounce", "timeout", "clearTimeout", "dateGenerator", "date", "Date", "dateNum", "getDate", "monthNum", "getMonth", "Y", "getFullYear", "M", "D", "markCoordinateTransform", "mark", "pageWidth", "pageHeight", "<PERSON><PERSON><PERSON><PERSON>", "height", "type", "<PERSON><PERSON><PERSON><PERSON>", "width", "x", "y", "Math", "round", "includes", "buttons", "markButtonsCoordinateTransform", "coordinateReversal", "markButtonsCoordinateReversal", "newButtons", "for<PERSON>ach", "item", "push", "buttonX", "buttonY", "buttonHeight", "throttleQueue", "queue", "execute", "param", "func", "division", "reset", "length", "getLastTask", "clearQueue", "startTimer", "pop", "setInterval", "clearTimer", "clearInterval"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/utils/fn.js"], "sourcesContent": ["// fix CFD-4940\nimport { markInfo } from '../../pages/foundation/sign/common/info/info';\n\nexport const throttleScroll = (action, delay) => {\n    let flag = false;\n    return function() {\n        const context = this;\n        const args = arguments;\n        let timer = null;\n        if (flag) {\n            timer = setTimeout(() => {\n                flag = false;\n                action.apply(context, args);\n            }, delay);\n        } else {\n            flag = true;\n            if (!timer) {\n                action.apply(context, args);\n            } else {\n                timer = setTimeout(() => {\n                    flag = false;\n                    action.apply(context, args);\n                }, delay);\n            }\n        }\n    };\n};\nexport const throttle = (action, delay) => {\n    let flag = false;\n    return function() {\n        const context = this;\n        const args = arguments;\n        if (flag) {\n            return;\n        }\n        flag = true;\n        action.apply(context, args);\n        setTimeout(() => {\n            flag = false;\n        }, delay);\n    };\n};\nexport const debounce = (action, delay) => {\n    let timeout = null;\n    return function() {\n        const context = this;\n        const args = arguments;\n        if (timeout !== null) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(() => {\n            action.apply(context, args);\n            clearTimeout(timeout);\n        }, delay);\n    };\n};\n// 生成格式为'2018年12月05日'的日期\nexport function dateGenerator() {\n    const date = new Date();\n    const dateNum = date.getDate();\n    const monthNum = date.getMonth() + 1;\n    const Y = date.getFullYear();\n    const M = monthNum < 10 ? `0${monthNum}` : `${monthNum}`;\n    const D = dateNum < 10 ? `0${dateNum}` : `${dateNum}`;\n    return `${Y}-${M}-${D}`;\n}\n\n/**\n * @param  {Object} mark 标签对象\n * @param  {Number} pageWidth 页高\n * @param  {Number} pageHeight 页宽\n * @return {Object}  返回标签左下角以页面左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,标签宽高占页面宽高的百分比width,height\n * @desc  标签坐标转换，换成百分比传给服务端\n */\nexport function markCoordinateTransform(mark, pageWidth, pageHeight) {\n    // fix CFD-4940: 签章位置为0校验\n    const markHeight = mark.height || markInfo(mark.type).height;\n    const markWidth = mark.width || markInfo(mark.type).width;\n    let x = (mark.x) / pageWidth;\n    let y = 1 - (mark.y + Math.round(markHeight)) / pageHeight;\n    const width = Math.round(markWidth) / pageWidth;\n    const height = Math.round(markHeight) / pageHeight;\n    // 有时候计算结果会为负\n    if (y < 0) {\n        y = 0;\n    }\n    if (x < 0) {\n        x = 0;\n    }\n    if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type)) {\n        return  { x, y, width, height, buttons: markButtonsCoordinateTransform(mark.buttons, pageWidth, pageHeight, height) };\n    }\n    return { x, y, width, height };\n}\n// 由百分比转为数值，与上面的 markCoordinateTransform 相反\nexport function coordinateReversal(mark, pageWidth, pageHeight) {\n    // 标签宽高如果是百分比的转换为整数数值\n    const width = mark.width <= 1 ? Math.round(mark.width * pageWidth) : mark.width;\n    const height = mark.height <= 1 ?  Math.round(mark.height * pageHeight) : mark.height;\n    // 旧数据坐标值还是数值，不做转换\n    const x = mark.x < 1 ? mark.x * pageWidth : mark.x;\n    const y = mark.y < 1 ? (1 - mark.y) * pageHeight - height : mark.y;\n    if (mark.type && ['MULTIPLE_BOX', 'SINGLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(mark.type)) {\n        return  { x, y, width, height, buttons: markButtonsCoordinateReversal(mark.buttons, pageWidth, pageHeight, height, mark.type) };\n    }\n    return { x, y, width, height };\n}\n\n/**\n * @param  {Object} button 复选框，单选框按钮\n * @param  {Number} pageWidth 页高\n * @param  {Number} pageHeight 页宽\n * @return {Object}  返回复选框，单选框按钮以标签左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,按钮宽高占页面宽高的百分比width,height\n * @desc  标签坐标转换，换成百分比传给服务端\n */\n\nexport function markButtonsCoordinateTransform(buttons, pageWidth, pageHeight, height) {\n    const newButtons = [];\n    buttons.forEach(item => {\n        newButtons.push({\n            ...item,\n            buttonX: item.buttonX / pageWidth,\n            buttonY: height - (item.buttonY + 28) / pageHeight,\n        });\n    });\n    return newButtons;\n}\n\n// 由百分比转为数值，与上面的 markButtonsCoordinateTransform 相反\nexport function markButtonsCoordinateReversal(buttons, pageWidth, pageHeight, height, type) {\n    const buttonHeight = type === 'CONFIRMATION_REQUEST_SEAL' ? 203 : 28;\n    const newButtons = [];\n    buttons.forEach(item => {\n        newButtons.push({\n            ...item,\n            buttonX: item.buttonX * pageWidth,\n            buttonY: height - (item.buttonY * pageHeight + buttonHeight),\n        });\n    });\n    return newButtons;\n}\n\n// 节流队列\nexport const throttleQueue = {\n    queue: [],\n    timer: null,\n    execute: function(param = { func: function() {} }, division) {\n        // 立即执行并清空队列\n        if (division === 0) {\n            param.func();\n            this.reset();\n            return;\n        }\n\n        // 没传参的属于自执行\n        if (!arguments.length) {\n            // 队列中没有更新的任务，重置队列和定时器\n            if (!this.queue.length) {\n                this.reset();\n                return;\n            } else {\n                // 执行最新的任务\n                this.getLastTask().param.func();\n                // 清理历史任务，定时器继续检测\n                this.clearQueue();\n                return;\n            }\n        }\n\n        this.queue.push({ param });\n\n        // 定时器未执行时，立即执行并启动定时器\n        if (!this.timer) {\n            // this.getLastTask().param.func();\n            this.startTimer(division);\n        }\n    },\n    getLastTask: function() {\n        return this.queue.pop();\n    },\n    startTimer: function(division = 500) {\n        // division在队列中不支持多个定时频率\n        this.timer = setInterval(() => {\n            this.execute();\n        }, division);\n    },\n    clearQueue: function() {\n        this.queue = [];\n    },\n    clearTimer: function() {\n        clearInterval(this.timer);\n        this.timer = null;\n    },\n    reset: function() {\n        this.clearQueue();\n        this.clearTimer();\n    },\n};\n"], "mappings": ";;;AAAA;AACA,SAASA,QAAQ,QAAQ,8CAA8C;AAEvE,OAAO,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC7C,IAAIC,IAAI,GAAG,KAAK;EAChB,OAAO,YAAW;IACd,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAMC,IAAI,GAAGC,SAAS;IACtB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIJ,IAAI,EAAE;MACNI,KAAK,GAAGC,UAAU,CAAC,MAAM;QACrBL,IAAI,GAAG,KAAK;QACZF,MAAM,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;MAC/B,CAAC,EAAEH,KAAK,CAAC;IACb,CAAC,MAAM;MACHC,IAAI,GAAG,IAAI;MACX,IAAI,CAACI,KAAK,EAAE;QACRN,MAAM,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;MAC/B,CAAC,MAAM;QACHE,KAAK,GAAGC,UAAU,CAAC,MAAM;UACrBL,IAAI,GAAG,KAAK;UACZF,MAAM,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;QAC/B,CAAC,EAAEH,KAAK,CAAC;MACb;IACJ;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMQ,QAAQ,GAAGA,CAACT,MAAM,EAAEC,KAAK,KAAK;EACvC,IAAIC,IAAI,GAAG,KAAK;EAChB,OAAO,YAAW;IACd,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAMC,IAAI,GAAGC,SAAS;IACtB,IAAIH,IAAI,EAAE;MACN;IACJ;IACAA,IAAI,GAAG,IAAI;IACXF,MAAM,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;IAC3BG,UAAU,CAAC,MAAM;MACbL,IAAI,GAAG,KAAK;IAChB,CAAC,EAAED,KAAK,CAAC;EACb,CAAC;AACL,CAAC;AACD,OAAO,MAAMS,QAAQ,GAAGA,CAACV,MAAM,EAAEC,KAAK,KAAK;EACvC,IAAIU,OAAO,GAAG,IAAI;EAClB,OAAO,YAAW;IACd,MAAMR,OAAO,GAAG,IAAI;IACpB,MAAMC,IAAI,GAAGC,SAAS;IACtB,IAAIM,OAAO,KAAK,IAAI,EAAE;MAClBC,YAAY,CAACD,OAAO,CAAC;IACzB;IACAA,OAAO,GAAGJ,UAAU,CAAC,MAAM;MACvBP,MAAM,CAACQ,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;MAC3BQ,YAAY,CAACD,OAAO,CAAC;IACzB,CAAC,EAAEV,KAAK,CAAC;EACb,CAAC;AACL,CAAC;AACD;AACA,OAAO,SAASY,aAAaA,CAAA,EAAG;EAC5B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;EACvB,MAAMC,OAAO,GAAGF,IAAI,CAACG,OAAO,CAAC,CAAC;EAC9B,MAAMC,QAAQ,GAAGJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;EACpC,MAAMC,CAAC,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC;EAC5B,MAAMC,CAAC,GAAGJ,QAAQ,GAAG,EAAE,GAAG,IAAIA,QAAQ,EAAE,GAAG,GAAGA,QAAQ,EAAE;EACxD,MAAMK,CAAC,GAAGP,OAAO,GAAG,EAAE,GAAG,IAAIA,OAAO,EAAE,GAAG,GAAGA,OAAO,EAAE;EACrD,OAAO,GAAGI,CAAC,IAAIE,CAAC,IAAIC,CAAC,EAAE;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACjE;EACA,MAAMC,UAAU,GAAGH,IAAI,CAACI,MAAM,IAAI/B,QAAQ,CAAC2B,IAAI,CAACK,IAAI,CAAC,CAACD,MAAM;EAC5D,MAAME,SAAS,GAAGN,IAAI,CAACO,KAAK,IAAIlC,QAAQ,CAAC2B,IAAI,CAACK,IAAI,CAAC,CAACE,KAAK;EACzD,IAAIC,CAAC,GAAIR,IAAI,CAACQ,CAAC,GAAIP,SAAS;EAC5B,IAAIQ,CAAC,GAAG,CAAC,GAAG,CAACT,IAAI,CAACS,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACR,UAAU,CAAC,IAAID,UAAU;EAC1D,MAAMK,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC,GAAGL,SAAS;EAC/C,MAAMG,MAAM,GAAGM,IAAI,CAACC,KAAK,CAACR,UAAU,CAAC,GAAGD,UAAU;EAClD;EACA,IAAIO,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,GAAG,CAAC;EACT;EACA,IAAID,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,GAAG,CAAC;EACT;EACA,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAACI,QAAQ,CAACZ,IAAI,CAACK,IAAI,CAAC,EAAE;IACpD,OAAQ;MAAEG,CAAC;MAAEC,CAAC;MAAEF,KAAK;MAAEH,MAAM;MAAES,OAAO,EAAEC,8BAA8B,CAACd,IAAI,CAACa,OAAO,EAAEZ,SAAS,EAAEC,UAAU,EAAEE,MAAM;IAAE,CAAC;EACzH;EACA,OAAO;IAAEI,CAAC;IAAEC,CAAC;IAAEF,KAAK;IAAEH;EAAO,CAAC;AAClC;AACA;AACA,OAAO,SAASW,kBAAkBA,CAACf,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAC5D;EACA,MAAMK,KAAK,GAAGP,IAAI,CAACO,KAAK,IAAI,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACX,IAAI,CAACO,KAAK,GAAGN,SAAS,CAAC,GAAGD,IAAI,CAACO,KAAK;EAC/E,MAAMH,MAAM,GAAGJ,IAAI,CAACI,MAAM,IAAI,CAAC,GAAIM,IAAI,CAACC,KAAK,CAACX,IAAI,CAACI,MAAM,GAAGF,UAAU,CAAC,GAAGF,IAAI,CAACI,MAAM;EACrF;EACA,MAAMI,CAAC,GAAGR,IAAI,CAACQ,CAAC,GAAG,CAAC,GAAGR,IAAI,CAACQ,CAAC,GAAGP,SAAS,GAAGD,IAAI,CAACQ,CAAC;EAClD,MAAMC,CAAC,GAAGT,IAAI,CAACS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGT,IAAI,CAACS,CAAC,IAAIP,UAAU,GAAGE,MAAM,GAAGJ,IAAI,CAACS,CAAC;EAClE,IAAIT,IAAI,CAACK,IAAI,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,2BAA2B,CAAC,CAACO,QAAQ,CAACZ,IAAI,CAACK,IAAI,CAAC,EAAE;IAC9F,OAAQ;MAAEG,CAAC;MAAEC,CAAC;MAAEF,KAAK;MAAEH,MAAM;MAAES,OAAO,EAAEG,6BAA6B,CAAChB,IAAI,CAACa,OAAO,EAAEZ,SAAS,EAAEC,UAAU,EAAEE,MAAM,EAAEJ,IAAI,CAACK,IAAI;IAAE,CAAC;EACnI;EACA,OAAO;IAAEG,CAAC;IAAEC,CAAC;IAAEF,KAAK;IAAEH;EAAO,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASU,8BAA8BA,CAACD,OAAO,EAAEZ,SAAS,EAAEC,UAAU,EAAEE,MAAM,EAAE;EACnF,MAAMa,UAAU,GAAG,EAAE;EACrBJ,OAAO,CAACK,OAAO,CAACC,IAAI,IAAI;IACpBF,UAAU,CAACG,IAAI,CAAC;MACZ,GAAGD,IAAI;MACPE,OAAO,EAAEF,IAAI,CAACE,OAAO,GAAGpB,SAAS;MACjCqB,OAAO,EAAElB,MAAM,GAAG,CAACe,IAAI,CAACG,OAAO,GAAG,EAAE,IAAIpB;IAC5C,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOe,UAAU;AACrB;;AAEA;AACA,OAAO,SAASD,6BAA6BA,CAACH,OAAO,EAAEZ,SAAS,EAAEC,UAAU,EAAEE,MAAM,EAAEC,IAAI,EAAE;EACxF,MAAMkB,YAAY,GAAGlB,IAAI,KAAK,2BAA2B,GAAG,GAAG,GAAG,EAAE;EACpE,MAAMY,UAAU,GAAG,EAAE;EACrBJ,OAAO,CAACK,OAAO,CAACC,IAAI,IAAI;IACpBF,UAAU,CAACG,IAAI,CAAC;MACZ,GAAGD,IAAI;MACPE,OAAO,EAAEF,IAAI,CAACE,OAAO,GAAGpB,SAAS;MACjCqB,OAAO,EAAElB,MAAM,IAAIe,IAAI,CAACG,OAAO,GAAGpB,UAAU,GAAGqB,YAAY;IAC/D,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAON,UAAU;AACrB;;AAEA;AACA,OAAO,MAAMO,aAAa,GAAG;EACzBC,KAAK,EAAE,EAAE;EACT5C,KAAK,EAAE,IAAI;EACX6C,OAAO,EAAE,SAAAA,CAASC,KAAK,GAAG;IAAEC,IAAI,EAAE,SAAAA,CAAA,EAAW,CAAC;EAAE,CAAC,EAAEC,QAAQ,EAAE;IACzD;IACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAChBF,KAAK,CAACC,IAAI,CAAC,CAAC;MACZ,IAAI,CAACE,KAAK,CAAC,CAAC;MACZ;IACJ;;IAEA;IACA,IAAI,CAAClD,SAAS,CAACmD,MAAM,EAAE;MACnB;MACA,IAAI,CAAC,IAAI,CAACN,KAAK,CAACM,MAAM,EAAE;QACpB,IAAI,CAACD,KAAK,CAAC,CAAC;QACZ;MACJ,CAAC,MAAM;QACH;QACA,IAAI,CAACE,WAAW,CAAC,CAAC,CAACL,KAAK,CAACC,IAAI,CAAC,CAAC;QAC/B;QACA,IAAI,CAACK,UAAU,CAAC,CAAC;QACjB;MACJ;IACJ;IAEA,IAAI,CAACR,KAAK,CAACL,IAAI,CAAC;MAAEO;IAAM,CAAC,CAAC;;IAE1B;IACA,IAAI,CAAC,IAAI,CAAC9C,KAAK,EAAE;MACb;MACA,IAAI,CAACqD,UAAU,CAACL,QAAQ,CAAC;IAC7B;EACJ,CAAC;EACDG,WAAW,EAAE,SAAAA,CAAA,EAAW;IACpB,OAAO,IAAI,CAACP,KAAK,CAACU,GAAG,CAAC,CAAC;EAC3B,CAAC;EACDD,UAAU,EAAE,SAAAA,CAASL,QAAQ,GAAG,GAAG,EAAE;IACjC;IACA,IAAI,CAAChD,KAAK,GAAGuD,WAAW,CAAC,MAAM;MAC3B,IAAI,CAACV,OAAO,CAAC,CAAC;IAClB,CAAC,EAAEG,QAAQ,CAAC;EAChB,CAAC;EACDI,UAAU,EAAE,SAAAA,CAAA,EAAW;IACnB,IAAI,CAACR,KAAK,GAAG,EAAE;EACnB,CAAC;EACDY,UAAU,EAAE,SAAAA,CAAA,EAAW;IACnBC,aAAa,CAAC,IAAI,CAACzD,KAAK,CAAC;IACzB,IAAI,CAACA,KAAK,GAAG,IAAI;EACrB,CAAC;EACDiD,KAAK,EAAE,SAAAA,CAAA,EAAW;IACd,IAAI,CAACG,UAAU,CAAC,CAAC;IACjB,IAAI,CAACI,UAAU,CAAC,CAAC;EACrB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}