{"ast": null, "code": "export default {\n  home: '首页',\n  addEnterprise: '新增企业',\n  lookoverEnterprise: '查看全部企业',\n  chooseEnterprise: '选择企业',\n  enterEnterprise: '请输入企业名称',\n  inApproval: '审批中',\n  copyright: '版权所有',\n  record: 'ICP主体备案号：浙ICP备14031930号',\n  chooseSignType: '选择签约方式',\n  agreeProtocal: '同意协议',\n  ok: '知道了',\n  newVersionTip: '新版本提示',\n  notice: '公告',\n  clickToLook: '点击查看',\n  bizManage: '支持企业对业务字段统一管理',\n  protocalUpdate: '隐私政策更新',\n  startUse: '开始使用',\n  joinSuccess: '你已成功加入',\n  contactus: '联系我们',\n  askOnline: '在线咨询',\n  askWeChat: '微信咨询 | 关注微信公众号',\n  downLoad: '下载移动客户端',\n  convenience: '无论你是在办公室、家里，还是在出差路上，上上签可以让你随时随地查阅、签署发送文件。',\n  news: '欢迎关注我们的微信公众号，锁定我们最新的动态资讯。',\n  tip: '提示',\n  unverify: '还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证。',\n  isGroupProxyAuth: '本企业目前认证状态为集团代认证，合同收件人将无法识别您的身份，建议您先补充实名认证材料。',\n  createCompnayP: {\n    p1: '即将为您生成新企业，请完善企业名称',\n    p2: '立即提交企业相关材料（用于实名认证），获得企业名称',\n    p3: '暂不认证，手动填写',\n    p4: '未经认证的企业名称仅对自己展示'\n  },\n  plzEnterRightEnt: '请输入正确的企业名称',\n  goAuthenticate: '去认证',\n  keepLaunch: '继续发起',\n  enterprise: '企业',\n  person: '个人',\n  usercenter: '用户中心',\n  console: '企业控制台',\n  entAccount: '企业账号',\n  personAccount: '个人账号',\n  viewAllEnt: '查看全部企业',\n  addEnt: '新增企业',\n  createEnt: '创建企业',\n  exit: '退出',\n  viewDetail: '点击查看详情',\n  message: '消息',\n  datainsure: '数据保',\n  video: '操作视频',\n  help: '帮助',\n  dynamic: '产品动态',\n  contractManage: '合同管理',\n  templateManage: '模板管理',\n  statisticCharts: '统计报表',\n  authenticating: '认证中企业',\n  ecology: '来自{developerName}的企业账号',\n  ecologyPerson: '来自{developerName}的个人账号',\n  unAuthenticate: '未认证企业',\n  rejectAuthenticate: '实名已驳回',\n  contactManage: '请联系管理员给您分配权限',\n  noResult: '没有结果',\n  confirm: '确定',\n  cancel: '取消',\n  createSuccess: '创建成功',\n  notifyPhone: '提交通知手机',\n  phone: '手机',\n  phonePlaceholder: '请填写11位手机号',\n  verifyCode: '验证码',\n  sixNumberPlaceholder: '请填写6位数字',\n  submit: '提交',\n  phoneInputError: '请输入正确的手机号',\n  verCodeInputErr: '请输入正确的验证码',\n  setSuccess: '设置成功',\n  offlineContractManage: '线下合同管理',\n  phoneSetTip: '提交通知手机号，可用于接受系统发出的通知短信，如签署通知、签约验证码等。',\n  noMoreTip: '不再提醒',\n  phoneSetAfterTip: '（如需稍后设置，可在【用户中心】的【通知】里补充手机号）',\n  theEnterprise: '该企业',\n  you: '您',\n  passAutEntHint: '通过企业认证后显示企业名称',\n  passAutPersonHint: '通过实名认证后显示个人姓名',\n  createEntNow: '立即创建企业',\n  announcement: {\n    pwReset: '您的密码已过期，为了您的账户安全，请及时前往【用户中心】修改密码'\n  },\n  partAuthSearch: '发合同前查询下相对方企业实名情况？'\n};", "map": {"version": 3, "names": ["home", "addEnterprise", "lookoverEnterprise", "chooseEnterprise", "enterEnterprise", "inApproval", "copyright", "record", "chooseSignType", "agreeProtocal", "ok", "newVersionTip", "notice", "clickToLook", "bizManage", "protocalUpdate", "startUse", "joinSuccess", "contactus", "askOnline", "askWeChat", "downLoad", "convenience", "news", "tip", "unverify", "isGroupProxyAuth", "createCompnayP", "p1", "p2", "p3", "p4", "plzEnterRightEnt", "goAuthenticate", "keepLaunch", "enterprise", "person", "usercenter", "console", "entAccount", "personAccount", "viewAllEnt", "addEnt", "createEnt", "exit", "viewDetail", "message", "datainsure", "video", "help", "dynamic", "contractManage", "templateManage", "statistic<PERSON><PERSON>s", "authenticating", "ecology", "<PERSON><PERSON><PERSON>", "unAuthenticate", "rejectAuthenticate", "contactManage", "noResult", "confirm", "cancel", "createSuccess", "notifyPhone", "phone", "phonePlaceholder", "verifyCode", "sixNumberPlaceholder", "submit", "phoneInputError", "verCodeInputErr", "setSuccess", "offlineContractManage", "phoneSetTip", "noMoreTip", "phoneSetAfterTip", "theEnterprise", "you", "passAutEntHint", "passAutPersonHint", "createEntNow", "announcement", "pw<PERSON><PERSON><PERSON>", "partAuthSearch"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/home/<USER>"], "sourcesContent": ["export default {\n    home: '首页',\n    addEnterprise: '新增企业',\n    lookoverEnterprise: '查看全部企业',\n    chooseEnterprise: '选择企业',\n    enterEnterprise: '请输入企业名称',\n    inApproval: '审批中',\n    copyright: '版权所有',\n    record: 'ICP主体备案号：浙ICP备14031930号',\n    chooseSignType: '选择签约方式',\n    agreeProtocal: '同意协议',\n    ok: '知道了',\n    newVersionTip: '新版本提示',\n    notice: '公告',\n    clickToLook: '点击查看',\n    bizManage: '支持企业对业务字段统一管理',\n    protocalUpdate: '隐私政策更新',\n    startUse: '开始使用',\n    joinSuccess: '你已成功加入',\n    contactus: '联系我们',\n    askOnline: '在线咨询',\n    askWeChat: '微信咨询 | 关注微信公众号',\n    downLoad: '下载移动客户端',\n    convenience: '无论你是在办公室、家里，还是在出差路上，上上签可以让你随时随地查阅、签署发送文件。',\n    news: '欢迎关注我们的微信公众号，锁定我们最新的动态资讯。',\n    tip: '提示',\n    unverify: '还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证。',\n    isGroupProxyAuth: '本企业目前认证状态为集团代认证，合同收件人将无法识别您的身份，建议您先补充实名认证材料。',\n    createCompnayP: {\n        p1: '即将为您生成新企业，请完善企业名称',\n        p2: '立即提交企业相关材料（用于实名认证），获得企业名称',\n        p3: '暂不认证，手动填写',\n        p4: '未经认证的企业名称仅对自己展示',\n    },\n    plzEnterRightEnt: '请输入正确的企业名称',\n    goAuthenticate: '去认证',\n    keepLaunch: '继续发起',\n    enterprise: '企业',\n    person: '个人',\n    usercenter: '用户中心',\n    console: '企业控制台',\n    entAccount: '企业账号',\n    personAccount: '个人账号',\n    viewAllEnt: '查看全部企业',\n    addEnt: '新增企业',\n    createEnt: '创建企业',\n    exit: '退出',\n    viewDetail: '点击查看详情',\n    message: '消息',\n    datainsure: '数据保',\n    video: '操作视频',\n    help: '帮助',\n    dynamic: '产品动态',\n    contractManage: '合同管理',\n    templateManage: '模板管理',\n    statisticCharts: '统计报表',\n    authenticating: '认证中企业',\n    ecology: '来自{developerName}的企业账号',\n    ecologyPerson: '来自{developerName}的个人账号',\n    unAuthenticate: '未认证企业',\n    rejectAuthenticate: '实名已驳回',\n    contactManage: '请联系管理员给您分配权限',\n    noResult: '没有结果',\n    confirm: '确定',\n    cancel: '取消',\n    createSuccess: '创建成功',\n    notifyPhone: '提交通知手机',\n    phone: '手机',\n    phonePlaceholder: '请填写11位手机号',\n    verifyCode: '验证码',\n    sixNumberPlaceholder: '请填写6位数字',\n    submit: '提交',\n    phoneInputError: '请输入正确的手机号',\n    verCodeInputErr: '请输入正确的验证码',\n    setSuccess: '设置成功',\n    offlineContractManage: '线下合同管理',\n    phoneSetTip: '提交通知手机号，可用于接受系统发出的通知短信，如签署通知、签约验证码等。',\n    noMoreTip: '不再提醒',\n    phoneSetAfterTip: '（如需稍后设置，可在【用户中心】的【通知】里补充手机号）',\n    theEnterprise: '该企业',\n    you: '您',\n    passAutEntHint: '通过企业认证后显示企业名称',\n    passAutPersonHint: '通过实名认证后显示个人姓名',\n    createEntNow: '立即创建企业',\n\n    announcement: {\n        pwReset: '您的密码已过期，为了您的账户安全，请及时前往【用户中心】修改密码',\n    },\n    partAuthSearch: '发合同前查询下相对方企业实名情况？',\n};\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE,IAAI;EACVC,aAAa,EAAE,MAAM;EACrBC,kBAAkB,EAAE,QAAQ;EAC5BC,gBAAgB,EAAE,MAAM;EACxBC,eAAe,EAAE,SAAS;EAC1BC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,yBAAyB;EACjCC,cAAc,EAAE,QAAQ;EACxBC,aAAa,EAAE,MAAM;EACrBC,EAAE,EAAE,KAAK;EACTC,aAAa,EAAE,OAAO;EACtBC,MAAM,EAAE,IAAI;EACZC,WAAW,EAAE,MAAM;EACnBC,SAAS,EAAE,eAAe;EAC1BC,cAAc,EAAE,QAAQ;EACxBC,QAAQ,EAAE,MAAM;EAChBC,WAAW,EAAE,QAAQ;EACrBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,2CAA2C;EACxDC,IAAI,EAAE,2BAA2B;EACjCC,GAAG,EAAE,IAAI;EACTC,QAAQ,EAAE,mCAAmC;EAC7CC,gBAAgB,EAAE,8CAA8C;EAChEC,cAAc,EAAE;IACZC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,2BAA2B;IAC/BC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACR,CAAC;EACDC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE,KAAK;EACrBC,UAAU,EAAE,MAAM;EAClBC,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE,OAAO;EAChBC,UAAU,EAAE,MAAM;EAClBC,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,QAAQ;EACpBC,MAAM,EAAE,MAAM;EACdC,SAAS,EAAE,MAAM;EACjBC,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,MAAM;EACtBC,cAAc,EAAE,MAAM;EACtBC,eAAe,EAAE,MAAM;EACvBC,cAAc,EAAE,OAAO;EACvBC,OAAO,EAAE,wBAAwB;EACjCC,aAAa,EAAE,wBAAwB;EACvCC,cAAc,EAAE,OAAO;EACvBC,kBAAkB,EAAE,OAAO;EAC3BC,aAAa,EAAE,cAAc;EAC7BC,QAAQ,EAAE,MAAM;EAChBC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,aAAa,EAAE,MAAM;EACrBC,WAAW,EAAE,QAAQ;EACrBC,KAAK,EAAE,IAAI;EACXC,gBAAgB,EAAE,WAAW;EAC7BC,UAAU,EAAE,KAAK;EACjBC,oBAAoB,EAAE,SAAS;EAC/BC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE,WAAW;EAC5BC,eAAe,EAAE,WAAW;EAC5BC,UAAU,EAAE,MAAM;EAClBC,qBAAqB,EAAE,QAAQ;EAC/BC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,MAAM;EACjBC,gBAAgB,EAAE,8BAA8B;EAChDC,aAAa,EAAE,KAAK;EACpBC,GAAG,EAAE,GAAG;EACRC,cAAc,EAAE,eAAe;EAC/BC,iBAAiB,EAAE,eAAe;EAClCC,YAAY,EAAE,QAAQ;EAEtBC,YAAY,EAAE;IACVC,OAAO,EAAE;EACb,CAAC;EACDC,cAAc,EAAE;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}