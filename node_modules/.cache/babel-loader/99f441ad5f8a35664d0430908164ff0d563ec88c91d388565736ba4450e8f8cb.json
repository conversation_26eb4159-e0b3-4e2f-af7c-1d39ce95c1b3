{"ast": null, "code": "export default {\n  certificationRenewalDialog: {\n    renewalTitle: 'تجديد الشهادة الرقمية',\n    renewalTip: 'انتهت صلاحية شهادتك، لتجنب مشاكل توقيع المستندات، يرجى التجديد في الوقت المناسب',\n    renewalTip2: 'إذا تغير صاحب الشهادة لهذا الحساب، يرجى تغيير هوية الحساب',\n    previousIdentity: 'صاحب الشهادة:',\n    previousCA: 'مصدر الشهادة الأصلي:',\n    previousExpiryDate: 'تاريخ صلاحية الشهادة الأصلية:',\n    previousId: 'الرقم التسلسلي للشهادة الأصلية:',\n    renewal: 'موافقة على التجديد',\n    reject: 'المعلومات غير صحيحة، أريد التغيير',\n    rejectMessage: 'يجب رفض هوية {name} الحالية قبل متابعة تغيير هوية الحساب الحالي، هل تريد المتابعة؟',\n    rejectConfirm: 'تأكيد',\n    renewalTips1: 'لماذا تظهر هذه النافذة؟',\n    renewalTips2: 'لضمان القوة القانونية لعقودك الإلكترونية، تستخدم جميع العقود شهادتك الرقمية لإكمال التوقيع، وتترك التوقيع وختم الوقت كدليل في الملف النهائي. يمكن استخدام الشهادة الرقمية فقط خلال فترة صلاحيتها، وبناءً على المتطلبات التنظيمية، لن تقوم المنصة بتحديث شهادتك تلقائياً. إذا انتهت صلاحية شهادتك، يجب تحديثها قبل متابعة العمل.',\n    renewalTips3: 'للمزيد من التفاصيل، يمكنك مراجعة',\n    renewalTips4: '《اتفاقية استخدام الشهادة الرقمية》',\n    renewalTips5: 'من المنصة',\n    tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',\n    tip9: '复制链接',\n    tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',\n    tip11: '复制成功',\n    tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',\n    tip14: '去变更',\n    tip15: '关闭',\n    tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。'\n  },\n  infoProtectDialog: {\n    userAuth: 'استخدام خدمة التحقق من الوجه يتطلب الموافقة على',\n    titleWithSeperator: '《كيف تحمي المنصة معلوماتك الشخصية》',\n    title: 'كيف تحمي المنصة معلوماتك الشخصية',\n    auth: 'التحقق من الهوية',\n    faceSign: 'التحقق من التوقيع',\n    contentDesp: 'عند تقديم معلوماتك الشخصية (المشار إليها فيما يلي باسم \"المعلومات الشخصية\")، أنت تدرك وتوافق على:',\n    detailTip1: '(1) لإكمال {title} الخاص بك، لقد فوضت مؤسستك للقيام بنفسها أو تفويض المنصة لتقديم معلوماتك الشخصية إلى الجهات الأخرى التي تقدم خدمات لتحقيق التوقيع الإلكتروني (مثل هيئات CA والموثقين وغيرهم)؛',\n    detailTip2: '(2) بالإضافة إلى التفويض في (1)، أنت توافق بشكل منفصل على تقديم معلومات وجهك للتحقق من هوية {title} (أي التحقق من وجه الممثل القانوني)، وتوافق على أن تقوم المنصة فقط بمراجعة وتخزين واسترجاع ومشاركة معلومات وجهك لتوفير خدمة التوقيع الإلكتروني وإصدار الشهادات اللاحقة. إذا كنت لا توافق على محتوى هذا البند، يجب عليك التوقف فوراً عن تقديم معلومات وجهك واختيار طريقة تحقق أخرى؛',\n    detailTip3: '(3) باستثناء ما ورد في (1) و(2) والحالات التي ينص عليها القانون، لن تقدم المنصة معلوماتك الشخصية لأي طرف ثالث دون تفويض منك.',\n    know: 'فهمت'\n  },\n  signIdentityGuide: {\n    title: 'تنبيه',\n    requestYou: {\n      0: 'يطلب منك',\n      1: 'يطلب منك بصفة'\n    },\n    tipToAuth: {\n      0: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.',\n      1: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.'\n    },\n    needResend: 'عرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المتحقق منها. يرجى التواصل مع المرسل للتأكد من معلومات الهوية وطلب إعادة إرسال العقد.',\n    note: 'ملاحظة:',\n    entName: 'اسم المؤسسة',\n    identityInfo: 'معلومات الهوية',\n    signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً لتوقيع العقد',\n    inTheName: 'باسم',\n    of: 'من',\n    identity: 'الهوية',\n    nameIs: 'الاسم هو',\n    IDNumIs: 'رقم الهوية هو'\n  },\n  pdf: {\n    previewFail: 'فشل عرض الملف',\n    pager: 'الصفحة {x} من {y}',\n    parseFailed: 'فشل تحليل ملف PDF، يرجى النقر على \"تأكيد\" للمحاولة مرة أخرى',\n    confirm: 'تأكيد'\n  },\n  tagManage: {\n    title: 'إعداد العلامات'\n  },\n  dialogApplyJoinEnt: {\n    beenAuthenticated: 'تم التحقق من الهوية',\n    assignedIdentity: 'الطرف الموقع الذي حدده المرسل هو:',\n    entBeenAuthenticated: 'تم التحقق من هوية هذه المؤسسة، معلومات المدير الرئيسي كما يلي:',\n    entAdminName: 'اسم المدير:',\n    entAdminAccount: 'حساب المدير:',\n    applyToBeAdmin: 'أريد تقديم استئناف لأصبح المدير الرئيسي',\n    contactToJoin: 'التواصل مع المدير للانضمام للمؤسسة',\n    applicant: 'مقدم الطلب',\n    inputYourName: 'يرجى إدخال اسمك',\n    account: 'الحساب',\n    send: 'إرسال',\n    contract: 'العقد',\n    sendWishToJoin: 'يمكنك تقديم استئناف لتصبح مديراً عبر حسابك، أو إرسال طلب انضمام للمؤسسة إلى المدير',\n    applyToJoin: 'لم تنضم لهذه المؤسسة بعد، لا يمكنك توقيع {alias}، هل تريد تقديم طلب انضمام؟',\n    sentSuccessful: 'تم الإرسال بنجاح',\n    contractAlias: {\n      doc: 'مستند',\n      letter: 'خطاب استفسار',\n      proof: 'تفويض'\n    }\n  },\n  selectBizLine: {\n    title: 'يرجى اختيار خط الأعمال'\n  },\n  importOffLineDoc: {\n    importDoc: 'استيراد عقد',\n    step0Title: 'الخطوة الأولى: تأكيد اسم المؤسسة للاستيراد',\n    step1Title: 'الخطوة الثانية: رفع ملف Excel',\n    step2Title: 'الخطوة الثالثة: رفع ملفات العقود',\n    step1Info: 'يرجى تحميل نموذج Excel أولاً، ثم ملؤه ورفعه. لا يتجاوز عدد العقود 1000.',\n    next: 'التالي',\n    entName: 'اسم المؤسسة',\n    archiveFolder: 'مجلد الأرشفة',\n    downloadExcel: 'تحميل Excel',\n    uploadExcel: 'رفع Excel',\n    reUploadExcel: 'إعادة الرفع',\n    step2Info: ['1. يجب أن تكون ملفات العقود بصيغة PDF أو صور؛', '2. ضع جميع ملفات العقود في مجلد واحد، ثم اضغط المجلد كملف zip (لا يتجاوز 150 ميجابايت)؛', '3. يجب أن تتطابق أسماء الملفات مع الامتدادات (مثل .pdf) مع الأسماء المدخلة في ملف Excel في الخطوة الثانية؛'],\n    uploadZip: 'انقر لرفع ملف Zip',\n    reUploadZip: 'إعادة رفع Zip',\n    done: 'تأكيد',\n    back: 'رجوع',\n    contractTitle: 'اسم العقد',\n    singerAccount: 'حساب الموقع',\n    singerName: 'اسم الموقع',\n    uploadSucTip: 'تم الرفع بنجاح، انقر على زر \"تأكيد\" لبدء الاستيراد',\n    outbox: 'صندوق الصادر',\n    fileLessThan: 'يرجى رفع ملف أقل من {num} ميجابايت',\n    fileTypeValid: 'يمكن رفع ملفات بصيغة {type} فقط!'\n  },\n  download: {\n    contactGetDownloadCodeTip: 'يرجى التواصل مع مرسل العقد للحصول على رمز التحميل، أو محاولة تسجيل الدخول إلى نظام مؤسستك للتحميل.',\n    downloadCode: 'رمز التحميل',\n    hint: 'تنبيه',\n    download: 'تحميل',\n    plsInput: 'يرجى الإدخال',\n    plsInputDownloadCode: 'يرجى إدخال رمز التحميل',\n    downloadCodeError: 'رمز التحميل غير صحيح',\n    allFiles: 'جميع الملفات',\n    cancel: 'إلغاء',\n    plsSelectFiles: 'يرجى اختيار الملفات أولاً',\n    publicCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين، هل تريد تحميلها مع العقود؟',\n    hybridCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين.',\n    sameTimeDownloadAttachTip: 'تحميل مرفقات العقود في نفس الوقت',\n    downloadContract: 'تحميل العقد',\n    downloadAttach: 'تحميل مرفقات العقد'\n  },\n  commonHeader: {\n    groupCertification: 'تحقق المجموعة',\n    goHomePage: 'العودة للصفحة الرئيسية',\n    companyPrivateSaveTypeContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود وتم الاتصال بخادم تخزين العقود',\n    companyPrivateSaveTypeNoContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود ولا يمكن الاتصال بخادم تخزين العقود',\n    advise: 'اقتراحات:',\n    checkCompanyInteralNetContact: '① التحقق من إمكانية الوصول إلى شبكة المؤسسة الداخلية',\n    checkContactServerNetContact: '② التحقق من حالة تشغيل خادم تخزين العقود'\n  },\n  transfer: {\n    list1: 'القائمة 1',\n    list2: 'القائمة 2',\n    maxSelectNum: 'يمكن اختيار {maxLength} كحد أقصى'\n  },\n  poperCascader: {\n    plsSelect: 'يرجى الاختيار',\n    person: 'شخص',\n    selectNumTip: 'تم اختيار {A}/{B} {C}',\n    allSelect: 'اختيار الكل'\n  },\n  authInfoChange: {\n    title: 'الكشف عن تغيير معلومات الهوية',\n    confirm: 'تأكيد',\n    changeAuth: 'تحديث الهوية',\n    notifyAdmin: 'إخطار المدير',\n    notifySuccess: 'تم الإخطار بنجاح',\n    operateSuccess: 'تمت العملية بنجاح',\n    warningTip: {\n      tip1: 'بعد المراجعة، تبين أن معلومات هوية مؤسستك \"{entName}\" على المنصة {oldAuthInfo} لا تتطابق مع أحدث معلومات السجل التجاري {newAuthInfo}.',\n      tip2: 'لضمان قانونية وفعالية عقودك الإلكترونية، يرجى إعادة التحقق من الهوية باستخدام أحدث معلومات المؤسسة.',\n      tip3: 'لن يؤدي هذا الإجراء إلى رفض معلومات مؤسستك الحالية.'\n    },\n    suggestTip: {\n      tip1: 'إذا كانت مؤسستك جزءاً من مجموعة، يرجى التواصل مع مدير العلاقات المخصص لك، أو الاتصال بخدمة العملاء على الرقم 400-993-6665 للمساعدة في تحديث معلومات التحقق من الهوية. يمكنك متابعة التوقيع بعد التحديث.',\n      tip2: 'انقر على [إخطار المدير {adminInfo}]،',\n      tip3: 'لإرسال إشعار فوري للمدير لتوجيهه لإعادة التحقق من الهوية. يمكنك أيضاً الإخطار شخصياً لتسريع العمل.'\n    }\n  }\n};", "map": {"version": 3, "names": ["certificationRenewalDialog", "renewalTitle", "renewalTip", "renewalTip2", "previousIdentity", "previousCA", "previousExpiryDate", "previousId", "renewal", "reject", "rejectMessage", "rejectConfirm", "renewalTips1", "renewalTips2", "renewalTips3", "renewalTips4", "renewalTips5", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "tip15", "tip16", "tip17", "infoProtectDialog", "userAuth", "titleWithSeperator", "title", "auth", "faceSign", "contentDesp", "detailTip1", "detailTip2", "detailTip3", "know", "signIdentityGuide", "requestYou", "tipToAuth", "needResend", "note", "entName", "identityInfo", "signNeedCoincidenceInfo", "inTheName", "of", "identity", "nameIs", "IDNumIs", "pdf", "previewFail", "pager", "parseFailed", "confirm", "tagManage", "dialogApplyJoinEnt", "beenAuthenticated", "assignedIdentity", "entBeenAuthenticated", "entAdminName", "entAdminAccount", "applyToBeAdmin", "contactToJoin", "applicant", "inputYourName", "account", "send", "contract", "sendWishToJoin", "apply<PERSON>oJoin", "sentSuccessful", "contractAlias", "doc", "letter", "proof", "selectBizLine", "importOffLineDoc", "importDoc", "step0Title", "step1Title", "step2Title", "step1Info", "next", "archiveFolder", "downloadExcel", "uploadExcel", "reUploadExcel", "step2Info", "uploadZip", "reUploadZip", "done", "back", "contractTitle", "singerA<PERSON>unt", "<PERSON><PERSON><PERSON>", "uploadSucTip", "outbox", "fileLessThan", "fileTypeValid", "download", "contactGetDownloadCodeTip", "downloadCode", "hint", "plsInput", "plsInputDownloadCode", "downloadCodeError", "allFiles", "cancel", "plsSelectFiles", "publicCloudDownloadTip", "hybridCloudDownloadTip", "sameTimeDownloadAttachTip", "downloadContract", "downloadAttach", "common<PERSON>eader", "groupCertification", "goHomePage", "companyPrivateSaveTypeContactTip", "companyPrivateSaveTypeNoContactTip", "advise", "checkCompanyInteralNetContact", "checkContactServerNetContact", "transfer", "list1", "list2", "maxSelectNum", "poperCascader", "plsSelect", "person", "selectNumTip", "allSelect", "authInfoChange", "changeAuth", "notifyAdmin", "notifySuccess", "operateSuccess", "warningTip", "tip1", "tip2", "tip3", "suggestTip"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/components/components-ar.js"], "sourcesContent": ["export default {\n    certificationRenewalDialog: {\n        renewalTitle: 'تجديد الشهادة الرقمية',\n        renewalTip: 'انتهت صلاحية شهادتك، لتجنب مشاكل توقيع المستندات، يرجى التجديد في الوقت المناسب',\n        renewalTip2: 'إذا تغير صاحب الشهادة لهذا الحساب، يرجى تغيير هوية الحساب',\n        previousIdentity: 'صاحب الشهادة:',\n        previousCA: 'مصدر الشهادة الأصلي:',\n        previousExpiryDate: 'تاريخ صلاحية الشهادة الأصلية:',\n        previousId: 'الرقم التسلسلي للشهادة الأصلية:',\n        renewal: 'موافقة على التجديد',\n        reject: 'المعلومات غير صحيحة، أريد التغيير',\n        rejectMessage: 'يجب رفض هوية {name} الحالية قبل متابعة تغيير هوية الحساب الحالي، هل تريد المتابعة؟',\n        rejectConfirm: 'تأكيد',\n        renewalTips1: 'لماذا تظهر هذه النافذة؟',\n        renewalTips2: 'لضمان القوة القانونية لعقودك الإلكترونية، تستخدم جميع العقود شهادتك الرقمية لإكمال التوقيع، وتترك التوقيع وختم الوقت كدليل في الملف النهائي. يمكن استخدام الشهادة الرقمية فقط خلال فترة صلاحيتها، وبناءً على المتطلبات التنظيمية، لن تقوم المنصة بتحديث شهادتك تلقائياً. إذا انتهت صلاحية شهادتك، يجب تحديثها قبل متابعة العمل.',\n        renewalTips3: 'للمزيد من التفاصيل، يمكنك مراجعة',\n        renewalTips4: '《اتفاقية استخدام الشهادة الرقمية》',\n        renewalTips5: 'من المنصة',\n        tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',\n        tip9: '复制链接',\n        tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',\n        tip11: '复制成功',\n        tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',\n        tip14: '去变更',\n        tip15: '关闭',\n        tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。',\n    },\n    infoProtectDialog: {\n        userAuth: 'استخدام خدمة التحقق من الوجه يتطلب الموافقة على',\n        titleWithSeperator: '《كيف تحمي المنصة معلوماتك الشخصية》',\n        title: 'كيف تحمي المنصة معلوماتك الشخصية',\n        auth: 'التحقق من الهوية',\n        faceSign: 'التحقق من التوقيع',\n        contentDesp: 'عند تقديم معلوماتك الشخصية (المشار إليها فيما يلي باسم \"المعلومات الشخصية\")، أنت تدرك وتوافق على:',\n        detailTip1: '(1) لإكمال {title} الخاص بك، لقد فوضت مؤسستك للقيام بنفسها أو تفويض المنصة لتقديم معلوماتك الشخصية إلى الجهات الأخرى التي تقدم خدمات لتحقيق التوقيع الإلكتروني (مثل هيئات CA والموثقين وغيرهم)؛',\n        detailTip2: '(2) بالإضافة إلى التفويض في (1)، أنت توافق بشكل منفصل على تقديم معلومات وجهك للتحقق من هوية {title} (أي التحقق من وجه الممثل القانوني)، وتوافق على أن تقوم المنصة فقط بمراجعة وتخزين واسترجاع ومشاركة معلومات وجهك لتوفير خدمة التوقيع الإلكتروني وإصدار الشهادات اللاحقة. إذا كنت لا توافق على محتوى هذا البند، يجب عليك التوقف فوراً عن تقديم معلومات وجهك واختيار طريقة تحقق أخرى؛',\n        detailTip3: '(3) باستثناء ما ورد في (1) و(2) والحالات التي ينص عليها القانون، لن تقدم المنصة معلوماتك الشخصية لأي طرف ثالث دون تفويض منك.',\n        know: 'فهمت',\n    },\n    signIdentityGuide: {\n        title: 'تنبيه',\n        requestYou: {\n            0: 'يطلب منك',\n            1: 'يطلب منك بصفة',\n        },\n        tipToAuth: {\n            0: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.',\n            1: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.',\n        },\n        needResend: 'عرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المتحقق منها. يرجى التواصل مع المرسل للتأكد من معلومات الهوية وطلب إعادة إرسال العقد.',\n        note: 'ملاحظة:',\n        entName: 'اسم المؤسسة',\n        identityInfo: 'معلومات الهوية',\n        signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً لتوقيع العقد',\n        inTheName: 'باسم',\n        of: 'من',\n        identity: 'الهوية',\n        nameIs: 'الاسم هو',\n        IDNumIs: 'رقم الهوية هو',\n    },\n    pdf: {\n        previewFail: 'فشل عرض الملف',\n        pager: 'الصفحة {x} من {y}',\n        parseFailed: 'فشل تحليل ملف PDF، يرجى النقر على \"تأكيد\" للمحاولة مرة أخرى',\n        confirm: 'تأكيد',\n    },\n    tagManage: {\n        title: 'إعداد العلامات',\n    },\n    dialogApplyJoinEnt: {\n        beenAuthenticated: 'تم التحقق من الهوية',\n        assignedIdentity: 'الطرف الموقع الذي حدده المرسل هو:',\n        entBeenAuthenticated: 'تم التحقق من هوية هذه المؤسسة، معلومات المدير الرئيسي كما يلي:',\n        entAdminName: 'اسم المدير:',\n        entAdminAccount: 'حساب المدير:',\n        applyToBeAdmin: 'أريد تقديم استئناف لأصبح المدير الرئيسي',\n        contactToJoin: 'التواصل مع المدير للانضمام للمؤسسة',\n        applicant: 'مقدم الطلب',\n        inputYourName: 'يرجى إدخال اسمك',\n        account: 'الحساب',\n        send: 'إرسال',\n        contract: 'العقد',\n        sendWishToJoin: 'يمكنك تقديم استئناف لتصبح مديراً عبر حسابك، أو إرسال طلب انضمام للمؤسسة إلى المدير',\n        applyToJoin: 'لم تنضم لهذه المؤسسة بعد، لا يمكنك توقيع {alias}، هل تريد تقديم طلب انضمام؟',\n        sentSuccessful: 'تم الإرسال بنجاح',\n        contractAlias: {\n            doc: 'مستند',\n            letter: 'خطاب استفسار',\n            proof: 'تفويض',\n        },\n    },\n    selectBizLine: {\n        title: 'يرجى اختيار خط الأعمال',\n    },\n    importOffLineDoc: {\n        importDoc: 'استيراد عقد',\n        step0Title: 'الخطوة الأولى: تأكيد اسم المؤسسة للاستيراد',\n        step1Title: 'الخطوة الثانية: رفع ملف Excel',\n        step2Title: 'الخطوة الثالثة: رفع ملفات العقود',\n        step1Info: 'يرجى تحميل نموذج Excel أولاً، ثم ملؤه ورفعه. لا يتجاوز عدد العقود 1000.',\n        next: 'التالي',\n        entName: 'اسم المؤسسة',\n        archiveFolder: 'مجلد الأرشفة',\n        downloadExcel: 'تحميل Excel',\n        uploadExcel: 'رفع Excel',\n        reUploadExcel: 'إعادة الرفع',\n        step2Info: ['1. يجب أن تكون ملفات العقود بصيغة PDF أو صور؛', '2. ضع جميع ملفات العقود في مجلد واحد، ثم اضغط المجلد كملف zip (لا يتجاوز 150 ميجابايت)؛', '3. يجب أن تتطابق أسماء الملفات مع الامتدادات (مثل .pdf) مع الأسماء المدخلة في ملف Excel في الخطوة الثانية؛'],\n        uploadZip: 'انقر لرفع ملف Zip',\n        reUploadZip: 'إعادة رفع Zip',\n        done: 'تأكيد',\n        back: 'رجوع',\n        contractTitle: 'اسم العقد',\n        singerAccount: 'حساب الموقع',\n        singerName: 'اسم الموقع',\n        uploadSucTip: 'تم الرفع بنجاح، انقر على زر \"تأكيد\" لبدء الاستيراد',\n        outbox: 'صندوق الصادر',\n        fileLessThan: 'يرجى رفع ملف أقل من {num} ميجابايت',\n        fileTypeValid: 'يمكن رفع ملفات بصيغة {type} فقط!',\n    },\n    download: {\n        contactGetDownloadCodeTip: 'يرجى التواصل مع مرسل العقد للحصول على رمز التحميل، أو محاولة تسجيل الدخول إلى نظام مؤسستك للتحميل.',\n        downloadCode: 'رمز التحميل',\n        hint: 'تنبيه',\n        download: 'تحميل',\n        plsInput: 'يرجى الإدخال',\n        plsInputDownloadCode: 'يرجى إدخال رمز التحميل',\n        downloadCodeError: 'رمز التحميل غير صحيح',\n        allFiles: 'جميع الملفات',\n        cancel: 'إلغاء',\n        plsSelectFiles: 'يرجى اختيار الملفات أولاً',\n        publicCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين، هل تريد تحميلها مع العقود؟',\n        hybridCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين.',\n        sameTimeDownloadAttachTip: 'تحميل مرفقات العقود في نفس الوقت',\n        downloadContract: 'تحميل العقد',\n        downloadAttach: 'تحميل مرفقات العقد',\n    },\n    commonHeader: {\n        groupCertification: 'تحقق المجموعة',\n        goHomePage: 'العودة للصفحة الرئيسية',\n        companyPrivateSaveTypeContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود وتم الاتصال بخادم تخزين العقود',\n        companyPrivateSaveTypeNoContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود ولا يمكن الاتصال بخادم تخزين العقود',\n        advise: 'اقتراحات:',\n        checkCompanyInteralNetContact: '① التحقق من إمكانية الوصول إلى شبكة المؤسسة الداخلية',\n        checkContactServerNetContact: '② التحقق من حالة تشغيل خادم تخزين العقود',\n    },\n    transfer: {\n        list1: 'القائمة 1',\n        list2: 'القائمة 2',\n        maxSelectNum: 'يمكن اختيار {maxLength} كحد أقصى',\n    },\n    poperCascader: {\n        plsSelect: 'يرجى الاختيار',\n        person: 'شخص',\n        selectNumTip: 'تم اختيار {A}/{B} {C}',\n        allSelect: 'اختيار الكل',\n    },\n    authInfoChange: {\n        title: 'الكشف عن تغيير معلومات الهوية',\n        confirm: 'تأكيد',\n        changeAuth: 'تحديث الهوية',\n        notifyAdmin: 'إخطار المدير',\n        notifySuccess: 'تم الإخطار بنجاح',\n        operateSuccess: 'تمت العملية بنجاح',\n        warningTip: {\n            tip1: 'بعد المراجعة، تبين أن معلومات هوية مؤسستك \"{entName}\" على المنصة {oldAuthInfo} لا تتطابق مع أحدث معلومات السجل التجاري {newAuthInfo}.',\n            tip2: 'لضمان قانونية وفعالية عقودك الإلكترونية، يرجى إعادة التحقق من الهوية باستخدام أحدث معلومات المؤسسة.',\n            tip3: 'لن يؤدي هذا الإجراء إلى رفض معلومات مؤسستك الحالية.',\n        },\n        suggestTip: {\n            tip1: 'إذا كانت مؤسستك جزءاً من مجموعة، يرجى التواصل مع مدير العلاقات المخصص لك، أو الاتصال بخدمة العملاء على الرقم 400-993-6665 للمساعدة في تحديث معلومات التحقق من الهوية. يمكنك متابعة التوقيع بعد التحديث.',\n            tip2: 'انقر على [إخطار المدير {adminInfo}]،',\n            tip3: 'لإرسال إشعار فوري للمدير لتوجيهه لإعادة التحقق من الهوية. يمكنك أيضاً الإخطار شخصياً لتسريع العمل.',\n        },\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,0BAA0B,EAAE;IACxBC,YAAY,EAAE,uBAAuB;IACrCC,UAAU,EAAE,iFAAiF;IAC7FC,WAAW,EAAE,2DAA2D;IACxEC,gBAAgB,EAAE,eAAe;IACjCC,UAAU,EAAE,sBAAsB;IAClCC,kBAAkB,EAAE,+BAA+B;IACnDC,UAAU,EAAE,iCAAiC;IAC7CC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EAAE,mCAAmC;IAC3CC,aAAa,EAAE,oFAAoF;IACnGC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,yBAAyB;IACvCC,YAAY,EAAE,iUAAiU;IAC/UC,YAAY,EAAE,kCAAkC;IAChDC,YAAY,EAAE,mCAAmC;IACjDC,YAAY,EAAE,WAAW;IACzBC,IAAI,EAAE,kEAAkE;IACxEC,IAAI,EAAE,yEAAyE;IAC/EC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yEAAyE;IAChFC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,kEAAkE;IACzEC,KAAK,EAAE,wEAAwE;IAC/EC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,kEAAkE;IACzEC,KAAK,EAAE;EACX,CAAC;EACDC,iBAAiB,EAAE;IACfC,QAAQ,EAAE,iDAAiD;IAC3DC,kBAAkB,EAAE,oCAAoC;IACxDC,KAAK,EAAE,kCAAkC;IACzCC,IAAI,EAAE,kBAAkB;IACxBC,QAAQ,EAAE,mBAAmB;IAC7BC,WAAW,EAAE,mGAAmG;IAChHC,UAAU,EAAE,iMAAiM;IAC7MC,UAAU,EAAE,uXAAuX;IACnYC,UAAU,EAAE,8HAA8H;IAC1IC,IAAI,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACfR,KAAK,EAAE,OAAO;IACdS,UAAU,EAAE;MACR,CAAC,EAAE,UAAU;MACb,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE;MACP,CAAC,EAAE,kEAAkE;MACrE,CAAC,EAAE;IACP,CAAC;IACDC,UAAU,EAAE,wIAAwI;IACpJC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,aAAa;IACtBC,YAAY,EAAE,gBAAgB;IAC9BC,uBAAuB,EAAE,mCAAmC;IAC5DC,SAAS,EAAE,MAAM;IACjBC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE;EACb,CAAC;EACDC,GAAG,EAAE;IACDC,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,6DAA6D;IAC1EC,OAAO,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACP1B,KAAK,EAAE;EACX,CAAC;EACD2B,kBAAkB,EAAE;IAChBC,iBAAiB,EAAE,qBAAqB;IACxCC,gBAAgB,EAAE,mCAAmC;IACrDC,oBAAoB,EAAE,gEAAgE;IACtFC,YAAY,EAAE,aAAa;IAC3BC,eAAe,EAAE,cAAc;IAC/BC,cAAc,EAAE,yCAAyC;IACzDC,aAAa,EAAE,oCAAoC;IACnDC,SAAS,EAAE,YAAY;IACvBC,aAAa,EAAE,iBAAiB;IAChCC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,OAAO;IACjBC,cAAc,EAAE,oFAAoF;IACpGC,WAAW,EAAE,6EAA6E;IAC1FC,cAAc,EAAE,kBAAkB;IAClCC,aAAa,EAAE;MACXC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,cAAc;MACtBC,KAAK,EAAE;IACX;EACJ,CAAC;EACDC,aAAa,EAAE;IACX/C,KAAK,EAAE;EACX,CAAC;EACDgD,gBAAgB,EAAE;IACdC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,4CAA4C;IACxDC,UAAU,EAAE,+BAA+B;IAC3CC,UAAU,EAAE,kCAAkC;IAC9CC,SAAS,EAAE,yEAAyE;IACpFC,IAAI,EAAE,QAAQ;IACdzC,OAAO,EAAE,aAAa;IACtB0C,aAAa,EAAE,cAAc;IAC7BC,aAAa,EAAE,aAAa;IAC5BC,WAAW,EAAE,WAAW;IACxBC,aAAa,EAAE,aAAa;IAC5BC,SAAS,EAAE,CAAC,+CAA+C,EAAE,yFAAyF,EAAE,4GAA4G,CAAC;IACrQC,SAAS,EAAE,mBAAmB;IAC9BC,WAAW,EAAE,eAAe;IAC5BC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,aAAa;IAC5BC,UAAU,EAAE,YAAY;IACxBC,YAAY,EAAE,oDAAoD;IAClEC,MAAM,EAAE,cAAc;IACtBC,YAAY,EAAE,oCAAoC;IAClDC,aAAa,EAAE;EACnB,CAAC;EACDC,QAAQ,EAAE;IACNC,yBAAyB,EAAE,oGAAoG;IAC/HC,YAAY,EAAE,aAAa;IAC3BC,IAAI,EAAE,OAAO;IACbH,QAAQ,EAAE,OAAO;IACjBI,QAAQ,EAAE,cAAc;IACxBC,oBAAoB,EAAE,wBAAwB;IAC9CC,iBAAiB,EAAE,sBAAsB;IACzCC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,OAAO;IACfC,cAAc,EAAE,2BAA2B;IAC3CC,sBAAsB,EAAE,kGAAkG;IAC1HC,sBAAsB,EAAE,uEAAuE;IAC/FC,yBAAyB,EAAE,kCAAkC;IAC7DC,gBAAgB,EAAE,aAAa;IAC/BC,cAAc,EAAE;EACpB,CAAC;EACDC,YAAY,EAAE;IACVC,kBAAkB,EAAE,eAAe;IACnCC,UAAU,EAAE,wBAAwB;IACpCC,gCAAgC,EAAE,mEAAmE;IACrGC,kCAAkC,EAAE,wEAAwE;IAC5GC,MAAM,EAAE,WAAW;IACnBC,6BAA6B,EAAE,sDAAsD;IACrFC,4BAA4B,EAAE;EAClC,CAAC;EACDC,QAAQ,EAAE;IACNC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE;EAClB,CAAC;EACDC,aAAa,EAAE;IACXC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,uBAAuB;IACrCC,SAAS,EAAE;EACf,CAAC;EACDC,cAAc,EAAE;IACZvG,KAAK,EAAE,+BAA+B;IACtCyB,OAAO,EAAE,OAAO;IAChB+E,UAAU,EAAE,cAAc;IAC1BC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAE,kBAAkB;IACjCC,cAAc,EAAE,mBAAmB;IACnCC,UAAU,EAAE;MACRC,IAAI,EAAE,uIAAuI;MAC7IC,IAAI,EAAE,qGAAqG;MAC3GC,IAAI,EAAE;IACV,CAAC;IACDC,UAAU,EAAE;MACRH,IAAI,EAAE,yMAAyM;MAC/MC,IAAI,EAAE,sCAAsC;MAC5CC,IAAI,EAAE;IACV;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}