{"ast": null, "code": "export default {\n  certificationRenewalDialog: {\n    renewalTitle: '数字证书续期',\n    renewalTip: '您的证书已过期，为避免文件签署无法正常进行，请及时续期',\n    renewalTip2: '若该账号持有证书的主体已变更，请您变更账号实名',\n    previousIdentity: '持有证书的主体：',\n    previousCA: '原证书颁发机构：',\n    previousExpiryDate: '原证书有效期：',\n    previousId: '原证书序列号：',\n    renewal: '同意续期',\n    reject: '信息有误，我要变更',\n    rejectMessage: '需要驳回当前{name}的实名后，您才可以继续变更当前账号实名，是否继续？',\n    rejectConfirm: '是的，我要驳回实名',\n    tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',\n    tip9: '复制链接',\n    tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',\n    tip11: '复制成功',\n    tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',\n    tip14: '去变更',\n    tip15: '关闭',\n    tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n    tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。'\n  },\n  infoProtectDialog: {\n    userAuth: '使用刷脸服务须同意',\n    titleWithSeperator: '《上上签如何保护您的个人信息》',\n    title: '上上签如何保护您的个人信息',\n    auth: '实名认证',\n    faceSign: '刷脸签署',\n    contentDesp: '您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：',\n    detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',\n    detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',\n    detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',\n    know: '知道了'\n  },\n  signIdentityGuide: {\n    title: '提示',\n    requestYou: {\n      0: '要求您',\n      1: '要求您以'\n    },\n    tipToAuth: {\n      0: '的身份进行实名认证，完成认证后即可查看和签署合同。',\n      1: '进行实名认证，完成认证后即可查看和签署合同。'\n    },\n    needResend: '进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',\n    note: '注：',\n    entName: '企业名称',\n    identityInfo: '身份信息',\n    signNeedCoincidenceInfo: '完全一致才能签署合同',\n    inTheName: '以',\n    of: '的',\n    identity: '身份',\n    nameIs: '姓名为',\n    IDNumIs: '身份证号为'\n  },\n  pdf: {\n    previewFail: '文件预览失败',\n    pager: '第{x}页，共{y}页',\n    parseFailed: '解析pdf文件失败，请点击“确定”重试',\n    confirm: '确定'\n  },\n  tagManage: {\n    title: '设置标签'\n  },\n  dialogApplyJoinEnt: {\n    beenAuthenticated: '已被实名',\n    assignedIdentity: '发件方填写的签约主体为：',\n    entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',\n    entAdminName: '管理员姓名：',\n    entAdminAccount: '管理员账号：',\n    applyToBeAdmin: '我要申诉成为主管理员',\n    contactToJoin: '联系管理员加入企业',\n    applicant: '申请人',\n    inputYourName: '请输入您的姓名',\n    account: '账号',\n    send: '发送',\n    sendWishToJoin: '您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请',\n    applyToJoin: '您还未加入该企业，无法签署该合同，是否要申请加入？',\n    sentSuccessful: '发送成功',\n    contractAlias: {\n      doc: '文件',\n      letter: '询征函',\n      proof: '授权书'\n    }\n  },\n  selectBizLine: {\n    title: '请选择业务线'\n  },\n  importOffLineDoc: {\n    importDoc: '导入合同',\n    step0Title: '第一步：确认导入企业名称',\n    step1Title: '第二步：上传Excel',\n    step2Title: '第三步：上传合同文件',\n    step1Info: '请先下载Excel模板，填写完成后再导入,合同数量不超过1000。',\n    next: '下一步',\n    entName: '企业名称',\n    archiveFolder: '归档文件夹',\n    downloadExcel: '下载Excel',\n    uploadExcel: '上传Excel',\n    reUploadExcel: '重新上传',\n    step2Info: ['1. 合同文件只能是PDF或图片；', '2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；', '3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；'],\n    uploadZip: '点击上传Zip',\n    reUploadZip: '重新上传Zip',\n    done: '确定',\n    back: '返回',\n    contractTitle: '合同名称',\n    singerAccount: '签署人账号',\n    singerName: '签署人名称',\n    uploadSucTip: '上传成功，点击\"确定\"按钮开始导入',\n    outbox: 'Исходящий',\n    fileLessThan: '请上传小于{num}M的文件',\n    fileTypeValid: '只能上传{type}格式的文件!'\n  },\n  authInfoChange: {\n    title: '实名信息变更检测',\n    confirm: '确定',\n    notifyAdmin: '通知管理员',\n    notifySuccess: '通知成功',\n    operateSuccess: '操作成功',\n    warningTip: {\n      tip1: '系统检测到您已在上上签平台实名，但使用的企业信息不是您最新的工商备案信息。',\n      tip2: '为保证您签署的电子合同合规性和效力，请使用最新的企业信息进行重新实名。'\n    },\n    suggestTip: {\n      tip1: '如您企业为集团架构，请联系您的专属CSM、或者拨打上上签客服热线400-993-6665帮助您完成实名认证信息的更新。更新后，方可继续签署。',\n      tip2: '点击【通知管理员{adminInfo}】，',\n      tip3: '可以立即发送通知给管理员，引导管理员去重新实名。您也可线下通知，及时推动业务开展。'\n    }\n  }\n};", "map": {"version": 3, "names": ["certificationRenewalDialog", "renewalTitle", "renewalTip", "renewalTip2", "previousIdentity", "previousCA", "previousExpiryDate", "previousId", "renewal", "reject", "rejectMessage", "rejectConfirm", "tip7", "tip8", "tip9", "tip10", "tip11", "tip12", "tip13", "tip14", "tip15", "tip16", "tip17", "infoProtectDialog", "userAuth", "titleWithSeperator", "title", "auth", "faceSign", "contentDesp", "detailTip1", "detailTip2", "detailTip3", "know", "signIdentityGuide", "requestYou", "tipToAuth", "needResend", "note", "entName", "identityInfo", "signNeedCoincidenceInfo", "inTheName", "of", "identity", "nameIs", "IDNumIs", "pdf", "previewFail", "pager", "parseFailed", "confirm", "tagManage", "dialogApplyJoinEnt", "beenAuthenticated", "assignedIdentity", "entBeenAuthenticated", "entAdminName", "entAdminAccount", "applyToBeAdmin", "contactToJoin", "applicant", "inputYourName", "account", "send", "sendWishToJoin", "apply<PERSON>oJoin", "sentSuccessful", "contractAlias", "doc", "letter", "proof", "selectBizLine", "importOffLineDoc", "importDoc", "step0Title", "step1Title", "step2Title", "step1Info", "next", "archiveFolder", "downloadExcel", "uploadExcel", "reUploadExcel", "step2Info", "uploadZip", "reUploadZip", "done", "back", "contractTitle", "singerA<PERSON>unt", "<PERSON><PERSON><PERSON>", "uploadSucTip", "outbox", "fileLessThan", "fileTypeValid", "authInfoChange", "notifyAdmin", "notifySuccess", "operateSuccess", "warningTip", "tip1", "tip2", "suggestTip", "tip3"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/components/components-ru.js"], "sourcesContent": ["export default {\n    certificationRenewalDialog: {\n        renewalTitle: '数字证书续期',\n        renewalTip: '您的证书已过期，为避免文件签署无法正常进行，请及时续期',\n        renewalTip2: '若该账号持有证书的主体已变更，请您变更账号实名',\n        previousIdentity: '持有证书的主体：',\n        previousCA: '原证书颁发机构：',\n        previousExpiryDate: '原证书有效期：',\n        previousId: '原证书序列号：',\n        renewal: '同意续期',\n        reject: '信息有误，我要变更',\n        rejectMessage: '需要驳回当前{name}的实名后，您才可以继续变更当前账号实名，是否继续？',\n        rejectConfirm: '是的，我要驳回实名',\n        tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',\n        tip9: '复制链接',\n        tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',\n        tip11: '复制成功',\n        tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',\n        tip14: '去变更',\n        tip15: '关闭',\n        tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',\n        tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。',\n    },\n    infoProtectDialog: {\n        userAuth: '使用刷脸服务须同意',\n        titleWithSeperator: '《上上签如何保护您的个人信息》',\n        title: '上上签如何保护您的个人信息',\n        auth: '实名认证',\n        faceSign: '刷脸签署',\n        contentDesp: '您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：',\n        detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',\n        detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',\n        detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',\n        know: '知道了',\n    },\n    signIdentityGuide: {\n        title: '提示',\n        requestYou: {\n            0: '要求您',\n            1: '要求您以',\n        },\n        tipToAuth: {\n            0: '的身份进行实名认证，完成认证后即可查看和签署合同。',\n            1: '进行实名认证，完成认证后即可查看和签署合同。',\n        },\n        needResend: '进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',\n        note: '注：',\n        entName: '企业名称',\n        identityInfo: '身份信息',\n        signNeedCoincidenceInfo: '完全一致才能签署合同',\n        inTheName: '以',\n        of: '的',\n        identity: '身份',\n        nameIs: '姓名为',\n        IDNumIs: '身份证号为',\n    },\n    pdf: {\n        previewFail: '文件预览失败',\n        pager: '第{x}页，共{y}页',\n        parseFailed: '解析pdf文件失败，请点击“确定”重试',\n        confirm: '确定',\n    },\n    tagManage: {\n        title: '设置标签',\n    },\n    dialogApplyJoinEnt: {\n        beenAuthenticated: '已被实名',\n        assignedIdentity: '发件方填写的签约主体为：',\n        entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',\n        entAdminName: '管理员姓名：',\n        entAdminAccount: '管理员账号：',\n        applyToBeAdmin: '我要申诉成为主管理员',\n        contactToJoin: '联系管理员加入企业',\n        applicant: '申请人',\n        inputYourName: '请输入您的姓名',\n        account: '账号',\n        send: '发送',\n        sendWishToJoin: '您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请',\n        applyToJoin: '您还未加入该企业，无法签署该合同，是否要申请加入？',\n        sentSuccessful: '发送成功',\n        contractAlias: {\n            doc: '文件',\n            letter: '询征函',\n            proof: '授权书',\n        },\n    },\n    selectBizLine: {\n        title: '请选择业务线',\n    },\n    importOffLineDoc: {\n        importDoc: '导入合同',\n        step0Title: '第一步：确认导入企业名称',\n        step1Title: '第二步：上传Excel',\n        step2Title: '第三步：上传合同文件',\n        step1Info: '请先下载Excel模板，填写完成后再导入,合同数量不超过1000。',\n        next: '下一步',\n        entName: '企业名称',\n        archiveFolder: '归档文件夹',\n        downloadExcel: '下载Excel',\n        uploadExcel: '上传Excel',\n        reUploadExcel: '重新上传',\n        step2Info: ['1. 合同文件只能是PDF或图片；', '2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；', '3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；'],\n        uploadZip: '点击上传Zip',\n        reUploadZip: '重新上传Zip',\n        done: '确定',\n        back: '返回',\n        contractTitle: '合同名称',\n        singerAccount: '签署人账号',\n        singerName: '签署人名称',\n        uploadSucTip: '上传成功，点击\"确定\"按钮开始导入',\n        outbox: 'Исходящий',\n        fileLessThan: '请上传小于{num}M的文件',\n        fileTypeValid: '只能上传{type}格式的文件!',\n    },\n    authInfoChange: {\n        title: '实名信息变更检测',\n        confirm: '确定',\n        notifyAdmin: '通知管理员',\n        notifySuccess: '通知成功',\n        operateSuccess: '操作成功',\n        warningTip: {\n            tip1: '系统检测到您已在上上签平台实名，但使用的企业信息不是您最新的工商备案信息。',\n            tip2: '为保证您签署的电子合同合规性和效力，请使用最新的企业信息进行重新实名。',\n        },\n        suggestTip: {\n            tip1: '如您企业为集团架构，请联系您的专属CSM、或者拨打上上签客服热线400-993-6665帮助您完成实名认证信息的更新。更新后，方可继续签署。',\n            tip2: '点击【通知管理员{adminInfo}】，',\n            tip3: '可以立即发送通知给管理员，引导管理员去重新实名。您也可线下通知，及时推动业务开展。',\n        },\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,0BAA0B,EAAE;IACxBC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,6BAA6B;IACzCC,WAAW,EAAE,yBAAyB;IACtCC,gBAAgB,EAAE,UAAU;IAC5BC,UAAU,EAAE,UAAU;IACtBC,kBAAkB,EAAE,SAAS;IAC7BC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE,uCAAuC;IACtDC,aAAa,EAAE,WAAW;IAC1BC,IAAI,EAAE,kEAAkE;IACxEC,IAAI,EAAE,yEAAyE;IAC/EC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yEAAyE;IAChFC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,kEAAkE;IACzEC,KAAK,EAAE,wEAAwE;IAC/EC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,kEAAkE;IACzEC,KAAK,EAAE;EACX,CAAC;EACDC,iBAAiB,EAAE;IACfC,QAAQ,EAAE,WAAW;IACrBC,kBAAkB,EAAE,iBAAiB;IACrCC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,mCAAmC;IAChDC,UAAU,EAAE,kFAAkF;IAC9FC,UAAU,EAAE,gJAAgJ;IAC5JC,UAAU,EAAE,uDAAuD;IACnEC,IAAI,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE;IACfR,KAAK,EAAE,IAAI;IACXS,UAAU,EAAE;MACR,CAAC,EAAE,KAAK;MACR,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,EAAE;MACP,CAAC,EAAE,2BAA2B;MAC9B,CAAC,EAAE;IACP,CAAC;IACDC,UAAU,EAAE,mDAAmD;IAC/DC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,MAAM;IACpBC,uBAAuB,EAAE,YAAY;IACrCC,SAAS,EAAE,GAAG;IACdC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;EACb,CAAC;EACDC,GAAG,EAAE;IACDC,WAAW,EAAE,QAAQ;IACrBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,OAAO,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACP1B,KAAK,EAAE;EACX,CAAC;EACD2B,kBAAkB,EAAE;IAChBC,iBAAiB,EAAE,MAAM;IACzBC,gBAAgB,EAAE,cAAc;IAChCC,oBAAoB,EAAE,mBAAmB;IACzCC,YAAY,EAAE,QAAQ;IACtBC,eAAe,EAAE,QAAQ;IACzBC,cAAc,EAAE,YAAY;IAC5BC,aAAa,EAAE,WAAW;IAC1BC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,SAAS;IACxBC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,IAAI;IACVC,cAAc,EAAE,iCAAiC;IACjDC,WAAW,EAAE,2BAA2B;IACxCC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE;MACXC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE;IACX;EACJ,CAAC;EACDC,aAAa,EAAE;IACX9C,KAAK,EAAE;EACX,CAAC;EACD+C,gBAAgB,EAAE;IACdC,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE,cAAc;IAC1BC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,mCAAmC;IAC9CC,IAAI,EAAE,KAAK;IACXxC,OAAO,EAAE,MAAM;IACfyC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,CAAC,mBAAmB,EAAE,yCAAyC,EAAE,iDAAiD,CAAC;IAC9HC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,OAAO;IACtBC,UAAU,EAAE,OAAO;IACnBC,YAAY,EAAE,mBAAmB;IACjCC,MAAM,EAAE,WAAW;IACnBC,YAAY,EAAE,gBAAgB;IAC9BC,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZtE,KAAK,EAAE,UAAU;IACjByB,OAAO,EAAE,IAAI;IACb8C,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,UAAU,EAAE;MACRC,IAAI,EAAE,uCAAuC;MAC7CC,IAAI,EAAE;IACV,CAAC;IACDC,UAAU,EAAE;MACRF,IAAI,EAAE,wEAAwE;MAC9EC,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE;IACV;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}