{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm.getIsForeignVersion ? _c(\"JaFooter\") : !_vm.isEntHost ? _c(\"CustomFooter\", {\n    staticClass: \"login-footer\"\n  }) : _c(\"footer\", {\n    staticClass: \"register-footer\"\n  }, [_c(\"ul\", {\n    staticClass: \"clear font-size-zero\"\n  }, [_c(\"li\", {\n    staticClass: \"lang-switch-btn\"\n  }, [_c(\"LangSwitch\"), _c(\"i\", [_vm._v(\"|\")])], 1), _c(\"li\", {\n    staticClass: \"open-platform\"\n  }, [_c(\"a\", {\n    attrs: {\n      target: \"_blank\",\n      href: `https://${_vm.openHost}/#/login`\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.openPlatform\")))])]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"about\"\n  }, [_c(\"a\", {\n    attrs: {\n      target: \"_blank\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleFooterClick(\"about\");\n      }\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.aboutBestSign\")))])]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"contact\"\n  }, [_c(\"a\", {\n    attrs: {\n      target: \"_blank\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleFooterClick(\"contact\");\n      }\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.contact\")))])]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"recruitment\"\n  }, [_c(\"a\", {\n    attrs: {\n      target: \"_blank\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleFooterClick(\"recruitment\");\n      }\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.recruitment\")))])]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"help\"\n  }, [_c(\"a\", {\n    attrs: {\n      target: \"_blank\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleFooterClick(\"help\");\n      }\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.help\")))])]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"copyright\"\n  }, [_c(\"span\", [_vm._v(\"V4.0.0 \" + _vm._s(_vm.$t(\"common.copyright\")) + \" © \" + _vm._s(_vm.backEndCopyRightRange || _vm.defaultCopyRightRange) + \" \" + _vm._s(_vm.$t(\"common.company\")))]), _c(\"i\", [_vm._v(\"|\")])]), _c(\"li\", {\n    staticClass: \"on-record\"\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"home.record\")))])]), _vm.isHubblePage ? _c(\"li\", {\n    staticClass: \"on-record\"\n  }, [_c(\"i\", [_vm._v(\"|\")]), _c(\"span\", [_vm._v(_vm._s(_vm.$t(\"commonFooter.hubbleRecordId\")))])]) : _vm._e()])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "getIsForeignVersion", "isEntHost", "staticClass", "_v", "attrs", "target", "href", "openHost", "_s", "$t", "on", "click", "$event", "handleFooterClick", "backEndCopyRightRange", "defaultCopyRightRange", "isHubblePage", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/components/register_footer/RegisterFooter.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.getIsForeignVersion\n    ? _c(\"JaFooter\")\n    : !_vm.isEntHost\n    ? _c(\"CustomFooter\", { staticClass: \"login-footer\" })\n    : _c(\"footer\", { staticClass: \"register-footer\" }, [\n        _c(\"ul\", { staticClass: \"clear font-size-zero\" }, [\n          _c(\n            \"li\",\n            { staticClass: \"lang-switch-btn\" },\n            [_c(\"LangSwitch\"), _c(\"i\", [_vm._v(\"|\")])],\n            1\n          ),\n          _c(\"li\", { staticClass: \"open-platform\" }, [\n            _c(\n              \"a\",\n              {\n                attrs: {\n                  target: \"_blank\",\n                  href: `https://${_vm.openHost}/#/login`,\n                },\n              },\n              [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.openPlatform\")))])]\n            ),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"about\" }, [\n            _c(\n              \"a\",\n              {\n                attrs: { target: \"_blank\" },\n                on: {\n                  click: function ($event) {\n                    return _vm.handleFooterClick(\"about\")\n                  },\n                },\n              },\n              [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.aboutBestSign\")))])]\n            ),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"contact\" }, [\n            _c(\n              \"a\",\n              {\n                attrs: { target: \"_blank\" },\n                on: {\n                  click: function ($event) {\n                    return _vm.handleFooterClick(\"contact\")\n                  },\n                },\n              },\n              [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.contact\")))])]\n            ),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"recruitment\" }, [\n            _c(\n              \"a\",\n              {\n                attrs: { target: \"_blank\" },\n                on: {\n                  click: function ($event) {\n                    return _vm.handleFooterClick(\"recruitment\")\n                  },\n                },\n              },\n              [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.recruitment\")))])]\n            ),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"help\" }, [\n            _c(\n              \"a\",\n              {\n                attrs: { target: \"_blank\" },\n                on: {\n                  click: function ($event) {\n                    return _vm.handleFooterClick(\"help\")\n                  },\n                },\n              },\n              [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"common.help\")))])]\n            ),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"copyright\" }, [\n            _c(\"span\", [\n              _vm._v(\n                \"V4.0.0 \" +\n                  _vm._s(_vm.$t(\"common.copyright\")) +\n                  \" © \" +\n                  _vm._s(\n                    _vm.backEndCopyRightRange || _vm.defaultCopyRightRange\n                  ) +\n                  \" \" +\n                  _vm._s(_vm.$t(\"common.company\"))\n              ),\n            ]),\n            _c(\"i\", [_vm._v(\"|\")]),\n          ]),\n          _c(\"li\", { staticClass: \"on-record\" }, [\n            _c(\"span\", [_vm._v(_vm._s(_vm.$t(\"home.record\")))]),\n          ]),\n          _vm.isHubblePage\n            ? _c(\"li\", { staticClass: \"on-record\" }, [\n                _c(\"i\", [_vm._v(\"|\")]),\n                _c(\"span\", [\n                  _vm._v(_vm._s(_vm.$t(\"commonFooter.hubbleRecordId\"))),\n                ]),\n              ])\n            : _vm._e(),\n        ]),\n      ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,mBAAmB,GAC1BF,EAAE,CAAC,UAAU,CAAC,GACd,CAACD,GAAG,CAACI,SAAS,GACdH,EAAE,CAAC,cAAc,EAAE;IAAEI,WAAW,EAAE;EAAe,CAAC,CAAC,GACnDJ,EAAE,CAAC,QAAQ,EAAE;IAAEI,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC/CJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,EAAE,CAChDJ,EAAE,CACA,IAAI,EACJ;IAAEI,WAAW,EAAE;EAAkB,CAAC,EAClC,CAACJ,EAAE,CAAC,YAAY,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC1C,CACF,CAAC,EACDL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCJ,EAAE,CACA,GAAG,EACH;IACEM,KAAK,EAAE;MACLC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,WAAWT,GAAG,CAACU,QAAQ;IAC/B;EACF,CAAC,EACD,CAACT,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,EACDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CACjCJ,EAAE,CACA,GAAG,EACH;IACEM,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC;IAC3BK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOf,GAAG,CAACgB,iBAAiB,CAAC,OAAO,CAAC;MACvC;IACF;EACF,CAAC,EACD,CAACf,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/D,CAAC,EACDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACnCJ,EAAE,CACA,GAAG,EACH;IACEM,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC;IAC3BK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOf,GAAG,CAACgB,iBAAiB,CAAC,SAAS,CAAC;MACzC;IACF;EACF,CAAC,EACD,CAACf,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC,EACDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCJ,EAAE,CACA,GAAG,EACH;IACEM,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC;IAC3BK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOf,GAAG,CAACgB,iBAAiB,CAAC,aAAa,CAAC;MAC7C;IACF;EACF,CAAC,EACD,CAACf,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7D,CAAC,EACDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CAChCJ,EAAE,CACA,GAAG,EACH;IACEM,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC;IAC3BK,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOf,GAAG,CAACgB,iBAAiB,CAAC,MAAM,CAAC;MACtC;IACF;EACF,CAAC,EACD,CAACf,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CACtD,CAAC,EACDX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACM,EAAE,CACJ,SAAS,GACPN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAClC,KAAK,GACLZ,GAAG,CAACW,EAAE,CACJX,GAAG,CAACiB,qBAAqB,IAAIjB,GAAG,CAACkB,qBACnC,CAAC,GACD,GAAG,GACHlB,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,gBAAgB,CAAC,CACnC,CAAC,CACF,CAAC,EACFX,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCJ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CACpD,CAAC,EACFZ,GAAG,CAACmB,YAAY,GACZlB,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACtBL,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACM,EAAE,CAACN,GAAG,CAACW,EAAE,CAACX,GAAG,CAACY,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,CACtD,CAAC,CACH,CAAC,GACFZ,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC;AACR,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBtB,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}