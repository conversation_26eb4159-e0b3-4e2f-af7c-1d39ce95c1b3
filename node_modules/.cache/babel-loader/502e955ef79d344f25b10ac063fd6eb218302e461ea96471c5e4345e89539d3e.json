{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport 'pdfview/build/pdf.js';\nimport 'pdfview/web/pdf_viewer';\nimport 'pdfview/web/pdf_viewer.css';\nimport workerSrc from 'pdfview/build/pdf.worker.js';\nimport { mapMutations, mapState } from 'vuex';\nimport { throttle } from 'utils/fn.js';\nPDFJS.workerSrc = workerSrc;\nPDFJS.disableWorker = true;\n// PDFJS.cMapUrl = pdfCmaps;\nPDFJS.cMapPacked = true;\nexport default {\n  props: {\n    topicId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      // DPR: window.devicePixelRatio || 1,\n      DPR: 2,\n      maxWidth: 0,\n      preContent: '',\n      preCoordinate: {},\n      textLayer: null,\n      showQuote: false,\n      quoteTop: null,\n      quoteLeft: null,\n      selectedArea: null,\n      currentPage: 1,\n      pageHeights: [],\n      scale: 1,\n      selectedIndex: 0,\n      scrollDisabled: false,\n      pdfLoading: true\n    };\n  },\n  computed: {\n    ...mapState('hubble', ['startSelectQuote', 'preQuote', 'quotePage', 'quoteCoordinate', 'showSlider', 'documentInfo']),\n    pdfUrl() {\n      return `/web/hubble/topic/${this.topicId}/documents/${this.documentInfo.documentId}/download?access_token=${this.$cookie.get('access_token')}`;\n    },\n    quoteStyle() {\n      return {\n        '--quote-top': this.quoteTop,\n        '--quote-left': this.quoteLeft\n      };\n    }\n  },\n  watch: {\n    scale() {\n      setTimeout(() => {\n        this.$refs.pdfContainer.style.width = `${this.maxWidth * this.scale}px`;\n      }, 1000);\n    },\n    startSelectQuote(val) {\n      if (val) {\n        this.clearSelectedArea();\n        const parent = document.querySelector(`#page-${this.quotePage}`);\n        const {\n          offsetHeight,\n          offsetWidth,\n          offsetTop\n        } = parent;\n        const {\n          x,\n          y,\n          width,\n          height\n        } = this.quoteCoordinate;\n        const realLeft = x * offsetWidth;\n        const realTop = offsetHeight * y;\n        const realWidth = width * offsetWidth;\n        const realHeight = height * offsetHeight;\n        this.currentQuoteArea = parent.querySelector('.hubble-document__selected');\n        this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;\n        this.$refs.documentContent.scrollTo({\n          top: realTop + offsetTop - 100,\n          behavior: 'smooth'\n        });\n        this.$store.state.hubble.startSelectQuote = false;\n      }\n    }\n  },\n  methods: {\n    ...mapMutations('hubble', ['setQuote', 'toggleSlider', 'setDocumentInfo']),\n    async init() {\n      await this.getDocumentInfo();\n      this.handlePDF();\n      this.$refs.documentContent.addEventListener('mouseup', () => {\n        const sel = window.getSelection();\n        const text = sel.toString();\n        this.clearSelectedArea();\n        if (text) {\n          this.preContent = text;\n          this.showQuote = true;\n          this.handleSelectedNode();\n        }\n      });\n    },\n    handlePDF() {\n      PDFJS.getDocument(this.pdfUrl).then(pdf => {\n        this.pageHeights = [];\n        for (var i = 1; i <= pdf.numPages; i++) {\n          this.renderPDF(pdf, i);\n        }\n      });\n    },\n    getDocumentInfo() {\n      return this.$http.get(`/web/hubble/topic/${this.topicId}/documents`).then(res => {\n        this.setDocumentInfo(res.data);\n        this.maxWidth = this.documentInfo.documentPreview?.maxWidth;\n        const scale = (this.$refs.documentContent.offsetWidth - 40) / this.maxWidth;\n        this.scale = scale < 1 ? 1 : scale > 2 ? 2 : scale;\n      });\n    },\n    async renderPDF(pdf, num) {\n      const page = await pdf.getPage(num);\n      const {\n        DPR\n      } = this;\n      const viewport = page.getViewport(this.scale * DPR);\n      const pageDiv = document.querySelector(`#page-${page.pageIndex + 1}`) || (() => {\n        const newPageDiv = document.createElement('div');\n        newPageDiv.setAttribute('id', `page-${page.pageIndex + 1}`);\n        newPageDiv.setAttribute('class', 'document-page');\n        newPageDiv.setAttribute('style', 'position: relative');\n        this.$refs.pdfContainer.appendChild(newPageDiv);\n        const quoteDiv = document.createElement('div');\n        quoteDiv.setAttribute('class', 'hubble-document__selected border-scroll');\n        quoteDiv.setAttribute('style', 'display: none');\n        newPageDiv.appendChild(quoteDiv);\n        const canvas = document.createElement('canvas');\n        newPageDiv.appendChild(canvas);\n        return newPageDiv;\n      })();\n      const canvas = pageDiv.querySelector('canvas');\n      const context = canvas.getContext('2d');\n      canvas.height = viewport.height;\n      canvas.width = viewport.width;\n      pageDiv.style.height = `${viewport.height / DPR}px`;\n      canvas.style.height = `${viewport.height / DPR}px`;\n      canvas.style.width = `${viewport.width / DPR}px`;\n      this.pageHeights.push(viewport.height / DPR);\n      const renderContext = {\n        canvasContext: context,\n        viewport\n      };\n      page.render(renderContext).then(() => {\n        this.pdfLoading && (this.pdfLoading = false);\n        return page.getTextContent();\n      }).then(textContent => {\n        const textLayerDiv = pageDiv.querySelector('.textLayer') || (() => {\n          const newDiv = document.createElement('div');\n          pageDiv.appendChild(newDiv);\n          newDiv.addEventListener('mouseup', () => {\n            this.selectedIndex = num;\n          });\n          return newDiv;\n        })();\n        const textLayerDivWidth = canvas.style.width;\n        const textLayerDivHeight = canvas.style.height;\n        textLayerDiv.setAttribute('class', 'textLayer');\n        textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`);\n        if (textLayerDiv) {\n          textLayerDiv.innerHTML = '';\n          PDFJS.renderTextLayer({\n            textContent,\n            container: textLayerDiv,\n            viewport: page.getViewport(this.scale)\n          });\n        }\n      });\n    },\n    handleSelectedNode() {\n      const sel = window.getSelection();\n      // 获取选中元素\n      const range = sel.getRangeAt(0);\n      const container = range.commonAncestorContainer;\n      const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT);\n      const selectedNodes = [];\n      while (walker.nextNode()) {\n        const node = walker.currentNode;\n        if (range.intersectsNode(node) && !node.className && node.nodeName !== 'CANVAS') {\n          selectedNodes.push(node);\n        }\n      }\n      // 确定选中元素范围\n      let minLeft = null;\n      let maxLeft = null;\n      let minTop = null;\n      let maxTop = null;\n      let startNode = null;\n      selectedNodes.forEach(node => {\n        const {\n          offsetLeft,\n          offsetWidth,\n          offsetHeight\n        } = node;\n        const {\n          top\n        } = node.getBoundingClientRect();\n        const realMaxLeft = offsetLeft + offsetWidth;\n        const realMaxTop = top + offsetHeight;\n        if (!minLeft || offsetLeft < minLeft) {\n          minLeft = offsetLeft;\n        }\n        if (!maxLeft || realMaxLeft > maxLeft) {\n          maxLeft = realMaxLeft;\n        }\n        if (!minTop || top < minTop) {\n          minTop = top;\n          startNode = node;\n        }\n        if (!maxTop || realMaxTop > maxTop) {\n          maxTop = realMaxTop;\n        }\n      });\n      // 给选中范围添加边框\n      const parent = startNode.parentNode.parentNode;\n      const realTop = this.$refs.documentContent.scrollTop + minTop - parent.offsetTop - 120 - 20;\n      const realLeft = minLeft;\n      const realWidth = maxLeft - minLeft;\n      const realHeight = maxTop - minTop + 4;\n      this.currentQuoteArea = parent.querySelector('.hubble-document__selected');\n      this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;\n      this.quoteTop = `${realTop + maxTop - minTop - 25 + parent.offsetTop}px`;\n      this.quoteLeft = `${realLeft + realWidth}px`;\n      this.preCoordinate = {\n        x: realLeft / parent.offsetWidth,\n        y: realTop / parent.offsetHeight,\n        width: realWidth / parent.offsetWidth,\n        height: realHeight / parent.offsetHeight\n      };\n    },\n    handleQuote() {\n      this.setQuote({\n        content: this.preContent,\n        documentId: this.documentInfo.documentId,\n        quoteCoordinate: this.preCoordinate,\n        pageNumber: this.selectedIndex,\n        quoteFormat: 'TEXT'\n      });\n      this.preContent = '';\n    },\n    clearSelectedArea() {\n      this.showQuote = false;\n      this.quoteTop = null;\n      this.quoteLeft = null;\n      this.currentQuoteArea && (this.currentQuoteArea.style.display = 'none');\n    },\n    handleScroll: throttle(function (e) {\n      if (this.scrollDisabled) {\n        return;\n      }\n      let height = 0;\n      this.pageHeights.some((el, i) => {\n        height += el + 20;\n        if (height > e.target.scrollTop) {\n          this.currentPage = i + 1;\n          return true;\n        }\n      });\n    }, 50),\n    scrollTo() {\n      let top = 0;\n      for (let i = 0; i < this.currentPage - 1; i++) {\n        top += this.pageHeights[i] + 20;\n      }\n      top += 10;\n      this.scrollDisabled = true;\n      this.$refs.documentContent.scrollTo({\n        top: top,\n        behavior: 'smooth'\n      });\n      setTimeout(() => {\n        this.scrollDisabled = false;\n      }, 1000);\n    },\n    handlePage(type) {\n      if (type === 'pre') {\n        this.currentPage = this.currentPage - 1 < 1 ? 1 : this.currentPage - 1;\n      } else {\n        this.currentPage = this.currentPage + 1 > this.documentInfo.pageSize ? this.documentInfo.pageSize : this.currentPage + 1;\n      }\n      this.scrollTo();\n    },\n    handleScale(type) {\n      this.clearSelectedArea();\n      if (type === 'plus') {\n        if (this.scale === 2) {\n          return;\n        }\n        this.scale = this.scale + 0.2 > 2 ? 2 : this.scale + 0.2;\n      } else {\n        if (this.scale === 0.5) {\n          return;\n        }\n        this.scale = this.scale - 0.2 < 0.5 ? 0.5 : this.scale - 0.2;\n      }\n      this.handlePDF();\n    }\n  },\n  mounted() {\n    this.init();\n  }\n};", "map": {"version": 3, "names": ["workerSrc", "mapMutations", "mapState", "throttle", "PDFJS", "disable<PERSON><PERSON><PERSON>", "cMapPacked", "props", "topicId", "type", "String", "default", "data", "DPR", "max<PERSON><PERSON><PERSON>", "preContent", "preCoordinate", "textLayer", "showQuote", "quoteTop", "quoteLeft", "<PERSON><PERSON><PERSON>", "currentPage", "pageHeights", "scale", "selectedIndex", "scrollDisabled", "pdfLoading", "computed", "pdfUrl", "documentInfo", "documentId", "$cookie", "get", "quoteStyle", "watch", "setTimeout", "$refs", "pdfContainer", "style", "width", "startSelectQuote", "val", "clearSelectedArea", "parent", "document", "querySelector", "quotePage", "offsetHeight", "offsetWidth", "offsetTop", "x", "y", "height", "quoteCoordinate", "realLeft", "realTop", "realWidth", "realHeight", "currentQuoteArea", "documentContent", "scrollTo", "top", "behavior", "$store", "state", "hubble", "methods", "init", "getDocumentInfo", "handlePDF", "addEventListener", "sel", "window", "getSelection", "text", "toString", "handleSelectedNode", "getDocument", "then", "pdf", "i", "numPages", "renderPDF", "$http", "res", "setDocumentInfo", "documentPreview", "num", "page", "getPage", "viewport", "getViewport", "pageDiv", "pageIndex", "newPageDiv", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "quoteDiv", "canvas", "context", "getContext", "push", "renderContext", "canvasContext", "render", "getTextContent", "textContent", "textLayerDiv", "newDiv", "textLayerDivWidth", "textLayerDivHeight", "innerHTML", "renderTextLayer", "container", "range", "getRangeAt", "commonAncestorContainer", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "selectedNodes", "nextNode", "node", "currentNode", "intersectsNode", "className", "nodeName", "minLeft", "maxLeft", "minTop", "maxTop", "startNode", "for<PERSON>ach", "offsetLeft", "getBoundingClientRect", "realMaxLeft", "realMaxTop", "parentNode", "scrollTop", "handleQuote", "setQuote", "content", "pageNumber", "quoteFormat", "display", "handleScroll", "e", "some", "el", "target", "handlePage", "pageSize", "handleScale", "mounted"], "sources": ["src/views/agent/chat/document/index.vue"], "sourcesContent": ["<template>\n    <div class=\"hubble-document\">\n        <div class=\"hubble-document__header\">\n            <div class=\"hubble-document__title\">\n                {{ documentInfo.fileName }}\n            </div>\n            <div class=\"hubble-document__scale-box\">\n                <i class=\"iconfont el-icon-ssq-suoxiaojing scale\" @click=\"handleScale('minus')\"></i>\n                <i class=\"iconfont el-icon-ssq-fangdajing1 scale\" @click=\"handleScale('plus')\"></i>\n                <i class=\"el-icon-ssq-jiantou1\" @click=\"handlePage('pre')\"></i>\n                <el-input v-model=\"currentPage\" @keyup.enter.native=\"scrollTo\" @blur=\"scrollTo\"></el-input>\n                <span><i>/</i>  <em>{{ documentInfo.pageSize }}</em></span>\n                <i class=\"el-icon-ssq-jiantou1\" @click=\"handlePage('next')\"></i>\n            </div>\n        </div>\n        <el-tooltip :open-delay=\"500\" effect=\"dark\" content=\"选中文字后点击提问\" placement=\"top\">\n            <div :id=\"showQuote ? '' : 'guide-quote'\" class=\"hubble-document__quote fixed\" v-show=\"!showQuote\"></div>\n        </el-tooltip>\n        <div class=\"hubble-document__content\" ref=\"documentContent\" @scroll=\"handleScroll\">\n            <div class=\"hubble-document__content-loading\" v-if=\"pdfLoading\">\n                <img src=\"~views/agent/img/loading.gif\" alt=\"\">\n            </div>\n            <div class=\"hubble-document__content-box\" ref=\"pdfContainer\">\n                <el-tooltip :open-delay=\"500\" effect=\"dark\" content=\"选中文字后点击提问\" placement=\"top\">\n                    <div :id=\"!showQuote ? '' : 'guide-quote'\" class=\"hubble-document__quote\" v-show=\"showQuote\" :style=\"quoteStyle\" @click.stop=\"handleQuote\"></div>\n                </el-tooltip>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport 'pdfview/build/pdf.js';\nimport 'pdfview/web/pdf_viewer';\nimport 'pdfview/web/pdf_viewer.css';\nimport workerSrc from 'pdfview/build/pdf.worker.js';\nimport { mapMutations, mapState } from 'vuex';\nimport { throttle } from 'utils/fn.js';\n\nPDFJS.workerSrc = workerSrc;\nPDFJS.disableWorker = true;\n// PDFJS.cMapUrl = pdfCmaps;\nPDFJS.cMapPacked = true;\nexport default {\n    props: {\n        topicId: {\n            type: String,\n            default: '',\n        },\n    },\n    data() {\n        return {\n            // DPR: window.devicePixelRatio || 1,\n            DPR: 2,\n            maxWidth: 0,\n            preContent: '',\n            preCoordinate: {},\n            textLayer: null,\n            showQuote: false,\n            quoteTop: null,\n            quoteLeft: null,\n            selectedArea: null,\n            currentPage: 1,\n            pageHeights: [],\n            scale: 1,\n            selectedIndex: 0,\n            scrollDisabled: false,\n            pdfLoading: true,\n        };\n    },\n    computed: {\n        ...mapState('hubble', ['startSelectQuote', 'preQuote', 'quotePage', 'quoteCoordinate', 'showSlider', 'documentInfo']),\n        pdfUrl() {\n            return `/web/hubble/topic/${this.topicId}/documents/${this.documentInfo.documentId}/download?access_token=${this.$cookie.get('access_token')}`;\n        },\n        quoteStyle() {\n            return {\n                '--quote-top': this.quoteTop,\n                '--quote-left': this.quoteLeft,\n            };\n        },\n    },\n    watch: {\n        scale() {\n            setTimeout(() => {\n                this.$refs.pdfContainer.style.width = `${this.maxWidth * this.scale}px`;\n            }, 1000);\n        },\n        startSelectQuote(val) {\n            if (val) {\n                this.clearSelectedArea();\n                const parent = document.querySelector(`#page-${this.quotePage}`);\n                const { offsetHeight, offsetWidth, offsetTop } = parent;\n                const { x, y, width, height } = this.quoteCoordinate;\n                const realLeft = x * offsetWidth;\n                const realTop = offsetHeight * y;\n                const realWidth = width * offsetWidth;\n                const realHeight = height * offsetHeight;\n                this.currentQuoteArea = parent.querySelector('.hubble-document__selected');\n                this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;\n                this.$refs.documentContent.scrollTo({\n                    top: realTop + offsetTop - 100,\n                    behavior: 'smooth',\n                });\n                this.$store.state.hubble.startSelectQuote = false;\n            }\n        },\n    },\n    methods: {\n        ...mapMutations('hubble', ['setQuote', 'toggleSlider', 'setDocumentInfo']),\n        async init() {\n            await this.getDocumentInfo();\n            this.handlePDF();\n            this.$refs.documentContent.addEventListener('mouseup', () => {\n                const sel = window.getSelection();\n                const text = sel.toString();\n                this.clearSelectedArea();\n                if (text) {\n                    this.preContent = text;\n                    this.showQuote = true;\n                    this.handleSelectedNode();\n                }\n            });\n        },\n        handlePDF() {\n            PDFJS.getDocument(this.pdfUrl).then((pdf) => {\n                this.pageHeights = [];\n                for (var i = 1; i <= pdf.numPages; i++) {\n                    this.renderPDF(pdf, i);\n                }\n            });\n        },\n        getDocumentInfo() {\n            return this.$http.get(`/web/hubble/topic/${this.topicId}/documents`).then((res) => {\n                this.setDocumentInfo(res.data);\n                this.maxWidth = this.documentInfo.documentPreview?.maxWidth;\n                const scale = (this.$refs.documentContent.offsetWidth - 40) / this.maxWidth;\n                this.scale = scale < 1 ? 1 : (scale > 2 ? 2 : scale);\n            });\n        },\n        async renderPDF(pdf, num) {\n            const page = await pdf.getPage(num);\n            const { DPR } = this;\n            const viewport = page.getViewport(this.scale * DPR);\n            const pageDiv = document.querySelector(`#page-${page.pageIndex + 1}`) || (() => {\n                const newPageDiv = document.createElement('div');\n                newPageDiv.setAttribute('id', `page-${page.pageIndex + 1}`);\n                newPageDiv.setAttribute('class', 'document-page');\n                newPageDiv.setAttribute('style', 'position: relative');\n                this.$refs.pdfContainer.appendChild(newPageDiv);\n                const quoteDiv = document.createElement('div');\n                quoteDiv.setAttribute('class', 'hubble-document__selected border-scroll');\n                quoteDiv.setAttribute('style', 'display: none');\n                newPageDiv.appendChild(quoteDiv);\n                const canvas = document.createElement('canvas');\n                newPageDiv.appendChild(canvas);\n                return newPageDiv;\n            })();\n            const canvas = pageDiv.querySelector('canvas');\n            const context = canvas.getContext('2d');\n            canvas.height = viewport.height;\n            canvas.width = viewport.width;\n            pageDiv.style.height = `${viewport.height / DPR}px`;\n            canvas.style.height = `${viewport.height / DPR}px`;\n            canvas.style.width = `${viewport.width / DPR}px`;\n            this.pageHeights.push(viewport.height / DPR);\n            const renderContext = {\n                canvasContext: context,\n                viewport,\n            };\n            page.render(renderContext).then(() => {\n                this.pdfLoading && (this.pdfLoading = false);\n                return page.getTextContent();\n            }).then((textContent) => {\n                const textLayerDiv = pageDiv.querySelector('.textLayer') || (() => {\n                    const newDiv = document.createElement('div');\n                    pageDiv.appendChild(newDiv);\n                    newDiv.addEventListener('mouseup', () => {\n                        this.selectedIndex = num;\n                    });\n                    return newDiv;\n                })();\n                const textLayerDivWidth = canvas.style.width;\n                const textLayerDivHeight = canvas.style.height;\n                textLayerDiv.setAttribute('class', 'textLayer');\n                textLayerDiv.setAttribute('style', `width:${textLayerDivWidth};height:${textLayerDivHeight}`);\n                if (textLayerDiv) {\n                    textLayerDiv.innerHTML = '';\n                    PDFJS.renderTextLayer({\n                        textContent,\n                        container: textLayerDiv,\n                        viewport: page.getViewport(this.scale),\n                    });\n                }\n            });\n        },\n        handleSelectedNode() {\n            const sel = window.getSelection();\n            // 获取选中元素\n            const range = sel.getRangeAt(0);\n            const container = range.commonAncestorContainer;\n            const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT);\n            const selectedNodes = [];\n            while (walker.nextNode()) {\n                const node = walker.currentNode;\n                if (range.intersectsNode(node) && !node.className && node.nodeName !== 'CANVAS') {\n                    selectedNodes.push(node);\n                }\n            }\n            // 确定选中元素范围\n            let minLeft = null;\n            let maxLeft = null;\n            let minTop = null;\n            let maxTop = null;\n            let startNode = null;\n            selectedNodes.forEach((node) => {\n                const { offsetLeft, offsetWidth, offsetHeight } = node;\n                const { top } = node.getBoundingClientRect();\n                const realMaxLeft = offsetLeft + offsetWidth;\n                const realMaxTop = top + offsetHeight;\n                if (!minLeft || offsetLeft < minLeft) {\n                    minLeft = offsetLeft;\n                }\n                if (!maxLeft || realMaxLeft > maxLeft) {\n                    maxLeft = realMaxLeft;\n                }\n                if (!minTop || top < minTop) {\n                    minTop = top;\n                    startNode = node;\n                }\n                if (!maxTop || realMaxTop > maxTop) {\n                    maxTop = realMaxTop;\n                }\n            });\n            // 给选中范围添加边框\n            const parent = startNode.parentNode.parentNode;\n            const realTop = this.$refs.documentContent.scrollTop + minTop - parent.offsetTop - 120 - 20;\n            const realLeft = minLeft;\n            const realWidth = maxLeft - minLeft;\n            const realHeight = maxTop - minTop + 4;\n            this.currentQuoteArea = parent.querySelector('.hubble-document__selected');\n            this.currentQuoteArea.style = `top: ${realTop}px; left: ${realLeft}px; width: ${realWidth}px; height: ${realHeight}px; display: block;`;\n            this.quoteTop = `${realTop + maxTop - minTop - 25 + parent.offsetTop}px`;\n            this.quoteLeft = `${realLeft + realWidth}px`;\n            this.preCoordinate = {\n                x: realLeft / parent.offsetWidth,\n                y: realTop / parent.offsetHeight,\n                width: realWidth / parent.offsetWidth,\n                height: realHeight / parent.offsetHeight,\n            };\n        },\n        handleQuote() {\n            this.setQuote({\n                content: this.preContent,\n                documentId: this.documentInfo.documentId,\n                quoteCoordinate: this.preCoordinate,\n                pageNumber: this.selectedIndex,\n                quoteFormat: 'TEXT',\n            });\n            this.preContent = '';\n        },\n        clearSelectedArea() {\n            this.showQuote = false;\n            this.quoteTop = null;\n            this.quoteLeft = null;\n            this.currentQuoteArea && (this.currentQuoteArea.style.display = 'none');\n        },\n        handleScroll: throttle(function(e) {\n            if (this.scrollDisabled) {\n                return;\n            }\n            let height = 0;\n            this.pageHeights.some((el, i) => {\n                height += el + 20;\n                if (height > e.target.scrollTop) {\n                    this.currentPage = i + 1;\n                    return true;\n                }\n            });\n        }, 50),\n        scrollTo() {\n            let top = 0;\n            for (let i = 0; i < this.currentPage - 1; i++) {\n                top += this.pageHeights[i] + 20;\n            }\n            top += 10;\n            this.scrollDisabled = true;\n            this.$refs.documentContent.scrollTo({\n                top: top,\n                behavior: 'smooth',\n            });\n            setTimeout(() => {\n                this.scrollDisabled = false;\n            }, 1000);\n        },\n        handlePage(type) {\n            if (type === 'pre') {\n                this.currentPage = this.currentPage - 1 < 1 ? 1 : this.currentPage - 1;\n            } else {\n                this.currentPage = this.currentPage + 1 > this.documentInfo.pageSize ? this.documentInfo.pageSize : this.currentPage + 1;\n            }\n            this.scrollTo();\n        },\n        handleScale(type) {\n            this.clearSelectedArea();\n            if (type === 'plus') {\n                if (this.scale === 2) {\n                    return;\n                }\n                this.scale = this.scale + 0.2 > 2 ? 2 : this.scale + 0.2;\n            } else {\n                if (this.scale === 0.5) {\n                    return;\n                }\n                this.scale = this.scale - 0.2 < 0.5 ? 0.5 : this.scale - 0.2;\n            }\n            this.handlePDF();\n        },\n    },\n    mounted() {\n        this.init();\n    },\n};\n</script>\n<style lang=\"scss\">\n.hubble-document{\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    &__header{\n        height: 55px;\n        line-height: 55px;\n        padding: 0 20px;\n        background: #FFFFFF;\n        box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);\n        position: relative;\n        z-index: 9;\n    }\n    &__title i{\n        margin-right: 20px;\n        cursor: pointer;\n        &.active{\n            color: #409EFF;\n        }\n    }\n    &__scale-box{\n        position: absolute;\n        height: 100%;\n        padding: 0 10px;\n        top: 0;\n        right: 0;\n        user-select: none;\n        background: #fff;\n        i{\n            cursor: pointer;\n            margin: 0 15px;\n            &.el-icon-ssq-jiantou1:nth-child(3){\n                transform: rotate(180deg);\n            }\n        }\n        .el-input{\n            display: inline-block;\n            width: 44px;\n            input{\n                height: 24px;\n                position: relative;\n                top: -2px;\n                text-align: center;\n            }\n        }\n        span{\n            display: inline-block;\n            position: relative;\n            padding-left: 13px;\n            font-size: 18px;\n            i{\n                position: absolute;\n                left: -10px;\n                top: -1px;\n            }\n            em{\n                font-weight: normal;\n            }\n        }\n    }\n    &__content{\n        flex: 1;\n        background: #f8f8f8;\n        position: relative;\n        overflow: auto;\n        ::selection {\n            background: #0079FE;\n        }\n        &-loading{\n            width: calc(100% - 40px);\n            height: 100vh;\n            margin: 20px auto;\n            background: #fff;\n            img{\n                position: absolute;\n                top: 50%;\n                left: 50%;\n                transform: translate(-50%, -50%);\n            }\n        }\n        &-box{\n            overflow: auto;\n            overflow-x: hidden;\n            margin: 20px auto 0;\n            background: #f8f8f8;\n            position: relative;\n            canvas{\n                position: absolute;\n                top: 0;\n                left: 0;\n            }\n            .textLayer{\n                top: 0;\n                left: 0;\n            }\n            .document-page{\n                background: #fff;\n                margin-bottom: 20px;\n            }\n        }\n    }\n    &__quote{\n        position: absolute;\n        top: var(--quote-top, calc(100% - 70px));\n        left: var(--quote-left, calc(100% - 50px));\n        z-index: 9;\n        right: 20px;\n        width: 49px;\n        height: 44px;\n        background: transparent;\n        transition: all .5s;\n        background-image: url(~views/agent/img/quoteIcon.png);\n        background-size: cover;\n        cursor: pointer;\n        &:hover{\n            background-image: url(~views/agent/img/quoteIconHover.png);\n        }\n        &.fixed{\n            top: calc(100% - 56px);\n            left: calc(100% - 56px);\n        }\n    }\n\n    @keyframes blink {\n        0% {\n            border-color: transparent;\n        }\n        50% {\n            border-color: #0C8AEE;\n        }\n        100% {\n            border-color: transparent;\n        }\n    }\n    .border-scroll {\n        position: absolute;\n        z-index: 9;\n        border: 2px dashed #0C8AEE;\n        animation: blink 1s infinite;\n    }\n}\n</style>\n"], "mappings": ";;;AAgCA;AACA;AACA;AACA,OAAAA,SAAA;AACA,SAAAC,YAAA,EAAAC,QAAA;AACA,SAAAC,QAAA;AAEAC,KAAA,CAAAJ,SAAA,GAAAA,SAAA;AACAI,KAAA,CAAAC,aAAA;AACA;AACAD,KAAA,CAAAE,UAAA;AACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACA;MACAC,GAAA;MACAC,QAAA;MACAC,UAAA;MACAC,aAAA;MACAC,SAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,KAAA;MACAC,aAAA;MACAC,cAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,GAAA1B,QAAA;IACA2B,OAAA;MACA,iCAAArB,OAAA,mBAAAsB,YAAA,CAAAC,UAAA,+BAAAC,OAAA,CAAAC,GAAA;IACA;IACAC,WAAA;MACA;QACA,oBAAAf,QAAA;QACA,qBAAAC;MACA;IACA;EACA;EACAe,KAAA;IACAX,MAAA;MACAY,UAAA;QACA,KAAAC,KAAA,CAAAC,YAAA,CAAAC,KAAA,CAAAC,KAAA,WAAA1B,QAAA,QAAAU,KAAA;MACA;IACA;IACAiB,iBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,iBAAA;QACA,MAAAC,MAAA,GAAAC,QAAA,CAAAC,aAAA,eAAAC,SAAA;QACA;UAAAC,YAAA;UAAAC,WAAA;UAAAC;QAAA,IAAAN,MAAA;QACA;UAAAO,CAAA;UAAAC,CAAA;UAAAZ,KAAA;UAAAa;QAAA,SAAAC,eAAA;QACA,MAAAC,QAAA,GAAAJ,CAAA,GAAAF,WAAA;QACA,MAAAO,OAAA,GAAAR,YAAA,GAAAI,CAAA;QACA,MAAAK,SAAA,GAAAjB,KAAA,GAAAS,WAAA;QACA,MAAAS,UAAA,GAAAL,MAAA,GAAAL,YAAA;QACA,KAAAW,gBAAA,GAAAf,MAAA,CAAAE,aAAA;QACA,KAAAa,gBAAA,CAAApB,KAAA,WAAAiB,OAAA,aAAAD,QAAA,cAAAE,SAAA,eAAAC,UAAA;QACA,KAAArB,KAAA,CAAAuB,eAAA,CAAAC,QAAA;UACAC,GAAA,EAAAN,OAAA,GAAAN,SAAA;UACAa,QAAA;QACA;QACA,KAAAC,MAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAzB,gBAAA;MACA;IACA;EACA;EACA0B,OAAA;IACA,GAAAlE,YAAA;IACA,MAAAmE,KAAA;MACA,WAAAC,eAAA;MACA,KAAAC,SAAA;MACA,KAAAjC,KAAA,CAAAuB,eAAA,CAAAW,gBAAA;QACA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA;QACA,MAAAC,IAAA,GAAAH,GAAA,CAAAI,QAAA;QACA,KAAAjC,iBAAA;QACA,IAAAgC,IAAA;UACA,KAAA5D,UAAA,GAAA4D,IAAA;UACA,KAAAzD,SAAA;UACA,KAAA2D,kBAAA;QACA;MACA;IACA;IACAP,UAAA;MACAlE,KAAA,CAAA0E,WAAA,MAAAjD,MAAA,EAAAkD,IAAA,CAAAC,GAAA;QACA,KAAAzD,WAAA;QACA,SAAA0D,CAAA,MAAAA,CAAA,IAAAD,GAAA,CAAAE,QAAA,EAAAD,CAAA;UACA,KAAAE,SAAA,CAAAH,GAAA,EAAAC,CAAA;QACA;MACA;IACA;IACAZ,gBAAA;MACA,YAAAe,KAAA,CAAAnD,GAAA,2BAAAzB,OAAA,cAAAuE,IAAA,CAAAM,GAAA;QACA,KAAAC,eAAA,CAAAD,GAAA,CAAAzE,IAAA;QACA,KAAAE,QAAA,QAAAgB,YAAA,CAAAyD,eAAA,EAAAzE,QAAA;QACA,MAAAU,KAAA,SAAAa,KAAA,CAAAuB,eAAA,CAAAX,WAAA,cAAAnC,QAAA;QACA,KAAAU,KAAA,GAAAA,KAAA,WAAAA,KAAA,WAAAA,KAAA;MACA;IACA;IACA,MAAA2D,UAAAH,GAAA,EAAAQ,GAAA;MACA,MAAAC,IAAA,SAAAT,GAAA,CAAAU,OAAA,CAAAF,GAAA;MACA;QAAA3E;MAAA;MACA,MAAA8E,QAAA,GAAAF,IAAA,CAAAG,WAAA,MAAApE,KAAA,GAAAX,GAAA;MACA,MAAAgF,OAAA,GAAAhD,QAAA,CAAAC,aAAA,UAAA2C,IAAA,CAAAK,SAAA;QACA,MAAAC,UAAA,GAAAlD,QAAA,CAAAmD,aAAA;QACAD,UAAA,CAAAE,YAAA,eAAAR,IAAA,CAAAK,SAAA;QACAC,UAAA,CAAAE,YAAA;QACAF,UAAA,CAAAE,YAAA;QACA,KAAA5D,KAAA,CAAAC,YAAA,CAAA4D,WAAA,CAAAH,UAAA;QACA,MAAAI,QAAA,GAAAtD,QAAA,CAAAmD,aAAA;QACAG,QAAA,CAAAF,YAAA;QACAE,QAAA,CAAAF,YAAA;QACAF,UAAA,CAAAG,WAAA,CAAAC,QAAA;QACA,MAAAC,MAAA,GAAAvD,QAAA,CAAAmD,aAAA;QACAD,UAAA,CAAAG,WAAA,CAAAE,MAAA;QACA,OAAAL,UAAA;MACA;MACA,MAAAK,MAAA,GAAAP,OAAA,CAAA/C,aAAA;MACA,MAAAuD,OAAA,GAAAD,MAAA,CAAAE,UAAA;MACAF,MAAA,CAAA/C,MAAA,GAAAsC,QAAA,CAAAtC,MAAA;MACA+C,MAAA,CAAA5D,KAAA,GAAAmD,QAAA,CAAAnD,KAAA;MACAqD,OAAA,CAAAtD,KAAA,CAAAc,MAAA,MAAAsC,QAAA,CAAAtC,MAAA,GAAAxC,GAAA;MACAuF,MAAA,CAAA7D,KAAA,CAAAc,MAAA,MAAAsC,QAAA,CAAAtC,MAAA,GAAAxC,GAAA;MACAuF,MAAA,CAAA7D,KAAA,CAAAC,KAAA,MAAAmD,QAAA,CAAAnD,KAAA,GAAA3B,GAAA;MACA,KAAAU,WAAA,CAAAgF,IAAA,CAAAZ,QAAA,CAAAtC,MAAA,GAAAxC,GAAA;MACA,MAAA2F,aAAA;QACAC,aAAA,EAAAJ,OAAA;QACAV;MACA;MACAF,IAAA,CAAAiB,MAAA,CAAAF,aAAA,EAAAzB,IAAA;QACA,KAAApD,UAAA,UAAAA,UAAA;QACA,OAAA8D,IAAA,CAAAkB,cAAA;MACA,GAAA5B,IAAA,CAAA6B,WAAA;QACA,MAAAC,YAAA,GAAAhB,OAAA,CAAA/C,aAAA;UACA,MAAAgE,MAAA,GAAAjE,QAAA,CAAAmD,aAAA;UACAH,OAAA,CAAAK,WAAA,CAAAY,MAAA;UACAA,MAAA,CAAAvC,gBAAA;YACA,KAAA9C,aAAA,GAAA+D,GAAA;UACA;UACA,OAAAsB,MAAA;QACA;QACA,MAAAC,iBAAA,GAAAX,MAAA,CAAA7D,KAAA,CAAAC,KAAA;QACA,MAAAwE,kBAAA,GAAAZ,MAAA,CAAA7D,KAAA,CAAAc,MAAA;QACAwD,YAAA,CAAAZ,YAAA;QACAY,YAAA,CAAAZ,YAAA,mBAAAc,iBAAA,WAAAC,kBAAA;QACA,IAAAH,YAAA;UACAA,YAAA,CAAAI,SAAA;UACA7G,KAAA,CAAA8G,eAAA;YACAN,WAAA;YACAO,SAAA,EAAAN,YAAA;YACAlB,QAAA,EAAAF,IAAA,CAAAG,WAAA,MAAApE,KAAA;UACA;QACA;MACA;IACA;IACAqD,mBAAA;MACA,MAAAL,GAAA,GAAAC,MAAA,CAAAC,YAAA;MACA;MACA,MAAA0C,KAAA,GAAA5C,GAAA,CAAA6C,UAAA;MACA,MAAAF,SAAA,GAAAC,KAAA,CAAAE,uBAAA;MACA,MAAAC,MAAA,GAAA1E,QAAA,CAAA2E,gBAAA,CAAAL,SAAA,EAAAM,UAAA,CAAAC,YAAA;MACA,MAAAC,aAAA;MACA,OAAAJ,MAAA,CAAAK,QAAA;QACA,MAAAC,IAAA,GAAAN,MAAA,CAAAO,WAAA;QACA,IAAAV,KAAA,CAAAW,cAAA,CAAAF,IAAA,MAAAA,IAAA,CAAAG,SAAA,IAAAH,IAAA,CAAAI,QAAA;UACAN,aAAA,CAAApB,IAAA,CAAAsB,IAAA;QACA;MACA;MACA;MACA,IAAAK,OAAA;MACA,IAAAC,OAAA;MACA,IAAAC,MAAA;MACA,IAAAC,MAAA;MACA,IAAAC,SAAA;MACAX,aAAA,CAAAY,OAAA,CAAAV,IAAA;QACA;UAAAW,UAAA;UAAAvF,WAAA;UAAAD;QAAA,IAAA6E,IAAA;QACA;UAAA/D;QAAA,IAAA+D,IAAA,CAAAY,qBAAA;QACA,MAAAC,WAAA,GAAAF,UAAA,GAAAvF,WAAA;QACA,MAAA0F,UAAA,GAAA7E,GAAA,GAAAd,YAAA;QACA,KAAAkF,OAAA,IAAAM,UAAA,GAAAN,OAAA;UACAA,OAAA,GAAAM,UAAA;QACA;QACA,KAAAL,OAAA,IAAAO,WAAA,GAAAP,OAAA;UACAA,OAAA,GAAAO,WAAA;QACA;QACA,KAAAN,MAAA,IAAAtE,GAAA,GAAAsE,MAAA;UACAA,MAAA,GAAAtE,GAAA;UACAwE,SAAA,GAAAT,IAAA;QACA;QACA,KAAAQ,MAAA,IAAAM,UAAA,GAAAN,MAAA;UACAA,MAAA,GAAAM,UAAA;QACA;MACA;MACA;MACA,MAAA/F,MAAA,GAAA0F,SAAA,CAAAM,UAAA,CAAAA,UAAA;MACA,MAAApF,OAAA,QAAAnB,KAAA,CAAAuB,eAAA,CAAAiF,SAAA,GAAAT,MAAA,GAAAxF,MAAA,CAAAM,SAAA;MACA,MAAAK,QAAA,GAAA2E,OAAA;MACA,MAAAzE,SAAA,GAAA0E,OAAA,GAAAD,OAAA;MACA,MAAAxE,UAAA,GAAA2E,MAAA,GAAAD,MAAA;MACA,KAAAzE,gBAAA,GAAAf,MAAA,CAAAE,aAAA;MACA,KAAAa,gBAAA,CAAApB,KAAA,WAAAiB,OAAA,aAAAD,QAAA,cAAAE,SAAA,eAAAC,UAAA;MACA,KAAAvC,QAAA,MAAAqC,OAAA,GAAA6E,MAAA,GAAAD,MAAA,QAAAxF,MAAA,CAAAM,SAAA;MACA,KAAA9B,SAAA,MAAAmC,QAAA,GAAAE,SAAA;MACA,KAAAzC,aAAA;QACAmC,CAAA,EAAAI,QAAA,GAAAX,MAAA,CAAAK,WAAA;QACAG,CAAA,EAAAI,OAAA,GAAAZ,MAAA,CAAAI,YAAA;QACAR,KAAA,EAAAiB,SAAA,GAAAb,MAAA,CAAAK,WAAA;QACAI,MAAA,EAAAK,UAAA,GAAAd,MAAA,CAAAI;MACA;IACA;IACA8F,YAAA;MACA,KAAAC,QAAA;QACAC,OAAA,OAAAjI,UAAA;QACAgB,UAAA,OAAAD,YAAA,CAAAC,UAAA;QACAuB,eAAA,OAAAtC,aAAA;QACAiI,UAAA,OAAAxH,aAAA;QACAyH,WAAA;MACA;MACA,KAAAnI,UAAA;IACA;IACA4B,kBAAA;MACA,KAAAzB,SAAA;MACA,KAAAC,QAAA;MACA,KAAAC,SAAA;MACA,KAAAuC,gBAAA,UAAAA,gBAAA,CAAApB,KAAA,CAAA4G,OAAA;IACA;IACAC,YAAA,EAAAjJ,QAAA,WAAAkJ,CAAA;MACA,SAAA3H,cAAA;QACA;MACA;MACA,IAAA2B,MAAA;MACA,KAAA9B,WAAA,CAAA+H,IAAA,EAAAC,EAAA,EAAAtE,CAAA;QACA5B,MAAA,IAAAkG,EAAA;QACA,IAAAlG,MAAA,GAAAgG,CAAA,CAAAG,MAAA,CAAAX,SAAA;UACA,KAAAvH,WAAA,GAAA2D,CAAA;UACA;QACA;MACA;IACA;IACApB,SAAA;MACA,IAAAC,GAAA;MACA,SAAAmB,CAAA,MAAAA,CAAA,QAAA3D,WAAA,MAAA2D,CAAA;QACAnB,GAAA,SAAAvC,WAAA,CAAA0D,CAAA;MACA;MACAnB,GAAA;MACA,KAAApC,cAAA;MACA,KAAAW,KAAA,CAAAuB,eAAA,CAAAC,QAAA;QACAC,GAAA,EAAAA,GAAA;QACAC,QAAA;MACA;MACA3B,UAAA;QACA,KAAAV,cAAA;MACA;IACA;IACA+H,WAAAhJ,IAAA;MACA,IAAAA,IAAA;QACA,KAAAa,WAAA,QAAAA,WAAA,oBAAAA,WAAA;MACA;QACA,KAAAA,WAAA,QAAAA,WAAA,YAAAQ,YAAA,CAAA4H,QAAA,QAAA5H,YAAA,CAAA4H,QAAA,QAAApI,WAAA;MACA;MACA,KAAAuC,QAAA;IACA;IACA8F,YAAAlJ,IAAA;MACA,KAAAkC,iBAAA;MACA,IAAAlC,IAAA;QACA,SAAAe,KAAA;UACA;QACA;QACA,KAAAA,KAAA,QAAAA,KAAA,sBAAAA,KAAA;MACA;QACA,SAAAA,KAAA;UACA;QACA;QACA,KAAAA,KAAA,QAAAA,KAAA,0BAAAA,KAAA;MACA;MACA,KAAA8C,SAAA;IACA;EACA;EACAsF,QAAA;IACA,KAAAxF,IAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}