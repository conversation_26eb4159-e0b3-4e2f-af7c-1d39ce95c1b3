{"ast": null, "code": "export default {\n  docTranslation: {\n    title: '合同翻译',\n    docLeft: {\n      title: '合同原文',\n      reUpload: '重新上传'\n    },\n    docRight: {\n      title: '合同译文',\n      applyTermBank: '应用术语库',\n      translationLang: '翻译语言',\n      reTranslation: '重新翻译',\n      startTranslation: '开始翻译',\n      translationing: '合同翻译中...',\n      translateResultTip: '翻译结果在此展示',\n      langs: {\n        zh: '英-->中',\n        en: '中-->英'\n      },\n      confirm: {\n        title: '提示',\n        buy: '去购买',\n        unEnoungh: '使用次数不足，请购买充值。',\n        tip1: '当前合同的字数为{wordCounts}，将扣除合同翻译的使用次数为：{billingCount}',\n        tip2: '扣除主体：{enterpriseName}',\n        ok: '确定'\n      },\n      errTip: '翻译失败，请重试'\n    },\n    sideBar: {\n      toolsTab: '工具栏',\n      termBank: '术语库',\n      termBankTip: '术语库用来定义某些特定词语翻译，例如“上上签”，可以翻译为“BestSign”，当然也可以定义不翻译的词语，例如“上上签”译文设置为“上上签”。',\n      placeholder: '请输入',\n      originaFile: '原文',\n      translationFile: '译文',\n      addTermBank: '新增词条',\n      download: '下载翻译',\n      historyTab: '历史记录',\n      currentHistoryTab: '文档记录'\n    },\n    upload: {\n      tip1: '将原始文档件拖拽至此上传',\n      tip2: '目前仅支持PDF、Word文档',\n      tip3: '释放鼠标完成上传',\n      choose: '选择文件',\n      typeTip: '目前仅支持PDF、Word文档',\n      err: '上传失败'\n    }\n  }\n};", "map": {"version": 3, "names": ["docTranslation", "title", "docLeft", "reUpload", "docRight", "applyTermBank", "translationLang", "reTranslation", "startTranslation", "translationing", "translateResultTip", "langs", "zh", "en", "confirm", "buy", "unEnoungh", "tip1", "tip2", "ok", "errTip", "sideBar", "toolsTab", "termBank", "termBankTip", "placeholder", "originaFile", "translationFile", "addTermBank", "download", "historyTab", "currentHistoryTab", "upload", "tip3", "choose", "typeTip", "err"], "sources": ["/Users/<USER>/ssq/hubble-agent-front/src/lang/module/docTranslation/docTranslation-en.js"], "sourcesContent": ["export default {\n    docTranslation: {\n        title: '合同翻译',\n        docLeft: {\n            title: '合同原文',\n            reUpload: '重新上传',\n        },\n        docRight: {\n            title: '合同译文',\n            applyTermBank: '应用术语库',\n            translationLang: '翻译语言',\n            reTranslation: '重新翻译',\n            startTranslation: '开始翻译',\n            translationing: '合同翻译中...',\n            translateResultTip: '翻译结果在此展示',\n            langs: {\n                zh: '英-->中',\n                en: '中-->英',\n            },\n            confirm: {\n                title: '提示',\n                buy: '去购买',\n                unEnoungh: '使用次数不足，请购买充值。',\n                tip1: '当前合同的字数为{wordCounts}，将扣除合同翻译的使用次数为：{billingCount}',\n                tip2: '扣除主体：{enterpriseName}',\n                ok: '确定',\n            },\n            errTip: '翻译失败，请重试',\n        },\n        sideBar: {\n            toolsTab: '工具栏',\n            termBank: '术语库',\n            termBankTip: '术语库用来定义某些特定词语翻译，例如“上上签”，可以翻译为“BestSign”，当然也可以定义不翻译的词语，例如“上上签”译文设置为“上上签”。',\n            placeholder: '请输入',\n            originaFile: '原文',\n            translationFile: '译文',\n            addTermBank: '新增词条',\n            download: '下载翻译',\n            historyTab: '历史记录',\n            currentHistoryTab: '文档记录',\n        },\n        upload: {\n            tip1: '将原始文档件拖拽至此上传',\n            tip2: '目前仅支持PDF、Word文档',\n            tip3: '释放鼠标完成上传',\n            choose: '选择文件',\n            typeTip: '目前仅支持PDF、Word文档',\n            err: '上传失败',\n        },\n    },\n};\n"], "mappings": "AAAA,eAAe;EACXA,cAAc,EAAE;IACZC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE;MACLD,KAAK,EAAE,MAAM;MACbE,QAAQ,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACNH,KAAK,EAAE,MAAM;MACbI,aAAa,EAAE,OAAO;MACtBC,eAAe,EAAE,MAAM;MACvBC,aAAa,EAAE,MAAM;MACrBC,gBAAgB,EAAE,MAAM;MACxBC,cAAc,EAAE,UAAU;MAC1BC,kBAAkB,EAAE,UAAU;MAC9BC,KAAK,EAAE;QACHC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACLb,KAAK,EAAE,IAAI;QACXc,GAAG,EAAE,KAAK;QACVC,SAAS,EAAE,eAAe;QAC1BC,IAAI,EAAE,mDAAmD;QACzDC,IAAI,EAAE,uBAAuB;QAC7BC,EAAE,EAAE;MACR,CAAC;MACDC,MAAM,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACLC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,0EAA0E;MACvFC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,MAAM;MACnBC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,iBAAiB,EAAE;IACvB,CAAC;IACDC,MAAM,EAAE;MACJf,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,iBAAiB;MACvBe,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,iBAAiB;MAC1BC,GAAG,EAAE;IACT;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}