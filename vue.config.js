'use strict';
const fs = require('fs');
const path = require('path');
const NODE_ENV = process.env.NODE_ENV;
// 是否是development模式
const isDevelopment = NODE_ENV.indexOf('development') > -1;
function resolve(dir) {
    return path.join(__dirname, dir);
}

// 将proxy.config.dev.js文件中的配置格式化成vue-cli可以接受的格式
function proxyFormat() {
    const proxyFile = fs.readFileSync(resolve('proxy.config.dev.js'), 'utf-8');
    // eslint-disable-next-line
    const proxyConfig = eval(proxyFile);
    return proxyConfig.reduce((proxy, item) => {
        const context = item.context;
        delete item.context;
        if (typeof context === 'string') {
            proxy[context] = item;
        } else {
            context.forEach((c) => {
                proxy[c] = item;
            });
        }
        return proxy;
    }, {});
}

module.exports = {
    publicPath: isDevelopment ? '/' : '/web',
    assetsDir: isDevelopment ? 'hubble' : '',
    outputDir: 'dist',
    lintOnSave: false,
    productionSourceMap: true,
    devServer: {
        port: 9527,
        host: '0.0.0.0',
        historyApiFallback: true,
        hot: true,
        allowedHosts: 'all',
        client: {
            overlay: {
                warnings: true,
                errors: true,
            },
            logging: 'none',
        },
        open: true,
        proxy: (() => {
            const proxy = proxyFormat();
            Object.keys(proxy).forEach(item => {
                proxy[item].router = function(request) {
                    const proxy = proxyFormat();
                    const item = Object.keys(proxy).find(key => request.url.indexOf(key) > -1);
                    return (item && proxy[item].target) || false;
                };
            });
            return proxy;
        })(),
    },
    configureWebpack: {
        resolve: {
            extensions: ['.js', '.vue', '.json', '.css', '.scss', '.less'],
            alias: {
                'src': resolve('src'),
                'http': resolve('src/http'),
                'styles': resolve('src/styles'),
                'lang': resolve('src/lang'),
                'const': resolve('src/const'),
                'img': resolve('src/assets/images'),
                'plugins': resolve('src/plugins'),
                'utils': resolve('src/utils'),
                'mixins': resolve('src/mixins'),
                'components': resolve('src/components'),
                'views': resolve('src/views/'),
                'iconfont': resolve('src/assets/iconfont'),
            },
        },
        devtool: isDevelopment ? 'eval-cheap-module-source-map' : undefined,
    },
    // 预定义变量
    css: {
        loaderOptions: {
            scss: {
                additionalData: `@import '~styles/variables.scss';`,
            },
        },
        extract: { // 开发环境不建议使用此模式
            ignoreOrder: true,
        },
    },
};
