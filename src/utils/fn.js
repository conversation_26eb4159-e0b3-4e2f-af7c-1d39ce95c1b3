// fix CFD-4940
import { markInfo } from './info';

export const throttleScroll = (action, delay) => {
    let flag = false;
    return function() {
        const context = this;
        const args = arguments;
        let timer = null;
        if (flag) {
            timer = setTimeout(() => {
                flag = false;
                action.apply(context, args);
            }, delay);
        } else {
            flag = true;
            if (!timer) {
                action.apply(context, args);
            } else {
                timer = setTimeout(() => {
                    flag = false;
                    action.apply(context, args);
                }, delay);
            }
        }
    };
};
export const throttle = (action, delay) => {
    let flag = false;
    return function() {
        const context = this;
        const args = arguments;
        if (flag) {
            return;
        }
        flag = true;
        action.apply(context, args);
        setTimeout(() => {
            flag = false;
        }, delay);
    };
};
export const debounce = (action, delay) => {
    let timeout = null;
    return function() {
        const context = this;
        const args = arguments;
        if (timeout !== null) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(() => {
            action.apply(context, args);
            clearTimeout(timeout);
        }, delay);
    };
};
// 生成格式为'2018年12月05日'的日期
export function dateGenerator() {
    const date = new Date();
    const dateNum = date.getDate();
    const monthNum = date.getMonth() + 1;
    const Y = date.getFullYear();
    const M = monthNum < 10 ? `0${monthNum}` : `${monthNum}`;
    const D = dateNum < 10 ? `0${dateNum}` : `${dateNum}`;
    return `${Y}-${M}-${D}`;
}

/**
 * @param  {Object} mark 标签对象
 * @param  {Number} pageWidth 页高
 * @param  {Number} pageHeight 页宽
 * @return {Object}  返回标签左下角以页面左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,标签宽高占页面宽高的百分比width,height
 * @desc  标签坐标转换，换成百分比传给服务端
 */
export function markCoordinateTransform(mark, pageWidth, pageHeight) {
    // fix CFD-4940: 签章位置为0校验
    const markHeight = mark.height || markInfo(mark.type).height;
    const markWidth = mark.width || markInfo(mark.type).width;
    let x = (mark.x) / pageWidth;
    let y = 1 - (mark.y + Math.round(markHeight)) / pageHeight;
    const width = Math.round(markWidth) / pageWidth;
    const height = Math.round(markHeight) / pageHeight;
    // 有时候计算结果会为负
    if (y < 0) {
        y = 0;
    }
    if (x < 0) {
        x = 0;
    }
    if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(mark.type)) {
        return  { x, y, width, height, buttons: markButtonsCoordinateTransform(mark.buttons, pageWidth, pageHeight, height) };
    }
    return { x, y, width, height };
}
// 由百分比转为数值，与上面的 markCoordinateTransform 相反
export function coordinateReversal(mark, pageWidth, pageHeight) {
    // 标签宽高如果是百分比的转换为整数数值
    const width = mark.width <= 1 ? Math.round(mark.width * pageWidth) : mark.width;
    const height = mark.height <= 1 ?  Math.round(mark.height * pageHeight) : mark.height;
    // 旧数据坐标值还是数值，不做转换
    const x = mark.x < 1 ? mark.x * pageWidth : mark.x;
    const y = mark.y < 1 ? (1 - mark.y) * pageHeight - height : mark.y;
    if (mark.type && ['MULTIPLE_BOX', 'SINGLE_BOX', 'CONFIRMATION_REQUEST_SEAL'].includes(mark.type)) {
        return  { x, y, width, height, buttons: markButtonsCoordinateReversal(mark.buttons, pageWidth, pageHeight, height, mark.type) };
    }
    return { x, y, width, height };
}

/**
 * @param  {Object} button 复选框，单选框按钮
 * @param  {Number} pageWidth 页高
 * @param  {Number} pageHeight 页宽
 * @return {Object}  返回复选框，单选框按钮以标签左下角为原点的直角坐标系在第一象限的百分比(基于页面宽高)坐标值x,y,按钮宽高占页面宽高的百分比width,height
 * @desc  标签坐标转换，换成百分比传给服务端
 */

export function markButtonsCoordinateTransform(buttons, pageWidth, pageHeight, height) {
    const newButtons = [];
    buttons.forEach(item => {
        newButtons.push({
            ...item,
            buttonX: item.buttonX / pageWidth,
            buttonY: height - (item.buttonY + 28) / pageHeight,
        });
    });
    return newButtons;
}

// 由百分比转为数值，与上面的 markButtonsCoordinateTransform 相反
export function markButtonsCoordinateReversal(buttons, pageWidth, pageHeight, height, type) {
    const buttonHeight = type === 'CONFIRMATION_REQUEST_SEAL' ? 203 : 28;
    const newButtons = [];
    buttons.forEach(item => {
        newButtons.push({
            ...item,
            buttonX: item.buttonX * pageWidth,
            buttonY: height - (item.buttonY * pageHeight + buttonHeight),
        });
    });
    return newButtons;
}

// 节流队列
export const throttleQueue = {
    queue: [],
    timer: null,
    execute: function(param = { func: function() {} }, division) {
        // 立即执行并清空队列
        if (division === 0) {
            param.func();
            this.reset();
            return;
        }

        // 没传参的属于自执行
        if (!arguments.length) {
            // 队列中没有更新的任务，重置队列和定时器
            if (!this.queue.length) {
                this.reset();
                return;
            } else {
                // 执行最新的任务
                this.getLastTask().param.func();
                // 清理历史任务，定时器继续检测
                this.clearQueue();
                return;
            }
        }

        this.queue.push({ param });

        // 定时器未执行时，立即执行并启动定时器
        if (!this.timer) {
            // this.getLastTask().param.func();
            this.startTimer(division);
        }
    },
    getLastTask: function() {
        return this.queue.pop();
    },
    startTimer: function(division = 500) {
        // division在队列中不支持多个定时频率
        this.timer = setInterval(() => {
            this.execute();
        }, division);
    },
    clearQueue: function() {
        this.queue = [];
    },
    clearTimer: function() {
        clearInterval(this.timer);
        this.timer = null;
    },
    reset: function() {
        this.clearQueue();
        this.clearTimer();
    },
};
