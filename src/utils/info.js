import i18n from 'src/lang';
import store from 'src/store';

export const A4_PAGE_WIDTH = 595;
export const A4_PAGE_HEIGHT = 841;
export const ORIGIN_TEMPLATE_PIC_WIDTH = 793;

export const FLOAT_TYPES = ['SIGNATURE', 'SEAL', 'DATE'];
export const SELECT_TYPES = ['SINGLE_BOX', 'MULTIPLE_BOX'];
export const COLOR_TYPES = ['SIGNATURE', 'SEAL', 'DATE', 'WATERMARK', 'DECORATE_RIDING_SEAL'];

const SCALE_VAL = A4_PAGE_WIDTH / ORIGIN_TEMPLATE_PIC_WIDTH;

/**
 * 返回标签信息的常量
 * 指定位置页、模板指定位置页、签署页均有引用
 * @param  {[String]} type [description]
 * @return {[Object]}
 */
const sealStyle = store.getters.getIsForeignVersion ? {
    type: 'el-icon-ssq-gaizhang',
    width: 133,
    height: 133,
    name: i18n.t('localCommon.seal'),
} : {
    type: 'el-icon-ssq-gaizhang',
    width: 222, // 226
    height: 203, // 211
    name: i18n.t('localCommon.seal'),
};
const signatureStyle =
// store.getters.getIsForeignVersion ? {
//     type: 'el-icon-ssq-gaizhang',
//     width: 96,
//     height: 96,
//     name: i18n.t('localCommon.seal'),
// } :
 {
     type: 'el-icon-ssq-qianming',
     width: 134, // 140
     height: 71, // 74
     name: i18n.t('localCommon.signature'),
 };
export function markInfo(type) { // 96dpi
    let style = null;
    switch (type) {
        case 'SEAL':
            style = sealStyle;
            break;
        case 'SIGNATURE':
            style = signatureStyle;
            break;
        case 'DATE':
            style = {
                width: 134, // 140
                height: 34, // 35
                name: i18n.t('field.signDate'),
            };
            break;
        case 'TEXT':
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('field.text'),
            };
            break;
        case 'NUMERIC_VALUE': // 数值
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('field.number'),
            };
            break;
        case 'BIZ_DATE':
            style = {
                width: 77, // 80
                height: 20, // 20
                name: i18n.t('field.date'),
            };
            break;
        case 'QR_CODE':
            style = {
                width: 110, // 110
                height: 127, // 127
                name: i18n.t('field.qrCode'),
            };
            break;
        case 'TEXT_NUMERIC':
            style = {
                width: 77, // 110
                height: 22, // 127
                name: i18n.t('field.number'),
            };
            break;
        case 'DYNAMIC_TABLE':
            style = {
                width: 134,
                height: 24,
                name: i18n.t('field.dynamicTable'),
            };
            break;
        case 'TERM':
            style = {
                width: 134,
                height: 24,
                name: i18n.t('field.terms'),
            };
            break;
        case 'MULTIPLE_BOX':
            style = {
                width: 46,
                height: 76,
                name: i18n.t('field.checkBox'),
                button: {
                    width: 26,
                    height: 26,
                    split: 5,
                    initSplit: 10,
                },
            };
            break;
        case 'SINGLE_BOX':
            style = {
                width: 46,
                height: 76,
                name: i18n.t('field.radioBox'),
                button: {
                    width: 26,
                    height: 26,
                    split: 5,
                    initSplit: 10,
                },
            };
            break;
        case 'PICTURE': {
            style = {
                width: 65,
                height: 26,
                name: i18n.t('field.image'),
            };
            break;
        }
        case 'CONFIRMATION_REQUEST_SEAL':
            style = {
                width: 46,
                height: 76,
                name: '询证章',
                button: {
                    width: 222, // 226
                    height: 203, // 211
                    split: 5,
                    initSplit: 10,
                },
            };
            break;
        default:
            style = {
                width: 0,
                height: 0,
                name: '',
            };
    }
    return style;
}

/**
 * saas-513
 * 暂时仅在 tempFieldDoc.vue 中使用
 * 用于拖拽自定义标签时，自适应宽高
 * @param  {[Object]} mark [标签对象]
 * @return {[Object]}
 */
export function textMarkInfo(mark) {
    if (mark.type !== 'TEXT' && mark.type !== 'DATE' && mark.type !== 'BIZ_DATE' && mark.type !== 'TEXT_NUMERIC') {
        return {
            width: 0,
            height: 0,
            name: '',
        };
    }

    // 标签真实宽高，字体大小 * 字数
    let realMarkWidth = mark.fontSize * (mark.name ? mark.name.length : 4);
    // 高度暂时不知道为什么要加 6px ，只不过加上 6px 就等于原来的值了
    const realMarkHeight = mark.fontSize + 6;

    if (mark.type === 'DATE') {
        // 日期标签
        realMarkWidth = mark.fontSize * 6;
    }
    return {
        // 最小宽高为 77 20
        width: (realMarkWidth > 77) ? realMarkWidth : 77, // 80
        height: (realMarkHeight > 20) ? realMarkHeight : 20, // 20
        name: i18n.t('field.text'),
    };
}

export function markIconInfo(type) {
    let style = null;
    switch (type) {
        case 'SEAL':
            style =  {
                type: i18n.t('lang') === 'zh' ? 'el-icon-ssq-gaizhang1' : (i18n.t('lang') === 'en' ? 'el-icon-ssq-seal' : 'el-icon-ssq-icon-test1'),
            };
            break;
        case 'SIGNATURE':
            style = {
                type: {
                    zh: 'el-icon-ssq-qianzi',
                    en: 'el-icon-ssq-qianziEn',
                    ja: 'el-icon-ssq-qianziJP',
                }[i18n.locale],
            };
            break;
    }
    return style;
}

/*
 * markInfo中的宽高是基于图片宽度793px设定的，最终转换为pdf时，是基于A4的宽高展示。
 * 当前计算的字段宽高像素值会保存在后端，用到最终的展示当中，所以这里计算出来的宽高实际上是按最终pdf的宽高做比例计算出来的。
 */
export function newMarkSizeInfo(type) {
    return {
        width: markInfo(type).width * SCALE_VAL,
        height: markInfo(type).height * SCALE_VAL,
    };
}
