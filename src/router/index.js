import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/agent',
    name: 'agent',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: () => import(/* webpackChunkName: "about" */ '../views/agent/index.vue'),
            children: [{
                path: 'chat/:topicId',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/agent/chat/index.vue'),
                meta: {
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'shareChat/:topicId',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/agent/chat/index.vue'),
                meta: {
                    isSharePage: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'upload',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/agent/upload/index.vue'),
                meta: {
                    isUploadPage: true,
                    hasSlider: true,
                    isHubblePage: true,
                    isHubbleNoLoginPage: true,
                },
            }, {
                path: 'share/:token',
                component: () => import(/* webpackChunkName: "hubble" */ 'views/agent/share/index.vue'),
                meta: {
                    isSharePage: true,
                    noLogin: true,
                    isHubblePage: true,
                },
            }],
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
