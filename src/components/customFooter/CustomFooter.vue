<!-- 定制二级域名展示的特定footer -->
<template>
    <footer class="custom-footer">
        <ul class="clear font-size-zero">
            <li><img src="~img/customFooterLogo.png" width="142" alt="$t('commonFooter.ssqLogo')"></li>
            <li>
                <span><!-- 电子签约服务由 -->{{ $t('commonFooter.provideTip') }}<strong><!-- 上上签 -->{{ $t('commonFooter.ssq') }}</strong><!-- 提供 -->{{ $t('commonFooter.provide') }}</span>
                <i>|</i>
            </li>
            <li class="lang-switch-btn">
                <LangSwitch></LangSwitch>
                <i>|</i>
            </li>
            <li v-if="lang === 'zh'">
                <span><!-- 签约服务热线 -->{{ $t('commonFooter.signHotline') }}：400-993-6665</span>
                <i>|</i>
            </li>
            <li>
                <span>{{ $t('commonFooter.record') }}</span>
            </li>
        </ul>
    </footer>
</template>
<script>
import LangSwitch from 'components/langSwitch';
export default {
    components: {
        LangSwitch,
    },
    data() {
        return {};
    },
    computed: {
        lang() {
            return this.$i18n.locale;
        },
    },
};
</script>
<style lang="scss">
$border-color: #ddd;
footer.custom-footer {
    box-sizing: border-box;
    width: 100%;
    height: 35px;
    // line-height: 35px;
    padding-top: 7px;
    // padding-bottom: 15px;
    border-top: 1px solid $border-color;
    background-color: #f6f6f6;
    ul {
        display: block;
        width: 100%;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        li {
            display: inline-block;
            vertical-align: text-bottom;
            font-size: 12px;
            color: #666;
            img{
                margin-right: 15px;
                vertical-align: middle;
                [dir="rtl"] & {
                    margin-right: 0;
                    margin-left: 15px;
                }
            }
            i {
                display: inline-block;
                margin: 0 10px;
                color: #d6d6d6;
            }
            strong{
                color: #333;
            }
            &.lang-switch-btn {
                span {
                    font-size: 12px;
                    color: #999999;
                }
                i.el-icon-ssq-diqiu {
                    margin: 0;
                    padding-right: 5px;
                    vertical-align: bottom;
                    [dir="rtl"] & {
                        padding-right: 0;
                        padding-left: 5px;
                    }
                }
            }
        }
    }
}
</style>
