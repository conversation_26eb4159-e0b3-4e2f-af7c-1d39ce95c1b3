import Cookies from 'js-cookie';

// 判断是否是服务端 通过协议为 https/http 来区分
export const isServer = () => {
    return window.location.protocol === 'https:';
};

Number.isInteger = Number.isInteger || function(value) {
    return typeof value === 'number' &&
        isFinite(value) &&
        Math.floor(value) === value;
};

export const VueCookie = {
    defaultOption: {},

    install: function(Vue) {
        Vue.prototype.$cookie = this;
        Vue.$cookie = this;
    },

    set: function(name, value, daysOrOptions) {
        let opts = daysOrOptions === void (0) ? {} : daysOrOptions;
        if (Number.isInteger(daysOrOptions)) {
            opts = { ...this.defaultOption, expires: daysOrOptions, secure: isServer() ? true : null };
        } else {
            opts = { ...this.defaultOption, ...opts, secure: isServer() ? true : null };
        }
        return Cookies.set(name, value, opts);
    },

    get: function(name) {
        return Cookies.get(name);
    },

    delete: function(name, options) {
        let opts = { expires: -1 };
        if (options !== undefined) {
            opts = Object.assign(options, opts);
        }
        this.set(name, '', opts);
    },
};
