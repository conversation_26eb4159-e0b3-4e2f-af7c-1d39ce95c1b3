import { VueCookie, isServer } from './cookie.js';
import Fingerprint2 from 'fingerprintjs2';
import { isSafari, isChrome, getBroswer } from 'utils/device.js';

// 浏览器指纹
function writeFingerprint() {
    new Fingerprint2().get(function(result) {
        const fingerPrint = result;
        VueCookie.set('browser_fingerprint', fingerPrint);
    });
}

// 预设 cookie option
function cookieOptionPreset() {
    // production 环境，判断 iframe 下、非 safari 、非chrome版本小于80的浏览器预设 sameSite
    const isProduction = process.env.NODE_ENV === 'production';
    const inFrame = window.top !== window.self;
    const isNotSafari = !isSafari();
    const isChromePrevVersion = isChrome() && parseInt(getBroswer().version) < 80;

    if (
        isProduction &&
        inFrame &&
        isNotSafari
    ) {
        if (isChromePrevVersion) {
            return false;
        }
        VueCookie.defaultOption = { sameSite: 'none', secure: isServer() ? true : null };
    }
}

function Cookie() {
    cookieOptionPreset();
    writeFingerprint();
    Vue.use(VueCookie);
}

export default Cookie;
