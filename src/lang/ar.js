
// الترجمة للغة العربية، حالياً تعمل فقط عند فتح الروابط القصيرة عبر الجوال
import utils from './module/utils/utils-ar.js';
import components from './module/components/components-ar.js';

import console from './module/console/console-ar.js';
import docList from './module/docList/docList-ar.js';
import home from './module/home/<USER>';
import sign from './module/sign/sign-ar.js';
import entAuth from './module/entAuth/entAuth-ar.js';
import docTranslation from './module/docTranslation/docTranslation-ar.js';
import consts from '@/lang/module/consts/ar';

export default {
    ...utils,
    ...components,
    ...docTranslation,
    fileService: 'أرشيف+',
    createSuccessful: 'تم الإنشاء بنجاح',
    setLabel: 'تعيين التصنيف',
    footerAd: {
        title: 'تنبيه انتقال الصفحة',
        content1: 'أنت على وشك زيارة صفحة ترويجية تابعة لطرف ثالث',
        content2: 'هل تريد المتابعة؟',
        bankContent: 'ستدخل قريباً إلى صفحة تعريف قروض الشركات "قرض سهل" من بنك نينغبو',
        bankTip1: 'السماح لبنك نينغبو بالاتصال بي هاتفياً',
        bankTip2: 'إرسال رسالة نصية لي تشرح كيفية التقديم',
        bankFooter: 'إضافة خدمة عملاء بنك نينغبو المخصصة للخدمة الشخصية',
        cancel: 'إلغاء',
        continue: 'متابعة',
    },
    commonFooter: {
        record: 'رقم سجل ICP الرئيسي: تشجيانغ ICP رقم ********',
        hubbleRecordId: 'سجل شبكة المعلومات: 330106973391501230011',
        openPlatform: 'منصة مفتوحة',
        aboutBestSign: 'عن بست ساين',
        contact: 'اتصل بنا',
        recruitment: 'التوظيف',
        help: 'مركز المساعدة',
        copyright: 'حقوق النشر',
        company: 'هانغتشو بست ساين المحدودة',
        ssqLogo: 'شعار بست ساين في الشريط السفلي',
        provideTip: 'خدمة التوقيع الإلكتروني مقدمة من',
        ssq: ' بست ساين',
        provide: '',
        signHotline: 'الخط الساخن للخدمة',
        langSwitch: 'اللغة',
    },
    login: {
        pswLogin: 'تسجيل الدخول بكلمة المرور',
        usePswLogin: 'تسجيل الدخول باستخدام كلمة المرور',
        verifyLogin: 'رمز التحقق',
        useVerifyLogin: 'تسجيل الدخول برمز التحقق',
        scanLogin: 'تسجيل الدخول بالمسح الضوئي',
        scanFailure: 'انتهت صلاحية رمز QR، يرجى المحاولة مرة أخرى',
        scanSuccess: 'تم المسح بنجاح',
        scanLoginTip: 'يرجى استخدام التطبيق للمسح وتسجيل الدخول',
        appLoginTip: 'يرجى النقر على تسجيل الدخول في تطبيق بست ساين',
        downloadApp: 'تنزيل تطبيق بست ساين',
        forgetPsw: 'نسيت؟',
        login: 'تسجيل الدخول',
        noAccount: 'ليس لديك حساب؟',
        registerNow: 'التسجيل الآن',
        accountPlaceholder: 'رقم الهاتف أو البريد الإلكتروني',
        passwordPlaceholder: 'كلمة المرور',
        pictureVer: 'يرجى ملء المحتوى في الصورة',
        verifyCodePlaceholder: 'يرجى إدخال رمز مكون من 6 أرقام',
        getVerifyCode: 'إرسال',
        noRegister: 'غير مسجل بعد',
        or: 'أو',
        errAccountOrPwdTip: 'كلمة المرور التي أدخلتها لا تتطابق مع رقم الحساب؟',
        errAccountOrPwdTip2: 'كلمة المرور التي أدخلتها لا تتطابق مع الحساب',
        errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',
        errPwd: 'يرجى إدخال كلمة المرور الصحيحة!',
        verCodeFormatErr: 'خطأ في رمز التحقق',
        grapVerCodeErr: 'خطأ في رمز التحقق الرسومي',
        grapVerCodeFormatErr: 'خطأ في تنسيق رمز التحقق الرسومي',
        lackAccount: 'يرجى إدخال الحساب أولاً',
        lackGrapCode: 'يرجى ملء رمز التحقق الرسومي أولاً.',
        getVerCodeTip: 'يرجى الحصول على رمز التحقق',

        loginView: 'تسجيل الدخول وعرض العقد',
        regView: 'التسجيل وعرض العقد',
        takeViewBtn: 'تسجيل الدخول والتوقيع',
        resendCode: 'إعادة الإرسال',
        regTip: 'بعد ملء رمز التحقق الصحيح، ستقوم بست ساين بإنشاء حساب لك',
        haveRead: 'لقد قرأت ووافقت على',
        bestsignAgreement: 'اتفاقية خدمة بست ساين',
        and: 'و',
        digitalCertificateAgreement: 'اتفاقية استخدام الشهادة الرقمية',
        privacyPolicy: 'سياسة الخصوصية',
        sendSuc: 'تم الإرسال بنجاح',
        lackVerCode: 'يرجى إدخال رمز التحقق أولاً',
        lackPsw: 'يرجى إدخال كلمة المرور أولاً',
        notMatch: 'كلمة المرور والحساب المدخلان غير متطابقين',
        cookieTip: 'غير قادر على قراءة وكتابة ملفات تعريف الارتباط، يرجى التحقق من عدم وجود وضع التصفح المتخفي أو تعطيل ملفات تعريف الارتباط',
        wrongLink: 'رابط غير صالح',
        footerTips: 'خدمة التوقيع الإلكتروني مقدمة من <span>بست ساين</span>',
        bestSign: 'بست ساين',
        bestSignDescription: 'رائد في صناعة التعاقدات الإلكترونية',
        /** نسيت كلمة المرور /forgotPassword start */
        forgetPswStep: 'التحقق من الحساب المسجل | إعادة تعيين كلمة المرور',
        pictureVerCodeInput: 'رمز التحقق الرسومي | يرجى ملء محتويات الصورة',
        accountInput: 'الحساب | يرجى إدخال حسابك',
        smsCodeInput: 'رمز التحقق | الحصول على رمز التحقق',
        haveRegistereLoginNow: 'تم التسجيل بالفعل، | تسجيل الدخول الآن',
        nextStep: 'التالي | إرسال',
        setNewPasswordInput: 'تعيين كلمة مرور جديدة | 6-18 رقم أو حرف',
        passwordResetSucceeded: 'تمت إعادة تعيين كلمة المرور بنجاح',
        /** نسيت كلمة المرور /forgotPassword end */
        accountNotRegistered: 'الحساب غير مسجل',
        loginAndDownload: 'تسجيل الدخول وتنزيل العقد',
        registerAndDownload: 'التسجيل وتنزيل العقد',
        inputPhone: 'يرجى إدخال رقم الهاتف',
        readContract: 'قراءة العقد',
        errorPhone: 'خطأ في تنسيق رقم الهاتف المحمول',
        companyCert: 'إجراء تصديق الشركة',
        regAndCompanyCert: 'التسجيل وإجراء تصديق الشركة',
    },
    ...sign,
    handwrite: {
        title: 'مباشرة على الشاشة',
        picSubmitTip: 'تم تقديم صورة التوقيع بنجاح',
        settingDefault: 'تعيين كافتراضي',
        replaceAllSignature: 'استخدام لجميع التوقيعات',
        replaceAllSeal: 'لجميع الأختام',
        canUseSeal: 'أختامي',
        applyForSeal: 'تقديم طلب لاستخدام الأختام',
        moreTip: 'سيتم حفظ توقيعك اليدوي كتوقيع افتراضي، ويستخدم فقط لتوقيع العقود. مسار الإدارة: [مركز المستخدم <- إدارة التوقيعات]',
        uploadPic: 'تحميل صورة',
        use: 'استخدام',
        clickExtend: 'انقر على السهم الأيمن لتوسيع المنطقة',
        rewrite: 'إعادة الكتابة',
        upload: 'تحميل صورة التوقيع',
        uploadTip1: 'تنبيه: عند تحميل صورة التوقيع، يرجى ملء الصورة بالكامل بالتوقيع',
        uploadTip2: 'يرجى استخدام ألوان داكنة أو نص أسود خالص للتوقيع.',
        cancel: 'إلغاء',
        confirm: 'تأكيد',
        upgradeBrowser: 'المتصفح لا يدعم الكتابة اليدوية، يرجى ترقية متصفحك.',
        submitTip: 'تم تقديم التوقيع اليدوي بنجاح',
        needWrite: 'يرجى كتابة اسمك أولاً',
        needRewrite: 'لا يمكن التعرف على التوقيع، يرجى المحاولة مرة أخرى',
        title2: 'رسم توقيعك باليد',
        QRCode: 'عن طريق مسح رمز QR',
        ok: 'موافق',
        clearTips: 'يرجى كتابة توقيع واضح يمكن التعرف عليه',
        isBlank: 'اللوحة فارغة، يرجى رسم التوقيع يدوياً قبل الإرسال!',
        success: 'تم تقديم التوقيع اليدوي بنجاح',
        signNotMatch: 'يرجى كتابة توقيعك بأحرف كبيرة والحفاظ على توافقه مع معلومات هويتك الحقيقية.',
        signNotMatchExact: 'الكلمة {numList} لا تتطابق، يرجى إعادة الكتابة',
        msg: {
            successToUser: 'تم تفعيل التوقيع الجديد، يرجى الانتقال إلى مركز المستخدم على الويب - إدارة التوقيع',
            successToSign: 'تم تفعيل التوقيع الجديد، يرجى التحقق من صفحة توقيع العقد',
            cantGet: 'لا يمكن الحصول على توقيع، جرب استخدام متصفح مختلف!',
        },
    },
    common: {
        aboutBestSign: 'عن بست ساين',
        contact: 'اتصل بنا',
        recruitment: 'التوظيف',
        copyright: 'حقوق النشر',
        advice: 'نصائح',
        notEmpty: 'لا يمكن أن يكون فارغاً',
        enter6to18n: 'يرجى إدخال 6-18 رقماً وحرفاً',
        ssqDes: 'رائد في منصات التوقيع الإلكتروني السحابية',
        openPlatform: 'منصة مفتوحة',
        company: 'هانغتشو بست ساين المحدودة',
        help: 'مركز المساعدة',

        errEmailOrTel: 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',
        verCodeFormatErr: 'خطأ في رمز التحقق',
        signPwdType: 'يرجى إدخال 6 أرقام',
        enterActualEntName: 'يرجى إدخال اسم العمل الحقيقي',
        enterCorrectName: 'يرجى إدخال اسم العمل الصحيح',
        enterCorrectPhoneNum: 'يرجى إدخال رقم الهاتف المحمول الصحيح',
        enterCorrectEmail: 'يرجى إدخال البريد الإلكتروني الصحيح',
        imgCodeErr: 'خطأ في رمز التحقق',
        enterCorrectIdNum: 'يرجى إدخال رقم الهوية الصحيح',
        enterCorrectFormat: 'يرجى إدخال التنسيق الصحيح',
        enterCorrectDateFormat: 'يرجى إدخال تنسيق التاريخ الصحيح',
    },
    entAuth: {
        ...entAuth,
        entCertification: 'تصديق الاسم الحقيقي للمؤسسة',
        subBaseInfo: 'تقديم المعلومات الأساسية',
        corDocuments: 'وثائق الشركة',
        license: 'الترخيص',
        upload: 'انقر للتحميل',
        uploadLimit: 'الصور متاحة فقط بتنسيقات jpeg وjpg وpng وحجم لا يزيد عن 10 ميجابايت',
        hi: 'مرحباً',
        exit: 'خروج',
        help: 'مساعدة',
        hotline: 'الخط الساخن للخدمة',
        acceptProtectingMethod: 'أقبل طريقة حماية المعلومات الشخصية التي أقدمها',
        comfirmSubmit: 'تأكيد الإرسال',
        cerficated: 'اكتمل التصديق',
        entName: 'اسم المؤسسة',
        serialNumber: 'الرقم التسلسلي',
        validity: 'الصلاحية',
        nationalNo: 'رقم التسجيل الوطني',
        corporationName: 'اسم الممثل القانوني',
        city: 'المدينة',
        entCertificate: 'شهادة الاسم الحقيقي للمؤسسة',
        certificationAuthority: 'هيئة التصديق',
        bestsignPlatform: 'منصة بست ساين السحابية للتوقيع الإلكتروني',
        notIssued: 'لم يتم إصدارها',
        date: '{year}-{month}-{day}',
        congratulations: 'تهانينا على إتمام عملية تصديق الاسم الحقيقي للمؤسسة بنجاح',
        continue: 'متابعة',
        rejectMessage: 'للأسباب التالية، لم يتم اجتياز تدقيق البيانات، يرجى التحقق',
        recertification: 'إعادة التصديق',
        waitMessage: 'ستتم المراجعة من قبل خدمة العملاء خلال يوم عمل واحد، يرجى الانتظار',
    },
    personalAuth: {
        info: 'معلومات',
        submitPicError: 'يرجى تحميل الصورة قبل الاستخدام',
    },
    home: {
        ...home,
        home: 'الرئيسية',
        contractDrafting: 'صياغة العقود',
        contractManagement: 'العقود',
        userCenter: 'الإدارة',
        service: 'الخدمة',
        enterpriseConsole: 'لوحة تحكم المؤسسة',
        groupConsole: 'لوحة تحكم المجموعة',
        startSigning: 'بدء التوقيع',
        contractType: 'عقد عادي | عقد قالب',
        sendContract: 'ابدأ الآن',
        shortcuts: 'الاختصارات | لا توجد اختصارات لأي مستندات',
        setting: 'الإعداد فوراً | إنشاء المزيد من الاختصارات',
        signNum: 'ملخص شهري للتوقيعات والتسليمات',
        contractNum: 'العقود المرسلة | العقود الموقعة',
        contractInFormation: 'لم ترسل أو توقع أي عقود هذا الشهر',
        type: 'شركة | شخص ',
        basicInformation: 'معلومات أساسية',
        more: 'المزيد',
        certified: 'مصدق | غير مصدق',
        account: 'الحساب',
        time: 'وقت الإنشاء | وقت التسجيل',
        day: 'يوم | شهر',
        sendContractNum: 'التسليمات | التوقيعات',
        num: '',
        realName: 'إنشاء حساب أعمال باسم حقيقي الآن | إنشاء حساب فردي باسم حقيقي الآن',
        update: 'تحديث المنتج',
        mark: 'هل ترغب في التوصية ببست ساين لأصدقائك وزملائك؟ يرجى تقييم اختيارك من 0 إلى 10.',
        countDes: {
            1: 'متاح: لعقد المؤسسة',
            2: '',
            3: 'للعقد الخاص',
            4: '',
        },
        chargeNow: 'الشحن الآن',
        myRechargeOrder: 'طلب إعادة الشحن الخاص بي',
        statusTip: {
            1: 'بحاجة إلى تشغيلي',
            2: 'يحتاج الآخرون إلى التوقيع',
            3: 'التوقيع على وشك الإغلاق',
            4: 'اكتمل التوقيع',
        },
        useTemplate: 'استخدام القالب',
        useLocalFile: 'تحميل ملف محلي',
    },
    docDetail: {
        canNotOperateTip: 'غير قادر على {operate} العقد',
        shareSignLink: 'مشاركة رابط التوقيع',
        faceSign: 'التوقيع بمسح الوجه',
        faceFirstVerifyCodeSecond: 'التوقيع ذو الأولوية بالتحقق من الوجه، التوقيع البديل بالتحقق من رمز الرسائل القصيرة',
        contractRecipient: 'مستلم العقد',
        personalOperateLog: 'سجل عمليات العقد الفردي',
        recordDialog: {
            date: 'التاريخ',
            user: 'المستخدم',
            operate: 'العملية',
            view: 'عرض',
            download: 'تنزيل',
        },
        remarks: 'ملاحظات',
        operateRecords: 'سجل العمليات',
        borrowingRecords: 'سجلات الاقتراض',
        currentHolder: 'المالك الحالي',
        currentEnterprise: 'الشركة تحت الحساب الحالي',
        companyInterOperationLog: 'سجل العمليات الداخلية للشركة',
        receiverMap: {
            sender: 'مرسل العقد',
            signer: 'مستلم العقد',
            ccUser: 'العقد منسوخ إلى',
        },
        downloadCode: 'رمز تنزيل العقد',
        noTagToAddHint: 'لا توجد علامات بعد؛ يرجى الذهاب إلى لوحة التحكم التجارية لإضافتها',
        requireFieldNotAllowEmpty: 'لا يمكن أن تكون الحقول المطلوبة فارغة',
        modifySuccess: 'تم التعديل بنجاح',
        uncategorized: 'غير مصنف',
        notAllowModifyContractType: 'لا يُسمح بتعديل نوع العقد للعقد تحت {type}',
        setTag: 'تعيين العلامات',
        contractTag: 'علامات العقد',
        plsInput: 'يرجى الإدخال',
        plsInputCompanyInternalNum: 'يرجى إدخال رقم الأعمال الداخلي',
        companyInternalNum: 'رقم الأعمال الداخلي',
        none: 'لا شيء',
        plsSelect: 'يرجى الاختيار',
        modify: 'تعديل',
        contractDetailInfo: 'تفاصيل العقد',
        slideContentTip: {
            signNotice: 'تعليمات التوقيع',
            contractAncillaryInformation: 'مرفقات العقد',
            content: 'المحتوى',
            document: 'المستند',
        },
        downloadDepositConfirmTip: {
            title: 'صفحة إثبات التوقيع التي قمت بتنزيلها هي نسخة غير حساسة، مع إخفاء المعلومات الخاصة وغير قابلة للاستخدام في إجراءات المحكمة. إذا كنت بحاجة إلى استخدامها في إجراءات المحكمة، يرجى الاتصال بنا للحصول على النسخة الكاملة.',
            hint: 'نصائح',
            confrim: 'متابعة التنزيل',
            cancel: 'إلغاء',
        },
        downloadTip: {
            title: 'نظراً لأن العقد لم يكتمل بعد، فإنك تقوم بتنزيل ملف معاينة للعقد الذي لم يدخل حيز التنفيذ بعد',
            hint: 'نصائح',
            confirm: 'تأكيد',
            cancel: 'إلغاء',
        },
        transferSuccessGoManagePage: 'تم النقل بنجاح وسيتم العودة إلى صفحة إدارة العقود',
        claimSign: 'استرداد وتوقيع',
        downloadDepositPageTip: 'تنزيل صفحة إثبات التوقيع (نسخة غير حساسة)',
        resend: 'إعادة الإرسال',
        proxySign: 'توقيع مفوض',
        notPassed: 'مرفوض',
        approving: 'قيد المراجعة',
        signning: 'التوقيع قيد التقدم',
        notarized: 'موثق',
        currentFolder: 'المجلد الحالي',
        archive: 'تم أرشفة العقد',
        deadlineForSigning: 'الموعد النهائي للتوقيع',
        endFinishTime: 'اكتمل التوقيع/تاريخ الإتمام',
        contractImportTime: 'وقت استيراد العقد',
        contractSendTime: 'وقت تسليم العقد',
        back: 'رجوع',
        contractInfo: 'معلومات العقد',
        basicInfo: 'معلومات أساسية',
        contractNum: 'رقم العقد',
        sender: 'المرسل',
        personAccount: 'حساب شخصي',
        entAccount: 'حساب مؤسسة',
        operator: 'المشغل',
        signStartTime: 'وقت بدء التوقيع',
        signDeadline: 'الموعد النهائي للتوقيع',
        contractExpireDate: 'تاريخ انتهاء صلاحية العقد',
        // none: 'لا شيء',
        edit: 'تعديل',
        settings: 'إعداد',
        from: 'المصدر',
        folder: 'مجلد',
        contractType: 'نوع العقد',
        reason: 'السبب',
        sign: 'توقيع',
        approval: 'موافقة',
        viewAttach: 'عرض الصفحات المرفقة',
        downloadContract: 'تنزيل العقد',
        downloadAttach: 'تنزيل الصفحة المرفقة',
        print: 'طباعة',
        certificatedTooltip: 'تم توثيق العقد والأدلة ذات الصلة في السلسلة القضائية لمحكمة هانغتشو للإنترنت',
        needMeSign: 'بحاجة إلى توقيعي',
        needMeApproval: 'بحاجة إلى موافقتي',
        inApproval: 'قيد النظر...',
        needOthersSign: 'بحاجة إلى توقيع الآخرين',
        signComplete: 'اكتمل التوقيع',
        signOverdue: 'توقيع متأخر',
        rejected: 'مرفوض',
        revoked: 'ملغى',
        contractCompleteTime: 'وقت اكتمال التوقيع',
        contractEndTime: 'وقت انتهاء التوقيع',
        reject: 'رفض',
        revoke: 'إلغاء',
        download: 'تنزيل',
        viewSignOrders: 'عرض ترتيب التوقيع',
        viewApprovalProcess: 'عرض عملية الموافقة',
        completed: 'اكتمل التوقيع',
        cc: 'نسخة',
        ccer: 'طرف النسخة',
        signer: 'الموقع',
        signSubject: 'موضوع التوقيع',
        signSubjectTooltip: 'موضوع التوقيع الذي ملأه المرسل هو',
        user: 'المستخدم',
        IDNumber: 'رقم الهوية',
        state: 'الحالة',
        time: 'الوقت',
        notice: 'تذكير',
        detail: 'التفاصيل',
        RealNameCertificationRequired: 'مطلوب التصديق بالاسم الحقيقي',
        RealNameCertificationNotRequired: 'لا يلزم التصديق بالاسم الحقيقي',
        MustHandwrittenSignature: 'يجب التوقيع بخط اليد',
        handWritingRecognition: 'تفعيل التعرف على الكتابة اليدوية',
        privateMessage: 'رسالة',
        attachment: 'مرفق',
        rejectReason: 'السبب',
        notSigned: 'غير موقع',
        notViewed: 'غير مطلع عليه',
        viewed: 'تم الاطلاع',
        signed: 'تم التوقيع',
        viewedNotSigned: 'قرئ ولم يتم التوقيع',
        notApproval: 'غير معتمد',
        remindSucceed: 'تم إرسال رسالة التذكير',
        reviewDetails: 'تفاصيل الموافقة',
        close: 'إغلاق',
        entInnerOperateDetail: 'تفاصيل العمليات الداخلية',
        approve: 'موافقة',
        disapprove: 'رفض',
        applySeal: 'طلب الختم',
        applied: 'تم التقديم بالفعل',
        apply: 'تقديم',
        toOtherSign: 'نقل إلى شخص آخر للتوقيع',
        handOver: 'نقل',
        approvalOpinions: 'تعليقات الموافقة',
        useSeal: 'ختم',
        signature: 'توقيع',
        use: 'استخدام',
        date: 'تاريخ',
        fill: 'ملء',
        times: 'ثانوي',
        place: 'مكان',
        contractDetail: 'تفاصيل العقد',
        viewMore: 'عرض المزيد',
        collapse: 'طي',
        signLink: 'رابط التوقيع',
        saveQRCode: 'احفظ رمز QR أو انسخ الرابط وشاركه مع الموقع',
        signQRCode: 'رمز QR لرابط التوقيع',
        copy: 'نسخ',
        copySucc: 'تم النسخ بنجاح',
        copyFail: 'فشل النسخ',
        certified: 'مصدق',
        unCertified: 'غير مصدق',
        claimed: 'تم المطالبة',
    },
    uploadFile: {
        thumbnails: 'صورة مصغرة',
        isUploading: 'جاري التحميل',
        move: 'نقل',
        delete: 'حذف',
        replace: 'استبدال',
        tip: 'نصيحة',
        understand: 'فهمت',
        totalPages: '{page} في المجموع',
        uploadFile: 'تحميل ملف محلي',
        matchErr: 'يوجد خلل بسيط في الخادم، يرجى المحاولة مرة أخرى لاحقاً.',
        inUploadingDeleteErr: 'يرجى الحذف بعد التحميل',
        timeOutErr: 'انتهت مهلة الطلب',
        imgUnqualified: 'تنسيق الصورة لا يلبي المتطلبات',
        imgBiggerThan20M: 'لا يمكن أن يتجاوز حجم الصورة 20 ميجابايت!',
        error: 'خطأ',
        hasCATip: 'ملف PDF الذي قمت بتحميله يحتوي بالفعل على شهادة رقمية، وهذا سيؤثر على توحيد وتكامل سلسلة أدلة توقيع العقد، لا يُنصح المستخدمون الشخصيون باستخدامه. يرجى تحميل ملف PDF لا يحتوي على أي شهادة رقمية كملف عقد.',

    },
    contractInfo: {
        internalNumber: 'رقم العمل الداخلي',
        contractName: 'اسم العقد',
        contractNameTooltip: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة وألا يتجاوز 100 كلمة',
        contractType: 'نوع العقد',
        toSelect: 'يرجى الاختيار',
        contractTypeErr: 'تم حذف نوع العقد الحالي. يرجى إعادة اختيار نوع العقد.',
        signDeadLine: 'الموعد النهائي للتوقيع',
        signDeadLineTooltip: 'إذا لم يتم توقيع العقد قبل هذا التاريخ، فلا يمكن متابعته',
        selectDate: 'اختر التاريخ والوقت',
        contractExpireDate: 'تاريخ انتهاء العقد',
        expireDateTooltip: 'وقت انتهاء الصلاحية في محتويات العقد لإدارة العقد اللاحقة',
        necessary: 'ضروري',
        notNecessary: 'اختياري',
        dateTips: 'تم تحديد تاريخ انتهاء العقد تلقائيًا لك، يرجى التأكيد',
        contractTitleErr: 'يرجى ألا يحتوي اسم العقد على أحرف خاصة',
        contractTitleLengthErr: 'يرجى ألا يتجاوز طول اسم العقد 100 كلمة.',
    },
    template: {
        templateList: {
            linkBoxTip: 'معرف الخزانة المرتبطة:',
        },
        dynamicTemplateUpdate: {
            title: 'تم إطلاق وظيفة جديدة للقوالب الديناميكية',
            newVersionDesc: 'تدعم الوظيفة الجديدة عرض الرأس والتذييل وتحافظ على تخطيط صفحة المستند إلى أقصى درجة.',
            updateTip: 'لا تتم مزامنة ميزة القالب الديناميكي السابقة وتوافقها. مطلوب الترقية اليدوية. إذا تم تحرير القوالب الديناميكية التي تم إنشاؤها قبل 26 يناير، فلن يتم حفظ العقود أو إرسالها. يمكن إرسال العقود قبل 1 مارس 2021 إذا لم يتم تحرير القوالب. يُوصَى بالترقية في أقرب وقت ممكن. القوالب غير الديناميكية غير متأثرة.',
            connectUs: 'إذا كان لديك أي أسئلة، يرجى الاتصال عبر الخط الساخن 400-993-6665 أو الاتصال بخدمة العملاء عبر الإنترنت.',
        },
        sendCode: {
            tip: 'إعدادات القالب الحالية لا تلبي شروط إنشاء رمز الإرسال. تحقق مما إذا كانت المتطلبات التالية مستوفاة:',
            fail: {
                1: 'لا تتضمن مستندات فارغة',
                2: 'الطرف المتعاقد لديه طرف متغير واحد فقط (بما في ذلك التوقيع والنسخ)، ويجب أن يكون الطرف المتغير هو المشغل الأول؛ يجب أن يكون للموقع موضع ختم التوقيع',
                3: 'الحساب الثابت للطرف المتعاقد لا يجب أن يكون فارغًا',
                4: 'لا يؤدي إلى تفعيل الموافقة قبل الإرسال',
                5: 'المحتويات التي يجب على المرسل ملؤها ليست فارغة (بما في ذلك حقل الوصف وحقل محتوى العقد)',
                6: 'ليس مزيجًا من القوالب',
            },
        },
        sendCodeGuide: {
            title: 'شرح وظائف رمز الإرسال المتقدمة',
            info: 'رمز إرسال القالب مناسب لملفات العقود التي لا تتطلب من المرسل ملء المحتوى، مثل شهادات الاستقالة واتفاقيات السرية وخطابات التفويض، ويمكن توفيرها لأي شخص يقوم بمسح الرمز للحصول عليها. إذا كان هناك معلومات يجب على المرسل ملؤها في المستند، أو إذا كان المرسل بحاجة إلى تحديد نطاق الأطراف المقابلة التي يمكنها الحصول على المستند عبر مسح الرمز، يمكنك استخدام الوظائف المتقدمة لـ "أرشيف+". من خلال مسح رمز QR الخاص بخزانة الملفات، يمكنك إكمال إرسال العقد تلقائيًا إلى أشخاص محددين، وملء المحتوى الذي يحتاج المرسل إلى ملئه، وحتى التحكم في نقاط زمنية للإرسال التلقائي؛ كما يتم تخزين الطرف المقابل الذي مسح الرمز في خزانة الملفات، ويمكن الاستعلام عنه في أي وقت. طريقة الاستخدام كالتالي:',
            tip1: {
                main: '1. BestSign',
                sub: '',
                line1: 'تقديم طلب إلى BestSign لتفعيل أرشيف+، مراجعة العقد المسبقة، المراجعة الذكية',
                line2: 'بعد التفعيل يمكنك الذهاب إلى القوائم المقابلة للعمليات والاستخدام',
            },
            tip2: {
                main: '2. مدير خزانة الملفات',
                sub: 'إنشاء خزانة ملفات، تكوين المراجعة الذكية',
                line1: '',
                line2: 'قم بإنشاء حقول إدخال في خزانة الملفات تتطابق مع محتوى العقد، وربط قوالب العقد، وإعداد العلاقات وشروط الإرسال التلقائي عند مسح الرمز، وتقديم رمز QR لخزانة الملفات إلى الطرف المقابل للتوقيع.',
            },
            tip3: {
                main: '3. طرف التوقيع',
                sub: 'مسح الرمز لملء المعلومات، والحصول على ملفات العقد',
                line1: '',
                line2: 'يقوم طرف التوقيع بمسح رمز إرسال خزانة الملفات الذي يوفره المرسل للملء، وسيتم أرشفة المعلومات التي تم جمعها عبر خزانة الملفات وملؤها تلقائيًا في العقد، ويحتاج الطرف المقابل فقط إلى الختم أو التوقيع البسيط عند استلام العقد لإكمال توقيع العقد',
            },
            tip4: {
                main: '4. مدير خزانة الملفات',
                sub: '',
                line1: 'عرض معلومات الطرف المقابل للتوقيع وحالة العقود المرسلة',
                line2: 'يمكن لمدير الجهة المرسلة الذهاب إلى خزانة الملفات لعرض معلومات الطرف المقابل الذي قام بمسح الرمز، وحالة إرسال العقد، وما إذا كان قد تم إكمال التوقيع، وما إلى ذلك',
            },
        },
    },
    style: {
        signature: {
            text: {
                x: '0',
                fontSize: '18',
            },
        },
    },
    resetPwd: {
        title: 'تنبيهات الأمان!',
        notice: 'عامل الأمان لكلمة المرور منخفض ويوجد خطر أمني. يرجى إعادة تعيين كلمة المرور',
        oldLabel: 'كلمة المرور الأصلية',
        oldPlaceholder: 'يرجى إدخال كلمة المرور الأصلية',
        newLabel: 'كلمة المرور الجديدة',
        newPlaceholder: '6-18 رقمًا وأحرف كبيرة وصغيرة، تدعم الرموز الخاصة',
        submit: 'إرسال',
        errorMsg: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',
        oldRule: 'لا يمكن أن تكون كلمة المرور الأصلية فارغة',
        newRule: 'لا يمكن أن تكون كلمة المرور الجديدة فارغة',
        success: 'نجاح!',
    },
    personAuthIntercept: {
        title: 'ندعوك بـ',
        name: 'الاسم:',
        id: 'رقم الهوية:',
        descNoAuth: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',
        desMore: 'وفقًا لمتطلبات المبادر، تحتاج أيضًا إلى استكمال',
        descNoSame: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمبادر للتأكد وإعادة إنشاء العقد.',
        descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',
        descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',
        tips: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',
        goOn: 'أنا الشخص نفسه، ابدأ المصادقة',
        goMore: 'اذهب لاستكمال المصادقة',
        descNoSame1: ' هوية لتوقيع العقد',
        descNoSame2: 'هذا لا يتطابق مع معلومات الاسم الحقيقي المكتملة للحساب الذي قمت بتسجيل الدخول إليه حاليًا.',
        goHome: 'العودة إلى صفحة قائمة العقود>>',
        authInfo: 'تم اكتشاف أن هوية الاسم الحقيقي لحسابك الحالي هي ',
        in: 'في',
        finishAuth: 'أكمل مصادقة الاسم الحقيقي لتوقيع العقود بشكل متوافق',
        ask: 'هل رقم الهاتف المحمول الحالي هو رقم هاتفك المعتاد؟',
        reAuthBtnText: 'نعم، أريد استخدام هذا الحساب لإعادة المصادقة والتوقيع',
        changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',
        changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',
        changePhoneTip2: '، لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد أنك الموقع.',
        confirmReject: 'نعم، أريد رفض المصادقة باسم حقيقي',
    },
    authIntercept: {
        title: 'يُطلب منك أن:',
        name: 'الاسم:',
        id: 'رقم الهوية:',
        descNoAuth1: 'يرجى التأكد من أن معلومات الهوية أعلاه هي معلوماتك الشخصية، واستخدامها للمصادقة باسمك الحقيقي.',
        descNoAuth2: 'بعد اجتياز المصادقة باسمك الحقيقي، يمكنك عرض وتوقيع العقد.',
        descNoSame1: 'لتوقيع العقد.',
        descNoSame2: 'تم اكتشاف أن المعلومات المذكورة أعلاه لا تتطابق مع معلومات اسمك الحقيقي الحالية، يرجى الاتصال بالمُرسل للتأكد وإعادة إنشاء العقد.',
        tips: 'ملاحظة: يجب أن تتطابق معلومات الهوية تمامًا لتوقيع العقد',
        goOn: 'أنا الشخص نفسه، ابدأ المصادقة',
        goHome: 'فهمت',
        goMore: 'اذهب لاستكمال المصادقة',
        authTip: 'إجراء المصادقة باسم حقيقي.',
        viewAndSign: 'بعد إكمال المصادقة، يمكنك عرض وتوقيع العقد',
        tips2: 'ملاحظة: يجب أن يتطابق اسم الشركة تمامًا لعرض وتوقيع العقد.',
        requestOtherAnth: 'طلب التحقق من قبل الآخرين',
        goAuth: 'الذهاب للمصادقة باسم حقيقي',
        requestSomeoneList: 'طلب من الأشخاص التاليين إكمال المصادقة باسم حقيقي:',
        ent: 'شركة',
        entName: 'اسم الشركة',
        account: 'الحساب',
        accountPH: 'هاتف أو بريد إلكتروني',
        send: 'إرسال',
        lackEntName: 'يرجى إدخال اسم الشركة',
        errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',
        successfulSent: 'تم الإرسال بنجاح',
    },
    thirdPartApprovalDialog: {
        title1: 'موافقة قبل التوقيع',
        title2: 'عملية الموافقة',
        content1: 'يمكنك التوقيع فقط بعد الموافقة، يرجى الانتظار بصبر.',
        content2: 'يتطلب الموافقة من منصة طرف ثالث (غير منصة BestSign).',
        cancelBtnText: 'عرض عملية الموافقة',
        confirmBtnText: 'تأكيد',
        iKnow: 'فهمت',
    },
    endSignEarlyPrompt: {
        cancel: 'إلغاء',
        confirm: 'تأكيد',
        signPrompt: 'إشعار التوقيع',
        signTotalCountTip: 'يتضمن هذا التوقيع {count} ملفات عقود',
        signatureTip: 'قام المرسل بتعيين {count} من أعضاء الشركة لتمثيل الشركة في التوقيع، حاليًا:',
        hasSigned: '{count} أشخاص قاموا بالتوقيع',
        hasNotSigned: '{count} أشخاص لم يقوموا بالتوقيع',
        noNeedSealTip: 'بعد الانتهاء من الختم، لن يحتاج أعضاء الشركة الذين لم يوقعوا إلى التوقيع.',
    },
    commonNomal: {
        yesterday: 'الأمس',
        ssq: 'بيست ساين',
        ssqPlatform: 'منصة بيست ساين للتوقيع الإلكتروني السحابية',
        ssqTestPlatform: '(للأغراض التجريبية فقط) منصة بيست ساين للتوقيع الإلكتروني السحابية',
        pageExpiredTip: 'انتهت صلاحية الصفحة، يرجى التحديث والمحاولة مرة أخرى',
        pswCodeSimpleTip: 'يجب أن تحتوي كلمة المرور على 6-18 رقمًا وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',
    },
    transferAdminDialog: {
        title: 'تأكيد الهوية',
        transfer: 'نقل',
        confirmAdmin: 'أنا المدير الرئيسي',
        content: 'يتعين على مدير النظام الرئيسي أن يكون مسؤولاً عن إدارة أختام الشركة، وإدارة العقود وإدارة صلاحيات الموظفين الآخرين، وعادة ما ينتمي إلى الممثل القانوني للشركة، أو مدير الشؤون المالية، أو مدير الشؤون القانونية، أو مدير قسم تكنولوجيا المعلومات، أو المسؤول عن أعمال الشركة. | يرجى التأكد من أنك تستوفي الهوية المذكورة أعلاه، وإذا لم تكن كذلك، فمن المستحسن نقلها إلى الشخص المعني.',
    },
    choseBoxForReceiver: {
        dataNeedForReceiver: 'المعلومات المطلوب تقديمها من طرف التوقيع',
        dataFromDataBox: 'يجب الحصول على المعلومات المطلوب تقديمها من طرف التوقيع من خلال جمع المستندات من خزانة ملفات معينة.',
        searchTp: 'يرجى إدخال اسم أو رمز خزانة الملفات.',
        search: 'بحث',
        boxNotFound: 'لا يمكن العثور على خزانة الملفات.',
        cancel: 'إلغاء',
        confirm: 'موافق',
    },
    localCommon: {
        cancel: 'إلغاء',
        confirm: 'تأكيد',
        toSelect: 'يرجى الاختيار',
        seal: 'ختم',
        signature: 'توقيع',
        signDate: 'تاريخ',
        text: 'نص',
        date: 'تاريخ',
        qrCode: 'رمز الاستجابة السريعة',
        number: 'رقمي',
        dynamicTable: 'نماذج ديناميكية',
        terms: 'شروط وأحكام العقد',
        checkBox: 'خانات اختيار للخيارات المتعددة',
        radioBox: 'خانة اختيار للخيارات الفردية',
        image: 'صورة',
        confirmSeal: 'ختم للاستعلامات',
        tip: 'نصائح',
        confirmRemark: 'ملاحظات بشأن الأختام التي لا تلبي المتطلبات',
        optional: 'اختياري',
        require: 'مطلوب',
        comboBox: 'قائمة منسدلة',
    },
    twoFactor: {
        signTip: 'إشعار التوقيع',
        settingTwoFactor: 'إعداد أداة التحقق ثنائي العنصر',
        step1: '1. تثبيت تطبيق التحقق',
        step1Tip: 'يتطلب التحقق ثنائي العنصر تثبيت تطبيق الهاتف المحمول التالي:',
        step2: '2.مسح رمز الاستجابة السريعة',
        step2Tip1: 'استخدم أداة التحقق التي قمت بتنزيلها لمسح رمز الاستجابة السريعة أدناه (يرجى التأكد من أن الوقت على هاتفك يتطابق مع الوقت الحالي، وإلا فلن يمكن تنفيذ التحقق ثنائي العنصر).',
        step2Tip2: 'سيتم عرض رمز التحقق المكون من 6 أرقام الذي يلزم للتحقق ثنائي العنصر على الشاشة.',
        step3: '3.أدخل رمز التحقق المكون من 6 أرقام',
        step3Tip: 'يرجى إدخال رمز التحقق المعروض على الشاشة',
        verifyCode6: 'رمز التحقق المكون من 6 أرقام',
        iosAddress: 'عنوان تنزيل iOS:',
        androidAddress: 'عنوان تنزيل Android:',
        chromeVerify: 'أداة التحقق من Google',
        nextBtn: 'الخطوة التالية',
        confirmSign: 'تأكيد التوقيع',
        dynamicCode: 'رمز أداة التحقق الديناميكي',
        password: 'رمز التوقيع المشفر',
        pleaseInput: 'يرجى الإدخال',
        twoFactorTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر لإكمال التوقيع.',
        passwordTip: 'بناءً على طلب المرسل، تحتاج إلى التوقيع المشفر لإكمال التوقيع.',
        twoFactorAndPasswordTip: 'بناءً على طلب المرسل، تحتاج إلى التحقق ثنائي العنصر والتوقيع المشفر لإكمال التوقيع.',
        passwordTip2: 'يرجى الاتصال بالمرسل للحصول على رمز التوقيع المشفر، وبعد إدخاله يمكنك توقيع العقد.',
        dynamicVerifyInfo: 'يرجى إدخال رمز أداة التحقق الديناميكي الصحيح. إذا كنت تقوم بالربط مرة أخرى، فيرجى إدخال رمز أداة التحقق الديناميكي للربط الأحدث.',
    },
    functionSupportDialog: {
        title: 'تقديم الوظائف',
        inputTip: 'إذا كانت لديك احتياجات استخدام ذات صلة، يرجى ملء احتياجاتك في النموذج التالي. ستقوم BestSign بترتيب متخصصين للاتصال بك وتقديم إرشادات الخدمة في غضون 24 ساعة.',
        useSence: 'سيناريو التطبيق',
        useSenceTip: 'مثل الموارد البشرية، الموزعين، وثائق الخدمات اللوجستية، إلخ',
        estimatedOnlineTime: 'تاريخ الإطلاق المتوقع',
        requireContent: 'المتطلبات',
        requireContentTip: 'يرجى وصف كيفية استخدام شركتك للتوقيع الإلكتروني حتى نتمكن من تقديم حل مناسب لك',
        getSupport: 'احصل على دعم خدمة احترافي',
        callServiceHotline: 'الخط الساخن: 400-993-6665',
        useSenceNotEmpty: 'لا يمكن أن يكون سيناريو الاستخدام فارغًا',
        requrieContentNotEmpty: 'لا يمكن أن يكون محتوى الطلب فارغًا',
        oneWeek: 'في غضون أسبوع',
        oneMonth: 'في غضون شهر',
        other: 'أخرى',
        submitSuccess: 'تم الإرسال بنجاح',
        submitTrial: 'تقديم طلب تجريبي',
        toTrial: 'للتجربة',
        trialTip: 'بعد تقديم طلب التجربة، سيتم تنشيط الوظيفة الحالية على الفور وستكون متاحة للتجربة. لمساعدتك بشكل أفضل على استخدام الميزات، يمكنك ملء المزيد من المتطلبات في النموذج أدناه. سيتصل بك مستشار BestSign لتقديم الخدمات.',
        applyTrial: 'التقدم بطلب تجربة',
        trialSuccTip: 'تم تنشيط الوظيفة. نرحب بتجربتها',
        goBuy: 'اشتر الآن',
        trialTipMap: {
            title: 'تعليمات التجربة',
            tip1: '1. استخدام فوري، صالح لمدة 7 أيام;',
            tip2: '2. خلال فترة التجربة، لن يتم احتساب رسوم للوظيفة;',
            tip3: '3. كل كيان شركة لديه فرصة تجربة واحدة فقط لوظيفة ما;',
            tip4: '4. الشراء الذاتي متاح خلال فترة التجربة، والاستخدام غير منقطع;',
            tip5: '5. إذا انتهت فترة التجربة الخاصة بك، يمكنك مسح الرمز والاتصال بمستشار BestSign المحترف للحصول على التفاصيل:',
        },
        contactAdminTip: 'للاستخدام، يرجى الاتصال بمسؤول المؤسسة {tip} للشراء والفتح',
        trialEndTip: 'بعد انتهاء فترة التجربة، انقر للشراء',
        trialRemainDayTip: 'بقي {day} أيام في فترة التجربة، انقر لشراء النسخ',
        trialEnd: 'انتهت وظيفة التجربة',
        trialEndMap: {
            deactivateTip: 'تم تعطيل ميزة {feature}. يرجى مسح التكوين أو تجديده قبل متابعة استخدامه.',
            feature1: 'مرفقات العقود',
            remove1: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن بيانات مرفقات العقود الإضافية المكونة وحذفها.',
            feature2: 'التعرف على الكتابة اليدوية',
            remove2: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن التعرف على الكتابة اليدوية المكون وحذفه.',
            feature3: 'زخرفة العقد: ختم الركوب + العلامة المائية',
            remove3: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن زخرفة العقد المكونة وحذفها.',
            feature4: 'موافقة إرسال العقد',
            remove4: 'طريقة مسح التكوين هي: وحدة تحكم المؤسسة - تعطيل جميع عمليات الموافقة',
        },
    },
    setSignPwdDialog: {
        tip: "بعد اكتمال الإعداد، سيتم استخدام كلمة مرور التوقيع أولاً افتراضيًا. يمكنك تسجيل الدخول إلى منصة التوقيع الإلكتروني لـ BestSign والدخول إلى 'مركز المستخدم' أو تسجيل الدخول إلى تطبيق BestSign والدخول إلى 'إدارة الحساب' لتغيير كلمة المرور.",
        saveAndReturnSign: 'حفظ والعودة للتوقيع',
        changeEmailVerify: 'التبديل إلى التحقق عبر البريد الإلكتروني',
        changePhoneVerify: 'التبديل إلى التحقق عبر رقم الجوال',
    },
    contractCompare: {
        reUpload: 'إعادة الرفع',
        title: 'مقارنة العقود',
        packagePurchase: 'شراء الباقة',
        packagePurchaseTitle: '【{title} خاصية】شراء الباقة ',
        myPackage: 'باقتي',
        packageDetail: 'تفاصيل الباقة',
        per: 'مرة',
        packageContent: 'محتوى الباقة يشمل:',
        num: 'عدد المرات {type}',
        limitTime: 'فترة الصلاحية',
        month: 'شهر',
        payNow: 'الشراء الآن',
        contactUs: 'اتصل بنا | امسح الرمز للتواصل مع مستشار BestSign المحترف للمعرفة',
        compareInfo1: 'تعليمات الاستخدام:',
        compareInfo2: '{index}. يمكن لجميع أعضاء الشركة استخدام {type} المشتراة وعدد {per}، إذا كنت بحاجة فقط للاستخدام الشخصي، يمكنك التبديل إلى حساب شخصي في أعلى اليمين;',
        compareInfo3: '{index}. يتم احتساب الاستخدام وفقًا لعدد {per} العقود المرفوعة',
        codePay: 'يرجى المسح للدفع',
        aliPay: 'الدفع عبر Alipay',
        wxPay: 'الدفع عبر WeChat',
        payIno: 'تفعيل الوظيفة | كائن الشراء | مبلغ الدفع',
        finishPay: 'إكمال الدفع',
        paySuccess: 'تم الشراء بنجاح',
        originFile: 'ملف العقد الأصلي',
        compareFile: 'ملف العقد للمقارنة',
        documentSelect: 'اختيار الملف',
        comparisonResult: 'نتيجة المقارنة',
        history: 'السجل التاريخي',
        currentHistory: 'سجل المستندات',
        noData: 'لا توجد بيانات',
        differences: '{num} اختلاف',
        historyLog: '{num} سجل',
        uploadLimit: 'اسحب الملفات التي تريد مقارنتها وأفلتها هنا للتحميل | نحن ندعم حاليًا ملفات PDF (بما في ذلك مسح PDF) وملفات Word',
        dragInfo: 'حرر زر الماوس لإكمال التحميل',
        uploadError: 'تنسيق الملف غير مدعوم',
        pageNum: 'الصفحة {page}',
        difference: 'الاختلاف {num}',
        download: 'تنزيل نتيجة المقارنة',
        comparing: 'جاري مقارنة العقود...',
        tip: 'تنبيه',
        confirm: 'تأكيد',
        toBuy: 'للشراء',
        translate: 'ترجمة العقد',
        doCompare: 'مقارنة',
        doTranslate: 'ترجمة',
        review: 'مراجعة العقد',
        doReview: 'مراجعة',
        reviewUploadFile: 'اسحب الملف المراد مراجعته وأفلته هنا للتحميل',
        reviewUpload: 'اسحب مرجع المراجعة وأفلته هنا للتحميل | مثل: "إجراءات إدارة الموزع" و"وثائق سياسة المشتريات الشركة" وغيرها من وثائق اللوائح المؤسسية المستخدمة لمراجعة العقود | حاليًا ندعم فقط ملفات PDF وWord',
        reviewOriginFile: 'العقد قيد المراجعة',
        reviewTargetFile: 'مرجع المراجعة',
        reviewResult: 'نتيجة المراجعة',
        uploadReviewFile: 'تحميل ملف مرجع المراجعة',
        risk: 'نقطة مخاطرة {num}',
        risks: '{num} نقطة مخاطرة',
        startReview: 'بدء المراجعة',
        reviewing: 'جاري مراجعة العقد...',
        noRisk: 'اكتملت المراجعة، لم يتم العثور على مخاطر',
        allowUpload: 'يمكن تحميل "إجراءات إدارة مشتريات الشركة" وغيرها من اللوائح التنظيمية التي يمكن أن توجه مراجعة العقود، أو تحميل الخطوط الحمراء للشركة، أو إرشادات أعمال القسم، إلخ، | مثل: يجب على الطرف الأول إكمال الدفع في غضون 5 أيام بعد توقيع العقد.',
        notAllowUpload: 'لا تستخدم عبارات غامضة أو وصفًا مبدئيًا كأساس للمراجعة، | مثل: يجب ألا تنتهك جميع بنود العقد متطلبات القوانين واللوائح ذات الصلة.',
        resumeReview: 'متابعة الملف التالي',
        close: 'إغلاق',
        extract: 'استخراج العقد',
        extractTitle: 'الكلمات الرئيسية المراد استخراجها',
        selectKeyword: 'يرجى اختيار من "الكلمات الرئيسية" أدناه',
        keyword: 'الكلمات الرئيسية',
        addKeyword: 'إضافة {keyword}',
        introduce: 'تفسير {keyword}',
        startExtract: 'بدء الاستخراج',
        extractTargetFile: 'العقد المراد استخراجه',
        extractKeyWord: 'استخراج الكلمات الرئيسية',
        extracting: 'جاري استخراج العقد',
        extractResult: 'نتيجة الاستخراج',
        extractUploadFile: 'اسحب الملف المراد استخراجه وأفلته هنا للتحميل',
        needExtractKeyword: 'يرجى اختيار الكلمات الرئيسية التي ترغب في استخراجها',
        summary: 'ملخص العقد',
        keySummary: 'ملخص محتوى الكلمات الرئيسية',
        deleteKeywordConfirm: 'هل تريد بالتأكيد حذف هذه الكلمة الرئيسية؟',
        keywordPosition: 'مواقع الكلمات الرئيسية ذات الصلة',
        riskJudgement: 'تقييم المخاطر',
        judgeTargetContract: 'العقد المراد تقييمه',
        interpretTargetContract: 'العقود المُفسَّرة',
        startJudge: 'بدء تقييم المخاطر',
        startInterpret: 'ابدأ التفسير',
        uploadText: 'يرجى تحميل المستندات لتقييم المخاطر',
        interpretText: 'الرجاء رفع الملفات التي تحتاج إلى تفسير',
        startTips: 'يمكننا الآن البدء في تقييم المخاطر',
        interpretTips: 'يمكننا الآن بدء عملية التفسير',
        infoExtract: 'استخراج المعلومات',
    },
    batchImport: {
        iKnow: 'فهمت',
    },
    templateCommon: {
        tip: 'نصيحة',
    },
    mgapprovenote: {
        SAQ: 'استبيان استقصائي',
        analyze: 'تحليل',
        annotate: 'تعليق توضيحي',
        law: 'البحث في البنود القانونية',
        case: 'البحث عن حالات مشابهة',
        translate: 'ترجمة',
        mark: 'علامة',
        tips: 'المحتوى أعلاه تم إنشاؤه بواسطة الذكاء الاصطناعي ولا يمثل موقف شانغشانغتشيان. إنه للرجوع إليه فقط. يرجى عدم حذف أو تعديل هذه العلامة.',
        limit: 'تم الوصول إلى حد الاستخدام. إذا كنت بحاجة إلى استمرار الاستخدام، يرجى ملء النموذج. سيتصل بك خدمة العملاء لدينا.',
        confirmTxt: 'الذهاب للتعبئة',
        content: 'محتوى ذو صلة',
        experience: 'الخبرة العملية',
        datas: 'المعطيات المرتبطة',
        terms: 'بنود مشابهة',
        original: 'المصدر',
        export: 'بيانات التصدير',
        preview: 'عرض مسبق للعقد',
        history: 'التسجيلات السابقة',
    },
    sealConfirm: {
        title: 'صفحة تأكيد الختم الإلكتروني',
        header: 'تأكيد الختم الإلكتروني',
        signerEnt: 'الشركة المتعاقدة:',
        abnormalSeal: 'ختم إلكتروني غير طبيعي:',
        sealNormal: 'الختم طبيعي',
        tip1: 'يرجى التأكد مما إذا كان الختم الإلكتروني طبيعيًا وقابلًا للاستخدام. إذا كان طبيعيًا، يمكنك النقر على زر "الختم طبيعي". بعد ذلك، عندما تستخدم هذه الشركة هذا الختم مرة أخرى، لن يرسل النظام إشعارات غير طبيعية إليك بعد الآن.',
        tip2: 'إذا كانت هناك مشكلة في الختم، يرجى التواصل فورًا مع الطرف الموقع لاستبدال الختم، وإعادة إرسال العقد للتوقيع، أو الرفض وإعادة التوقيع.',
    },
    ...console,
    ...docList,
    ...consts,
    keyInfoExtract: {
        operate: 'استخراج المعلومات',
        contractType: 'أنواع العقود المتوقعة',
        tooltips: 'اختر المعلومات الرئيسية',
        predictText: 'جاري التنبؤ',
        extractText: 'جاري الاستخراج',
        errorMessage: 'لقد استنفدت حد الاستخدام الخاص بك، إذا كانت لديك احتياجات أخرى، يمكنك ملء الاستبيان، وسنتصل بك ونجدد المزيد من الجرعات لك.',
        result: 'النتيجة:',
    },
    judgeRisk: {
        title: 'محامي الذكاء الاصطناعي',
        deepInference: 'المعاملات القانونية الذكية',
        showAll: 'عرض المزيد',
        tips: 'جاري التقييم',
        dialogTitle: 'مراجعة العقود بواسطة “محامي الذكاء الاصطناعي”',
        aiInterpret: 'تفسير بالذكاء الاصطناعي',
    },
    workspace: {
        create: 'تم الإنشاء',
        reviewing: 'قيد المراجعة',
        completed: 'تم الانتهاء',
        noData: 'لا توجد بيانات',
        introduce: 'شرح {keyword}',
        termsDetail: 'تفاصيل المصطلح',
        extractFormat: 'تنسيق الاستخراج',
        optional: 'اختياري',
        required: 'مطلوب',
        operate: 'إجراء',
        detail: 'التفاصيل',
        delete: 'حذف',
        agreement: {
            uploadError: 'يمكن رفع ملفات PDF، DOC، DOCX فقط!',
            extractionRequest: 'تم إرسال طلب الاستخراج، يرجى مراجعة نتائج الاستخراج في قائمة المصطلحات لاحقًا',
            upload: 'رفع الملف',
            define: 'تعريف المصطلح',
            extract: 'استخراج الاتفاقية',
            drag: 'اسحب الملف إلى هنا أو',
            add: 'انقر للإضافة',
            format: 'يدعم تنسيقات doc، docx، pdf',
            fileName: 'اسم الملف',
            status: 'الحالة',
            completed: 'تم الرفع',
            failed: 'فشل الرفع',
            size: 'الحجم',
            terms: 'المصطلحات',
            success: 'تم استخراج الملف، إجمالي {total}',
            ongoing: 'جارٍ استخراج الملف... إجمالي {total}',
            tips: 'تخطي هذه الشاشة لا يؤثر على نتائج الاستخراج',
            others: 'متابعة الرفع',
            result: 'الانتقال إلى صفحة تنزيل نتائج الاستخراج',
            curProgress: 'التقدم الحالي: ',
            refresh: 'تحديث',
            details: 'تم تحميل {successNum} من أصل {length}',
            start: 'بدء الاستخراج',
            more: 'إضافة ملف',
            skip: 'تخطي الاستخراج وإكمال الرفع.',
            tiqu: 'بدء الاستخراج',
            chouqu: 'بدء الاستخراج',
        },
        review: {
            distribution: 'توزيع المراجعة',
            Incomplete: 'غير مكتمل',
            createReview: 'إنشاء مراجعة',
            manageReview: 'إدارة المراجعات',
            reviewDetail: 'تفاصيل المراجعة',
            reviewId: 'رقم المراجعة',
            reviewStatus: 'حالة المراجعة',
            reviewName: 'اسم المراجعة',
            reviewStartTime: 'وقت بدء المراجعة',
            reviewCompleteTime: 'وقت انتهاء المراجعة',
            reviewDesc: 'الإصدار: إصدار {reviewVersion}  |  رقم المراجعة: {reviewId}',
            distribute: 'بدء المراجعة',
            drag: 'اسحب الاتفاقية للمراجعة إلى هذه المنطقة',
            content: 'المحتوى قيد المراجعة',
            current: 'سجلات التوزيع الحالية',
            history: 'السجلات التاريخية',
            page: 'الصفحة {page}:',
            users: 'المستخدمون المطلوب مراجعتهم',
            message: 'رسالة',
            modify: 'تعديل',
            placeholder: 'يفصل بين المستخدمين المتعددين بفاصلة منقوطة ";"',
            submit: 'تأكيد',
            reupload: 'إعادة رفع اتفاقية المراجعة',
            finish: 'إنهاء المراجعة',
            reviewSummary: 'ملخص المراجعة',
            initiator: 'مُبادِر المراجعة',
            versionSummary: 'ملخص الإصدار',
            version: 'الإصدار',
            versionOrder: 'الإصدار {version}',
            curReviewStatus: 'حالة مراجعة الإصدار الحالي',
            curReviewVersion: 'الإصدار الحالي',
            curReviewPopulation: 'عدد مراجعي الإصدار الحالي',
            curReviewStartTime: 'وقت بدء مراجعة الإصدار الحالي',
            curReviewInitiator: 'مُبادِر مراجعة الإصدار الحالي',
            checkComments: 'عرض تجميعي لملاحظات التعديل',
            overview: 'نظرة سريعة على نتائج المراجعة',
            reviewer: 'المراجع',
            reviewResult: 'نتيجة المراجعة',
            replyTime: 'وقت الرد',
            agreement: 'الاتفاقية المُراجعة',
            files: 'الاتفاقيات ذات الصلة',
            fileName: 'اسم الاتفاقية',
            numberOfModificationSuggestions: 'عدد اقتراحات التعديل',
            uploadTime: 'وقت الرفع',
            download: 'تنزيل',
            dispatch: 'توزيع',
            recent: 'آخر وقت مراجعة: ',
            replyContent: 'محتوى رد المراجعة',
            advice: 'اقتراحات تعديل الاتفاقية',
            noIdea: 'لا توجد اقتراحات تعديل حالياً',
            origin: 'المحتوى الأصلي:',
            revised: 'المحتوى المعدل:',
            suggestion: 'اقتراح التعديل:',
            dateMark: '{name} كتب في <span style="color: #0988EC">الإصدار {version}</span> بتاريخ {date}',
            unReviewed: 'لم يتم المراجعة بعد',
            revisionFiles: 'ملفات التعديل',
            staffReplyAggregation: 'تجميع معلومات التعديل',
            staffReply: 'معلومات مراجعة {name}',
            tips: 'ملاحظة',
            tipsContent: 'سيؤدي الانتهاء إلى إيقاف التوزيع والعمليات اللاحقة لهذه المراجعة، هل تتابع؟',
            confirm: 'تأكيد',
            cancel: 'إلغاء',
            successMessage: 'تم الانتهاء',
            PASS: 'مقبول',
            REJECT: 'مرفوض',
            uploadErrorMessage: 'يدعم حاليًا ملفات docx فقط',
            successInitiated: 'تم بدء المراجعة',
            autoDistribute: 'التوزيع الذكي',
            requiredUsers: 'المستخدمون الذين يحتاجون إلى مراجعة',
            contentToReview: 'المحتوى للمراجعة',
            termDetails: 'تفاصيل المصطلح',
            term: 'المصطلح',
            aiDistribute: 'التوزيع الذكي AI',
            noData: 'لا توجد بيانات متاحة',
            docIconAlt: 'أيقونة المستند',
            docxIconAlt: 'أيقونة DOCX',
            pdfIconAlt: 'أيقونة PDF',
            requiredUsersError: 'يرجى ملء المستخدمين الذين يحتاجون إلى مراجعة',
            selectContentError: 'يرجى اختيار المحتوى للمراجعة',
            initiateReviewSuccess: 'تم بدء المراجعة',
            syncInitiated: 'تم بدء المزامنة',
        },
        contentTracing: {
            title: 'تتبع المحتوى',
            fieldContent: 'محتوى الحقل',
            originalResult: 'النتيجة الأصلية',
            contentSource: 'مصدر المحتوى',
            page: 'الصفحة ',
        },
    },
    hubblePackage: {
        title: 'حزمتي',
        details: 'تفاصيل الحزمة',
        remainingPages: 'إجمالي الصفحات المتبقية',
        pages: 'صفحات',
        usedPages: 'المستخدمة',
        remaining: 'المتبقي المتاح',
        total: 'إجمالي',
        expiryTime: 'وقت الانتهاء',
        amount: 'العدد',
        unitPrice: 'سعر الوحدة',
        copy: 'نسخة',
        words: 'ألف كلمة',
    },
    workspaceIndex: {
        title: 'مساحة العمل',
        package: 'حزم الاستخدام',
        agreement: 'إدارة الاتفاقيات',
        review: 'إدارة المراجعات',
        term: 'إدارة المصطلحات',
    },
    agreement: {
        title: 'إدارة الاتفاقيات',
        exportList: 'تصدير قائمة الاتفاقيات',
        exportAllChecked: 'Excel (جميع الحقول، الاتفاقيات المحددة)',
        exportCurrentChecked: 'Excel (الحقول الحالية، الاتفاقيات المحددة)',
        exportAllMatched: 'Excel (جميع الحقول، المطابقة للشروط)',
        exportCurrentMatched: 'Excel (الحقول الحالية، المطابقة للشروط)',
        add: 'إضافة اتفاقية',
        upload: 'رفع اتفاقية',
        operation: 'إجراء',
        download: 'تنزيل الاتفاقية',
        details: 'التفاصيل',
        delete: 'حذف',
        relatedTaskStatus: 'حالة المهمة المرتبطة بالاستخراج',
        confirmDelete: 'هل تريد تأكيد حذف هذه الاتفاقية؟',
        prompt: 'تلميح',
        booleanYes: 'نعم',
        booleanNo: 'لا',
        defaultExportName: 'تصدير.xlsx',
        taskNotStarted: 'لم تبدأ مهمة الاستخراج',
        taskStarted: 'بدأت مهمة الاستخراج (جارٍ البحث في المحتوى)',
        contentSearchCompleted: 'اكتمل البحث في المحتوى (جارٍ تنسيق النتائج)',
        resultFormattingCompleted: 'اكتمل تنسيق النتائج (جارٍ مراجعة النتائج)',
        resultVerificationCompleted: 'اكتملت مراجعة النتائج',
    },
    filter: {
        filter: 'تصفية',
        refreshExtraction: 'إعادة الاستخراج',
        extractTerms: 'استخراج تعريفات المصطلحات',
        refreshList: 'تحديث القائمة',
        currentCondition: 'الاتفاقيات المعروضة تحت الشروط الحالية.',
        when: 'عندما',
        selectCondition: 'اختر الشرط',
        enterCondition: 'أدخل الشرط',
        yes: 'نعم',
        no: 'لا',
        addCondition: 'إضافة شرط',
        reset: 'إعادة تعيين',
        confirm: 'تأكيد',
        and: 'و',
        or: 'أو',
        equals: 'يساوي',
        notEquals: 'لا يساوي',
        contains: 'يحتوي على',
        notContains: 'لا يحتوي على',
        greaterThan: 'أكبر من',
        greaterThanOrEquals: 'أكبر من أو يساوي',
        lessThan: 'أقل من',
        lessThanOrEquals: 'أقل من أو يساوي',
        emptyCondition: 'لا يمكن أن يكون شرط التصفية فارغًا',
    },
    fieldConfig: {
        button: 'تكوين الحقول',
        header: 'الحقول المطلوب عرضها',
        submit: 'إنهاء',
        cancel: 'إلغاء',
    },
    agreementDetail: {
        detail: 'تفاصيل الاتفاقية',
        add: 'إضافة اتفاقية',
        id: 'رقم الاتفاقية',
        file: 'ملف الاتفاقية',
        download: 'تنزيل الاتفاقية',
        replaceFile: 'استبدال ملف الاتفاقية',
        uploadFile: 'رفع ملف الاتفاقية',
        relatedExtractionStatus: 'حالة مهمة الاستخراج المرتبطة',
        dataSource: 'مصدر البيانات',
        yes: 'نعم',
        no: 'لا',
        select: 'اختر',
        input: 'أدخل',
        save: 'حفظ',
        cancel: 'إلغاء',
        page: 'الصفحة {page}',
        addDataSource: 'إضافة مصدر بيانات',
        pageNo: 'الصفحة ',
        pageSuffix: '',
        submit: 'إرسال',
        inputDataSource: 'أدخل محتوى مصدر البيانات',
        pageFormatError: 'يُدعم أرقام الصفحات مفصولة بفاصلة إنجليزية أو أرقام فقط',
        confirmDelete: 'هل تريد تأكيد حذف مصدر البيانات الحالي؟',
        tips: 'تلميح',
        uploadSuccess: 'تم الرفع بنجاح',
    },
    termManagement: {
        title: 'إدارة المصطلحات',
        batchDelete: 'حذف جماعي',
        import: 'استيراد مصطلحات',
        export: 'تصدير مصطلحات',
        add: '+ إضافة مصطلح',
        name: 'اسم المصطلح',
        definition: 'شرح المصطلح',
        formatRequirement: 'متطلبات تنسيق الاستخراج',
        dataFormat: 'تنسيق البيانات',
        operation: 'إجراء',
        edit: 'تعديل',
        delete: 'حذف',
        detail: 'تفاصيل المصطلح',
        addTitle: 'إضافة مصطلح',
        namePlaceholder: 'أدخل المصطلح المتخصص',
        definitionPlaceholder: 'أدخل شرح المصطلح',
        formatRequirementPlaceholder: 'أدخل متطلبات تنسيق استخراج المصطلح',
        dataFormatPlaceholder: 'متطلبات تنسيق استخراج المصطلح المطلوبة',
        cancel: 'إلغاء',
        confirmEdit: 'تأكيد التعديل',
        importTitle: 'استيراد مصطلحات',
        uploadTemplate: 'رفع ملف القالب',
        downloadTemplate: 'تنزيل ملف القالب',
        extractType: {
            text: 'نص',
            longText: 'نص طويل',
            date: 'تاريخ',
            number: 'رقم',
            boolean: 'نعم/لا',
        },
        importSuccess: 'تم الاستيراد بنجاح',
        deleteConfirm: 'هل تريد تأكيد حذف هذا المصطلح؟',
        prompt: 'تلميح',
        nameEmptyError: 'اسم المصطلح لا يمكن أن يكون فارغًا',
    },
    agent: {
        extractTitle: 'استخراج المعلومات',
        riskTitle: 'محامي الذكاء الاصطناعي',
        feedback: 'تغذية الراجِع',
        toMini: 'انتقل إلى التطبيق المصغر للاطلاع',
        otherContract: 'أرني المخاطر الخفية في العقود الأخرى',
        others: 'الأخرى',
        submit: 'انقر لإرسال',
        autoExtract: 'يتم استخراج الخطوات التالية تلقائيًا حتى انتهاء العملية',
        autoRisk: 'يتم تحليل الخطوات التالية تلقائيًا حتى انتهاء العملية',
        aiGenerated: 'AI Generated - © BestSign',
        chooseRisk: 'الرجاء تحديد الملف المراد تحليله',
        chooseExtract: 'اختر الملف لبدء عملية الاستخراج',
        analyzing: 'جارٍ تحليل المحتوى',
        advice: 'جارٍ إنشاء اقتراحات التعديل',
        options: 'جاري توليد الخيارات',
        inputTips: 'الرجاء إدخال البيانات المطلوبة بدقة',
        chargeTip: 'الرصيد غير كافٍ، يرجى إعادة الشحن',
        original: 'النص الأصلي',
        revision: 'اقتراحات التعديل',
        diff: 'مقارنة',
        locate: 'تحديد مكان النص الأصلي',
        custom: 'حدد قواعد الفحص حسب الحاجة',
        content: 'إحداثيات النص المصدر',
        satisfy: 'أنا راضٍ بالتحليل، استمر في التحليل التالي',
        dissatisfy: 'أنا غير راضٍ بالتحليل، أعيد التحليل',
        selectFunc: 'الرجاء اختيار الوظيفة التي ترغب في استخدامها',
        deepInference: 'المعاملات القانونية الذكية',
        deepThinking: 'في حالة تفكير عميق',
        deepThoughtCompleted: 'تم الانتهاء من التفكير العميق',
        reJudge: 'إعادة الحكم',
        confirm: 'تأكيد',
        tipsContent: 'إعادة التقييم ستخصم من عدد المحاولات. هل تريد المتابعة؟',
        useLawyer: 'استخدم محامي الذكاء',
        interpretFinish: 'اكتمل التفسير الآلي',
        exportPDF: 'تصدير تقرير PDF',
        defaultExportName: 'تصدير.pdf',
        exporting: 'جاري تصدير التقرير... الرجاء الانتظار',
    },
    authorize: {
        title: 'الشروط والأحكام',
        content: 'فعّل العقود الذكية - تحليل بالذكاء الاصطناعي يُسهّل عملك!موافقتك تمنحك تجربة مجانية',
        cancel: 'لا، شكرًا',
        confirm: 'موافقة والبدء',
        contract: 'اطّلع على 《شروط استخدام منتج هابل》',
    },
    sealDistribute: {
        requestSeal: 'طلب تخصيص الختم',
        company: 'الشركة',
        applicant: 'مقدم الطلب',
        accountID: 'رقم الحساب',
        submissionTime: 'الوقت',
        status: 'الحالة',
        agree: 'تمت الموافقة',
        unAgree: 'تم الرفض',
        ifAgree: 'إذا وافقت،',
        applyTime: 'وقت ختم مقدم الطلب هو:',
        to: 'إلى',
        placeHolderTime: 'سنة-شهر-يوم',
        senderCompany: 'الشركة المرسلة',
        documentTitle: 'عنوان العقد',
        sealApplicationScope: 'نطاق تطبيق الختم',
        applyforSeal: 'طلب الختم',
        reject: 'رفض',
        approve: 'موافقة',
    },
    sealApproval: {
        sealRight: 'صلاحيات الختم',
        requestSeal: 'طلب تخصيص الختم',
        allEntContract: 'العقود من جميع المؤسسات',
        partEntContract: 'العقود من مؤسسات محددة: ',
        pleaseInputRight: 'الرجاء إدخال الصلاحيات',
        successTransfer: 'بعد نجاح التسليم،',
        getRight: 'سيتم الحصول على الصلاحيات المذكورة أعلاه أو يمكن تحرير وتعيين صلاحيات توقيع جديدة مباشرة.',
        signAllEntContract: 'توقيع العقود من جميع المؤسسات',
        sign: 'توقيع',
        sendContract: 'العقود المرسلة',
        sealUseTime: 'فترة استخدام الختم: ',
        currentStatus: 'الحالة الحالية: ',
        takeBackSeal: 'استرجاع الختم',
        agree: 'موافقة',
        hasAgree: 'تمت الموافقة',
        hasReject: 'تم الرفض',
        hasDone: 'مكتمل',
        ask: 'سوف',
        giveYou: 'يتم تخصيص الختم لك',
        hopeAsk: 'يأمل في',
        hopeGive: 'تسليم الختم إلى',
        hopeGiveYou: 'تسليم الختم ذي الصلة إليك',
        noSettingTime: 'لا يوجد إعداد للوقت',
        approvalSuccess: 'تمت الموافقة بنجاح',
        getSealSuccess: 'تم الحصول على الختم بنجاح',
    },
    hubbleEntry: {
        smartAdvisor: 'مستشار العقد الذكي',
        tooltips: 'هذه الميزة غير مفعّلة. راجع مستشاري التوقيع الإلكتروني BestSign للشراء.',
        confirm: 'نعم',
    },
    lang: 'ar',
};

