// 语言为俄文时的文案
import utils from './module/utils/utils-ru.js';
import mixin from './module/mixins/mixins-ru.js';
import components from './module/components/components-ru.js';

// import docList from './module/docList/docList-ru.js';
// import console from './module/console/console-ru.js';
// import userCentral from './module/usercentral/usercentral-ru.js';
// import home from './module/home/<USER>';
import sign from './module/sign/sign-ru.js';
// import entAuth from './module/entAuth/entAuth-ru.js';

export default {
    ...utils,
    ...mixin,
    ...components,
    // ...docList,
    login: {
        pswLogin: 'Пароль для входа',
        usePswLogin: '«Войти с паролем»',
        verifyLogin: 'Код подтверждения для входа',
        useVerifyLogin: '«Войти с кодом подтверждения»',
        scanLogin: 'Сканирование кода входа',
        scanFailure: 'Срок действия QR-кода истек, пожалуйста, обновите и попробуйте снова',
        scanSuccess: 'Сканирование кода успешно',
        scanLoginTip: 'Пожалуйста, используйте приложение BestSign, для входа отсканируйте APP',
        appLoginTip: 'Пожалуйста, нажмите «Войти в приложение BestSign»',
        downloadApp: 'Скачать приложение BestSign',
        forgetPsw: 'Забыли пароль',
        login: 'Войти',
        noAccount: 'Нет аккаунта',
        registerNow: 'Зарегистрируйтесь сейчас',
        accountPlaceholder: 'Введите пароль',
        passwordPlaceholder: 'Пожалуйста, введите пароль для входа',
        pictureVer: 'Пожалуйста, заполните содержание на картинке»',
        verifyCodePlaceholder: 'Пожалуйста, введите 6-значный код подтверждения',
        getVerifyCode: 'Получить код подтверждения',
        noRegister: 'Еще не зарегистрирован',
        or: 'или',
        errAccountOrPwdTip: 'Введенный вами пароль не соответствует номеру учетной записи?',
        errAccountOrPwdTip2: 'Введенный вами пароль не соответствует номеру учетной записи.',
        errEmailOrTel: 'Пожалуйста, введите правильный E-mail или номер телефона!',
        errPwd: 'Пожалуйста, введите правильный пароль!',
        verCodeFormatErr: 'Ошибочный код подтверждения',
        grapVerCodeErr: 'Ошибка графического кода подтверждения',
        grapVerCodeFormatErr: 'Ошибка формата графического кода подтверждения',
        lackAccount: 'Пожалуйста, заполните номер счета и затем получите',
        lackGrapCode: 'Пожалуйста, сначала заполните графический код подтверждения.',
        getVerCodeTip: '«Пожалуйста, получите код подтверждения»',

        loginView: 'Войдите и просмотрите договор',
        regView: 'Зарегистрируйтесь и просмотрите договор',
        takeViewBtn: '登录并签署',
        resendCode: 'Получить заново',
        regTip: 'После ввода правильного кода подтверждения, BestSign создаvn для вас аккаунт.',
        haveRead: 'Я прочитал и согласился',
        bestsignAgreement: 'Сервисное соглашение BestSign',
        and: 'и',
        digitalCertificateAgreement: 'Соглашение об использовании цифрового сертификата',
        privacyPolicy: 'Политика конфиденциальности',
        sendSuc: 'Успешно отправлен',
        lackVerCode: 'Пожалуйста, сначала введите код подтверждения',
        lackPsw: 'Пожалуйста, сначала введите ваш пароль',
        notMatch: 'Введенный вами пароль и аккаунт не соответствует',
        cookieTip: 'Не удается прочитать и записать файлы cookie, проверьте, нет ли режима трассировки / инкогнито или других запрещенных файлов cookie',
        wrongLink: 'Нелегальная ссылка',
        footerTips: 'Услуга электронной подписи <span> BestSign </span> предоставляет',
        bestSign: 'BestSign',
        bestSignDescription: 'Лидер индустрии электронных <br/> контрактов',
        /** 忘记密码 /forgotPassword start */
        forgetPswStep: 'Подтвердить зарегестрированный аккаунт | Сбросить пароль',
        pictureVerCodeInput: 'Графический код подтверждения | Пожалуйста, заполните содержание изображения',
        accountInput: 'Аккаунт | Пожалуйста, введите ваш аккаунт',
        smsCodeInput: 'Код подтверждения | Получить код подтверждения',
        haveRegistereLoginNow: 'Я уже зарегистрировался, | Войдите сейчас',
        nextStep: 'Следующий шаг | Отправить',
        setNewPasswordInput: 'Установить новый пароль | 6-18 цифр, прописные и строчные буквы',
        passwordResetSucceeded: 'Сброс пароля выполнен успешно!',
        /** 忘记密码 /forgotPassword end */
        accountNotRegistered: 'Аккаунт не зарегистрирован',
        loginAndDownload: 'Войдите и скачайте договор',
        registerAndDownload: 'Зарегистрируйтесь и скачайте договор',
        inputPhone: 'Пожалуйста, введите номер телефона',
        readContract: 'Читать контракт',
        errorPhone: 'Ошибка формата телефона',
        companyCert: 'Проведение корпоративной аутентификаций',
        regAndCompanyCert: 'Регистрация и проведение корпоративной аутентификаций',
    },
    ...sign,
    handwrite: {
        title: 'Ручная подпись',
        picSubmitTip: '图片签名提交成功',
        settingDefault: '设置为默认签名',
        moreTip: '更多签名管理请到上上签电子签约平台（ent.bestsign.cn）用户中心处管理',
        uploadPic: '上传照片',
        use: '使用',
        clickExtend: '点右箭头延长手写区',
        rewrite: 'Написать заново',
        upload: '上传签名图片',
        cancel: 'Не писать',
        confirm: 'Использовать',
        upgradeBrowser: 'Ваш браузер не поддерживает для ручной подписи холста, прошу обновить.',
        submitTip: 'Ручная подпись завершена успешно',
        title2: 'Подпишите рукой',
        QRCode: 'Сканировать подпись',
        needWrite: 'Прошу ручной росписью правильно написать Ф. И. О.',
        needRewrite: '笔画无法辨认，请重新书写',
        ok: 'Подтвердить',
        clearTips: 'Прошу отчетливо написать подпись',
        isBlank: 'Холст пуст, прошу ручной росписью подписать, затем передать!',
        success: 'Ручная подпись завершена успешно',
        signNotMatch: '请正楷书写签名，须与实名证件信息一致。',
        signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',
        msg: {
            successToUser: 'Новая подпись вступила в силу, прошу перейти в( личный кабинет Web-отдел подписи) и проверить',
            successToSign: 'Новая подпись вступила в силу, прошу перейти на страницу договора подписи и проверить',
            cantGet: 'Не получается получить подпись, прошу попробовать другой браузер！',
        },
    },
    common: {
        aboutBestSign: 'О компании',
        contact: 'Свяжитесь с нами',
        recruitment: 'Рекрутинг талант',
        copyright: 'авторское право',
        advice: 'совет',
        notEmpty: 'Не должно быть пустым!',
        enter6to18n: 'Пожалуйста, введите 6-18 цифр, букв',
        ssqDes: 'Лидер облачной платформы Электронной Цифровой Подписи',
        openPlatform: 'Открытая платформа',
        company: 'HangZhou BestSign Ltd.',
        help: 'Справочный центр',
    },
    entAuth: {
        // ...entAuth,
        entCertification: 'Подтверждение личности компании',
        subBaseInfo: 'Отправить основную информацию',
        corDocuments: 'Свидетельство о регистраций компании',
        license: 'Лицензия на ведение коммерческой деятельности',
        upload: 'Нажмите, чтобы загрузить',
        uploadLimit: 'Изображения ограничены форматом jpeg, jpg, png,  размер не должен превышать 10M',
        hi: 'Здравствуйте',
        exit: 'Выход',
        help: 'Помощь',
        hotline: 'Горячая линия',
        acceptProtectingMethod: 'Я принимаю метод защиты личной информаций, предоставленной мной BestSign',
        comfirmSubmit: 'Подтвердить отправку',
        cerficated: 'Аутентификация завершена',
        entName: 'Название компании',
        serialNumber: 'Целый серийный номер',
        validity: 'Срок действия',
        nationalNo: ' Государственный регистрационный номер',
        corporationName: 'Ф.И.О. законного представителя',
        city: 'Все города',
        entCertificate: 'Сертификат настоящей подлинности организаций ',
        certificationAuthority: 'Центр по выдаче сертификата ',
        bestsignPlatform: ' Облачная платформа электронной подписи BestSign',
        notIssued: 'Не отпускается',
        date: '{day}число{month}месяц{year}год',
        congratulations: 'Поздравляем, успешно завершили аутентификацию компании',
        continue: 'Следующий',
        rejectMessage: ' По следующим причинам проверка данных не удалась, пожалуйста, проверьте еще раз',
        recertification: 'Пройти заново аутентификацию',
        waitMessage: 'Проверка клиента будет завершено в течение одного рабочего дня',
    },
    home: {
        // ...home,
        home: 'Страница',
        contractDrafting: 'Составить контракт',
        contractManagement: 'Управление контрактами',
        userCenter: 'Личный кабинет',
        service: 'Сервис',
        enterpriseConsole: 'Платформа управления компаниями',
        groupConsole: 'Group console',
        startSigning: 'Начать подписание',
        countDes: {
            1: 'Может быть выпущено: ',
            2: 'корпоративных контрактов и',
            3: '',
            4: 'частных контрактов',
        },
        // countDes: 'Может быть выпущено对公合同 | 份 | 对私合同 | 份' ,
        chargeNow: 'Немедленно пополнить счет',
        statusTip: {
            1: 'Нужно мне выполнить',
            2: 'Подпись с другой стороны',
            3: 'Подписывание скоро завершится',
            4: 'Подписание завершено',
        },
        useTemplate: '使用模板',
        useLocalFile: 'Загрузить локальные файлы',
        enterEnterpriseName: '请输入企业名称',
    },
    docDetail: {
        back: 'Назад',
        contractInfo: 'Информация о контракте',
        basicInfo: 'Основную информацию',
        contractNum: 'Номер контракта ',
        sender: 'Отправитель',
        personAccount: 'Личный кабинет ',
        entAccount: 'Аккаунт компаний',
        operator: 'Исполнитель',
        signStartTime: 'Отправить время подписания договора',
        signDeadline: 'Крайний срок подписания',
        contractExpireDate: 'Окончание срока действия договора',
        none: 'Ни один ',
        edit: 'Редактировать ',
        settings: 'Установка ',
        from: 'Ресурс ',
        folder: 'Папка',
        contractType: 'Тип контракта',
        reason: 'Причина ',
        sign: 'Подписать',
        approval: 'утверждение', // 1期不做审批
        viewAttach: 'Просмотреть страницу ',
        downloadContract: 'Скачать договор',
        downloadAttach: 'Скачать страницу ',
        print: 'Распечатать ',
        certificatedTooltip: 'Контракт и связанные с ним доказательства  были задокументированы в судебной цепочке Интернет-суда Ханчжоу.',
        needMeSign: 'Нужно мне подписать ',
        needMeApproval: 'Нужно мне утвердить', // 1期不做审批
        inApproval: 'В процессе рассмотрения..', // 1期不做审批
        needOthersSign: 'Подпись с другой стороны',
        signComplete: 'Подписание завершено',
        signOverdue: 'Просроченный подпись ',
        rejected: 'Отказано в подписи',
        revoked: 'Отозвано',
        contractCompleteTime: 'Время подписания завершено',
        contractEndTime: 'Время окончания подписи',
        reject: 'Отказ в подписи',
        revoke: 'Отменять',
        download: 'Скачать',
        viewSignOrders: 'Посмотреть порядок подписания',
        viewApprovalProcess: 'Проверить процесс утверждения', // 1期不做审批
        completed: 'Завершено ',
        cc: 'Отправить копию',
        ccer: 'Отправляющий',
        signer: 'Подписант',
        signSubject: 'Тема подписи',
        signSubjectTooltip: 'Отправитель должен заполнить подписывающую сторону',
        user: 'Пользователь',
        IDNumber: 'Номер паспорта ',
        state: 'Статус',
        time: 'Время',
        notice: 'Напоминать',
        detail: 'Детально',
        RealNameCertificationRequired: 'Требуется аутентификация личности',
        RealNameCertificationNotRequired: 'Не требуется аутентификаций',
        MustHandwrittenSignature: 'Обязательно подпишите рукой',
        handWritingRecognition: '开启手写笔迹识别',
        privateMessage: 'Личное письмо ',
        attachment: 'Прикрепленный файл ',
        rejectReason: 'Причина ',
        notSigned: 'Не подписано',
        notViewed: 'Не проверено ',
        viewed: 'Проверено ',
        signed: 'Подписано',
        viewedNotSigned: 'Прочтен но не подписан', // todo
        notApproval: 'Не утвержден ',
        remindSucceed: 'Напоминание отправлено ',
        reviewDetails: 'Детальное утверждение', // 1期不做审批
        close: 'Закрыть', // 1期不做审批
        entInnerOperateDetail: 'Детали процесса работы внутреннего отдела предприятии', // 1期不做审批
        approve: '同意', // 1期不做审批
        disapprove: 'Отклонить', // 1期不做审批
        applySeal: 'Заявка на испльзование печати', // 1期不做审批
        applied: 'Зарегистрировано', // 1期不做审批
        apply: 'Подавать заявление', // 1期不做审批
        toOtherSign: 'Переслать другому человеку для подписи', // 1期不做审批
        handOver: 'Передать', // 1期不做审批
        approvalOpinions: 'Резолюция', // 1期不做审批
        useSeal: 'Приложить печать', // 1期不做审批
        signature: 'Подписать', // 1期不做审批
        use: 'Использовать', // 1期不做审批
        date: 'Дата', // 1期不做审批
        fill: 'Заполнить', // 1期不做审批
        times: 'Раз', // 1期不做审批
        place: 'Место-пункт', // 1期不做审批
        contractDetail: 'Детали контракта',
        viewMore: 'Посмотреть больше',
        collapse: 'Сложить',
        signLink: 'Подписать ссылку',
        saveQRCode: 'Сохранить штрих-код или скопировать ссылку, поделиться со стороной  подписанта',
        signQRCode: 'Подписать ссылку штрих-кода',
        copy: 'Копия',
        copySucc: 'Копия завершена',
        copyFail: 'Копия не удалась',
        certified: 'сертифицировано',
        unCertified: 'неподтвержденный',
        claimed: '已认领',
    },
    uploadFile: {
        isUploading: 'Загрузки',
        move: 'Переместить',
        delete: 'Удалить',
        replace: 'Заменить',
        tip: 'подсказк',
        totalPages: 'всего {page} страниц',
        uploadFile: 'Загрузить локальные файлы',
        matchErr: 'На сервере небольшой пробел, повторите попытку позже.',
        inUploadingDeleteErr: 'После загрузки удалить',
        timeOutErr: 'Запрос времени ожидается',
    },
    contractInfo: {
        contractName: 'Название контракта',
        contractNameTooltip: 'Пожалуйста, не включайте специальные символы в название контракта, также длина не должна превышать 100 слов.',
        contractType: 'Тип контракта ',
        toSelect: 'Пожалуйста, выберите',
        contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',
        signDeadLine: 'Крайний срок подписания',
        signDeadLineTooltip: 'Если договор не подписан до этой даты, вы не можете продолжать подписывать',
        selectDate: 'Выбрать дату и время',
        contractExpireDate: 'Окончание срока действия договора',
        expireDateTooltip: 'Срок истечения в содержании контракта удобен для последующего управления контрактом',
        notNecessary: 'Не может быть заполнен',
        dateTips: 'Дата окончания срока действия контракта была автоматически определена для вас, пожалуйста, подтвердите ',
        contractTitleErr: 'В названии договора не должно содержаться специальные символы',
        contractTitleLengthErr: 'Название договора не должно превышать 100 букв',
    },
    // userCentral: userCentral,
    template: {
        dynamicTemplateUpdate: {
            title: '动态模板新功能上线',
            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',
            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',
            connectUs: '如有任何疑问，烦请联系拨打热线400-993-6665或者联系在线客服。',
        },
    },
    style: {
        signature: {
            text: {
                x: '0',
                fontSize: '17',
            },
        },
    },
    workspace: {
        create: 'Создан.',
        reviewing: 'Цензура',
        completed: 'Готово',
    },
    keyInfoExtract: {
        operate: 'Extract Information',
        contractType: 'Predicted Contract Types',
        tooltips: 'Select the Key Information',
        predictText: 'Predicting',
        extractText: 'Extracting',
        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',
        result: 'result:',
    },
    judgeRisk: {
        title: 'AI Lawyer',
        deepInference: 'AI-Юрист',
        showAll: 'Show More',
        tips: 'Judging',
        dialogTitle: '“AI Lawyer” Reviews Contracts',
        aiInterpret: 'ИИ-интерпретация',
    },
    contractCompare: {
        riskJudgement: 'Рисковое оценивание',
        judgeTargetContract: 'Контракт, подлежащий оценке',
        interpretTargetContract: 'Контракты с ИИ-анализом',
        startJudge: 'Начать рисковое оценивание',
        startInterpret: 'Начать анализ',
        uploadText: 'Пожалуйста, загрузите документы, которые требуют рискового оценивания.',
        interpretText: 'Пожалуйста, загрузите файлы для анализа',
        startTips: 'Теперь мы можем начать оценивать риски.',
        interpretTips: 'Теперь мы можем начать интерпретацию',
        infoExtract: 'Извлечение соглашения',
    },
    agent: {
        extractTitle: '信息提取',
        riskTitle: 'AI律师',
        feedback: '问卷反馈',
        toMini: '去小程序查看',
        otherContract: '让我看看其他合同的隐藏风险?',
        others: '其他',
        submit: '发送',
        autoExtract: '自动进行下一步提取直到提取结束',
        autoRisk: '自动进行下一步分析直到分析结束',
        aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',
        chooseRisk: '请选择需要进行分析的文件',
        chooseExtract: '请选择需要进行提取的文件',
        analyzing: '内容分析中',
        advice: '修改建议生成中',
        options: '选项生成中',
        inputTips: '请输入确切内容',
        chargeTip: '余额不足，请充值',
        original: '原文',
        revision: '修改建议',
        diff: '对比',
        locate: '获取原文定位中',
        custom: '请输入自定义审查规则',
        content: '原文位置',
        satisfy: '对分析结果满意，继续下一项分析',
        dissatisfy: '对分析结果不满意，重新进行分析',
        selectFunc: '请选择你期望使用的功能。',
        deepInference: 'AI-Юрист',
        deepThinking: '深度思考中',
        deepThoughtCompleted: '已深度思考',
        reJudge: '重新判断',
        confirm: 'подтверд',
        tipsContent: 'Повторная проверка уменьшит количество доступных попыток. Продолжить?',
        exportPDF: 'Экспорт отчёта PDF',
        defaultExportName: 'export.pdf',
        exporting: 'Идёт экспорт отчёта... Подождите',
    },
    authorize: {
        title: 'Условия использования',
        content: 'Умные контракты + ИИ-анализ = эффективная работа!Согласитесь для бесплатного доступа',
        cancel: 'Позже',
        confirm: 'Принять и начать',
        contract: 'Ознакомьтесь с 《Условиями использования продукта Хаббл》',
    },
    ...console,
    hubbleEntry: {
        smartAdvisor: 'ИИ-эксперт по контрактам',
        tooltips: 'Функция недоступна. Обратитесь к консультантам BestSign для подключения.',
        confirm: 'Понятно',
    },
    lang: 'ru',
};
