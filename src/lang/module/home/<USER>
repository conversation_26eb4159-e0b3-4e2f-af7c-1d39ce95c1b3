export default {
    home: 'Home',
    addEnterprise: 'Add new company ',
    lookoverEnterprise: 'View all companies',
    chooseEnterprise: 'Choose a company',
    enterEnterprise: 'Please enter the company name',
    inApproval: 'Approval…',
    copyright: 'Аll rights reserved',
    record: 'ICP main body record number: Zhejiang ICP No. 14031930',
    chooseSignType: 'Choose contract method',
    agreeProtocal: 'Terms of use',
    ok: 'Understood',
    newVersionTip: 'New version prompt',
    notice: 'Аnnouncement',
    clickToLook: 'Click to view',
    bizManage: 'Support enterprise unified management of business fields',
    protocalUpdate: 'Registration agreement update',
    startUse: 'Start using',
    joinSuccess: 'You have successfully joined',
    contactus: 'Contact us',
    askOnline: 'Online consultation',
    askWeChat: 'WeChat inquiry | WeChat subscription',
    downLoad: 'Download the user-end mobile app',
    convenience: 'Whether you are in the office, at home, or on the road, BestSign allows you to access, sign and send documents anywhere, anytime.',
    news: 'Welcome to follow our WeChat account for our latest news and information.',
    tip: 'Notice',
    unverify: 'You have not been certified by real name, and the recipient of the contract will not be able to identify you. We recommend that you first perform real-name certification.',
    isGroupProxyAuth: 'The current certification status of this company is group certification, and the contract recipient will not be able to identify your identity. It is recommended that you supplement the real-name certification materials first.',
    createCompnayP: {
        p1: 'Coming soon to generate a new business for you, please improve the company name',
        p2: 'Immediately provide the relevant company materials (for identification), and get the name of the company',
        p3: 'Temporarily not authenticated, fill in manually',
        p4: 'Uncertified business names show only to themselves,',
    },
    plzEnterRightEnt: 'Please enter the correct company name',
    goAuthenticate: 'Go to authentication',
    keepLaunch: 'Continue to initiate',
    enterprise: 'Company',
    person: 'Person',
    usercenter: 'Admin',
    console: 'Organization Management Platform',
    entAccount: 'Company account ',
    personAccount: 'Personal account ',
    viewAllEnt: 'View all companies',
    addEnt: 'Add new company ',
    createEnt: 'Create a business',
    exit: 'Log out',
    viewDetail: 'Click for details',
    message: 'Message',
    datainsure: 'Data protection',
    video: 'operation video',
    help: 'Help',
    dynamic: 'Product dynamic',
    contractManage: 'Contract',
    templateManage: 'Template',
    statisticCharts: 'Report',
    authenticating: 'Authenticates...',
    ecology: '来自{developerName}的企业账号',
    ecologyPerson: '来自{developerName}的个人账号',
    unAuthenticate: 'Do not authenticate',
    rejectAuthenticate: 'Authenticate rejected',
    contactManage: 'Please contact the administrator to assign you permission',
    noResult: 'no result',
    confirm: 'the confirmation',
    cancel: 'Cancel ',
    createSuccess: 'Created successfully',
    notifyPhone: 'Fill in the notification phone',
    phone: 'phone',
    phonePlaceholder: 'Fill in the notification phone',
    verifyCode: 'Verification code',
    sixNumberPlaceholder: 'Please input 6-digit code',
    submit: 'submit',
    phoneInputError: 'Please input correct telephone',
    verCodeInputErr: 'Verification Code Error',
    setSuccess: 'Success',
    offlineContractManage: 'Offline Contract',
    phoneSetTip: 'Submit the notification mobile phone number, which can be used to receive notification messages sent by the system, such as signing notifications, contract verification codes, etc.',
    noMoreTip: 'No more remind',
    phoneSetAfterTip: '(If you need to set it later, you can add your mobile phone number in the [Notification] of [User Center])',
    theEnterprise: 'The enterprise',
    you: 'you',
    passAutEntHint: 'Display the company name after passing the company certification',
    passAutPersonHint: 'Display personal name after passing real-name authentication',
    createEntNow: 'Create a business now',

    announcement: {
        pwReset: 'your password has expired，please go to The User Center to change your password to keep your account safe. ',
    },
    partAuthSearch: 'Query the real name of the opposite party before issuing the contract?',
};
