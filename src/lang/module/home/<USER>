export default {
    home: '首页',
    addEnterprise: '新增企业',
    lookoverEnterprise: '查看全部企业',
    chooseEnterprise: '选择企业',
    enterEnterprise: '请输入企业名称',
    inApproval: '审批中',
    copyright: '版权所有',
    record: 'ICP主体备案号：浙ICP备14031930号',
    chooseSignType: '选择签约方式',
    agreeProtocal: '同意协议',
    ok: '知道了',
    newVersionTip: '新版本提示',
    notice: '公告',
    clickToLook: '点击查看',
    bizManage: '支持企业对业务字段统一管理',
    protocalUpdate: '隐私政策更新',
    startUse: '开始使用',
    joinSuccess: '你已成功加入',
    contactus: '联系我们',
    askOnline: '在线咨询',
    askWeChat: '微信咨询 | 关注微信公众号',
    downLoad: '下载移动客户端',
    convenience: '无论你是在办公室、家里，还是在出差路上，上上签可以让你随时随地查阅、签署发送文件。',
    news: '欢迎关注我们的微信公众号，锁定我们最新的动态资讯。',
    tip: '提示',
    unverify: '还未实名认证，合同收件人将无法识别您的身份，建议您先进行实名认证。',
    isGroupProxyAuth: '本企业目前认证状态为集团代认证，合同收件人将无法识别您的身份，建议您先补充实名认证材料。',
    createCompnayP: {
        p1: '即将为您生成新企业，请完善企业名称',
        p2: '立即提交企业相关材料（用于实名认证），获得企业名称',
        p3: '暂不认证，手动填写',
        p4: '未经认证的企业名称仅对自己展示',
    },
    plzEnterRightEnt: '请输入正确的企业名称',
    goAuthenticate: '去认证',
    keepLaunch: '继续发起',
    enterprise: '企业',
    person: '个人',
    usercenter: '用户中心',
    console: '企业控制台',
    entAccount: '企业账号',
    personAccount: '个人账号',
    viewAllEnt: '查看全部企业',
    addEnt: '新增企业',
    createEnt: '创建企业',
    exit: '退出',
    viewDetail: '点击查看详情',
    message: '消息',
    datainsure: '数据保',
    video: '操作视频',
    help: '帮助',
    dynamic: '产品动态',
    contractManage: '合同管理',
    templateManage: '模板管理',
    statisticCharts: '统计报表',
    authenticating: '认证中企业',
    ecology: '来自{developerName}的企业账号',
    ecologyPerson: '来自{developerName}的个人账号',
    unAuthenticate: '未认证企业',
    rejectAuthenticate: '实名已驳回',
    contactManage: '请联系管理员给您分配权限',
    noResult: '没有结果',
    confirm: '确定',
    cancel: '取消',
    createSuccess: '创建成功',
    notifyPhone: '提交通知手机',
    phone: '手机',
    phonePlaceholder: '请填写11位手机号',
    verifyCode: '验证码',
    sixNumberPlaceholder: '请填写6位数字',
    submit: '提交',
    phoneInputError: '请输入正确的手机号',
    verCodeInputErr: '请输入正确的验证码',
    setSuccess: '设置成功',
    offlineContractManage: '线下合同管理',
    phoneSetTip: '提交通知手机号，可用于接受系统发出的通知短信，如签署通知、签约验证码等。',
    noMoreTip: '不再提醒',
    phoneSetAfterTip: '（如需稍后设置，可在【用户中心】的【通知】里补充手机号）',
    theEnterprise: '该企业',
    you: '您',
    passAutEntHint: '通过企业认证后显示企业名称',
    passAutPersonHint: '通过实名认证后显示个人姓名',
    createEntNow: '立即创建企业',

    announcement: {
        pwReset: '您的密码已过期，为了您的账户安全，请及时前往【用户中心】修改密码',
    },
    partAuthSearch: '发合同前查询下相对方企业实名情况？',
};
