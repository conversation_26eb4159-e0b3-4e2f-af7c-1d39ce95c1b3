export default {
    home: 'الصفحة الرئيسية',
    addEnterprise: 'إضافة مؤسسة',
    lookoverEnterprise: 'عرض جميع المؤسسات',
    chooseEnterprise: 'اختيار مؤسسة',
    enterEnterprise: 'يرجى إدخال اسم المؤسسة',
    inApproval: 'قيد الموافقة',
    copyright: 'جميع الحقوق محفوظة',
    record: 'رقم تسجيل ICP: 浙ICP备14031930号',
    chooseSignType: 'اختيار طريقة التوقيع',
    agreeProtocal: 'الموافقة على الاتفاقية',
    ok: 'فهمت',
    newVersionTip: 'تنبيه النسخة الجديدة',
    notice: 'إعلان',
    clickToLook: 'انقر للعرض',
    bizManage: 'دعم الإدارة الموحدة لحقول الأعمال للمؤسسات',
    protocalUpdate: 'تحديث سياسة الخصوصية',
    startUse: 'بدء الاستخدام',
    joinSuccess: 'لقد انضممت بنجاح إلى',
    contactus: 'اتصل بنا',
    askOnline: 'استشارة عبر الإنترنت',
    askWeChat: 'استشارة WeChat | متابعة الحساب العام',
    downLoad: 'تحميل تطبيق الهاتف المحمول',
    convenience: 'سواء كنت في المكتب أو المنزل أو في رحلة عمل، يمكن لـ BestSign أن يتيح لك مراجعة وتوقيع وإرسال المستندات في أي وقت وأي مكان.',
    news: 'مرحباً بك لمتابعة حسابنا العام على WeChat، ومتابعة أحدث أخبارنا.',
    tip: 'تنبيه',
    unverify: 'لم يتم التحقق من الهوية بعد، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإجراء التحقق من الهوية أولاً.',
    isGroupProxyAuth: 'حالة التحقق الحالية لمؤسستك هي تحقق بالوكالة للمجموعة، لن يتمكن مستلمو العقد من التعرف على هويتك، نوصي بإكمال مواد التحقق من الهوية أولاً.',
    createCompnayP: {
        p1: 'سيتم إنشاء مؤسسة جديدة لك، يرجى إكمال اسم المؤسسة',
        p2: 'تقديم مواد المؤسسة ذات الصلة فوراً (للتحقق من الهوية)، والحصول على اسم المؤسسة',
        p3: 'تخطي التحقق، ملء يدوي',
        p4: 'اسم المؤسسة غير المتحقق منه يظهر فقط لنفسك',
    },
    plzEnterRightEnt: 'يرجى إدخال اسم مؤسسة صحيح',
    goAuthenticate: 'اذهب للتحقق',
    keepLaunch: 'متابعة البدء',
    enterprise: 'مؤسسة',
    person: 'فرد',
    usercenter: 'مركز المستخدم',
    console: 'لوحة تحكم المؤسسة',
    entAccount: 'حساب مؤسسة',
    personAccount: 'حساب شخصي',
    viewAllEnt: 'عرض جميع المؤسسات',
    addEnt: 'إضافة مؤسسة',
    createEnt: 'إنشاء مؤسسة',
    exit: 'خروج',
    viewDetail: 'انقر لعرض التفاصيل',
    message: 'رسائل',
    datainsure: 'حماية البيانات',
    video: 'فيديو العمليات',
    help: 'مساعدة',
    dynamic: 'تحديثات المنتج',
    contractManage: 'إدارة العقود',
    templateManage: 'إدارة القوالب',
    statisticCharts: 'التقارير الإحصائية',
    authenticating: 'مؤسسة قيد التحقق',
    ecology: 'حساب مؤسسة من {developerName}',
    ecologyPerson: 'حساب شخصي من {developerName}',
    unAuthenticate: 'مؤسسة غير متحقق منها',
    rejectAuthenticate: 'تم رفض التحقق من الهوية',
    contactManage: 'يرجى الاتصال بالمدير لتخصيص الصلاحيات لك',
    noResult: 'لا توجد نتائج',
    confirm: 'تأكيد',
    cancel: 'إلغاء',
    createSuccess: 'تم الإنشاء بنجاح',
    notifyPhone: 'تقديم هاتف الإشعار',
    phone: 'هاتف',
    phonePlaceholder: 'يرجى إدخال رقم هاتف من 11 رقماً',
    verifyCode: 'رمز التحقق',
    sixNumberPlaceholder: 'يرجى إدخال 6 أرقام',
    submit: 'تقديم',
    phoneInputError: 'يرجى إدخال رقم هاتف صحيح',
    verCodeInputErr: 'يرجى إدخال رمز تحقق صحيح',
    setSuccess: 'تم الإعداد بنجاح',
    offlineContractManage: 'إدارة العقود خارج الإنترنت',
    phoneSetTip: 'تقديم رقم هاتف الإشعار، يمكن استخدامه لتلقي الرسائل القصيرة الصادرة عن النظام، مثل إشعارات التوقيع ورموز التحقق من التوقيع وغيرها.',
    noMoreTip: 'عدم التذكير مرة أخرى',
    phoneSetAfterTip: '(إذا كنت ترغب في الإعداد لاحقاً، يمكنك إضافة رقم الهاتف في [الإشعارات] في [مركز المستخدم])',
    theEnterprise: 'هذه المؤسسة',
    you: 'أنت',
    passAutEntHint: 'سيظهر اسم المؤسسة بعد اجتياز التحقق من هوية المؤسسة',
    passAutPersonHint: 'سيظهر الاسم الشخصي بعد اجتياز التحقق من الهوية',
    createEntNow: 'إنشاء مؤسسة الآن',

    announcement: {
        pwReset: 'انتهت صلاحية كلمة المرور الخاصة بك، لأمان حسابك، يرجى الذهاب إلى [مركز المستخدم] لتغيير كلمة المرور في أقرب وقت',
    },
    partAuthSearch: 'هل تريد البحث عن حالة التحقق من هوية الطرف الآخر قبل إرسال العقد؟',
};
