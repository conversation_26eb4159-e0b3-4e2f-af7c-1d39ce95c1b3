export default {
    docSlider: {
        linkContractMap: {
            notSupportFuctionTip: '会社がこの機能を有効にしていない場合は、カスタマー サービスに連絡して有効にすることができます。',
            dissolveContractTip: '确定要解除关联合同',
            inputContractNum: '请先输入合同编号',
            linkSuccess: '关联成功',
            linkExist: '合同关联已存在',
        },
    },
    docContentTable: {
        catchMap: {
            download: '下载',
            reject: '拒签',
            revoke: '撤销',
            delete: '删除',
            cantOperate: '无法{operate}合同',
            hybridNetHeader: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。',
            hybridNetMsg: '建议您：检查网络是否正常',
            checkNet: '请检查网络是否正常',
            hybridNotConnect: '原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',
            hybridSuggest: '建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行',
        },
        confirm: '确定',
        searchAll: '全选',
        isCheckingNet: '正在检查混合云网络环境',
        transferSucess: '转交成功',
    },
    docDialog: {
        confirm: '确认',
        cancel: '取消',
        notCliam: '合同未被认领',
        chooseNewOwner: '请选择新持有人',
        newOwner: '新持有人',
        originOwner: '原持有人',
        contractTransfer: '合同转交',
    },
};
