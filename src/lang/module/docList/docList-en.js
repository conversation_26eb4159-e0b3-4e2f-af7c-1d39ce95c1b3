export default {
    docSlider: {
        linkContractMap: {
            notSupportFuctionTip: 'If your company has not activated this function, you can contact customer service to activate it.',
            dissolveContractTip: 'Are you sure you want to dissolve the associated contract',
            inputContractNum: 'Please enter the contract number first',
            linkSuccess: 'Relate successfully',
            linkExist: 'Relation of these contracts already exist',
        },
    },
    docContentTable: {
        catchMap: {
            download: 'Download',
            reject: 'Refuse',
            revoke: 'Revoke',
            delete: 'Delete',
            cantOperate: 'Unable to {operate} contract',
            hybridNetHeader: 'The sender\'s enterprise uses the contract private storage method, but the current network cannot connect to the sender\'s contract storage server.',
            hybridNetMsg: 'I suggest you check if the network has been connected to the sender\'s intranet and try again.',
            checkNet: 'Please check if the network is connected to the intranet.',
            hybridNotConnect: 'Reason: Your company uses contract private storage, but the current network cannot connect to the contract storage server.',
            hybridSuggest: 'Recommended: (1) Check whether the network is normal; (2) Check whether the contract storage server is running normally',
        },
        confirm: 'OK',
        searchAll: 'Select all',
        isCheckingNet: 'Checking the hybrid cloud network environment',
        transferSucess: 'transfer successful',
    },
    docDialog: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        notCliam: 'the contract has not been claimed',
        chooseNewOwner: 'please select the new holder',
        newOwner: 'new holder',
        originOwner: 'previous holder',
        contractTransfer: 'contract transfer',
    },
};
