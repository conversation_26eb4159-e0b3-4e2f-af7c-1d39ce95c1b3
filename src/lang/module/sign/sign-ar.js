export default {
    ssoLoginConfig: {
        notBelongToEntTip: 'تحتاج إلى إعادة تسجيل الدخول إلى منصة BestSign لإرسال العقد (أو إدارة القوالب)',
        operationStep: {
            one: 'الخطوة الأولى: بعد النقر على متابعة، العودة إلى صفحة تسجيل الدخول',
            two: 'الخطوة الثانية: أدخل كلمة المرور للدخول إلى منصة BestSign',
            three: 'الخطوة الثالثة: إرسال العقد (أو إدارة القوالب)',
        },
        continue: 'متابعة',
        cancel: 'إلغاء',
        tip: 'تنبيه',
    },
    sign: {
        sealLabelsTip: 'تحتاج إلى وضع {sealLabelslen} ختم على العقد. سيقوم {personStr} بوضع {otherSealLen} أختام، والـ {mySealLen} المتبقية ستقوم أنت بختمها شخصياً. الأختام المطلوبة معروضة على الصفحة. يرجى التأكيد للمتابعة.',
        continue: 'متابعة',
        nonMainlandCARenewalTip: 'بعد طلب التجديد، سيرفض النظام تلقائياً نتيجة التحقق الأصلية. يرجى إكمال التحقق في أقرب وقت.',
        reselect: 'إعادة الاختيار',
        approvalFeatures: {
            dialogTitle: 'تقديم الميزات الجديدة',
            understand: 'فهمت',
            feature1: 'تعليق توضيحي على النص',
            feature2: 'تمييز الحقول',
            tip1: 'انقر على الزر لتمييز جميع "حقول محتوى القالب" في العقد لتسهيل التقاط المعلومات الرئيسية.',
            tip2: 'انقر على زر التلميح في الأسفل لتفعيل تمييز حقول محتوى القالب.',
            tip3: 'من خلال التمييز، يمكنك تحديد موقع حقول تعبئة المحتوى في العقد بسرعة وإكمال الموافقة بكفاءة.',
            tip4: 'اضغط مع الاستمرار على الماوس لتحديد فقرة ثم حرر الماوس، انقر على زر التعليق لإضافة نص تعليق توضيحي، بعد الانتهاء انقر على تعديل أو حذف. يمكن عرض محتوى التعليق في صفحة تفاصيل العقد - سجل العمليات الداخلية للشركة.',
            tip5: 'الخطوة الأولى: حدد حقل النص المراد التعليق عليه، أضف التعليق;',
            tip6: 'الخطوة الثانية: انقر لتحرير أو حذف التعليق.',
            annotate: 'تعليق',
            delete: 'حذف',
            edit: 'تعديل',
            operateTitle: 'إضافة تعليق للموافقة',
            placeholder: 'لا يتجاوز 255 حرفاً',
        },
        needRemark: 'لا تزال بحاجة إلى إضافة ملاحظة',
        notNeedRemark: 'لا تحتاج إلى إضافة ملاحظة',
        switchToReceiver: 'تم التبديل إلى {receiver}',
        notAddEntTip: 'المستخدم الحالي ليس عضواً في هذه المؤسسة، يرجى الاتصال بالمدير الرئيسي للانضمام إلى المؤسسة.',
        contractPartiesYouChoose: 'يمكنك اختيار الأطراف المتعاقدة:',
        contractPartyFilled: 'الطرف المتعاقد الذي حدده المرسل هو:',
        certifyOtherCompanies: 'التحقق من مؤسسات أخرى',
        youCanAlso: 'يمكنك أيضاً:',
        needVerification: 'تحتاج إلى التحقق من هويتك قبل التوقيع',
        prompt: 'تنبيه',
        submit: 'تأكيد',
        cancel: 'إلغاء',
        sign: 'توقيع الآن',
        addSeal: 'يرجى تسجيل الدخول إلى موقع BestSign باستخدام الكمبيوتر لإضافة ختم',
        noSealAvailable: 'عذراً، ليس لديك أختام متاحة حالياً، يرجى الاتصال بمدير المؤسسة لإضافة ختم وتفويضه.',
        memberNoSealAvailable: 'لا توجد أختام متاحة حالياً، يرجى الاتصال بالمدير للتكوين قبل التوقيع. أو اتصل بالمدير الرئيسي شخصياً للتكوين.',
        noticeAdminFoSeal: 'إرسال إشعار للمدير',
        requestSomeone: 'طلب تحقق شخص آخر',
        requestOthersToContinue: 'إخطار المدير لاستكمال التحقق من الهوية',
        requestOthersToContinueSucceed: 'تم إرسال إشعار إلى المدير',
        requestSomeoneList: 'طلب من الأشخاص التاليين إكمال التحقق من الهوية:',
        electronicSeal: 'الختم الإلكتروني',
        changeTheSeal: 'لا تريد استخدام هذا الختم؟ يمكنك تغييره بعد التحقق من الهوية',
        goToVerify: 'اذهب للتحقق من الهوية',
        noSealToChoose: 'لا توجد أختام متاحة للتبديل، إذا كنت بحاجة إلى إدارة الأختام، يرجى التحقق من الهوية أولاً',
        goVerify: 'اذهب للتحقق',
        goToVerifyEnt: 'اذهب للتحقق من المؤسسة',
        digitalCertificateTip: 'BestSign يقوم بتفعيل شهادتك الرقمية',
        signDes: 'أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',
        signAgain: 'متابعة التوقيع',
        send: 'إرسال',
        person: 'شخصي',
        ent: 'مؤسسة',
        entName: 'اسم المؤسسة',
        account: 'الحساب',
        accountPH: 'الهاتف أو البريد الإلكتروني',
        approved: 'موافق',
        signVerification: 'التحقق من التوقيع',
        cannotReview: 'لا يمكن عرض العقد',
        connectFail: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود.',
        connectFailTip: 'يمكنك محاولة الحلول التالية:',
        connectFailTip1: '1. تحديث الصفحة.',
        connectFailTip2: '2. انتظر بصبر وحاول مرة أخرى لاحقاً. قد يكون السبب هو حدوث خلل في الخادم الذي نشرته المؤسسة المرسلة، ويحتاج فريق تكنولوجيا المعلومات إلى وقت لإعادة تشغيل الخادم.',
        connectFailTip3: '3. هل أكدت المؤسسة المرسلة أنك تحتاج إلى استخدام شبكة WiFi محددة للوصول؟ إذا كان هناك مثل هذا التوضيح، فأنت بحاجة إلى تغيير الشبكة المتصلة بهاتفك أو جهاز الكمبيوتر.',
        personalMaterials: 'يطلب المرسل منك توفير مزيد من مواد التحقق',
        noSupportface: 'يطلب مُنشئ العقد التوقيع بالتعرف على الوجه، ولكن لا يتوفر التوقيع بالتعرف على الوجه لغير المقيمين في البر الرئيسي، يرجى الاتصال بالمُنشئ لتعديل متطلبات التوقيع',
        lackEntName: 'يرجى إدخال اسم المؤسسة',
        errAccount: 'يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح',
        noticeAdmin: 'طلب الانضمام',
        signDone: 'اكتمل التوقيع',
        signDoneTip: 'لقد وقعت هذا العقد',
        approveDone: 'اكتملت الموافقة',
        approveDoneTip: 'لقد وافقت على هذا العقد',
        completeSign: 'يرجى النقر على "موقع الختم" أو "موقع التوقيع" لإكمال التوقيع',
        fillFirst: 'يرجى ملء محتوى العقد في مربع الإدخال أولاً',
        stillSignTip: 'بعد توقيعك على {alias} هذا، قد يقوم الموقعون الآخرون بتغيير محتوى {alias}، هل تريد المتابعة؟',
        signHighLightTip: 'يوجد {count} موقع في {alias} يمكن إضافته أو تعديله',
        riskDetails: 'تفاصيل المخاطر',
        noviewDifference: 'نظراً لأن المُنشئ قد فعّل وظيفة السماح للموقعين بملء الحقول الثابتة في {alias}، فقد يقوم الموقعون الآخرون بتغيير محتوى العقد الذي حدده المُنشئ. لا يقوم BestSign بمراجعة الاختلافات في المحتوى بين النسخة قبل التوقيع والنسخة السارية. عند اكتمال توقيعك على {alias}، يُعتبر أنك توافق على إضافة أو تعديل محتوى الحقول الثابتة في {alias} من قبل الموقعين الآخرين، وتقر بالنسخة السارية بعد اكتمال توقيع جميع الأطراف على {alias}.\n' +
            'إذا كنت لا توافق على السماح للموقعين الآخرين بتغيير حقول {alias} بعد توقيعك، يمكنك رفض هذا التوقيع والتفاوض مع المرسل (أي مطالبة المُنشئ بإغلاق وظيفة "ملء الحقول من قبل الموقع" لتجنب المخاطر المرتبطة).',
        highLightTip: 'سيتم عرض هذا المحتوى المحفوف بالمخاطر بتأثير "تمييز"، يرجى التحقق بعناية. يمكن إلغاء التمييز بتحديث الصفحة.',
        commonTip: 'تنبيه',
        understand: 'فهمت',
        view: 'عرض',
        start: 'بدء',
        nextStep: 'الخطوة التالية',
        help: 'مساعدة',
        faceFailed: 'عذراً، فشلت مطابقة الوجه',
        dualFailed: 'نعتذر، لم تنجح عملية التحقق من التسجيل المزدوج. يرجى التحقق من معلوماتك والمحاولة مرة أخرى.',
        faceFailedtips: 'تنبيه',
        verifyTry: 'يرجى التحقق من معلومات الهوية والمحاولة مرة أخرى',
        faceLimit: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم',
        upSignReq: 'تم الوصول إلى الحد الأقصى لعدد مرات مطابقة الوجه اليوم، يرجى المحاولة غداً أو الاتصال بمُنشئ العقد لتعديل متطلبات التوقيع',
        reqFace: 'يطلب المرسل منك التحقق من الوجه',
        signAfterFace: 'يمكنك إكمال توقيع العقد بعد اجتياز التحقق من الوجه',
        qrcodeInvalid: 'انتهت صلاحية معلومات رمز QR، يرجى التحديث',
        faceFirstExceed: 'فشل التحقق من الوجه، سيتم استخدام رمز التحقق بدلاً من ذلك',
        date: 'التاريخ',
        chooseSeal: 'اختيار الختم',
        seal: 'ختم',
        signature: 'توقيع',
        handwrite: 'كتابة يدوية',
        mysign: 'توقيعي',
        approvePlace: 'رسالة الموافقة، اختيارية',
        approvePlace_1: 'رسالة الموافقة',
        approvePlace_2: 'اختياري، لا يتجاوز 255 حرفاً.',
        approveAgree: 'نتيجة الموافقة: موافق',
        approveReject: 'نتيجة الموافقة: مرفوض',
        signBy: 'بواسطة',
        signByEnd: 'ختم',
        sealBy: 'بواسطة',
        sealByEnd: 'توقيع',
        coverBy: 'يحتاج إلى ختم',
        applicant: 'مقدم الطلب',
        continueVeri: 'متابعة التحقق',
        registerAndReal: 'يرجى التسجيل والتحقق من الهوية',
        goToResiter: 'يرجى التسجيل والتحقق',
        sureToUse: 'تأكيد الاستخدام',
        toSign: 'توقيع?',
        pleaseComplete: 'يرجى إكمال',
        confirmSign: 'ثم تأكيد التوقيع',
        admin: 'المدير',
        contratAdmin: 'يرجى الاتصال بالمدير لإضافة حسابك',
        addToEnt: 'كعضو في المؤسسة',
        alreadyExists: 'موجود بالفعل في BestSign',
        sendMsg: 'سيرسل BestSign المحتوى التالي إلى المدير عبر الرسائل القصيرة:',
        applyJoin: 'طلب الانضمام',
        title: 'العنوان',
        viewImg: 'عرض الصورة',
        priLetter: 'رسالة خاصة',
        priLetterFromSomeone: 'رسالة خاصة من {name}',
        readLetter: 'فهمت',
        approve: 'موافق',
        disapprove: 'رفض',
        refuseSign: 'رفض التوقيع',
        paperSign: 'التحول إلى التوقيع الورقي',
        refuseTip: 'يرجى اختيار سبب الرفض',
        refuseReason: 'تعبئة سبب رفض التوقيع يساعد الطرف الآخر على فهم مشكلتك وتسريع عملية العقد',
        reasonWriteTip: 'يرجى إدخال سبب رفض التوقيع',
        refuseReasonOther: 'المزيد من أسباب رفض التوقيع (اختياري) | المزيد من أسباب رفض التوقيع (مطلوب)',
        refuseConfirm: 'رفض التوقيع',
        refuseConfirmTip: 'أنت ترفض التوقيع بسبب "{reason}"، هل تريد المتابعة؟ بعد التأكيد، لن تتمكن من التوقيع مرة أخرى.',
        waitAndThink: 'دعني أفكر مرة أخرى',
        signValidationTitle: 'التحقق من التوقيع',
        email: 'البريد الإلكتروني',
        phoneNumber: 'رقم الهاتف',
        password: 'كلمة المرور',
        verificationCode: 'رمز التحقق',
        mailVerificationCode: 'رمز التحقق',
        forgetPsw: 'نسيت كلمة المرور',
        if: '، هل',
        forgetPassword: 'نسيت كلمة المرور',
        rejectionVer: 'التحقق من رفض التوقيع',
        msgTip: 'لم تستلم الرسالة القصيرة؟ جرب',
        voiceVerCode: 'رمز التحقق الصوتي',
        SMSVerCode: 'رمز التحقق عبر الرسائل القصيرة',
        or: 'أو',
        emailVerCode: 'رمز التحقق عبر البريد الإلكتروني',
        SentSuccessfully: 'تم الإرسال بنجاح!',
        intervalTip: 'الفاصل الزمني بين الإرسال قصير جداً',
        signPsw: 'كلمة مرور التوقيع',
        useSignPsw: 'استخدام كلمة مرور التوقيع للتحقق',
        setSignPsw: 'إعداد التحقق بكلمة مرور التوقيع',
        useVerCode: 'استخدام رمز التحقق',
        inputVerifyCodeTip: 'يرجى إدخال رمز التحقق',
        inputSignPwdTip: 'يرجى إدخال كلمة مرور التوقيع',
        signConfirmTip: {
            1: 'هل أنت متأكد من أنك تريد توقيع {contract} هذا؟',
            2: 'النقر على زر التأكيد سيؤدي إلى التوقيع الفوري على {contract} هذا',
            confirm: 'تأكيد التوقيع',
        },
        signSuc: 'تم التوقيع بنجاح',
        refuseSuc: 'تم رفض التوقيع بنجاح',
        approveSuc: 'تمت الموافقة بنجاح',
        hdFile: 'عرض الملف عالي الدقة',
        otherOperations: 'عمليات أخرى',
        reviewDetails: 'تفاصيل الموافقة',
        close: 'إغلاق',
        submitter: 'مقدم الطلب',
        signatory: 'الموقع',
        reviewSchedule: 'مراحل المراجعة',
        signByPc: 'تم التوقيع بواسطة {name}',
        signPageDescription: 'الصفحة {index} من {total}',
        sealBySomeone: 'تم الختم بواسطة {name}',
        signDate: 'تاريخ التوقيع',
        download: 'تحميل',
        signPage: 'عدد الصفحات: {page}',
        signNow: 'التوقيع الآن',
        sender: 'المرسل',
        signer: 'الموقع',
        startSignTime: 'وقت بدء التوقيع',
        signDeadLine: 'الموعد النهائي للتوقيع',
        authGuide: {
            goToHome: 'العودة للصفحة الرئيسية',
            tip_1: 'بعد اكتمال التحقق، يمكنك عرض وتوقيع العقد.',
            tip_2: 'يرجى استخدام الهوية | للتحقق.',
            tip_3: 'تم إرسال العقد',
            tip_4: 'يرجى الاتصال بمنشئ العقد | لتغيير المستلم.',
            tip_5: 'هويتك المؤكدة | لا يمكنها عرض العقد',
            new_tip_1: 'بناءً على متطلبات المرسل، تحتاج إلى إكمال الخطوات التالية:',
            new_tip_2: 'بناءً على متطلبات المرسل، تحتاج إلى:',
            new_tip_3: 'إكمال الخطوات التالية.',
            new_tip_4: 'إذا كان لديك بالفعل صلاحيات الختم، سيتم تجاوز الخطوة 2 تلقائياً',
            entUserName: 'الاسم:',
            idNumberForVerify: 'رقم الهوية:',
            realNameAuth: 'التحقق من الهوية الحقيقية',
            applySeal: 'طلب ختم',
            signContract: 'توقيع العقد',
        },
        switch: 'تبديل',
        rejectReasonList: {
            // authReason: '不想/不会做实名认证',
            signOperateReason: 'لدي استفسارات حول عملية التوقيع/التحقق، وأحتاج إلى مزيد من التواصل',
            termReason: 'لدي تحفظات على شروط/محتوى العقد، وأحتاج إلى مزيد من التواصل',
            explainReason: 'لست على دراية بمحتوى العقد، يرجى الإخطار مسبقاً',
            otherReason: 'أخرى (يرجى ذكر السبب)',
        },
        selectSignature: 'اختيار التوقيع',
        selectSigner: 'اختيار الموقع',
        pleaseScanToSign: 'يرجى المسح الضوئي للتوقيع باستخدام Alipay أو WeChat',
        pleaseScanAliPay: 'يرجى مسح رمز QR باستخدام تطبيق Alipay للتوقيع',
        pleaseScanWechat: 'يرجى مسح رمز QR باستخدام تطبيق WeChat للتوقيع',
        requiredFaceSign: 'يطلب مرسل العقد منك التوقيع بالتعرف على الوجه',
        requiredDualSign: 'يطلب مرسل العقد منك إكمال عملية التحقق من التسجيل المزدوج',
        verCodeVerify: 'التحقق من رمز التحقق',
        applyToSign: 'طلب توقيع العقد',
        autoRemindAfterApproval: '*بعد الموافقة، سيتم إرسال تذكير توقيع تلقائي للموقع',
        cannotSignBeforeApproval: 'لم تكتمل الموافقة، لا يمكن التوقيع حالياً!',
        finishSignatureBeforeSign: 'يرجى إكمال الختم/التوقيع قبل تأكيد التوقيع',
        uploadFileOnRightSite: 'لديك مرفقات لم يتم تحميلها بعد، يرجى تحميلها في الشريط الجانبي الأيمن أولاً',
        cannotApplySealNeedPay: 'هذا العقد يتطلب منك الدفع، لا يدعم طلب ختم شخص آخر',
        unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',
        units: '{num} نسخة',
        contractToPrivate: 'عقد شخصي',
        contractToPublic: 'عقد مؤسسي',
        paySum: 'المجموع {sum} يتطلب دفعك',
        payTotal: 'المجموع {total} يوان.',
        fundsLack: 'عدد العقود المتاحة لديك غير كافٍ، لضمان توقيع العقد بسلاسة، نقترح إعادة الشحن على الفور.',
        contactToRecharge: 'يرجى الاتصال بالمدير الرئيسي لإعادة الشحن.',
        deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية.',
        needSignerPay: 'قام مرسل العقد بتعيين الدفع على الطرف المقابل، وحددك لدفع رسوم العقد.',
        recharge: 'إعادة الشحن',
        toSubmit: 'تقديم',
        appliedSeal: 'تم تقديم طلب الختم',
        noSeal: 'لا يوجد ختم',
        noSwitchSealNeedDistribute: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بمدير المؤسسة الرئيسي لإضافة ختم وتفويضه',
        viewApproveProcess: 'عرض عملية الموافقة',
        approveProcess: 'عملية الموافقة',
        noApproveContent: 'لم يتم تقديم مواد الموافقة',
        knew: 'فهمت',
        noSwitchSealNeedAppend: 'لا توجد أختام متاحة للتبديل، يرجى الاتصال بالمدير لإضافة ختم',
        hadAutoSet: 'تم تلقائياً في {num} موضع آخر',
        setThatSignature: 'وضع هذا التوقيع',
        setThatSeal: 'وضع هذا الختم',
        applyThatSeal: 'طلب هذا الختم',
        hasSetTip: 'تم الوضع تلقائياً في {index} موضع آخر',
        hasSetSealTip: 'تم وضع هذا الختم تلقائياً في {index} موضع آخر',
        hasSetSignatureTip: 'تم وضع هذا التوقيع تلقائياً في {index} موضع آخر',
        hasApplyForSealTip: 'تم طلب هذا الختم تلقائياً في {index} موضع آخر',
        savedOnLeftSite: 'تم الحفظ في شريط التوقيع على اليسار',
        ridingSealMinLimit: 'المستند صفحة واحدة فقط، لا يمكن وضع ختم متداخل',
        ridingSealMaxLimit: 'يتجاوز 146 صفحة، لا يدعم وضع ختم متداخل',
        ridingSealMinOrMaxLimit: 'المستند إما صفحة واحدة أو يتجاوز 146 صفحة، لا يمكن وضع ختم متداخل',
        noSealForRiding: 'ليس لديك ختم متاح للاستخدام، لا يمكن وضع ختم متداخل',
        noSwitchSealNeedAppendBySelf: 'لا توجد أختام متاحة للتبديل، يمكنك الذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',
        gotoAppendSeal: 'اذهب لإضافة ختم',
        approvalFlowSuccessfulSet: 'تم إعداد تدفق الموافقة بنجاح',
        mandate: 'موافقة على التفويض',
        loginToAppendSeal: 'يمكنك أيضاً تسجيل الدخول إلى BestSign باستخدام الكمبيوتر والذهاب إلى لوحة تحكم المؤسسة لإضافة ختم',
        signIdentityAs: 'تقوم حالياً بالتوقيع باسم {person}',
        enterNextContract: 'انتقل إلى العقد التالي',
        fileList: 'قائمة الملفات',
        addSignerFile: 'إضافة مواد ملحقة',
        signatureFinish: 'تم الختم/التوقيع بالكامل',
        dragSignatureTip: 'يرجى سحب الختم/التاريخ التالي وإفلاته في المستند، يمكن السحب والإفلات عدة مرات',
        noticeToManager: 'إرسال إشعار للمدير',
        gotoAuthPerson: 'اذهب للتحقق الشخصي',
        senderRequire: 'يطلب المرسل',
        senderRequireUseFollowIdentity: 'يطلب المرسل منك استيفاء أحد الهويات التالية',
        suggestToAuth: 'لم تقم بالتحقق من هويتك بعد، نقترح التحقق من هويتك قبل التوقيع',
        contactEntAdmin: 'يرجى الاتصال بمدير المؤسسة الرئيسي',
        setYourAccount: 'لتعيين حسابك',
        authInfoUnMatchNeedResend: 'للتوقيع على العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد',
        noEntNameNeedResend: 'لم يتم تحديد اسم المؤسسة الموقعة، لا يمكن توقيع هذا العقد، يرجى الاتصال بالمُنشئ لإعادة إرسال العقد',
        pleaseUse: 'يرجى استخدام',
        me: 'أنا',
        myself: 'نفسي،',
        reAuthBtnTip: 'أنا المستخدم الفعلي لرقم الهاتف هذا،',
        reAuthBtnContent: 'بعد إعادة التحقق من الهوية، سيتم رفض التحقق الأصلي لهذا الحساب، يرجى التأكيد.',
        descNoSame1: ' للتوقيع على العقد بهوية',
        descNoSame2: 'هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',
        authInfoNoSame: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية المؤكدة لحسابك الحالي المسجل.',
        authInfoNoSame2: 'للتوقيع على العقد بهوية. هذا لا يتطابق مع معلومات الهوية الأساسية لحسابك الحالي المسجل.',
        goHome: 'العودة إلى قائمة العقود>>',
        authInfo: 'تم اكتشاف أن هوية حسابك الحالي المؤكدة هي ',
        authInfo2: 'تم اكتشاف أن معلومات الهوية الأساسية لحسابك الحالي هي ',
        in: 'في',
        finishAuth: 'أكمل التحقق من الهوية للتوقيع القانوني على العقد',
        ask: 'هل تريد متابعة التوقيع بالحساب الحالي؟',
        reAuthBtnText: 'نعم، أريد إعادة التحقق من الهوية والتوقيع بهذا الحساب',
        changePhoneText: 'لا، اتصل بالمرسل لتغيير رقم هاتف التوقيع',
        changePhoneTip1: 'بناءً على طلب المرسل، يرجى الاتصال بـ',
        changePhoneTip2: 'لتغيير معلومات التوقيع (رقم الهاتف/الاسم)، وتحديد توقيعك.',
        confirmOk: 'تأكيد',
        goOnAuth: {
            0: 'للتحقق،',
            1: 'يرجى التحقق من الهوية،',
            2: 'للتحقق من الهوية،',
        },
        signContractAfterAuth: {
            0: 'بعد اكتمال التحقق، يمكنك توقيع العقد.',
            1: 'بعد اكتمال التحقق، يمكنك توقيع العقد.',
        },
        useIdentity: 'باستخدام هوية {name}',
        inTheName: 'باسم',
        of: 'من',
        identity: 'هوية',
        nameIs: 'الاسم هو',
        IDNumIs: 'رقم الهوية هو',
        provideMoreAuthData: 'توفير مزيد من مواد التحقق',
        leadToAuthBeforeSign: 'متابعة التحقق قبل التوقيع على العقد',
        groupProxyAuthNeedMore: 'حالة التحقق الحالية هي تحقق بالوكالة للمجموعة، إذا كنت تريد التوقيع بشكل منفصل، يرجى توفير مواد تحقق إضافية',
        contactSender: 'إذا كان لديك أي استفسارات، يرجى الاتصال بالمرسل.',
        note: 'ملاحظة:',
        identityInfo: 'معلومات الهوية',
        signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً للتوقيع على العقد.',
        needAuthPermissionContactAdmin: 'ليس لديك صلاحية التحقق من الهوية حالياً، يرجى الاتصال بالمدير',
        iHadReadContract: 'قرأت وفهمت محتوى {alias}',
        scrollToBottomTip: 'تحتاج إلى التمرير حتى الصفحة الأخيرة',
        getVerCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',
        appScanVerify: 'التحقق بمسح رمز QR في تطبيق BestSign',
        downloadBSApp: 'تحميل تطبيق BestSign',
        scanned: 'تم المسح بنجاح',
        confirmInBSApp: 'يرجى تأكيد التوقيع في تطبيق BestSign',
        qrCodeExpired: 'انتهت صلاحية رمز QR، يرجى التحديث والمحاولة مرة أخرى',
        appKey: 'التحقق الأمني للتطبيق',
        goToScan: 'اذهب للمسح',
        setNotificationInUserCenter: 'يرجى تعيين طريقة الإشعار في مركز المستخدم أولاً',
        doNotWantUseVerCode: 'لا أريد استخدام رمز التحقق',
        try: 'جرب',
        retry: 'إعادة المحاولة',
        goToFaceVerify: 'اذهب للتحقق من الوجه',
        faceExceedTimes: 'تم الوصول إلى الحد الأقصى لعدد مرات التحقق من الوجه اليوم',
        returnBack: 'عودة',
        switchTo: 'التبديل إلى',
        youCanChooseIdentityBlow: 'يمكنك اختيار أحد الأطراف المتعاقدة التالية',
        needDrawSignatureFirst: 'ليس لديك توقيع، يرجى إضافة توقيع يدوي أولاً',
        lacksSealNeedAppend: 'لم تقم بإضافة أي أختام بعد، يرجى إضافة ختم أولاً.',
        manageSeal: 'إدارة الأختام',
        needDistributeSealToSelf: 'ليس لديك ختم متاح، يرجى تعيين نفسك كحامل للختم أولاً',
        chooseSealAfterAuth: 'لا تريد استخدام الختم أعلاه؟ يمكنك تغيير الختم بعد التحقق من الهوية',
        appendDrawSignature: 'إضافة توقيع يدوي',
        senderUnFill: '(لم يملأ المرسل)',
        declare: 'توضيح',
        fileLessThan: 'يرجى تحميل ملف أقل من {num}M',
        fileNeedUploadImg: 'يرجى استخدام تنسيقات الملفات المدعومة عند التحميل',
        serverError: 'واجه الخادم مشكلة صغيرة، يرجى المحاولة مرة أخرى لاحقاً',
        oldFormatTip: 'يدعم تنسيقات jpg، png، jpeg، pdf، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',
        fileLimitFormatAndSize: 'لا يتجاوز عدد صور المواد الواحدة 10 صور.',
        fileFormatImage: 'يدعم تنسيقات jpg، png، jpeg، حجم الصورة الواحدة لا يتجاوز 20M، يسمح بتحميل 10 صور',
        fileFormatFile: 'يدعم تنسيقات pdf، excel، word، txt، zip، xml، حجم الملف الواحد لا يتجاوز 10M',
        signNeedKnow: 'ملاحظات التوقيع',
        signNeedKnowFrom: 'ملاحظات التوقيع من {sender}',
        approvalInfo: 'ملاحظات الموافقة',
        approveNeedKnowFrom: 'مواد الموافقة المقدمة من {sender}-{sendEmployeeName} ({approvalType})',
        approveBeforeSend: 'الموافقة قبل إرسال العقد',
        approveBeforeSign: 'الموافقة قبل توقيع العقد',
        approveOperator: 'الموافق',
        approvalOpinion: 'رسالة الموافقة',
        employeeDefault: 'موظف',
        setLabel: 'تعيين العلامة',
        addRidingSeal: 'إضافة ختم متداخل',
        delRidingSeal: 'حذف الختم المتداخل',
        file: 'ملف مرفق',
        compressedFile: 'ملف مضغوط',
        attachmentContent: 'محتوى المرفق',
        pleaseClickView: '(يرجى النقر للتحميل والعرض)',
        downloadFile: 'تحميل الملف الأصلي',
        noLabelPleaseAppend: 'لا توجد علامات بعد، يرجى الذهاب إلى لوحة تحكم المؤسسة للإضافة',
        archiveTo: 'أرشفة إلى',
        hadArchivedToFolder: 'تم نقل العقد بنجاح إلى مجلد {folderName} الخاص بـ {who}',
        pleaseScanToHandleWrite: 'يرجى مسح الرمز باستخدام WeChat أو متصفح الهاتف للتوقيع اليدوي على الجهاز المحمول',
        save: 'حفظ',
        remind: 'تذكير',
        riskTip: 'تنبيه المخاطر',
        chooseApplyPerson: 'اختيار منفذ الختم',
        chooseAdminSign: 'اختيار مدير الأختام',
        useSealByOther: 'ختم بواسطة شخص آخر',
        getSeal: 'الحصول على ختم',
        nowApplySealList: 'أنت تطلب الأختام التالية',
        nowAdminSealList: 'أنت تطلب الحصول على الأختام التالية',
        chooseApplyPersonToDeal: 'يرجى اختيار منفذ الختم، سيتم معالجة العقد من قبل الشخص المختار (يمكنك متابعة المشاهدة ومتابعة هذا العقد)',
        chooseTransferPerson: 'تحويل التوقيع لشخص آخر',
        chooseApplyPersonToMandate: 'يرجى اختيار مدير الأختام، عندما يتلقى الشخص المختار الإشعار ويوافق على المراجعة، ستحصل على صلاحية استخدام هذا الختم، وعندها يمكنك استخدام هذا الختم للختم وتوقيع العقد',
        contactGroupAdminToDistributeSeal: 'يرجى الاتصال بمدير المجموعة لتخصيص الختم',
        sealApplySentPleaseWait: 'تم إرسال طلب تخصيص الختم، يرجى انتظار الموافقة. أو يمكنك اختيار طريقة ختم أخرى',
        successfulSent: 'تم الإرسال بنجاح',
        authTip: {
            t2: ['ملاحظة:', 'يجب أن تتطابق تماماً للتوقيع على العقد.', 'اسم المؤسسة', 'معلومات الهوية', 'يجب أن تتطابق تماماً لعرض وتوقيع العقد.'],
            t3: '{x} يطلب منك {text} التحقق من الهوية.',
            tCommon1: 'باسم {entName}',
            tCommon2_1: 'باسم {name} ورقم هوية {idCard}',
            tCommon2_2: 'باسم {name}',
            tCommon2_3: 'برقم هوية {idCard}',
            viewAndSign1: 'بعد اكتمال التحقق يمكنك عرض وتوقيع العقد.',
            viewAndSignConflict: '{x} يطلب منك {text} لعرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المؤكدة. يرجى الاتصال بالمُنشئ للتأكد من معلومات الهوية وطلب إعادة إنشاء العقد.',
        },
        needSomeoneToSignature: 'يحتاج {x} إلى ختم {y}',
        needToSet: 'يحتاج إلى ختم',
        approver: 'مقدم الطلب:',
        clickToSignature: 'انقر هنا للتوقيع',
        transferToOtherToSign: 'تحويل للتوقيع من شخص آخر',
        signatureBy: 'توقيع بواسطة {x}',
        tipRightNumber: 'يرجى إدخال رقم صحيح',
        tipRightIdCard: 'يرجى إدخال رقم هوية مقيم في البر الرئيسي من 18 رقماً بشكل صحيح',
        tipRightPhoneNumber: 'يرجى إدخال رقم هاتف من 11 رقماً بشكل صحيح',
        tip: 'تنبيه',
        tipRequired: 'لا يمكن ترك الحقول المطلوبة فارغة',
        confirm: 'تأكيد',
        viewContractDetail: 'عرض تفاصيل العقد',
        required: 'مطلوب',
        optional: 'اختياري',
        decimalLimit: 'محدود بـ {x} أرقام بعد العلامة العشرية',
        intLimit: 'يجب أن يكون رقماً صحيحاً',
        invalidContract: 'توقيع هذا العقد يعني موافقتك على إلغاء العقود التالية:',
        No: 'الرقم',
        chooseFrom2: 'قام المرسل بتعيين اختيار واحد من اثنين للختم، يرجى اختيار موضع واحد للختم',
        crossPlatformCofirm: {
            message: 'مرحباً، يحتاج العقد الحالي إلى توقيع عبر المنصات، وستحتاج الملفات الموقعة إلى النقل خارج الحدود، هل توافق؟',
            title: 'تفويض البيانات',
            confirmButtonText: 'موافق على التفويض',
            cancelButtonText: 'إلغاء',
        },
        sealScope: 'نطاق استخدام الختم',
        currentContract: 'العقد الحالي',
        allContract: 'جميع العقود',
        docView: 'معاينة العقد',
        fixTextDisplay: 'تصحيح رموز الصفحة غير المقروءة',
        allPage: '{num} إجمالي الصفحات',
        notJoinTip: 'يرجى الاتصال بالمسؤول ليضيفك كعضو في الشركة قبل التوقيع',

    },
    signJa: {
        beforeSignTip1: 'بناءً على طلب المرسل، يرجى التوقيع باسم هذه المؤسسة:',
        beforeSignTip2: 'حدد المرسل {signer} لإكمال التوقيع. إذا كانت المعلومات صحيحة، يمكنك التوقيع مباشرة.',
        beforeSignTip3: 'إذا كانت المعلومات غير صحيحة، يرجى الاتصال بالمرسل لتغيير معلومات الموقع المحدد.',
        beforeSignTip4: 'تم اكتشاف أن الاسم المسجل لهذا الحساب هو {currentUser}، وهو لا يتطابق مع {signer} المطلوب من المرسل حالياً، هل تؤكد التغيير إلى {signer}',
        beforeSignTip5: 'تم اكتشاف أن الاسم المرتبط بالحساب الحالي هو: {currentUser}، وهو لا يتطابق مع متطلب الطرف الأول بتوقيع {signer}',
        beforeSignTip6: 'يرجى التأكيد على التغيير إلى {signer} المحدد من قبل الطرف الأول للتوقيع وفقاً للوضع الفعلي',
        beforeSignTip7: 'أو التواصل مع الطرف الأول لتغيير الموقع المحدد',
        entNamePlaceholder: 'يرجى إدخال اسم المؤسسة',
        corporateNumberPlaceholder: 'يرجى إدخال رقم الشركة',
        corporateNumber: 'رقم الشركة',
        singerNamePlaceholder: 'يرجى إدخال اسم الموقع',
        singerName: 'اسم الموقع',
        itsMe: 'هذا أنا',
        wrongInformation: 'معلومات خاطئة',
        confirmChange: 'تأكيد التغيير',
        communicateSender1: 'لا تغيير، التواصل مع الطرف الأول',
        communicateSender2: 'إلغاء، التواصل مع المرسل',
        createSeal: {
            title: 'إدخال الاسم',
            tip: 'يرجى إدخال اسمك (يمكن استخدام المسافة للتغيير إلى سطر جديد)',
            emptyErr: 'يرجى إدخال الاسم',
        },
        areaRegister: 'مكان تسجيل الشركة',
        jp: 'اليابان',
        cn: 'الصين',
        are: 'الإمارات العربية المتحدة',
        other: 'أخرى',
        plsSelect: 'يرجى الاختيار',
        tip1: 'يجب على الشركات المسجلة في البر الرئيسي للصين إكمال التسجيل باستخدام الاسم الحقيقي على ent.bestsign.cn. عند توقيع العقود مع الشركات خارج البر الرئيسي للصين، يمكن استخدام وظيفة "التوقيع عبر الحدود" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',
        tip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للصين، يمكنك تسجيل الدخول مباشرة إلى ent.bestsign.cn واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الصينية.',
        tip3: 'يرجى تقديم رقم الوثيقة الذي حصلت عليه من هيئة الرقابة التجارية المحلية',
        tip4: 'يرجى اتباع الخطوات التالية',
        tip5: '1. يرجى الاتصال بمدير العلاقات الخاص بك لإرشادك خلال عملية التحقق من هوية الشركة.',
        tip6: 'انقر على "إدارة الرصيد".',
        tip7: '2. يرجى تحميل لقطة شاشة للعقد التجاري لشركتك مع BestSign أو مراسلات البريد الإلكتروني مع مدير العلاقات المخصص.',
        tip8: 'شراء عقد واحد على الأقل والاحتفاظ بلقطة شاشة لسجل الشراء.',
        tip9: '3. هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',
        tip10: '4. سوف تقوم BestSign بمراجعة الطلب خلال ثلاثة أيام عمل بعد التقديم.',
        tip11: 'تنبيه هام',
        tip12: 'يجب أن يكون المشتري مستخدماً تجارياً.',
        tip13: 'يجب أن يتطابق اسم الشركة في حساب الدفع تماماً مع "اسم الشركة" الذي أدخلته.',
        tip14: 'هذه الطريقة متاحة فقط للشركات خارج اليابان والبر الرئيسي للصين.',
        comNum: 'رقم وثيقة الشركة',
        buyRecord: 'المواد الداعمة',
        selectArea: 'يرجى اختيار مكان تسجيل الشركة',
        uaeTip1: 'يجب على الشركات المسجلة في الإمارات العربية المتحدة إكمال التسجيل باستخدام الاسم الحقيقي على uae.bestsign.com. عند توقيع العقود مع الشركات خارج الإمارات العربية المتحدة، يمكن استخدام وظيفة "التوقيع عبر الحدود" لإكمال التوقيع المتبادل للعقود بكفاءة مع ضمان أمان بيانات المستخدم وعدم تسريبها.',
        uaeTip2: 'إذا كانت شركتك قد أكملت مصادقة الهوية الحقيقية على نسخة BestSign للإمارات، يمكنك تسجيل الدخول مباشرة إلى uae.bestsign.com واستخدام الخدمات ذات الصلة بسهولة. يرجى ملاحظة أن البيانات التي تنشأ في النسخة الدولية من BestSign منفصلة تماماً عن النسخة الإماراتية.',
        uaeTip3: 'الشركات المسجلة خارج دولة الإمارات العربية المتحدة والبر الرئيسى للصين ، يجب أن تكون مسجلة في ent.bestsign.com الاسم الحقيقي . عند توقيع العقود مع الشركات في دولة الإمارات العربية المتحدة ، يمكن استخدام " التوقيع عبر الحدود " وظيفة لضمان أمن بيانات المستخدم ، لا تسرب ، فرضية كفاءة إنجاز العقود المتبادلة .',
    },
    signPC: {
        commonSign: 'تأكيد التوقيع',
        contractVerification: 'التحقق من العقد',
        VerCodeVerify: 'التحقق من رمز التحقق',
        QrCodeVerify: 'التحقق من رمز QR',
        verifyTip: 'BestSign يقوم بتفعيل شهادتك الرقمية الآمنة، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',
        verifyAllTip: 'BestSign يقوم بتفعيل شهادة المؤسسة الرقمية وشهادتك الشخصية الرقمية، أنت في بيئة توقيع آمنة، يرجى التوقيع بثقة!',
        selectSeal: 'اختيار الختم',
        adminGuideTip: 'نظراً لأنك المدير الرئيسي للمؤسسة، يمكنك تخصيص ختم المؤسسة لنفسك مباشرة',
        toAddSealWithConsole: 'الختم الرسمي الإلكتروني في انتظار التفعيل. لإضافة أختام أخرى، يرجى الانتقال إلى لوحة التحكم.',
        use: 'استخدام',
        toAddSeal: 'اذهب لإضافة ختم',
        mySeal: 'ختمي',
        operationCompleted: 'اكتملت العملية',
        FDASign: {
            date: 'وقت التوقيع',
            signerAdd: 'إضافة',
            signerEdit: 'تعديل',
            editTip: 'تنبيه: للأسماء الصينية يرجى إدخال النطق الصوتي، مثل San Zhang (张三)',
            inputNameTip: 'يرجى إدخال اسمك',
            inputName: 'يرجى إدخال الإنجليزية أو النطق الصوتي الصيني',
            signerNameFillTip: 'لا تزال بحاجة إلى ملء اسم التوقيع',
            plsInput: 'يرجى الإدخال',
            plsSelect: 'يرجى الاختيار',
            customInput: 'إدخال مخصص',
        },
        signPlaceBySigner: {
            signGuide: 'دليل التوقيع',
            howDragSeal: 'كيفية سحب الختم',
            howDragSignature: 'كيفية سحب التوقيع',
            iKnow: 'فهمت',
            step: {
                one: 'الخطوة الأولى: قراءة العقد',
                two1: 'الخطوة الثانية: النقر على "سحب الختم"',
                two2: 'الخطوة الثانية: النقر على "سحب التوقيع"',
                three: 'الخطوة الثالثة: النقر على زر "التوقيع"',
            },
            dragSeal: 'سحب الختم',
            continueDragSeal: 'متابعة سحب الختم',
            dragSignature: 'سحب التوقيع',
            continueDragSignature: 'متابعة سحب التوقيع',
            dragPlace: 'اضغط هنا للسحب',
            notRemind: 'لا تذكرني مرة أخرى',
            signTip: {
                one: 'الخطوة الأولى: من خلال النقر على "بدء"، حدد موقع التوقيع/الختم المطلوب.',
                two: 'الخطوة الثانية: من خلال النقر على "موقع التوقيع/موقع الختم"، أكمل التوقيع/الختم حسب المتطلبات.',
            },
            finishSignatureBeforeSign: 'يرجى إكمال سحب التوقيع/سحب الختم قبل تأكيد التوقيع',
        },
        continueOperation: {
            success: 'نجحت العملية',
            exitApproval: 'الخروج من الموافقة',
            continueApproval: 'متابعة الموافقة',
            next: 'التالي:',
            none: 'لا يوجد المزيد',
            tip: 'تنبيه',
            approvalProcess: 'يحتاج إلى موافقة {totalNum} شخص، حالياً {passNum} شخص وافقوا',
            receiver: 'المستلم:',
        },
    },
    signTip: {
        contractDetail: 'تفاصيل العقد',
        downloadBtn: 'تحميل التطبيق',
        tips: 'تنبيهات',
        submit: 'تأكيد',
        SigningCompleted: 'تم التوقيع بنجاح',
        submitCompleted: 'في انتظار معالجة الآخرين',
        noTurnSign: 'لم يحن دورك في التوقيع أو ليس لديك صلاحية التوقيع أو انتهت صلاحية تسجيل الدخول',
        noRightSign: 'العقد قيد التوقيع، المستخدم الحالي غير مسموح له بعملية التوقيع',
        noNeedSign: 'عقد قرار داخلي، لم يعد بحاجة للتوقيع',
        ApprovalCompleted: 'تمت الموافقة بنجاح',
        contractRevoked: 'تم إلغاء {alias} هذا',
        contractRefused: 'تم رفض {alias} هذا',
        linkExpired: 'انتهت صلاحية هذا الرابط',
        contractClosed: 'انتهى موعد توقيع {alias} هذا',
        approvalReject: 'تم رفض موافقة {alias} هذا',
        approving: '{alias} قيد الموافقة',
        viewContract: 'عرض تفاصيل {alias}',
        viewContractList: 'عرض قائمة العقود',
        needMeSign: '({num} في انتظار التوقيع)',
        downloadContract: 'تحميل العقد',
        sign: 'تم التوقيع',
        signed: 'تم التوقيع',
        approved: 'تمت الموافقة',
        approval: 'موافقة',
        person: 'شخص',
        personHas: 'قد',
        personHave: 'قد',
        personHasnot: 'لم',
        personsHavenot: 'لم',
        headsTaskDone: '{num}{has}{done}',
        headsTaskNotDone: '{num}{not}{done}',
        taskStatusBetween: '،',
        cannotReview: 'لا يمكن عرض العقد',
        cannotDownload: 'هذا العقد لا يدعم التحميل على الهاتف. لأن العقد مخزن بشكل خاص من قبل المرسل، لا يمكن لـ BestSign الوصول إلى العقد.',
        privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود',
        beenDeleted: 'تم حذف حسابك من قبل مدير المؤسسة',
        unActive: 'لا يمكن متابعة تفعيل الحساب',
        back: 'عودة',
        contratStatusDes: 'حالة {key}:',
        contractConditionDes: 'وضع {key}:',
        contractIng: '{alias} {key} جارٍ',
        contractComplete: 'اكتمل {alias} {key}',
        dataProduct: {
            tip1: 'من {entName} إلى السادة مسؤولي الموزعين/الموردين المتميزين:',
            tip2: 'لشكركم على مساهمتكم في التطور المستقر لـ {entName}، نقدم بالتعاون مع {bankName} خدمات التمويل لسلسلة التوريد لمساعدة مؤسستكم على التطور السريع!',
            btnText: 'اذهب لمشاركة هذه الأخبار السارة مع المدير',
        },
        signOnGoing: 'العقد {status} جارٍ',
        operate: 'عملية العقد',
        freeContract: 'أكمل إرسال العقد الأول، يمكنك الحصول على المزيد من العقود مجاناً',
        sendContract: 'اذهب لإرسال العقد',
        congratulations: 'تهانينا {name} على إكمال توقيع {num} عقد،',
        carbonSaving: 'تقدير توفير الكربون {num}g',
        signGift: 'BestSign يهديك {num} عقد مؤسسي (صالح حتى {limit})',
        followPublic: 'تابع الحساب العام على WeChat لتلقي رسائل العقود في أي وقت',
        congratulationsSingle: 'تهانينا {name} على إكمال توقيع العقد،',
        carbonSavingSingle: 'تقدير زيادة توفير الكربون 2002.4g',
        viewContractTip: 'إذا كنت تريد تغيير الشخص المسؤول عن الختم، يمكنك النقر على زر "عرض التفاصيل" لفتح صفحة تفاصيل العقد، ثم النقر على زر "طلب ختم"',
        congratulationsCn: 'شكراً لاختيار التوقيع الإلكتروني!',
        carbonSavingSingleCn: 'لقد قللت الكربون بمقدار {num}gCO2e للأرض',
        carbonVerification: "*محسوب علمياً بواسطة 'كربون ستوب'",
    },
    view: {
        title: 'عرض العقد',
        ok: 'إكمال',
        cannotReview: 'لا يمكن عرض العقد',
        privateStorage: 'المؤسسة المرسلة تستخدم تخزين العقود الخاص، ولكن الشبكة الحالية لا يمكنها الاتصال بخادم تخزين العقود',
    },
    prepare: {
        sealArea: 'موقع الختم',
        senderNotice: 'الطرف المرسل الحالي للعقد هو: {entName}',
        preSetDialogConfirm: 'فهمت',
        preSetDialogContact: 'اتصل بفريق مبيعات BestSign للتفعيل الآن',
        preSetDialogInfo: 'بعد إعداد العقد مسبقاً، يقوم النظام تلقائياً بملء معلومات الأطراف المتعاقدة، ومتطلبات التوقيع، ومواقع التوقيع، وحقول وصف العقد وفقاً للقالب',
        preSetDialogTitle: 'ما هو قالب الإعداد المسبق للعقد؟',
        initialValues: 'تعيين القيم الأولية بناءً على محتوى العقد',
        proxyUpload: 'بعد تحميل الملف المحلي، يمكنك اختيار طرف بدء العقد',
        signHeaderTitle: 'إضافة الملفات والأطراف المتعاقدة',
        step1: 'الخطوة الأولى',
        confirmSender: 'تأكيد المرسل',
        step2: 'الخطوة الثانية',
        uploadFile: 'تحميل الملف',
        step3: 'الخطوة الثالثة',
        addSigner: 'إضافة طرف متعاقد',
        actionDemo: 'عرض توضيحي للعملية',
        next: 'الخطوة التالية',
        isUploadingErr: 'لم يكتمل تحميل الملف بعد، يرجى المتابعة بعد الاكتمال',
        noUploadFileErr: 'لم يتم تحميل ملف، يرجى التحميل قبل المتابعة',
        noContractTitleErr: 'لم يتم إدخال اسم العقد، يرجى الإدخال قبل المتابعة',
        contractTypeErr: 'تم حذف نوع العقد الحالي، يرجى إعادة اختيار نوع العقد',
        expiredDateErr: 'خطأ في الموعد النهائي للتوقيع، يرجى التعديل قبل المتابعة',
        noExpiredDateErr: 'يرجى تعيين الموعد النهائي للتوقيع قبل المتابعة',
        describeFieldsErr: 'يرجى ملء حقول المحتوى المطلوبة قبل المتابعة',
        noRecipientsErr: 'يرجى إضافة طرف متعاقد واحد على الأقل',
        noAccountErr: 'لا يمكن ترك الحساب فارغاً',
        noUserNameErr: 'لا يمكن ترك الاسم فارغاً',
        noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',
        accountFormatErr: 'التنسيق غير صحيح، يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',
        userNameFormatErr: 'التنسيق غير صحيح، يرجى إدخال اسم صحيح',
        enterpriseNameErr: 'يرجى إدخال اسم مؤسسة صحيح',
        idNumberForVerifyErr: 'التنسيق غير صحيح، يرجى إدخال رقم هوية صحيح',
        signerErr: 'خطأ في الطرف المتعاقد',
        noSignerErr: 'يرجى إضافة موقع واحد على الأقل',
        lackAttachmentNameErr: 'يرجى إدخال اسم المرفق',
        repeatRecipientsErr: 'لا يمكن إضافة نفس الطرف المتعاقد مرتين عند عدم التوقيع بالترتيب',
        innerContact: 'جهة اتصال داخلية',
        outerContact: 'جهة اتصال خارجية',
        search: 'بحث',
        accountSelected: 'الحسابات المختارة',
        groupNameAll: 'الكل',
        unclassified: 'غير مصنف',
        fileLessThan: 'يرجى تحميل ملف أقل من {num}M',
        beExcel: 'يرجى تحميل ملف Excel',
        usePdf: 'يرجى استخدام ملف PDF أو صورة عند التحميل',
        usePdfFile: 'يرجى استخدام ملف PDF عند التحميل',
        fileNameMoreThan: 'تجاوز طول اسم الملف {num}، تم اقتطاعه تلقائياً',
        needAddSender: 'لم يتم تعيين مؤسستك/نفسك كطرف متعاقد، بعد إرسال العقد، لن تشارك في عملية التوقيع. هل تريد إضافة طرفك كموقع؟',
        addSender: 'إضافة كطرف متعاقد',
        tip: 'تنبيه',
        cancel: 'إلغاء',
    },
    addReceiver: {
        English: 'الإنجليزية',
        Japanese: 'اليابانية',
        Chinese: 'الصينية',
        Arabic: 'Arabic',
        setNoticelang: 'تعيين لغة الإشعارات',
        limitFaceConfigTip: 'سعر العقد الخاص بك منخفض جداً، هذه الميزة غير متوفرة، يرجى الاتصال بـ BestSign للتفاوض',
        individual: 'فرد متعاقد',
        enterprise: 'مؤسسة متعاقدة',
        addInstructions: 'إضافة ملاحظات التوقيع',
        instructionsContent: 'المواد المقدمة تساعدك في تتبع حالة تنفيذ العقد وتحديد ما إذا كان تنفيذ الأعمال طبيعياً. بعد الإعداد، يجب على الموقع تقديمها وفقاً للمتطلبات',
        addContractingInfo: 'تقديم مواد الطرف المتعاقد',
        contractingInfoContent: 'المواد المقدمة تساعدك في التحقق من مؤهلات الطرف المتعاقد وتحديد ما إذا كان يمكن بدء أو مواصلة الأعمال معه. إذا كان الطرف المتعاقد قد قدم نفس المواد من قبل، فلا داعي لتقديمها مرة أخرى',
        payer: 'الطرف الدافع',
        handWriting: 'تفعيل التعرف على الكتابة اليدوية',
        realName: 'يحتاج المسؤول إلى التحقق من الهوية',
        sameTip: 'تنبيه: يجب أن يتطابق اسم المؤسسة للطرف المتعاقد تماماً للتوقيع',
        proxy: 'استلام بالوكالة من الطرف الآخر',
        aboradTip: 'تنبيه: هذا الطرف المتعاقد شخص خارج البلاد، هناك مخاطر في التحقق من الهوية، يرجى التحقق من هوية هذا الشخص أولاً',

        busRole: 'دور العمل',
        busRoleTip: 'يساعد في تحديد الطرف المتعاقد وتسهيل الإدارة',
        busRolePlaceholder: 'مثل موظف/موزع',
        handWritingTip: 'يحتاج هذا المستخدم إلى كتابة اسمه بخط واضح ومقروء عند التوقيع لإكمال التوقيع',
        instructions: 'إضافة ملاحظات التوقيع | (محدود بـ 255 حرفاً)',
        contractingParty: 'مواد الطرف المتعاقد',
        signerPay: 'يدفع هذا الموقع رسوم هذا العقد',
        afterReadingTitle: 'التوقيع بعد القراءة',
        afterReading: 'يجب على الموقع القراءة وفهم محتوى العقد قبل متابعة العمليات اللاحقة.',
        handWritingTips: 'سيتم مقارنة الاسم المكتوب بخط اليد مع الاسم المحدد من قبل المرسل أو في معلومات التحقق من الهوية، يجب أن تتطابق المقارنة لإكمال التوقيع',
        SsTitle: 'الختم والتوقيع',
        SsTip: 'عند التوقيع باستخدام ختم المؤسسة، يجب أيضاً إضافة التوقيع الشخصي لإكمال التوقيع. يجب إكمال التحقق من الهوية الشخصية قبل التوقيع',
        signature: 'توقيع',
        stamp: 'ختم',
        Ss: 'ختم وتوقيع',
        mutexError: 'تم تعيين "{msg}"، يرجى حذف إعداد "{msg}" أولاً قبل الاختيار',
        handWriteNotAllowed: 'التوقيع اليدوي غير مسموح به',
        forceHandWrite: 'التوقيع اليدوي إلزامي',
        faceFirst: 'التعرف على الوجه أولاً، رمز التحقق احتياطي للتوقيع',
        faceVerify: 'التوقيع بالتعرف على الوجه إلزامي',
        attachmentRequired: 'إضافة مواد ملحقة للعقد',
        newAttachmentRequired: 'تقديم مواد الطرف المتعاقد',
        attachmentError: 'لا يمكن أن تكون أسماء المواد الملحقة للعقد متطابقة',
        receiver: 'هاتف/بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',
        receiverJa: 'بريد إلكتروني الاستلام | (يدعم حتى 5، يمكن الفصل بفاصلة منقوطة)',
        orderSignLabel: 'التوقيع بالترتيب',
        contactAddress: 'دليل جهات الاتصال',
        signOrder: 'ترتيب التوقيع',
        account: 'الحساب',
        accountPlaceholder: 'هاتف/بريد إلكتروني (مطلوب)',
        accountPlaceholderJa: 'بريد إلكتروني (مطلوب)',
        accountReceptionCollection: 'استلام بالوكالة',
        accountReceptionCollectionTip1: 'لا تعرف حساب الطرف الآخر المحدد أو ليس لديه حساب،',
        accountReceptionCollectionTip2: 'يرجى اختيار استلام بالوكالة',
        signSubjectPerson: 'الطرف المتعاقد: فرد',
        nameTips: 'الاسم (اختياري، للتحقق من هوية التوقيع)',
        requiredNameTips: 'الاسم (مطلوب، للتحقق من هوية التوقيع)',
        entOperatorNameTips: 'الاسم (اختياري)',
        needAuth: 'يحتاج إلى التحقق من الهوية',
        operatorNeedAuth: 'يحتاج المسؤول إلى التحقق من الهوية',
        signSubjectEnt: 'الطرف المتعاقد: شركة',
        entNameTips: 'اسم المؤسسة (مطلوب، للتحقق من هوية التوقيع)',
        operator: 'المسؤول',
        sign: 'توقيع',
        more: 'المزيد',
        faceFirstTips: 'يستخدم النظام التحقق من الوجه افتراضياً عند التوقيع، عندما يصل عدد مرات فشل التحقق من الوجه إلى الحد الأقصى اليومي، يتم التبديل تلقائياً إلى التحقق برمز التحقق',
        mustFace: 'التوقيع بالتعرف على الوجه إلزامي',
        mustHandWrite: 'التوقيع اليدوي إلزامي',
        fillIDNumber: 'رقم الهوية',
        fillNoticeCall: 'هاتف الإشعار',
        fillNoticeCallTips: 'يرجى إدخال هاتف الإشعار',
        addNotice: 'إضافة رسالة خاصة',
        attachTips: 'إضافة مواد ملحقة للعقد',
        faceSign: 'التوقيع بالتعرف على الوجه إلزامي',
        faceSignTips: 'يحتاج هذا المستخدم إلى اجتياز التحقق من الوجه لإكمال التوقيع (التوقيع بالتعرف على الوجه متاح حالياً فقط للمقيمين في البر الرئيسي)',
        handWriteNotAllowedTips: 'يمكن لهذا المستخدم فقط اختيار توقيع معد مسبقاً أو استخدام الخط الافتراضي للتوقيع لإكمال التوقيع',
        handWriteTips: 'يحتاج هذا المستخدم إلى التوقيع اليدوي لإكمال التوقيع',
        idNumberTips: 'للتحقق من هوية التوقيع',
        verifyBefore: 'التحقق من الهوية قبل عرض الملف',
        verify: 'التحقق من الهوية',
        verifyTips: '20 حرفاً كحد أقصى',
        verifyTips2: 'يجب عليك تقديم معلومات التحقق هذه لهذا المستخدم',
        sendToThirdPlatform: 'إرسال إلى منصة خارجية',
        platFormName: 'اسم المنصة',
        fillThirdPlatFormName: 'يرجى إدخال اسم المنصة الخارجية',
        attach: 'المواد',
        attachName: 'اسم المواد',
        exampleID: 'مثال: صورة بطاقة الهوية',
        attachInfo: 'ملاحظات',
        attachInfoTips: 'مثال: يرجى تحميل صورة بطاقة هويتك',
        addAttachRequire: 'إضافة مواد',
        addSignEnt: 'إضافة مؤسسة متعاقدة',
        addSignPerson: 'إضافة فرد متعاقد',
        selectContact: 'اختيار جهة اتصال',
        save: 'حفظ',
        searchVerify: 'التحقق من البحث',
        fillImageContentTips: 'يرجى ملء محتوى الصورة',
        ok: 'موافق',
        findContact: 'تم العثور على الأطراف المتعاقدة التالية في العقد',
        signer: 'الطرف المتعاقد',
        signerTips: 'تلميح: بعد اختيار الطرف المتعاقد، يمكن للمنصة المساعدة في تحديد موقع التوقيع والختم.',
        add: 'إضافة',
        notAdd: 'عدم الإضافة',
        cc: 'نسخة',
        notNeedAuth: 'لا يحتاج إلى التحقق من الهوية',
        operatorNotNeedAuth: 'لا يحتاج المسؤول إلى التحقق من الهوية',
        extracting: 'جاري الاستخراج',
        autoFill: 'ملء الموقع تلقائياً',
        failExtracting: 'لم يتم العثور على أطراف متعاقدة',
        idNumberForVerifyErr: 'يرجى إدخال رقم هوية صحيح',
        noAccountErr: 'لا يمكن ترك الحساب فارغاً',
        noUserNameErr: 'لا يمكن ترك الاسم فارغاً',
        noIDNumberErr: 'لا يمكن ترك رقم الهوية فارغاً',
        noEntNameErr: 'لا يمكن ترك اسم المؤسسة فارغاً',
        accountFormatErr: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',
        enterpriseNameErr: 'يرجى إدخال اسم شركة صحيح',
        userNameFormatErr: 'يرجى إدخال اسم صحيح',
        riskCues: 'تنبيه المخاطر',
        riskCuesMsg: 'إذا لم يوقع الطرف المتعاقد بالتحقق من الهوية، ستحتاج إلى تقديم دليل التحقق من هوية هذا الطرف بنفسك عند حدوث نزاع في الملف. لتجنب المخاطر، يرجى اختيار التحقق من الهوية.',
        confirmBtnText: 'اختيار التحقق من الهوية',
        cancelBtnText: 'اختيار عدم التحقق من الهوية',
        attachLengthErr: 'يمكنك إضافة 50 مرفق كحد أقصى لكل موقع',
        collapse: 'طي',
        expand: 'توسيع',
        delete: 'حذف',
        saySomething: 'قل شيئاً',
        addImage: 'إضافة مستند',
        addImageTips: '(يدعم word وpdf والصور، لا يتجاوز 3 مستندات)',
        give: 'إعطاء',
        fileMax: 'تجاوز عدد التحميلات الحد الأقصى!',
        signerLimit: 'نسختك الحالية لا تدعم أكثر من {limit} موقع نسبي/مستلم نسخة.',
        showExamle: 'عرض الصور النموذجية',
        downloadExamle: 'تنزيل الملف النموذجي',
    },
    addReceiverGuide: {
        guideTitle: 'كيفية إضافة موقع جديد',
        receiverType: 'تحتاج إلى اختيار طريقة مشاركة الموقع في العقد (واحد من ستة):',
        asEntSign: 'التوقيع نيابة عن المؤسسة:',
        signatureSub: 'المدير القانوني أو التنفيذي يوقع على العقد نيابة عن المؤسسة. يمكن للمؤسسة أخذ العقد بعد اكتمال التوقيع',
        vipOnly: 'متاح فقط للنسخة المتقدمة',
        sealSub: 'يحتاج الموقع إلى وضع ختم رسمي أو ختم خاص بالعقد وغيره',
        stampSub: 'يحتاج الموقع إلى الختم والتوقيع نيابة عن المؤسسة',
        confirmSeal: 'استخدام ختم التحقق من الأعمال نيابة عن المؤسسة',
        confirmSealSub: 'كشوف الحسابات المالية وخطابات التأكيد وغيرها من الوثائق تحتاج إلى التحقق قبل الختم',
        asPersonSign: 'التوقيع بصفة شخصية:',
        asPersonSignTip: 'التوقيع بصفة شخصية فقط، لا يمثل أي مؤسسة',
        asPersonSignDesc: 'العقود الشخصية للموقع، مثل عقود القرض واتفاقيات التوظيف والاستقالة وغيرها',
        scanSign: 'التوقيع بالمسح الضوئي',
        scanSignDesc: 'لا تحتاج إلى كتابة الموقع عند إرسال العقد، بعد الإرسال يمكن لأي شخص المسح/النقر على رابط التحقق للتوقيع، مناسب لسيناريوهات استلام مستندات الشحن',
        selectSignTypeTip: 'يرجى اختيار طريقة مشاركة الطرف المتعاقد في العقد أولاً',
        notRemind: 'عدم التذكير مرة أخرى',
        sign: 'توقيع',
        entSign: 'توقيع المؤسسة',
        stamp: 'ختم',
        stampSign: 'ختم وتوقيع',
        requestSeal: 'ختم التحقق من الأعمال',
    },
    linkContract: {
        title: 'ربط العقود',
        connectMore: 'ربط المزيد من العقود',
        placeholder: 'يرجى إدخال رقم العقد',
        revoke: 'تم إلغاء العقد',
        overdue: 'تجاوز موعد التوقيع',
        approvalNotPassed: 'تم رفض الموافقة',
        reject: 'تم رفض العقد',
        signing: 'قيد التوقيع',
        complete: 'مكتمل',
        approvaling: 'قيد الموافقة',
        disconnect: 'إلغاء الربط',
        disconnectSuccess: 'تم إلغاء الربط بنجاح',
        connectLimit: 'الحد الأقصى لعدد العقود المرتبطة هو 100 عقد',
    },
    field: {
        fieldTip: {
            title: 'موقع توقيع مفقود',
            error: 'لم يتم تحديد مواقع التوقيع ({type}) في العقود التالية',
            add: 'إضافة حقل',
            continue: 'متابعة الإرسال',
        },
        accountCharge: {
            notice: 'يتم احتساب رسوم هذا العقد حسب عدد الحسابات المشاركة',
            able: 'يمكن الإرسال بشكل طبيعي',
            unable: 'عدد الحسابات المتاحة غير كافٍ، يرجى الاتصال بخدمة عملاء BestSign',
            notify: 'هذا العقد يرسل إشعارات باللغة الإنجليزية لجميع الأطراف المتعاقدة',
            noNotify: {
                1: 'هذا العقد لا يرسل إشعارات متعلقة بالتوقيع',
                2: '(بما في ذلك الرسائل القصيرة والبريد الإلكتروني للتوقيع والموافقة والنسخ والموعد النهائي للتوقيع)',
            },
        },
        ridingStamp: 'ختم متداخل',
        watermark: 'علامة مائية',
        senderSignature: 'توقيع صاحب الختم',
        optional: 'اختياري',
        clickDecoration: 'انقر لتزيين العقد',
        decoration: 'تزيين العقد',
        sysError: 'النظام مشغول، يرجى المحاولة لاحقاً',
        partedMarkedError: 'يجب تحديد كل من الختم والتوقيع للأطراف المتعاقدة المحددة لـ "الختم والتوقيع"',
        fieldTitle: 'يوجد {length} عقد يحتاج إلى تحديد مواقع التوقيع',
        send: 'إرسال',
        contractDispatchApply: 'طلب إرسال العقد',
        contractNeedYouSign: 'هذا المستند يحتاج إلى توقيعك',
        ifSignRightNow: 'هل تريد التوقيع الآن',
        signRightNow: 'التوقيع الآن',
        signLater: 'التوقيع لاحقاً',
        signaturePositionErr: 'يرجى تحديد مواقع التوقيع لكل موقع',
        sendSucceed: 'تم الإرسال بنجاح',
        confirm: 'تأكيد',
        cancel: 'إلغاء',
        qrCodeTips: 'امسح الرمز بعد التوقيع لعرض تفاصيل التوقيع والتحقق من صلاحية التوقيع وما إذا تم العبث بالعقد',
        pagesField: 'الصفحة {currentPage} من {totalPages}',
        suitableWidth: 'عرض مناسب',
        signCheck: 'التحقق من التوقيع',
        locateSignaturePosition: 'تحديد موقع التوقيع',
        locateTips: 'يمكن المساعدة في تحديد موقع التوقيع بسرعة. حالياً يدعم فقط تحديد أول موقع توقيع لكل موقع',
        step1: 'الخطوة الأولى',
        selectSigner: 'اختيار الطرف المتعاقد',
        step2: 'الخطوة الثانية',
        dragSignaturePosition: 'سحب موقع التوقيع',
        signingField: 'حقل التوقيع',
        docTitle: 'المستند',
        totalPages: 'عدد الصفحات: {totalPages} صفحة',
        receiver: 'المستلم',
        delete: 'حذف',
        deductPublicNotice: 'عند عدم كفاية عدد العقود الشخصية المتاحة، سيتم خصم من العقود المؤسسية',
        unlimitedNotice: 'رسوم هذا العقد غير محدودة الاستخدام',
        charge: 'الرسوم',
        units: '{num} نسخة',
        contractToPrivate: 'عقد شخصي',
        contractToPublic: 'عقد مؤسسي',
        costTips: {
            1: 'عقد مؤسسي: عقد يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',
            2: 'عقد شخصي: عقد لا يحتوي على حساب مؤسسة بين الموقعين (لا يشمل المرسل)',
            3: 'يتم حساب عدد النسخ المحتسبة حسب عدد المستندات',
            4: 'عدد النسخ المحتسبة = عدد المستندات × عدد مجموعات (صفوف) المستخدمين المستوردين بالدفعة',
        },
        toCharge: 'إعادة الشحن',
        contractNeedCharge: {
            1: 'عدد العقود المتاحة غير كافٍ، لا يمكن الإرسال',
            2: 'عدد العقود المتاحة غير كافٍ، يرجى الاتصال بالمدير الرئيسي لإعادة الشحن',
        },
        chooseApprover: 'اختيار المراجع:',
        nextStep: 'الخطوة التالية',
        submitApproval: 'تقديم للموافقة',
        autoSendAfterApproval: '*بعد الموافقة، سيتم إرسال العقد تلقائياً',
        chooseApprovalFlow: 'يرجى اختيار تدفق موافقة',
        completeApprovalFlow: 'تدفق الموافقة الذي قدمته غير مكتمل، يرجى إكماله وإعادة التقديم',
        viewPrivateLetter: 'عرض الرسالة الخاصة',
        addPrivateLetter: 'إضافة رسالة خاصة',
        append: 'إضافة',
        privateLetter: 'رسالة خاصة',
        signNeedKnow: 'ملاحظات التوقيع',
        maximum5M: 'يرجى تحميل مستند أقل من 5M',
        uploadServerFailure: 'فشل التحميل إلى الخادم',
        uploadFailure: 'فشل التحميل',
        pager: 'رقم الصفحة',
        seal: 'ختم',
        signature: 'توقيع',
        signDate: 'تاريخ التوقيع',
        text: 'نص',
        date: 'تاريخ',
        qrCode: 'رمز QR',
        number: 'رقم',
        dynamicTable: 'جدول ديناميكي',
        terms: 'بنود العقد',
        checkBox: 'خانة اختيار',
        radioBox: 'زر راديو',
        image: 'صورة',
    },
    addressBook: {
        innerMember: {
            title: 'الأعضاء الداخليون للمؤسسة',
            tips: 'تعديل معلومات موظفي المؤسسة لجعل المرسلين يجدون جهات الاتصال الداخلية بسرعة أكبر',
            operation: 'اذهب إلى لوحة التحكم',
        },
        outerContacts: {
            title: 'جهات الاتصال الخارجية للمؤسسة',
            tips: 'دعوة شركائك للتسجيل والتحقق من الهوية مسبقاً لتسهيل إجراء الأعمال معك',
            operation: 'دعوة شركائك',
        },
        myContacts: {
            title: 'جهات اتصالي',
            tips: 'تعديل جهات الاتصال للتأكد من دقة معلومات الموقع',
            operation: 'اذهب إلى مركز المستخدم',
        },
        selected: 'الحسابات المختارة',
        search: 'بحث',
        loadMore: 'تحميل المزيد',
        end: 'تم تحميل الكل',
    },
    dataBoxInvite: {
        title: 'دعوة شركائك',
        step1: 'مشاركة الرابط مع شركائك لإنشاء مؤسسة مسبقاً',
        step2: 'سيظهر الشركاء المفوضون عبر الرابط/رمز QR في دليل العناوين',
        step3: 'قم بإدارة المزيد لشركائك في "الملفات+"',
        imgName: 'مشاركة رمز QR للجمع',
        saveQrcode: 'حفظ رمز QR محلياً',
        copy: 'نسخ',
        copySuccess: 'تم النسخ بنجاح',
        copyFailed: 'فشل النسخ',
    },
    shareView: {
        title: 'إعادة توجيه للمراجعة',
        account: 'رقم الهاتف/البريد الإلكتروني',
        role: 'دور المراجع',
        note: 'ملاحظات',
        link: 'الرابط:',
        signerMessage: 'رسالة الموقع',
        rolePlaceholder: 'مثل القانوني للشركة، قائد القسم وغيرهم',
        notePlaceholder: 'اترك رسالة للمراجع، في حدود 200 حرف',
        generateLink: 'إنشاء رابط',
        saveQrcode: 'حفظ رمز البرنامج الصغير لـ WeChat',
        regenerateLink: 'إعادة إنشاء الرابط',
        inputAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني',
        inputCorrectAccount: 'يرجى إدخال رقم هاتف أو بريد إلكتروني صحيح',
        accountInputTip: 'لضمان فتح الرابط بشكل طبيعي، يرجى إدخال معلومات مراجع العقد بدقة',
        shareLinkTip1: 'احفظ رمز برنامج WeChat المصغر أو',
        shareLinkTip: 'انسخ الرابط للمشاركة مع المراجعين',
        linkTip1: 'نص العقد محتوى سري، يرجى عدم تسريبه للخارج في حالة عدم الضرورة',
        linkTip2: 'صلاحية الرابط يومان؛ بعد إعادة إنشاء الرابط، يصبح الرابط السابق غير صالح تلقائياً',
    },
    recoverSpecialSeal: {
        title: 'الختم غير قابل للاستخدام',
        description1: 'يطلب المرسل منك استخدام هذا الختم للتوقيع على العقد، ولكن شركتك قد حذفت هذا الختم. لضمان سير التوقيع بسلاسة، يرجى طلب استعادة هذا الختم من المدير.',
        description2: 'إذا كان هذا الختم غير مناسب للاستخدام المستمر، يمكنك الاتصال بالمرسل لتعديل متطلبات صورة الختم قبل توقيع العقد.',
        postRecover: 'طلب استعادة الختم',
        note: 'بعد النقر، سيتلقى المدير رسالة قصيرة/بريد إلكتروني لطلب استعادة الختم، كما يمكن رؤية الطلب في صفحة إدارة الأختام.',
        requestSend: 'تم تقديم طلب الاستعادة بنجاح',
    },
    paperSign: {
        title: 'استخدام طريقة التوقيع الورقي',
        stepText: ['الخطوة التالية', 'تأكيد التوقيع الورقي', 'تأكيد'],
        needUploadFile: 'يرجى تحميل النسخة الممسوحة ضوئياً أولاً',
        uploadError: 'فشل التحميل',
        cancel: 'إلغاء',
        downloadPaperFile: 'الحصول على ملف التوقيع الورقي',
        step0: {
            title: 'تحتاج أولاً إلى تحميل وطباعة العقد، ووضع الختم المادي، ثم إرساله بالبريد إلى المرسل.',
            address: 'عنوان البريد:',
            contactName: 'اسم المستلم:',
            contactPhone: 'معلومات الاتصال بالمستلم:',
            defaultValue: 'يرجى الحصول عليها من المرسل خارج الإنترنت',
        },
        step1: {
            title0: 'الخطوة الأولى: تحميل وطباعة العقد الورقي',
            title0Desc: ['يجب أن يتضمن العقد المطبوع صورة الختم الإلكتروني الموقع. يرجى', 'الحصول على ملف التوقيع الورقي.'],
            title1: 'الخطوة الثانية: وضع الختم',
            title1Desc: 'ضع ختم الشركة الساري للعقد على العقد الورقي.',
            title2: ['الخطوة الثالثة:', 'تحميل النسخة الممسوحة ضوئياً،', 'العودة إلى صفحة التوقيع، انقر على زر التوقيع، أكمل التوقيع الورقي'],
            title2Desc: ['بعد تحويل العقد الورقي إلى نسخة ممسوحة ضوئياً (ملف PDF) وتحميلها،', 'لن يظهر صورة ختمك في العقد الإلكتروني، ولكن سيتم تسجيل عملية العملية هذه.'],
        },
        step2: {
            title: ['تحميل نسخة العقد الورقي الممسوحة ضوئياً (ملف PDF)', 'يرجى التأكد من تحميل وتوقيع العقد الورقي قبل النقر على زر التأكيد لإنهاء عملية التوقيع الورقي.'],
            uploadFile: 'تحميل النسخة الممسوحة ضوئياً',
            getCodeVerify: 'الحصول على رمز التحقق من توقيع العقد',
            isUploading: 'جاري التحميل...',
        },
    },
    allowPaperSignDialog: {
        title: 'السماح بالتوقيع الورقي',
        content: 'هذا العقد من {senderName} إلى {receiverName}، يسمح بالتوقيع بالطريقة الورقية.',
        tip: 'يمكنك أيضاً اختيار تحميل وثيقة العقد وطباعتها، وتقديمها للمسؤول عن ختم المؤسسة للتوقيع خارج الإنترنت.',
        icon: 'تحويل إلى توقيع ورقي >>',
        goSign: 'اذهب للتوقيع الإلكتروني',
        cancel: 'إلغاء',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: 'تنبيه الختم',
            tip: 'تم اكتشاف أن صورة ختمك الحالي لا تتطابق مع هوية مؤسستك، نتيجة التعرف على صورة الختم الحالي:',
            tip1: 'تم اكتشاف ختم مؤسسة واسم مؤسسة:',
            tip2: 'هل تريد متابعة استخدام صورة الختم الحالية؟',
            tip3: 'وفقاً لمتطلبات المرسل، تحتاج إلى استخدام ختم باسم المؤسسة:',
            tip4: 'الختم',
            tip5: 'يرجى التأكد من أن الختم يتوافق مع المتطلبات، وإلا سيؤثر على صلاحية العقد!',
            tip6: 'غير متطابق، يرجى التأكد من أن الختم يتوافق مع متطلبات المرسل.',
            guide: 'كيفية تحميل الختم الصحيح >>',
            next: 'متابعة الاستخدام',
            tip7: 'واسم ختمك لا يتوافق مع المعايير، يحتوي على كلمة "{keyWord}".',
            tip8: 'تم اكتشاف أن اسم الختم لا يتوافق مع المعايير، يحتوي على كلمة "{keyWord}"، هل تريد متابعة الاستخدام؟',
        },
        exampleSeal: {
            title: 'طريقة تحميل نموذج الختم',
            way1: ['الطريقة الأولى:', '1- ضع ختماً فعلياً على ورقة بيضاء', '2- التقط صورة وقم بتحميلها إلى المنصة'],
            way2: ['الطريقة الثانية:', 'استخدم مباشرة وظيفة إنشاء الختم الإلكتروني في المنصة، كما هو موضح:'],
            errorWay: ['طريقة خاطئة:', 'حمل الختم باليد', 'صورة غير ذات صلة', 'رخصة العمل'],
        },
        confirm: 'تأكيد',
        cancel: 'إلغاء',
    },
    addSealDialog: {
        title: 'إضافة صورة الختم',
        dec1: 'يرجى اختيار صورة ختم من المجلد المحلي (التنسيقات: JPG، JPEG، PNG، إلخ). سيقوم النظام بدمج صورة الختم هذه في العقد الحالي.',
        dec2: 'بعد ذلك، تحتاج إلى النقر على زر "التوقيع" لاجتياز التحقق من التوقيع لإتمام الختم.',
        updateNewSeal: 'رفع ختم جديد',
    },
};
