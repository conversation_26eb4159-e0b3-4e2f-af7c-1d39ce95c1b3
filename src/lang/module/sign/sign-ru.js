export default {
    sign: {
        sealLabelsTip: '您需要再这份合同上盖{sealLabelslen}个章。{personStr}将为您盖${otherSealLen}个章，剩余{mySealLen}个章由您亲自盖章。所需使用的章已经在页面上展示。请确认是否继续。',
        continue: '继续',
        needRemark: '您还需要填写备注',
        notNeedRemark: '您不需要填写备注',
        contractPartiesYouChoose: '您可以选择的签约主体:',
        contractPartyFilled: '发件人填写的签约主体为:',
        certifyOtherCompanies: '认证其他企业',
        youCanAlso: '您也可以：',
        needVerification: 'Для подписи вам нужна сертификация',
        prompt: 'Напоминать',
        submit: 'Подтвердить',
        cancel: 'Отменить',
        sign: '立即签约',
        addSeal: '请使用电脑登录上上签官网添加印章',
        noSealAvailable: '对不起，您目前没有可使用的印章，请联系企业主管理员添加印章并授权。',
        requestSomeone: 'Запросить кого-то еще для аутентификации',
        requestSomeoneList: '请求以下人员完成实名认证：',
        electronicSeal: 'электронная печать',
        changeTheSeal: '不想使用该印章？实名认证后可更换印章',
        goToVerify: 'Перейти к сертификации подлинного имени',
        noSealToChoose: 'Нет переключаемых печатей, если необходимо управлять печатью, сначала проверьте подлинное имя',
        goVerify: '去认证',
        goToVerifyEnt: 'Перейти в сертификационную компанию',
        digitalCertificateTip: '上上签正在调用您的数字证书',
        signDes: '您在安全签约环境中，请放心签署！',
        signAgain: '继续签署',
        send: '发送',
        person: '个人',
        ent: '企业',
        entName: '企业名称',
        account: '账号',
        accountPH: '手机或邮箱',
        approved: 'утверждение',
        signVerification: 'Подписать',
        cannotReview: '无法查看合同',
        connectFail: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',
        personalMaterials: '发件人要求您补充更多认证材料',
        noSupportface: '合同发起方要求您刷脸签署，非大陆人士暂不支持刷脸签署，请联系发起方修改签署要求',
        lackEntName: '请填写企业名称',
        errAccount: '请填写正确的邮箱或手机号',
        noticeAdmin: '申请加入',
        signDone: '签署完成',
        signDoneTip: '您已签署该合同',
        approveDone: '审批完成',
        approveDoneTip: '您已审批该合同',
        completeSign: `请先点击“盖章处”或“签字处”完成签署`,
        fillFirst: '请先在输入框中填写合同内容',
        stillSignTip: '在您签署此合同后，仍有其他签署方可能填写合同内容，是否继续签署？',
        riskDetails: '风险详情',
        noviewDifference: '由于其他签署方仍可能填写发起方指定的合同内容，上上签不对协议的当前版本与生效版本之间的内容差异进行审核，并默认您认可并同意签署生效版本。',
        start: 'Начать',
        nextStep: 'Следующий',
        help: '帮助',
        faceFailed: '非常抱歉，您的人脸比对失败',
        dualFailed: '非常抱歉，双录校验不通过，请核实您的信息后重试',
        faceFailedtips: '提示',
        verifyTry: '请核实身份信息后重试',
        faceLimit: '今天的人脸比对次数已达到上限',
        upSignReq: '请明天重试或联系合同发起者修改签署要求',
        reqFace: '发件人要求你进行刷脸校验',
        signAfterFace: '刷脸通过后即可完成合同签署',
        date: 'Дата',
        chooseSeal: '选择印章',
        seal: '印章',
        signature: '签名',
        handwrite: '手写',
        mysign: '我的签名',
        approvePlace: '审批留言，可不填',
        approvePlace_1: '审批留言',
        approvePlace_2: '可不填',
        approveAgree: '审批结果：同意',
        approveReject: '审批结果：驳回',
        signBy: '由',
        signByEnd: '盖章',
        sealBy: '由',
        sealByEnd: '签名',
        coverBy: '需盖',
        applicant: '申请人',
        continueVeri: '继续认证',
        registerAndReal: '请注册并实名',
        goToResiter: '请注册并认证',
        sureToUse: '确定使用',
        toSign: '签约吗?',
        pleaseComplete: '请先完成',
        confirmSign: '再确认签署',
        admin: '管理员',
        contratAdmin: '请联系管理员将您的账号',
        addToEnt: '添加为企业成员',
        alreadyExists: '在上上签已存在',
        sendMsg: '上上签将以短信形式给管理员发以下内容：',
        applyJoin: '申请加入',
        title: 'заголовок',
        viewImg: 'вид',
        priLetter: 'личное письмо',
        priLetterFromSomeone: 'письмо от {name}',
        readLetter: 'Понял.',
        approve: '同意',
        disapprove: 'Отклонить',
        refuseSign: 'Отказ в подписи',
        paperSign: '改用纸质签署',
        refuseTip: '请选择拒绝理由',
        refuseReason: 'Укажите причину отказа, чтобы помочь другой стороне понять вашу проблему и ускорить процесс заключения контракта',
        reasonWriteTip: '请填写拒签理由',
        refuseReasonOther: '更多拒签理由（可不填） | 更多拒签理由（必填）',
        refuseConfirm: 'Отказ в подписи',
        signValidationTitle: '签署校验',
        email: 'E-mail',
        phoneNumber: 'номер телефона',
        password: '密码',
        verificationCode: 'Код подтверждения',
        mailVerificationCode: 'Pin Code',
        forgetPsw: 'Забыли пароль',
        if: '，是否',
        forgetPassword: 'Забыли пароль',
        rejectionVer: '拒签校验',
        msgTip: 'Все время не получаете СМС сообщение? Пожалуйста, попробуйте еще раз ',
        voiceVerCode: 'Голосовой код подтверждения',
        SMSVerCode: 'СМС подтверждение ',
        or: 'или ',
        emailVerCode: 'код подтверждения почтового ящика ',
        SentSuccessfully: 'Успешно отправлен',
        intervalTip: 'Интервал отправки слишком короткий ',
        signPsw: 'Пароль для подписи',
        signSuc: 'Успешно подписано',
        refuseSuc: '拒签成功',
        approveSuc: '审批成功',
        hdFile: '查看高清文件',
        otherOperations: 'Другие операций',
        reviewDetails: 'Детальное утверждение',
        close: 'Закрыть',
        submitter: '提交人',
        signatory: 'Подписант',
        reviewSchedule: '审批进度',
        signByPc: '由{name}签名',
        signPageDescription: '{index}-я страница, всего {total} страниц',
        sealBySomeone: '由{name}盖章',
        signDate: '签署日期',
        download: 'Скачать',
        signPage: 'Количество страниц: {page} страницы',
        viewContract: '查看合同',
        signNow: '立即签署',
        sender: 'Отправитель',
        signer: 'Подписант',
        startSignTime: '发起签约时间',
        signDeadLine: 'Крайний срок подписания',
        authGuide: {
            goToHome: 'Вернуться к меню',
            tip_1: '认证完成后，可查看并签署合同。',
            tip_2: '请使用身份 | 进行认证。',
            tip_3: '发来合同',
            tip_4: '请联系合同发起者 | 更改收件人。',
            tip_5: '您认证的 | 无法查看合同',
            new_tip_1: '基于发件方的合规要求，您需要完成以下步骤：',
            new_tip_2: '基于发件方的合规要求，您需要以：',
            new_tip_3: '完成以下步骤。',
            new_tip_4: '如果您已有印章权限，会为您自动跳过第2步',
            entUserName: '姓名：',
            idNumberForVerify: '身份证号：',
            realNameAuth: '实名认证',
            applySeal: '申请印章',
            signContract: '签署合同',
        },
        switch: 'переключение',
        selectSignature: '选择签名',
        selectSigner: '选择签名人',
        pleaseScanToSign: '请用支付宝或微信扫一扫签署',
        pleaseScanAliPay: '请使用支付宝app扫描二维码签署',
        pleaseScanWechat: '请使用微信app扫描二维码签署',
        requiredFaceSign: '合同发件人要求您刷脸签署',
        requiredDualSign: '合同发件人要求你使用双录校验',
        qrcodeInvalid: '二维码信息已过期，请刷新',
        faceFirstExceed: '刷脸未通过，接下来将使用验证码校验',
        verCodeVerify: '验证码校验',
        applyToSign: '申请签署合同',
        autoRemindAfterApproval: '*审批通过后，自动发送签署提醒给签署人',
        cannotSignBeforeApproval: '审批未完成，暂不能签署！',
        finishSignatureBeforeSign: '请先完成盖章/签名再确认签署',
        uploadFileOnRightSite: '您还有附件未上传，请先在右边栏上传附件',
        cannotApplySealNeedPay: '该份合同需要您支付，不支持申请他人盖章',
        unlimitedNotice: '该合同计费不限量使用',
        units: '{num}份',
        contractToPrivate: '对私合同',
        contractToPublic: '对公合同',
        paySum: '共{sum}需要您支付',
        payTotal: '共计{total}元',
        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值',
        contactToRecharge: '请联系主管理员充值',
        deductPublicNotice: '对私合同可用份数不足时会扣除对公合同。',
        needSignerPay: '合同发送方设置了签署方付费，并指定由您来支付合同费用。',
        recharge: '充值',
        toSubmit: '提交',
        appliedSeal: '用印申请已提交',
        noSeal: '无印章',
        noSwitchSealNeedDistribute: '没有可切换的印章，请联系企业主管理员添加印章并授权',
        knew: '知道了',
        noSwitchSealNeedAppend: '没有可切换的印章，请联系管理员添加印章',
        hadAutoSet: '已在另外{num}处自动',
        setThatSignature: '放置该签名',
        setThatSeal: '放置该印章',
        applyThatSeal: '申请该印章',
        savedOnLeftSite: '已保存到左侧签名栏',
        ridingSealMinLimit: '文档页数仅一页，无法加盖骑缝章',
        ridingSealMaxLimit: '超过146页，不支持加盖骑缝章',
        ridingSealMinOrMaxLimit: '文档页数仅一页或者超过146页，无法加盖骑缝章',
        noSealForRiding: '您没有可使用的印章，无法加盖骑缝章',
        noSwitchSealNeedAppendBySelf: '没有可切换的印章，您可以前往企业控制台添加印章',
        gotoAppendSeal: '去添加印章',
        approvalFlowSuccessfulSet: '审批流设置成功',
        mandate: '同意授权',
        loginToAppendSeal: '您也可以用电脑登录上上签，去企业控制台添加印章',
        signIdentityAs: '当前正在以{person}的名义签署合同',
        enterNextContract: '进入下一份合同',
        fileList: '文件列表',
        addSignerFile: '添加附属资料',
        signatureFinish: '已全部盖章/签名',
        dragSignatureTip: '请将以下签章/日期拖放到文件中，可多次拖放',
        noticeToManager: '给管理员发通知',
        gotoAuthPerson: '去认证个人',
        senderRequire: '发件方要求您',
        senderRequireUseFollowIdentity: '发件方要求您满足以下身份之一',
        suggestToAuth: '您还未实名认证，建议您实名认证后签署',
        contactEntAdmin: '请联系企业主管理员',
        setYourAccount: '将您的账号',
        authInfoUnMatchNeedResend: '进行签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同',
        noEntNameNeedResend: '未指定签约企业名称，该合同无法被签署，请联系发起方重新发送合同',
        pleaseUse: '请使用',
        me: '我',
        myself: '本人，',
        reAuthBtnTip: '我是当前手机号的实际使用者，',
        reAuthBtnContent: '重新实名后，该账号的原实名会被驳回，请确认。',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        authInfoNoSame: '的身份签署合同。这与您当前登录的账号已完成的实名信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '当前账号是否是您的常用手机号？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署手机号，并指定由您签署。',
        confirmOk: '确认',
        goOnAuth: {
            0: '进行认证，',
            1: '请进行实名认证，',
            2: '进行实名认证，',
        },
        signContractAfterAuth: {
            0: '认证完成后，可签署合同。',
            1: '完成认证后即可签署合同。',
        },
        useIdentity: '以{name}的身份',
        inTheName: '以',
        of: '的',
        identity: '身份',
        nameIs: '姓名为',
        IDNumIs: '身份证号为',
        provideMoreAuthData: '补充更多认证材料',
        leadToAuthBeforeSign: '继续认证后即可签署合同',
        groupProxyAuthNeedMore: '您目前认证状态为集团代认证，若需单独签署合同请补充实名认证材料',
        contactSender: '如有疑问请联系发件方。',
        note: '注:',
        identityInfo: '身份信息',
        signNeedCoincidenceInfo: '完全一致才能签署合同。',
        needAuthPermissionContactAdmin: '您暂时没有实名认证权限，请联系管理员',
        iHadReadContract: '已阅读，本人已知晓{alias}内容',
        getVerCodeFirst: '请先获取验证码',
        appScanVerify: '上上签APP扫码校验',
        downloadBSApp: '下载上上签APP',
        scanned: '扫码成功',
        confirmInBSApp: '请在上上签APP中确认签署',
        qrCodeExpired: '二维码已失效，请刷新重试',
        appKey: 'APP安全校验',
        goToScan: '去扫码',
        setNotificationInUserCenter: '请先到用户中心设置通知方式',
        doNotWantUseVerCode: '不想用验证码',
        try: '试试',
        retry: '重试',
        faceExceedTimes: '当日刷脸次数已达上线，请明日再试',
        goToFaceVerify: '去刷脸',
        returnBack: '返回',
        switchTo: '切换至',
        youCanChooseIdentityBlow: '您可以选择以下签约主体',
        needDrawSignatureFirst: '您还没有签名，请先添加手绘签名',
        lacksSealNeedAppend: '您还未添加任何印章，请先去添加印章。',
        manageSeal: '管理印章',
        needDistributeSealToSelf: '您暂无可用印章，请先将自己设为印章持有人',
        chooseSealAfterAuth: '不想使用上面印章？ 实名认证后可更换印章',
        appendDrawSignature: '添加手绘签名',
        senderUnFill: '（发件人未填写）',
        declare: '说明',
        fileLessThan: '请上传小于{num}M的文件',
        fileNeedUploadImg: '上传时请使用支持的附件格式',
        serverError: '服务器开了点小差，请稍后再试',
        oldFormatTip: '支持jpg、png、jpeg、pdf、txt、zip、xml格式，单份文件大小不超过10M',
        fileLimitFormatAndSize: '单个资料图片数量不超过10张。',
        fileFormatImage: '支持jpg、png、jpeg格式，单张图片大小不超过20M，允许上传10张',
        fileFormatFile: '支持pdf、txt、zip、xml格式，单份文件大小不超过10M',
        signNeedKnow: '签约须知',
        signNeedKnowFrom: '来自{sender}的签约须知',
        approvalInfo: '审批须知',
        approveNeedKnowFrom: '来自{sender}提交的审批资料',
        setLabel: '设置标签',
        addRidingSeal: '添加骑缝章',
        delRidingSeal: '删除骑缝章',
        file: '文件',
        attachmentContent: '附件内容',
        downloadFile: '下载源文件',
        noLabelPleaseAppend: '还没有标签，请前往企业控制台添加',
        archiveTo: '归档到',
        hadArchivedToFolder: '已将合同成功移动到{who}的{folderName}文件夹中',
        pleaseScanToHandleWrite: '请用微信或者手机浏览器扫码，在移动设备上手写签名',
        save: '保存',
        remind: '提醒',
        riskTip: '风险提醒',
        chooseApplyPerson: '选择申请人',
        chooseAdminSign: '选择印章管理员',
        useSealByOther: '他人盖章',
        getSeal: '获取印章',
        nowApplySealList: '您正在请求以下印章',
        nowAdminSealList: '你正在申请获得以下印章',
        chooseApplyPersonToDeal: '请选择申请人，您的申请以及合同将会转交给所选人来处理',
        chooseApplyPersonToMandate: '请选择印章管理员，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同',
        contactGroupAdminToDistributeSeal: '请联系集团管理员分配印章',
        sealApplySentPleaseWait: '印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式',
        successfulSent: '发送成功',
        needSomeoneToSignature: '由{x}盖{y}',
        needToSet: '需盖',
        approver: '申请人：',
        clickToSignature: '点击此处签名',
        transferToOtherToSign: '转给其他人签',
        signatureBy: '由{x}签名',
        tipRightNumber: '请输入正确的数字',
        tipRequired: '必填值不可为空',
        confirm: '确定',
        viewContractDetail: '查看合同详情',
        crossPlatformCofirm: {
            message: '您好，当前合同需要跨平台签署，签署的文件需要传输到境外，您是否同意？',
            title: '数据授权',
            confirmButtonText: '同意授权',
            cancelButtonText: '取消',
        },
        sealScope: '印章使用范围',
        currentContract: '当前合同',
        allContract: '所有合同',
    },
    signJa: {
        beforeSignTip1: '根据发件方要求, 请以此企业名义进行签署：',
        beforeSignTip2: '发件方指定了 {signer} 完成签署。如确认信息正确, 可直接签署。',
        beforeSignTip3: '如信息有误, 请与发件方联系, 更换指定的签署人信息。',
        beforeSignTip4: '检测到该账号已注册的姓名为 {currentUser}, 与当前发件方要求的 {signer} 不一致, 是否确认更换为 {signer} ',
        beforeSignTip5: '检测到当前账号绑定的姓名为：{currentUser}, 与甲方指定要求 {signer} 签署, 不一致',
        beforeSignTip6: '请根据实际情况, 确认修改为甲方指定的 {signer} 进行签署',
        beforeSignTip7: '或者与甲方进行沟通，更换指定的签署人',
        entNamePlaceholder: '请输入企业名称',
        corporateNumberPlaceholder: '请输入法人番号',
        corporateNumber: '法人番号',
        singerNamePlaceholder: '请输入签署人姓名',
        singerName: '签署人姓名',
        businessPic: '印鉴证明书',
        waitApprove: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',
        itsMe: '是我本人',
        wrongInformation: '信息有误',
        confirmChange: '确认更换',
        communicateSender1: '不更换, 与甲方沟通',
        communicateSender2: '取消, 去与发件方沟通',
    },
    signPC: {
        commonSign: 'Подтвердить подпись',
        contractVerification: 'Проверка подписи',
        VerCodeVerify: 'Проверить код подтверждение',
        QrCodeVerify: 'Проверка QR-кода',
        verifyTip: 'BestSign в данный момент проверяет ваш безопасный цифровой сертификат, вы находитесь в защищенной среде электронной подписи, пожалуйста, будьте спокойны в подписывании!',
        verifyAllTip: '上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！',
        selectSeal: 'Электронная печать',
        toAddSealWithConsole: ' Вы также можете добавить печать на платформе управление компаниями',
        use: 'Использовать',
        toAddSeal: 'Добавить печать',
        mySeal: 'Моя печать',
        operationCompleted: 'Операция завершена',
    },
    signTip: {
        contractDetail: 'Информация о контракте',
        downloadBtn: '下载APP',
        tips: 'Напоминать',
        submit: 'Подтвердить',
        SigningCompleted: 'Успешно подписано',
        submitCompleted: '等待他人处理',
        noTurnSign: '尚未轮到签署或没有签署权限或登录身份已过期',
        noRightSign: '合同正在签署中，当前用户不允许签署操作',
        noNeedSign: '内部决议合同，已无需签署',
        ApprovalCompleted: '审批成功',
        contractRevoked: '该合同已被撤销',
        contractRefused: '该合同已被拒签',
        linkExpired: '该链接已失效',
        contractClosed: '该合同已截止签约',
        approvalReject: '该合同审批已被驳回',
        approving: '合同正在审批中',
        viewContract: 'Просмотреть контракт',
        downloadContract: '下载合同',
        signed: 'контракта',
        approved: 'утверждение',
        approval: 'утверждение',
        personHas: ' человек ',
        personHave: ' человек ',
        personHasnot: '人未',
        personsHavenot: '人未',
        cannotReview: '无法查看合同',
        cannotDownload: '该合同不支持手机下载。因为合同由发件方私有存储，上上签无法取到合同。',
        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',
        beenDeleted: '您的账号已被企业管理员删除',
        unActive: '无法继续激活账',
        back: '返回',
        contratStatusDes: 'Статус контракта:',
        contractConditionDes: 'Статус контракта:',
        contractIng: 'Подписание {key}',
        contractComplete: 'Подписание {key} завершено',
        operate: '合同操作',
        freeContract: '完成首次合同发送，可免费再获取合同份数',
        sendContract: '去发合同',
        congratulations: '恭喜{name}企业已完成{num}份合同签署，',
        carbonSaving: '预估节碳{num}g',
        signGift: '上上签赠送您{num}份对私合同（使用期限至{limit}）',
        followPublic: '关注微信公众号，随时接收合同消息',
        congratulationsSingle: '恭喜{name}完成合同签署，',
        carbonSavingSingle: '预估新增节碳量2002.4g',
        viewContractTip: '如需更换盖章人，可点击“查看详情”按钮打开合同详情页，随后点击“申请盖章”按钮',
    },
    view: {
        title: '查看合同',
        ok: '完成',
        cannotReview: '无法查看合同',
        privateStorage: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器',
    },
    prepare: {
        signHeaderTitle: 'Добавить файл и подписанта',
        step1: 'Шаг 1',
        uploadFile: 'Загрузить файлы',
        step2: 'Шаг 2',
        addSigner: 'Добавить подписывающую сторону',
        step3: 'Шаг 3',
        actionDemo: 'Процесс демонстраций',
        next: 'Следующий',
        isUploadingErr: 'Файл еще не был загружен. Пожалуйста, продолжите после завершения.',
        noUploadFileErr: 'Файл не загружен, пожалуйста, продолжите после загрузки',
        noContractTitleErr: 'Название договора не заполнено, после заполнения, можете продолжить',
        contractTypeErr: 'Данный тип договора был удален, прошу снова выбрать тип договора',
        expiredDateErr: 'Срок подписания неверный, прошу после поправки продолжить',
        noExpiredDateErr: 'Пожалуйста, заполните срок подписания и продолжайте',
        noRecipientsErr: 'Добавьте хотя бы одного подписанта',
        noAccountErr: 'Аккаунт не может быть пустым',
        noUserNameErr: '姓名不能为空',
        noIDNumberErr: '身份证号码不能为空',
        accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',
        userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',
        enterpriseNameErr: 'Неверный формат, введите правильное название компании',
        idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',
        signerErr: 'Подписавшая сторона имеет ошибку',
        noSignerErr: 'Прошу по меньшей мере добавить одного подписчика',
        lackAttachmentNameErr: 'Пожалуйста, заполните название вложения',
        repeatRecipientsErr: 'Во время подписания нельзя добавлять повторно подписывающие стороны, если не в последовательном порядке',
        innerContact: 'Контакты внутреннего отдела',
        outerContact: 'Контакты внешнего отдела',
        search: 'Поиск',
        accountSelected: 'Уже выбран аккаунт',
        groupNameAll: 'Полностью',
        unclassified: 'Не отсортировано',
        fileLessThan: 'Загрузка файла меньше {num}м',
        beExcel: 'Загрузка файла Excel',
        usePdf: 'при загрузке используйте файл PDF или рисунок',
        fileNameMoreThan: 'имя файла с длиной более ${num}, будет автоматически удалено',
        needAddSender: '未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？',
        addSender: '添加为签约方',
        tip: '提示',
        cancel: '取消',
    },
    addReceiver: {
        limitFaceConfigTip: '你的合同单价过低，该功能不可用，请联系上上签协商',
        orderSignLabel: 'Последовательное подписание ',
        contactAddress: 'Контактная адресная книга',
        signOrder: 'Порядок подписания ',
        account: 'Аккаунт ',
        accountPlaceholder: 'Телефон/электронная почта (требуется)',
        accountReceptionCollection: '前台代收',
        accountReceptionCollectionTip1: '不知道对方具体账号或对方没有账号，',
        accountReceptionCollectionTip2: '请选择前台代收',
        signSubjectPerson: 'Тема подписи: Физическое лицо',
        nameTips: 'Ф. И. О.  (выборочное заполнение)',
        requiredNameTips: '姓名（必填，用于签约身份核对）',
        entOperatorNameTips: 'Ф. И. О.  (выборочное заполнение)',
        needAuth: 'Нужно аутентифицироваться',
        signSubjectEnt: 'Тема подписи: Юридическое лицо ',
        entNameTips: 'Наименование компании (Выборочное заполнение)',
        operator: 'Исполнитель',
        sign: 'Подписать ',
        more: 'Больше',
        faceFirst: 'Приоритетная чистка, резервное копирование кода подтверждения',
        faceFirstTips: 'При подписании система по умолчанию выполняет проверку лица. Когда число раз, когда кисть не проходит, достигает верхнего предела дня, она автоматически переключается на проверку кода проверки',
        mustFace: 'Обязательно подпишите с распознаванием лица ',
        handWriteNotAllowed: 'Рукописные подписи не допускаются',
        mustHandWrite: 'Обязательно подпишите рукой ',
        fillIDNumber: 'паспортные данные ',
        fillNoticeCall: 'номер телефона для уведомления ',
        fillNoticeCallTips: 'Введите номер телефона для уведомления ',
        addNotice: 'Добавить личное сообщение',
        attachTips: 'Требования к приложению',
        faceSign: 'Обязательно подпишите с распознаванием лица ',
        faceSignTips: 'Данный пользователь должен пройти аутентификацию лица, чтобы завершить подписание ',
        handWriteNotAllowedTips: 'Пользователь может выбрать только подпись, которая была установлена, или использовать подпись шрифта по умолчанию для завершения подписи',
        handWriteTips: 'Пользователю нужна рукописная подпись для завершения подписи',
        idNumberTips: 'Используется для подписи проверки личности',
        verifyBefore: 'Подтвердите личность перед просмотром файлов',
        verify: 'Подтвердить личность',
        verifyTips: 'максимум 20 букв',
        verifyTips2: 'Вы должны предоставить эту информацию для проверки данному пользователю',
        sendToThirdPlatform: 'Отправить платформе третьей стороне',
        platFormName: 'Название платформы',
        fillThirdPlatFormName: 'Введите название третьей платформы',
        attach: 'Прикрепленный файл ',
        attachName: 'Название приложении',
        exampleID: 'например: фото паспорта',
        attachInfo: 'Инструкция приложения',
        attachInfoTips: 'например:  загрузите фото паспорта',
        addAttachRequire: 'Добавить требования к вложению',
        addSignEnt: 'Добавить подпись компании ',
        addSignPerson: 'Добавить подпись физического лица ',
        selectContact: 'Выбрать контактное лицо',
        save: 'Сохранить',
        searchVerify: 'Проверка запроса',
        fillImageContentTips: 'Пожалуйста, заполните содержание изображения',
        ok: 'Подтвердить ',
        findContact: 'Найти следующих подписывающих сторон с договора',
        signer: 'Подписант ',
        signerTips: 'Маленькое примечание: после выбора подписанта, платформа поможет вам определить место подписи и  печати',
        add: 'Добавить ',
        notAdd: 'Не добавляйте',
        cc: 'Отправить копию ',
        notNeedAuth: 'Не требует аутентификаций',
        extracting: 'Извлечение…',
        autoFill: 'Автоматически заполнить данные подписанта',
        failExtracting: 'не получено подписавшей стороной',
        idNumberForVerifyErr: 'Неверный формат, пожалуйста, введите правильное данные паспорта',
        noAccountErr: 'Аккаунт не может быть пустым',
        noUserNameErr: '姓名不能为空',
        noIDNumberErr: '身份证号码不能为空',
        accountFormatErr: 'Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.',
        enterpriseNameErr: 'Неверный формат, введите правильное название компании',
        userNameFormatErr: 'Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.',
        riskCues: 'Предупреждение о риске',
        riskCuesMsg: 'Если подписавшая сторона подписывается не с настоящим именем, вам необходимо будет предоставить паспорт подписавшей стороны в случае возникновения спора. Чтобы избежать риска, выберите нужное настоящее имя.',
        confirmBtnText: 'Выбрать нужные данные',
        cancelBtnText: 'Выбрать не нужные данные',
        attachLengthErr: 'Вы можете добавить до 50 запросов на вложение только одному подписанту',
        collapse: 'Сложить',
        expand: 'Разверните',
        delete: 'Удалить',
        saySomething: 'Комментарий (скажите что-нибудь)',
        addImage: 'Добавить фото',
        addImageTips: 'поддерживает pdf、word、jpg、png, формат, максимум можно загрузить 10 шт.',
        give: 'дайте',
        fileMax: 'количество отдачи превысило верхний предел',
    },
    field: {
        send: 'Отправить ',
        contractDispatchApply: 'подаять заявку на контракт',
        contractNeedYouSign: 'Данный документ нужно подписать вам ',
        ifSignRightNow: 'Подписать ли его сейчас',
        signRightNow: 'Подпишите сейчас',
        signLater: 'Знак позже',
        signaturePositionErr: 'Пожалуйста, укажите место подписи для каждого подписанта',
        sendSucceed: 'Успешно отправлен',
        confirm: 'Подтвердить ',
        cancel: 'Отменить',
        qrCodeTips: 'После подписания кода вы можете просмотреть детали подписи, проверить действительность подписи и проверить, был ли подделан договор.',
        pagesField: '-я страница, всего {totalPages} страниц ',
        suitableWidth: 'Соответствующая сторона ',
        signCheck: 'Проверка подписи',
        locateSignaturePosition: '定位签署位置',
        locateTips: 'Может помочь быстро найти местоположение подписи. В настоящее время поддерживается только первое местоположение подписи для каждого подписанта',
        step1: 'Шаг 1',
        selectSigner: 'Выбрать подписанта',
        step2: 'Шаг 2',
        dragSignaturePosition: 'Перетащить место подписи',
        signingField: 'Место подписи',
        docTitle: 'Документ ',
        totalPages: 'Количество страниц: {totalPages} страницы',
        receiver: 'Приемник',
        delete: 'Удалить',
        deductPublicNotice: 'Если количество копий частного договора недостаточно, договор будет вычтен',
        unlimitedNotice: '该合同计费不限量使用',
        charge: 'Расчет',
        units: '{num} ',
        clickDecoration: '点击合同装饰',
        contractToPrivate: 'корпоративный контракт',
        contractToPublic: 'частный контракт',
        costTips: {
            1: 'Для публичного договора: подписант(не включая отправителя) договор с корпоративным аккаунтом',
            2: 'Для частного договора: подписант(не включая отправителя) договор без корпоративного аккаунта',
            3: 'количество тарификации рассчитывается исходя из количество копий файла.',
            4: 'количество тарификации=количество файлов×партия введенных количество пользователей',
        },
        costInfo: '发送合同成功后将立即扣除费用，合同完成、逾期、撤回或拒签均不退还。',
        toCharge: 'Пополнить баланс ',
        contractNeedCharge: {
            1: 'Количество доступных контрактов недостаточна, и не может быть отправлено',
            2: 'Количество доступных контрактов недостаточна, пожалуйста, свяжитесь с главным администратором, чтобы пополнить»',
        },
        chooseApprover: '选择审批人：',
        nextStep: '下一步',
        submitApproval: '提交审批',
        autoSendAfterApproval: '*审批通过后，自动发送合同',
        chooseApprovalFlow: '请选择一个审批流',
        completeApprovalFlow: '您提交的审批流程不完整，请补全后重新提交',
        viewPrivateLetter: '查看私信',
        addPrivateLetter: '添加私信',
        append: '添加',
        privateLetter: '私信',
        signNeedKnow: '签约须知',
        maximum5M: '请上传小于5M的文档',
        uploadServerFailure: '上传到服务器失败',
        uploadFailure: '上传失败',
        pager: '页码',
        seal: 'Поставить печать ',
        signature: 'Подписать',
        signDate: 'Дата подписания',
        text: '文本',
        date: '日期',
        qrCode: '二维码',
        number: '数字',
        dynamicTable: '动态表格',
        terms: '合同条款',
        checkBox: '复选框',
        radioBox: '单选框',
        image: '图片',
    },
    paperSign: {
        title: '使用纸质方式签署',
        stepText: ['下一步', '确认纸质签', '确定'],
        needUploadFile: '请先上传扫描件',
        uploadError: '上传失败',
        cancel: '取消',
        downloadPaperFile: '获取纸质签文件',
        step0: {
            title: '您需要先下载打印合同，加盖物理章后，邮寄给发件方。',
            address: '邮寄地址：',
            contactName: '接收人姓名：',
            contactPhone: '接收人联系方式：',
            defaultValue: '请通过线下方式向发件方索取',
        },
        step1: {
            title0: '第一步：下载&打印纸质合同',
            title0Desc: ['下载打印的合同应包含已签署的电子章的图案。请', '获取纸质签文件。'],
            title1: '第二步：加盖印章',
            title1Desc: '在纸质合同上加盖合同有效的公司印章。',
            title2: ['第三步：', '上传扫描件，', '回到签署页面，点击签署按钮，完成纸质签'],
            title2Desc: ['将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，', '电子合同中不展示您的印章图案，但会记录您此次操作过程。'],
        },
        step2: {
            title: ['将纸质合同扫描件（PDF格式文件）上传', '请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。'],
            uploadFile: '上传扫描件',
            getCodeVerify: '获取合同签署校验',
            isUploading: '上传中...',
        },
    },
    allowPaperSignDialog: {
        title: '允许纸质签',
        content: '该合同为{senderName}发给{receiverName}的合同, 允许使用纸质方式签署。',
        tip: '您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。',
        icon: '转纸质签署 >>',
        goSign: '去电子签',
        cancel: '取消',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章提示',
            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',
            tip1: '检测到有企业印章与企业名称：',
            tip2: '是否要继续使用当前印章图片？',
            tip3: '根据发件方要求，您需要使用企业名称为：',
            tip4: '的印章',
            tip5: '请确认印章已符合要求，否则将会影响合同的有效性！',
            tip6: '不匹配，请确保印章符合发件方要求。',
            guide: '如何上传正确的印章 >>',
            next: '继续使用',
            tip7: '且您的印章名称不符合规范，带有“{keyWord}”字样。',
            tip8: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？',
        },
        exampleSeal: {
            title: '上传印章图案方式',
            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],
            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],
            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],
        },
        confirm: '确认',
        cancel: '取消',
    },
    addSealDialog: {
        title: '添加印章图片',
        dec1: '请从本地文件夹中选择一张印章图片（格式为JPG、JPEG、PNP等），由系统将此印章图片合并进入当前合同中。',
        dec2: '之后还需要您点击“签署”按钮通过签署校验，即可完成盖章。',
        updateNewSeal: '上传新章',
    },
};
