export default {
    sign: {
        sealLabelsTip: '契約書に{sealLabelslen}つの押印が必要です。{personStr}が{otherSealLen}つの押印を行い、残りの{mySealLen}つはあなたが押印します。印章はページ上に表示されています。続行するかどうかご確認ください。',
        continue: '続行',
        nonMainlandCARenewalTip: '申请续期后，系统会自动驳回原实名结果，请尽快完成认证。',
        reselect: '再選',
        approvalFeatures: {
            dialogTitle: '新機能紹介',
            understand: 'わかりました',
            feature1: '文の強調注釈',
            feature2: 'フィールドのハイライト',
            tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',
            tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',
            tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。',
            tip4: 'テキストフィールドを選択後、注釈ボタンをクリックして注釈テキストを追加できます。完了したら、修正または削除をクリックします。注釈の内容は、契約詳細ページ - 会社内部操作ログで確認できます。-公司内部操作日志中查看。',
            tip5: 'ステップ1：注釈を追加するテキストフィールドを選択します。',
            tip6: 'ステップ2：注釈を編集または削除します。',
            annotate: '注釈',
            delete: '削除',
            edit: '修正',
            operateTitle: '承認注釈の追加',
            placeholder: '255語以内',
        },
        contractHighLight: {
            dialogTitle: '契約書ハイライト表示',
            understand: 'わかりました',
            tip1: 'ボタンをクリックすると契約書のすべての「テンプレート内容フィールド」がハイライト表示されます。重要情報の把握に便利です。',
            tip2: '左下角の提示ボタンをクリックすると、テンプレート内容フィールドのハイライト表示がオンになります。',
            tip3: 'ハイライト表示により、契約書の内容をすばやく見つけてフィールドに記入できます。',
        },
        needRemark: '備考を記入する必要があります',
        notNeedRemark: '備考を記入しなくても問題ありません',
        switchToReceiver: 'お客様は{receiver}に切り替えています',
        notAddEntTip: '現在のユーザはこの企業のメンバーではありません、プライマリ管理者に連絡して企業に加入してください。',
        contractPartiesYouChoose: '選択できる契約主体は',
        contractPartyFilled: '発信者の記入した契約主体は',
        certifyOtherCompanies: 'その他企業の認証',
        youCanAlso: 'することもできます。',
        needVerification: '署名するには実名認証した後でなければなりません',
        prompt: '注意',
        submit: '確定',
        cancel: 'キャンセル',
        sign: 'すぐに契約',
        addSeal: 'パソコンを使用してベストサインのオフィシャルページにログインして印章を追加してください。',
        noSealAvailable: '申し訳ありません。現在使用可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',
        memberNoSealAvailable: '現在使用可能な印章がありません。管理者に連絡して構成した後再署名してください。もしくはオフラインにて管理者主任に連絡して構成してください。',
        noticeAdminFoSeal: '管理者主任に通知する',
        requestSomeone: '他人の認証を要求する',
        requestOthersToContinue: '管理者主任に通知して実名認証を追加します',
        requestOthersToContinueSucceed: '管理者に通知を送信しました',
        requestSomeoneList: '以下のスタッフに実名認証を要求する',
        electronicSeal: '電子公印',
        changeTheSeal: 'この印章を使いたくありません。実名認証後印章を変更できます',
        goToVerify: '実名認証を行う',
        noSealToChoose: '切替可能な印章がありません。管理印章が必要であれば先に実名認証を行ってください',
        goVerify: '認証を行う',
        goToVerifyEnt: '企業認証を行う',
        digitalCertificateTip: 'ベストサインがお客様のデジタル証明書をコールしています',
        signDes: 'お客様の契約環境は安全です。安心して署名してください。',
        signAgain: '署名を続ける',
        send: '送信',
        person: '個人',
        ent: '企業',
        entName: '企業名',
        account: 'アカウント',
        accountPH: '携帯電話またはメールアドレス',
        approved: '審査',
        signVerification: '署名',
        cannotReview: '契約書が確認できません',
        connectFail: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません.',
        connectFailTip: '您可以尝试以下方法解决问题：',
        connectFailTip1: '1、刷新页面。',
        connectFailTip2: '2、耐心等待并稍后重试。有可能是因为发件方企业部署的服务器出现了异常，企业IT技术人员重启服务器需要时间。',
        connectFailTip3: '3、发件方企业是否向你强调过，需要使用特定的wifi网络才能访问？如果有过这方面的说明，你需要切换手机或电脑设备连接的网络。',
        personalMaterials: '発信者は更に認証資料の追加を要求しています',
        noSupportface: '契約発起側がお客様の顔認証署名を要求しています。中国大陸者以外は顔認証署名をサポートしていませんので、発起側に署名要求を修正するよう連絡してください。',
        lackEntName: '企業名を入力してください',
        errAccount: '正確なメールアドレスもしくは携帯番号を記入してください',
        noticeAdmin: '参加の申請',
        signDone: '署名完了',
        signDoneTip: 'すでに契約書に署名しています',
        approveDone: '審査完了',
        approveDoneTip: 'すでに契約書を審査しています',
        completeSign: 'まず先に「捺印箇所」もしくは「署名箇所」をタップして署名を完了してください',
        fillFirst: 'まず先に入力ボックスに契約内容を記入してください',
        stillSignTip: 'この{alias}に署名した後、{alias}の内容を変更する可能性のある他の署名者がいますが、署名を続けますか？',
        signHighLightTip: '追加または変更が可能な{alias}内容は計{count}箇所',
        riskDetails: 'リスク詳細',
        noviewDifference: '他の署名者は依然として発信者が指定した{alias}の内容を変更することができるため、ベストサインでは現在の契約書と有効なバージョンの違いの内容に審査を行わず、デフォルトで有効なバージョンに署名することを認め、同意するものとします。',
        highLightTip: 'こうしたリスクのある内容は「ハイライト表示」されます、詳細に確認してください。ページを更新するとハイライト表示を取り消せます。',
        commonTip: '注意',
        understand: 'わかりました',
        view: '表示',
        start: '開始',
        nextStep: '次へ',
        help: 'ヘルプ',
        faceFailed: '申し訳ありません。顔認証に失敗しました',
        dualFailed: '録音・録画認証に失敗しました。お客様の情報をご確認の上、再度お試しください。',
        faceFailedtips: '注意',
        verifyTry: 'ID情報を確認した後お試しください',
        faceLimit: '今日の顔認証回数が上限に達しています',
        upSignReq: '今日の顔認証回数が上限に達しています。明日再度行うか契約送信元に署名要求の修正を依頼してください。',
        reqFace: '送信元がお客様に顔認証の検証を要請しています',
        signAfterFace: '顔認証通過後すぐに契約書の署名が完了します',
        qrcodeInvalid: '二次元コード情報が失効しています。リロードしてください',
        faceFirstExceed: '顔スキャン失敗、続いて認証コードを使用して認証を行います',
        date: '日付',
        chooseSeal: '印章の選択',
        seal: '印章',
        signature: '署名',
        handwrite: '手書き',
        mysign: 'マイサイン',
        approvePlace: '承認メッセージ（任意）',
        approvePlace_1: '承認メッセージ',
        approvePlace_2: '任意、255文字以下',
        approveAgree: '審査結果：同意',
        approveReject: '審査結果：却下',
        signBy: 'より',
        signByEnd: '捺印',
        sealBy: 'より',
        sealByEnd: '署名',
        coverBy: '捺印必要',
        applicant: '申請者',
        continueVeri: '認証を続ける',
        registerAndReal: '実名を新規登録してください',
        goToResiter: '新規登録及び認証をしてください',
        sureToUse: '使用の確定',
        toSign: '契約しますか？',
        pleaseComplete: 'まずは完了してください',
        confirmSign: '署名の再確認',
        admin: '管理者',
        contratAdmin: '管理者にお客様のアカウントを連絡してください',
        addToEnt: '企業メンバーとして追加',
        alreadyExists: 'ベストサインに存在しています',
        sendMsg: 'ベストサインはSMS形式で管理者に下記の内容を送信します：',
        applyJoin: '参加の申請',
        title: '表題',
        viewImg: '画像の確認',
        priLetter: 'メッセージ',
        priLetterFromSomeone: '{name}からのメッセージ',
        readLetter: 'わかりました。',
        approve: '同意',
        disapprove: '却下',
        refuseSign: '拒否',
        paperSign: '紙媒体の署名に変更',
        refuseTip: '拒否理由を選択してください',
        refuseReason: '拒否理由を記入することで相手方にお客様の問題を理解する手助けになり、契約手続きを早くします',
        reasonWriteTip: '拒否理由を記入してください',
        refuseReasonOther: '拒否理由の詳細(オプション) | 拒否理由の詳細(必須)',
        refuseConfirm: '拒否',
        refuseConfirmTip: 'あなたは「{reason}」という理由でこの契約書への署名を拒否します。続行したら、この契約書に署名できなくなります。続行しますか？',
        waitAndThink: 'キャンセル',
        signValidationTitle: '署名検証',
        email: 'メールアドレス',
        phoneNumber: '携帯電話',
        password: 'パスワード',
        verificationCode: '認証コード',
        mailVerificationCode: '認証コード',
        forgetPsw: 'パスワードをお忘れの場合',
        if: '、どうか',
        forgetPassword: 'パスワードをお忘れの場合',
        rejectionVer: '検証の拒否',
        msgTip: 'SMSを受け取っていない場合、',
        voiceVerCode: '音声認証コード',
        SMSVerCode: 'SMS認証コード',
        or: 'または',
        tryMore: 'を試してください',
        emailVerCode: '電子メール認証コード',
        SentSuccessfully: '送信完了',
        intervalTip: '送信時間間隔が短すぎます',
        signPsw: '署名パスワード',
        useSignPsw: '契約パスワードで認証',
        setSignPsw: '署名パスワードの設定',
        useVerCode: '認証コードで認証',
        inputVerifyCodeTip: '認証コードを入力してください',
        inputSignPwdTip: '契約パスワードを入力してください',
        signConfirmTip: {
            1: '本当にこの契約書に署名しますか？',
            2: '確定ボタンをタップしてすぐにこの契約書に署名する',
            confirm: '署名の確認',
        },
        signSuc: '署名成功',
        refuseSuc: '拒否成功',
        approveSuc: '承認完了',
        hdFile: '高解像度ファイルの確認',
        otherOperations: 'その他の操作',
        reviewDetails: '審査詳細',
        close: '閉じる',
        submitter: '提出者',
        signatory: '署名者',
        reviewSchedule: '審査進度',
        signByPc: '{name}による署名',
        signPageDescription: '第{index}ページ、計{total}ページ',
        sealBySomeone: '{name}による捺印',
        signDate: '署名日時',
        download: 'ダウンロード',
        signPage: 'ページ数：{page}ページ',
        signNow: 'すぐ署名',
        sender: '発信者',
        signer: '契約者',
        startSignTime: '契約発信日時',
        signDeadLine: '契約期限',
        authGuide: {
            goToHome: 'トップページに戻る',
            tip_1: '認証の完了後、契約書を閲覧し、署名ができるようになります。',
            tip_2: '身分を使用して | 認証を行ってください。',
            tip_3: '契約書の送付',
            tip_4: '契約書の送付者に | 受け取り者を変更するように連絡してください。',
            tip_5: 'お客様の認証した | 契約書が確認できません',
            new_tip_1: '発信者のコンプライアンス要件に基づき、下記の手順を完了する必要があります：',
            new_tip_2: '発信者のコンプライアンス要件に基づき、必要な書類は：',
            new_tip_3: '下記の手順を完了してください。',
            new_tip_4: '印章権限をお持ちであれば、自動で第2ステップにジャンプします',
            entUserName: '氏名：',
            idNumberForVerify: '身分証明書：',
            realNameAuth: '実名認証',
            applySeal: '印章の申請',
            signContract: '契約書の署名',
        },
        switch: '切り替え',
        rejectReasonList: {
            // authReason: '実名認証をしたくありません/できません',
            signOperateReason: '署名操作/検証操作に対して疑問をお持ちであれば、更に交流を進める必要があります',
            termReason: '契約条件/内容に異議があれば、更に交流を進める必要があります',
            explainReason: '契約内容に対してわからないことがあれば、事前に告知してください',
            otherReason: 'その他（理由を記載してください）',
        },
        selectSignature: '署名の選択',
        selectSigner: '署名者の選択',
        pleaseScanToSign: 'AlipayもしくはWechatスキャンを使用して署名してください',
        pleaseScanAliPay: 'AlipayアプリでQRコードを読み取り、署名してください',
        pleaseScanWechat: 'WeChatアプリでQRコードを読み取り、署名してください',
        requiredFaceSign: '契約書送信元がお客様に顔認証署名を要請しています',
        requiredDualSign: '契約送信者が録音・録画認証を要求しています。',
        verCodeVerify: '認証コードによる検証',
        applyToSign: '契約書の署名申請',
        autoRemindAfterApproval: '*審査に合格後、署名者に自動的にリマインダーを送信',
        cannotSignBeforeApproval: '審査がまだ終わっていないため、署名できません。',
        finishSignatureBeforeSign: '先に捺印/サインをしてから再度署名を確認してください',
        uploadFileOnRightSite: 'アップロードしていない添付書類がある場合、まずは右側の欄で添付書類をアップロードしてください',
        cannotApplySealNeedPay: '当該契約書はお客様が支払うものですので、他の人の捺印での申請を認めていません',
        cannotOtherSealReason: 'この契約は顔認証による本人確認が必要なため、代理での押印はできません',
        unlimitedNotice: 'この契約書の費用は使い放題です',
        units: '{num}部',
        contractToPrivate: '個人向け契約書',
        contractToPublic: '企業向け契約書',
        paySum: '共{sum}需要您支付',
        payTotal: '共计{total}元.',
        fundsLack: '您的可用合同份数不足，为确保合同顺利签署，建议立即充值.',
        contactToRecharge: '管理者主任に連絡してチャージしてください.',
        deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます。',
        needSignerPay: '契約送信者により着払い設定がされており、あなたは契約書送信料金の支払者に指定されています。',
        recharge: 'リチャージ',
        toSubmit: '提出',
        appliedSeal: '印章使用申請書を提出しています',
        noSeal: '印章がありません',
        noSwitchSealNeedDistribute: '切り替え可能な印章が登録されていません。企業の管理者主任に連絡して印章を追加し、許可してください。',
        viewApproveProcess: '承認フローを確認',
        approveProcess: '承認フロー',
        noApproveContent: '承認書類が提出されていません',
        knew: 'わかりました',
        noSwitchSealNeedAppend: '現在切り替え可能な印章がありません。管理者主任に連絡して印章を追加してください。',
        hadAutoSet: '別の{num}箇所で自動的に',
        setThatSignature: '当該署名を置きます',
        setThatSeal: '当該印章を置きます',
        applyThatSeal: '当該印章を申請',
        hasSetTip: '他の{index}箇所に自動配置',
        hasSetSealTip: 'この印鑑は他の{index}箇所に自動的に置かれている',
        hasSetSignatureTip: '署名は他の{index}か所に自動的に配置されました',
        hasApplyForSealTip: 'この印鑑は他の{index}か所で自動申請されています',
        savedOnLeftSite: '左側の署名欄に保存されました',
        ridingSealMinLimit: '書類は1ページのみのため、割り印が押せません',
        ridingSealMaxLimit: '146ページを超えているため、割り印をサポートしていません',
        ridingSealMinOrMaxLimit: '書類は1ページのみか146ページを超えているため、割り印が押せません',
        noSealForRiding: '使用可能な印章が登録されていないため、割り印を押せません',
        noSwitchSealNeedAppendBySelf: '切り替え可能な印章が登録されていません。企業の管理コンソールで印章を追加してください。',
        gotoAppendSeal: '印章を追加しにいく',
        approvalFlowSuccessfulSet: '承認フロー設定完了',
        mandate: '授権の同意',
        loginToAppendSeal: 'パソコンを使用してベストサインにログインして、企業の管理コンソールで印章を追加してください',
        signIdentityAs: '現在{person}の名義で契約書に署名しています',
        enterNextContract: '次の契約書に入る',
        fileList: 'ファイルリスト',
        addSignerFile: '付属資料の追加',
        signatureFinish: 'すでに全部捺印/署名しています',
        dragSignatureTip: '下記の捺印/日時をファイルにドラッグ＆ドロップしてください。複数回可能です',
        noticeToManager: '管理者に通知しました',
        gotoAuthPerson: '個人認証を行う',
        senderRequire: '発信者がお客様に要求しています',
        senderRequireUseFollowIdentity: '発信者がお客様に下記の身分証明の一つを要求しています',
        suggestToAuth: 'お客様は実名認証を行っていません。実名認証をした後署名するようにしてください',
        contactEntAdmin: '企業の管理者主任に連絡してください',
        setYourAccount: 'お客様のアカウントで',
        authInfoUnMatchNeedResend: '契約書の署名を行ってください。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください',
        noEntNameNeedResend: '契約企業名が指定されておらず、この契約書を署名することはできません。送信者に連絡して再度契約書の起稿を要求してください',
        pleaseUse: '使用してください',
        me: '私',
        myself: '本人',
        reAuthBtnTip: '私が現在の携帯番号の実質使用者です。',
        reAuthBtnContent: '再実名登録後、このアカウントの元の実名での利用は却下されますので、ご確認ください。',
        descNoSame1: ' の身分で契約書を署名',
        descNoSame2: 'これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',
        authInfoNoSame: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',
        authInfoNoSame2: 'の身分で契約書を署名。これとお客様が現在ログインしているアカウントと完了した実名情報と一致していません。',
        goHome: '契約書リストページに戻る>>',
        authInfo: '現在検出しているアカウントの実名IDは ',
        authInfo2: '現在検出しているアカウントの実名IDは ',
        in: 'に',
        finishAuth: '実名が完了しています。コンプライアンスに則した契約書の署名に用いられます',
        ask: '現在のアカウントで署名を続けますか？',
        reAuthBtnText: 'はい。このアカウントで再度実名署名を行います',
        changePhoneText: 'いいえ。発送者に連絡して署名の携帯番号を変更します',
        changePhoneTip1: '発信者の要求で、連絡してください',
        changePhoneTip2: '署名情報（携帯番号/氏名）を変更し、お客様による署名が指示されています。',
        confirmOk: '確認',
        goOnAuth: {
            0: '認証の実行',
            1: '実名認証を実行してください',
            2: '実名認証を実行します',
        },
        signContractAfterAuth: {
            0: '認証の完了後、契約書の署名ができるようになります。',
            1: '認証が完了した後契約書に署名ができるようになります。',
        },
        useIdentity: '{name}の身分で',
        inTheName: 'XX',
        of: 'の',
        identity: '身分',
        nameIs: '氏名は',
        IDNumIs: '身分証明番号は',
        provideMoreAuthData: '更に認証資料を追加する',
        leadToAuthBeforeSign: '認証が続けた後契約書に署名ができるようになります',
        groupProxyAuthNeedMore: '現在の認証状態はグループが代理認証しています。もしも単独で契約書に署名する必要があれば実名認証資料を追加してください',
        contactSender: '疑問がある場合発行者に連絡してください。',
        note: '注意：',
        identityInfo: 'ID情報',
        signNeedCoincidenceInfo: '完全一致しないと契約書の署名はできません。',
        needAuthPermissionContactAdmin: '実名認証権限がありません。管理者に連絡してください',
        iHadReadContract: '閲読し、{alias}内容を熟知しました',
        scrollToBottomTip: '最後のページまでスクロールする必要があります',
        getVerCodeFirst: '先に認証コードを取得してください',
        appScanVerify: 'ベストサインアプリの二次元コード検証',
        downloadBSApp: 'ベストサインアプリをダウンロード',
        scanned: '二次元コード成功',
        confirmInBSApp: 'ベストサインアプリの中で署名を確認してください',
        qrCodeExpired: '二次元コードが失効しています。リロードしてください',
        appKey: 'アプリセキュリティ検証',
        goToScan: '読み取る',
        setNotificationInUserCenter: 'ユーザーセンターで通知方法を設定してください',
        doNotWantUseVerCode: '認証コードを使いたくありません',
        try: '試す',
        retry: '再接続',
        goToFaceVerify: '顔認証に入る',
        faceExceedTimes: '当日の顔認証回数が上限に達しています',
        returnBack: '戻る',
        switchTo: '切り替え',
        youCanChooseIdentityBlow: '以下の契約主体を選択することができます',
        needDrawSignatureFirst: 'まだサインしていません。先に手書きサインを追加してください',
        lacksSealNeedAppend: 'まだ印章を何も追加していません。先に印章を追加してください。',
        manageSeal: '印章の管理',
        needDistributeSealToSelf: '現在使用可能な印章がありません。先に自分が印章の所有者になるよう設定してください',
        chooseSealAfterAuth: '上の印章を使いたくありません。実名認証後印章を変更できます',
        appendDrawSignature: '手書きサインの追加',
        senderUnFill: '（発信者が記入されていません)',
        declare: '説明',
        fileLessThan: '{num}M以下のファイルをアップロードしてください',
        fileNeedUploadImg: 'アップロードする際はサポートしているフォーマットを使ってください',
        serverError: 'サーバーエラーが発生しています。しばらくしてから試してください',
        oldFormatTip: 'jpg、png、jpeg、pdf、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下としてください',
        fileLimitFormatAndSize: '1つの資料画像は10枚を超えないようにしてください。',
        fileFormatImage: 'jpg、png、jpegフォーマットをサポートし、1画像当たりの容量は20M以下とし、10枚までアップロードできます',
        fileFormatFile: 'pdf、excel、word、txt、zip、xmlフォーマットをサポートし、1ファイルあたり10M以下とします',
        signNeedKnow: '契約注意事項',
        signNeedKnowFrom: '{sender}からの契約注意事項',
        approvalInfo: '審査注意事項',
        approveNeedKnowFrom: '{sender}-{sendEmployeeName}により提出された審査資料',
        approveBeforeSend: '合同发送前审批',
        approveBeforeSign: '合同签署前审批',
        approveOperator: '承認者',
        approvalOpinion: '承認メッセージ',
        employeeDefault: '従業員',
        setLabel: '設定タグ',
        addRidingSeal: '割り印の追加',
        delRidingSeal: '割り印の削除',
        file: 'ファイル',
        compressedFile: '圧縮ファイル',
        attachmentContent: '付属内容',
        pleaseClickView: '（クリックしてダウンロードしてください）',
        downloadFile: 'ソースファイルをダウンロード',
        noLabelPleaseAppend: 'タグがありません。企業の管理コンソールで追加してください。',
        archiveTo: 'ファイリング先',
        hadArchivedToFolder: '{who}の{folderName}フォルダーへの契約書の移動が完了しました',
        pleaseScanToHandleWrite: 'WechatまたはスマホのQRコードリーダーで読み取り',
        save: '保存',
        remind: 'リマインダー',
        riskTip: 'リスクに関するリマインダー',
        chooseApplyPerson: '押印者の設定',
        useSealByOther: '押印を委任',
        getSeal: '押印権限を申請',
        nowApplySealList: '以下の印章を要求しています',
        nowAdminSealList: '以下の印章取得を申請中です',
        chooseApplyPersonToDeal: '押印者を選択して下さい（この契約を引き続き閲覧し、フォローすることができます）。',
        chooseAdminSign: '印章管理者の設定	',
        chooseTransferPerson: '他の人に署名を渡す',
        chooseApplyPersonToMandate: '印章管理者を選択してください。この管理者による審査が完了したら、この印章を契約書に押印できるようになります。',
        contactGroupAdminToDistributeSeal: '集団の管理者に印章を分配するよう連絡してください',
        sealApplySentPleaseWait: '印章の分配申請を送信しました。審査が通るまでしばらくお待ちください。もしくはその他捺印の方法を選択することができます',
        successfulSent: '送信完了',
        authTip: {
            t2: ['注意：', '完全一致しないと契約書の署名はできません。', '企業名', ' ID情報', 'が完全一致しないと契約書の署名はできません。'],
            t3: '{x}はお客様に{text}の実名認証を実行するよう要求しています',
            tCommon1: '{entName}の身分で',
            tCommon2_1: '名前を{name}、身分証番号を{idCard}とします。',
            tCommon2_2: '名前を{name}とします',
            tCommon2_3: '身分証番号を{idCard}とします。',
            viewAndSign1: '認証が完了した後契約書の閲覧及び署名ができるようになります。',
            viewAndSignConflict: '{x}はお客様の{text}で契約書の確認と署名を行うよう要求しています。これはお客様のID情報と一致していません。ご自身で送付者に連絡してID情報を確認し、再度契約書の起稿を要求してください',
        },
        needSomeoneToSignature: '{x}により{y}が捺印されました',
        needToSet: '捺印必要',
        approver: '申請者：',
        clickToSignature: 'ここをタップして署名',
        transferToOtherToSign: '他の人に転送して署名',
        signatureBy: '{x}による署名',
        tipRightNumber: '正確な数字を入力してください',
        tipRightIdCard: '18桁の中国本土居民身分証番号を正しく入力してください',
        tipRightPhoneNumber: '11桁の携帯電話番号を正しく入力してください',
        tip: '提示',
        tipRequired: '値は必ず入力し空欄にできません',
        confirm: '確定',
        viewContractDetail: '契約内容の確認',
        required: '必須項目',
        optional: 'オプション',
        decimalLimit: '小数点以下{x}桁まで',
        intLimit: '整数の要求',
        invalidContract: 'この契約ご同意すると以下の契約を無効にし：',
        No: 'ナンバリング',
        chooseFrom2: '差出人は2種類のスタンプを設定しておりますので、1種類お選びください',
        crossPlatformCofirm: {
            message: 'この契約書はクロスプラットフォーム署名が必要です。契約書データを国外に送信することに同意しますか',
            title: 'データ承認',
            confirmButtonText: '同意する',
            cancelButtonText: 'キャンセル',
        },
        sealScope: '印章使用範囲',
        currentContract: '現在の契約書',
        allContract: 'すべての契約書',
        docView: '契約プレビュー',
        fixTextDisplay: 'ページの文字化けを修正',
        allPage: '合計{num}ページ',
        notJoinTip: '管理者に連絡して、企業のメンバーとして追加してもらってから署名してください',
    },
    signJa: {
        beforeSignTip1: '送信者のリクエストにより、この企業名義で署名してください：',
        beforeSignTip2: '送信者は{signer}を署名者に指定しています。情報が正しいことを確認したら署名できます。',
        beforeSignTip3: '情報が正しくなければ、送信者に連絡して、指定署名者の情報を変更してください。',
        beforeSignTip4: 'このアカウントで登録されている氏名は{currentUser}であり、送信者がリクエストした {signer} と異なります、 {signer} に変更しますか？',
        beforeSignTip5: '現在のアカウントに紐付けされた氏名は{currentUser}であり、甲の指定する{signer}と一致しません',
        beforeSignTip6: '実際の状況に基づき、甲の指定する {signer} に変更することを確認してください',
        beforeSignTip7: 'または甲と相談の上、指定署名者を変更します',
        entNamePlaceholder: '企業名を入力してください',
        corporateNumberPlaceholder: '法人番号を入力してください',
        corporateNumber: '法人番号',
        singerNamePlaceholder: '署名者の氏名を入力してください',
        singerName: '署名者氏名',
        itsMe: 'は私本人です',
        businessPic: '印鑑証明書',
        waitApprove: 'レビュー中。レビューの進行状況を理解する必要がある場合は、メールでお問い合わせください：<EMAIL>',
        wrongInformation: '情報が正しくありません',
        confirmChange: '変更する',
        communicateSender1: '変更せず、甲と相談する',
        communicateSender2: 'キャンセルし、送信者と相談する',
        createSeal: {
            title: '氏名を入力',
            tip: 'あなたの氏名を入力してください（スペースは改行で行えます）',
            emptyErr: '氏名を入力してください',
        },
        areaRegister: '企業登録地',
        jp: '日本',
        cn: '中国大陸',
        are: 'アラブ首長国連邦',
        other: 'その他',
        plsSelect: '選択してください',
        tip1: '登録先は中国大陸部の企業で、ent.bestsign.cnで実名登録を完了する必要があります。中国大陸部以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
        tip2: '企業が中国大陸版に署名して実名認証を完了している場合は、ent.bestsign.cnに直接ログインし、関連サービスを簡単に利用できます。海外版に署名したデータは、中国大陸版とは完全に独立していることに注意してください。',
        tip3: '現地の商業規制当局から取得した証明書番号を提供してください',
        tip4: '次の手順に従います',
        tip5: '1. 御社の担当カスタマーマネージャーに連絡し、企業実名認証の完了までご案内させていただきます。',
        tip6: '「チャージ管理」をクリックします。',
        tip7: '2. 御社とベストサインとの契約書のキャプチャ、または、カスタマーマネージャーとやり取りしたメールのキャプチャをアップロードしてください。',
        tip8: '少なくとも1つの契約を購入し、購入履歴のスクリーンショットを保存します。',
        tip9: '3. この方式は、日本および中国本土以外の企業のみ、ご利用いただけます。',
        tip10: '4. 資料提出後、ベストサインは3営業日以内に認証を完了します。',
        tip11: '重要なヒント',
        tip12: '購入者はエンタープライズユーザーでなければなりません。',
        tip13: '支払口座の企業フルネームは、記入した企業名と完全に一致している必要があります。',
        tip14: '日本、中国大陸部以外の企業でなければ使用できません。',
        comNum: '企業証明書番号',
        buyRecord: '証明書資料',
        selectArea: '企業登録先を選択してください',
        uaeTip1: '登録先はアラブ首長国連邦の企業で、uae.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
        uaeTip2: '企業がアラブ首長国連邦版に署名して実名認証を完了している場合は、uae.bestsign.comに直接ログインして、関連サービスを簡単に利用できます。注意する必要があるのは、海外版に署名したデータは、アラブ首長国連邦版とは完全に独立しています。',
        uaeTip3: '登録先はアラブ首長国連邦と中国大陸以外の企業で、ent.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
    },
    signPC: {
        commonSign: '署名の確認',
        contractVerification: '署名認証',
        VerCodeVerify: '認証コードによる検証',
        QrCodeVerify: '二次元コード検証',
        verifyTip: 'ベストサインでは現在お客様の安全なデジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',
        verifyAllTip: 'ベストサインでは企業デジタル証書とお客様の個人デジタル証書をコールしています。契約環境は安全ですので安心して署名してください。',
        selectSeal: '印章の選択',
        adminGuideTip: '因为您是企业主管理员，可以直接将企业印章分配给自己',
        toAddSealWithConsole: '電子公印は使用開始待ちです。他の印鑑を追加するには、コンソールへ移動してください。',
        use: '使用',
        toAddSeal: '印章を追加しにいく',
        mySeal: '私の印章',
        operationCompleted: '操作完了',
        FDASign: {
            date: '署名時間',
            signerAdd: '追加',
            signerEdit: '変更',
            editTip: '注意：中国語氏名はピンイン入力してください。例：San Zhang（張三）',
            inputNameTip: 'あなたの氏名を入力してください',
            inputName: '英語または中国語ピンインを入力してください',
            signerNameFillTip: '氏名のサインも記入する必要があります',
            plsInput: '入力してください',
            plsSelect: '選択してください',
            customInput: '自由入力',
        },
        signPlaceBySigner: {
            signGuide: '签署指导',
            howDragSeal: '如何拖章',
            howDragSignature: '如何拖签名',
            iKnow: '我知道了',
            step: {
                one: '第一步：阅读合同',
                two1: '第二步：点击“拖章”',
                two2: '第二步：点击“拖签名”',
                three: '第三步：点击“签署”按钮',
            },
            dragSeal: '拖章',
            continueDragSeal: '继续拖章',
            dragSignature: '拖签名',
            continueDragSignature: '继续拖签名',
            dragPlace: '按住此处拖动',
            notRemind: '不再提醒',
            signTip: {
                one: '第一步：通过点击“开始”，定位到需要签名/盖章处。',
                two: '第二步：通过点击“签名处/盖章处”，根据要求完成签名/盖章。',
            },
            finishSignatureBeforeSign: '请先完成拖签名/拖章再确认签署',
        },
        continueOperation: {
            success: '操作成功',
            exitApproval: '承認を終了',
            continueApproval: '承認を継続',
            next: '次へ ',
            none: 'ないです',
            tip: '注意',
            approvalProcess: '{totalNum}人の承認が必要で、そのうちの{passNum}人が承認済みです',
            receiver: '受信先：',
        },
    },
    signTip: {
        contractDetail: '契約状態',
        downloadBtn: 'アプリのダウンロード',
        tips: '注意',
        submit: '確定',
        SigningCompleted: '署名成功',
        submitCompleted: '他の人の処理待ち',
        noTurnSign: '署名の順番が来ていないか、署名権限がないか、ログインステータスが期限切れです',
        noRightSign: '契約書は署名中です、現在のユーザは署名操作が許可されていません',
        noNeedSign: '内部決議契約書、署名は不要',
        ApprovalCompleted: '承認完了',
        contractRevoked: '当該{alias}は取り下げられました',
        contractRefused: '当該{alias}は署名拒否されました',
        linkExpired: '当該リンクは無効です',
        contractClosed: '当該{alias}は契約期限切れです',
        approvalReject: '当該{alias}の審査は却下されました',
        approving: '{alias}は審査中です',
        viewContract: '{alias}を確認',
        viewContractList: '契約リストを見る',
        needMeSign: '（署名待ち{num}件）',
        downloadContract: '契約書のダウンロード',
        sign: '署名',
        signed: '署名',
        approved: '審査',
        approval: '審査',
        person: '人',
        personHas: '済み',
        personHave: '済み',
        personHasnot: '未',
        personsHavenot: '未',
        headsTaskDone: '{num}{done}{has}',
        headsTaskNotDone: '{num}{not}{done}',
        taskStatusBetween: '、',
        cannotReview: '契約書が確認できません',
        cannotDownload: '当該契約書は携帯電話でのダウンロードをサポートしていません。契約書が発信側の個人契約ストレージを使用しているため、ベストサインでは契約書を取得できません。',
        privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません',
        beenDeleted: 'お客様のアカウントはすでに企業管理者から削除されています',
        unActive: 'アカウントの有効化を継続できません',
        back: '戻る',
        contratStatusDes: '{key}状況：',
        contractConditionDes: '詳細：',
        contractIng: '{alias}{key}中',
        contractComplete: '{alias}{key}完了',
        dataProduct: {
            tip1: '{entName}各優良販売業者様/サプライヤー企業担当者様：',
            tip2: 'この度、｛entName｝の安定した発展への貢献に感謝し、｛bankName｝と共同で、お客様のビジネスの発展を加速させるサプライチェーン金融サービスを開始することになりました。',
            btnText: '社長にこの嬉しい知らせを伝えに行く',
        },
        signOnGoing: '署名{status}中',
        operate: '契約操作',
        freeContract: '初回の契約書送信を完了すると、更に無料の契約書部数を取得できます。',
        sendContract: '契約書を送る',
        congratulations: '{name}が{num}件の契約締結を完了したことをお祝いします，',
        carbonSaving: '推定で{num}gのカーボン削減が達成されました.',
        signGift: 'ベストサインより、{limit}まで利用可能な法人契約を{num}件贈呈いたします。',
        followPublic: 'WeChat公式アカウントをフォローし、契約の最新情報を迅速に受け取ってください。',
        congratulationsSingle: '{name}様、署名完了しました。',
        carbonSavingSingle: '凡そ2002.4gの二酸化炭素排出量が削減されました。',
        viewContractTip: '捺印者を変更する場合は、「詳細の確認」ボタンをクリックして契約詳細ページを開き、その後「捺印申請」ボタンをクリックしてください。',
        congratulationsCn: '電子署名を選択していただきありがとうございます！',
        carbonSavingSingleCn: '地球のために{num}gCO2eのカーボンを削減しました',
        carbonVerification: '*「カーボンストップ」による科学的計算',
    },
    view: {
        title: '契約書の確認',
        ok: '完了',
        cannotReview: '契約書が確認できません',
        privateStorage: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは契約ストレージのサーバーに接続することができません',
    },
    prepare: {
        sealArea: '捺印所',
        senderNotice: '現在の契約書送信主体は：{entName}です。',
        preSetDialogConfirm: 'わかりました。',
        preSetDialogContact: '開通するようすぐにベストサインの販売スタッフに連絡する',
        preSetDialogInfo: '契約書の事前設定に成功すると、システムはテンプレートに基づき対応する署名者情報、署名要件、署名位置、契約内容フィールドなどを自動的に入力します',
        preSetDialogTitle: '契約書事前設定テンプレートとは何ですか？',
        initialValues: '契約内容を基に事前設定する初期値',
        proxyUpload: 'ローカル文書のアップロード後、契約発起側は選択する事ができます',
        signHeaderTitle: '文書と契約者の追加',
        step1: '手順1',
        confirmSender: '発起側の確認',
        step2: '手順2',
        uploadFile: 'ファイルのアップロード',
        step3: '手順3',
        addSigner: '契約者の追加',
        actionDemo: '操作デモ',
        next: '次へ',
        isUploadingErr: '文書のアップロードが完了していません。完了後継続して操作してください',
        noUploadFileErr: 'アップロードが完了していません。アップロード後継続して操作してください',
        noContractTitleErr: '契約書名称が未記入です。記入した後継続してください',
        contractTypeErr: '現在の契約タイプは削除されています。契約タイプを再選択してください',
        expiredDateErr: '契約有効時間に誤りがあります。修正後継続してください',
        noExpiredDateErr: '署名期限を記入した後継続してください',
        describeFieldsErr: '必須内容フィールドを記入した後継続してください',
        noRecipientsErr: '少なくとも契約者1つを追加',
        noAccountErr: 'アカウントは空欄に出来ません',
        noUserNameErr: '氏名は空欄に出来ません',
        noIDNumberErr: '身分証番号は空欄にできません',
        accountFormatErr: 'フォーマットが正確でありません。正しいメールアドレスを入力してください',
        userNameFormatErr: 'フォーマットが正確でありません。正確な氏名を入力してください',
        enterpriseNameErr: '正確な企業名を入力してください',
        idNumberForVerifyErr: 'フォーマットが正確でありません。正確な身分証を入力してください',
        signerErr: '契約者に誤りがあります',
        noSignerErr: '少なくとも署名者1名を追加',
        lackAttachmentNameErr: '付属書類名を入力してください',
        repeatRecipientsErr: '順番に署名しない場合、重複して契約者を追加することはできません',
        innerContact: '内部担当者',
        outerContact: '外部担当者',
        search: '検索',
        accountSelected: 'すでにアカウント選択済み',
        groupNameAll: '全部',
        unclassified: '未分類',
        fileLessThan: '{num}M以下のファイルをアップロードしてください',
        beExcel: 'Excelファイルをアップロードしてください',
        usePdf: 'アップロードする際PDFファイルもしくは画像を使用してください',
        usePdfFile: 'アップロードの際はPDFファイルをご使用ください',
        fileNameMoreThan: 'ファイル名の長さが{num}を超えると、自動で切り取ります',
        needAddSender: '本企業/本人が署名者に設定されていないので、契約書が送信されたら、あなたは契約書に署名しません。署名者として追加しますか？',
        addSender: '署名者として追加',
        tip: '注意',
        cancel: 'キャンセル',
    },
    addReceiver: {
        English: '英語',
        Japanese: '日本語',
        Chinese: '中国語',
        Arabic: 'アラビア語',
        setNoticelang: '署名通知の言語設定',
        limitFaceConfigTip: '契約単価が低いため、この機能は利用できません。ベストサインにご連絡ください',
        individual: '契約個人',
        enterprise: '契約企業',
        addInstructions: '契約注意事項の追加',
        instructionsContent: '提出した資料はお客様が契約履行状態を追跡する助けを行い、業務の執行が正常かどうか判断します。設定後、署名者は必ず要求に基づき提出しなければなりません',
        addContractingInfo: '契約主体の資料を提出',
        contractingInfoContent: '提出した資料はお客様が契約者の主体資質を確認する助けを行い、業務の展開を開始/継続するかの判断をします。契約者がすでに同様の資料を提出している場合、再提出しなくてもかまいません',
        payer: '支払い者',
        handWriting: '手書き筆跡の識別のオン',
        realName: '担当者は実名が必要です',
        sameTip: '注意：契約者の企業名が完全一致している状態でのみ署名が可能です',
        proxy: '相手側受付での受け取り',
        aboradTip: '注意：当該契約者は域外の人であり、実名認証にはリスクがあります。まず先に当該者の身分を確認してください。',

        busRole: '業務の役割',
        busRoleTip: '契約者を識別する助けとなり、管理に便宜を図ります',
        busRolePlaceholder: 'もしも従業員/販売業者であれば',
        handWritingTip: 'この使用者は署名する際はっきりと識別可能な氏名を手書きしてもらうことで署名を完了できます',
        instructions: '契約注意事項の追加　|　（255字以内）',
        contractingParty: '契約主体資料',
        signerPay: '本契約は当該署名者が支払います',
        afterReadingTitle: '閲読完了後再署名する',
        afterReading: '署名者は必ず閲読し、契約書内容を理解したうえで操作を続けることができます',
        handWritingTips: 'この使用者の手書きの氏名と発信者指定もしくは実名情報の中の氏名と比較し、一致した段階で署名が完了します',
        SsTitle: '捺印及び署名',
        SsTip: '企業の印章を使用して署名する際、同時に追加する個人サインで署名を完了する必要があります。署名前に個人実名認証を完了する必要があります',
        signature: '署名',
        stamp: '捺印',
        Ss: '捺印及び署名',
        mutexError: '「{msg}」を設定済みです。先に「{msg}」の設定を削除した後再選択してください',
        handWriteNotAllowed: '手書き署名は許されていません',
        forceHandWrite: '手書き署名が必須です',
        faceFirst: '顔認証が優先で、予備で認証コードの署名になります',
        faceVerify: '顔認証署名が必須です',
        attachmentRequired: '契約書付属資料の追加',
        newAttachmentRequired: '契約主体資料を提出',
        attachmentError: '契約付属資料名称は同じものにできません',
        receiver: '携帯電話/メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',
        receiverJa: 'メールアドレスで受信| （最多で5個をサポートし、セミコロンで区切ることができます）',
        orderSignLabel: '署名の順序',
        contactAddress: '担当者アドレス帳',
        signOrder: '順序で署名する',
        account: 'アカウント',
        accountPlaceholder: '携帯電話/メールアドレス（必須項目）',
        accountPlaceholderJa: 'メールアドレス（必須項目）',
        accountReceptionCollection: '受付での受け取り',
        accountReceptionCollectionTip1: '相手方の具体的なアカウントを知らないか相手方がアカウントを持っていません',
        accountReceptionCollectionTip2: '受付での受け取りを選択してください',
        signSubjectPerson: '契約主体：個人',
        nameTips: '氏名（オプション、契約身分の確認で用います）',
        requiredNameTips: '氏名（必須項目、契約身分の確認で用います）',
        entOperatorNameTips: '氏名（オプション）',
        needAuth: '実名が必要です',
        operatorNeedAuth: '担当者は実名が必要です',
        signSubjectEnt: '契約主体：企業',
        entNameTips: '企業名（必須項目、契約身分の確認で用います）',
        operator: '担当者',
        sign: '署名',
        more: 'もっと',
        faceFirstTips: '署名する際システムは初期設定で顔認証による検証を採用しています。顔認証が通らない回数が1日の上限を超えた場合自動で認証コードによる検証に切り替わります',
        mustFace: '顔認証署名が必須です',
        mustHandWrite: '手書き署名が必須です',
        fillIDNumber: '身分証明書番号',
        fillNoticeCall: '通知携帯電話',
        fillNoticeCallTips: '通知携帯電話を記入してください',
        addNotice: 'メッセージの追加',
        attachTips: '契約書付属資料の追加',
        faceSign: '顔認証署名が必須です',
        faceSignTips: 'この使用者が署名を完了するには、顔認証する必要があります（顔認証署名は当面の間、中国大陸の住民のみ利用可能です）',
        handWriteNotAllowedTips: 'この使用者はすでに設定しているサインもしくは初期設定の字体サインを選択して署名を完了する必要があります',
        handWriteTips: 'この使用者は手書きサインで署名を完了する必要があります',
        idNumberTips: '契約身分の確認で用います',
        verifyBefore: '文書を確認する前に身分を認証',
        verify: '身分の認証',
        verifyTips: '最多20文字',
        verifyTips2: 'お客様はこの認証情報をこの使用者に提供しなければなりません',
        sendToThirdPlatform: '第三者プラットフォームに送信',
        platFormName: 'プラットフォーム名',
        fillThirdPlatFormName: 'プラットフォーム名を入力してください',
        attach: '資料',
        attachName: '資材名',
        exampleID: '例：身分証写真',
        attachInfo: '備考',
        attachInfoTips: '例：本人の身分証写真をアップロードしてください',
        addAttachRequire: '資料の追加',
        addSignEnt: '契約企業の追加',
        addSignPerson: '契約個人の追加',
        selectContact: '担当者の選択',
        save: '保存',
        searchVerify: '認証の確認',
        fillImageContentTips: '画像の内容を入力してください',
        ok: '確定',
        findContact: '契約書の中から下記の契約者を探します',
        signer: '契約者',
        signerTips: 'ヒント：契約者の選択後、プラットフォームはサイン及び捺印の位置決めを補助します。',
        add: '追加',
        notAdd: '追加なし',
        cc: '副本発信',
        notNeedAuth: '実名は必要ありません',
        operatorNotNeedAuth: '担当者の実名は必要ありません',
        extracting: '抽出中',
        autoFill: '署名者の自動記入',
        failExtracting: '契約者が抽出できません',
        idNumberForVerifyErr: '正確な身分証を入力してください',
        noAccountErr: 'アカウントは空欄に出来ません',
        noUserNameErr: '氏名は空欄に出来ません',
        noIDNumberErr: '身分証番号は空欄にできません',
        noEntNameErr: '企業名は空欄にできません',
        accountFormatErr: '正しいメールアドレスを入力してください',
        enterpriseNameErr: '正確な会社名を入力してください',
        userNameFormatErr: '正確な氏名を入力してください',
        riskCues: 'リスクに関して',
        riskCuesMsg: 'もしも契約者が実名署名をせず、文書において紛糾が発生した場合、署名者の身分を証明する証拠を自分で用意する必要があります。リスクを避ける必要がある場合、「実名が必要です」を選択してください。',
        confirmBtnText: '「実名が必要です」を選択',
        cancelBtnText: '「実名は必要ありません」を選択',
        attachLengthErr: 'お客様は1人の署名者に対し、最大50件の添付ファイル要求しか追加できません',
        collapse: '折りたたむ',
        expand: '展開する',
        delete: '削除',
        saySomething: 'なにか話しましょう',
        addImage: 'ファイルの追加',
        addImageTips: '（Word/PDF及び画像をサポート、ファイル3つ以下とします）',
        give: '先',
        fileMax: 'アップロード数量が上限を超えています',
        signerLimit: '現在のバージョンは{limit}個を超える相対的署名/副本発信先をサポートしていません。',
        showExamle: 'サンプル画像を確認',
        downloadExamle: 'サンプルファイルをダウンロード',
    },
    addReceiverGuide: {
        notRemind: '次回はお知らせしないでください',
        sign: '自署',
        entSign: '企業の自署',
        stamp: '捺印',
        stampSign: '捺印及び署名',
        requestSeal: '業務照合印',
        'guideTitle': '新しい署名者を追加する方法',
        'receiverType': '署名者が契約に参加する方法を選択する必要があります (6 つのうちの 1 つを選択してください)：',
        'asEntSign': '企業を代表してサインオンします：',
        'sealSub': '署名者は、契約書に公印または契約書用特別印鑑等を押印する必要があります',
        'signatureSub': '法人または役員が、企業に代わって契約に署名します。企業は、署名者が契約を閲覧できないように契約を譲渡する権利を有します',
        'vipOnly': 'プレミアムバージョンが利用可能',
        'stampSub': '署名者は、印鑑を押すだけでなく、企業を代表して署名する必要があります',
        'confirmSeal': '企業を代表して業務用チェックスタンプを使用する',
        'confirmSealSub': '財務諸表や確認書などの書類は、最初に確認されてから押印されます',
        'asPersonSign': '個人に代わって署名するには:',
        'asPersonSignTip': 'ビジネスを代表するものではなく、個人のみを代表して署名されています',
        'asPersonSignDesc': 'ローン契約、参入および退出などの署名者の私的な契約',
        'scanSign': 'コードをスキャンして署名する',
        'scanSignDesc': '契約書を発行する際に署名者を書く必要はありません. 契約書が発行された後、誰でもコードをスキャンするか、検査ページのリンクをクリックして署名することができます. 物流書類の受領シナリオに適用できます',
        'selectSignTypeTip': '最初に署名者が契約に参加する方法を選択してください',
    },
    linkContract: {
        title: '契約書の関連付け',
        connectMore: '関連するその他の契約書',
        placeholder: '契約書番号を入力してください',
        revoke: '署名抹消済み',
        overdue: '署名期限切れ',
        approvalNotPassed: '審査却下',
        reject: '署名拒否済み',
        signing: '署名中',
        complete: '完了済み',
        approvaling: '審査中',
        disconnect: '関連付けの解除',
        disconnectSuccess: '関連付けの解除完了',
        connectLimit: '関連付け契約書数の上限は100部です',
    },
    field: {
        fieldTip: {
            title: '署名位置の欠落',
            error: '下記の契約書指定署名位置がありません（{type}）',
            add: 'フィールドの追加',
            continue: '送信を継続',
        },
        accountCharge: {
            notice: 'この契約書は参加アカウント数に基づき課金します',
            able: '正常に送信できます',
            unable: '使用アカウント数が不足しています。ベストサインのカスタマーサービスに連絡してください',
            notify: 'この契約書はすべての契約書先に英語で通知を送信します',
            noNotify: {
                1: 'この契約書は契約の関連通知を発信しません',
                2: '（署名・審査・副本発信・契約有効期限などの通知SMS及びメールを含む）',
            },
        },
        ridingStamp: '割り印',
        watermark: 'すかし',
        senderSignature: '捺印者署名',
        optional: 'オプション',
        clickDecoration: '契約書装飾をタップ',
        decoration: '契約書装飾',
        sysError: 'システムビジーです。時間をおいてから試してください',
        partedMarkedError: '「捺印とサイン」の契約者を指定しました。必ず同時に捺印とサインを指定してください',
        fieldTitle: '全部で{length}部の契約書に署名位置指定が必要です',
        send: '発信',
        contractDispatchApply: '契約書の送信申請',
        contractNeedYouSign: 'このファイルはお客様の署名が必要です',
        ifSignRightNow: 'すぐに署名しますか',
        signRightNow: 'すぐに署名します',
        signLater: '後で署名します',
        signaturePositionErr: '各署名者に署名位置を指定してください',
        sendSucceed: '送信完了',
        confirm: '確定',
        cancel: 'キャンセル',
        qrCodeTips: '署名後読み取り、すぐに署名詳細・署名の有効性及びこの契約書が改ざんされているかどうかの検証を確認できます',
        pagesField: '第{currentPage}ページ、計{totalPages}}ページ',
        suitableWidth: '幅の調整',
        signCheck: '署名の確認',
        locateSignaturePosition: '署名位置指定',
        locateTips: '素早く署名位置を指定することができます。現在、各署名者の最初の署名位置の指定のみをサポートしています',
        step1: '手順1',
        selectSigner: '契約者を選択',
        step2: '手順2',
        dragSignaturePosition: '署名位置をドラッグ',
        signingField: '署名フィールド',
        docTitle: 'ファイル',
        totalPages: 'ページ数：{totalPages}ページ',
        receiver: '受信先',
        delete: '削除',
        deductPublicNotice: '個人向け契約書の使用可能部数が不足している際は企業向け契約書から差し引きます',
        unlimitedNotice: 'この契約書の費用は使い放題です',
        charge: '課金',
        units: '{num}部',
        contractToPrivate: '個人向け契約書',
        contractToPublic: '企業向け契約書',
        costTips: {
            1: '企業向け契約書：署名者（送信者を含まず）の中に企業アカウントがある契約書',
            2: '個人向け契約書：署名者（送信者を含まず）の中に企業アカウントがない契約書',
            3: '課金部数はファイル数に基づき計算します',
            4: '課金部数 = ファイル部数 X ユーザーグループ（列）の一括インポート',
        },
        costInfo: '契約の送信に成功すると、直ちに費用が控除され、契約の完了、期限超過、撤回、または署名拒否は返金されません。',
        toCharge: 'リチャージする',
        contractNeedCharge: {
            1: '使用可能な契約部数が不足しているため、送信できません',
            2: '使用可能な契約部数が不足しています。管理者主任にリチャージするよう連絡してください',
        },
        chooseApprover: '審査者の選択：',
        nextStep: '次へ',
        submitApproval: '審査提出',
        autoSendAfterApproval: '*審査に合格後、契約書を自動で送信します',
        chooseApprovalFlow: '審査フロー1つを選択してください',
        completeApprovalFlow: 'お客様の提出した審査フローに不備があります。追加してから再度提出してください。',
        viewPrivateLetter: 'メッセージの確認',
        addPrivateLetter: 'メッセージの追加',
        append: '追加',
        privateLetter: 'メッセージ',
        signNeedKnow: '契約注意事項',
        maximum5M: '5M以下のファイルをアップロードしてください',
        uploadServerFailure: 'サーバーへのアップロードに失敗しました',
        uploadFailure: 'アップロードエラーです',
        pager: 'ページ番号',
        seal: '捺印',
        signature: '署名',
        signDate: '署名日時',
        text: 'テキスト',
        date: '日付',
        qrCode: '二次元コード',
        number: '数字',
        dynamicTable: '動的フォーマット',
        terms: '契約条項',
        checkBox: 'チェックボックス',
        radioBox: 'ラジオボタン',
        image: '画像',
    },
    addressBook: {
        innerMember: {
            title: '企業内部メンバー',
            tips: '送信者が素早く内部の担当者を探せるように企業メンバー情報を調整します',
            operation: '管理コンソールに入る',
        },
        outerContacts: {
            title: '外部企業担当者',
            tips: '業務の展開がうまくいくよう、お客様の協力パートナーを招待し、事前に実名登録をしてください',
            operation: '協力パートナーを招待する',
        },
        myContacts: {
            title: '私の担当者',
            tips: '署名者の情報が正確かつ間違いがないように、担当者を修正します',
            operation: 'ユーザーセンターに入る',
        },
        selected: 'すでにアカウント選択済み',
        search: '検索',
        loadMore: 'もっと読む',
        end: '全ロード完了',
    },
    dataBoxInvite: {
        title: '協力パートナーを招待する',
        step1: 'お客様の協力パートナーとのリンクを共有し、事前に企業を構築',
        step2: 'リンク/二次元コードの授権後の協力パートナーがアドレス帳に表示されます',
        step3: '「ファイル＋」の中で協力パートナーをさらに管理します',
        imgName: '採集した二次元コードを共有',
        saveQrcode: '二次元コードをローカルに保存',
        copy: 'コピー',
        copySuccess: 'コピー完了',
        copyFailed: 'コピー失敗',
    },
    shareView: {
        title: 'レビューのために転送',
        account: '電話番号/メールアドレス',
        role: 'レビュー担当者の役割',
        note: '備考',
        link: 'リンクを：',
        signerMessage: '署名者のメッセージ',
        rolePlaceholder: '例：会社の法務、部門のリーダーなど',
        notePlaceholder: 'レビュー担当者へのメッセージ、200文字以内',
        generateLink: 'リンクを生成',
        regenerateLink: 'リンクを再生成',
        saveQrcode: 'ミニプログラムコードを保存',
        inputAccount: '電話番号またはメールアドレスを入力してください',
        inputCorrectAccount: '正しい携帯電話番号またはメールアドレスを入力してください',
        accountInputTip: 'リンクが正常に開かれるようにするために、契約審査者の情報を正確に入力してください',
        shareLinkTip1: 'ミニプログラムコードを保存するか',
        shareLinkTip: 'リンクをコピーして、レビュー担当者と共有してください',
        linkTip1: '契約の本文は機密情報ですので、必要のない限り外部に漏らさないでください',
        linkTip2: 'リンクの有効期限は2日間です；リンクを再生成した後、以前のリンクは自動的に無効になります',
    },
    recoverSpecialSeal: {
        title: '印章が使用できません',
        description1: '送信者は契約時にこの印章を使用して契約書に署名するよう要求していますが、御社はこの印章をすでに削除しています。確実に署名が行えるよう、管理者にこの印章の復元を要求してください',
        description2: 'もし、この印章が本当に今後の使用に適さない場合、送信者に連絡して印章のデザインの修正を要求した後、再度契約書に署名するようにしてください。',
        postRecover: '印章の復元申請',
        note: 'タップ後管理者が印章復元申請のSMS/メールを受け取ると同時に、同時に印章の管理ページで申請を見ることができます。',
        requestSend: '復元申請の提出完了',
    },
    paperSign: {
        title: '紙媒体の署名を使用',
        stepText: ['次へ', '紙媒体署名確認', '確定'],
        needUploadFile: '先にスキャンファイルをアップロードしてください',
        uploadError: 'アップロードエラーです',
        cancel: 'キャンセル',
        downloadPaperFile: '紙媒体署名ファイルの取得',
        step0: {
            title: '先に契約書をダウンロード印刷し、物理的な捺印をした上で、送信者へ郵送する必要があります',
            address: '郵送住所：',
            contactName: '受取者氏名：',
            contactPhone: '受取者連絡方法：',
            defaultValue: 'オフライン方法で送信者から受け取ってください',
        },
        step1: {
            title0: '手順1：ダウンロード及び紙媒体契約書の印刷',
            title0Desc: ['ダウンロードし印刷した契約書にはすでに署名している電子印章の図案が含まれていなければなりません。紙の署名書類を入手してください。', '获取纸质签文件。'],
            title1: '手順2：印章の捺印',
            title1Desc: '紙媒体契約書に契約書に有効な会社の印章を捺印してください。',
            title2: ['手順3：', 'スキャンデータのアップロード，', '署名ページに戻り、署名ボタンをタップして、紙媒体署名を完了してください'],
            title2Desc: ['紙媒体契約書をスキャンしデータ（PDFファイル）に変換後アップロードしてください。', '電子契約書内にはお客様の印章図案は表示されませんが、この操作を行ったことは記録されます。'],
        },
        step2: {
            title: ['紙媒体契約書のスキャンデータ（PDFファイル）をアップロードしてください', '紙媒体契約書をダウンロードし署名した後、再度確定ボタンをタップして、紙媒体契約書のプロセスを終了します。'],
            uploadFile: 'スキャンファイルのアップロード',
            getCodeVerify: '契約署名検証の取得',
            isUploading: 'アップロード中...',
        },
    },
    allowPaperSignDialog: {
        title: '紙契約書への署名を許可する',
        content: 'この契約書は{senderName}が{receiverName}へ送信したもので、紙形式での署名が許可されています。',
        tip: '契約書を印刷して、会社の責任者が紙の契約書に押印することも選択できます。',
        icon: '紙契約書へ署名に変更 >>',
        goSign: '電子署名する',
        cancel: 'キャンセル',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章の提示',
            tip: '現在の印章画像がお客様の企業身分と適合していないことが検出されました。現在の印章画像識別の結果は：',
            tip1: '検出された企業印章と企業名：',
            tip2: 'このまま現在の印章画像を使用しますか？',
            tip3: '発信者の要求に基づき、お客様が使用する企業名は：',
            tip4: 'の印章です',
            tip5: '印章が要件に適合していることを確認してください。さもなければ契約書の有効性に影響を及ぼす恐れがあります。',
            tip6: '適合していません。印章が発信者の要件に適合しているかどうか確認してください。',
            guide: '正確な印章をアップロードするには >>',
            next: '続けて使用',
            tip7: 'また、あなたの印鑑名は規範に合わず、「{keyWord}」という文字が付いています。',
            tip8: '印鑑名が規範に合わないことが検出されました。「{keyWord}」という文字が付いていますが、引き続き使用しますか？',
        },
        exampleSeal: {
            title: '印章図案のアップロード方法',
            way1: ['方法1：', '1、白い紙の上で実物の印章図案を捺印します。', '2、撮影し、画像をプラットフォームにアップロードします'],
            way2: ['方法2：', '図のように直接プラットフォームの電子印章機能で生成します：'],
            errorWay: ['間違った方法：', '印章を手で持つ', '関係ない画像', '営業許可証'],
        },
        confirm: '確認',
        cancel: 'キャンセル',
    },
    addSealDialog: {
        title: '印章画像を追加',
        dec1: 'ローカルフォルダから印章画像を1枚選択してください（形式：JPG、JPEG、PNG等）。システムがこの印章画像を現在の契約書に合成します。',
        dec2: 'その後、「署名」ボタンをクリックして署名検証を通過すると、押印が完了します。',
        updateNewSeal: '新しい印章をアップロード',
    },
};
