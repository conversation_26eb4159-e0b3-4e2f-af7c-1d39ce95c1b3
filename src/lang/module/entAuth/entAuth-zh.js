export default {
    submitBaiscInfo: '提交基本信息',
    firstStep: '第一步',
    secondStep: '第二步',
    thirdStep: '第三步',
    nextStep: '下一步',
    back: '返回',
    submit: '提交',
    confirm: '确定',
    affirm: '确认',
    cancel: '取消',
    tip: '提示',
    account: '账号',
    view: '查看',
    iSee: '我知道了',
    submitAttorney: '提交授权书',
    plsDownloadSSQAttorneyTip: '请下载《上上签企业服务授权书》，加盖企业公章后拍照上传。',
    ssqAttorney: '上上签企业服务授权书',
    downloadAttorney: '请点击下载授权书',
    imgSupportType: '支持png、jpeg、jpg、bmp格式且大小不超过10M。',
    imgNoWatermarkTip: '仅支持无水印或”仅用于上上签实名认证“水印字样的照片',
    confirmSubmit: '确认提交',
    backToPreviousPage: '返回上一页',
    uploadImgBeforeSubmit: '请上传照片后再提交',
    phoneNumBuyTip: '请确保该手机号是在运营商处实名购买，否则将无法通过上上签实名验证。',
    operatorPhoneVerify: '经办人手机号校验',
    operatorName: '经办人姓名',
    operatorIdNum: '经办人身份证号',
    modifyOperator: '修改经办人',
    changeIdCard: '更换证件',
    operatorPassPhoneVerifyTip: ' 经办人在 {time} 已通过手机号校验，校验结果允许被复用。',
    operatorPhoneNum: '经办人手机号',
    verifyCode: '验证码',
    tryFaceVerify: '试试刷脸校验',
    afterPhoneVerifyTip: '手机号校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',
    operatorFaceVerify: '经办人刷脸校验',
    operatorPassFaceVerifyTip: ' 经办人在 {time} 已通过刷脸校验，校验结果允许被复用。',
    alipayFaceVerify: '支付宝扫一扫刷脸认证',
    tryPhoneVerify: '试试手机号校验',
    scanFaceNow: '立即刷脸',
    afterFaceVerifyTip: '刷脸校验通过后系统将为您自动生成授权书，下载后加盖公章再上传即可',
    plsEnterRightPhoneNum: '请先填写正确的手机号',
    plsGetVerifyCodeFirst: '请先获取验证码',
    agreeSsqProtectMethod: '我同意上上签对我提交的个人身份信息的保护方法',
    ssqHowToProtectInfo: '《上上签如何保护您的个人信息》',
    noEntNameTip: '由于贵公司没有企业名称，不支持授权书认证',
    legalName: '法定代表人姓名',
    legalIdCardNum: '法定代表人身份证号',
    uploadIdCard: '上传身份证',
    legalPassFaceVerifyTip: '法定代表人在 {time} 已通过刷脸认证，认证结果允许被复用。',
    plsEnterLegalIdCardNum: '请先输入法定代表人身份证号',
    plsUploadLegalIdCardImg: '请先上传法定代表人身份证',
    pageExpiredTip: '当前页面已过期，请重新刷新页面',
    faceFunctionNotEnabled: '刷脸服务暂时无法使用，请稍后再试',
    plsSubmitLegalIdCard: '请提交法定代表人的身份证',
    legalPassPhoneVerifyTip: '法定代表人在 {time} 已通过手机号认证，认证结果允许被复用。',
    legalPhoneNum: '法定代表人手机号',
    noPublicAccount: '没有对公账户？',
    useSpecialMethod: '我是没有公户的分公司，使用特殊通道',
    ssqRemitMoneyTip: '上上签将向下方对公账户汇入一笔钱（少于0.99元），您回填打款金额即可通过企业认证。',
    accountName: '户名',
    bank: '银行',
    accountBank: '开户行',
    accountBankName: '开户行名称',
    bankAccount: '银行账户',
    plsInputBankNameTip: '请输入银行名称，无需写分行或支行，如“中国银行”',
    locationOfBank: '开户行所在地',
    plsSelect: '请选择',
    province: '省',
    city: '市',
    cityCounty: '市/县',
    nameOfBank: '开户支行名称',
    plsInputZhiBankName: '填写开户支行名称，如“杭州银行文创支行”',
    canNotFindBank: '找不到对应银行？',
    clickChangeReversePay: '点击页面下方“切换打款方式”，进入反向打款。',
    other: '其他',
    otherZhiBank: '开户支行名称(如支行名称选择\'其他\'请填写)',
    bankAccount1: '银行账号',
    plsInputBankAccount: '请输入银行账号',
    remitNotSuccess: '汇款不成功？',
    switchPayMethod: '切换打款方式',
    whySubmitBankInfo: '为什么要提交银行卡信息',
    submitBankInfoExplan: '银行卡信息认证是企业实名认证的一个验证环节，上上签会向该银行卡汇入一笔验证资金，不会从您的账户扣款。',
    plsSelectBank: '请从下拉列表中选择银行',
    plsSelectArea: '请从下拉列表中选择省,市/县',
    plsInputNameOfAccountBank: '请输入开户行名称',
    plsInputCorrectBankInfo: '请填写正确的对公银行信息',
    plsInputFullBankName: '请填写开户行全称（包含支行名称）',
    area: '地区',
    contactCustomerService: '联系客服',
    beiJing: '北京市',
    dongCheng: '东城区',
    fillJointBankNum: '该账户需要填写开户银行联行号才能汇款',
    bankType: {
        type1: '银行',
        type2: '信用社联合社',
        type3: '信用合作社',
        type4: '农联社',
    },
    accountBankArea: '开户行所在地',
    changeRemitMethod: '换种汇款方式',
    canNotRemitAuth: '无法向您的企业对公账户发起打款认证，请选择其他认证方式',
    bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',
    faceVerify: '刷脸校验',
    havePublicAccount: '我有对公账户',
    ensureInfoSafeTip: '为保障企业信息安全、不被他人盗用，请先完成法人刷脸，然后再作打款认证',
    submitEntBankInfo: '提交企业开户信息',
    fillBackAmout: '填写回填金额',
    legalPerson: '法人',
    operatorMange: '经办人',
    unionNum: '联行号',
    getUnionNumTip: '请向贵公司/单位的财务沟通获取联行号，或根据银行支行信息',
    uinonNumSearchOnline: '联行号在线查询',
    remitProcessMap: {
        submit: '打款申请已提交',
        accept: '打款已受理',
        success: '打款已成功',
        receiveWait: '支付请求已受理,等待银行返回结果，请耐心等待',
        successAttention: '打款已成功，请仔细核对账户收支明细。请注意：',
        remarkTip: '汇款备注为：款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',
        thirdChannel: '上上签通过第三方支付通道汇款,汇款通道为:',
        remitNotSsq: '上上签通过第三方支付通道汇款，汇款方户名并非上上签',
        remitPartySsq: '汇款方为“杭州尚尚签网络科技有限公司”',
    },
    close: '关闭',
    name: '姓名',
    idCardNum: '身份证号',
    reversePayMap: {
        remitTip1: '向上上签银行对公账户汇入0.01元，再勾选以下选项并点击“确定”，即可通过企业认证。',
        iAgree: '我知情并同意：',
        remitAuthUse: '向上上签指定的账户汇入的0.01元，如果认证通过，将用于购买1份对公合同；如果认证失败，也不能要求上上签退款。',
        authFailure: '不满足以下要求会导致认证失败：',
        authFailureReason1: '（1）汇款方为当前法人姓名：',
        authFailureReason2: '（2）汇款方必须使用自己名下的账户进行汇款',
        authFailureReason3: '（1）汇款方为当前企业名称：',
        authFailureReason4: '（2）汇款方必须使用对公账户进行汇款',
        plsInputBankName: '请输入银行账户名称',
        authFailureReason5: '（3）汇款方向接收方汇出的单笔金额为0.01元人民币，不能大于0.01元或使用其他币种',
        authFailureReason6: '（4）汇款日期，请确保在 {date} 后汇入',
        authFailureReason7: '（5）接收方银行账户信息如下，不能汇入其他账户中：',
        authFailureReason8: '（6）汇款方向接收方汇款时，必须且仅备注该验证码：',
        remitDelay: '汇款到账可能有延迟，请耐心等待。',
        failureReason: '认证实名失败的可能原因：',
        wait30min: '因银行系统原因，您可在汇款成功30分钟后查询结果',
        queryProgress: '查询进度',
        inputRemitBankName: '请填入付款方银行户名',
        queryFailureTip: '查询失败，请稍后重试...',
        remitAuthSuccess: '已通过打款认证，汇款资金已用于购买1份对公合同。',
    },
    hourMinSec: '{hour}时{min}分{sec}秒',
    minSec: '{min}分{sec}秒',
    sec: '{sec}秒',
    ssqRemitTip: '上上签已向贵账户提交一笔1元以下的汇款，1-2个工作日内到账，请于到账后在此确认到账金额。',
    inputRightRemitAmout: '您需要查询账户的收支明细，正确输入这笔金额才能通过认证。',
    notGetRemit: '如果您在账户上没有查到汇款金额，点此',
    queryRemitProgress: '查一查打款进度',
    inputWrongAccount: '账号填错了？',
    remitNote: '汇款备注为：',
    ssqApplyCaTip: '款项用于上上签平台企业实名认证和申请CA证书，请在上上签页面回填金额',
    remitAmout: '汇款金额',
    yuan: '元',
    receiveAmout: '到账金额为0.01-0.99元之间',
    maxRemitTimeTip: '最多只能打款四次哦！是否确定要重新提交填写账号？',
    remitFailure: '打款未成功，即将返回企业打款页面，需要重新申请打款',
    plsInputRemitAmout: '请输入0.01-0.99元之间的金额',
    reSubmitBankInfo: '您需要重新提交银行卡信息',
    amoutError: '金额错误',
    reSubmit: '重新提交',
    amoutInvalid: '金额失效',
    authReject: '实名材料已被驳回，请重新提交',
    authCertificate: '授权书认证',
    entPayAuth: '企业打款认证',
    legalPhoneAuth: '法定代表人手机号认证',
    legalFaceAuth: '法定代表人刷脸认证',
    sender: '发件人',
    requireYouThrough: '{name}要求您通过{type}',
    selectOneAuthMethod: '请选择以下任意一种实名方式：',
    entCertificate: '企业证件',
    personCertificate: '个人证件',
    iAmLegal: '我是法定代表人',
    iAmNotLegal: '我不是法定代表人，我是企业经办人',
    receiveSsqPhone: '我接受上上签电话回访',
    plsAgreeSsqPhone: '请先同意接受上上签电话回访',
    submitInfoError: '您提交的个人证件的姓名或身份证号有误，请重新核实。',
    submitIndividualEntAuth: '您正作为个体工商户提交企业认证，您的营业执照上无企业名称',
    submitIndividualCorrectIdCard: '请如实提交法定代表人个人身份证',
    entPersonInfoError: '您提交的企业证件信息有误/个人证件信息有误，请核实后重新提交。',
    noEntName: '没有企业名称',
    businessLicenseBlank: '营业执照的企业名称处为：空白',
    addressName: '/地址/法定代表人姓名',
    plsClick: '请点击',
    haveEntName: '有企业名称，要求客服人工审核，',
    checkFollowInfoTip: '核对以下信息并点击“确定”，您可以继续提交其他认证材料。企业信息转由上上签人工核对，如果信息不正确将驳回您提交的全部资料。信息核对需要约1个工作日。',
    entName: '企业名称',
    unifySocialCode: '统一社会信用代码',
    uploadColorImgTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。企业信息将用于申请数字证书。',
    businessLicense: '营业执照',
    uploadBusinessLicenseTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照',
    imgLimitTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或“仅用于上上签实名认证”水印字样的图片',
    autoRecognizedTip: '以下信息自动识别，需仔细核对，如识别有误，请修正。',
    iNoEntName: '我没有企业名称',
    clickUseSpecialMethod: '点击使用特殊通道',
    socialCodeBuisnessNum: '统一社会信用代码/工商注册号',
    plsSubmitCorrectLegalName: '请提交正确的法定代表人姓名',
    plsSubmitBusinessLicense: '请先提交营业执照',
    noEntUploadBusinessTip: '请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。企业信息将用于申请数字证书。',
    imgRequireTip: '照片仅限jpeg、jpg、png格式且大小不超过10M。仅支持无水印或者“仅用于上上签实名认证”水印字样的照片。',
    noEntNameUse: '没有企业名称的个体工商户须使用',
    legalPlusCode: '法定代表人姓名加上统一社会信用',
    codeAsEntName: '代码作为企业名称',
    ssq: '上上签',
    entAuth: '企业认证',
    eleContractServiceBy: '电子签约服务由',
    spericalProvide: '提供',
    redirectWait: '正在跳转，请稍后',
    businessLicenseImg: '营业执照照片',
    idCardNational: '身份证国徽面',
    idCardFace: '身份证人像面',
    dueToSignRequire: '因合同签订需要',
    authorizeSomeone: '我授权{name}获得以下信息：',
    authoizedInfo: '我的账号基本信息(账号和名称)和企业基本信息(企业名称、统一社会信用代码或注册号、法定代表人姓名)',
    iSubmitInfoToSsq: '我在上上签平台提交的',
    authMaterials: '认证材料',
    sureInfo: '确定无误',
    modifyInfo: '修改信息',
    additionalInfo: '补充信息',
    ssqNotifyMethod: '我在上上签平台用作通知方式的',
    phoneNum: '手机号',
    email: '邮箱',
    submitInfoPlsVerify: '已为您提交企业基本信息，请核实',
    operatorIdCardFaceImg: '经办人身份证人像面',
    operatorIdCardNational: '经办人身份证国徽面',
    legalIdCardFaceImg: '法定代表人身份证人像面',
    legalIdCardNational: '法定代表人身份证国徽面',
    plsAgreeAuthorized: '请先同意授权',
    finishConfirmInfo: '您已完成信息确认，请继续企业认证流程。',
    entOpenMultipleBusiniss: '企业开通了多业务线，操作无法继续',
    contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理',
    signContract: '签署合同',
    faceVerifyFailure: '刷脸认证失败',
    reFaceVerifyAuth: '重新刷脸认证',
    threeTimesFailure: '非常抱歉，您今天已连续3次刷脸比对失败，请选择其他认证方式',
    faceVerifySucess: '刷脸认证成功',
    chooseOtherAuthMethod: '选择其他认证方式',
    plsContinueOnPc: '请在电脑网页端继续操作',
    accountAppealSuccess: '账号申诉成功',
    faceCompareSuccess: '刷脸对比成功',
    plsUseWechatScan: '请使用微信浏览器扫描二维码',
    wechatCannotScanFace: '因微信策略变更，当前无法刷脸',
    plsUploadClearIdCardImg: '请上传清晰的身份证照片，系统将自动识别证件信息。照片仅限jpeg、jpg、png格式且大小不超过10M。',
    uploadImgMap: {
        tip1: '请上传清晰的身份证照片，',
        tip2: '系统将自动识别证件信息。',
        tip3: '照片仅限jpeg、jpg、png格式且大小不超过10M。',
        tip4: '仅支持无水印或“仅用于上上签实名认证”水印字样的图片',
    },
    plsSubmitIdCard: '请先提交身份证',
    noNameNoSupportEntRemit: '由于贵公司没有企业名称，不支持企业打款认证',
    checkMoreVerison: '查看更多版本',
    viewCertificate: '查看证书',
    continueAuthNewEnt: '继续认证新企业',
    continueBuyPackage: '继续购买套餐',
    needSubmitBasicInfo: '仅要求您提交企业基本信息',
    youFinishEntAuth: '您已完成企业认证',
    loginSsqWebToEntAuth: '登录上上签官网完成完整的企业认证流程，您可以在上上签平台获得更高权限',
    entAuthCertificate: '企业实名证书',
    youFinishEntAuthTip: '您已完成企业实名认证',
    backToHome: '返回首页',
    youNeedMoreOperate: '您还需{operate}完成以下操作：',
    goPc: '前往PC端',
    addEntMemberStepMap: {
        title: '添加企业成员步骤：',
        step1: '1、进入企业控制台',
        step2: '2、打开成员管理',
        step3: '3、点击添加新成员',
        step4: '4、输入账号、姓名，选择角色',
        step5: '5、点击保存，即可添加新成员',
    },
    addEntMember: '添加企业成员',
    addSealStepMap: {
        title: '添加印章步骤：',
        step1: '1、进入企业控制台',
        step2: '2、打开印章列表',
        step3: '3、点击新增印章',
        step4: '4、输入印章名称，上传印章图案或生产电子签章',
        step5: '5、点击保存，新增印章',
        step6: '6、点击添加持有人',
        step7: '7、选择企业成员',
        step8: '8、点击确定，即可添加印章持有人',
    },
    addSeal: '添加印章',
    waitApporve: '等待审核',
    submitAuthMaterial: '提交认证材料',
    authFinish: '认证完成',
    plsSubmitBeforeTip: '请在{date}前完成所有材料提交，否则基本信息将失效。',
    oneDayFinishApprove: '客服将在一个工作日内完成审核，请耐心等待',
    entAuthFinish: '企业已实名完成',
    baseInfoInvalid: '基本信息已失效，请重新提交',
    missParams: '缺少参数!',
    illegalLink: '非法链接',
    cookieNotEnabe: '无法读写cookie，是否开启了无痕／隐身模式或其他禁用cookie的操作',
    truthSubmitOperatorIdCard: '请如实提交经办人个人身份证',
    abandonAttorneyAuth: '放弃授权书认证，',
    modifyCertiInfo: '修改证件资料',
    modifyAttorneyInfo: '修改授权书资料',
    plsUploadBusinessLicense: '请上传营业执照！',
    plsUploadLegalCerti: '请上传法人证件！',
    plsUploadOperatorCerti: '请上传经办人证件！',
    legalIdCardSubmit: '法定代表人身份证提交',
    serviceAttorneryAuth: '服务授权书认证',
    accountAppealMap: {
        entApeal: '企业账号申诉',
        apealSuccess: '申诉完成',
        comName: '公司名称:',
        account: '账号:',
        verifyCode: '验证码:',
        ener6Digtal: '请填写6位数字',
        beMainManagerTip: '企业账号申请成功后，您将成为企业主管理员',
        mainAccount: '主管理员账号',
        continueSign: '继续签署',
        continueConfirm: '继续认证',
        plsEnterComName: '请先填写公司名称',
        plsEnterCorrentComName: '请填写正确的公司名称',
        sendSuccess: '发送成功！',
        plsEnterCorrectCode: '请填写正确的验证码',
    },
    faceInitLoading: '初始化刷脸标签，请稍后',
    wxFaceVersionTip: '刷脸需要微信版本7.0.12及以上，请先升级',
    wxIosFaceVersionTip: '刷脸需要ios版本10.3及以上，请先升级',
    wxAndroidVersionTip: '刷脸需要android版本5.0及以上，请先升级',
    faceInitFailure: '刷脸标签初始化失败: ',
    entAuthCertificateTip: '企业实名认证证书',
    idCardHandHeld: '手持身份证认证',
    faceAuth: '刷脸认证',
    noMainlandAuth: '非大陆认证',
    entAuthTip: '企业实名认证',
    signIntro: '签署引导',
    companySet: '企业设置',
};
