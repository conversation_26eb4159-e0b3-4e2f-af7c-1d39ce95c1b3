export default {
    submitBaiscInfo: 'تقديم المعلومات الأساسية',
    firstStep: 'الخطوة الأولى',
    secondStep: 'الخطوة الثانية',
    thirdStep: 'الخطوة الثالثة',
    nextStep: 'الخطوة التالية',
    back: 'عودة',
    submit: 'تقديم',
    confirm: 'تأكيد',
    affirm: 'تأكيد',
    cancel: 'إلغاء',
    tip: 'تنبيه',
    account: 'الحساب',
    view: 'عرض',
    iSee: 'فهمت',
    submitAttorney: 'تقديم تفويض',
    plsDownloadSSQAttorneyTip: 'يرجى تحميل "تفويض خدمات BestSign للمؤسسات"، ووضع ختم المؤسسة عليه ثم التقاط صورة وتحميلها.',
    ssqAttorney: 'تفويض خدمات BestSign للمؤسسات',
    downloadAttorney: 'يرجى النقر لتحميل التفويض',
    imgSupportType: 'يدعم تنسيقات png وjpeg وjpg وbmp وحجم لا يتجاوز 10M.',
    imgNoWatermarkTip: 'يدعم فقط الصور بدون علامة مائية أو بعلامة مائية "للتحقق من هوية BestSign فقط"',
    confirmSubmit: 'تأكيد التقديم',
    backToPreviousPage: 'عودة للصفحة السابقة',
    uploadImgBeforeSubmit: 'يرجى تحميل الصور قبل التقديم',
    phoneNumBuyTip: 'يرجى التأكد من أن رقم الهاتف هذا تم شراؤه بشكل رسمي من مشغل الاتصالات، وإلا لن يتمكن من اجتياز التحقق من هوية BestSign.',
    operatorPhoneVerify: 'التحقق من هاتف المسؤول',
    operatorName: 'اسم المسؤول',
    operatorIdNum: 'رقم هوية المسؤول',
    modifyOperator: 'تعديل المسؤول',
    changeIdCard: 'تغيير الهوية',
    operatorPassPhoneVerifyTip: ' اجتاز المسؤول التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',
    operatorPhoneNum: 'رقم هاتف المسؤول',
    verifyCode: 'رمز التحقق',
    tryFaceVerify: 'جرب التحقق بالوجه',
    afterPhoneVerifyTip: 'بعد اجتياز التحقق من الهاتف، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',
    operatorFaceVerify: 'التحقق من وجه المسؤول',
    operatorPassFaceVerifyTip: ' اجتاز المسؤول التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',
    alipayFaceVerify: 'مسح للتحقق من الوجه عبر Alipay',
    tryPhoneVerify: 'جرب التحقق من الهاتف',
    scanFaceNow: 'التحقق من الوجه الآن',
    afterFaceVerifyTip: 'بعد اجتياز التحقق من الوجه، سيقوم النظام تلقائياً بإنشاء تفويض لك، قم بتحميله ووضع ختم المؤسسة عليه ثم قم بتحميله',
    plsEnterRightPhoneNum: 'يرجى إدخال رقم هاتف صحيح أولاً',
    plsGetVerifyCodeFirst: 'يرجى الحصول على رمز التحقق أولاً',
    agreeSsqProtectMethod: 'أوافق على طريقة BestSign في حماية معلومات هويتي الشخصية',
    ssqHowToProtectInfo: '"كيف يحمي BestSign معلوماتك الشخصية"',
    noEntNameTip: 'نظراً لعدم وجود اسم مؤسسة لشركتك، لا يدعم التحقق بالتفويض',
    legalName: 'اسم الممثل القانوني',
    legalIdCardNum: 'رقم هوية الممثل القانوني',
    uploadIdCard: 'تحميل بطاقة الهوية',
    legalPassFaceVerifyTip: 'اجتاز الممثل القانوني التحقق من الوجه في {time}، يمكن إعادة استخدام نتيجة التحقق.',
    plsEnterLegalIdCardNum: 'يرجى إدخال رقم هوية الممثل القانوني أولاً',
    plsUploadLegalIdCardImg: 'يرجى تحميل بطاقة هوية الممثل القانوني أولاً',
    pageExpiredTip: 'انتهت صلاحية الصفحة الحالية، يرجى تحديث الصفحة',
    faceFunctionNotEnabled: 'خدمة التحقق من الوجه غير متاحة مؤقتاً، يرجى المحاولة لاحقاً',
    plsSubmitLegalIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني',
    legalPassPhoneVerifyTip: 'اجتاز الممثل القانوني التحقق من الهاتف في {time}، يمكن إعادة استخدام نتيجة التحقق.',
    legalPhoneNum: 'رقم هاتف الممثل القانوني',
    noPublicAccount: 'ليس لديك حساب مؤسسي؟',
    useSpecialMethod: 'أنا فرع لا يملك حساباً عاماً، استخدم قناة خاصة',
    ssqRemitMoneyTip: 'سيقوم BestSign بتحويل مبلغ (أقل من 0.99 يوان) إلى الحساب المؤسسي أدناه، يمكنك إدخال مبلغ التحويل لاجتياز التحقق من هوية المؤسسة.',
    accountName: 'اسم الحساب',
    bank: 'البنك',
    accountBank: 'البنك المفتوح فيه الحساب',
    accountBankName: 'اسم البنك المفتوح فيه الحساب',
    bankAccount: 'الحساب البنكي',
    plsInputBankNameTip: 'يرجى إدخال اسم البنك، لا داعي لكتابة فرع أو فرع فرعي، مثل "بنك الصين"',
    locationOfBank: 'موقع البنك المفتوح فيه الحساب',
    plsSelect: 'يرجى الاختيار',
    province: 'المقاطعة',
    city: 'المدينة',
    cityCounty: 'المدينة/المقاطعة',
    nameOfBank: 'اسم فرع البنك',
    plsInputZhiBankName: 'ادخل اسم فرع البنك، مثل "فرع بنك هانغتشو الثقافي"',
    canNotFindBank: 'لا يمكن العثور على البنك؟',
    clickChangeReversePay: 'انقر على "تغيير طريقة الدفع" في أسفل الصفحة للدفع العكسي',
    other: 'أخرى',
    otherZhiBank: 'اسم فرع البنك (إذا اخترت "أخرى" للفرع، يرجى الملء)',
    bankAccount1: 'رقم الحساب المصرفي',
    plsInputBankAccount: 'يرجى إدخال رقم الحساب المصرفي',
    remitNotSuccess: 'التحويل غير ناجح؟',
    switchPayMethod: 'تغيير طريقة الدفع',
    whySubmitBankInfo: 'لماذا يجب تقديم معلومات البطاقة المصرفية',
    submitBankInfoExplan: 'التحقق من معلومات البطاقة المصرفية هو جزء من عملية التحقق من هوية المؤسسة. سنقوم بتحويل مبلغ تحقق إلى هذه البطاقة المصرفية، ولن يتم خصم أي مبلغ من حسابك.',
    plsSelectBank: 'يرجى اختيار البنك من القائمة المنسدلة',
    plsSelectArea: 'يرجى اختيار المحافظة والمدينة/المقاطعة من القائمة المنسدلة',
    plsInputNameOfAccountBank: 'يرجى إدخال اسم البنك',
    plsInputCorrectBankInfo: 'يرجى إدخال معلومات الحساب المؤسسي الصحيحة',
    plsInputFullBankName: 'يرجى إدخال الاسم الكامل للبنك (متضمناً اسم الفرع)',
    area: 'المنطقة',
    contactCustomerService: 'اتصل بخدمة العملاء',
    beiJing: 'بكين',
    dongCheng: 'منطقة دونغتشنغ',
    fillJointBankNum: 'يحتاج هذا الحساب إلى رقم البنك المشترك لإتمام التحويل',
    bankType: {
        type1: 'بنك',
        type2: 'جمعية ائتمان تعاونية',
        type3: 'تعاونية ائتمانية',
        type4: 'جمعية زراعية',
    },
    accountBankArea: 'موقع البنك',
    changeRemitMethod: 'تغيير طريقة التحويل',
    canNotRemitAuth: 'لا يمكن بدء التحقق بالتحويل لحسابك المؤسسي، يرجى اختيار طريقة تحقق أخرى',
    bankMaintenanceTip: 'إشعار صيانة النظام المصرفي: بنك غوانغفا (11 يناير 02:30-03:30)، البنك الزراعي (12 يناير 02:00-06:00)، بنك البناء (12 يناير 03:30-05:30)، البنك الصناعي والتجاري (12 يناير 04:00-06:15). يرجى تجنب التحويلات المؤسسية خلال هذه الفترات.',
    faceVerify: 'التحقق من الوجه',
    havePublicAccount: 'لدي حساب مؤسسي',
    ensureInfoSafeTip: 'لضمان أمن معلومات مؤسستك وحمايتها من الاستخدام غير المصرح به، يرجى إكمال التحقق من وجه الممثل القانوني أولاً، ثم إجراء التحقق بالتحويل',
    submitEntBankInfo: 'تقديم معلومات الحساب المؤسسي',
    fillBackAmout: 'إدخال مبلغ الإيداع',
    legalPerson: 'الممثل القانوني',
    operatorMange: 'المسؤول',
    unionNum: 'رقم البنك المشترك',
    getUnionNumTip: 'يرجى التواصل مع المالية في مؤسستك للحصول على رقم البنك المشترك، أو وفقاً لمعلومات الفرع',
    uinonNumSearchOnline: 'البحث عن رقم البنك المشترك عبر الإنترنت',
    remitProcessMap: {
        submit: 'تم تقديم طلب التحويل',
        accept: 'تم قبول التحويل',
        success: 'تم التحويل بنجاح',
        receiveWait: 'تم استلام طلب الدفع، في انتظار رد البنك',
        successAttention: 'تم التحويل بنجاح، يرجى التحقق من تفاصيل الحساب. يرجى الانتباه:',
        remarkTip: 'ملاحظة التحويل: المبلغ للتحقق من هوية المؤسسة وطلب شهادة CA',
        thirdChannel: 'يتم التحويل عبر قناة دفع طرف ثالث، القناة هي:',
        remitNotSsq: 'يتم التحويل عبر قناة طرف ثالث، اسم المحول ليس المنصة',
        remitPartySsq: 'المحول هو "شركة هانغتشو للتكنولوجيا المحدودة"',
    },
    close: 'إغلاق',
    name: 'الاسم',
    idCardNum: 'رقم بطاقة الهوية',
    reversePayMap: {
        remitTip1: 'قم بتحويل 0.01 يوان إلى حساب المنصة المؤسسي، ثم حدد الخيارات أدناه وانقر على "تأكيد" للتحقق',
        iAgree: 'أوافق وأقر بما يلي:',
        remitAuthUse: 'سيتم استخدام مبلغ 0.01 يوان المحول إلى حساب المنصة لشراء عقد مؤسسي واحد عند نجاح التحقق؛ وفي حالة الفشل، لا يمكن المطالبة باسترداد المبلغ',
        authFailure: 'عدم استيفاء المتطلبات التالية سيؤدي إلى فشل التحقق:',
        authFailureReason1: '(1) يجب أن يكون المحول هو الممثل القانوني الحالي:',
        authFailureReason2: '(2) يجب استخدام حساب شخصي للتحويل',
        authFailureReason3: '(1) يجب أن يكون المحول هو المؤسسة الحالية:',
        authFailureReason4: '(2) يجب استخدام حساب مؤسسي للتحويل',
        plsInputBankName: 'يرجى إدخال اسم الحساب المصرفي',
        authFailureReason5: '(3) يجب أن يكون مبلغ التحويل 0.01 يوان بالضبط',
        authFailureReason6: '(4) يجب أن يكون تاريخ التحويل بعد {date}',
        authFailureReason7: '(5) معلومات حساب المستلم كالتالي، لا يمكن التحويل لحساب آخر:',
        authFailureReason8: '(6) يجب استخدام رمز التحقق هذا كملاحظة للتحويل:',
        remitDelay: 'قد يستغرق وصول التحويل بعض الوقت، يرجى الانتظار',
        failureReason: 'أسباب فشل التحقق المحتملة:',
        wait30min: 'بسبب أنظمة البنوك، يمكن الاستعلام عن النتيجة بعد 30 دقيقة من نجاح التحويل',
        queryProgress: 'استعلام عن التقدم',
        inputRemitBankName: 'يرجى إدخال اسم الحساب المصرفي للمحول',
        queryFailureTip: 'فشل الاستعلام، يرجى المحاولة لاحقاً',
        remitAuthSuccess: 'تم اجتياز التحقق بنجاح، وتم استخدام المبلغ لشراء عقد مؤسسي واحد',
    },
    hourMinSec: '{hour} ساعة {min} دقيقة {sec} ثانية',
    minSec: '{min} دقيقة {sec} ثانية',
    sec: '{sec} ثانية',
    ssqRemitTip: 'تم تقديم طلب تحويل بمبلغ أقل من 1 يوان لحسابك، سيصل خلال 1-2 يوم عمل، يرجى تأكيد المبلغ هنا عند وصوله',
    inputRightRemitAmout: 'يجب مراجعة كشف حسابك وإدخال المبلغ الصحيح لاجتياز التحقق',
    notGetRemit: 'إذا لم تجد المبلغ في حسابك، انقر هنا',
    queryRemitProgress: 'للتحقق من حالة التحويل',
    inputWrongAccount: 'هل أدخلت رقم حساب خاطئ؟',
    remitNote: 'ملاحظة التحويل:',
    ssqApplyCaTip: 'المبلغ مخصص للتحقق من هوية المؤسسة وطلب شهادة CA، يرجى إدخال المبلغ على المنصة',
    remitAmout: 'مبلغ التحويل',
    yuan: 'يوان',
    receiveAmout: 'مبلغ الإيداع بين 0.01-0.99 يوان',
    maxRemitTimeTip: 'يمكنك التحويل أربع مرات فقط! هل تريد تأكيد إعادة تقديم رقم الحساب؟',
    remitFailure: 'فشل التحويل، سيتم العودة لصفحة الدفع المؤسسي، يجب إعادة طلب التحويل',
    plsInputRemitAmout: 'يرجى إدخال مبلغ بين 0.01-0.99 يوان',
    reSubmitBankInfo: 'يجب إعادة تقديم معلومات البطاقة المصرفية',
    amoutError: 'خطأ في المبلغ',
    reSubmit: 'إعادة التقديم',
    amoutInvalid: 'المبلغ غير صالح',
    authReject: 'تم رفض وثائق التحقق، يرجى إعادة التقديم',
    authCertificate: 'التحقق بالتفويض',
    entPayAuth: 'التحقق بالدفع المؤسسي',
    legalPhoneAuth: 'التحقق من هاتف الممثل القانوني',
    legalFaceAuth: 'التحقق من وجه الممثل القانوني',
    sender: 'المرسل',
    requireYouThrough: 'يطلب {name} منك اجتياز {type}',
    selectOneAuthMethod: 'يرجى اختيار إحدى طرق التحقق التالية:',
    entCertificate: 'وثائق المؤسسة',
    personCertificate: 'وثائق شخصية',
    iAmLegal: 'أنا الممثل القانوني',
    iAmNotLegal: 'لست الممثل القانوني، أنا مسؤول المؤسسة',
    receiveSsqPhone: 'أوافق على تلقي مكالمة من المنصة',
    plsAgreeSsqPhone: 'يرجى الموافقة على تلقي مكالمة من المنصة',
    submitInfoError: 'المعلومات الشخصية (الاسم أو رقم الهوية) غير صحيحة، يرجى التحقق',
    submitIndividualEntAuth: 'أنت تقدم طلب تحقق كمؤسسة فردية، رخصة عملك لا تحتوي على اسم مؤسسة',
    submitIndividualCorrectIdCard: 'يرجى تقديم بطاقة هوية الممثل القانوني الصحيحة',
    entPersonInfoError: 'معلومات المؤسسة أو المعلومات الشخصية غير صحيحة، يرجى التحقق وإعادة التقديم',
    noEntName: 'لا يوجد اسم مؤسسة',
    businessLicenseBlank: 'خانة اسم المؤسسة في الرخصة التجارية: فارغة',
    addressName: '/العنوان/اسم الممثل القانوني',
    plsClick: 'يرجى النقر',
    haveEntName: 'يوجد اسم مؤسسة، يتطلب مراجعة يدوية من خدمة العملاء',
    checkFollowInfoTip: 'تحقق من المعلومات التالية وانقر "تأكيد"، يمكنك متابعة تقديم وثائق التحقق الأخرى. ستتم مراجعة معلومات المؤسسة يدوياً، وإذا كانت المعلومات غير صحيحة سيتم رفض جميع الوثائق المقدمة. تستغرق المراجعة حوالي يوم عمل واحد.',
    entName: 'اسم المؤسسة',
    unifySocialCode: 'رقم التعريف الاجتماعي الموحد',
    uploadColorImgTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',
    businessLicense: 'الرخصة التجارية',
    uploadBusinessLicenseTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل',
    imgLimitTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية "للتحقق على المنصة فقط"',
    autoRecognizedTip: 'المعلومات التالية تم التعرف عليها تلقائياً، يرجى التحقق بعناية وتصحيح أي أخطاء',
    iNoEntName: 'ليس لدي اسم مؤسسة',
    clickUseSpecialMethod: 'انقر لاستخدام القناة الخاصة',
    socialCodeBuisnessNum: 'رقم التعريف الاجتماعي الموحد/رقم السجل التجاري',
    plsSubmitCorrectLegalName: 'يرجى تقديم اسم الممثل القانوني الصحيح',
    plsSubmitBusinessLicense: 'يرجى تقديم الرخصة التجارية أولاً',
    noEntUploadBusinessTip: 'يرجى رفع النسخة الأصلية الملونة أو نسخة مختومة بختم المؤسسة؛ للمؤسسات غير التجارية، استخدم شهادة التسجيل. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية "للتحقق على المنصة فقط". سيتم استخدام معلومات المؤسسة لطلب الشهادة الرقمية.',
    imgRequireTip: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت. نقبل فقط الصور بدون علامة مائية أو بعلامة مائية "للتحقق على المنصة فقط".',
    noEntNameUse: 'المؤسسات الفردية بدون اسم مؤسسة يجب استخدام',
    legalPlusCode: 'اسم الممثل القانوني مع رقم التعريف الاجتماعي',
    codeAsEntName: 'كاسم للمؤسسة',
    ssq: 'المنصة',
    entAuth: 'التحقق من المؤسسة',
    eleContractServiceBy: 'خدمة التوقيع الإلكتروني مقدمة من',
    spericalProvide: 'توفير',
    redirectWait: 'جاري التحويل، يرجى الانتظار',
    businessLicenseImg: 'صورة الرخصة التجارية',
    idCardNational: 'الجانب الخلفي لبطاقة الهوية',
    idCardFace: 'الجانب الأمامي لبطاقة الهوية',
    dueToSignRequire: 'نظراً لمتطلبات التوقيع',
    authorizeSomeone: 'أفوض {name} للحصول على المعلومات التالية:',
    authoizedInfo: 'معلومات حسابي الأساسية (رقم الحساب والاسم) ومعلومات المؤسسة الأساسية (اسم المؤسسة، رقم التعريف الاجتماعي الموحد أو رقم التسجيل، اسم الممثل القانوني)',
    iSubmitInfoToSsq: 'المعلومات التي قدمتها على المنصة',
    authMaterials: 'وثائق التحقق',
    sureInfo: 'المعلومات صحيحة',
    modifyInfo: 'تعديل المعلومات',
    additionalInfo: 'معلومات إضافية',
    ssqNotifyMethod: 'طريقة الإشعار على المنصة',
    phoneNum: 'رقم الهاتف',
    email: 'البريد الإلكتروني',
    submitInfoPlsVerify: 'تم تقديم معلومات المؤسسة الأساسية، يرجى التحقق',
    operatorIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية المسؤول',
    operatorIdCardNational: 'الجانب الخلفي لبطاقة هوية المسؤول',
    legalIdCardFaceImg: 'الجانب الأمامي لبطاقة هوية الممثل القانوني',
    legalIdCardNational: 'الجانب الخلفي لبطاقة هوية الممثل القانوني',
    plsAgreeAuthorized: 'يرجى الموافقة على التفويض أولاً',
    finishConfirmInfo: 'تم تأكيد المعلومات، يرجى متابعة عملية التحقق من المؤسسة',
    entOpenMultipleBusiniss: 'المؤسسة لديها خطوط أعمال متعددة، لا يمكن متابعة العملية',
    contactSsqDeal: 'يرجى التواصل مع خط الأعمال الذي أكمل التحقق من المؤسسة على المنصة سابقاً',
    signContract: 'توقيع العقد',
    faceVerifyFailure: 'فشل التحقق من الوجه',
    reFaceVerifyAuth: 'إعادة التحقق من الوجه',
    threeTimesFailure: 'عذراً، لقد فشلت في التحقق من الوجه 3 مرات متتالية اليوم، يرجى اختيار طريقة تحقق أخرى',
    faceVerifySucess: 'نجح التحقق من الوجه',
    chooseOtherAuthMethod: 'اختيار طريقة تحقق أخرى',
    plsContinueOnPc: 'يرجى المتابعة على جهاز الكمبيوتر',
    accountAppealSuccess: 'نجح استئناف الحساب',
    faceCompareSuccess: 'نجحت مطابقة الوجه',
    plsUseWechatScan: 'يرجى استخدام متصفح WeChat لمسح رمز QR',
    wechatCannotScanFace: 'بسبب تغيير سياسة WeChat، لا يمكن إجراء التحقق من الوجه حالياً',
    plsUploadClearIdCardImg: 'يرجى رفع صورة واضحة لبطاقة الهوية، سيتم التعرف على معلومات البطاقة تلقائياً. الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',
    uploadImgMap: {
        tip1: 'يرجى رفع صورة واضحة لبطاقة الهوية،',
        tip2: 'سيتم التعرف على معلومات البطاقة تلقائياً.',
        tip3: 'الصور يجب أن تكون بصيغة jpeg أو jpg أو png وحجم لا يتجاوز 10 ميجابايت.',
        tip4: 'نقبل فقط الصور بدون علامة مائية أو بعلامة مائية "للتحقق على المنصة فقط"',
    },
    plsSubmitIdCard: 'يرجى تقديم بطاقة الهوية أولاً',
    noNameNoSupportEntRemit: 'نظراً لعدم وجود اسم مؤسسة، لا يمكن استخدام التحقق بالتحويل المؤسسي',
    checkMoreVerison: 'عرض المزيد من الإصدارات',
    viewCertificate: 'عرض الشهادة',
    continueAuthNewEnt: 'متابعة التحقق من مؤسسة جديدة',
    continueBuyPackage: 'متابعة شراء الباقة',
    needSubmitBasicInfo: 'مطلوب فقط تقديم معلومات المؤسسة الأساسية',
    youFinishEntAuth: 'لقد أكملت التحقق من المؤسسة',
    loginSsqWebToEntAuth: 'سجل الدخول إلى موقع المنصة لإكمال عملية التحقق الكاملة للمؤسسة للحصول على صلاحيات أعلى',
    entAuthCertificate: 'شهادة التحقق من المؤسسة',
    youFinishEntAuthTip: 'لقد أكملت التحقق من هوية المؤسسة',
    backToHome: 'العودة إلى الصفحة الرئيسية',
    youNeedMoreOperate: 'تحتاج إلى {operate} لإكمال العمليات التالية:',
    goPc: 'الذهاب إلى نسخة الكمبيوتر',
    addEntMemberStepMap: {
        title: 'خطوات إضافة عضو مؤسسة:',
        step1: '1- الدخول إلى لوحة تحكم المؤسسة',
        step2: '2- فتح إدارة الأعضاء',
        step3: '3- النقر على إضافة عضو جديد',
        step4: '4- إدخال الحساب والاسم، اختيار الدور',
        step5: '5- النقر على حفظ لإضافة العضو الجديد',
    },
    addEntMember: 'إضافة عضو مؤسسة',
    addSealStepMap: {
        title: 'خطوات إضافة ختم:',
        step1: '1- الدخول إلى لوحة تحكم المؤسسة',
        step2: '2- فتح قائمة الأختام',
        step3: '3- النقر على إضافة ختم جديد',
        step4: '4- إدخال اسم الختم، رفع تصميم الختم أو إنشاء ختم إلكتروني',
        step5: '5- النقر على حفظ لإضافة الختم',
        step6: '6- النقر على إضافة حامل',
        step7: '7- اختيار عضو المؤسسة',
        step8: '8- النقر على تأكيد لإضافة حامل الختم',
    },
    addSeal: 'إضافة ختم',
    waitApporve: 'في انتظار المراجعة',
    submitAuthMaterial: 'تقديم وثائق التحقق',
    authFinish: 'اكتمل التحقق',
    plsSubmitBeforeTip: 'يرجى إكمال تقديم جميع الوثائق قبل {date}، وإلا ستنتهي صلاحية المعلومات الأساسية.',
    oneDayFinishApprove: 'سيقوم فريق خدمة العملاء بإكمال المراجعة خلال يوم عمل واحد، يرجى الانتظار',
    entAuthFinish: 'اكتمل التحقق من المؤسسة',
    baseInfoInvalid: 'انتهت صلاحية المعلومات الأساسية، يرجى إعادة التقديم',
    missParams: 'معلمات مفقودة!',
    illegalLink: 'رابط غير صالح',
    cookieNotEnabe: 'لا يمكن قراءة/كتابة ملفات تعريف الارتباط، هل قمت بتفعيل وضع التصفح الخفي أو تعطيل ملفات تعريف الارتباط؟',
    truthSubmitOperatorIdCard: 'يرجى تقديم بطاقة هوية المسؤول الصحيحة',
    abandonAttorneyAuth: 'التخلي عن التحقق بالتفويض،',
    modifyCertiInfo: 'تعديل معلومات الوثائق',
    modifyAttorneyInfo: 'تعديل معلومات التفويض',
    plsUploadBusinessLicense: 'يرجى رفع الرخصة التجارية!',
    plsUploadLegalCerti: 'يرجى رفع وثائق الممثل القانوني!',
    plsUploadOperatorCerti: 'يرجى رفع وثائق المسؤول!',
    legalIdCardSubmit: 'تقديم بطاقة هوية الممثل القانوني',
    serviceAttorneryAuth: 'التحقق بتفويض الخدمة',
    accountAppealMap: {
        entApeal: 'استئناف حساب المؤسسة',
        apealSuccess: 'نجح الاستئناف',
        comName: 'اسم الشركة:',
        account: 'الحساب:',
        verifyCode: 'رمز التحقق:',
        ener6Digtal: 'يرجى إدخال 6 أرقام',
        beMainManagerTip: 'بعد نجاح طلب حساب المؤسسة، ستصبح المدير الرئيسي للمؤسسة',
        mainAccount: 'حساب المدير الرئيسي',
        continueSign: 'متابعة التوقيع',
        continueConfirm: 'متابعة التحقق',
        plsEnterComName: 'يرجى إدخال اسم الشركة أولاً',
        plsEnterCorrentComName: 'يرجى إدخال اسم الشركة الصحيح',
        sendSuccess: 'تم الإرسال بنجاح!',
        plsEnterCorrectCode: 'يرجى إدخال رمز التحقق الصحيح',
    },
    faceInitLoading: 'جاري تهيئة التحقق من الوجه، يرجى الانتظار',
    wxFaceVersionTip: 'يتطلب التحقق من الوجه WeChat إصدار 7.0.12 أو أعلى، يرجى الترقية',
    wxIosFaceVersionTip: 'يتطلب التحقق من الوجه iOS إصدار 10.3 أو أعلى، يرجى الترقية',
    wxAndroidVersionTip: 'يتطلب التحقق من الوجه Android إصدار 5.0 أو أعلى، يرجى الترقية',
    faceInitFailure: 'فشل تهيئة التحقق من الوجه: ',
    entAuthCertificateTip: 'شهادة التحقق من هوية المؤسسة',
    idCardHandHeld: 'التحقق بحمل بطاقة الهوية',
    faceAuth: 'التحقق من الوجه',
    noMainlandAuth: 'التحقق خارج البر الرئيسي',
    entAuthTip: 'التحقق من هوية المؤسسة',
    signIntro: 'دليل التوقيع',
    companySet: 'إعدادات المؤسسة',
};
