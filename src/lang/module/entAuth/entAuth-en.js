export default {
    submitBaiscInfo: 'Submit basic information',
    firstStep: 'First step',
    secondStep: 'Second step',
    thirdStep: 'Third step',
    nextStep: 'Next step',
    back: 'Back',
    submit: 'Submit',
    confirm: 'Ok',
    affirm: 'Confirm',
    cancel: 'Cancel',
    tip: 'Tip',
    account: 'Account number',
    view: 'View',
    iSee: 'I See',
    submitAttorney: 'Submit the authorization letter',
    plsDownloadSSQAttorneyTip: 'Please download BestSign Corporate Service Authorization Letter, take a picture of it with the corporate seal and then upload the picture.',
    ssqAttorney: 'BestSign Corporate Service Authorization Letter',
    downloadAttorney: 'Download authorization letter',
    imgSupportType: 'Support png, jpeg, jpg, bmp format and the size should not exceed 10M.',
    imgNoWatermarkTip: 'Only photos with no watermark or with "only for real name authentication" watermark can be used.',
    confirmSubmit: 'Confirm submission',
    backToPreviousPage: 'Back to previous page',
    uploadImgBeforeSubmit: 'Please upload photos before submitting',
    phoneNumBuyTip: 'Please make sure that the cell phone number is purchased under real name from the telecom operator, otherwise it will not pass the real name authentication.',
    operatorPhoneVerify: 'Verify the cell phone number of the operator',
    operatorName: 'Operator\'s name',
    operatorIdNum: 'Operator\'s ID card number ',
    modifyOperator: 'Modify the operator',
    changeIdCard: 'Change ID documents',
    operatorPassPhoneVerifyTip: ' The operator has passed the cell phone number authentication on {time}, and the authentication result is allowed to be reused.',
    operatorPhoneNum: 'Cell phone number of the operator',
    verifyCode: 'Verification code',
    tryFaceVerify: 'Try face scan authentication',
    afterPhoneVerifyTip: 'The system will automatically generate the authorization letter for you after cell phone number authentication. Please download the letter, stamp it and then upload it.',
    operatorFaceVerify: 'Face scan authentication of the operator',
    operatorPassFaceVerifyTip: ' The operator has passed the face scan authentication on {time}, and the authentication result is allowed to be reused.',
    alipayFaceVerify: 'Alipay face scan authentication',
    tryPhoneVerify: 'Try cell phone number authentication',
    scanFaceNow: 'Scan your face now',
    afterFaceVerifyTip: 'The system will automatically generate the authorization letter for you after face scan authentication. Please download the letter, stamp it and then upload it.',
    plsEnterRightPhoneNum: 'Please fill in the correct cell phone number first',
    plsGetVerifyCodeFirst: 'Please get the verification code first',
    agreeSsqProtectMethod: 'I agree to the method provided by BestSign to protect my personal information',
    ssqHowToProtectInfo: '《How Does BestSign Protect Your Personal Information》',
    noEntNameTip: 'Since your company does not have a corporate name, it does not support authentication of the authorization letter',
    legalName: 'Name of the legal representative',
    legalIdCardNum: 'Legal representative\'s ID number',
    uploadIdCard: 'Upload ID',
    legalPassFaceVerifyTip: 'The legal representative has passed the facial recognition authentication on {time}, and the authentication result is allowed to be reused.',
    plsEnterLegalIdCardNum: 'Please enter the legal representative\'s ID number first',
    plsUploadLegalIdCardImg: 'Please upload the legal representative\'s ID card first',
    pageExpiredTip: 'The current page has expired. Please refresh the page',
    faceFunctionNotEnabled: 'Face scan is temporarily unavailable. Please try again later',
    plsSubmitLegalIdCard: 'Please submit the ID card of the legal representative',
    legalPassPhoneVerifyTip: 'The legal representative has passed the mobile phone number authentication on {time}, and the authentication result is allowed to be reused. ',
    legalPhoneNum: 'Mobile phone number of legal representative',
    noPublicAccount: 'No corporate account?',
    useSpecialMethod: 'I am a branch without a corporate account. Use the special channel.',
    ssqRemitMoneyTip: 'BestSign will remit a sum of money (less than ¥0.99) to the corporate account below, and you can pass the corporate authentication by backfilling the payment amount.',
    accountName: 'Account name',
    bank: 'Bank',
    accountBank: 'Account Bank',
    accountBankName: 'Bank name',
    bankAccount: 'Bank Account',
    plsInputBankNameTip: 'Please enter the name of your bank, e.g. "Bank of China". Branch or sub-branch name is not required. ',
    locationOfBank: 'Bank address',
    plsSelect: 'Please choose',
    province: 'Province',
    city: 'City',
    cityCounty: 'City / County',
    nameOfBank: 'Branch name',
    plsInputZhiBankName: 'Fill in the branch name, e.g. "Bank of Hangzhou Wenchuang Sub-branch"',
    canNotFindBank: 'Can\'t find the bank name?',
    clickChangeReversePay: 'Click "switch payment method" at the bottom of the page for reverse payment.',
    other: 'Other',
    otherZhiBank: 'Branch name (please fill in the brank name if you choose \'other\')',
    bankAccount1: 'Bank Account',
    plsInputBankAccount: 'Please enter your bank account number',
    remitNotSuccess: 'Remittance unsuccessful?',
    switchPayMethod: 'Switch the payment method',
    whySubmitBankInfo: 'Why do you need to submit your bank card information?',
    submitBankInfoExplan: 'Bank card information authentication is a verification step in the corporate real name authentication process. BestSign will remit a verification fund to the bank card without debiting your account.',
    plsSelectBank: 'Please select a bank from the drop-down list',
    plsSelectArea: 'Please select the province, city/county from the drop-down list',
    plsInputNameOfAccountBank: 'Please enter the name of your bank',
    plsInputCorrectBankInfo: 'Please fill in the correct information of the bank used by your company',
    plsInputFullBankName: 'Please fill in the full name of the bank that opened the account (including the name of the branch)',
    area: 'Area',
    contactCustomerService: 'Contact Customer Service',
    beiJing: 'Beijing',
    dongCheng: 'Dongcheng District',
    fillJointBankNum: 'The account needs to fill in the bank\'s joint bank number in order to send money',
    bankType: {
        type1: 'Bank',
        type2: 'Union of Credit Unions',
        type3: 'Credit union',
        type4: 'Agricultural Cooperative',
    },
    accountBankArea: 'Bank address',
    changeRemitMethod: 'Change remittance method',
    canNotRemitAuth: 'Please choose other authentication methods as payment authentication failed for your corporate account.',
    bankMaintenanceTip: '接以下银行系统维护的通知：广发银行（1月11日02:30至03:30）、农业银行（1月12日02:00至06:00）、建设银行（1月12日03:30至05:30）、工商银行（1月12日04:00至06:15）。请您尽量避免在此时间段内发起对公打款，或可采用我司提供其他认证服务。银行系统恢复后服务即可重新使用。',
    faceVerify: 'Face scan verification',
    havePublicAccount: 'I have a corporate account.',
    ensureInfoSafeTip: 'In order to protect the security of corporate information and prevent its misuse, please complete face scan of the legal representative first, and then proceed to payment for authentication.',
    submitEntBankInfo: 'Submit your corporate account information.',
    fillBackAmout: 'Fill in the backfill amount',
    legalPerson: 'Legal representative',
    operatorMange: 'Operator',
    unionNum: 'Union number',
    getUnionNumTip: 'Please obtain the inter-bank number from the financial communication of your company/unit, or according to the bank branch information',
    uinonNumSearchOnline: 'Online inquiry of joint bank number',
    remitProcessMap: {
        submit: 'Payment request has been submitted',
        accept: 'Payment has been accepted',
        success: 'Payment has been successfully made',
        receiveWait: 'Payment request has been accepted, and we are waiting for the bank to return the result. Your patience is much appreciated.',
        successAttention: 'The payment has been successfully made. Please check the account details carefully and please note that:',
        remarkTip: 'Remittance note: This payment is for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',
        thirdChannel: 'BestSign remits money through third-party payment channels. The remittance channel is:',
        remitNotSsq: 'BestSign remits money through third-party payment channels, and the account name of the remitter is not BestSign.',
        remitPartySsq: 'The remitter is "Hangzhou BestSign Network Technology Co., Ltd."',
    },
    close: 'Close',
    name: 'Name',
    idCardNum: 'Identity number',
    reversePayMap: {
        remitTip1: 'Remit 0.01 RMB to the corporate account of BestSign, then check the following options and click "OK" to pass corporate authentication.',
        iAgree: 'I am informed and agree that: ',
        remitAuthUse: 'RMB 0.01 remitted to the account designated by BestSign will be used to purchase 1 public contract if authentication is successful. This payment will not be refunded by BestSign if the authentication fails.',
        authFailure: 'Failure to meet the following requirements will result in authentication failure：',
        authFailureReason1: '(1) The remitting party is an existing legal entity.',
        authFailureReason2: '(2) The remitting party must use an account under its own name to make the remittance.',
        authFailureReason3: '(1) The remitting party\'s name is the current name of the company：',
        authFailureReason4: '(2) The remitter must use a corporate account for the remittance.',
        plsInputBankName: 'Please enter the name of the bank account.',
        authFailureReason5: '(3) The amount of the single payment remitted by the remitter to the recipient is 0.01 RMB. It shall not exceed 0.01 RMB or in other currencies.',
        authFailureReason6: '(4) Date of remittance. Please ensure that the remittance is sent after {date}',
        authFailureReason7: '(5) The recipient\'s bank account information is as follows. Payment cannot be remitted to other accounts：',
        authFailureReason8: '(6) When remitting money to the recipient, you must use and only note this verification code:',
        remitDelay: 'There may be a delay in the arrival of the remittance.',
        failureReason: 'Possible reasons for real name authentication failure: ',
        wait30min: 'The bank system allows you to check the result after 30 minutes of successful remittance.',
        queryProgress: 'Query progress',
        inputRemitBankName: 'Please fill in the payer\'s bank account name.',
        queryFailureTip: 'Query failed. Please try again later...',
        remitAuthSuccess: 'The remittance has been used to purchase 1 corporate contract.',
    },
    hourMinSec: '{hour}hour {min}minute {sec}second',
    minSec: '{min}minute {sec}second',
    sec: '{sec}second',
    ssqRemitTip: 'BestSign has submitted a transfer of less than ¥1 to your account, which will arrive within 1-2 business days. Please confirm the amount here after it arrives.',
    inputRightRemitAmout: 'You will need to check the details of your account\'s transactions and enter this amount correctly to pass the authentication.',
    notGetRemit: 'If you don\'t see the remittance amount on your account, click here',
    queryRemitProgress: 'Check the progress of the payment.',
    inputWrongAccount: 'Wrong account number?',
    remitNote: 'Remittance notes:',
    ssqApplyCaTip: 'The amount is used for corporate real name authentication and CA certificate application on BestSign platform. Please backfill the amount on BestSign page.',
    remitAmout: 'Remittance amount',
    yuan: 'RMB',
    receiveAmout: 'The remittance amount is between RMB 0.01-0.99',
    maxRemitTimeTip: 'Payments can be made maximally four times! Are you sure you want to resubmit the account number?',
    remitFailure: 'The payment is not successful. You will be re-directed to the corporate payment page to re-apply for payment.',
    plsInputRemitAmout: 'Please enter an amount between RMB 0.01 and RMB0.99.',
    reSubmitBankInfo: 'You need to resubmit your bank card information.',
    amoutError: 'Wrong amount',
    reSubmit: 'Resubmit',
    amoutInvalid: 'Invalid amount',
    authReject: 'Information for real name authentication has been rejected. Please resubmit the information.',
    authCertificate: 'Authorization letter authentication',
    entPayAuth: 'Corporate payment authentication',
    legalPhoneAuth: 'Legal representative\'s cell phone number authentication',
    legalFaceAuth: 'Legal representative\'s face scan authentication',
    sender: 'Sender',
    requireYouThrough: '{name} requires you to pass {type}',
    selectOneAuthMethod: 'Please choose any one of the following real name authentication methods:',
    entCertificate: 'Corporate certificates ',
    personCertificate: 'Personal ID documents',
    iAmLegal: 'I am the legal representative',
    iAmNotLegal: 'I am not a legal representative. I am the operator',
    receiveSsqPhone: 'I accept follow up by BestSign on the phone',
    plsAgreeSsqPhone: 'Please agree to BestSign\'s follow-up call first',
    submitInfoError: 'The name or ID number on the personal ID documents you submitted is wrong. Please verify again.',
    submitIndividualEntAuth: 'You are submitting information for corporate authentication as an individual businessperson. There is no corporate name on your business license.',
    submitIndividualCorrectIdCard: 'Please submit the legal representative\'s personal ID card. ',
    entPersonInfoError: 'You submit the wrong corporate / personal documents. Please verify and resubmit.',
    noEntName: 'No business name',
    businessLicenseBlank: 'Corporate name on the business license is: blank',
    addressName: '/address/legal representative\'s name',
    plsClick: 'Please click',
    haveEntName: 'Corporate name found. Request manual review by Customer Service.',
    checkFollowInfoTip: 'Verify the following information and click "OK".  You can continue to submit other information for authentication. Corporate information will be manually reviewed by BestSign. If the information is not correct, all the information you submit will be rejected. It takes about 1 business day for information verification.',
    entName: 'Company Name',
    unifySocialCode: 'Unified social credit code',
    uploadColorImgTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Enterprise information will be used to apply for a digital certificate.',
    businessLicense: 'Business license',
    uploadBusinessLicenseTip: 'Please upload the original in color or a copy with the corporate seal. If yours is not a company, please use the registration license.',
    imgLimitTip: 'Photos are only in jpeg, jpg, png format and shall be no more than 10M in size. Only images with no watermark or with "only for real name authentication" watermark are allowed.',
    autoRecognizedTip: 'The following information will be automatically verified. Please check it carefully and correct it if there is any mistake.',
    iNoEntName: 'I don\'t have a business name',
    clickUseSpecialMethod: 'Click to use the special channel.',
    socialCodeBuisnessNum: 'Uniform social credit code/business registration number',
    plsSubmitCorrectLegalName: 'Please submit the correct legal representative\'s name',
    plsSubmitBusinessLicense: 'Please submit the business license first',
    noEntUploadBusinessTip: 'Please upload the color original or the photocopy with the official seal of the company; for non-corporate units, please use the registration license. The photos are only in jpeg, jpg, png format and the size does not exceed 10M. Only photos without watermark or "only for signing real-name authentication" watermark are supported. Enterprise information will be used to apply for a digital certificate.',
    imgRequireTip: 'Photos are only available in jpeg, jpg, and png formats and the size does not exceed 10M. Only photos without watermark or "only for signing BestSign real-name authentication" watermark are supported.',
    noEntNameUse: 'Individual businessperson with a corporate name  must use',
    legalPlusCode: 'legal representative\'s name plus the unified social credit code',
    codeAsEntName: 'Code as corporate name',
    ssq: 'BestSign',
    entAuth: 'Enterprise Certification',
    eleContractServiceBy: 'E-contracting service is provided by',
    spericalProvide: '', // 英文翻译特意设置为'', 因翻译文案中有<span>标签，英文中provide在上文eleContractServiceBy中有体现
    redirectWait: 'Please wait for a moment',
    businessLicenseImg: 'Business license photo',
    idCardNational: 'National emblem side of the ID card',
    idCardFace: 'Portrait side of the ID card',
    dueToSignRequire: 'Due to the need of contract signing',
    authorizeSomeone: 'I authorize {name} to obtain the following information:',
    authoizedInfo: 'Basic information of my account (account number and name) and corporate basic information (corporate name, unified social credit code or registration number, legal representative\'s name)',
    iSubmitInfoToSsq: 'My submission on BestSign platform',
    authMaterials: 'Documents for authentication',
    sureInfo: 'All correct',
    modifyInfo: 'Revise information',
    additionalInfo: 'Additional information',
    ssqNotifyMethod: 'The notication method I use on BestSign platform ',
    phoneNum: 'Phone number',
    email: 'Mailbox',
    submitInfoPlsVerify: 'Corporate basic information has been submitted for you. Please verify',
    operatorIdCardFaceImg: 'Portrait side of the operator\'s ID card ',
    operatorIdCardNational: 'National emblem side of the operator\'s ID card ',
    legalIdCardFaceImg: 'Portrait side of the legal representative\'s ID card',
    legalIdCardNational: 'National emblem side of the legal representative\'s ID card ',
    plsAgreeAuthorized: 'Please agree to authorize first',
    finishConfirmInfo: 'You have completed information verification. Please continue the corporate authentication process.',
    entOpenMultipleBusiniss: 'The company has opened multiple business lines, and the operation can not continue.',
    contactSsqDeal: '请联系当初在上上签完成企业实名认证的业务线处理.',
    signContract: 'Sign the contract',
    faceVerifyFailure: 'Face scan authentication failed',
    reFaceVerifyAuth: 'Scan face again for authentication',
    threeTimesFailure: 'Sorry, you have failed 3 times in a row today. Please choose another authentication method.',
    faceVerifySucess: 'Face scan authentication successful',
    chooseOtherAuthMethod: 'Choose another authentication method',
    plsContinueOnPc: 'Please continue to operate on the webpage',
    accountAppealSuccess: 'Account appeal successful',
    faceCompareSuccess: 'Face scan comparison successful',
    plsUseWechatScan: 'Please use WeChat browser to scan the QR code.',
    wechatCannotScanFace: 'Due to the change of WeChat\'s policy, face scan is currently unavailable.',
    plsUploadClearIdCardImg: 'Please upload a clear ID photo. The system will automatically identify the ID information. The photo is only in jpeg, jpg, png format and the size should not exceed 10M.',
    uploadImgMap: {
        tip1: 'Please upload a clear photo of your ID card.',
        tip2: 'The system will automatically recognize the ID information.',
        tip3: 'The photo must be in jpeg, jpg, or png format only and must not exceed 10M in size.',
        tip4: 'Only pictures with no watermark or with "only for real name authentication" watermark are allowed.',
    },
    plsSubmitIdCard: 'Please submit your ID card first',
    noNameNoSupportEntRemit: 'As your company does not have a corporate name, corporate payment authentication method is not supported.',
    checkMoreVerison: 'View more versions',
    viewCertificate: 'View certificate',
    continueAuthNewEnt: 'Proceed with another corporate authentication.',
    continueBuyPackage: 'Continue to purchase a package',
    needSubmitBasicInfo: 'You are only required to submit basic information about your business.',
    youFinishEntAuth: 'You have completed corporate authentication.',
    loginSsqWebToEntAuth: 'Login to the official website of BestSign to complete the entire corporate authentication process. You can obtain higher level of authorization on BestSign platform.',
    entAuthCertificate: 'Corporate Real Name Authentication Certificate',
    youFinishEntAuthTip: 'You have already completed corporate real name authentication.',
    backToHome: 'Back to homepage',
    youNeedMoreOperate: 'You also need to {operate} complete the following operations ',
    goPc: 'Go to PC',
    addEntMemberStepMap: {
        title: 'Steps to add corporate members: ',
        step1: '1、Enter the corporate console',
        step2: '2、Open "Member Management"',
        step3: '3、Click to add a new member',
        step4: '4、Enter the account number and name, and select the role',
        step5: '5、Click "Save" and the new member will be added.员',
    },
    addEntMember: 'Add corporate members',
    addSealStepMap: {
        title: 'Steps to add a seal: ',
        step1: '1、Enter the corporate console',
        step2: '2、Open the seal list',
        step3: '3、Click "Add Seal"',
        step4: '4、Enter the name of the seal, upload the seal pattern or produce electronic signatures',
        step5: '5、Click "Save" and the new seal will be added',
        step6: '6、Click "Add Holder"',
        step7: '7、Select the corporate member',
        step8: '8、Click "OK" to add the seal holder',
    },
    addSeal: 'Add the seal',
    waitApporve: 'Pending for review',
    submitAuthMaterial: 'Submit auth information required',
    authFinish: 'Authentication completed',
    plsSubmitBeforeTip: 'Please submit all information before {date}, otherwise the basic information will be invalid.',
    oneDayFinishApprove: 'Customer service will complete the review in one business day. We appreciate your patience.',
    entAuthFinish: 'Corporate real name authentication completed',
    baseInfoInvalid: 'Basic information is no longer valid. Please resubmit the information.',
    missParams: 'Missing parameters!',
    illegalLink: 'Illegal link',
    cookieNotEnabe: 'Can not read or write cookies. Please check whether you have initiated the no trace / private mode or other operations that have disabled the cookies.',
    truthSubmitOperatorIdCard: 'Please submit the personal ID card of the operator.',
    abandonAttorneyAuth: 'Waiver of authorization letter authentication, ',
    modifyCertiInfo: 'Revise document information',
    modifyAttorneyInfo: 'Revise of authorization letter information',
    plsUploadBusinessLicense: 'Please upload the business license!',
    plsUploadLegalCerti: 'Please upload the legal entity\'s documents!',
    plsUploadOperatorCerti: 'Please upload the operator\'s documents!',
    legalIdCardSubmit: 'Legal representative\'s ID card submitted',
    serviceAttorneryAuth: 'Service authorization letter authentication',
    accountAppealMap: {
        entApeal: 'Business account appeal',
        apealSuccess: 'Complaint completed',
        comName: 'Company Name:',
        account: 'account number:',
        verifyCode: 'Verification code:',
        ener6Digtal: 'Please fill in 6 digits',
        beMainManagerTip: 'After successful business account application, you will become the business owner administrator',
        mainAccount: 'Primary administrator account',
        continueSign: 'Continue to sign',
        continueConfirm: '继续认证',
        plsEnterComName: 'Please fill in the company name first',
        plsEnterCorrentComName: 'Please fill in the correct company name',
        sendSuccess: 'Sent successfully!',
        plsEnterCorrectCode: 'Please enter correct verfication code',
    },
    faceInitLoading: 'Initialize the face tag, please wait',
    wxFaceVersionTip: 'Face brushing requires WeChat version 7.0.12 and above, please upgrade first',
    wxIosFaceVersionTip: 'Face brushing requires ios version 10.3 and above, please upgrade first',
    wxAndroidVersionTip: 'Face brushing requires android version 5.0 and above, please upgrade first',
    faceInitFailure: 'Failed to initialize face tag: ',
    entAuthCertificateTip: 'Enterprise real-name certification',
    idCardHandHeld: 'Handheld ID card authentication',
    faceAuth: 'Face recognition',
    noMainlandAuth: 'Non-mainland certification',
    entAuthTip: 'Enterprise real-name certification',
    signIntro: 'Guide of sign',
    companySet: 'Enterprise settings',

};
