export default {
    CSCommon: {
        chooseMember: 'Select member',
        choosedMember: 'Selected member',
        chooseRole: 'Select a role',
        choosedRole: 'Selected role',
        adjustDept: 'Adjustment department',
        chooseDept: 'Select department',
        choosedDept: 'Selected department',
        search: 'Search for',
        selectAll: 'Select all',
        tip: 'Prompt',
        warnTip: 'Please note:',
        name: 'Name',
        save: 'Save',
        edit: 'Edit',
        upload: 'Upload',
        delete: 'Delete',
        none: 'No',
        pleaseInput: 'Please enter',
        know: 'Understood',
        done: 'To complete',
        change: 'Change',
        remind: 'Remind',
        operate: 'Operate',
        view: 'View',
        date: 'Date',
        loading: 'Loading',
        saving: 'Saving',
        submit: 'Submit',
        admin: 'Primary administrator',
        staff: 'Staff',
        confirm: 'Confirm',
        cancel: 'Cancel',
        contract: 'Contract',
        template: 'Template',
        seal: 'Seal',
    },
    CSTips: {
        errorTip: 'Error message',
        serverError: 'The server has a small gap, please try again later.',
        noneMemberChoosedTip: 'Please select a member first',
        loginOverdue: '<PERSON><PERSON> has expired, please log in again',
    },
    CSSetting: {
        admin: 'Primary administrator',
    },
    CSMembers: {
        addMember: 'Add member',
        searchTip: 'Support input account / name search',
    },
    CSSeals: {
        signPwdType: 'Please enter 6 digits',
    },
    CSBusiness: {
        unSort: 'Not sorted',
    },
};
