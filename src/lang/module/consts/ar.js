export default {
    consts: {
        contractAlias: {
            doc: 'مستند',
            letter: 'خطاب استفسار',
            proof: 'تفويض',
            contract: 'عقد',
            agreement: 'موافقة',
            service_report: 'تقرير خدمة',
        },
    },
    pointPositionDoc: {
        pageTip: 'الصفحة {pageNum} من {pageSize}',
        nextDoc: 'الانتقال إلى المستند التالي',
        deleteTip: 'تم الحذف بنجاح',
    },
    pointPositionMiniDoc: {
        document: 'مستند',
        documentsLength: '{documentsLength} مستندات',
        pager: 'رقم الصفحة',
        page: 'صفحة',
    },
    pointPositionSite: {
        watermarkTip: 'سيتم استبدال المعلومات تلقائياً بالمعلومات الحقيقية بعد إرسال العقد',
        singlePageRidingSealTip: 'لا يمكن إضافة ختم عبر الصفحات للمستند ذو الصفحة الواحدة',
    },
};
