export default {
    consts: {
        contractAlias: {
            doc: '文件',
            letter: '询征函',
            proof: '授权书',
            contract: '合同',
            agreement: '同意书',
            service_report: '工单',
        },
    },
    pointPositionDoc: {
        pageTip: '第{pageNum}页共{pageSize}页',
        nextDoc: '进入下一份文档',
        deleteTip: '删除成功',
    },
    pointPositionMiniDoc: {
        document: '文档',
        documentsLength: '共{documentsLength}份',
        pager: '页码',
        page: '页',
    },
    pointPositionSite: {
        watermarkTip: '发送合同后自动替换为真实信息',
        singlePageRidingSealTip: '单页文档无法添加骑缝章',
    },
};
