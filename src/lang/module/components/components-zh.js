export default {
    certificationRenewalDialog: {
        renewalTitle: '数字证书续期',
        renewalTip: '您的证书已过期，为避免文件签署无法正常进行，请及时续期',
        renewalTip2: '若该账号持有证书的主体已变更，请您变更账号实名',
        previousIdentity: '持有证书的主体：',
        previousCA: '原证书颁发机构：',
        previousExpiryDate: '原证书有效期：',
        previousId: '原证书序列号：',
        renewal: '同意续期',
        reject: '信息有误，我要变更',
        rejectMessage: '需要驳回当前{name}的实名后，您才可以继续变更当前账号实名，是否继续？',
        rejectConfirm: '确定',
        renewalTips1: '为什么会出现此弹窗？',
        renewalTips2: '为保证您电子合同的法律效力，您参与的电子合同都会使用您的数字证书完成签署，并在最终合同文件中留下签名和时间戳作为证据。数字证书只能在有效期限内使用，基于合规要求上上签不会主动为您更新证书，若您证书过期，则需要完成更新后才能进行后续业务。',
        renewalTips3: '具体内容，您可查看上上签',
        renewalTips4: '《数字证书使用协议》',
        renewalTips5: '',
        tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',
        tip9: '复制链接',
        tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',
        tip11: '复制成功',
        tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',
        tip14: '去变更',
        tip15: '关闭',
        tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。',
    },
    infoProtectDialog: {
        userAuth: '使用刷脸服务须同意',
        titleWithSeperator: '《上上签如何保护您的个人信息》',
        title: '上上签如何保护您的个人信息',
        auth: '实名认证',
        faceSign: '签署校验',
        contentDesp: '您提交个人身份等信息（以下简称"个人信息"）时已经充分知悉并同意：',
        detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',
        detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',
        detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',
        know: '知道了',
    },
    signIdentityGuide: {
        title: '提示',
        requestYou: {
            0: '要求您',
            1: '要求您以',
        },
        tipToAuth: {
            0: '的身份进行实名认证，完成认证后即可查看和签署合同。',
            1: '进行实名认证，完成认证后即可查看和签署合同。',
        },
        needResend: '进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',
        note: '注：',
        entName: '企业名称',
        identityInfo: '身份信息',
        signNeedCoincidenceInfo: '完全一致才能签署合同',
        inTheName: '以',
        of: '的',
        identity: '身份',
        nameIs: '姓名为',
        IDNumIs: '身份证号为',
    },
    pdf: {
        previewFail: '文件预览失败',
        pager: '第{x}页，共{y}页',
        parseFailed: '解析pdf文件失败，请点击“确定”重试',
        confirm: '确定',
    },
    tagManage: {
        title: '设置标签',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: '已被实名',
        assignedIdentity: '发件方填写的签约主体为：',
        entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',
        entAdminName: '管理员姓名：',
        entAdminAccount: '管理员账号：',
        applyToBeAdmin: '我要申诉成为主管理员',
        contactToJoin: '联系管理员加入企业',
        applicant: '申请人',
        inputYourName: '请输入您的姓名',
        account: '账号',
        send: '发送',
        contract: '合同',
        sendWishToJoin: '您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请',
        applyToJoin: '您还未加入该企业，无法签署该{alias}，是否要申请加入？',
        sentSuccessful: '发送成功',
        contractAlias: {
            doc: '文件',
            letter: '询征函',
            proof: '授权书',
        },
    },
    selectBizLine: {
        title: '请选择业务线',
    },
    importOffLineDoc: {
        importDoc: '导入合同',
        step0Title: '第一步：确认导入企业名称',
        step1Title: '第二步：上传Excel',
        step2Title: '第三步：上传合同文件',
        step1Info: '请先下载Excel模板，填写完成后再导入,合同数量不超过1000。',
        next: '下一步',
        entName: '企业名称',
        archiveFolder: '归档文件夹',
        downloadExcel: '下载Excel',
        uploadExcel: '上传Excel',
        reUploadExcel: '重新上传',
        step2Info: ['1. 合同文件只能是PDF或图片；', '2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；', '3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；'],
        uploadZip: '点击上传Zip',
        reUploadZip: '重新上传Zip',
        done: '确定',
        back: '返回',
        contractTitle: '合同名称',
        singerAccount: '签署人账号',
        singerName: '签署人名称',
        uploadSucTip: '上传成功，点击"确定"按钮开始导入',
        outbox: '发件箱',
        fileLessThan: '请上传小于{num}M的文件',
        fileTypeValid: '只能上传{type}格式的文件!',
    },
    download: {
        contactGetDownloadCodeTip: '请联系合同发起方获取下载码，或尝试登录本企业业务系统下载。',
        downloadCode: '下载码',
        hint: '提示',
        download: '下载',
        plsInput: '请输入',
        plsInputDownloadCode: '请输入下载码',
        downloadCodeError: '下载码错误',
        allFiles: '全部文件',
        cancel: '取 消',
        plsSelectFiles: '请先选择文件',
        publicCloudDownloadTip: '要下载的合同中包含签署人上传的其他资料，是否随合同一起下载？',
        hybridCloudDownloadTip: '要下载的合同中包含签署人上传的其他资料。',
        sameTimeDownloadAttachTip: '同时下载合同附属资料',
        downloadContract: '下载合同',
        downloadAttach: '下载合同附属资料',
    },
    commonHeader: {
        groupCertification: '集团代认证',
        goHomePage: '返回首页',
        companyPrivateSaveTypeContactTip: '您的企业采用了合同私有存储的方式当前网络已连接至合同存储服务器',
        companyPrivateSaveTypeNoContactTip: '您的企业采用了合同私有存储的方式，当前网络无法连接至合同存储服务器',
        advise: '建议：',
        checkCompanyInteralNetContact: '① 检查当前网络能否访问企业内网',
        checkContactServerNetContact: '② 检查合同存储服务器是否正常运行',
    },
    transfer: {
        list1: '列表1',
        list2: '列表2',
        maxSelectNum: '最多只能选择{maxLength}个',
    },
    poperCascader: {
        plsSelect: '请选择',
        person: '人',
        selectNumTip: '已选择{A}/{B}个{C}',
        allSelect: '全选',
    },
    authInfoChange: {
        title: '实名信息变更检测',
        confirm: '确认',
        changeAuth: '更新实名',
        notifyAdmin: '通知管理员',
        notifySuccess: '通知成功',
        operateSuccess: '操作成功',
        warningTip: {
            tip1: '经审查，贵司“{entName}”在上上签的企业实名信息{oldAuthInfo}与最新的工商备案信息{newAuthInfo}不一致。',
            tip2: '为保证您签署的电子合同合规性和效力，请使用最新的企业信息进行重新实名。',
            tip3: '此操作不会驳回您当前的企业信息。',
        },
        suggestTip: {
            tip1: '如您企业为集团架构，请联系您的专属CSM、或者拨打上上签客服热线400-993-6665帮助您完成实名认证信息的更新。更新后，方可继续签署。',
            tip2: '点击【通知管理员{adminInfo}】，',
            tip3: '可以立即发送通知给管理员，引导管理员去重新实名。您也可线下通知，及时推动业务开展。',
        },
    },
};
