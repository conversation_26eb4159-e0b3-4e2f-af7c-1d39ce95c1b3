export default {
    certificationRenewalDialog: {
        renewalTitle: 'デジタル証書期限延長',
        renewalTip: 'お客様のデジタル証書は期限切れとなりましたので、契約の署名手続を正常に行うために、直ちに期限延長を行ってください',
        renewalTip2: 'このアカウントの電子証明書を保有する主体が変更された場合は、アカウントの実名認証を更新してください。',
        previousIdentity: '証書の所有者：',
        previousCA: '原証書の発行機構：',
        previousExpiryDate: '原証書の有効期限：',
        previousId: '原証書のシリアルコード：',
        renewal: '期限延長に同意する',
        reject: '個人の実名情報に誤りがあります。変更を行いたいです。',
        rejectMessage: 'このアカウントの実名認証を変更するには、先に{name}の実名認証を却下する必要があります。続行しますか？',
        rejectConfirm: '確定',
        renewalTips1: 'この通知が表示される理由',
        renewalTips2: '電子契約の法的効力を確保するため、あなたが署名した電子契約は電子証明書を用いて署名され、最終契約書に電子署名とタイムスタンプが証拠として記録されます。デジタル証明書は有効期限内でのみ使用可能です。コンプライアンス要件により、BestSignは自動的に証明書を更新することはできません。証明書が期限切れとなった場合、更新手続きを完了してから、ご利用ください。',
        renewalTips3: '詳細については、',
        renewalTips4: 'BestSignの電子証明書利用規約',
        renewalTips5: 'をご確認ください。',
        tip7: 'BestSignにおける貴社の身分情報が貴社最新の企業情報と一致していないため、企業の電子証明書の更新ができませんでした。最新の企業情報で身分情報を更新ないと、企業の電子証明書を更新できません！',
        tip8: '下記リンクをコピーして、BestSignプラットフォーム上の貴社の管理者（{adminEmpName}、{adminAccount}）に送ってください。企業の身分情報を更新したら、この契約への署名を続行できます！',
        tip9: 'リンクのコピー',
        tip10: '企業情報が一致していないため、貴社の企業電子証明書の更新に失敗しました。【{currentEmpName}】の企業情報を更新してください。リンクをブラウザにコピーして操作を行ってください: {link}',
        tip11: 'コピーに成功しました',
        tip12: 'BestSignにおける貴社の身分情報が貴社最新の企業情報と一致していないため、企業の電子証明書の更新ができませんでした。最新の企業情報で身分情報を更新ないと、企業の電子証明書を更新できません！',
        tip13: '貴社は「【{groupName}】」グループの本社であり、身分情報を自由に変更することはできません。お客様のアカウントマネージャーにご連絡いただき、本人確認情報の変更手続きについてご案内を受けた後、電子証明書を正常に更新することができます！',
        tip14: '変更',
        tip15: '閉じる',
        tip16: 'BestSignにおける貴社の身分情報が貴社最新の企業情報と一致していないため、企業の電子証明書の更新ができませんでした。最新の企業情報で身分情報を更新ないと、企業の電子証明書を更新できません！',
        tip17: '申し訳ございませんが、貴社の経営状態に異常があるため、企業の電子証明書を更新できません。もし貴社の経営状態が正常であることを確認された場合は、BestSignのカスタマーサービスに電話(400-993-6665)してご連絡ください。',
    },
    infoProtectDialog: {
        userAuth: '使用刷脸服务须同意',
        titleWithSeperator: '《上上签如何保护您的个人信息》',
        title: '上上签如何保护您的个人信息',
        auth: '实名认证',
        faceSign: '签署校验',
        contentDesp: '您提交个人身份等信息（以下简称"个人信息"）时已经充分知悉并同意：',
        detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',
        detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',
        detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',
        know: '知道了',
    },
    signIdentityGuide: {
        title: '提示',
        requestYou: {
            0: '要求您',
            1: '要求您以',
        },
        tipToAuth: {
            0: '的身份进行实名认证，完成认证后即可查看和签署合同。',
            1: '进行实名认证，完成认证后即可查看和签署合同。',
        },
        needResend: '进行查看和签署合同。这和您实名的身份信息不符。请您自行联系发起方确认身份信息，并要求再次发起合同。',
        note: '注：',
        entName: '企业名称',
        identityInfo: '身份信息',
        signNeedCoincidenceInfo: '完全一致才能签署合同',
        inTheName: '以',
        of: '的',
        identity: '身份',
        nameIs: '姓名为',
        IDNumIs: '身份证号为',
    },
    pdf: {
        previewFail: '文件预览失败',
        pager: '{x}/{y}ページ目',
        parseFailed: '解析pdf文件失败，请点击“确定”重试',
        confirm: '确定',
    },
    tagManage: {
        title: '设置标签',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: '已被实名',
        assignedIdentity: '发件方填写的签约主体为：',
        entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',
        entAdminName: '管理员姓名：',
        entAdminAccount: '管理员账号：',
        applyToBeAdmin: '我要申诉成为主管理员',
        contactToJoin: '联系管理员加入企业',
        applicant: '申请人',
        inputYourName: '请输入您的姓名',
        account: '账号',
        send: '发送',
        contract: '合同',
        sendWishToJoin: '您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请',
        applyToJoin: '您还未加入该企业，无法签署该{alias}，是否要申请加入？',
        sentSuccessful: '发送成功',
        contractAlias: {
            doc: '文件',
            letter: '询征函',
            proof: '授权书',
        },
    },
    selectBizLine: {
        title: '请选择业务线',
    },
    importOffLineDoc: {
        importDoc: '导入合同',
        step0Title: '第一步：确认导入企业名称',
        step1Title: '第二步：上传Excel',
        step2Title: '第三步：上传合同文件',
        step1Info: '请先下载Excel模板，填写完成后再导入,合同数量不超过1000。',
        next: '下一步',
        entName: '企业名称',
        archiveFolder: '归档文件夹',
        downloadExcel: '下载Excel',
        uploadExcel: '上传Excel',
        reUploadExcel: '重新上传',
        step2Info: ['1. 合同文件只能是PDF或图片；', '2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；', '3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；'],
        uploadZip: '点击上传Zip',
        reUploadZip: '重新上传Zip',
        done: '确定',
        back: '返回',
        contractTitle: '合同名称',
        singerAccount: '签署人账号',
        singerName: '签署人名称',
        uploadSucTip: '上传成功，点击"确定"按钮开始导入',
        outbox: '发件箱',
        fileLessThan: '请上传小于{num}M的文件',
        fileTypeValid: '只能上传{type}格式的文件!',
    },
    download: {
        contactGetDownloadCodeTip: '请联系合同发起方获取下载码，或尝试登录本企业业务系统下载。',
        downloadCode: '下载码',
        hint: '提示',
        download: '下载',
        plsInput: '请输入',
        plsInputDownloadCode: '请输入下载码',
        downloadCodeError: '下载码错误',
        allFiles: '全部文件',
        cancel: '取 消',
        plsSelectFiles: '请先选择文件',
        publicCloudDownloadTip: '要下载的合同中包含签署人上传的其他资料，是否随合同一起下载？',
        hybridCloudDownloadTip: '要下载的合同中包含签署人上传的其他资料。',
        sameTimeDownloadAttachTip: '同时下载合同附属资料',
        downloadContract: '下载合同',
        downloadAttach: '下载合同附属资料',
    },
    commonHeader: {
        groupCertification: '集团代认证',
        goHomePage: '返回首页',
        companyPrivateSaveTypeContactTip: '您的企业采用了合同私有存储的方式当前网络已连接至合同存储服务器',
        companyPrivateSaveTypeNoContactTip: '您的企业采用了合同私有存储的方式，当前网络无法连接至合同存储服务器',
        advise: '建议：',
        checkCompanyInteralNetContact: '① 检查当前网络能否访问企业内网',
        checkContactServerNetContact: '② 检查合同存储服务器是否正常运行',
    },
    transfer: {
        list1: '列表1',
        list2: '列表2',
        maxSelectNum: '最多只能选择{maxLength}个',
    },
    poperCascader: {
        plsSelect: '请选择',
        person: '人',
        selectNumTip: '已选择{A}/{B}个{C}',
        allSelect: '全选',
    },
    authInfoChange: {
        title: '実名情報変更の検知',
        confirm: '确认',
        changeAuth: '実名更新',
        notifyAdmin: '管理者に通知',
        notifySuccess: '通知完了',
        operateSuccess: '処理完了',
        warningTip: {
            tip1: '審査の結果、貴社“{entName}”の上上簽での企業実名情報{oldAuthInfo}と最新の工商届出情報{newAuthInfo}が一致しません。',
            tip2: 'お客様が締結する電子契約の法規準拠性と効力を保証するため、最新の企業情報を使用して再度実名認証を行ってください。',
            tip3: 'この操作を行っても、お客様の現在の企業情報は却下されません。',
        },
        suggestTip: {
            tip1: 'お客様の企業がグループ組織ではない場合、専属CSMへご連絡いただくか、ベストサインカスタマーサービスホットライン400-993-6665へおかけいただけますと、実名認証情報の更新をお手伝いします。更新後に、引き続き締結を行うことができます。',
            tip2: '【管理員{adminInfo}に通知する】',
            tip3: 'をクリックし、ただちに管理員へ通知を送信して、管理員に再度実名認証を行うよう促すことができます。オフラインで通知し、業務の展開を速やかに推進することもできます。',
        },
    },
};
