export default {
    certificationRenewalDialog: {
        renewalTitle: 'تجديد الشهادة الرقمية',
        renewalTip: 'انتهت صلاحية شهادتك، لتجنب مشاكل توقيع المستندات، يرجى التجديد في الوقت المناسب',
        renewalTip2: 'إذا تغير صاحب الشهادة لهذا الحساب، يرجى تغيير هوية الحساب',
        previousIdentity: 'صاحب الشهادة:',
        previousCA: 'مصدر الشهادة الأصلي:',
        previousExpiryDate: 'تاريخ صلاحية الشهادة الأصلية:',
        previousId: 'الرقم التسلسلي للشهادة الأصلية:',
        renewal: 'موافقة على التجديد',
        reject: 'المعلومات غير صحيحة، أريد التغيير',
        rejectMessage: 'يجب رفض هوية {name} الحالية قبل متابعة تغيير هوية الحساب الحالي، هل تريد المتابعة؟',
        rejectConfirm: 'تأكيد',
        renewalTips1: 'لماذا تظهر هذه النافذة؟',
        renewalTips2: 'لضمان القوة القانونية لعقودك الإلكترونية، تستخدم جميع العقود شهادتك الرقمية لإكمال التوقيع، وتترك التوقيع وختم الوقت كدليل في الملف النهائي. يمكن استخدام الشهادة الرقمية فقط خلال فترة صلاحيتها، وبناءً على المتطلبات التنظيمية، لن تقوم المنصة بتحديث شهادتك تلقائياً. إذا انتهت صلاحية شهادتك، يجب تحديثها قبل متابعة العمل.',
        renewalTips3: 'للمزيد من التفاصيل، يمكنك مراجعة',
        renewalTips4: '《اتفاقية استخدام الشهادة الرقمية》',
        renewalTips5: 'من المنصة',
        tip7: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip8: '复制下方链接给到您企业在上上签平台的管理员（{adminEmpName}，{adminAccount}）完成企业实名变更后，可继续签署本合同！',
        tip9: '复制链接',
        tip10: '因企业信息不一致，贵司企业证书续期失败。【{currentEmpName}】邀请您完成企业信息变更，请复制链接到浏览器进行操作: {link}',
        tip11: '复制成功',
        tip12: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip13: '贵司是“【{groupName}】”的集团主企业，无法随意变更实名。请您联系您的客户经理，让其引导您完成企业实名变更后即可成功续期数字证书！',
        tip14: '去变更',
        tip15: '关闭',
        tip16: '贵司在上上签的企业实名信息与贵司最新的企业信息不一致，企业证书续期失败。以最新的企业信息完成企业实名变更后就能续期成功企业证书！',
        tip17: '很抱歉，因贵司经营状态异常，无法完成企业证书续期。若您核实过企业经营状态正常，可拨打上上签客服电话(400-993-6665)沟通。',
    },
    infoProtectDialog: {
        userAuth: 'استخدام خدمة التحقق من الوجه يتطلب الموافقة على',
        titleWithSeperator: '《كيف تحمي المنصة معلوماتك الشخصية》',
        title: 'كيف تحمي المنصة معلوماتك الشخصية',
        auth: 'التحقق من الهوية',
        faceSign: 'التحقق من التوقيع',
        contentDesp: 'عند تقديم معلوماتك الشخصية (المشار إليها فيما يلي باسم "المعلومات الشخصية")، أنت تدرك وتوافق على:',
        detailTip1: '(1) لإكمال {title} الخاص بك، لقد فوضت مؤسستك للقيام بنفسها أو تفويض المنصة لتقديم معلوماتك الشخصية إلى الجهات الأخرى التي تقدم خدمات لتحقيق التوقيع الإلكتروني (مثل هيئات CA والموثقين وغيرهم)؛',
        detailTip2: '(2) بالإضافة إلى التفويض في (1)، أنت توافق بشكل منفصل على تقديم معلومات وجهك للتحقق من هوية {title} (أي التحقق من وجه الممثل القانوني)، وتوافق على أن تقوم المنصة فقط بمراجعة وتخزين واسترجاع ومشاركة معلومات وجهك لتوفير خدمة التوقيع الإلكتروني وإصدار الشهادات اللاحقة. إذا كنت لا توافق على محتوى هذا البند، يجب عليك التوقف فوراً عن تقديم معلومات وجهك واختيار طريقة تحقق أخرى؛',
        detailTip3: '(3) باستثناء ما ورد في (1) و(2) والحالات التي ينص عليها القانون، لن تقدم المنصة معلوماتك الشخصية لأي طرف ثالث دون تفويض منك.',
        know: 'فهمت',
    },
    signIdentityGuide: {
        title: 'تنبيه',
        requestYou: {
            0: 'يطلب منك',
            1: 'يطلب منك بصفة',
        },
        tipToAuth: {
            0: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.',
            1: 'إجراء التحقق من الهوية، بعد إكمال التحقق يمكنك عرض وتوقيع العقد.',
        },
        needResend: 'عرض وتوقيع العقد. هذا لا يتطابق مع معلومات هويتك المتحقق منها. يرجى التواصل مع المرسل للتأكد من معلومات الهوية وطلب إعادة إرسال العقد.',
        note: 'ملاحظة:',
        entName: 'اسم المؤسسة',
        identityInfo: 'معلومات الهوية',
        signNeedCoincidenceInfo: 'يجب أن تتطابق تماماً لتوقيع العقد',
        inTheName: 'باسم',
        of: 'من',
        identity: 'الهوية',
        nameIs: 'الاسم هو',
        IDNumIs: 'رقم الهوية هو',
    },
    pdf: {
        previewFail: 'فشل عرض الملف',
        pager: 'الصفحة {x} من {y}',
        parseFailed: 'فشل تحليل ملف PDF، يرجى النقر على "تأكيد" للمحاولة مرة أخرى',
        confirm: 'تأكيد',
    },
    tagManage: {
        title: 'إعداد العلامات',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: 'تم التحقق من الهوية',
        assignedIdentity: 'الطرف الموقع الذي حدده المرسل هو:',
        entBeenAuthenticated: 'تم التحقق من هوية هذه المؤسسة، معلومات المدير الرئيسي كما يلي:',
        entAdminName: 'اسم المدير:',
        entAdminAccount: 'حساب المدير:',
        applyToBeAdmin: 'أريد تقديم استئناف لأصبح المدير الرئيسي',
        contactToJoin: 'التواصل مع المدير للانضمام للمؤسسة',
        applicant: 'مقدم الطلب',
        inputYourName: 'يرجى إدخال اسمك',
        account: 'الحساب',
        send: 'إرسال',
        contract: 'العقد',
        sendWishToJoin: 'يمكنك تقديم استئناف لتصبح مديراً عبر حسابك، أو إرسال طلب انضمام للمؤسسة إلى المدير',
        applyToJoin: 'لم تنضم لهذه المؤسسة بعد، لا يمكنك توقيع {alias}، هل تريد تقديم طلب انضمام؟',
        sentSuccessful: 'تم الإرسال بنجاح',
        contractAlias: {
            doc: 'مستند',
            letter: 'خطاب استفسار',
            proof: 'تفويض',
        },
    },
    selectBizLine: {
        title: 'يرجى اختيار خط الأعمال',
    },
    importOffLineDoc: {
        importDoc: 'استيراد عقد',
        step0Title: 'الخطوة الأولى: تأكيد اسم المؤسسة للاستيراد',
        step1Title: 'الخطوة الثانية: رفع ملف Excel',
        step2Title: 'الخطوة الثالثة: رفع ملفات العقود',
        step1Info: 'يرجى تحميل نموذج Excel أولاً، ثم ملؤه ورفعه. لا يتجاوز عدد العقود 1000.',
        next: 'التالي',
        entName: 'اسم المؤسسة',
        archiveFolder: 'مجلد الأرشفة',
        downloadExcel: 'تحميل Excel',
        uploadExcel: 'رفع Excel',
        reUploadExcel: 'إعادة الرفع',
        step2Info: ['1. يجب أن تكون ملفات العقود بصيغة PDF أو صور؛', '2. ضع جميع ملفات العقود في مجلد واحد، ثم اضغط المجلد كملف zip (لا يتجاوز 150 ميجابايت)؛', '3. يجب أن تتطابق أسماء الملفات مع الامتدادات (مثل .pdf) مع الأسماء المدخلة في ملف Excel في الخطوة الثانية؛'],
        uploadZip: 'انقر لرفع ملف Zip',
        reUploadZip: 'إعادة رفع Zip',
        done: 'تأكيد',
        back: 'رجوع',
        contractTitle: 'اسم العقد',
        singerAccount: 'حساب الموقع',
        singerName: 'اسم الموقع',
        uploadSucTip: 'تم الرفع بنجاح، انقر على زر "تأكيد" لبدء الاستيراد',
        outbox: 'صندوق الصادر',
        fileLessThan: 'يرجى رفع ملف أقل من {num} ميجابايت',
        fileTypeValid: 'يمكن رفع ملفات بصيغة {type} فقط!',
    },
    download: {
        contactGetDownloadCodeTip: 'يرجى التواصل مع مرسل العقد للحصول على رمز التحميل، أو محاولة تسجيل الدخول إلى نظام مؤسستك للتحميل.',
        downloadCode: 'رمز التحميل',
        hint: 'تنبيه',
        download: 'تحميل',
        plsInput: 'يرجى الإدخال',
        plsInputDownloadCode: 'يرجى إدخال رمز التحميل',
        downloadCodeError: 'رمز التحميل غير صحيح',
        allFiles: 'جميع الملفات',
        cancel: 'إلغاء',
        plsSelectFiles: 'يرجى اختيار الملفات أولاً',
        publicCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين، هل تريد تحميلها مع العقود؟',
        hybridCloudDownloadTip: 'تحتوي العقود المراد تحميلها على مرفقات أخرى تم رفعها من قبل الموقعين.',
        sameTimeDownloadAttachTip: 'تحميل مرفقات العقود في نفس الوقت',
        downloadContract: 'تحميل العقد',
        downloadAttach: 'تحميل مرفقات العقد',
    },
    commonHeader: {
        groupCertification: 'تحقق المجموعة',
        goHomePage: 'العودة للصفحة الرئيسية',
        companyPrivateSaveTypeContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود وتم الاتصال بخادم تخزين العقود',
        companyPrivateSaveTypeNoContactTip: 'مؤسستك تستخدم التخزين الخاص للعقود ولا يمكن الاتصال بخادم تخزين العقود',
        advise: 'اقتراحات:',
        checkCompanyInteralNetContact: '① التحقق من إمكانية الوصول إلى شبكة المؤسسة الداخلية',
        checkContactServerNetContact: '② التحقق من حالة تشغيل خادم تخزين العقود',
    },
    transfer: {
        list1: 'القائمة 1',
        list2: 'القائمة 2',
        maxSelectNum: 'يمكن اختيار {maxLength} كحد أقصى',
    },
    poperCascader: {
        plsSelect: 'يرجى الاختيار',
        person: 'شخص',
        selectNumTip: 'تم اختيار {A}/{B} {C}',
        allSelect: 'اختيار الكل',
    },
    authInfoChange: {
        title: 'الكشف عن تغيير معلومات الهوية',
        confirm: 'تأكيد',
        changeAuth: 'تحديث الهوية',
        notifyAdmin: 'إخطار المدير',
        notifySuccess: 'تم الإخطار بنجاح',
        operateSuccess: 'تمت العملية بنجاح',
        warningTip: {
            tip1: 'بعد المراجعة، تبين أن معلومات هوية مؤسستك "{entName}" على المنصة {oldAuthInfo} لا تتطابق مع أحدث معلومات السجل التجاري {newAuthInfo}.',
            tip2: 'لضمان قانونية وفعالية عقودك الإلكترونية، يرجى إعادة التحقق من الهوية باستخدام أحدث معلومات المؤسسة.',
            tip3: 'لن يؤدي هذا الإجراء إلى رفض معلومات مؤسستك الحالية.',
        },
        suggestTip: {
            tip1: 'إذا كانت مؤسستك جزءاً من مجموعة، يرجى التواصل مع مدير العلاقات المخصص لك، أو الاتصال بخدمة العملاء على الرقم 400-993-6665 للمساعدة في تحديث معلومات التحقق من الهوية. يمكنك متابعة التوقيع بعد التحديث.',
            tip2: 'انقر على [إخطار المدير {adminInfo}]،',
            tip3: 'لإرسال إشعار فوري للمدير لتوجيهه لإعادة التحقق من الهوية. يمكنك أيضاً الإخطار شخصياً لتسريع العمل.',
        },
    },
};
