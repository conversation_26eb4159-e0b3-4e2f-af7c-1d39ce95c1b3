export default {
    certificationRenewalDialog: {
        renewalTitle: 'Digital certificate renewal',
        renewalTip: 'Your certificate has expired, please renew it in time to avoid document signing',
        renewalTip2: 'If the entity holding the certificate for this account has changed, please update the real-name authentication of the account.',
        previousIdentity: 'Subject holding the certificate:',
        previousCA: 'Original certification authority: ',
        previousExpiryDate: 'Validity period of the original certificate:',
        previousId: 'Original certificate serial number:',
        renewal: 'Agree to renew',
        reject: 'The personal real-name information is incorrect; I need to make changes.',
        rejectMessage: "To proceed with changing the current account's real-name authentication, the existing real-name authentication of '{name}' needs to be rejected first. Would you like to continue?",
        rejectConfirm: 'Confirm',
        renewalTips1: 'Why is this notification displayed?',
        renewalTips2: 'To ensure the legal validity of your electronic contracts, all contracts you participate in will be signed using your digital certificate, with signatures and timestamps embedded in the final contract document as evidence.Digital certificates can only be used within their validity period. Due to compliance requirements, BestSign will not automatically renew your certificate. If your certificate expires, you will need to complete the renewal process before proceeding with further business operations.',
        renewalTips3: 'For detailed information, please refer to the',
        renewalTips4: 'BestSign Digital Certificate Usage Agreement.',
        renewalTips5: '',
        tip7: 'Your company\'s identity verification information in BestSign does not match your company\'s latest corporate information, resulting in failure to renew your enterprise certificate. After updating your identity verification information to match the latest corporate details, you will be able to successfully renew your enterprise certificate!',
        tip8: 'Please copy the link below to your company\'s administrator ({adminEmpName}, {adminAccount}) on the BestSign platform. After completing the identity verification update, you can continue signing this contract!',
        tip9: 'Copy link',
        tip10: 'Due to inconsistent company information, your company\'s certificate renewal has failed. [{currentEmpName}] invites you to complete the company information update. Please copy the link to your browser to proceed: {link}',
        tip11: 'Copied Successfully',
        tip12: 'Your company\'s identity verification information on BestSign is inconsistent with your company\'s latest information, resulting in the failure of company certificate renewal. After completing the identity verification information update with your latest company information, the company certificate can be successfully renewed!',
        tip13: 'Your company is the main enterprise of the group \"【{groupName}】\" and cannot arbitrarily change the identity verification information. Please contact your account manager to guide you through the identity verification change process, after which you can successfully renew your digital certificate!',
        tip14: 'Go to Change',
        tip15: 'Close',
        tip16: 'Your company\'s identity verification information in BestSign is inconsistent with your company\'s latest corporate information, resulting in the failure of enterprise certificate renewal. Once you complete the identity verification update with the latest corporate information, you will be able to successfully renew your enterprise certificate!',
        tip17: 'We regret to inform you that the enterprise certificate renewal cannot be completed due to your company\'s abnormal business status. If you have verified that your company\'s business status is normal, please call BestSign customer service (************) for assistance.',
    },
    infoProtectDialog: {
        userAuth: '使用刷脸服务须同意',
        titleWithSeperator: '《上上签如何保护您的个人信息》',
        title: '上上签如何保护您的个人信息',
        auth: '实名认证',
        faceSign: '刷脸签署',
        contentDesp: '您提交个人身份等信息（以下简称"个人信息"）时已经充分知悉并同意：',
        detailTip1: '（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；',
        detailTip2: '（2）除（1）授权内容外，您单独同意提交您的人脸信息用于本次{title}的意愿性认证（即法定代表人刷脸认证），并同意上上签仅为提供电子签约服务以及后续出证的需要，审核、储存、调取、共享等方式处理您的人脸信息。若您不同意本条所述内容，您应立即停止提交您的人脸信息，并选择其它认证方式；',
        detailTip3: '（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。',
        know: '知道了',
    },
    signIdentityGuide: {
        title: 'Tip',
        requestYou: {
            0: 'requires you',
            1: 'requires you',
        },
        tipToAuth: {
            0: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication',
            1: 'to perform real-name authentication, and you can view and sign the contract after completing the authentication.',
        },
        needResend: 'to view and sign the contract.This does not match your real-name identity information. Please contact the initiator yourself to confirm your identity information and request to initiate the contract again.',
        note: 'Note:',
        entName: 'name of enterprise',
        identityInfo: 'Identity Information',
        signNeedCoincidenceInfo: 'The information needs to be completely consistent to sign the contract',
        inTheName: 'As',
        of: '',
        identity: '',
        nameIs: 'name as',
        IDNumIs: 'ID number as',
    },
    pdf: {
        previewFail: 'File preview failed',
        pager: 'Page {x}，{y} in total',
        parseFailed: 'Failed to parse the pdf file, please click "OK" to try again',
        confirm: 'Confirm',
    },
    tagManage: {
        title: 'Set label',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: 'Has been real-named',
        assignedIdentity: 'The contract subject filled in by the sender is:',
        entBeenAuthenticated: 'The company has been real-named, and the main administrator information is as follows:',
        entAdminName: 'Administrator name:',
        entAdminAccount: 'Administrator account:',
        applyToBeAdmin: 'I want to appeal',
        contactToJoin: 'Join the company',
        applicant: 'Applicant',
        inputYourName: 'Please enter your name',
        account: 'Account',
        send: 'Send',
        contract: 'contract',
        sendWishToJoin: 'You can become an administrator through account appeal, or you can send an application to the administrator to join the company',
        applyToJoin: 'You have not joined the company and cannot sign the {alias}. Do you want to apply for joining?',
        sentSuccessful: 'Sent successfully',
        contractAlias: {
            doc: '文件',
            letter: '询征函',
            proof: '授权书',
        },
    },
    selectBizLine: {
        title: 'Please select line of business',
    },
    importOffLineDoc: {
        importDoc: 'Import contracts',
        step0Title: 'Step 1: Confirm the name of the imported company',
        step1Title: 'Step 2: Upload Excel',
        step2Title: 'Step 3: Upload the contract file',
        step1Info: 'Please download the Excel template first and fill it out before importing, the number of contracts should not exceed 1000.',
        next: 'Next step',
        entName: 'Company Name',
        archiveFolder: 'Archived folder',
        downloadExcel: 'Download Excel',
        uploadExcel: 'Upload Excel',
        reUploadExcel: 'Re-upload',
        step2Info: ['1. Contract files can only be PDF or images；', '2. After placing all contract files in a folder, compress the folder into a zip (no more than 150M)；', '3. The file name including the file extension (such as .pdf) should be consistent with the file name in Excel in the second step；'],
        uploadZip: 'Click Upload Zip',
        reUploadZip: 'Re-upload Zip',
        done: 'confirm',
        back: 'Back',
        contractTitle: 'Contract name',
        singerAccount: 'Signer account',
        singerName: 'Signatory name',
        uploadSucTip: 'Upload is successful, click "OK" button to start importing',
        outbox: 'Outbox',
        fileLessThan: 'Please upload files smaller than {num}M',
        fileTypeValid: 'Only {type} format files can be uploaded!',

    },
    download: {
        contactGetDownloadCodeTip: 'Please contact the contract initiator to obtain the download code, or try to log in to our business system to download.',
        downloadCode: 'download code',
        hint: 'tips',
        download: 'download',
        plsInput: 'please put in',
        plsInputDownloadCode: 'Please enter the download code',
        downloadCodeError: 'wrong download code',
        allFiles: 'all files',
        cancel: 'cancel',
        plsSelectFiles: 'please select a file first',
        publicCloudDownloadTip: 'The contract to be downloaded contains otherdocuments uploaded by the signatory. Do you want to download it with the contract?',
        hybridCloudDownloadTip: 'The contract to be downloaded contains other documents uploaded by the signer..',
        sameTimeDownloadAttachTip: 'download other documents attached to the contract at the same time',
        downloadContract: 'download contract',
        downloadAttach: 'download other documents attached to the contract ',
    },
    commonHeader: {
        groupCertification: 'group certification',
        goHomePage: 'Back to homepage',
        companyPrivateSaveTypeContactTip: 'Your company adopts contract private storage. The current network is connected to the storage server',
        companyPrivateSaveTypeNoContactTip: 'Your company adopts contract private storage mode and the current network cannot connect to the storage server',
        advise: 'advise：',
        checkCompanyInteralNetContact: '① Check whether the current network can access the corporate intranet',
        checkContactServerNetContact: '② Check the contract storage server for proper operation',
    },
    transfer: {
        list1: 'List 1',
        list2: 'List 2',
        maxSelectNum: 'Can only choose at most {maxLength}',
    },
    poperCascader: {
        plsSelect: 'please select',
        person: 'people',
        selectNumTip: '{A}/{B} of {C} selected',
        allSelect: 'select all',
    },
    authInfoChange: {
        title: 'Real name change detection',
        confirm: 'Confirm',
        changeAuth: 'Real name update',
        notifyAdmin: 'Notify the administrator',
        notifySuccess: 'Success',
        operateSuccess: 'Success',
        warningTip: {
            tip1: "It is found that the real name information {oldAuthInfo} of your company '{entName}' on BestSign platform is not consistent with the latest information {newAuthInfo} at the Industry and Commerce Bureau. ",
            tip2: 'To ensure compliance and effectiveness of your signed e-contracts, please use the latest business information to go through real-name authentication again. ',
            tip3: 'This operation will not affect your current corporate information. ',
        },
        suggestTip: {
            tip1: "If your company has a group structure, please contact your exclusive CSM, or call BestSign's customer service hotline ************ to update information for real-name authentication, after which you can continue to sign.",
            tip2: 'By clicking [Notify Administrator{adminInfo}],点击【通知管理员{adminInfo}】，',
            tip3: ' you can immediately send a notification to the administrator to guide him/her to go through real-name authentication again. You can also notify offline to ensure timely business activities.',
        },
    },
};
