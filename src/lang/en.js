
// 语言为英语时的文案，暂只在wap打开短链接时生效
import utils from './module/utils/utils-en.js';
import mixin from './module/mixins/mixins-en.js';
import components from './module/components/components-en.js';

import console from './module/console/console-en.js';
import userCentral from './module/usercentral/usercentral-en.js';
import docList from './module/docList/docList-en.js';
import home from './module/home/<USER>';
import sign from './module/sign/sign-en.js';
import entAuth from './module/entAuth/entAuth-en.js';
import docTranslation from './module/docTranslation/docTranslation-en.js';
import consts from '@/lang/module/consts/en';

export default {
    ...utils,
    ...mixin,
    ...components,
    ...docTranslation,
    footerAd: {
        title: 'Page jump prompt',
        content1: 'You are going to visit a third-party promotion page',
        content2: 'Do you want to continue?',
        bankContent: '即将进入宁波银行"容易贷"企业贷款介绍页面',
        bankTip1: '让宁波银行主动给我打电话',
        bankTip2: '向我发送一条短信，介绍如何办理',
        bankFooter: '加宁波银行专属客服，一对一服务我',
        cancel: 'Cancel',
        continue: 'Continue',
    },
    commonFooter: {
        record: 'ICP main body record number: Zhejiang ICP No. ********',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'Open platform',
        aboutBestSign: 'About BestSign',
        contact: 'Contact us',
        recruitment: 'Recruitment',
        help: 'Help Center',
        copyright: 'Copyright',
        company: 'HangZhou BestSign Ltd.',
        ssqLogo: 'BestSign bottom bar logo',
        provideTip: 'E-signing service is provided by',
        ssq: ' BestSign',
        provide: '',
        signHotline: 'Service hotline',
        langSwitch: 'Language',
    },
    login: {
        pswLogin: 'Password Login',
        usePswLogin: 'Log in with password',
        verifyLogin: 'Verification Code',
        useVerifyLogin: 'Log in with verification code',
        scanLogin: 'Scan Code Login',
        scanFailure: 'QR code has expired, please retry',
        scanSuccess: 'Scan successfully',
        scanLoginTip: 'Please use the app on the APP to scan the login',
        appLoginTip: 'Please click login in the app BestSign',
        downloadApp: 'Download BestSign APP',
        forgetPsw: 'Forget?',
        login: 'Sign in',
        noAccount: 'No account?',
        registerNow: 'Sign up now',
        accountPlaceholder: 'Telephone or Email',
        passwordPlaceholder: 'Password',
        pictureVer: 'Please fill in the content in the picture',
        verifyCodePlaceholder: 'Please input 6-dight code',
        getVerifyCode: 'Send',
        noRegister: 'Not registered yet',
        or: 'or',
        errAccountOrPwdTip: 'The password you entered does not match the account number?',
        errAccountOrPwdTip2: 'The password you entered does not match the account',
        errEmailOrTel: 'Please input correct Email or telephone number!',
        errPwd: 'Please input correct password!',
        verCodeFormatErr: 'Verification Code Error',
        grapVerCodeErr: 'Graphic verification code error',
        grapVerCodeFormatErr: 'Graphic verification code format error',
        lackAccount: 'Please input account first',
        lackGrapCode: 'PPlease fill in the graphic verification code first.',
        getVerCodeTip: 'Please get verification code',

        loginView: 'Login and View Contract',
        regView: 'Register and View Contract',
        takeViewBtn: 'Login and sign',
        resendCode: 'Reacquire',
        regTip: 'After filling in the correct verification code, BestSign will  be to create an account for you',
        haveRead: 'I have read and agreed',
        bestsignAgreement: 'BestSign Service Agreement',
        and: 'and',
        digitalCertificateAgreement: 'Digital certificate usage agreement',
        privacyPolicy: 'Privacy Policy',
        sendSuc: 'Send successfully',
        lackVerCode: 'Please Enter the Verification Code First',
        lackPsw: 'Please enter your password first',
        notMatch: 'The password and account entered do not match',
        cookieTip: 'Unable to read and write cookies, please check if there is no trace/incognito mode or other cookies disabled operation',
        wrongLink: 'Illegal link',
        footerTips: 'Electronic sign-up service provided by <span>BestSign</span>',
        bestSign: 'BestSign',
        bestSignDescription: 'Electronic contracting industry leader',
        /** 忘记密码 /forgotPassword start */
        forgetPswStep: 'Verify registered account | Reset password',
        pictureVerCodeInput: 'Graphic verification code | Please fill in the contents of the picture',
        accountInput: 'Account | Please enter your account',
        smsCodeInput: 'Verification code | Get verification code',
        haveRegistereLoginNow: 'Have registered， |  login now',
        nextStep: 'Next | Submit',
        setNewPasswordInput: 'Set new password | 6-18-digit number or letter',
        passwordResetSucceeded: ' Password reset succeeded',
        /** 忘记密码 /forgotPassword end */
        accountNotRegistered: 'Account not registered',
        loginAndDownload: 'login and download contract',
        registerAndDownload: 'register and download contract',
        inputPhone: 'please input phone number',
        readContract: 'read contract',
        errorPhone: 'Mobile phone format error',
        companyCert: 'Conducting corporate certification',
        regAndCompanyCert: 'Register and conduct corporate certification',
    },
    ...sign,
    handwrite: {
        title: 'Directly on the screen',
        picSubmitTip: 'Signature image submitted successfully.',
        settingDefault: 'Set as default',
        replaceAllSignature: 'Used for all signatures',
        replaceAllSeal: 'For all seals',
        canUseSeal: 'My Seals',
        applyForSeal: 'Apply for using the seals',
        moreTip: 'Your handwritten signature will be saved as your default signature, used only for contract signing. Management path: [User Center -> Signature Management]',
        uploadPic: 'Upload image',
        use: 'Use',
        clickExtend: 'Click right arrow to extend area',
        rewrite: 'Rewrite',
        upload: 'Upload signature image',
        uploadTip1: 'Tip: When uploading a signature image, please make sure the signature fills the entire image',
        uploadTip2: 'Please use dark-colored or pure black text for your signature.',
        cancel: 'Cancel',
        confirm: 'Confirm',
        upgradeBrowser: 'The browser does not support handwriting, please upgrade your browser.',
        submitTip: 'Handwritten signature submitted successfully',
        needWrite: 'Please write your name first',
        needRewrite: "Can't identify the signature, please try rewrite",
        title2: 'Hand drawn your signature',
        QRCode: 'By scanning the QR',
        ok: 'OK',
        clearTips: 'Please write a clearly identifiable signature',
        isBlank: 'The canvas is empty, please hand-paint the signature before submitting!',
        success: 'Handwritten signature submitted successfully',
        signNotMatch: 'Please write your signature in block letters and keep it consistent with your real name ID information.',
        signNotMatchExact: 'The {numList} word does not match, please rewrite',
        msg: {
            successToUser: 'The new signature has taken effect, please go to the Web User Center - Signature Management',
            successToSign: 'The new signature has taken effect, please check the contract signing page',
            cantGet: 'Can\'t get a signature, try using a different browser!',
        },
    },
    common: {
        aboutBestSign: 'About BestSign',
        contact: 'Contact us',
        recruitment: 'Recruitment',
        copyright: 'Copyright',
        advice: 'Advice',
        notEmpty: 'Can not be empty',
        enter6to18n: 'Please enter 6-18 digits, letters',
        ssqDes: 'Leader of electronic signing cloud platform',
        openPlatform: 'Open platform',
        company: 'HangZhou BestSign Ltd.',
        help: 'Help Center',

        errEmailOrTel: 'Please input correct Email or telephone number!',
        verCodeFormatErr: 'Verification Code Error',
        signPwdType: 'Please enter 6 digits',
        enterActualEntName: 'Please fill in the real business name',
        enterCorrectName: 'Please enter the correct business name',
        enterCorrectPhoneNum: 'Please enter the correct cell phone number',
        enterCorrectEmail: 'Please enter the correct e-mail',
        imgCodeErr: 'Verification code error',
        enterCorrectIdNum: 'Please enter the correct ID number',
        enterCorrectFormat: 'Please enter the correct format',
        enterCorrectDateFormat: 'Please enter the correct date format',
    },
    entAuth: {
        ...entAuth,
        entCertification: 'Enterprise real name certification',
        subBaseInfo: 'Submit basic information',
        corDocuments: 'Corporate documents',
        license: 'License',
        upload: 'Click to upload',
        uploadLimit: 'Images are only available in jpeg, jpg, png formats and no larger than 10M',
        hi: 'Hi',
        exit: 'Exit',
        help: 'Help',
        hotline: 'Service hotline',
        acceptProtectingMethod: 'I accept the method of protecting personally identifiable information I submit',
        comfirmSubmit: 'Confirm submission',
        cerficated: 'Certification completed',
        entName: 'Enterprise name',
        serialNumber: 'Integer serial number',
        validity: 'Validity',
        nationalNo: 'National Registration No.',
        corporationName: 'Name of legal representative',
        city: 'City',
        entCertificate: 'Enterprise Real Name Certificate',
        certificationAuthority: 'Certification Authority',
        bestsignPlatform: 'BestSign electronic signing cloud platform',
        notIssued: 'Not issued',
        date: '{year}-{month}-{day}',
        congratulations: 'Congratulations on your successful completion of the enterprise real name certification',
        continue: 'Continue',
        rejectMessage: 'For the following reasons, the data audit did not pass, please check',
        recertification: 'Recertification',
        waitMessage: 'Customer service will be reviewed within one business day, please be patient',
    },
    personalAuth: {
        info: 'info',
        submitPicError: '请上传照片后再使用',
    },
    home: {
        ...home,
        home: 'Home',
        contractDrafting: 'Contract drafting',
        contractManagement: 'Contract',
        userCenter: 'Admin',
        service: 'Service',
        enterpriseConsole: 'Enterprise console',
        groupConsole: 'Group console',
        startSigning: 'Start signing',
        contractType: 'Ordinary contract| Template contract',
        sendContract: 'Start now',
        shortcuts: 'shortcuts | No shortcuts for any documents',
        setting: 'Set up immediately | Create more shortcuts',
        signNum: 'Monthly summary of signatures and deliveries',
        contractNum: 'contracts sent | contracts signed',
        contractInFormation: 'You have not sent or signed any contracts this month',
        type: 'company | person ',
        basicInformation: 'basic information',
        more: 'more',
        certified: 'certified | uncertified',
        account: 'Account',
        time: 'Time of creation |Time of registration',
        day: 'day | month',
        sendContractNum: 'deliveries | signatures',
        num: '',
        realName: 'create a business account with real name now | create an individual account with real name now',
        update: 'Product update',
        mark: 'Would you like to recommend BestSign to your friends and colleagues? Please rate your choice from 0 to 10.',
        countDes: {
            1: 'Available: for the enterprise contract',
            2: '',
            3: 'for the private contract',
            4: '',
        },
        chargeNow: 'Charge now',
        myRechargeOrder: 'My recharge order',
        statusTip: {
            1: 'Need me to operate',
            2: 'Need  others to sign',
            3: 'Signing is about to close',
            4: 'Signing complete',
        },
        useTemplate: 'Use template',
        useLocalFile: 'Upload local file',
    },
    docDetail: {
        canNotOperateTip: 'Unable to {operate} contract',
        shareSignLink: 'share the signing link',
        faceSign: 'signing with face scan',
        faceFirstVerifyCodeSecond: 'Priority sign with facial verification, alternate sign with SMS code verification',
        contractRecipient: 'contract receiver',
        personalOperateLog: 'individual contract operation log',
        recordDialog: {
            date: 'date',
            user: 'user',
            operate: 'operation',
            view: 'view',
            download: 'download',
        },
        remarks: 'notes',
        operateRecords: 'record of operation',
        borrowingRecords: 'borrowing records',
        currentHolder: 'current holder',
        currentEnterprise: 'company under the current account',
        companyInterOperationLog: 'internal operation log of the company',
        receiverMap: {
            sender: 'contract sender',
            signer: 'contract receiver',
            ccUser: 'contract copied to',
        },
        downloadCode: 'contract download code',
        noTagToAddHint: 'No labels yet; please go to business control panel to add them',
        requireFieldNotAllowEmpty: 'required fields cannot be empty',
        modifySuccess: 'modification successful',
        uncategorized: 'unclassified',
        notAllowModifyContractType: 'The contract type is not allowed to be modified for the contract under {type}',
        setTag: 'set labels',
        contractTag: 'contract labels',
        plsInput: 'please put in',
        plsInputCompanyInternalNum: 'please enter the internal business number',
        companyInternalNum: 'Internal business number',
        none: 'none',
        plsSelect: 'Please select',
        modify: 'modify',
        contractDetailInfo: 'contract details',
        slideContentTip: {
            signNotice: 'signing instructions',
            contractAncillaryInformation: 'attachments to the contract',
            content: 'content',
            document: 'document',
        },
        downloadDepositConfirmTip: {
            title: 'The signing proof page you downloaded is a non-sensitive version, with  private information hidden and not applicable for court proceedings. If you need to use it for court proceedings, please contact us for the full version.',
            hint: 'tips',
            confrim: 'continue to download',
            cancel: 'cancel',
        },
        downloadTip: {
            title: 'As the contract is not yet completed, you are downloading a preview file of the contract that is not yet in effect',
            hint: 'tips',
            confirm: 'confirm',
            cancel: 'cancel',
        },
        transferSuccessGoManagePage: 'The transfer is successful and will return to the contract management page',
        claimSign: 'retrieve and sign',
        downloadDepositPageTip: 'download signing proof page (non-sensitive version)',
        resend: 'resend',
        proxySign: 'entrusted signing',
        notPassed: 'rejected',
        approving: 'under review',
        signning: 'signing in progress',
        notarized: 'notarized',
        currentFolder: 'current folder',
        archive: 'contract filed',
        deadlineForSigning: 'deadline for signing',
        endFinishTime: 'signing completed/completion date',
        contractImportTime: 'contract import time',
        contractSendTime: 'contract delivery time',
        back: 'back',
        contractInfo: 'Contract information',
        basicInfo: 'Basic information',
        contractNum: 'Contract No.',
        sender: 'Sender',
        personAccount: 'Personal account',
        entAccount: 'Enterprise account',
        operator: 'Operator',
        signStartTime: 'Start signing time',
        signDeadline: 'Signing deadline',
        contractExpireDate: 'Contract expiration date',
        // none: 'None',
        edit: 'Modify',
        settings: 'Set up',
        from: 'Source',
        folder: 'Folder',
        contractType: 'Contract type',
        reason: 'Reason',
        sign: 'Sign',
        approval: 'approval',
        viewAttach: 'View the attached pages',
        downloadContract: 'Download the contract',
        downloadAttach: 'Download the attached page',
        print: 'Print',
        certificatedTooltip: 'The contract and related evidence have been documented in the judicial chain of the Hangzhou Internet Court',
        needMeSign: 'Need me to sign',
        needMeApproval: 'Need my approval',
        inApproval: 'Sub-judice…',
        needOthersSign: 'Need others to sign',
        signComplete: 'Signing complete',
        signOverdue: 'Overdue signing',
        rejected: 'Rejected',
        revoked: 'Revoked',
        contractCompleteTime: 'Signing complete time',
        contractEndTime: 'Signing end time',
        reject: 'Reject',
        revoke: 'Revoke',
        download: 'Download',
        viewSignOrders: 'View the order of signing',
        viewApprovalProcess: 'View approval process',
        completed: 'Signing completed',
        cc: 'Cc',
        ccer: 'Copy party',
        signer: 'Signer',
        signSubject: 'Sign-up subject',
        signSubjectTooltip: 'The signing subject filled in by the sender is',
        user: 'User',
        IDNumber: 'ID number',
        state: 'State',
        time: 'Time',
        notice: 'Remind',
        detail: 'Details',
        RealNameCertificationRequired: 'Real-name certification required',
        RealNameCertificationNotRequired: 'No real name certification required',
        MustHandwrittenSignature: 'Must sign with handwritten signature',
        handWritingRecognition: 'Turn on handwriting recognition',
        privateMessage: 'Message',
        attachment: 'Attachment',
        rejectReason: 'Reason',
        notSigned: 'Not signed',
        notViewed: 'Not viewed',
        viewed: 'Viewed',
        signed: 'Signed',
        viewedNotSigned: 'Read not signed',
        notApproval: 'Unapproved',
        remindSucceed: 'Reminder message sent',
        reviewDetails: 'Approval details',
        close: 'Shut Down',
        entInnerOperateDetail: 'Internal operation details',
        approve: 'Agree',
        disapprove: 'Reject',
        applySeal: 'Application for printing',
        applied: 'Already applied',
        apply: 'Application',
        toOtherSign: 'Transfer to someone else to sign',
        handOver: 'Transfer',
        approvalOpinions: 'Approval comments',
        useSeal: 'Print',
        signature: 'Signature',
        use: 'Use',
        date: 'Date',
        fill: 'Fil in',
        times: 'Secondary',
        place: 'Place',
        contractDetail: 'Contract details',
        viewMore: 'View more',
        collapse: 'Fold',
        signLink: 'Signing a link',
        saveQRCode: 'Save the QR code or copy the link and share it with the signatory',
        signQRCode: 'Sign the link QR code',
        copy: 'Copy',
        copySucc: 'Successful copy',
        copyFail: 'Replication copy',
        certified: 'Certified',
        unCertified: 'Uncertified',
        claimed: 'Claimed',
    },
    uploadFile: {
        thumbnails: 'Thumbnail',
        isUploading: 'Uploading',
        move: 'Move',
        delete: 'Delete',
        replace: 'Replace',
        tip: 'Tip',
        understand: 'Get it',
        totalPages: '{page} in total',
        uploadFile: 'Upload local file',
        matchErr: 'The server has a small gap, please try again later.',
        inUploadingDeleteErr: 'Please delete after uploading',
        timeOutErr: 'Request timed out',
        imgUnqualified: 'Image format does not meet the requirements',
        imgBiggerThan20M: 'Image size cannot exceed 20MB!',
        error: 'Error',
        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',

    },
    contractInfo: {
        internalNumber: 'Internal business number',
        contractName: 'Contract name',
        contractNameTooltip: 'Contract name please do not contain special characters and is no longer than 100 words long',
        contractType: 'Contract type',
        toSelect: 'Please choose',
        contractTypeErr: 'The current contract type has been deleted. Please re-select the contract type.',
        signDeadLine: 'Signing deadline',
        signDeadLineTooltip: 'If the contract is not signed before this date, it cannot be continued',
        selectDate: 'Select date and time',
        contractExpireDate: 'Contract expiration date',
        expireDateTooltip: 'Expiration time in the contents of the contract for your subsequent contract management',
        necessary: 'Necessary',
        notNecessary: 'Optional',
        dateTips: 'The contract expiration date has been automatically identified for you, please confirm',
        contractTitleErr: 'Contract name please do not contain special characters',
        contractTitleLengthErr: 'Please do not exceed 100 words in the length of the contract name.',
    },
    userCentral: userCentral,
    template: {
        templateList: {
            linkBoxTip: 'Associated Cabinet ID：',
        },
        dynamicTemplateUpdate: {
            title: 'New function of dynamic template is online',
            newVersionDesc: 'The new function supports header and footer display and keeps  document page layout to the maximum degree.',
            updateTip: 'The previous dynamic template feature is not synchronized and compatible. Manual upgrading is required. If dynamic templates created before January 26th are edited, contracts will not be saved nor sent. Contracts can still be sent before March 1, 2021 if templates have not been edited. Upgrading is recommended as soon as possible. Non-dynamic templates are not affected.',
            connectUs: 'If you have any questions, please contact via the hotline ************ or contact online customer service.',
        },
        sendCode: {
            tip: 'The current template settings do not meet the conditions for generating the send-code. Check whether the following requirements are met:',
            fail: {
                1: 'No blank documents are included',
                2: 'The contracting party has only one variable party (including signature and copy), and the variable party must be the first operator; The signatory must have a signature stamping position',
                3: ' The fixed account of the contracting party shall not be empty',
                4: 'Does not trigger pre-shipment approval',
                5: 'The contents that the sender has to fill are not empty(Include the description field and the contract content field)',
                6: 'Not template combination',
            },
        },
        sendCodeGuide: {
            title: '发送码高级功能说明',
            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',
            tip1: {
                main: '1. 上上签',
                sub: '',
                line1: '向上上签申请开通档案+、合同预审、智能预审',
                line2: '开通后可以到对应的菜单中操作使用',
            },
            tip2: {
                main: '2. 档案柜管理员',
                sub: '创建档案柜、配置智能预审',
                line1: '',
                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',
            },
            tip3: {
                main: '3. 签约方',
                sub: '扫码填资料、获取合同文件',
                line1: '',
                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',
            },
            tip4: {
                main: '4. 档案柜管理员',
                sub: '',
                line1: '查看签约的相对方、发送的合同情况',
                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',
            },
        },
    },
    style: {
        signature: {
            text: {
                x: '0',
                fontSize: '18',
            },
        },
    },
    resetPwd: {
        title: 'Safety tips！',
        notice: 'The security factor of your password is low and there is a security risk. Please reset your password',
        oldLabel: 'Original password',
        oldPlaceholder: 'Please enter the original password',
        newLabel: 'New password',
        newPlaceholder: '6-18 digits and uppercase and lowercase letters, support special characters',
        submit: 'Submit',
        errorMsg: 'Password should contain 6-18 digits and upper - and lowercase letters, please reset',
        oldRule: 'The original password cannot be empty',
        newRule: 'The new password cannot be empty',
        success: 'Success!',
    },
    personAuthIntercept: {
        title: '邀请您以',
        name: '姓名：',
        id: '身份证号：',
        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',
        desMore: '根据发起方要求，您还需要补充',
        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        tips: '实名认证通过后，可查看并签署合同。',
        goOn: '是我本人，开始认证',
        goMore: '去补充认证',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '当前账号是否是您的常用手机号？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',
        confirmReject: '是的，我要驳回实名',
    },
    authIntercept: {
        title: '要求您以：',
        name: '姓名为：',
        id: '身份证号为：',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        descNoSame1: '签署合同。',
        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',
        tips: '注：身份信息完全一致才能签署合同',
        goOn: '是我本人，开始认证',
        goHome: '我知道了',
        goMore: '去补充认证',
        authTip: '进行实名认证。',
        viewAndSign: '完成认证后即可查看和签署合同',
        tips2: '注：企业名称完全一致才能查看和签署合同。',
        requestOtherAnth: 'Request verification by others',
        goAuth: '去实名认证',
        requestSomeoneList: '请求以下人员完成实名认证：',
        ent: '企业',
        entName: '企业名称',
        account: '账号',
        accountPH: '手机或邮箱',
        send: '发送',
        lackEntName: '请填写企业名称',
        errAccount: '请填写正确的邮箱或手机号',
        successfulSent: '发送成功',
    },
    thirdPartApprovalDialog: {
        title1: 'Pre-signature Approval',
        title2: 'Approval Process',
        content1: 'Signing requires approval. Please wait patiently.',
        content2: '需由第三方平台（非上上签平台）审批合同。',
        cancelBtnText: 'View Approval Process',
        confirmBtnText: 'Confirm',
        iKnow: 'I see',
    },
    endSignEarlyPrompt: {
        cancel: '取消',
        confirm: '确认',
        signPrompt: '签署提示',
        signTotalCountTip: '本次签署共包含{count}份合同文件',
        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',
        hasSigned: '{count}人已签字',
        hasNotSigned: '{count}人未签字',
        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',
    },
    commonNomal: {
        yesterday: 'Yesterday',
        ssq: 'BestSign',
        ssqPlatform: 'BestSign E-signature cloud platform',
        ssqTestPlatform: '(For testing purposes only) BestSign E-Signature Cloud Platform',
        pageExpiredTip: 'The page has expired, please refresh and try again',
        pswCodeSimpleTip: 'The password must contain 6-18 digits and uppercase and lowercase letters, please reset',
    },
    transferAdminDialog: {
        title: 'Identification',
        transfer: 'Transfer',
        confirmAdmin: 'I am the main administrator',
        content: 'The system administrator is responsible for managing the company seal, contracts, and other personnel permissions, generally belonging to the legal representative, financial manager, legal manager, IT department manager, or business leader of the company.| Please confirm if you meet the above identity requirements. If not, it is recommended to forward it to the relevant personnel.',
    },
    choseBoxForReceiver: {
        dataNeedForReceiver: 'Information to be submitted by the signing party',
        dataFromDataBox: 'The information to be submitted by the signing party needs to be obtained through document collection from a certain file cabinet.',
        searchTp: 'Please enter the name or code of the file cabinet.',
        search: 'Search',
        boxNotFound: 'The file cabinet cannot be found.',
        cancel: 'Cancel',
        confirm: 'Ok',
    },
    localCommon: {
        cancel: 'Cancel',
        confirm: 'Confirm',
        toSelect: 'Please select',
        seal: 'Stamp',
        signature: 'Sign',
        signDate: 'Date',
        text: 'Text',
        date: 'Date',
        qrCode: 'QR Code',
        number: 'Digital',
        dynamicTable: 'Dynamic forms',
        terms: 'Terms and conditions of the contract',
        checkBox: 'Checkboxes for multiple options',
        radioBox: 'Checkbox for single options',
        image: 'Image',
        confirmSeal: 'Stamp for inquiries',
        tip: 'Tips',
        confirmRemark: 'Remarks regarding seals that do not meet requirements',
        optional: 'Optional',
        require: 'Required',
        comboBox: 'DropDown',
    },
    twoFactor: {
        signTip: 'Signing Prompt',
        settingTwoFactor: '设置二要素验证器',
        step1: '1. 安装验证器应用',
        step1Tip: '二要素身份验证需要您安装一下手机应用程序：',
        step2: '2.扫描二维码',
        step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',
        step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',
        step3: '3.输入6位验证码',
        step3Tip: '请输入屏幕上显示的验证码',
        verifyCode6: '6位验证码',
        iosAddress: 'iOS下载地址：',
        androidAddress: 'Android下载地址：',
        chromeVerify: '谷歌身份验证器',
        nextBtn: '下一步',
        confirmSign: 'Confirm to Sign',
        dynamicCode: '验证器动态码',
        password: 'Encryption Code',
        pleaseInput: 'Please input',
        twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',
        passwordTip: 'The sender requires encrypted signature for this document. Please complete the signing process accordingly.',
        twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',
        passwordTip2: 'Please contact the sender to obtain the encryption signing code. The contract will be ready for signing once you enter the code.',
        dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。',
    },
    functionSupportDialog: {
        title: 'Function introduction',
        inputTip: 'If you have relevant use needs, please fill in your needs in the following form. BestSign will arrange professionals to contact you and provide service guidance within 24 hours.',
        useSence: 'Application scenario',
        useSenceTip: 'Such as HR, dealers, logistics documents, etc',
        estimatedOnlineTime: 'Expected launch date',
        requireContent: 'Requirements',
        requireContentTip: 'Please describe how your company will use e-signature so that we can provide appropriate solution for you',
        getSupport: 'Get professional service support',
        callServiceHotline: 'Hotline：************',
        useSenceNotEmpty: 'The usage scenario cannot be empty',
        requrieContentNotEmpty: 'The demand content cannot be empty',
        oneWeek: 'Within a week',
        oneMonth: 'Within a month',
        other: 'Others',
        submitSuccess: 'Submitted successfully',
        submitTrial: 'Submit for trial',
        toTrial: 'To trial',
        trialTip: 'After submitting a trial application, the current function will be immediately activated and available for trial. In order to better help you use the features, you can fill in more requirements in the form below. BestSign consultant will contact you to provide services.',
        applyTrial: 'Apply for trial',
        trialSuccTip: 'The function has been activated. Welcome to try it out',
        goBuy: 'Buy now',
        trialTipMap: {
            title: 'Trial instructions',
            tip1: '1. Instant use, valid for 7 days；',
            tip2: '2. During the trial period, the function will not be charged；',
            tip3: '3. Each company entity has only one trial opportunity for a function；',
            tip4: '4. Self-service purchase is available during the probation period, and the use is uninterrupted;',
            tip5: '5. If your trial has ended, you can scan the code and contact the BestSign professional consultant for details:',
        },
        contactAdminTip: 'To use, please contact your enterprise administrator {tip} to purchase and open',
        trialEndTip: 'After the trial period, click to buy',
        trialRemainDayTip: 'There are {day} days left in the trial period, click to purchase copies',
        trialEnd: 'Trial function ended',
        trialEndMap: {
            deactivateTip: '{feature} feature has been disabled. Please clear the configuration or renew it before continuing to use it.',
            feature1: 'Contract Collateral',
            remove1: 'The method for clearing the configuration is: Edit the template - find the configured additional contract attachment data and delete it.',
            feature2: 'Handwriting handwriting recognition',
            remove2: 'The method for clearing the configuration is: Edit the template - Find the configured handwriting recognition and delete it.',
            feature3: 'Contract decoration: riding seal+watermark',
            remove3: 'The method to clear the configuration is: Edit Template - Find the configured contract decoration and delete it.',
            feature4: 'Contract sending approval',
            remove4: 'The method for clearing the configuration is: Enterprise Console - Deactivate all approval processes',
        },
    },
    setSignPwdDialog: {
        tip: "After the setting is completed, the signing password will be first used by default. You may log in to BestSign's e-Signature platform and enter 'User Center' or log in to BestSign app and enter 'Account Management' to change the password.",
        saveAndReturnSign: 'Save and return to sign',
        changeEmailVerify: 'Switch to Email Verification',
        changePhoneVerify: 'Switch to Phone Verification',
    },
    contractCompare: {
        reUpload: 'Re-upload',
        title: 'Contract Comparison',
        packagePurchase: 'Plan Purchase',
        packagePurchaseTitle: '[{title}] Plan Purchase',
        myPackage: 'My Plan',
        packageDetail: 'Plan Details',
        per: '次',
        packageContent: 'Plan Includes：',
        num: '{type}次数',
        limitTime: 'Validity Period',
        month: 'month',
        payNow: 'Buy Now',
        contactUs: 'Contact Us | Scan QR Code to Consult Professional Advisor',
        compareInfo1: 'Usage Instructions：',
        compareInfo2: '{index}、The available quota for {type} of purchases is accessible to all members of the enterprise. If you only require it for personal use, you may switch to a personal account by changing the login entity in the upper right corner.',
        compareInfo3: '{index}、Usage Calculated Based on Number of Uploaded Contract {per}',
        codePay: 'Please Scan QR Code to Pay',
        aliPay: 'Alipay Payment',
        wxPay: 'WeChat Payment',
        payIno: 'Activated Features | Purchased For | Payment Amount',
        finishPay: 'Payment Completed',
        paySuccess: 'Purchase Successful',
        originFile: 'Original Contract File',
        compareFile: 'Contract File for Comparison',
        documentSelect: 'Select File',
        comparisonResult: 'Comparison Result',
        history: 'History',
        currentHistory: 'Document Records',
        noData: 'No Data Available',
        differences: '{num} Differences',
        historyLog: '{num} Records',
        uploadLimit: 'Drag & Drop Files to Compare | Supported Formats: PDF (including scanned), Word',
        dragInfo: 'Release Mouse to Upload',
        uploadError: 'Unsupported File Format',
        pageNum: '第{page}页',
        difference: 'Differences {num}',
        download: 'Download Comparison Result',
        comparing: 'Comparing Contracts...',
        tip: 'Notification',
        confirm: 'Confirm',
        toBuy: 'Go to Purchase',
        translate: 'Contract Translation',
        doCompare: 'Compare',
        doTranslate: 'Translate',
        review: 'Contract Review',
        doReview: 'Review',
        reviewUploadFile: 'Drag Files to be Reviewed Here',
        reviewUpload: 'Drag Review Reference Files Here | e.g., "Distributor Management Policy", "Procurement Regulations" | Supported Formats: PDF, Word',
        reviewOriginFile: 'Contract Under Review',
        reviewTargetFile: 'Review Basis',
        reviewResult: 'Review Result',
        uploadReviewFile: 'Upload Reference Files for Review',
        risk: 'Risk Point {num}',
        risks: '{num} Risk Point(s)',
        startReview: 'Start Review',
        reviewing: 'Reviewing Contract...',
        noRisk: 'Review Completed - No Risks Detected',
        allowUpload: 'You can upload company regulations (e.g., "Procurement Management Policy"), compliance guidelines, or departmental instructions to guide contract review. | Example: "Party A must complete payment within 5 days after contract signing."',
        notAllowUpload: 'Avoid vague or general statements as review basis. | Example: "All contract terms must comply with applicable laws and regulations."',
        resumeReview: 'Continue to Next File',
        close: 'Close',
        extract: 'Contract Extraction',
        extractTitle: 'Keywords to Extract',
        selectKeyword: 'Select Keywords from the List Below',
        keyword: 'Keywords',
        addKeyword: 'Add{keyword}',
        introduce: '{keyword} Definition',
        startExtract: 'Start Extraction',
        extractTargetFile: 'Contract for Extraction',
        extractKeyWord: 'Extract Keywords',
        extracting: 'Extracting Contract...',
        extractResult: 'Extraction Result',
        extractUploadFile: 'Drag & Drop Files for Extraction Here',
        needExtractKeyword: 'Select Keywords to Extract',
        summary: 'Contract Summary',
        keySummary: 'Keyword Summary',
        deleteKeywordConfirm: 'Confirm Deletion of this Keyword?',
        keywordPosition: 'Keyword Locations',
        riskJudgement: 'Risk Assessment',
        judgeTargetContract: 'Contract Under Assessment',
        interpretTargetContract: 'AI-Interpreted Contracts',
        startJudge: 'Start',
        startInterpret: 'Start Now',
        uploadText: 'Please upload the documents for risk assessment',
        interpretText: 'Please upload documents for AI analysis',
        startTips: 'Now we can start to judge the risks',
        interpretTips: 'Now we can start to interpret the document',
        infoExtract: 'Extract information',
    },
    batchImport: {
        iKnow: 'I see',
    },
    templateCommon: {
        tip: 'Tip',
    },
    mgapprovenote: {
        SAQ: 'Questionnaire Survey',
        analyze: 'Analysis',
        annotate: 'Annotation',
        law: 'Legal Clause Search',
        case: 'Similar Case Search',
        translate: 'Translate',
        mark: 'mark',
        tips: 'The above content is AI-generated and does not represent the position of ShangShangQian. It is for your reference only. Please do not delete or modify this mark.',
        limit: 'Usage limit reached. If you need continued use, please fill out the form. Our customer service will contact you.',
        confirmTxt: 'Go to fill',
        content: 'Related content',
        experience: 'Professional Experience',
        datas: 'Relevant Data',
        terms: 'Analogous Clauses',
        original: 'Source',
        export: 'Export',
        preview: 'Contract Preview',
        history: 'History',
    },
    sealConfirm: {
        title: 'Confirm Electronic Seal Page',
        header: 'Confirm Electronic Seal',
        signerEnt: 'Contracting Company:',
        abnormalSeal: 'Abnormal Electronic Seal:',
        sealNormal: 'Seal Normal',
        tip1: 'Please confirm if the electronic seal is normal and usable. If it is normal, you can click the "Seal Normal" button. Subsequently, when this company uses this seal again, the system will no longer send you abnormal notifications.',
        tip2: 'If there is an issue with the seal, please promptly communicate with the signing party to replace the seal, resend the contract for signing, or reject and re-sign.',
    },
    ...console,
    ...docList,
    ...consts,
    keyInfoExtract: {
        operate: 'Extract Information',
        contractType: 'Predicted Contract Types',
        tooltips: 'Select the Key Information',
        predictText: 'Predicting',
        extractText: 'Extracting',
        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',
        result: 'result:',
    },
    judgeRisk: {
        title: 'AI Lawyer',
        deepInference: 'AI Legal',
        showAll: 'Show More',
        tips: 'Judging',
        dialogTitle: '“AI Lawyer” Reviews Contracts',
        aiInterpret: 'AI Interpretation',
    },
    sealDistribute: {
        requestSeal: 'Request Corporate Seal Authorization',
        company: 'Company',
        applicant: 'Applicant',
        accountID: 'Account ID',
        submissionTime: 'Submission Time',
        status: 'Status',
        agree: 'Approved',
        unAgree: 'Rejected',
        ifAgree: 'If approved,',
        applyTime: ' applicant\'s stamp usage period is:',
        to: 'to',
        placeHolderTime: 'Year-Month-Day',
        senderCompany: 'Sender Company',
        documentTitle: 'Document Title',
        sealApplicationScope: 'Seal Application Scope',
        applyforSeal: 'Apply for E-Seal',
        reject: 'Reject',
        approve: 'Approve',
    },
    sealApproval: {
        sealRight: 'Seal Permission',
        requestSeal: 'Request Corporate Seal Authorization',
        allEntContract: 'Contracts from All Enterprises',
        partEntContract: 'Contracts from Selected Enterprises: ',
        pleaseInputRight: 'Please Enter Permission',
        successTransfer: 'After successful handover,',
        getRight: 'will obtain the above permissions or can directly edit and assign new signing permissions.',
        signAllEntContract: 'Sign contracts from all enterprises',
        sign: 'Sign',
        sendContract: 'sent contracts',
        sealUseTime: 'Seal Usage Period: ',
        currentStatus: 'Current Status: ',
        takeBackSeal: 'Recall Seal',
        agree: 'Agree',
        hasAgree: 'Agreed',
        hasReject: 'Rejected',
        hasDone: 'Completed',
        ask: ' has assigned ',
        giveYou: "'s seal to you",
        hopeAsk: 'hopes to',
        hopeGive: 'hand over the seal to',
        hopeGiveYou: 'hand over the relevant seal to you',
        noSettingTime: 'No Time Setting',
        approvalSuccess: 'Approval Successful',
        getSealSuccess: 'Seal Obtained Successfully',
    },
    workspace: {
        create: 'Created',
        reviewing: 'Reviewing',
        completed: 'Completed',
        noData: 'Empty',
        introduce: 'Meaning of the {keyword}',
        termsDetail: 'Details',
        extractFormat: 'Format',
        optional: 'Optional',
        required: 'Required',
        operate: 'Operation',
        detail: 'Details',
        delete: 'Delete',
        agreement: {
            uploadError: 'Only PDF, DOC, or DOCX files can be uploaded!',
            extractionRequest: 'Extraction request has been submitted. Please check the results in the terms list later.',
            upload: 'Upload',
            define: 'Defination',
            extract: 'Extraction',
            drag: 'Drag the file here or',
            add: 'click',
            format: 'Supported file formats are doc,docx,pdf',
            fileName: 'FileName',
            status: 'Status',
            completed: 'Completed',
            failed: 'Failed',
            size: 'Size',
            terms: 'Terms',
            success: 'The extraction has been completed, totaling {total}',
            ongoing: 'In progress...totaling {total}',
            tips: 'Skipping this page does not affect the results of the extraction',
            others: 'Upload other agreements',
            result: 'Jump to the download page of the extraction result',
            curProgress: 'Current progress: ',
            refresh: 'Refresh',
            details: 'Loaded {successNum}，totaling {length}',
            start: 'Start',
            more: 'Add',
            skip: 'Skip',
            tiqu: 'Start',
            chouqu: 'Start',
        },
        review: {
            distribution: 'Distribution review',
            Incomplete: 'Incomplete',
            createReview: 'Create Review',
            manageReview: 'Review Management',
            reviewDetail: 'Review Details',
            reviewId: 'Review ID',
            reviewStatus: 'Review Status',
            reviewName: 'Review Name',
            reviewStartTime: 'Review Start Time',
            reviewCompleteTime: 'Review Complete Time',
            reviewDesc: 'Version：V.{reviewVersion} | ReviewId：{reviewId}',
            distribute: 'Initiate Review',
            drag: 'Drag the agreement to be reviewed here',
            content: 'Content',
            current: 'Current',
            history: 'History',
            page: 'Page {x}：',
            users: 'Users：',
            message: 'Message',
            modify: 'Modify',
            placeholder: 'Please use semicolons to separate multiple users',
            submit: 'Submit',
            reupload: 'Reupload',
            finish: 'Finish',
            reviewSummary: 'Review Summary',
            initiator: 'Initiator',
            versionSummary: 'Version Summary',
            version: 'Version',
            versionOrder: 'Version {version}',
            curReviewStatus: 'Current Version’s Status',
            curReviewVersion: 'Current Version',
            curReviewPopulation: 'Current Version’s Review Population',
            curReviewStartTime: 'Current Version’s Review Start Time',
            curReviewInitiator: 'Current Version’s Review Initiator',
            checkComments: 'Check Comments',
            overview: 'Overview',
            reviewer: 'Reviewer',
            reviewResult: 'Review Result',
            replyTime: 'Reply Time',
            agreement: 'Agreements',
            files: 'Files',
            fileName: 'FileName',
            numberOfModificationSuggestions: 'Number of modification suggestions',
            uploadTime: 'Upload Time',
            download: 'Download',
            dispatch: 'Dispatch',
            recent: 'Latest review time：',
            replyContent: 'Reply Content',
            advice: 'Advice',
            noIdea: 'Have no advice',
            origin: 'Original Content: ',
            revised: 'Revised Content',
            suggestion: 'Suggestion: ',
            dateMark: '{name} written in <span style="color: #0988EC">Version {version}</span> on {date}',
            unReviewed: 'Not yet reviewed',
            revisionFiles: 'Revision Files',
            staffReplyAggregation: 'Overview',
            staffReply: '{name}’s Comments',
            tips: 'Tips',
            tipsContent: 'If you are sure to perform this operation, this review will no longer support distribution and subsequent operations. Do you want to continue?',
            confirm: 'Confirm',
            cancel: 'Cancel',
            successMessage: 'Completed',
            PASS: 'Pass',
            REJECT: 'Reject',
            uploadErrorMessage: 'Currently, only DOCX format files are supported for upload.',
            successInitiated: 'Review initiated',
            autoDistribute: 'Intelligent Distribution',
            requiredUsers: 'Users Requiring Review',
            contentToReview: 'Content to Review',
            termDetails: 'Term Details',
            term: 'Term',
            aiDistribute: 'AI Intelligent Distribution',
            noData: 'No Data Available',
            docIconAlt: 'Document Icon',
            docxIconAlt: 'DOCX Icon',
            pdfIconAlt: 'PDF Icon',
            requiredUsersError: 'Please fill in the users requiring review',
            selectContentError: 'Please select the content to review',
            initiateReviewSuccess: 'Review Initiated',
            syncInitiated: 'Synchronization Initiated',
        },
        contentTracing: {
            title: 'Content Tracing',
            fieldContent: 'Field Content',
            originalResult: 'Original Result',
            contentSource: 'Content Source',
            page: 'Page',
        },
    },
    hubblePackage: {
        title: 'My Package',
        details: 'Package Details',
        remainingPages: 'Remaining Total Pages',
        pages: 'Pages',
        usedPages: 'Used',
        remaining: 'Available Remaining',
        total: 'Total',
        expiryTime: 'Expiry Time',
        amount: 'Quantity',
        unitPrice: 'Each',
        copy: 'Copy',
        words: 'Thousand Characters',
    },
    workspaceIndex: {
        title: 'Workspace',
        package: 'Package Usage',
        agreement: 'Agreement',
        review: 'Review',
        term: 'Term',
        amount: '数目',
        unitPrice: '单价',
    },
    agreement: {
        title: 'Agreement Management',
        exportList: 'Export Agreement List',
        exportAllChecked: 'Excel (All Fields, Selected Agreements)',
        exportCurrentChecked: 'Excel (Current Fields, Selected Agreements)',
        exportAllMatched: 'Excel (All Fields, Matched Conditions)',
        exportCurrentMatched: 'Excel (Current Fields, Matched Conditions)',
        add: 'Add Agreement',
        upload: 'Upload Agreement',
        operation: 'Operation',
        download: 'Download Agreement',
        details: 'Details',
        delete: 'Delete',
        relatedTaskStatus: 'Related Extraction Task Status',
        confirmDelete: 'Are you sure to delete the current agreement?',
        prompt: 'Prompt',
        booleanYes: 'Yes',
        booleanNo: 'No',
        defaultExportName: 'export.xlsx',
        taskNotStarted: 'Extraction Task Not Started',
        taskStarted: 'Extraction Task Started (Content Searching)',
        contentSearchCompleted: 'Content Search Completed (Formatting Results)',
        resultFormattingCompleted: 'Result Formatting Completed (Verifying Results)',
        resultVerificationCompleted: 'Result Verification Completed',
    },
    filter: {
        filter: 'Filter',
        refreshExtraction: 'Refresh Extraction',
        extractTerms: 'Extract Term Definitions',
        refreshList: 'Refresh List',
        currentCondition: 'Displaying agreements under current conditions.',
        when: 'When',
        selectCondition: 'Please Select Condition',
        enterCondition: 'Please Enter Condition',
        yes: 'Yes',
        no: 'No',
        addCondition: 'Add Condition',
        reset: 'Reset',
        confirm: 'Confirm',
        and: 'And',
        or: 'Or',
        equals: 'Equals',
        notEquals: 'Not Equals',
        contains: 'Contains',
        notContains: 'Does Not Contain',
        greaterThan: 'Greater Than',
        greaterThanOrEquals: 'Greater Than or Equals',
        lessThan: 'Less Than',
        lessThanOrEquals: 'Less Than or Equals',
        emptyCondition: 'Filter condition cannot be empty',
    },
    fieldConfig: {
        button: 'Field Configuration',
        header: 'Fields to be displayed',
        submit: 'Submit',
        cancel: 'Cancel',
    },
    agreementDetail: {
        detail: 'Agreement Details',
        add: 'Add Agreement',
        id: 'Agreement ID',
        file: 'Agreement File',
        download: 'Download Agreement',
        replaceFile: 'Replace Agreement File',
        uploadFile: 'Upload Agreement File',
        relatedExtractionStatus: 'Related Extraction Task Status',
        dataSource: 'Data Source',
        yes: 'Yes',
        no: 'No',
        select: 'Please Select',
        input: 'Please Input',
        save: 'Save',
        cancel: 'Cancel',
        page: 'Page {page}',
        addDataSource: 'Add Data Source',
        pageNo: 'Page',
        pageSuffix: '',
        submit: 'Submit',
        inputDataSource: 'Please input data source content',
        pageFormatError: 'Page number only supports comma-separated numbers or pure numbers',
        confirmDelete: 'Are you sure to delete the current data source?',
        tips: 'Tips',
        uploadSuccess: 'Upload Successful',
    },
    termManagement: {
        title: 'Term Management',
        batchDelete: 'Batch Delete',
        import: 'Import Terms',
        export: 'Export Terms',
        add: '+ Add Term',
        name: 'Term Name',
        definition: 'Term Definition',
        formatRequirement: 'Extraction Format Requirement',
        dataFormat: 'Data Format',
        operation: 'Operation',
        edit: 'Edit',
        delete: 'Delete',
        detail: 'Term Details',
        addTitle: 'Add Term',
        namePlaceholder: 'Enter your specialized term',
        definitionPlaceholder: 'Enter the definition of the term',
        formatRequirementPlaceholder: 'Enter the extraction format requirement of the term',
        dataFormatPlaceholder: 'Expected extracted term format',
        cancel: 'Cancel',
        confirmEdit: 'Confirm Edit',
        importTitle: 'Import Terms',
        uploadTemplate: 'Upload Term Template',
        downloadTemplate: 'Download Term Template',
        extractType: {
            text: 'Text',
            longText: 'Long Text',
            date: 'Date',
            number: 'Number',
            boolean: 'Yes/No',
        },
        importSuccess: 'Import Successful',
        deleteConfirm: 'Are you sure to delete the current term?',
        prompt: 'Prompt',
        nameEmptyError: 'Term name cannot be empty',
    },
    agent: {
        extractTitle: 'Information Extraction',
        riskTitle: 'AI Lawyer',
        feedback: 'Questionnaire',
        toMini: 'Check Details in App',
        otherContract: 'Analyze latent risks in other contracts?',
        others: 'Others',
        submit: 'Submit',
        autoExtract: 'Automatic Extraction Until Completion',
        autoRisk: 'Auto-Analysis Process Running',
        aiGenerated: 'AI Generated - © BestSign',
        chooseRisk: 'Select File for Analysis',
        chooseExtract: 'Specify Source File for Extraction',
        analyzing: 'Analyzing',
        advice: 'Generating Revision Suggestions',
        options: 'Generating Options',
        inputTips: 'Enter Precise Information',
        chargeTip: 'Insufficient Balance - Top Up Required',
        original: 'Original',
        revision: 'Revision',
        diff: 'Comparison',
        locate: 'Locating',
        custom: 'Please enter custom review rules',
        content: 'Locate Original Text',
        satisfy: 'Satisfied with the analysis results, continue to the next analysis',
        dissatisfy: 'Not satisfied with the analysis results, re-analyze',
        selectFunc: 'Please select the function you wish to use',
        deepInference: 'AI Legal',
        deepThinking: 'Deep Thinking',
        deepThoughtCompleted: 'Deep Thinking Completed',
        reJudge: 'Re-judge',
        confirm: 'Confirm',
        tipsContent: 'Proceeding will deduct from your usage credits. Continue?',
        useLawyer: 'Use AI Lawyer',
        interpretFinish: 'AI Analysis Complete',
        exportPDF: 'Export PDF Report',
        defaultExportName: 'export.pdf',
        exporting: 'Exporting report... Please wait',
    },
    authorize: {
        title: 'Terms of Use',
        content: 'Unlock smart contracts - AI analysis boosts efficiency!Agree to start your free trial',
        cancel: 'Not Now',
        confirm: 'Accept & Start',
        contract: 'View Hubble Product Terms of Use',
    },
    hubbleEntry: {
        smartAdvisor: 'Smart Contract Advisor',
        tooltips: 'This feature is not enabled. Contact BestSign e-signature advisors to purchase access.',
        confirm: 'Got it',
    },
    lang: 'en',
};
