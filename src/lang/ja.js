// 语言为中文时的文案
import utils from './module/utils/utils-zh.js';
import mixin from './module/mixins/mixins-zh.js';
import components from './module/components/components-ja.js';

import docList from './module/docList/docList-ja.js';
import console from './module/console/console-zh.js';
import userCentral from './module/usercentral/usercentral-zh.js';
import home from './module/home/<USER>';
import sign from './module/sign/sign-ja.js';
import entAuth from './module/entAuth/entAuth-zh.js';
import consts from './module/consts/ja.js';
import docTranslation from './module/docTranslation/docTranslation-ja.js';

export default {
    ...utils,
    ...mixin,
    ...components,
    ...docList,
    ...docTranslation,
    footerAd: {
        title: 'ジャンププロンプト',
        content1: 'アクセスしたページは、サードパーティのプロモーションページにジャンプします。',
        content2: '続行しますか？',
        bankContent: '即将进入宁波银行"容易贷"企业贷款介绍页面',
        bankTip1: '让宁波银行主动给我打电话',
        bankTip2: '向我发送一条短信，介绍如何办理',
        bankFooter: '加宁波银行专属客服，一对一服务我',
        cancel: 'キャンセル',
        continue: '続行',
    },
    commonFooter: {
        record: 'ICP主体企業登録番号：浙ICP備********号',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'オープンプラットフォーム',
        aboutBestSign: '弊社について',
        contact: 'お問い合わせ先',
        recruitment: '採用情報',
        help: 'ヘルプセンター',
        copyright: '無断転載禁止',
        company: 'ベストサイン・ジャパン株式会社',
        ssqLogo: 'ベストサイン ボトムバーロゴ',
        provideTip: '電子契約サービスは',
        ssq: 'ベストサインにより',
        provide: '提供',
        signHotline: '契約サービスホットライン',
        langSwitch: '言語',
    },
    login: {
        pswLogin: '密码登录',
        usePswLogin: '使用密码登录',
        verifyLogin: '验证码登录',
        useVerifyLogin: '使用验证码登录',
        scanLogin: '扫码登录',
        scanFailure: '二维码已失效,请刷新重试',
        scanSuccess: '扫码成功',
        scanLoginTip: '请使用上上签APP扫一扫登录',
        appLoginTip: '请在上上签APP中点击登录',
        downloadApp: '下载上上签APP',
        forgetPsw: '忘记密码',
        login: '登录',
        noAccount: '没有账号',
        registerNow: '马上注册',
        accountPlaceholder: '请输入手机或邮箱',
        passwordPlaceholder: '请输入登录密码',
        pictureVer: '请填写图片中的内容',
        verifyCodePlaceholder: '请输入6位验证码',
        getVerifyCode: '認証コードを取得する',
        noRegister: '尚未注册',
        or: '或',
        errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',
        errAccountOrPwdTip2: '你输入的密码和账号不匹配',
        errEmailOrTel: '请输入正确的邮箱或手机号!',
        errPwd: '请输入正确的密码!',
        verCodeFormatErr: '验证码错误',
        grapVerCodeErr: '图形验证码错误',
        grapVerCodeFormatErr: '图形验证码格式错误',
        lackAccount: '请填写账号后再获取',
        lackGrapCode: '请先填写图形验证码',
        getVerCodeTip: '请获取验证码',

        loginView: '登录并查看合同',
        regView: '注册并查看合同',
        takeViewBtn: '登录并签署',
        resendCode: '重新获取',
        regTip: '填写正确的验证码后上上签将为您创建账号',
        haveRead: '我已阅读并同意',
        bestsignAgreement: '上上签服务协议',
        and: '和',
        digitalCertificateAgreement: '数字证书使用协议',
        privacyPolicy: '隐私政策',
        sendSuc: '发送成功',
        lackVerCode: '请先输入验证码',
        lackPsw: '请先输入密码',
        notMatch: '您输入的密码和账号不匹配',
        cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',
        wrongLink: '非法链接',
        footerTips: '电子签约服务由<span>上上签</span>提供',
        bestSign: '上上签',
        bestSignDescription: '电子签约行业领跑者',
        /** 忘记密码 /forgotPassword start */
        forgetPswStep: '验证注册账号 | 重新设置密码',
        pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',
        accountInput: '账号 | 请填写您的账号',
        smsCodeInput: '验证码 | 获取验证码',
        haveRegistereLoginNow: '我已注册， | 马上登录',
        nextStep: '下一步 | 提交',
        setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',
        passwordResetSucceeded: '密码重置成功!',
        /** 忘记密码 /forgotPassword end */
        accountNotRegistered: '账号未注册',
        loginAndDownload: '登录并下载合同',
        registerAndDownload: '注册并下载合同',
        inputPhone: '请输入手机号',
        readContract: '读取合同',
        errorPhone: '手机格式错误',
        companyCert: '进行企业认证',
        regAndCompanyCert: '注册并进行企业认证',
    },
    ...sign,
    handwrite: {
        title: '手書きサイン',
        picSubmitTip: '署名画像が正常に提出されました',
        settingDefault: 'デフォルト署名に設定',
        replaceAllSignature: '全ての署名に使われます',
        replaceAllSeal: '全てのスタンプに使います',
        canUseSeal: '私の印章',
        applyForSeal: '印章使用申請',
        moreTip: 'あなたの手書き署名はデフォルト署名として保存され、契約書の署名にのみ使用されます。管理方法：【ユーザーセンター->署名管理】',
        uploadPic: '画像をアップロード',
        use: '使う',
        clickExtend: '右矢印をクリックして手書きエリアを拡大',
        upload: '署名画像をアップロード',
        uploadTip1: 'ヒント：署名画像をアップロードする際は、署名が画像全体を満たすようにしてください',
        uploadTip2: '署名は濃い色または黒色の文字でお願いします',
        rewrite: '書き直し',
        cancel: '取り消し',
        confirm: '使用',
        upgradeBrowser: 'お使いのブラウザはキャンバス署名機能に対応していません。ブラウザを更新してください。',
        submitTip: '手描きの署名を提出成功',
        title2: '署名を手書きしてください',
        QRCode: 'QRコード読み取りで署名',
        needWrite: '正しい名前を手書きしてください！',
        needRewrite: '文字が判読できません。書き直してください',
        ok: '確定',
        clearTips: '判読可能な署名をお書きください',
        isBlank: 'キャンバスが空です。署名を描いてから送信してください！',
        success: '署名が正常に送信されました',
        signNotMatch: '楷書で署名してください。身分証明書の記載と一致する必要があります。',
        signNotMatchExact: '第{numList}文字の認識に失敗しました。楷書で署名してください。身分証明書の記載と一致する必要があります。',
        msg: {
            successToUser: '新しい署名を設定しました。ウェブサイトで「保存」ボタンをクリックしてください。',
            successToSign: '新しい署名が有効になりました。契約書の署名ページをご確認ください。',
            cantGet: '署名を取得できません。他のブラウザをお試しください。',
        },
    },
    common: {

        aboutBestSign: '关于公司',
        contact: '联系我们',
        recruitment: '诚聘英才',
        copyright: '版权所有',
        advice: '咨询建议',
        notEmpty: '空にすることはできません!',
        enter6to18n: '请输入6-18位数字、大小写字母',
        ssqDes: '电子签约云平台领导者',
        openPlatform: '开放平台',
        company: '杭州尚尚签网络科技有限公司',
        help: '帮助中心',
        errEmailOrTel: '请输入正确的邮箱或手机号!',
        verCodeFormatErr: '验证码错误',
        signPwdType: '6桁の数字を入力してください',
        enterActualEntName: '请填写真实的企业名称',
        enterCorrectName: '请输入正确的姓名',
        enterCorrectPhoneNum: '请输入正确的手机号',
        enterCorrectEmail: '请输入正确的邮箱',
        imgCodeErr: '图形验证码错误',
        enterCorrectIdNum: '请输入正确的证件号码',
        enterCorrectFormat: '请输入正确的格式',
        enterCorrectDateFormat: '请输入正确的日期格式',

    },
    entAuth: {
        ...entAuth,
        entCertification: '企业实名认证',
        subBaseInfo: '提交基本信息',
        corDocuments: '企业证件',
        license: '营业执照',
        upload: 'アップロードをクリック',
        uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',
        hi: '你好',
        exit: '退出',
        help: '帮助',
        hotline: '服务热线',
        acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',
        comfirmSubmit: '确认提交',
        cerficated: '认证完成',
        serialNumber: '证书序列号',
        validity: '有效期',
        entName: '企业名称',
        nationalNo: '国家注册号',
        corporationName: '法定代表人姓名',
        city: '所在城市',
        entCertificate: '企业实名证书',
        certificationAuthority: '证书颁发机构',
        bestsignPlatform: '上上签电子签约云平台',
        notIssued: '未发放',
        date: '{year}年{month}月{day}日',
        congratulations: '恭喜您，成功完成企业实名认证',
        continue: '继续',
        rejectMessage: '由于如下原因，资料审核不通过，请核对',
        recertification: '重新认证',
        waitMessage: '客服将在一个工作日内完成审核，请耐心等待',
    },
    personalAuth: {
        info: '提示',
        submitPicError: '请上传照片后再使用',
    },
    home: {
        ...home,
        home: '首页',
        contractDrafting: '合同起草',
        contractManagement: '合同管理',
        userCenter: '用户中心',
        service: '服务',
        enterpriseConsole: '企业控制台',
        groupConsole: '集团控制台',
        startSigning: '契約書を送る',
        contractType: '发送普通合同 | 发送模板合同 ',
        sendContract: '发送合同',
        shortcuts: '快捷入口 | 没有任何文件快捷入口',
        setting: '立即设置 | 设置更多快捷入口',
        signNum: '签发量月度报表',
        contractNum: '合同发送量 | 合同签署量',
        contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',
        type: '企业 | 个人',
        basicInformation: '基本信息',
        more: '更多',
        certified: '已认证 | 未认证',
        account: '账号',
        time: '创建时间 |注册时间',
        day: '日 | 月',
        sendContractNum: '发送量 | 签署量',
        num: '份',
        realName: '立即企业实名 | 立即个人实名',
        update: '产品最新公告',
        mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',
        countDes: {
            1: '可发：对公合同',
            2: '份',
            3: '对私合同',
            4: '份',
        },
        chargeNow: '立即充值',
        myRechargeOrder: '我的充值订单',
        statusTip: {
            1: '需要我操作',
            2: '需要他人签署',
            3: '即将截止签约',
            4: '签约完成',
        },
        useTemplate: '使用模板',
        useLocalFile: '上传本地文件',
        enterEnterpriseName: '请输入企业名称',
    },
    docDetail: {
        canNotOperateTip: '无法{operate}合同',
        shareSignLink: '分享签署链接',
        faceSign: '刷脸签署',
        faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',
        contractRecipient: '合同收件方',
        personalOperateLog: '个人合同操作日志',
        recordDialog: {
            date: '日期',
            user: '用户',
            operate: '操作',
            view: '查看',
            download: '下载',
        },
        remarks: '备注',
        operateRecords: '操作记录',
        borrowingRecords: '借阅记录',
        currentHolder: '当前持有人',
        currentEnterprise: '当前企业',
        companyInterOperationLog: '公司内部操作日志',
        receiverMap: {
            sender: '合同发件人',
            signer: '合同接收人',
            ccUser: '合同抄送人',
        },
        downloadCode: '合同下载码',
        noTagToAddHint: '还没有标签，请前往企业控制台添加',
        requireFieldNotAllowEmpty: '必填项不能为空',
        modifySuccess: '修改成功',
        uncategorized: '未分类',
        notAllowModifyContractType: '{type}中的合同不允许修改合同类型',
        setTag: '设置标签',
        contractTag: '合同标签',
        plsInput: '请输入',
        plsInputCompanyInternalNum: '请输入公司内部编号',
        companyInternalNum: '公司内部编号',
        none: '无',
        plsSelect: '请选择',
        modify: '修改',
        contractDetailInfo: '合同详细信息',
        slideContentTip: {
            signNotice: '签约须知',
            contractAncillaryInformation: '合同附属资料',
            content: '内容',
            document: '文档',
        },
        downloadDepositConfirmTip: {
            title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',
            hint: '提示',
            confrim: '继续下载',
            cancel: '取消',
        },
        downloadTip: {
            title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',
            hint: '提示',
            confirm: '确定',
            cancel: '取消',
        },
        transferSuccessGoManagePage: '转交成功，将返回合同管理页面',
        claimSign: '认领签署',
        downloadDepositPageTip: '下载签约存证页(脱敏版)',
        resend: '重新发送',
        proxySign: '代签署',
        notPassed: '已驳回',
        approving: '审批中',
        signning: '签署中',
        notarized: '已公正',
        currentFolder: '当前文件夹',
        archive: '归档',
        deadlineForSigning: '截止签约时间',
        endFinishTime: '签约完成/签约结束时间',
        contractImportTime: '合同导入时间',
        contractSendTime: '合同发送时间',
        back: '返回',
        contractInfo: '合同信息',
        basicInfo: '基本信息',
        contractNum: '合同编号',
        sender: '发件方',
        personAccount: '个人账号',
        entAccount: '企业账号',
        operator: '经办人',
        signStartTime: '发起签约时间',
        signDeadline: '签约截止时间',
        contractExpireDate: '合同到期时间',
        // none: '无',
        edit: '修改',
        settings: '设置',
        from: '来源',
        folder: '文件夹',
        contractType: '合同类型',
        reason: '理由',
        sign: '署名',
        approval: '审批',
        viewAttach: '查看附页',
        downloadContract: '下载合同',
        downloadAttach: '下载签约存证',
        print: '打印',
        certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',
        needMeSign: '需要我签署',
        needMeApproval: '需要我审批',
        inApproval: '审批中',
        needOthersSign: '需要他人签署',
        signComplete: '签约完成',
        signOverdue: '逾期未签',
        rejected: '已拒签',
        revoked: '已撤销',
        contractCompleteTime: '签约完成时间',
        contractEndTime: '签约结束时间',
        reject: '拒签',
        revoke: '撤销',
        download: '下载',
        viewSignOrders: '查看签署顺序',
        viewApprovalProcess: '承認フローを確認',
        completed: '已完成',
        cc: '抄送',
        ccer: '抄送方',
        signer: '签约方',
        signSubject: '签约主体',
        signSubjectTooltip: '发件方填写的签约主体为',
        user: '用户',
        IDNumber: '身份证号',
        state: '状态',
        time: '时间',
        notice: '提醒',
        detail: '详情',
        RealNameCertificationRequired: '需要实名认证',
        RealNameCertificationNotRequired: '不需要实名认证',
        MustHandwrittenSignature: '必须手写签名',
        handWritingRecognition: '开启手写笔迹识别',
        privateMessage: '私信',
        attachment: '资料',
        rejectReason: '原因',
        notSigned: '未签署',
        notViewed: '未查看',
        viewed: '已查看',
        signed: '已签署',
        viewedNotSigned: '已读未签',
        notApproval: '未审批',
        remindSucceed: '提醒消息已发送',
        reviewDetails: '审批详情',
        close: '关 闭',
        entInnerOperateDetail: '企业内部操作详情',
        approve: '同意',
        disapprove: '驳回',
        applySeal: '申请用印',
        applied: '已申请',
        apply: '申请',
        toOtherSign: '转给其他人签',
        handOver: '转交',
        approvalOpinions: '审批意见',
        useSeal: '用印',
        signature: '签名',
        use: '使用',
        date: '日期',
        fill: '填写',
        times: '次',
        place: '处',
        contractDetail: '合同明细',
        viewMore: '查看更多',
        collapse: '收起',
        signLink: '签署链接',
        saveQRCode: '保存二维码或复制链接，分享给签署方',
        signQRCode: '签署链接二维码',
        copy: '复制',
        copySucc: '复制成功',
        copyFail: '复制失败',
        certified: '已认证',
        unCertified: '未认证',
        claimed: '已认领',
    },
    uploadFile: {
        thumbnails: '缩略图',
        isUploading: '正在上传',
        move: '移动',
        delete: '删除',
        replace: '替换',
        tip: '提示',
        understand: 'わかりました。',
        totalPages: '{page}页',
        uploadFile: '上传本地文件',
        matchErr: '服务器开了点小差，请稍后再试',
        inUploadingDeleteErr: '请在上传完毕后删除',
        timeOutErr: '请求超时',
        imgUnqualified: '画像フォーマットが要件に適合していません',
        imgBiggerThan20M: '上传图片大小不能超过 20MB!',
        error: '出错啦',
        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',
    },
    contractInfo: {
        contractName: '契約表題',
        contractNameTooltip: '契約表題には特殊文字を含めず、100文字以内にしてください',
        customNumber: '企業内部番号',
        contractType: '契約タイプ',
        signDeadLine: '契約期限',
        signDeadLineTooltip: '契約書がこの日付までに署名完了しない場合、引き続き署名を続けることはできません',
        selectDate: '日時の選択',
        contractExpireDate: '契約満期日',
        contractExpireDays: '契約書有効期間（日）',
        expireDateTooltip: '契約内容の有効期間は後で契約書を管理するのに役立ちます',
        notNecessary: '任意',
        dateTips: 'お客様のために契約書有効期日を自動識別しました。確認してください',
        contractTitleErr: '契約名には特殊文字を含めないでください',
        contractTitleLengthErr: '契約名には100字を超えないでください',
        internalNumber: '公司内部编号',
        toSelect: '選択してください',
        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',
        necessary: '必須項目',
    },
    template: {
        templateList: {
            linkBoxTip: '関連ファイルキャビネットID：',
        },
        dynamicTemplateUpdate: {
            title: '动态模板新功能上线',
            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',
            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',
            connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。',
        },
        sendCode: {
            tip: '現在のテンプレート設定が送信コード生成条件を満たしていません。以下の要件を満たしているかどうかチェックしてください。',
            fail: {
                1: '空白の文書は含まれていません',
                2: '契約者はただ1つの可変者(署名とccを含む)、そして可変者は第1の操作者でなければなりません;署名者は必ず捺印所を設けなければなりません',
                3: '契約者のうち固定者のアカウントは空ではありません',
                4: '送信前承認はありません',
                5: '送信者はフィールドが空にならないように必ず記入します（記述フィールドと契約内容フィールドを含みます）',
                6: '非テンプレートの組み合わせです',
            },
        },
        sendCodeGuide: {
            title: '发送码高级功能说明',
            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',
            tip1: {
                main: '1. 上上签',
                sub: '',
                line1: '向上上签申请开通档案+、合同预审、智能预审',
                line2: '开通后可以到对应的菜单中操作使用',
            },
            tip2: {
                main: '2. 档案柜管理员',
                sub: '创建档案柜、配置智能预审',
                line1: '',
                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',
            },
            tip3: {
                main: '3. 签约方',
                sub: '扫码填资料、获取合同文件',
                line1: '',
                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',
            },
            tip4: {
                main: '4. 档案柜管理员',
                sub: '',
                line1: '查看签约的相对方、发送的合同情况',
                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',
            },
        },
    },
    style: {
        signature: {
            text: {
                x: '34',
                fontSize: '18',
            },
        },
    },
    resetPwd: {
        title: '安全提示！',
        notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',
        oldLabel: '原密码',
        oldPlaceholder: '请输入原密码',
        newLabel: '新密码',
        newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',
        submit: '确定',
        errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',
        oldRule: '原密码不能为空',
        newRule: '新密码不能为空',
        success: '修改成功',
    },
    personAuthIntercept: {
        title: '邀请您以',
        name: '姓名：',
        id: '身份证号：',
        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',
        desMore: '根据发起方要求，您还需要补充',
        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        tips: '实名认证通过后，可查看并签署合同。',
        goOn: '是我本人，开始认证',
        goMore: '去补充认证',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '是否继续以当前账号签署？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',
        confirmReject: '是的，我要驳回实名',
    },
    authIntercept: {
        title: '要求您以：',
        name: '姓名为：',
        id: '身份证号为：',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        descNoSame1: '签署合同。',
        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',
        tips: '注：身份信息完全一致才能签署合同',
        goOn: '是我本人，开始认证',
        goHome: '我知道了',
        goMore: '去补充认证',
        authTip: '进行实名认证。',
        viewAndSign: '完成认证后即可查看和签署合同',
        tips2: '注：企业名称完全一致才能查看和签署合同。',
        requestOtherAnth: '请求他人认证',
        goAuth: '去实名认证',
        requestSomeoneList: '请求以下人员完成实名认证：',
        ent: '企业',
        entName: '企业名称',
        account: '账号',
        accountPH: '手机或邮箱',
        send: '发送',
        lackEntName: '请填写企业名称',
        errAccount: '请填写正确的邮箱或手机号',
        successfulSent: '发送成功',
    },
    thirdPartApprovalDialog: {
        title1: '署名前承認',
        title2: '承認フロー',
        content1: '承認後に署名できますので、しばらくお待ちください。',
        content2: '需由第三方平台（非上上签平台）审批合同。可在上上签平台“接收方视角”页面中启用或关闭第三方平台审批。',
        cancelBtnText: '承認フローを確認',
        confirmBtnText: '確認',
        iKnow: '理解',
    },
    endSignEarlyPrompt: {
        cancel: '取消',
        confirm: '确认',
        signPrompt: '签署提示',
        signTotalCountTip: '本次签署共包含{count}份合同文件',
        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',
        hasSigned: '{count}人已签字',
        hasNotSigned: '{count}人未签字',
        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',
    },
    commonNomal: {
        yesterday: '昨日',
        ssq: 'ベストサイン',
        ssqPlatform: 'ベストサイン電子契約クラウドプラットフォーム',
        ssqTestPlatform: '（テスト用限定）ベストサイン電子署名クラウドプラットフォーム',
        pageExpiredTip: 'ページは有効期限切れです。ページをリロードしてください',
        pswCodeSimpleTip: 'パスワードは数字・大文字/小文字を含む6～18桁にする必要があります。再設定してください',
    },
    transferAdminDialog: {
        title: '身分確認',
        transfer: '引き継ぎ',
        confirmAdmin: '私が管理者主任です',
        content: 'システムの管理者主任は担当企業印章の管理・契約書の管理およびその他人員権限の管理が必要となります。通常企業の法定代表人・財務管理者・法務管理者・IT部門管理者もしくは企業業務責任者が担当します。| お客様が上記の身分かどうか確認してください。もしも違っていれば、関連する人に引き継いでください。',
    },
    choseBoxForReceiver: {
        dataNeedForReceiver: '契約主体が提出しなければならない資料',
        dataFromDataBox: '契約主体が提出しなければならない資料はアーカイブスの資料を通じて採集入手しなければなりません',
        searchTp: 'アーカイブス名または番号を入力してください',
        search: '検索',
        boxNotFound: 'アーカイブスが見つかりません',
        cancel: 'キャンセル',
        confirm: '確　認',
    },
    localCommon: {
        cancel: 'キャンセル',
        confirm: '確認',
        toSelect: '選択してください',
        seal: '捺印',
        signature: '署名',
        signDate: '署名日時',
        text: 'テキスト',
        date: '日付',
        qrCode: '二次元コード',
        number: 'デジタル',
        dynamicTable: '動態テンプレート',
        terms: '契約条項',
        checkBox: 'チェックボックス',
        radioBox: 'ラジオボタン',
        image: '画像',
        confirmSeal: '業務照合印',
        confirmRemark: '印章不適合の備考',
        optional: 'オプション',
        require: '必須項目',
        tip: '注意',
        comboBox: '検索候補',
    },
    twoFactor: {
        signTip: '署名案内',
        settingTwoFactor: '二要素認証を設定します',
        step1: '1.バリデータアプリケーションのインストール',
        step1Tip: '二要素身分認証には、携帯電話アプリケーションのインストールが必要です',
        step2: '2.QRコードを読み取ります',
        step2Tip1: 'ダウンロードしたバリデータを使用し、下記のQRコードを読み取ってください（携帯電話の時刻が現在時刻と一致していないと、二要素身分認証ができませんのでご注意ください）。',
        step2Tip2: '画面には、二要素認証に必要な6桁の認証コードが表示されます。',
        step3: '3.6桁の認証コードを入力します',
        step3Tip: '画面に表示される認証コードを入力してください',
        verifyCode6: '6桁の認証コード',
        iosAddress: 'IOS版ダウンロードはこちら：',
        androidAddress: 'Andro証明書版ダウンロードはこちら：',
        chromeVerify: 'google身分バリデータ',
        nextBtn: '次のステップ',
        confirmSign: '署名を確認する',
        dynamicCode: 'ワンタイムパスワード',
        password: '署名コード',
        pleaseInput: '入力してください',
        twoFactorTip: '送信者の要求により、暗号化署名方法で署名する必要があります。',
        passwordTip: '送信者に、署名コード入力での署名が求められています。',
        twoFactorAndPasswordTip: '送信者の要請により、二要素認証を通り、正しい署名コードを入力することで署名を完了する必要があります',
        passwordTip2: '署名コードは、送信者様へお問い合わせください。署名コードを入力しての署名となります。',
        dynamicVerifyInfo: '正しいワンタイムパスワードを入力してください。認証システムのアカウントを設定し直した場合は、最新のワンタイムパスワードを入力してください。',
    },
    functionSupportDialog: {
        title: '機能紹介',
        inputTip: '関連する使用条件がある場合は、以下のフォームにご記入ください。ベストサインでは24時間以内に専門スタップからご連絡し、サービスのご案内を差し上げます。',
        useSence: '使用場面',
        useSenceTip: '例：人事/販売業者/物流帳票...',
        estimatedOnlineTime: '予定オンライン時間',
        requireContent: '必要内容',
        requireContentTip: '御社がどのように電子契約を使用するのか大まかに説明ください。弊社からお客様のために適切なプランを作成します。',
        getSupport: '専門サービスサポートの提供',
        callServiceHotline: 'すぐにカスタマーサポート：<EMAIL>',
        useSenceNotEmpty: '使用場面は空欄に出来ません',
        requrieContentNotEmpty: '必要内容は空欄にできません',
        oneWeek: '一週間以内',
        oneMonth: '一ヶ月以内',
        other: 'その他',
        submitSuccess: 'コミット成功',
        submitTrial: '試用に提出する',
        toTrial: '試用に行く',
        trialTip: '試用申請を提出すると、現在の機能はすぐに開通し、試用することができる。機能の使用を支援するために、次の表により多くのニーズを記入することができます。電子契約コンサルタントに署名すると、サービスが提供されます。',
        applyTrial: '試用申請',
        trialSuccTip: '機能が開通しましたので、お試しください',
        goBuy: '直接購入',
        trialTipMap: {
            title: '試用の心得',
            tip1: '1. オープン即使用、有効期間は7日間；',
            tip2: '2. 試用期間中、機能は無料；',
            tip3: '3. ビジネス主体ごとに、1つの機能を1回だけ試用する機会；',
            tip4: '4. 試用期間中は自由に購入でき、無停止で使用できる；',
            tip5: '5. 試用が終了した場合は、スキャンコードを確認して、詳細については前に署名した専門コンサルタントに連絡してください：',
        },
        contactAdminTip: '使用する場合は、Enterprise Administrator {tip} にお問い合わせください。',
        trialEndTip: '試用期間が終了したら、クリックして購入してください',
        trialRemainDayTip: '試用期間が残りました{day}日、クリックして購入してください',
        trialEnd: '試用機能終了',
        trialEndMap: {
            deactivateTip: '{feature}機能は無効になっています。構成をクリアするか、継続料金を払ってから使用できます。',
            feature1: '契約付属資料',
            remove1: '構成の消去方法は、「テンプレートの編集」-構成済みの追加契約付属資料を見つけて削除します。',
            feature2: '手書きの筆跡認識',
            remove2: '構成をクリアする方法は、「テンプレートを編集」-構成済みのストローク認識を見つけて削除します。',
            feature3: '契約装飾：スリット章+透かし',
            remove3: '構成の消去方法は、「テンプレートの編集」-構成された契約装飾を見つけて削除します。',
            feature4: '契約送信承認',
            remove4: '構成方法の消去：Company Console-すべての承認フローを非アクティブにする',
        },
    },
    setSignPwdDialog: {
        tip: '設定後、署名パスワードが優先的に適用されます。ユーザーセンターもしくはアカウント管理から設定を変更出来ます。',
        saveAndReturnSign: '保存して署名に戻る',
        changeEmailVerify: 'メール認証に切り替える',
        changePhoneVerify: '携帯電話認証に切り替える',
    },
    contractCompare: {
        reUpload: '再アップロード',
        title: '契約書比較',
        packagePurchase: 'プラン購入',
        packagePurchaseTitle: '【{title}機能】プラン購入',
        myPackage: 'マイプラン',
        packageDetail: 'プラン詳細',
        per: '次',
        packageContent: 'プラン内容：',
        num: '{type}次数',
        limitTime: '有効期間',
        month: '月',
        payNow: '今すぐ購入',
        contactUs: 'お問い合わせ | QRコードをスキャンして専門アドバイザーに相談',
        compareInfo1: 'ご利用ガイド：',
        compareInfo2: '{index}、購入{type}に基づく利用可能限度額は、対応する企業の全メンバーが利用可能です。個人でご利用の場合は、画面上部のログイン主体を個人アカウントに切り替えてください。',
        compareInfo3: '{index}、アップロードした契約書の{per}数に基づく使用量計算',
        codePay: 'QRコードをスキャンして支払い',
        aliPay: 'アリペイ支払い',
        wxPay: 'ウィーチャット支払い',
        payIno: '機能有効化 | 購入対象 | 支払金額',
        finishPay: '支払い完了',
        paySuccess: '購入成功',
        originFile: '原本契約書ファイル',
        compareFile: '比較用契約書ファイル',
        documentSelect: 'ファイルを選択',
        comparisonResult: '比較結果',
        history: '履歴',
        currentHistory: '文書記録',
        noData: 'データなし',
        differences: '{num}つの差異',
        historyLog: '{num}件の記録',
        uploadLimit: '比較するファイルをドラッグ＆ドロップ | 対応形式: PDF（スキャン済み含む）、Word',
        dragInfo: 'マウスを離してアップロード',
        uploadError: '非対応ファイル形式',
        pageNum: '第{page}页',
        difference: '差異 {num}',
        download: '比較結果をダウンロード',
        comparing: '契約書比較中...',
        tip: '通知',
        confirm: '確定',
        toBuy: '購入へ進む',
        translate: '契約書翻訳',
        doCompare: '比較',
        doTranslate: '翻訳',
        review: '契約書審査',
        doReview: '審査',
        reviewUploadFile: '審査対象ファイルをここにドラッグ＆ドロップ',
        reviewUpload: '審査基準ファイルをドラッグ | 例：「販売代理店管理規程」「調達規程」| 対応形式: PDF、Word',
        reviewOriginFile: '審査対象契約書',
        reviewTargetFile: '審査基準',
        reviewResult: '審査結果',
        uploadReviewFile: '審査基準ファイルをアップロード',
        risk: 'リスク項目 {num}',
        risks: '{num}つのリスク項目',
        startReview: '審査を開始',
        reviewing: '契約書審査中...',
        noRisk: '審査完了 - リスク未検出',
        allowUpload: '審査の参考として、「調達管理規程」などの社内規程、コンプライアンス規定、部門ガイドラインをアップロード可能です。| 例: 「甲は契約締結後5日以内に支払いを完了すること」 | 如：甲方需在合同签订后的5日内完成付款。',
        notAllowUpload: '曖昧な表現や原則的な説明を審査基準に使用しないでください。 | 例: 「全ての契約条項は関連法令に準拠すること」',
        resumeReview: '次のファイルへ進む',
        close: '閉じる',
        extract: '契約書抽出',
        extractTitle: '抽出するキーワード',
        selectKeyword: '下記の「キーワード」から選択',
        keyword: 'キーワード',
        addKeyword: '追加{keyword}',
        introduce: '{keyword}定義',
        startExtract: '抽出を開始',
        extractTargetFile: '抽出対象契約書',
        extractKeyWord: 'キーワード抽出',
        extracting: '契約書抽出中...',
        extractResult: '抽出結果',
        extractUploadFile: '抽出するファイルをドラッグ＆ドロップ',
        needExtractKeyword: '抽出するキーワードを選択',
        summary: '契約書要約',
        keySummary: 'キーワード要約',
        deleteKeywordConfirm: 'このキーワードを削除しますか',
        keywordPosition: 'キーワードの位置',
        riskJudgement: 'リスク判定',
        judgeTargetContract: 'リスク判定を開始',
        interpretTargetContract: 'AI解読済み契約書',
        startJudge: 'リスク評価を開始する',
        startInterpret: '解読開始',
        uploadText: 'リスク判断が必要なファイルのアップロードをお願いします',
        interpretText: '解読が必要なファイルをアップロードしてください',
        startTips: 'これでリスクはんだんを始められです',
        interpretTips: 'これよりAI解読を開始いたします',
        infoExtract: '抽出情報',
    },
    batchImport: {
        iKnow: '理解',
    },
    templateCommon: {
        tip: '注意',
    },
    mgapprovenote: {
        SAQ: '问卷调查',
        analyze: '分析',
        annotate: '批注',
        law: '法规',
        case: '案例',
        translate: '翻译',
        mark: '标记',
        tips: '上記内容はAIによって生成されたものであり、上上签の立場を代表するものではありません。参考のためだけにご利用ください。このマークを削除または変更しないでください。',
        limit: '使用回数が上限に達しました。継続使用の需要がある場合はフォームにご記入ください。カスタマーサービスよりご連絡いたします。',
        confirmTxt: '記入する',
        content: '関連する段落',
        experience: '業務経験',
        datas: '関連データ',
        terms: '類似条項',
        original: '引用元',
        export: 'エクスポート',
        preview: '契約書閲覧',
        history: '履歴',
    },
    sealConfirm: {
        title: '印章確認ページ',
        header: '印章確認',
        signerEnt: '契約企業：',
        abnormalSeal: '異常な印章：',
        sealNormal: '印章正常',
        tip1: '印章が正常に使用可能かどうかをご確認ください。正常な場合は、「印章正常」ボタンをクリックしてください。以降、この会社がこの印章を使用する際、システムは異常通知を送信しなくなります。',
        tip2: '印章に問題がある場合は、速やかに契約相手と連絡を取り、印章の変更を行い、契約書を再送して署名してもらうか、または却下して再署名を行ってください。',
    },
    userCentral: userCentral,
    ...console,
    ...consts,
    keyInfoExtract: {
        operate: '情報抽出',
        contractType: 'Predicted Contract Types',
        tooltips: 'Select the Key Information',
        predictText: 'Predicting',
        extractText: 'Extracting',
        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',
        result: 'result:',
    },
    judgeRisk: {
        title: 'AI弁護士',
        deepInference: 'AI法務',
        showAll: 'Show More',
        tips: 'Judging',
        dialogTitle: '「AI弁護士」による契約書の審査',
        aiInterpret: 'AI解読',
    },
    sealDistribute: {
        requestSeal: '押印権限申請',
        company: '会社',
        applicant: '申請者',
        accountID: 'アカウント',
        submissionTime: '時間',
        status: '状態',
        agree: '同意済み',
        unAgree: '却下済',
        ifAgree: '同意する場合、',
        applyTime: '申請者の印章利用期間は：',
        to: '～',
        placeHolderTime: '年-月-日',
        senderCompany: '送信企業',
        documentTitle: '契約書タイトル',
        sealApplicationScope: '印章使用範囲',
        applyforSeal: '印章申請',
        reject: '却下',
        approve: '同意',
    },
    sealApproval: {
        sealRight: '印鑑権限',
        requestSeal: '押印権限申請',
        allEntContract: '全ての企業からの契約書',
        partEntContract: '一部の企業からの契約書：',
        pleaseInputRight: '権限を入力してください',
        successTransfer: '引継ぎ完了後、',
        getRight: '上記の権限を取得するか、新しい署名権限を直接編集して割り当てることができます。',
        signAllEntContract: '全ての企業からの契約書に署名',
        sign: '署名',
        sendContract: '送信された契約書',
        sealUseTime: '印鑑使用期間：',
        currentStatus: '現在の状態：',
        takeBackSeal: '印鑑を回収',
        agree: '同意',
        hasAgree: '同意済み',
        hasReject: '却下済み',
        hasDone: '完了',
        ask: 'が',
        giveYou: 'の印鑑をあなたに割り当てます',
        hopeAsk: 'は',
        hopeGive: 'の印鑑を引き継ぎたい',
        hopeGiveYou: 'の関連印鑑をあなたに引き継ぎます',
        noSettingTime: '時間設定なし',
        approvalSuccess: '承認成功',
        getSealSuccess: '印鑑取得成功',
    },
    workspace: {
        create: '作成済み',
        reviewing: '審査中',
        completed: '完了',
        noData: 'データなし',
        introduce: '{keyword}の説明',
        termsDetail: '用語の詳細',
        extractFormat: '抽出形式',
        optional: '任意',
        required: '必須',
        operate: '操作',
        detail: '詳細',
        delete: '削除',
        agreement: {
            uploadError: 'アップロードできるのはPDF、DOC、またはDOCXファイルのみです。',
            extractionRequest: '抽出リクエストが送信されました。後で用語リストで結果を確認してください。',
            upload: 'ファイルのアップロード',
            define: '用語の定義',
            extract: '協定の抽出',
            drag: 'ファイルをここにドラッグするか、',
            add: 'クリックして追加',
            format: 'doc、docx、pdf形式をサポート',
            fileName: 'ファイル名',
            status: '状態',
            completed: 'アップロード完了',
            failed: 'アップロード失敗',
            size: 'サイズ',
            terms: '用語',
            success: 'ファイル抽出完了、合計{total}個',
            ongoing: 'ファイル抽出中... 合計{total}個',
            tips: 'この画面をスキップしても抽出結果に影響はありません',
            others: '続けてアップロード',
            result: '抽出結果のダウンロードページに移動',
            curProgress: '現在の進捗: ',
            refresh: '更新',
            details: '{successNum}個ロード済み、合計{length}個',
            start: '抽出開始',
            more: 'ファイルを追加',
            skip: '抽出をスキップし、アップロードを完了。',
            tiqu: '抽出開始',
            chouqu: '抽出開始',
        },
        review: {
            distribution: '配布審査',
            Incomplete: '未終了',
            createReview: '審査を作成',
            manageReview: '審査管理',
            reviewDetail: '審査の詳細',
            reviewId: '審査番号',
            reviewStatus: '審査状態',
            reviewName: '審査名',
            reviewStartTime: '審査開始時間',
            reviewCompleteTime: '審査終了時間',
            reviewDesc: 'バージョン：バージョン{reviewVersion}  |  審査番号：{reviewId}',
            distribute: '審査を開始',
            drag: '審査待ちの協定をこの領域にドラッグ',
            content: '審査待ちの内容',
            current: '配布待ち記録',
            history: '履歴記録',
            page: '第{page}ページ：',
            users: '審査が必要なユーザー',
            message: 'メッセージ',
            modify: '修正',
            placeholder: '複数のユーザーはセミコロン";"で区切ってください',
            submit: '確定',
            reupload: '審査の再アップロード',
            finish: '審査終了',
            reviewSummary: '審査概要',
            initiator: '審査の発起人',
            versionSummary: 'バージョン概要',
            version: 'バージョン',
            versionOrder: '第{version}版',
            curReviewStatus: '現在のバージョン審査状態',
            curReviewVersion: '現在のバージョン',
            curReviewPopulation: '現在のバージョン審査人数',
            curReviewStartTime: '現在のバージョン審査開始時間',
            curReviewInitiator: '現在のバージョン審査発起人',
            checkComments: '修正意見を集約表示',
            overview: '審査結果の概要',
            reviewer: '審査者',
            reviewResult: '審査結果',
            replyTime: '返信時間',
            agreement: '審査の協定',
            files: '関連協定',
            fileName: '協定名',
            numberOfModificationSuggestions: '修正意見数',
            uploadTime: 'アップロード時間',
            download: 'ダウンロード',
            dispatch: '配布',
            recent: '最新の審査時間：',
            replyContent: '審査の返信内容',
            advice: '協定の修正意見',
            noIdea: '修正意見なし',
            origin: '原文内容：',
            revised: '修正後の内容：',
            suggestion: '修正意見：',
            dateMark: '{name} は <span style="color: #0988EC">バージョン {version}</span> に {date} に記載されています',
            unReviewed: 'まだレビューされていません',
            revisionFiles: '修正協定',
            staffReplyAggregation: '修正情報の集約',
            staffReply: '{name}の審査情報',
            tips: '提示',
            tipsContent: '終了後、この審査は配布及び後続操作をサポートしなくなります。続けますか',
            confirm: '確定',
            cancel: 'キャンセル',
            successMessage: '終了しました',
            PASS: '承認',
            REJECT: '不承認',
            uploadErrorMessage: '現在、アップロードできるのはDOCX形式のファイルのみです。',
            successInitiated: '審査が開始されました',
            autoDistribute: 'インテリジェント分配',
            requiredUsers: 'レビューが必要なユーザー',
            contentToReview: 'レビューするコンテンツ',
            termDetails: '用語の詳細',
            term: '用語',
            aiDistribute: 'AIインテリジェント分配',
            noData: 'データがありません',
            docIconAlt: 'ドキュメントアイコン',
            docxIconAlt: 'DOCXアイコン',
            pdfIconAlt: 'PDFアイコン',
            requiredUsersError: 'レビューが必要なユーザーを入力してください',
            selectContentError: 'レビューするコンテンツを選択してください',
            initiateReviewSuccess: 'レビューが開始されました',
            syncInitiated: '同期が開始されました',
        },
        contentTracing: {
            title: '内容追跡',
            fieldContent: 'フィールド内容',
            originalResult: 'オリジナル結果',
            contentSource: '内容の出典',
            page: 'ページ',
        },
    },
    hubblePackage: {
        title: '私のパッケージ',
        details: 'パッケージの詳細',
        remainingPages: '残りの総ページ数',
        pages: 'ページ',
        usedPages: '使用済み',
        remaining: '利用可能残数',
        total: '合計',
        expiryTime: '有効期限',
        amount: '数量',
        unitPrice: '単価',
        copy: '部',
        words: '千文字',
    },
    workspaceIndex: {
        title: 'ワークスペース',
        package: 'パッケージ使用量',
        agreement: '契約管理',
        review: 'レビュー管理',
        term: '用語管理',
    },
    agreement: {
        title: '契約管理',
        exportList: '契約リストをエクスポート',
        exportAllChecked: 'Excel（すべてのフィールド、選択された契約）',
        exportCurrentChecked: 'Excel（現在のフィールド、選択された契約）',
        exportAllMatched: 'Excel（すべてのフィールド、条件に一致）',
        exportCurrentMatched: 'Excel（現在のフィールド、条件に一致）',
        add: '契約を追加',
        upload: '契約をアップロード',
        operation: '操作',
        download: 'ダウンロード',
        details: '詳細',
        delete: '削除',
        relatedTaskStatus: '関連する抽出タスクの状態',
        confirmDelete: '現在の契約を削除しますか？',
        prompt: 'プロンプト',
        booleanYes: 'はい',
        booleanNo: 'いいえ',
        defaultExportName: 'export.xlsx',
        taskNotStarted: '抽出タスクは開始されていません',
        taskStarted: '抽出タスクが開始されました（コンテンツ検索中）',
        contentSearchCompleted: 'コンテンツ検索が完了しました（結果をフォーマット中）',
        resultFormattingCompleted: '結果のフォーマットが完了しました（結果を校正中）',
        resultVerificationCompleted: '結果の校正が完了しました',
    },
    filter: {
        filter: 'フィルター',
        refreshExtraction: '抽出をリフレッシュ',
        extractTerms: '用語定義を抽出',
        refreshList: 'リストをリフレッシュ',
        currentCondition: '現在の条件で表示されている契約。',
        when: '時',
        selectCondition: '条件を選択してください',
        enterCondition: '条件を入力してください',
        yes: 'はい',
        no: 'いいえ',
        addCondition: '条件を追加',
        reset: 'リセット',
        confirm: '確認',
        and: 'かつ',
        or: 'または',
        equals: '等しい',
        notEquals: '等しくない',
        contains: '含む',
        notContains: '含まない',
        greaterThan: 'より大きい',
        greaterThanOrEquals: '以上',
        lessThan: '未満',
        lessThanOrEquals: '以下',
        emptyCondition: 'フィルター条件は空にできません',
    },
    fieldConfig: {
        button: 'フィールド設定',
        header: '表示するフィールド',
        submit: '完了',
        cancel: 'キャンセル',
    },
    agreementDetail: {
        detail: '契約詳細',
        add: '契約を追加',
        id: '契約番号',
        file: '契約ファイル',
        download: '契約をダウンロード',
        replaceFile: '契約ファイルの置換',
        uploadFile: '契約ファイルをアップロード',
        relatedExtractionStatus: '関連する抽出タスクのステータス',
        dataSource: 'データソース',
        yes: 'はい',
        no: 'いいえ',
        select: '選択してください',
        input: '入力してください',
        save: '保存',
        cancel: 'キャンセル',
        page: '第 {page} ページ',
        addDataSource: 'データソースを追加',
        pageNo: '第',
        pageSuffix: 'ページ',
        submit: '送信',
        inputDataSource: 'データソース内容を入力してください',
        pageFormatError: 'ページ番号はカンマ区切りの数字のみをサポートします',
        confirmDelete: '現在のデータソースを削除しますか？',
        tips: 'ヒント',
        uploadSuccess: 'アップロード成功',
    },
    termManagement: {
        title: '用語管理',
        batchDelete: '一括削除',
        import: '用語のインポート',
        export: '用語のエクスポート',
        add: '+ 用語を追加',
        name: '用語名',
        definition: '用語の定義',
        formatRequirement: '抽出形式の要件',
        dataFormat: 'データ形式',
        operation: '操作',
        edit: '編集',
        delete: '削除',
        detail: '用語詳細',
        addTitle: '用語を追加',
        namePlaceholder: '専門用語を記入してください',
        definitionPlaceholder: '用語の定義を記入してください',
        formatRequirementPlaceholder: '用語の抽出形式の要件を記入してください',
        dataFormatPlaceholder: '期待する抽出用語形式',
        cancel: 'キャンセル',
        confirmEdit: '変更を確認',
        importTitle: '用語のインポート',
        uploadTemplate: '用語テンプレートファイルをアップロード',
        downloadTemplate: '用語テンプレートファイルをダウンロード',
        extractType: {
            text: 'テキスト',
            longText: '長いテキスト',
            date: '日付',
            number: '数値',
            boolean: 'はい/いいえ',
        },
        importSuccess: 'インポート成功',
        deleteConfirm: '現在の用語を削除しますか？',
        prompt: 'プロンプト',
        nameEmptyError: '用語名は空にできません',
    },
    agent: {
        extractTitle: '情報抽出',
        riskTitle: 'AI弁護士',
        feedback: '調査フィードバック',
        toMini: 'ミニアプリで確認',
        otherContract: '他の契約書の潜在リスクを分析?',
        others: 'その他',
        submit: '送信',
        autoExtract: '抽出完了まで自動処理継続',
        autoRisk: '分析プロセスを自動実行',
        aiGenerated: 'AI Generated - © BestSign',
        chooseRisk: '解析対象ファイルを選択',
        chooseExtract: '抽出用ソースファイル指定',
        analyzing: 'コンテンツ解析実行中',
        advice: '修正案自動作成中',
        options: '選択肢生成処理中',
        inputTips: '正確な情報を入力必須',
        chargeTip: '残高不足のためチャージ必須',
        original: '原本',
        revision: '改善提案',
        diff: '比較',
        locate: '原稿位置特定処理中',
        custom: 'カスタム審査ルールを入力してください',
        content: '原文の位置',
        satisfy: '对分析结果满意，继续下一项分析',
        dissatisfy: '对分析结果不满意，重新进行分析',
        selectFunc: 'ご希望の機能をお選びください',
        deepInference: 'AI法務',
        deepThinking: '深い思考中',
        deepThoughtCompleted: '深い思考が完了しました',
        reJudge: '再判断',
        confirm: '確認',
        tipsContent: '再判定を行うと利用回数が減ります。続けますか？',
        useLawyer: 'AI弁護士を起動',
        interpretFinish: 'AI解読完了',
        exportPDF: 'PDFレポート出力',
        defaultExportName: 'export.pdf',
        exporting: 'レポート出力中... しばらくお待ちください',
    },
    authorize: {
        title: 'ご利用条件	',
        content: 'AI契約分析で業務効率化！同意の上、今すぐ体験',
        cancel: 'いいえ',
        confirm: '同意して利用開始',
        contract: '『ハッブル製品利用条件』を確認',
    },
    hubbleEntry: {
        smartAdvisor: 'スマート契約アドバイザー',
        tooltips: 'この機能は有料です。BestSign電子契約アドバイザーにご連絡ください。',
        confirm: '了解',
    },
    lang: 'ja',
};
