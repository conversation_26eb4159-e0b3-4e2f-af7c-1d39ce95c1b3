// 语言为中文时的文案
import utils from './module/utils/utils-zh.js';
import mixin from './module/mixins/mixins-zh.js';
import components from './module/components/components-zh.js';

import docList from './module/docList/docList-zh.js';
import console from './module/console/console-zh.js';
import userCentral from './module/usercentral/usercentral-zh.js';
import home from './module/home/<USER>';
import sign from './module/sign/sign-zh.js';
import entAuth from './module/entAuth/entAuth-zh.js';
import consts from './module/consts/zh.js';
import docTranslation from './module/docTranslation/docTranslation-zh.js';

export default {
    ...utils,
    ...mixin,
    ...components,
    ...docList,
    ...docTranslation,
    footerAd: {
        title: '跳转提示',
        content1: '即将跳转到第三方服务页面',
        content2: '是否继续？',
        bankContent: '即将进入宁波银行“容易贷”企业贷款介绍页面',
        bankTip1: '让宁波银行主动给我打电话',
        bankTip2: '向我发送一条短信，介绍如何办理',
        bankFooter: '加宁波银行专属客服，一对一服务我',
        cancel: '取消',
        continue: '继续',
    },
    commonFooter: {
        record: 'ICP主体备案号：浙ICP备********号',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: '开放平台',
        aboutBestSign: '关于公司',
        contact: '联系我们',
        recruitment: '诚聘英才',
        help: '帮助中心',
        copyright: '版权所有',
        company: '杭州尚尚签网络科技有限公司',
        ssqLogo: '上上签底栏Logo',
        provideTip: '电子签约服务由',
        ssq: '上上签',
        provide: '提供',
        signHotline: '签约服务热线',
        langSwitch: '切换语言',
    },
    login: {
        pswLogin: '密码登录',
        usePswLogin: '使用密码登录',
        verifyLogin: '验证码登录',
        useVerifyLogin: '使用验证码登录',
        scanLogin: '扫码登录',
        scanFailure: '二维码已失效,请刷新重试',
        scanSuccess: '扫码成功',
        scanLoginTip: '请使用上上签APP扫一扫登录',
        appLoginTip: '请在上上签APP中点击登录',
        downloadApp: '下载上上签APP',
        forgetPsw: '忘记密码',
        login: '登录',
        noAccount: '没有账号',
        registerNow: '马上注册',
        accountPlaceholder: '请输入手机或邮箱',
        passwordPlaceholder: '请输入登录密码',
        pictureVer: '请填写图片中的内容',
        verifyCodePlaceholder: '请输入6位验证码',
        getVerifyCode: '获取验证码',
        noRegister: '尚未注册',
        or: '或',
        errAccountOrPwdTip: '你输入的密码和账号不匹配，是否',
        errAccountOrPwdTip2: '你输入的密码和账号不匹配',
        errEmailOrTel: '请输入正确的邮箱或手机号!',
        errPwd: '请输入正确的密码!',
        verCodeFormatErr: '验证码错误',
        grapVerCodeErr: '图形验证码错误',
        grapVerCodeFormatErr: '图形验证码格式错误',
        lackAccount: '请填写账号后再获取',
        lackGrapCode: '请先填写图形验证码',
        getVerCodeTip: '请获取验证码',

        loginView: '登录并查看合同',
        regView: '注册并查看合同',
        takeViewBtn: '登录并签署',
        resendCode: '重新获取',
        regTip: '填写正确的验证码后上上签将为您创建账号',
        haveRead: '我已阅读并同意',
        bestsignAgreement: '上上签服务协议',
        and: '和',
        digitalCertificateAgreement: '数字证书使用协议',
        privacyPolicy: '隐私政策',
        sendSuc: '发送成功',
        lackVerCode: '请先输入验证码',
        lackPsw: '请先输入密码',
        notMatch: '您输入的密码和账号不匹配',
        cookieTip: '无法读写cookie，请检查是否开启了无痕／隐身模式或其他禁用cookie的操作',
        wrongLink: '非法链接',
        footerTips: '电子签约服务由<span>上上签</span>提供',
        bestSign: '上上签',
        bestSignDescription: '电子签约行业领跑者',
        /** 忘记密码 /forgotPassword start */
        forgetPswStep: '验证注册账号 | 重新设置密码',
        pictureVerCodeInput: '图形验证码 | 请填写图片中的内容',
        accountInput: '账号 | 请填写您的账号',
        smsCodeInput: '验证码 | 获取验证码',
        haveRegistereLoginNow: '我已注册， | 马上登录',
        nextStep: '下一步 | 提交',
        setNewPasswordInput: '设置新密码 | 请设置6-18位数字、大小写字母组成的密码',
        passwordResetSucceeded: '密码重置成功!',
        /** 忘记密码 /forgotPassword end */
        accountNotRegistered: '账号未注册',
        loginAndDownload: '登录并下载合同',
        registerAndDownload: '注册并下载合同',
        inputPhone: '请输入手机号',
        readContract: '读取合同',
        errorPhone: '手机格式错误',
        companyCert: '进行企业认证',
        regAndCompanyCert: '注册并进行企业认证',
    },
    ...sign,
    handwrite: {
        title: '手写签名',
        picSubmitTip: '图片签名提交成功',
        settingDefault: '设置为默认签名',
        replaceAllSignature: '用于所有签名处',
        replaceAllSeal: '用于所有盖章处',
        canUseSeal: '我的印章',
        applyForSeal: '申请用印',
        moreTip: '将保存您的手写签名作为默认签名，仅用于合同签署。管理路径：【用户中心->签名管理】',
        uploadPic: '上传照片',
        use: '使用',
        clickExtend: '点右箭头延长手写区',
        upload: '上传签名图片',
        uploadTip1: '提示：上传签名图片时请将签名充满整个图片',
        uploadTip2: '签名请使用颜色较深或者纯黑色文字。',
        rewrite: '重写',
        cancel: '不写了',
        confirm: '使用',
        upgradeBrowser: '您的浏览器不支持画布手绘签名，请升级浏览器。',
        submitTip: '手绘签名提交成功',
        title2: '手绘您的签名',
        QRCode: '扫码签名',
        needWrite: '请手绘正确的姓名！',
        needRewrite: '笔画无法辨认，请重新书写',
        ok: '确定',
        clearTips: '请书写清晰可辨的签名',
        isBlank: '画布为空，请先手绘签名再提交！',
        success: '手绘签名提交成功',
        signNotMatch: '请正楷书写签名，须与实名证件信息一致。',
        signNotMatchExact: '第{numList}个字识别失败，请正楷书写签名，须与实名证件信息一致。',
        msg: {
            successToUser: '已设置好新签名，请在web端点击“保存”按钮。',
            successToSign: '新签名已生效，请到合同签署页查看。',
            cantGet: '获取不到签名，请尝试使用其他浏览器！',
        },
    },
    common: {

        aboutBestSign: '关于公司',
        contact: '联系我们',
        recruitment: '诚聘英才',
        copyright: '版权所有',
        advice: '咨询建议',
        notEmpty: '不能为空!',
        enter6to18n: '请输入6-18位数字、大小写字母',
        ssqDes: '电子签约云平台领导者',
        openPlatform: '开放平台',
        company: '杭州尚尚签网络科技有限公司',
        help: '帮助中心',
        errEmailOrTel: '请输入正确的邮箱或手机号!',
        verCodeFormatErr: '验证码错误',
        signPwdType: '请输入6位数字',
        enterActualEntName: '请填写真实的企业名称',
        enterCorrectName: '请输入正确的姓名',
        enterCorrectPhoneNum: '请输入正确的手机号',
        enterCorrectEmail: '请输入正确的邮箱',
        imgCodeErr: '图形验证码错误',
        enterCorrectIdNum: '请输入正确的证件号码',
        enterCorrectFormat: '请输入正确的格式',
        enterCorrectDateFormat: '请输入正确的日期格式',

    },
    entAuth: {
        ...entAuth,
        entCertification: '企业实名认证',
        subBaseInfo: '提交基本信息',
        corDocuments: '企业证件',
        license: '营业执照',
        upload: '点击上传',
        uploadLimit: '图片仅限jpeg、jpg、png格式，且大小不超过10M',
        hi: '你好',
        exit: '退出',
        help: '帮助',
        hotline: '服务热线',
        acceptProtectingMethod: '我接受上上签对我提交的个人身份信息的保护方法',
        comfirmSubmit: '确认提交',
        cerficated: '认证完成',
        serialNumber: '证书序列号',
        validity: '有效期',
        entName: '企业名称',
        nationalNo: '国家注册号',
        corporationName: '法定代表人姓名',
        city: '所在城市',
        entCertificate: '企业实名证书',
        certificationAuthority: '证书颁发机构',
        bestsignPlatform: '上上签电子签约云平台',
        notIssued: '未发放',
        date: '{year}年{month}月{day}日',
        congratulations: '恭喜您，成功完成企业实名认证',
        continue: '继续',
        rejectMessage: '由于如下原因，资料审核不通过，请核对',
        recertification: '重新认证',
        waitMessage: '客服将在一个工作日内完成审核，请耐心等待',
    },
    personalAuth: {
        info: '提示',
        submitPicError: '请上传照片后再使用',
    },
    home: {
        ...home,
        home: '首页',
        contractDrafting: '合同起草',
        contractManagement: '合同管理',
        userCenter: '用户中心',
        service: '服务',
        enterpriseConsole: '企业控制台',
        groupConsole: '集团控制台',
        startSigning: '发起签约',
        contractType: '发送普通合同 | 发送模板合同 ',
        sendContract: '发送合同',
        shortcuts: '快捷入口 | 没有任何文件快捷入口',
        setting: '立即设置 | 设置更多快捷入口',
        signNum: '签发量月度报表',
        contractNum: '合同发送量 | 合同签署量',
        contractInFormation: '您在这一个月中没有任何合同发送量和合同签署量',
        type: '企业 | 个人',
        basicInformation: '基本信息',
        more: '更多',
        certified: '已认证 | 未认证',
        account: '账号',
        time: '创建时间 |注册时间',
        day: '日 | 月',
        sendContractNum: '发送量 | 签署量',
        num: '份',
        realName: '立即企业实名 | 立即个人实名',
        update: '产品最新公告',
        mark: '您是否愿意把上上签推荐给您的朋友和同事？请在0～10中进行选择打分。',
        countDes: {
            1: '可发：对公合同',
            2: '份',
            3: '对私合同',
            4: '份',
        },
        chargeNow: '立即充值',
        myRechargeOrder: '我的充值订单',
        statusTip: {
            1: '需要我操作',
            2: '需要他人签署',
            3: '即将截止签约',
            4: '签约完成',
        },
        useTemplate: '使用模板',
        useLocalFile: '上传本地文件',
        enterEnterpriseName: '请输入企业名称',
    },
    docDetail: {
        canNotOperateTip: '无法{operate}合同',
        shareSignLink: '分享签署链接',
        faceSign: '刷脸签署',
        faceFirstVerifyCodeSecond: '优先刷脸，备用验证码签署',
        contractRecipient: '合同收件方',
        personalOperateLog: '个人合同操作日志',
        recordDialog: {
            date: '日期',
            user: '用户',
            operate: '操作',
            view: '查看',
            download: '下载',
        },
        remarks: '备注',
        operateRecords: '操作记录',
        borrowingRecords: '借阅记录',
        currentHolder: '当前持有人',
        currentEnterprise: '当前企业',
        companyInterOperationLog: '公司内部操作日志',
        receiverMap: {
            sender: '合同发件人',
            signer: '合同接收人',
            ccUser: '合同抄送人',
        },
        downloadCode: '合同下载码',
        noTagToAddHint: '还没有标签，请前往企业控制台添加',
        requireFieldNotAllowEmpty: '必填项不能为空',
        modifySuccess: '修改成功',
        uncategorized: '未分类',
        notAllowModifyContractType: '{type}中的合同不允许修改合同类型',
        setTag: '设置标签',
        contractTag: '合同标签',
        plsInput: '请输入',
        plsInputCompanyInternalNum: '请输入公司内部编号',
        companyInternalNum: '公司内部编号',
        none: '无',
        plsSelect: '请选择',
        modify: '修改',
        contractDetailInfo: '合同详细信息',
        slideContentTip: {
            signNotice: '签约须知',
            contractAncillaryInformation: '合同附属资料',
            content: '内容',
            document: '文档',
        },
        downloadDepositConfirmTip: {
            title: '您下载的签约存证页为脱敏版，经办人隐私信息已被隐去，不适用于法庭诉讼。如有诉讼需要，可联系上上签领取完整版签约存证页。',
            hint: '提示',
            confrim: '继续下载',
            cancel: '取消',
        },
        downloadTip: {
            title: '由于合同尚未完成，您下载到的是未生效的合同预览文件',
            hint: '提示',
            confirm: '确定',
            cancel: '取消',
        },
        transferSuccessGoManagePage: '转交成功，将返回合同管理页面',
        claimSign: '认领签署',
        downloadDepositPageTip: '下载签约存证页(脱敏版)',
        resend: '重新发送',
        proxySign: '代签署',
        notPassed: '已驳回',
        approving: '审批中',
        signning: '签署中',
        notarized: '已公正',
        currentFolder: '当前文件夹',
        archive: '归档',
        deadlineForSigning: '截止签约时间',
        endFinishTime: '签约完成/签约结束时间',
        contractImportTime: '合同导入时间',
        contractSendTime: '合同发送时间',
        back: '返回',
        contractInfo: '合同信息',
        basicInfo: '基本信息',
        contractNum: '合同编号',
        sender: '发件方',
        personAccount: '个人账号',
        entAccount: '企业账号',
        operator: '经办人',
        signStartTime: '发起签约时间',
        signDeadline: '签约截止时间',
        contractExpireDate: '合同到期时间',
        // none: '无',
        edit: '修改',
        settings: '设置',
        from: '来源',
        folder: '文件夹',
        contractType: '合同类型',
        reason: '理由',
        sign: '签署',
        approval: '审批',
        viewAttach: '查看附页',
        downloadContract: '下载合同',
        downloadAttach: '下载签约存证',
        print: '打印',
        certificatedTooltip: '该合同及相关证据已在杭州互联网法院司法链存证',
        needMeSign: '需要我签署',
        needMeApproval: '需要我审批',
        inApproval: '审批中',
        needOthersSign: '需要他人签署',
        signComplete: '签约完成',
        signOverdue: '逾期未签',
        rejected: '已拒签',
        revoked: '已撤销',
        contractCompleteTime: '签约完成时间',
        contractEndTime: '签约结束时间',
        reject: '拒签',
        revoke: '撤销',
        download: '下载',
        viewSignOrders: '查看签署顺序',
        viewApprovalProcess: '查看审批流程',
        completed: '已完成',
        cc: '抄送',
        ccer: '抄送方',
        signer: '签约方',
        signSubject: '签约主体',
        signSubjectTooltip: '发件方填写的签约主体为',
        user: '用户',
        IDNumber: '身份证号',
        state: '状态',
        time: '时间',
        notice: '提醒',
        detail: '详情',
        RealNameCertificationRequired: '需要实名认证',
        RealNameCertificationNotRequired: '不需要实名认证',
        MustHandwrittenSignature: '必须手写签名',
        handWritingRecognition: '开启手写笔迹识别',
        privateMessage: '私信',
        attachment: '资料',
        rejectReason: '原因',
        notSigned: '未签署',
        notViewed: '未查看',
        viewed: '已查看',
        signed: '已签署',
        viewedNotSigned: '已读未签',
        notApproval: '未审批',
        remindSucceed: '提醒消息已发送',
        reviewDetails: '审批详情',
        close: '关 闭',
        entInnerOperateDetail: '企业内部操作详情',
        approve: '同意',
        disapprove: '驳回',
        applySeal: '申请用印',
        applied: '已申请',
        apply: '申请',
        toOtherSign: '转给其他人签',
        handOver: '转交',
        approvalOpinions: '审批意见',
        useSeal: '用印',
        signature: '签名',
        use: '使用',
        date: '日期',
        fill: '填写',
        times: '次',
        place: '处',
        contractDetail: '合同明细',
        viewMore: '查看更多',
        collapse: '收起',
        signLink: '签署链接',
        saveQRCode: '保存二维码或复制链接，分享给签署方',
        signQRCode: '签署链接二维码',
        copy: '复制',
        copySucc: '复制成功',
        copyFail: '复制失败',
        certified: '已认证',
        unCertified: '未认证',
        claimed: '已认领',
    },
    uploadFile: {
        thumbnails: '缩略图',
        isUploading: '正在上传',
        move: '移动',
        delete: '删除',
        replace: '替换',
        tip: '提示',
        understand: '我知道了',
        totalPages: '{page}页',
        uploadFile: '上传本地文件',
        matchErr: '服务器开了点小差，请稍后再试',
        inUploadingDeleteErr: '请在上传完毕后删除',
        timeOutErr: '请求超时',
        imgUnqualified: '图片格式不符合要求',
        imgBiggerThan20M: '上传图片大小不能超过 20MB!',
        error: '出错啦',
        hasCATip: '您上传的PDF中已包含数字证书，会影响合同签署证据链的统一和完整，不建议个人用户如此使用。请上传未包含任何数字证书的PDF作为合同文件。',
    },
    contractInfo: {
        internalNumber: '公司内部编号',
        contractName: '合同标题',
        contractNameTooltip: '合同标题请不要包含特殊字符，且长度不超过100字',
        contractType: '合同类型',
        toSelect: '请选择',
        contractTypeErr: '当前合同类型已删除，请重新选择合同类型',
        signDeadLine: '签约截止时间',
        signDeadLineTooltip: '如果合同在此日期前未完成签署，则无法继续签署',
        selectDate: '选择日期时间',
        contractExpireDate: '合同到期日',
        expireDateTooltip: '合同内容中的到期时间，便于您后续的合同管理',
        necessary: '必填',
        notNecessary: '选填',
        dateTips: '已为您自动识别合同到期日，请确认',
        contractTitleErr: '合同名称请不要包含特殊字符',
        contractTitleLengthErr: '合同名称长度请不要超过100字',
    },
    template: {
        templateList: {
            linkBoxTip: '关联档案柜ID：',
        },
        dynamicTemplateUpdate: {
            title: '动态模板新功能上线',
            newVersionDesc: '新功能支持展示页眉页脚，最大程度保留文档页面布局。',
            updateTip: '之前的动态模板功能无法同步兼容，需要手动升级。1月26日前创建的动态模板经编辑后，将无法保存并发送合同。模板不编辑，在2021年3月1日之前仍能发送合同。建议尽快升级。非动态模板不受影响。',
            connectUs: '如有任何疑问，烦请联系拨打热线************或者联系在线客服。',
        },
        sendCode: {
            tip: '当前模板设置不符合发送码生成条件，请检查是否符合以下要求：',
            fail: {
                1: '不包含空白文档',
                2: '签约方只有一个可变方（包含签署和抄送），且可变方必须是第一操作人；签署人必须设有签字盖章处',
                3: '签约方中固定方账号不为空',
                4: '不会触发发送前审批',
                5: '发件方必填字段不为空（含描述字段和合同内容字段）',
                6: '非模板组合',
            },
        },
        sendCodeGuide: {
            title: '发送码高级功能说明',
            info: ' 模板发送码适用于不需要发件方填写内容的合同文件，例如离职证明、保密承诺书、授权委托书等，可提供任意扫码人员获取。若文件中有发件方必须填写的资料，或者发件方需要限定扫码获取到文件的相对方范围，可以使用档案+的高级功能。通过扫描档案柜的二维码，可以完成指定人员的合同自动发送，并且填充发件方需要填写的内容，甚至控制自动发送的时间节点；扫码的相对方也会存放在档案柜中，可以随时查询。具体使用方法如下：',
            tip1: {
                main: '1. 上上签',
                sub: '',
                line1: '向上上签申请开通档案+、合同预审、智能预审',
                line2: '开通后可以到对应的菜单中操作使用',
            },
            tip2: {
                main: '2. 档案柜管理员',
                sub: '创建档案柜、配置智能预审',
                line1: '',
                line2: '在档案柜中创建与合同内容相同的填写字段、绑定合同模板、设置对应关系以及扫码自动发出的条件，将档案柜的二维码提供给签约的相对方。',
            },
            tip3: {
                main: '3. 签约方',
                sub: '扫码填资料、获取合同文件',
                line1: '',
                line2: '签约方扫描发件方提供的档案柜发送码进行填写，通过档案柜收集到的资料会存档并且自动填充到合同中，对方收到合同只需要简单盖章或签名，即可完成合同签署',
            },
            tip4: {
                main: '4. 档案柜管理员',
                sub: '',
                line1: '查看签约的相对方、发送的合同情况',
                line2: '发件方的管理员可以到档案柜中，查看扫码的相对方信息、合同发出的情况、是否完成签署等',
            },
        },
    },
    style: {
        signature: {
            text: {
                x: '34',
                fontSize: '18',
            },
        },
    },
    resetPwd: {
        title: '安全提示！',
        notice: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',
        oldLabel: '原密码',
        oldPlaceholder: '请输入原密码',
        newLabel: '新密码',
        newPlaceholder: '6-18位数字和大小写字母，支持特殊字符',
        submit: '确定',
        errorMsg: '密码需包含6-18位数字和大小写字母，请重新设置',
        oldRule: '原密码不能为空',
        newRule: '新密码不能为空',
        success: '修改成功',
    },
    personAuthIntercept: {
        title: '邀请您以',
        name: '姓名：',
        id: '身份证号：',
        descNoAuth: '请确认以上身份信息为您本人，并以此进行实名认证。',
        desMore: '根据发起方要求，您还需要补充',
        descNoSame: '检测到上述信息与您当前的实名信息不符，请联系发起方确认并重新发起合同。',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        tips: '实名认证通过后，可查看并签署合同。',
        goOn: '是我本人，开始认证',
        goMore: '去补充认证',
        descNoSame1: ' 的身份签署合同',
        descNoSame2: '这与您当前登录的账号已完成的实名信息不符。',
        goHome: '返回合同列表页>>',
        authInfo: '检测到您当前账号的实名身份为 ',
        in: '于',
        finishAuth: '完成实名，用于合规签署合同',
        ask: '是否继续以当前账号签署？',
        reAuthBtnText: '是的，我要用本账号重新实名签署',
        changePhoneText: '不是，联系发件方更改签署手机号',
        changePhoneTip1: '应发件方要求，请联系',
        changePhoneTip2: '，更换签署信息(手机号/姓名)，并指定由您签署。',
        confirmReject: '是的，我要驳回实名',
    },
    authIntercept: {
        title: '要求您以：',
        name: '姓名为：',
        id: '身份证号为：',
        descNoAuth1: '请确认以上身份信息为您本人，并以此进行实名认证。',
        descNoAuth2: '实名认证通过后，可查看并签署合同。',
        descNoSame1: '签署合同。',
        descNoSame2: '检测到上述信息与您当前的实名信息不符，请联系发件方确认并重新发起合同。',
        tips: '注：身份信息完全一致才能签署合同',
        goOn: '是我本人，开始认证',
        goHome: '我知道了',
        goMore: '去补充认证',
        authTip: '进行实名认证。',
        viewAndSign: '完成认证后即可查看和签署合同',
        tips2: '注：企业名称完全一致才能查看和签署合同。',
        requestOtherAnth: '请求他人认证',
        goAuth: '去实名认证',
        requestSomeoneList: '请求以下人员完成实名认证：',
        ent: '企业',
        entName: '企业名称',
        account: '账号',
        accountPH: '手机或邮箱',
        send: '发送',
        lackEntName: '请填写企业名称',
        errAccount: '请填写正确的邮箱或手机号',
        successfulSent: '发送成功',
    },
    thirdPartApprovalDialog: {
        title1: '签署前审批',
        title2: '审批流程',
        content1: '审批通过后您才可以签署，请耐心等待。',
        content2: '需由第三方平台（非上上签平台）审批合同。',
        cancelBtnText: '查看审批流程',
        confirmBtnText: '确认',
        iKnow: '我知道了',
    },
    endSignEarlyPrompt: {
        cancel: '取消',
        confirm: '确认',
        signPrompt: '签署提示',
        signTotalCountTip: '本次签署共包含{count}份合同文件',
        signatureTip: '发件人为您的企业设置了{count}位企业成员代表企业签字，当前：',
        hasSigned: '{count}人已签字',
        hasNotSigned: '{count}人未签字',
        noNeedSealTip: '完成盖章后，未签字的企业成员将无需签字。',
    },
    commonNomal: {
        yesterday: '昨天',
        ssq: '上上签',
        ssqPlatform: '上上签电子签约云平台',
        ssqTestPlatform: '（限测试用）BestSign电子签约云平台',
        pageExpiredTip: '页面已过期，请刷新重试',
        pswCodeSimpleTip: '密码需包含6-18位数字和大小写字母，请重新设置',
    },
    transferAdminDialog: {
        title: '身份确认',
        transfer: '转交',
        confirmAdmin: '我是主管理员',
        content: '系统主管理员需要负责企业印章的管理、合同的管理和其它人员权限的管理，一般归属于企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人。| 请确认您是否符合以上身份，如果不符合，建议转交给相关人员。',
    },
    choseBoxForReceiver: {
        dataNeedForReceiver: '签约主体需要提交的资料',
        dataFromDataBox: '签约主体提交的资料需要通过某个档案柜的资料采集来获取',
        searchTp: '请输入档案柜名称或编号',
        search: '搜索',
        boxNotFound: '未找到档案柜',
        cancel: '取 消',
        confirm: '确 认',
    },
    localCommon: {
        cancel: '取消',
        confirm: '确认',
        toSelect: '请选择',
        seal: '盖章',
        signature: '签名',
        signDate: '签署日期',
        text: '文本',
        date: '日期',
        qrCode: '二维码',
        number: '数字',
        dynamicTable: '动态表格',
        terms: '合同条款',
        checkBox: '复选框',
        radioBox: '单选框',
        image: '图片',
        confirmSeal: '业务核对章',
        confirmRemark: '不符合章的备注',
        optional: '选填',
        require: '必填',
        tip: '提示',
        comboBox: '下拉框',
    },
    twoFactor: {
        signTip: '签署提示',
        settingTwoFactor: '设置二要素验证器',
        step1: '1. 安装验证器应用',
        step1Tip: '二要素身份验证需要您安装一下手机应用程序：',
        step2: '2.扫描二维码',
        step2Tip1: '使用下载好的验证器扫描下方二维码（请确保您手机上的时间与当前时间一致，否则无法执行二要素身份验证）。',
        step2Tip2: '屏幕上将显示二要素验证所需要的6位验证码。',
        step3: '3.输入6位验证码',
        step3Tip: '请输入屏幕上显示的验证码',
        verifyCode6: '6位验证码',
        iosAddress: 'iOS下载地址：',
        androidAddress: 'Android下载地址：',
        chromeVerify: '谷歌身份验证器',
        nextBtn: '下一步',
        confirmSign: '确认签署',
        dynamicCode: '验证器动态码',
        password: '加密签署码',
        pleaseInput: '请输入',
        twoFactorTip: '应发件方要求，您需要通过二要素验证才可以完成签署。',
        passwordTip: '应发件方要求，您需要通过加密签署才可以完成签署。',
        twoFactorAndPasswordTip: '应发件方要求，您需要通过二要素验证以及加密签署才可以完成签署。',
        passwordTip2: '请联系发件方索要加密签署码，输入后即可签署合同。',
        dynamicVerifyInfo: '请输入正确的验证器动态码，若您是再次绑定，请输入最新绑定的验证器动态码。',
    },
    functionSupportDialog: {
        title: '功能介绍',
        inputTip: '若您有相关使用需求，欢迎在以下表格中填写您的需求，上上签会在24小时内安排专业人士联系您并提供服务指导。',
        useSence: '使用场景',
        useSenceTip: '如：人事/经销商/物流单据...',
        estimatedOnlineTime: '预计上线时间',
        requireContent: '请详细描述您的需求场景(选填)',
        requireContentTip: '请描述场景需求与期望的解决方案',
        getSupport: '获取专业服务支持',
        callServiceHotline: '立即拨打客服热线：************',
        useSenceNotEmpty: '使用场景不能为空',
        requrieContentNotEmpty: '需求内容不能为空',
        oneWeek: '一周内',
        oneMonth: '一月内',
        other: '其他',
        submitSuccess: '提交成功',
        submitTrial: '立即试用',
        toTrial: '去试用',
        trialTip: '提交试用申请后，当前功能将立即开通。为了便于我们精准了解您的需求，您可以补充填写更多问题场景，我们将尽快与您联系。',
        applyTrial: '申请试用',
        trialSuccTip: '功能已开通，欢迎试用',
        goBuy: '直接购买',
        trialTipMap: {
            title: '试用须知',
            tip1: '1. 即开即用，有效期为7天；',
            tip2: '2. 试用期间，功能不收费；',
            tip3: '3. 每个企业主体，一个功能仅一次试用机会；',
            tip4: '4. 试用期间可自助购买，使用不间断；',
            tip5: '5. 如您的试用已结束，可扫码联系上上签专业顾问了解详情：',
        },
        contactAdminTip: '如需使用，请联系您的企业管理员{tip}购买开通',
        trialEndTip: '试用期结束，点击购买',
        trialRemainDayTip: '试用期剩{day}天，点击购买',
        trialEnd: '试用功能结束',
        trialEndMap: {
            deactivateTip: '{feature}功能已停用，请清除配置，或者续费后，方可继续使用。',
            feature1: '合同附属资料',
            remove1: '清除配置方法为：编辑模板-找到配置好的添加合同附属资料，将其删除。',
            feature2: '手写笔迹识别',
            remove2: '清除配置方法为：编辑模板-找到配置好的笔迹识别，将其删除。',
            feature3: '合同装饰：骑缝章+水印',
            remove3: '清除配置方法为：编辑模板-找到配置好的合同装饰，将其删除。',
            feature4: '合同发送审批',
            remove4: '清除配置方法为：企业控制台-停用所有审批流',
        },
    },
    setSignPwdDialog: {
        tip: '设置完成后，默认优先签约密码，如需修改可登录上上签电子签约平台在「用户中心」或者登录上上签小程序在「账号管理」进行配置调整。',
        saveAndReturnSign: '保存并返回签署',
        changeEmailVerify: '切换邮箱验证',
        changePhoneVerify: '切换手机号验证',
    },
    contractCompare: {
        reUpload: '重新上传',
        title: '合同比对',
        packagePurchase: '套餐购买',
        packagePurchaseTitle: '【{title}功能】套餐购买',
        payOnce: '特惠限购一次',
        myPackage: '我的套餐',
        packageDetail: '套餐详情',
        per: '次',
        packageContent: '套餐包含内容为：',
        num: '{type}次数',
        limitTime: '有效期',
        month: '月',
        payNow: '立即购买',
        contactUs: '联系我们 | 扫码联系上上签专业顾问了解',
        compareInfo1: '使用说明：',
        compareInfo2: '{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；',
        compareInfo3: '{index}、按上传的合同{per}数统计用量',
        codePay: '请用扫码支付',
        aliPay: '支付宝支付',
        wxPay: '微信支付',
        payIno: '开通功能 | 购买对象 | 支付金额',
        finishPay: '完成支付',
        paySuccess: '购买成功',
        originFile: '原始合同文件',
        compareFile: '比对合同文件',
        documentSelect: '选择文件',
        comparisonResult: '比对结果',
        history: '历史记录',
        currentHistory: '文档记录',
        noData: '暂无数据',
        differences: '{num}处差异',
        historyLog: '{num}条记录',
        uploadLimit: '将要比对的文件拖拽至此上传 | 目前支持PDF（含PDF扫描件）、Word文件',
        dragInfo: '释放鼠标完成上传',
        uploadError: '文件格式不支持',
        pageNum: '第{page}页',
        difference: '差异{num}',
        download: '下载比对结果',
        comparing: '合同比对中...',
        tip: '提示',
        confirm: '确定',
        toBuy: '去购买',
        translate: '合同翻译',
        doCompare: '比对',
        doTranslate: '翻译',
        review: '合同审查',
        doReview: '审查',
        reviewUploadFile: '将被审查的文件拖拽至此上传',
        reviewUpload: '将审查依据拖拽至此上传 | 如：《经销商管理办法》《公司采购制度文件》等用于审查合同的公司规章制度性文件 | 目前仅支持PDF、Word文件',
        reviewOriginFile: '被审查合同',
        reviewTargetFile: '审查依据',
        reviewResult: '审查结果',
        uploadReviewFile: '上传审查依据文件',
        risk: '风险点{num}',
        risks: '{num}处风险点',
        startReview: '开始审查',
        reviewing: '合同审查中...',
        noRisk: '审查已完成，未发现风险',
        allowUpload: '可上传《公司采购管理办法》等可指导合同审查的规章制度条例，或上传公司红线规定、部门业务指导等， | 如：甲方需在合同签订后的5日内完成付款。',
        notAllowUpload: '不要以模糊语句或者原则性描述作为审查依据， | 如：所有合同条款不得违法相关法律法规要求。',
        resumeReview: '继续下一份',
        close: '关闭',
        extract: '合同抽取',
        extractTitle: '期望抽取的关键词',
        selectKeyword: '请从下方“关键词”中勾选',
        keyword: '关键词',
        addKeyword: '添加{keyword}',
        introduce: '{keyword}释义',
        startExtract: '开始抽取',
        extractTargetFile: '被抽取合同',
        extractKeyWord: '抽取关键词',
        extracting: '合同抽取中',
        extractResult: '抽取结果',
        extractUploadFile: '将被抽取的文件拖拽至此上传',
        needExtractKeyword: '请选择期望抽取的关键词',
        summary: '合同摘要',
        keySummary: '关键词内容摘要',
        deleteKeywordConfirm: '确定要删除该关键词吗？',
        keywordPosition: '关键词相关位置',
        riskJudgement: '风险判断',
        judgeTargetContract: '被判断合同',
        interpretTargetContract: '被解读合同',
        startJudge: '开始风险判断',
        startInterpret: '开始解读',
        uploadText: '请上传需要进行风险判断的文件',
        interpretText: '请上传需要进行解读的文件',
        startTips: '现在我们可以开始判断风险了',
        interpretTips: '现在我们可以开始进行解读了',
        infoExtract: '协议提取',
    },
    batchImport: {
        iKnow: '我知道了',
    },
    templateCommon: {
        tip: '提示',
    },
    mgapprovenote: {
        SAQ: '问卷调查',
        analyze: '分析',
        annotate: '批注',
        law: '法规',
        case: '案例',
        translate: '翻译',
        mark: '标记',
        tips: '以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记',
        limit: '使用次数已达上限，如有持续使用的需求请填写表单，我们客服人员会联系你。',
        confirmTxt: '去填写',
        content: '查找相关段落',
        experience: '业务经验',
        datas: '相关数据',
        terms: '类似条款',
        original: '来源',
        export: '导出内容',
        preview: '合同预览',
        history: '历史记录',
    },
    sealConfirm: {
        title: '印章确认页',
        header: '确认印章',
        signerEnt: '签约企业：',
        abnormalSeal: '异常印章：',
        sealNormal: '印章正常',
        tip1: '请确认印章是否正常可用，如果正常，可点击“印章正常”按钮，后续该企业再使用此印章，系统将不再给您推送异常提醒。',
        tip2: '如果印章有问题，请及时与签约方沟通更换印章，重新发送合同与其签署，或者进行驳回重签。',
    },
    userCentral: userCentral,
    ...console,
    ...consts,
    keyInfoExtract: {
        operate: '合同信息提取',
        contractType: '预测的合同类型',
        tooltips: '选择需要提取的关键信息',
        predictText: '正在预测合同类型',
        extractText: '正在提取合同信息',
        errorMessage: '你的使用额度已经用完，如有进一步需求可以填写表单，我们会联系你，为你补充更多的用量。',
        result: '提取结果：',
    },
    judgeRisk: {
        title: 'AI律师',
        deepInference: 'AI法务',
        showAll: '展开全文',
        tips: '正在判断合同风险',
        dialogTitle: '“AI律师”审合同',
        aiInterpret: 'AI解读',
    },
    sealDistribute: {
        requestSeal: '申请印章分配',
        company: '公司',
        applicant: '申请人',
        accountID: '账号',
        submissionTime: '时间',
        status: '状态',
        agree: '已同意',
        unAgree: '已驳回',
        ifAgree: '如果同意，',
        applyTime: '申请人印章时间时间为：',
        to: '至',
        placeHolderTime: '年-月-日',
        senderCompany: '发件方企业',
        documentTitle: '合同标题',
        sealApplicationScope: '印章适用范围',
        applyforSeal: '申请印章',
        reject: '驳回',
        approve: '同意',
    },
    sealApproval: {
        requestSeal: '申请印章分配',
        sealRight: '印章权限',
        allEntContract: '所有企业发来的合同',
        partEntContract: '部分企业发来的合同：',
        pleaseInputRight: '请输入权限',
        successTransfer: '交接成功后，',
        getRight: '将可以获得以上权限或可直接编辑分配新的签署权限。',
        signAllEntContract: '签署所有企业发送的合同',
        sign: '签署',
        sendContract: '发送的合同',
        sealUseTime: '印章使用时间：',
        currentStatus: '当前状态：',
        takeBackSeal: '收回印章',
        agree: '同意',
        hasAgree: '已同意',
        hasReject: '已驳回',
        hasDone: '已完成',
        ask: '将',
        giveYou: '的印章分配给你',
        hopeAsk: '希望将',
        hopeGive: '的印章交接于',
        hopeGiveYou: '的相关印章交接与您',
        noSettingTime: '无时间设置',
        approvalSuccess: '审批成功',
        getSealSuccess: '印章获取成功',
    },
    workspace: {
        create: '已创建',
        reviewing: '审查中',
        completed: '已完成',
        noData: '暂无数据',
        introduce: '{keyword}释义',
        termsDetail: '术语详情',
        extractFormat: '提取格式',
        optional: '选填',
        required: '必填',
        operate: '操作',
        detail: '详情',
        delete: '删除',
        agreement: {
            uploadError: '只能上传PDF、DOC、DOCX文件!',
            extractionRequest: '已发起提取请求，请稍后至术语列表中查看提取结果',
            upload: '文件上传',
            define: '术语定义',
            extract: '协议抽取',
            drag: '将文件拖拽到此处，或',
            add: '点击添加',
            format: '支持doc、docx、pdf格式',
            fileName: '文件名称',
            status: '状态',
            completed: '上传完成',
            failed: '上传失败',
            size: '大小',
            terms: '术语',
            success: '文件抽取完成，共{total}个',
            ongoing: '文件正在抽取中...共{total}个',
            tips: '跳过该界面不影响抽取结果',
            others: '继续上传',
            result: '跳转至抽取结果下载页面',
            curProgress: '当前进度: ',
            refresh: '刷新',
            details: '已加载{successNum}个，共{length}个',
            start: '开始抽取',
            more: '添加文件',
            skip: '跳过抽取，完成上传。',
            tiqu: '开始提取',
            chouqu: '开始抽取',
        },
        review: {
            distribution: '分发审查',
            Incomplete: '未结束',
            createReview: '创建审查',
            manageReview: '审查管理',
            reviewDetail: '审查详情',
            reviewId: '审查编号',
            reviewStatus: '审查状态',
            reviewName: '审查名称',
            reviewStartTime: '审查发起时间',
            reviewCompleteTime: '审查结束时间',
            reviewDesc: '版本：版本{reviewVersion}  |  审阅编号：{reviewId}',
            distribute: '发起审查',
            drag: '拖动待审查的协议到当前区域',
            content: '待审阅的内容',
            current: '待分发记录',
            history: '历史记录',
            page: '第{page}页：',
            users: '需要审阅的用户',
            message: '留言',
            modify: '修改',
            placeholder: '多个用户请使用分号";"进行分隔',
            submit: '确定',
            reupload: '重新上传协议审查',
            finish: '结束审查',
            reviewSummary: '审查概要',
            initiator: '审查发起人',
            versionSummary: '版本概要',
            version: '版本',
            versionOrder: '第{version}版',
            curReviewStatus: '当前版本审查状态',
            curReviewVersion: '当前版本',
            curReviewPopulation: '当前版本审查人数',
            curReviewStartTime: '当前版本审查发起时间',
            curReviewInitiator: '当前版本审查发起人',
            checkComments: '聚合查看修订意见',
            overview: '审查结果速览',
            reviewer: '审查人',
            reviewResult: '审查结果',
            replyTime: '回复时间',
            agreement: '审查的协议',
            files: '相关协议',
            fileName: '协议名称',
            numberOfModificationSuggestions: '修改意见数',
            uploadTime: '上传时间',
            download: '下载',
            dispatch: '分发',
            recent: '最新审查时间：',
            replyContent: '审查回复内容',
            advice: '协议修改意见',
            noIdea: '暂无修改意见',
            origin: '原文内容：',
            revised: '修改后内容：',
            suggestion: '修改意见：',
            dateMark: '{name} 在 <span style="color: #0988EC">版本{version}</span>写于 {date}',
            unReviewed: '暂未审查',
            revisionFiles: '修订协议',
            staffReplyAggregation: '聚合修订信息',
            staffReply: '{name}的审查信息',
            tips: '提示',
            tipsContent: '结束后此次审查将不再支持分发及后续操作，是否继续',
            confirm: '确定',
            cancel: '取消',
            successMessage: '已结束',
            PASS: '通过',
            REJECT: '不通过',
            uploadErrorMessage: '目前只支持上传docx格式的文件',
            successInitiated: '已发起审查',
            autoDistribute: '智能分发',
            requiredUsers: '需要审查的用户',
            contentToReview: '审查的内容',
            termDetails: '术语详情',
            term: '术语',
            aiDistribute: 'AI智能分发',
            noData: '暂无数据',
            docIconAlt: '文档图标',
            docxIconAlt: 'DOCX图标',
            pdfIconAlt: 'PDF图标',
            requiredUsersError: '请填写需要审查的用户',
            selectContentError: '请选择审查的内容',
            initiateReviewSuccess: '已发起审查',
            syncInitiated: '已发起同步',
        },
        contentTracing: {
            title: '内容溯源',
            fieldContent: '字段内容',
            originalResult: '原始结果',
            contentSource: '内容源自',
            page: '第',
        },
    },
    hubblePackage: {
        title: '我的套餐',
        details: '套餐详情',
        remainingPages: '剩余总页数',
        pages: '页',
        usedPages: '已用',
        remaining: '可用剩余',
        total: '共',
        expiryTime: '到期时间',
        amount: '数目',
        unitPrice: '单价',
        copy: '份',
        words: '千字',
    },
    workspaceIndex: {
        title: '工作空间',
        package: '套餐用量',
        agreement: '协议管理',
        review: '审查管理',
        term: '术语管理',
    },
    agreement: {
        title: '协议管理',
        exportList: '导出协议列表',
        exportAllChecked: 'Excel(全部字段，勾选协议)',
        exportCurrentChecked: 'Excel(当前字段，勾选协议)',
        exportAllMatched: 'Excel(全部字段，符合条件)',
        exportCurrentMatched: 'Excel(当前字段，符合条件)',
        add: '添加协议',
        upload: '上传协议',
        operation: '操作',
        download: '下载协议',
        details: '详情',
        delete: '删除',
        relatedTaskStatus: '关联提取任务状态',
        confirmDelete: '是否确认删除当前协议',
        prompt: '提示',
        booleanYes: '是',
        booleanNo: '否',
        defaultExportName: 'export.xlsx',
        taskNotStarted: '抽取任务未开始',
        taskStarted: '抽取任务已开始 (内容检索中)',
        contentSearchCompleted: '内容检索已完成 (结果格式化中)',
        resultFormattingCompleted: '结果格式化已完成 (结果校对中)',
        resultVerificationCompleted: '结果校对已完成',
    },
    filter: {
        filter: '过滤',
        refreshExtraction: '重新提取',
        extractTerms: '术语定义提取',
        refreshList: '刷新列表',
        currentCondition: '当前条件下，展示的协议。',
        when: '当',
        selectCondition: '请选择条件',
        enterCondition: '请输入条件',
        yes: '是',
        no: '否',
        addCondition: '添加条件',
        reset: '重置',
        confirm: '确定',
        and: '且',
        or: '或',
        equals: '等于',
        notEquals: '不等于',
        contains: '包含',
        notContains: '不包含',
        greaterThan: '大于',
        greaterThanOrEquals: '大于等于',
        lessThan: '小于',
        lessThanOrEquals: '小于等于',
        emptyCondition: '过滤条件不能为空',
    },
    fieldConfig: {
        button: '字段配置',
        header: '期望展示的字段',
        submit: '完成',
        cancel: '取消',
    },
    agreementDetail: {
        detail: '协议详情',
        add: '添加协议',
        id: '协议编号',
        file: '协议文件',
        download: '协议下载',
        replaceFile: '协议文件替换',
        uploadFile: '协议文件上传',
        relatedExtractionStatus: '关联提取任务状态',
        dataSource: '数据来源',
        yes: '是',
        no: '否',
        select: '请选择',
        input: '请输入',
        save: '保存',
        cancel: '取消',
        page: '第 {page} 页',
        addDataSource: '添加数据来源',
        pageNo: '第',
        pageSuffix: '页',
        submit: '提交',
        inputDataSource: '请输入数据来源内容',
        pageFormatError: '页码只支持英文逗号隔开的数字或者纯数字',
        confirmDelete: '是否确认删除当前数据来源',
        tips: '提示',
        uploadSuccess: '上传成功',
    },
    termManagement: {
        title: '术语管理',
        batchDelete: '批量删除',
        import: '术语导入',
        export: '术语导出',
        add: '+ 添加术语',
        name: '术语名称',
        definition: '术语释义',
        formatRequirement: '提取格式要求',
        dataFormat: '数据格式',
        operation: '操作',
        edit: '修改',
        delete: '删除',
        detail: '术语详情',
        addTitle: '添加术语',
        namePlaceholder: '填写你的专业术语',
        definitionPlaceholder: '填写术语的释义',
        formatRequirementPlaceholder: '填写术语的提取格式要求',
        dataFormatPlaceholder: '期望提取的术语格式要求',
        cancel: '取消',
        confirmEdit: '确认修改',
        importTitle: '术语导入',
        uploadTemplate: '上传术语模板文件',
        downloadTemplate: '下载术语模板文件',
        extractType: {
            text: '文本',
            longText: '长文本',
            date: '日期',
            number: '数值',
            boolean: '是/否',
        },
        importSuccess: '导入成功',
        deleteConfirm: '是否确认删除当前术语',
        prompt: '提示',
        nameEmptyError: '术语名称不能为空',
    },
    agent: {
        extractTitle: '信息提取',
        riskTitle: 'AI律师',
        feedback: '问卷反馈',
        toMini: '去小程序查看',
        otherContract: '让我看看其他合同的隐藏风险?',
        others: '其他',
        submit: '发送',
        autoExtract: '自动进行下一步提取直到提取结束',
        autoRisk: '自动进行下一步分析直到分析结束',
        aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',
        chooseRisk: '请选择需要进行分析的文件',
        chooseExtract: '请选择需要进行提取的文件',
        analyzing: '内容分析中',
        advice: '修改建议生成中',
        options: '选项生成中',
        inputTips: '请输入确切内容',
        chargeTip: '余额不足，请充值',
        original: '原文',
        revision: '修改建议',
        diff: '对比',
        locate: '获取原文定位中',
        custom: '请输入自定义审查规则',
        content: '原文位置',
        satisfy: '对分析结果满意，继续下一项分析',
        dissatisfy: '对分析结果不满意，重新进行分析',
        selectFunc: '请选择你期望使用的功能。',
        deepInference: 'AI法务',
        deepThinking: '深度思考中',
        deepThoughtCompleted: '已深度思考',
        reJudge: '重新判断',
        confirm: '确认',
        tipsContent: '重新判断将会扣除份数，是否继续?',
        useLawyer: '使用AI律师',
        interpretFinish: 'AI解读已完成',
        exportPDF: '导出PDF报告',
        defaultExportName: 'export.pdf',
        exporting: '正在导出报告，请稍后...',
    },
    authorize: {
        title: '使用须知',
        content: '开启智能合同，AI帮你分析，让你的工作更轻松高效！同意以下内容，即可体验。',
        cancel: '不用了',
        confirm: '同意并使用',
        contract: '查看《哈勃产品使用须知》',
    },
    hubbleEntry: {
        smartAdvisor: '智能签约顾问',
        tooltips: '您暂未开通此功能，可联系上上签电子签约顾问购买开通。',
        confirm: '好的',
    },
    lang: 'zh',
};
